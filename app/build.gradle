plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'org.jetbrains.kotlin.kapt'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'androidx.navigation.safeargs.kotlin'
    id 'kotlin-parcelize'
    id 'org.jetbrains.kotlin.plugin.compose'
    id 'com.google.devtools.ksp'
}

def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

def apiPropertiesFile = rootProject.file("api.properties")
def apiProperties = new Properties()
apiProperties.load(new FileInputStream(apiPropertiesFile))

def googleMapsApiKey = apiProperties["googleMapsApiKey"]

android {
    namespace 'com.app.messej'
    compileSdk 35

    signingConfigs {
        debug {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(rootDir.getCanonicalPath() + '/keystore/' + keystoreProperties['keyStoreFile'])
            storePassword keystoreProperties['keyStorePassword']
        }
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(rootDir.getCanonicalPath() + '/keystore/' + keystoreProperties['keyStoreFile'])
            storePassword keystoreProperties['keyStorePassword']
        }
    }

    defaultConfig {
        applicationId "com.app.messej"
        minSdk 26
        targetSdk 35
        versionCode 10341
        versionName "10.34.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        resourceConfigurations += ['en', 'ar', 'es', 'hi', 'tl', 'fr', 'ru', 'ku', 'fa']
        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
    }

    setProperty("archivesBaseName", "Flashat-v${defaultConfig.versionName}-${new Date().format('yyyyMMdd')}")

    bundle {
        language {
            enableSplit = false
        }
    }

    buildTypes {
        debug {
//            applicationIdSuffix ".debug"
            minifyEnabled false
            buildConfigField "String", "BASE_URL", '"https://api.dev.eu.messej.com"'
            buildConfigField "String", "SOCKET_URL", '"http://chatsocket-dev.messej.com"'
            buildConfigField "String", "PODIUM_SOCKET_URL", '"http://podiumsocket-dev.messej.com"'
            buildConfigField "String", "IPAPI_KEY", '"XxtJICp8B5mzYt6Y38D0k5NF4z5EOypwZ3F9uKmw2q4OTUiAqx"'
            buildConfigField "String", "GOOGLE_MAPS_API_KEY", '"${googleMapsApiKey}"'
            buildConfigField "String", "HUDDLE_INVITE_LINK", '"huddle-dev.messej.com"'
            buildConfigField "String", "APP_INVITE_LINK", '"user-dev.messej.com"'
            buildConfigField "String", "FLASH_VIDEO_LINK", '"flash-dev.messej.com"'
            buildConfigField "String", "PODIUM_INVITE_LINK", '"podium-dev.messej.com"'
            buildConfigField "String", "AGORA_APP_ID", '"********************************"'
            buildConfigField "String", "BASE_URL_BUY_COINS_FROM_WEB", '"http://dev.web.flashat.com"'
            manifestPlaceholders = [
                GOOGLE_MAPS_API_KEY: googleMapsApiKey,
                usesCleartextTraffic:"true",
                BRANCH_KEY: apiProperties["branchApiKey"]
            ]
            buildConfigField "String", "MEDIA_UPLOAD_ENVIRONMENT", '"development"'
            debuggable true
            signingConfig signingConfigs.debug
        }
        preprod {
//            initWith qa
//            applicationIdSuffix ".preprod"
            minifyEnabled false
            debuggable = true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BASE_URL", '"https://api.eks-staging.messej.com"'
            buildConfigField "String", "SOCKET_URL", '"https://chatsocket-eks-staging.messej.com"'
            buildConfigField "String", "PODIUM_SOCKET_URL", '"https://podiumsocket-stg.messej.com"'
            buildConfigField "String", "IPAPI_KEY", '"RJ1z3VX2jjQiw8GYi8h2hEPv8h3JZ694aiTUa1Y1jykb3sGk6o"'
            buildConfigField "String", "GOOGLE_MAPS_API_KEY", '"${googleMapsApiKey}"'
            buildConfigField "String", "HUDDLE_INVITE_LINK", '"huddle-eks-staging.messej.com"'
            buildConfigField "String", "APP_INVITE_LINK", '"user-eks-staging.messej.com"'
            buildConfigField "String", "FLASH_VIDEO_LINK", '"flash-stg.messej.com"'
            buildConfigField "String", "PODIUM_INVITE_LINK", '"podium-stg.messej.com"'
            buildConfigField "String", "AGORA_APP_ID", '"********************************"'
            buildConfigField "String", "BASE_URL_BUY_COINS_FROM_WEB", '"http://eks-stg.web.flashat.com"'
            manifestPlaceholders = [
                    GOOGLE_MAPS_API_KEY: googleMapsApiKey,
                    usesCleartextTraffic:"true",
                    BRANCH_KEY: apiProperties["branchApiKey"]
            ]
            buildConfigField "String", "MEDIA_UPLOAD_ENVIRONMENT", '"eks-staging"'
            matchingFallbacks = ['release']

            signingConfig signingConfigs.release
        }
        qa {
            applicationIdSuffix ".qa"
            minifyEnabled true
            debuggable false
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BASE_URL", '"https://api.eks-staging.messej.com"'
            buildConfigField "String", "SOCKET_URL", '"https://chatsocket-eks-staging.messej.com"'
            buildConfigField "String", "PODIUM_SOCKET_URL", '"https://podiumsocket-stg.messej.com"'
            buildConfigField "String", "IPAPI_KEY", '"RJ1z3VX2jjQiw8GYi8h2hEPv8h3JZ694aiTUa1Y1jykb3sGk6o"'
            buildConfigField "String", "GOOGLE_MAPS_API_KEY", '"${googleMapsApiKey}"'
            buildConfigField "String", "HUDDLE_INVITE_LINK", '"huddle-eks-staging.messej.com"'
            buildConfigField "String", "APP_INVITE_LINK", '"user-eks-staging.messej.com"'
            buildConfigField "String", "FLASH_VIDEO_LINK", '"flash-stg.messej.com"'
            buildConfigField "String", "PODIUM_INVITE_LINK", '"podium-stg.messej.com"'
            buildConfigField "String", "AGORA_APP_ID", '"********************************"'
            buildConfigField "String", "BASE_URL_BUY_COINS_FROM_WEB", '"http://eks-stg.web.flashat.com"'
            manifestPlaceholders = [
                    GOOGLE_MAPS_API_KEY: googleMapsApiKey,
                    usesCleartextTraffic:"true",
                    BRANCH_KEY: apiProperties["branchApiKey"]
            ]
            buildConfigField "String", "MEDIA_UPLOAD_ENVIRONMENT", '"eks-staging"'
            matchingFallbacks = ['release']

            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BASE_URL", '"https://api.messej.com"'
            buildConfigField "String", "SOCKET_URL", '"https://chatsocket.messej.com"'
            buildConfigField "String", "PODIUM_SOCKET_URL", '"https://podiumsocket.messej.com"'
            buildConfigField "String", "IPAPI_KEY", '"RJ1z3VX2jjQiw8GYi8h2hEPv8h3JZ694aiTUa1Y1jykb3sGk6o"'
            buildConfigField "String", "GOOGLE_MAPS_API_KEY", '"${googleMapsApiKey}"'
            buildConfigField "String", "HUDDLE_INVITE_LINK", '"huddle.messej.com"'
            buildConfigField "String", "APP_INVITE_LINK", '"user.messej.com"'
            buildConfigField "String", "FLASH_VIDEO_LINK", '"flash.messej.com"'
            buildConfigField "String", "PODIUM_INVITE_LINK", '"podium.messej.com"'
            buildConfigField "String", "AGORA_APP_ID", '"********************************"'
            buildConfigField "String", "BASE_URL_BUY_COINS_FROM_WEB", '"https://web.flashat.com"'
            manifestPlaceholders = [
                    GOOGLE_MAPS_API_KEY: googleMapsApiKey,
                    usesCleartextTraffic:"true",
                    BRANCH_KEY: apiProperties["branchApiKey"]
            ]
            buildConfigField "String", "MEDIA_UPLOAD_ENVIRONMENT", '"production"'
            debuggable false

            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
        dataBinding true
        compose true
        buildConfig = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.14"
    }
    packagingOptions {
        jniLibs {
            useLegacyPackaging false
        }
    }
    testOptions {
        unitTests {
            all {
                useJUnitPlatform()
            }
            unitTests.returnDefaultValues = true
        }
    }
}

ksp {
    arg("room.schemaLocation", "$projectDir/schemas")
}

dependencies {
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.fragment:fragment-ktx:1.8.9'
    testImplementation 'org.junit.jupiter:junit-jupiter'

//    Map and Location
    implementation 'com.google.android.gms:play-services-maps:19.2.0'
    implementation "com.google.android.gms:play-services-location:21.3.0"
    implementation 'com.google.android.libraries.places:places:4.4.1'
    implementation("com.google.android.play:app-update:2.1.0")
    implementation("com.google.android.play:app-update-ktx:2.1.0")

    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.compose.material3:material3:1.3.2'

//    DeSugaring - v1.2.0 for AGP 7.3.0+
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.5")

//    Jetpack
    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.9.3'
    implementation 'androidx.navigation:navigation-ui-ktx:2.9.3'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.9.2'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.2'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.9.2'
    implementation "androidx.recyclerview:recyclerview:1.4.0"
    implementation "androidx.exifinterface:exifinterface:1.4.1"
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.viewpager2:viewpager2:1.1.0'
    implementation "androidx.datastore:datastore-preferences:1.1.7"
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.2.0-beta01"
    implementation "androidx.work:work-runtime:2.10.3"
    implementation 'androidx.concurrent:concurrent-futures-ktx:1.3.0'

    implementation 'androidx.media3:media3-common:1.8.0'
    implementation "androidx.media3:media3-transformer:1.8.0"
    implementation "androidx.media3:media3-exoplayer:1.8.0"
    implementation "androidx.media3:media3-ui:1.8.0"
    implementation 'androidx.media3:media3-effect:1.8.0'

    implementation "androidx.camera:camera-core:1.4.2"
    implementation "androidx.camera:camera-camera2:1.4.2"
    implementation "androidx.camera:camera-lifecycle:1.4.2"
    implementation "androidx.camera:camera-video:1.4.2"
    implementation "androidx.camera:camera-view:1.4.2"
    implementation "androidx.camera:camera-extensions:1.4.2"

    def composeBom = platform('androidx.compose:compose-bom:2025.08.00')
    implementation composeBom
    androidTestImplementation composeBom
    implementation 'androidx.compose.foundation:foundation'
    implementation 'androidx.compose.material:material'
    implementation 'androidx.compose.runtime:runtime-livedata'
    implementation 'androidx.compose.ui:ui-android'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    //Compose pagination
    implementation "androidx.paging:paging-compose:3.3.6"
    debugImplementation 'androidx.compose.ui:ui-tooling'
    preprodImplementation 'androidx.compose.ui:ui-tooling'

    implementation 'androidx.constraintlayout:constraintlayout-compose:1.1.1'


    // Scene view
    implementation("io.github.sceneview:sceneview:2.3.0")

    implementation 'com.google.android.gms:play-services-base:18.7.2'
    implementation 'com.google.android.gms:play-services-auth-api-phone:18.2.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.10.2'
    implementation 'com.google.android.flexbox:flexbox:3.0.0'


    //Optional for phone number hint
    implementation 'com.google.android.gms:play-services-auth:21.4.0'

    implementation 'com.github.hadilq:live-event:1.3.0'
    implementation 'com.github.Kennyc1012:MultiStateView:2.2.0'

//    Http
    implementation 'com.google.code.gson:gson:2.13.1'
    implementation 'com.squareup.retrofit2:retrofit:3.0.0'
    implementation 'com.squareup.retrofit2:converter-gson:3.0.0'
    implementation 'com.squareup.okhttp3:okhttp:5.1.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:5.1.0'
    implementation 'com.github.f4b6a3:uuid-creator:6.1.1'

    implementation 'io.github.cymchad:BaseRecyclerViewAdapterHelper:3.0.14'
    implementation "com.github.thesurix:gesture-recycler:1.17.0"
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'jp.wasabeef.transformers:glide:1.0.6'
    implementation 'jp.wasabeef:glide-transformations:4.3.0'
    // If you want to use the GPU Filters

    implementation 'com.makeramen:roundedimageview:2.3.0'
    implementation 'id.zelory:compressor:3.0.1'
    implementation "com.airbnb.android:lottie:6.6.7"
    implementation 'com.github.yalantis:ucrop:2.2.10'
    implementation 'com.github.aitsuki:SwipeMenuRecyclerView:2.1.5'

    implementation 'com.tbuonomo:dotsindicator:5.1.0'
    implementation 'me.xdrop:fuzzywuzzy:1.4.0'
    implementation 'com.hbb20:ccp:2.7.3'
    implementation 'com.github.aabhasr1:OtpView:v1.1.2-ktx'
    implementation 'com.github.stfalcon-studio:StfalconImageViewer:1.0.1'
    implementation 'com.github.shahimclt:RecordView:3.1.5'
    implementation "com.webtoonscorp.android:readmore-view:1.4.0"
    implementation 'com.facebook.shimmer:shimmer:0.5.0'
    implementation 'com.github.gayanvoice:android-animations-kotlin:1.0.1'
    implementation 'com.afollestad.material-dialogs:core:3.3.0'
//    Room
    ksp "androidx.room:room-compiler:2.7.2"
    implementation "androidx.room:room-ktx:2.7.2"
    implementation "androidx.room:room-paging:2.7.2"
    implementation "androidx.paging:paging-runtime-ktx:3.3.6"
    implementation "androidx.paging:paging-compose:3.3.6"
    implementation "androidx.activity:activity-ktx:1.10.1"

//    Firebase
    implementation platform('com.google.firebase:firebase-bom:34.1.0')
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-config'
    implementation 'com.google.firebase:firebase-messaging'

    implementation 'com.android.billingclient:billing:7.1.1'

    implementation "androidx.credentials:credentials:1.5.0"
    implementation "androidx.credentials:credentials-play-services-auth:1.5.0"
    implementation "com.google.android.libraries.identity.googleid:googleid:1.1.1"

    // AWS
    implementation 'com.amazonaws:aws-android-sdk-core:2.81.0'
    implementation 'com.amazonaws:aws-android-sdk-s3:2.81.0'

//    Socket.io
    implementation ('io.socket:socket.io-client:2.1.2') {
        exclude group: 'org.json', module: 'json'
    }

    implementation 'io.agora.rtc:full-sdk:4.5.2'

//    Permissions
    implementation 'com.afollestad.assent:rationales:3.0.2'
    implementation 'com.afollestad.assent:core:3.0.2'
    //MarkDown library
    implementation 'io.noties.markwon:core:4.6.2'
    implementation 'io.noties.markwon:html:4.6.2'
    implementation 'io.noties.markwon:editor:4.6.2'

    //Biometric dependency
    implementation "androidx.biometric:biometric-ktx:1.2.0-alpha05"

    implementation "com.github.skydoves:balloon:1.4.7"

    implementation 'com.burhanrashid52:photoeditor:3.0.2'

    implementation 'io.github.scwang90:refresh-layout-kernel:2.1.1'     //core
    implementation 'io.github.scwang90:refresh-header-classics:2.1.1'   //ClassicsHeader
    implementation 'io.github.scwang90:refresh-footer-classics:2.1.1'   //ClassicsFooter

    //Compose Coil Image Library
    implementation("io.coil-kt.coil3:coil-compose:3.3.0")
    implementation("io.coil-kt.coil3:coil-network-okhttp:3.3.0")

    implementation "app.juky:squircleview:0.6.1"
    implementation 'com.github.hyuwah:DraggableView:1.0.1'

    androidTestImplementation 'androidx.test.ext:junit:1.3.0'
    testImplementation "org.junit.jupiter:junit-jupiter-api:5.13.4"
    testImplementation "org.junit.jupiter:junit-jupiter-params:5.13.4"
    testImplementation "org.junit.jupiter:junit-jupiter-engine:5.13.4"
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.7.0'

    //Branch.io
    implementation "io.branch.sdk.android:library:5.20.0"
}

tasks {
    tasks.register("clearBuilds") {
        group = "Custom Tasks" // This makes it visible in the Gradle sidebar
        description = "Removes the already existing APKS and AABS."
        def outputDirs = [
                layout.buildDirectory.dir("outputs/apk/debug").get().asFile,
                layout.buildDirectory.dir("outputs/apk/release").get().asFile,
                layout.buildDirectory.dir("outputs/apk/qa").get().asFile,
                layout.buildDirectory.dir("outputs/bundle/release").get().asFile
        ]
        outputDirs.each { dir ->
            if(dir.exists()) {
                dir.eachFileMatch(~/.*\.(apk|aab)$/) { file ->
                    file.delete()
                }
            }
        }
        println "Cleared old APKs before build."
    }
    tasks.register("generateReleaseAndQa", Copy) {
        dependsOn "clearBuilds","assembleQa", "assembleRelease", "bundleRelease"
//        dependsOn "clearBuilds", "assembleDebug", "assembleQa"
        group = "Custom Tasks" // This makes it visible in the Gradle sidebar
        description = "Generates both signed APK and bundle for release and QA."

        def outputDirs = [
                layout.buildDirectory.dir("outputs/apk/debug").get().asFile,
                layout.buildDirectory.dir("outputs/apk/release").get().asFile,
                layout.buildDirectory.dir("outputs/apk/qa").get().asFile,
                layout.buildDirectory.dir("outputs/bundle/release").get().asFile
        ]

        def destinationDir = file("G://My Drive/Messej/APK Builds/")

        from(outputDirs)
        into(destinationDir)
        include("*.apk", "*.aab")

        doLast {
            println "Copied release files to ${destinationDir}"
        }
    }
    tasks.register("generateDev", Copy) {
        dependsOn "assembleDebug"
        group = "Custom Tasks" // This makes it visible in the Gradle sidebar
        description = "Generates signed APK for Debug."

        def outputDirs = [
                layout.buildDirectory.dir("outputs/apk/debug").get().asFile,
        ]

        def destinationDir = file("G://My Drive/Messej/APK Builds/")

        from(outputDirs)
        into(destinationDir)
        include("*.apk", "*.aab")

        doLast {
            println "Copied release files to ${destinationDir}"
        }
    }

    tasks.register("generateQa", Copy) {
        dependsOn "assembleQa"
        group = "Custom Tasks" // This makes it visible in the Gradle sidebar
        description = "Generates signed APK for QA."

        def outputDirs = [
                layout.buildDirectory.dir("outputs/apk/qa").get().asFile,
        ]

        def destinationDir = file("G://My Drive/Messej/APK Builds/")

        from(outputDirs)
        into(destinationDir)
        include("*.apk", "*.aab")

        doLast {
            println "Copied release files to ${destinationDir}"
        }
    }
}