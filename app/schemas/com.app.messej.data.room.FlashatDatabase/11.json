{"formatVersion": 1, "database": {"version": 11, "identityHash": "67c9c98838fffbee48d88cd0f676627d", "entities": [{"tableName": "fls_remotekeys", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`query` TEXT NOT NULL, `nextPage` TEXT, PRIMARY KEY(`query`))", "fields": [{"fieldPath": "query", "columnName": "query", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextPage", "columnName": "nextPage", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["query"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_huddles", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `about` TEXT, `thumbnail` TEXT, `group_photo` TEXT, `manager_premium_status` INTEGER, `category` TEXT, `created_by` TEXT, `private` INTEGER NOT NULL, `status` TEXT NOT NULL, `admin_status` TEXT, `user_status` TEXT NOT NULL, `total_members` INTEGER NOT NULL, `activity` TEXT, `time_created` TEXT, `time_updated` TEXT, `online_participants` INTEGER NOT NULL, `unread_count` INTEGER NOT NULL, `tribe` INTEGER NOT NULL DEFAULT 0, `updated_by` INTEGER NOT NULL, `participant_share` INTEGER NOT NULL, `invite_link` TEXT, `manager_id` INTEGER NOT NULL, `request_to_join` INTEGER NOT NULL, `requested_invited_count` INTEGER NOT NULL, `muted` INTEGER NOT NULL, `pinned` INTEGER NOT NULL, `role` TEXT, `sender_details` TEXT, `last_message` TEXT, `last_read_message` TEXT, `privacy` TEXT, `huddle_type` TEXT NOT NULL, `huddle_show_type` TEXT DEFAULT 'NONE', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "about", "columnName": "about", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupPhoto", "columnName": "group_photo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "managerPremium", "columnName": "manager_premium_status", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": false}, {"fieldPath": "created<PERSON>y", "columnName": "created_by", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isPrivate", "columnName": "private", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "adminStatus", "columnName": "admin_status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userStatus", "columnName": "user_status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalMembers", "columnName": "total_members", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activity", "columnName": "activity", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "onlineParticipants", "columnName": "online_participants", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unread_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isTribe", "columnName": "tribe", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "updatedBy", "columnName": "updated_by", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "participantShare", "columnName": "participant_share", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "inviteLink", "columnName": "invite_link", "affinity": "TEXT", "notNull": false}, {"fieldPath": "managerId", "columnName": "manager_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "requestToJoin", "columnName": "request_to_join", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "requestsAndInvites", "columnName": "requested_invited_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT", "notNull": false}, {"fieldPath": "senderDetails", "columnName": "sender_details", "affinity": "TEXT", "notNull": false}, {"fieldPath": "_lastMessage", "columnName": "last_message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastReadMessage", "columnName": "last_read_message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "privacy", "columnName": "privacy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "huddleType", "columnName": "huddle_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "involvement", "columnName": "huddle_show_type", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_huddle_interventions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `blocked_from_huddle` TEXT NOT NULL, `admin_invited` TEXT NOT NULL, `user_blocked` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "huddleId", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "blocked_from_huddle", "affinity": "TEXT", "notNull": true}, {"fieldPath": "invitedToBeAdmin", "columnName": "admin_invited", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userBlocked", "columnName": "user_blocked", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_messages", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `last_message` TEXT, `last_message_id` TEXT, `sender` INTEGER NOT NULL, `receiver` INTEGER NOT NULL, `receiver_data` TEXT NOT NULL, `room_id` TEXT NOT NULL, `room_name` TEXT, `status` TEXT, `type` TEXT NOT NULL, `unread_count` INTEGER NOT NULL, `updated` TEXT, `is_ignored` INTEGER, `followed_by_each` INTEGER, `private_message_tab` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "_lastMessage", "columnName": "last_message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastMessageId", "columnName": "last_message_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "receiverDetails", "columnName": "receiver_data", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomName", "columnName": "room_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unread_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updated", "columnName": "updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isIgnored", "columnName": "is_ignored", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "followedByEach", "columnName": "followed_by_each", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "privateMessageTab", "columnName": "private_message_tab", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_messages_type", "unique": false, "columnNames": ["type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_type` ON `${TABLE_NAME}` (`type`)"}, {"name": "index_fls_messages_unread_count", "unique": false, "columnNames": ["unread_count"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_unread_count` ON `${TABLE_NAME}` (`unread_count`)"}, {"name": "index_fls_messages_updated", "unique": false, "columnNames": ["updated"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_updated` ON `${TABLE_NAME}` (`updated`)"}, {"name": "index_fls_messages_private_message_tab", "unique": false, "columnNames": ["private_message_tab"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_private_message_tab` ON `${TABLE_NAME}` (`private_message_tab`)"}], "foreignKeys": []}, {"tableName": "fls_messages_room_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`canChat` INTEGER, `error` TEXT, `chatBlockedBySender` INTEGER, `userBlockedByReceiver` INTEGER, `userBlockedBySender` INTEGER, `chatDisabledBySender` INTEGER, `chatDisabledByReceiver` INTEGER, `localId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `participants` TEXT NOT NULL, `creator` INTEGER, `userBlockStatus` TEXT, `followedStatus` TEXT, `threadType` TEXT, `created` TEXT, `id` TEXT NOT NULL, `roomStatus` TEXT, `type` TEXT, `chatType` TEXT)", "fields": [{"fieldPath": "canChat", "columnName": "canChat", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "error", "columnName": "error", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatBlockedBySender", "columnName": "chatBlockedBySender", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userBlockedByReceiver", "columnName": "userBlockedByReceiver", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userBlockedBySender", "columnName": "userBlockedBySender", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatDisabledBySender", "columnName": "chatDisabledBySender", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatDisabledByReceiver", "columnName": "chatDisabledByReceiver", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "localId", "columnName": "localId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chatRoom.participants", "columnName": "participants", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.creator", "columnName": "creator", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatRoom.userBlockStatus", "columnName": "userBlockStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.followedStatus", "columnName": "followedStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.threadType", "columnName": "threadType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.created", "columnName": "created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.roomStatus", "columnName": "roomStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.type", "columnName": "type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.chatType", "columnName": "chatType", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["localId"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_huddles_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `sent` TEXT, `remover` TEXT, `deleted` INTEGER NOT NULL, `is_activity` INTEGER NOT NULL, `activity_meta` TEXT, `media` TEXT, `media_meta` TEXT, `read` TEXT, `receiver` INTEGER, `reply_to` TEXT, `sender` INTEGER NOT NULL, `sender_relation` TEXT, `sender_details` TEXT, `liked` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `huddle_id` TEXT NOT NULL, `huddle_type` TEXT NOT NULL, `total_likes` INTEGER NOT NULL, `total_comments` INTEGER NOT NULL DEFAULT 0, `star_type` TEXT, `pinned` INTEGER, `reply_message_id` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sentTime", "columnName": "sent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "remover", "columnName": "remover", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActivity", "columnName": "is_activity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activityMeta", "columnName": "activity_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "read", "columnName": "read", "affinity": "TEXT", "notNull": false}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "replyTo", "columnName": "reply_to", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderRelation", "columnName": "sender_relation", "affinity": "TEXT", "notNull": false}, {"fieldPath": "senderDetails", "columnName": "sender_details", "affinity": "TEXT", "notNull": false}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleType", "columnName": "huddle_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalLikes", "columnName": "total_likes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalComments", "columnName": "total_comments", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "starType", "columnName": "star_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "replyMessageId", "columnName": "reply_message_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_chats_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_chats_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}], "foreignKeys": []}, {"tableName": "fls_messages_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `sent` TEXT, `deleted` INTEGER NOT NULL, `is_activity` INTEGER NOT NULL, `activity_meta` TEXT, `media` TEXT, `media_meta` TEXT, `read` TEXT, `receiver` INTEGER NOT NULL, `reply_to` TEXT, `sender` INTEGER NOT NULL, `liked` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `blocked` INTEGER NOT NULL, `user_id` INTEGER NOT NULL, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sentTime", "columnName": "sent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActivity", "columnName": "is_activity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activityMeta", "columnName": "activity_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "read", "columnName": "read", "affinity": "TEXT", "notNull": false}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "replyTo", "columnName": "reply_to", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "blocked", "columnName": "blocked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_messages_chats_room_id", "unique": false, "columnNames": ["room_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_chats_room_id` ON `${TABLE_NAME}` (`room_id`)"}, {"name": "index_fls_messages_chats_created", "unique": false, "columnNames": ["created"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_chats_created` ON `${TABLE_NAME}` (`created`)"}], "foreignKeys": []}, {"tableName": "fls_broadcast_message", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `broadcast_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `deleted` INTEGER NOT NULL, `media` TEXT, `media_meta` TEXT, `broadcaster` INTEGER NOT NULL, `subscriber` INTEGER NOT NULL, `broadcast_type` TEXT NOT NULL, `liked` INTEGER NOT NULL, `starred` INTEGER NOT NULL, `total_likes` INTEGER NOT NULL, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "messageId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "broadcastId", "columnName": "broadcast_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "broadcaster", "columnName": "broadcaster", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "subscriber", "columnName": "subscriber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "broadcastMode", "columnName": "broadcast_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "starred", "columnName": "starred", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalLikes", "columnName": "total_likes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_broadcast_message_broadcaster", "unique": false, "columnNames": ["broadcaster"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_broadcaster` ON `${TABLE_NAME}` (`broadcaster`)"}, {"name": "index_fls_broadcast_message_broadcast_type", "unique": false, "columnNames": ["broadcast_type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_broadcast_type` ON `${TABLE_NAME}` (`broadcast_type`)"}, {"name": "index_fls_broadcast_message_created", "unique": false, "columnNames": ["created"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_created` ON `${TABLE_NAME}` (`created`)"}, {"name": "index_fls_broadcast_message_starred", "unique": false, "columnNames": ["starred"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_starred` ON `${TABLE_NAME}` (`starred`)"}, {"name": "index_fls_broadcast_message_message", "unique": false, "columnNames": ["message"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_message` ON `${TABLE_NAME}` (`message`)"}], "foreignKeys": []}, {"tableName": "fls_huddles_reported_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT, `message` TEXT, `created` TEXT NOT NULL, `message_sent` TEXT, `time_updated` TEXT, `media` TEXT, `media_meta` TEXT, `sender_id` INTEGER NOT NULL, `report_id` INTEGER NOT NULL, `reports_count` INTEGER NOT NULL, `deletedUser` INTEGER NOT NULL, `sender_membership` TEXT, `sender_name` TEXT NOT NULL, `sender_username` TEXT, `thumbnail` TEXT, `status` TEXT, `huddle_id` INTEGER NOT NULL, `verified` INTEGER NOT NULL, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sentTime", "columnName": "message_sent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedTime", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender", "columnName": "sender_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportId", "columnName": "report_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportsCount", "columnName": "reports_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userDeleted", "columnName": "deletedUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderMembership", "columnName": "sender_membership", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender<PERSON>ame", "columnName": "sender_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "senderUsername", "columnName": "sender_username", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "reportStatus", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_reported_chats_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_reported_chats_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}], "foreignKeys": []}, {"tableName": "fls_huddles_reported_comments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `huddle_id` INTEGER NOT NULL, `post_id` TEXT NOT NULL, `created` TEXT NOT NULL, `comment` TEXT NOT NULL, `sender_name` TEXT NOT NULL, `report_id` INTEGER NOT NULL, `reports_count` INTEGER NOT NULL, `status` TEXT, `time_updated` TEXT, `deletedUser` INTEGER NOT NULL, `sender_membership` TEXT, `sender_id` INTEGER NOT NULL, `thumbnail` TEXT, PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "commentId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageId", "columnName": "post_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "created", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "message", "columnName": "comment", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sender<PERSON>ame", "columnName": "sender_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "reportId", "columnName": "report_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportsCount", "columnName": "reports_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportStatus", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedTime", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userDeleted", "columnName": "deletedUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderMembership", "columnName": "sender_membership", "affinity": "TEXT", "notNull": false}, {"fieldPath": "senderId", "columnName": "sender_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_reported_comments_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_reported_comments_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}], "foreignKeys": []}, {"tableName": "fls_media", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `key` TEXT NOT NULL, `name` TEXT NOT NULL, `path` TEXT NOT NULL, `mediaType` TEXT NOT NULL, `uploadState` TEXT NOT NULL, `local_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "mediaType", "columnName": "mediaType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uploadState", "columnName": "uploadState", "affinity": "TEXT", "notNull": true}, {"fieldPath": "localId", "columnName": "local_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["local_id"]}, "indices": [{"name": "index_fls_media_message_id", "unique": true, "columnNames": ["message_id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_media_message_id` ON `${TABLE_NAME}` (`message_id`)"}], "foreignKeys": []}, {"tableName": "fls_stars_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `stars` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `superstar_id` INTEGER, `last_broadcast_time` TEXT, `unread_messages_count` INTEGER NOT NULL, `muted` INTEGER NOT NULL, `pinned` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `archived` INTEGER NOT NULL, `archived_pinned` TEXT, `hidden` INTEGER NOT NULL, `isSuperStar` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "superstarId", "columnName": "superstar_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastBroadcastTime", "columnName": "last_broadcast_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "unreadMessagesCount", "columnName": "unread_messages_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "archived", "columnName": "archived", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "archivedPinned", "columnName": "archived_pinned", "affinity": "TEXT", "notNull": false}, {"fieldPath": "hidden", "columnName": "hidden", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSuperStar", "columnName": "isSuperStar", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_stars_list_last_broadcast_time", "unique": false, "columnNames": ["last_broadcast_time"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_last_broadcast_time` ON `${TABLE_NAME}` (`last_broadcast_time`)"}, {"name": "index_fls_stars_list_isSuperStar", "unique": false, "columnNames": ["isSuperStar"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_isSuperStar` ON `${TABLE_NAME}` (`isSuperStar`)"}, {"name": "index_fls_stars_list_membership", "unique": false, "columnNames": ["membership"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_membership` ON `${TABLE_NAME}` (`membership`)"}], "foreignKeys": []}, {"tableName": "fls_broadcast_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `stars` INTEGER NOT NULL, `broadcastLikersPrivacy` INTEGER, `isFollowed` INTEGER, `relative_type` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "broadcastLikersPrivacy", "columnName": "broadcastLikersPrivacy", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isFollowed", "columnName": "isFollowed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "relativeType", "columnName": "relative_type", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_user_nick_names", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `nickName` TEXT, `timeCreated` TEXT, `timeUpdated` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_recent_search", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `keyword` TEXT NOT NULL, `timeCreated` TEXT, `screenType` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "keyword", "columnName": "keyword", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "screenType", "columnName": "screenType", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_business_statements", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`accountName` TEXT NOT NULL, `address` TEXT, `availableBalance` REAL, `balanceBroughtForward` REAL, `beneficiaryName` TEXT, `grandTotal` REAL, `grandTotalGenerated` REA<PERSON>, `grandTotalRefunded` REAL, `grandTotalRewarded` REAL, `grandTotalWithdrawn` REAL, `statementDate` TEXT, `thisMonthGenerated` REAL, `thisMonthNet` REAL, `thisMonthRefunded` REAL, `thisMonthRewarded` REAL, `thisMonthTotal` REAL, `thisMonthWithdrawn` REAL, `totalPendingPP` REAL, `balanceCarryMonth` TEXT, `local_id` INTEGER NOT NULL, PRIMARY KEY(`local_id`))", "fields": [{"fieldPath": "accountName", "columnName": "accountName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "address", "columnName": "address", "affinity": "TEXT", "notNull": false}, {"fieldPath": "availableBalance", "columnName": "availableBalance", "affinity": "REAL", "notNull": false}, {"fieldPath": "balanceBroughtForward", "columnName": "balanceBroughtForward", "affinity": "REAL", "notNull": false}, {"fieldPath": "beneficiary<PERSON><PERSON>", "columnName": "beneficiary<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "grandTotal", "columnName": "grandTotal", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalGenerated", "columnName": "grandTotalGenerated", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalRefunded", "columnName": "grandTotalRefunded", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalRewarded", "columnName": "grandTotalRewarded", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalWithdrawn", "columnName": "grandTotalWithdrawn", "affinity": "REAL", "notNull": false}, {"fieldPath": "statementDate", "columnName": "statementDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thisMonthGenerated", "columnName": "thisMonthGenerated", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthNet", "columnName": "thisMonthNet", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthRefunded", "columnName": "thisMonthRefunded", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthRewarded", "columnName": "thisMonthRewarded", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthTotal", "columnName": "thisMonthTotal", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthWithdrawn", "columnName": "thisMonthWithdrawn", "affinity": "REAL", "notNull": false}, {"fieldPath": "totalPendingPP", "columnName": "totalPendingPP", "affinity": "REAL", "notNull": false}, {"fieldPath": "balance<PERSON><PERSON>ry<PERSON><PERSON><PERSON>", "columnName": "balance<PERSON><PERSON>ry<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "localId", "columnName": "local_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["local_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_business_operations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`appSharesCount` INTEGER, `dears` INTEGER, `dearsToday` INTEGER, `newAppSharePercentage` INTEGER, `newBroadcastPercentage` INTEGER, `newCustomersPercentage` INTEGER, `newFollowersPercentage` INTEGER, `newHuddlePercentage` INTEGER, `newLikesPercentage` INTEGER, `newParticipantPercentage` INTEGER, `fans` INTEGER, `fansToday` INTEGER, `huddlesCount` INTEGER, `likers` INTEGER, `rating` INTEGER, `appReviewStatus` TEXT, `followers` INTEGER, `membership` TEXT NOT NULL, `minimumPpRequired` REAL, `participantCount` INTEGER, `payoutEligiblity` INTEGER, `percentOfAppShares` INTEGER, `percentOfFollowers` INTEGER, `percentOfLikes` INTEGER, `percentOfParticipants` INTEGER, `profileCompletePercentage` INTEGER, `totalBroadcasts` INTEGER, `totalLikes` INTEGER, `appSharesTarget` INTEGER, `broadcastTarget` INTEGER, `customersTarget` INTEGER, `followersTarget` INTEGER, `huddlesTarget` INTEGER, `likesTarget` INTEGER, `participantsTarget` INTEGER, `payout_minimumPpForFirstPayout` REAL, `payout_minimumPpForFourthPayout` REAL, `payout_minimumPpForSecondPayout` REAL, `payout_minimumPpForThirdPayout` REAL, `payout_payoutMinAppSharesMonthly` INTEGER, `payout_payoutMinBroadcasts` INTEGER, `payout_payoutMinDears` INTEGER, `payout_payoutMinHuddles` INTEGER, `payout_payoutMinLikes` INTEGER, `payout_payoutMinNoFans` INTEGER, `payout_payoutMinNoLikers` INTEGER, `payout_payoutMinPointsForNextReview` INTEGER, `payout_payoutMinTotalParticipantsInHuddles` INTEGER, `payout_payoutMinTotalPostsByOthersParticipants` INTEGER, `payout_refundablePp` REAL, PRIMARY KEY(`membership`))", "fields": [{"fieldPath": "appSharesCount", "columnName": "appSharesCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dears<PERSON><PERSON>y", "columnName": "dears<PERSON><PERSON>y", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newAppSharePercentage", "columnName": "newAppSharePercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newBroadcastPercentage", "columnName": "newBroadcastPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newCustomersPercentage", "columnName": "newCustomersPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newFollowersPercentage", "columnName": "newFollowersPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newHuddlePercentage", "columnName": "newHuddlePercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newLikesPercentage", "columnName": "newLikesPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newParticipantPercentage", "columnName": "newParticipantPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fansToday", "columnName": "fansToday", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddlesCount", "columnName": "huddlesCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "rating", "columnName": "rating", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "appReviewStatus", "columnName": "appReviewStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "followers", "columnName": "followers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "minimumPpRequired", "columnName": "minimumPpRequired", "affinity": "REAL", "notNull": false}, {"fieldPath": "participantCount", "columnName": "participantCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutEligiblity", "columnName": "payoutEligiblity", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "percentOfAppShares", "columnName": "percentOfAppShares", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "percentOfFollowers", "columnName": "percentOfFollowers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "percentOfLikes", "columnName": "percentOfLikes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "percentOfParticipants", "columnName": "percentOfParticipants", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "profileCompletePercentage", "columnName": "profileCompletePercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalBroadcasts", "columnName": "totalBroadcasts", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalLikes", "columnName": "totalLikes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.appSharesTarget", "columnName": "appSharesTarget", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.broadcastTarget", "columnName": "broadcastTarget", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.customersTarget", "columnName": "customersTarget", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.followersTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.huddlesTarget", "columnName": "huddles<PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.likesTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.participantsTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.minimumPpForFirstPayout", "columnName": "payout_minimumPpForFirstPayout", "affinity": "REAL", "notNull": false}, {"fieldPath": "payoutStatus.minimumPpForFourthPayout", "columnName": "payout_minimumPpForFourthPayout", "affinity": "REAL", "notNull": false}, {"fieldPath": "payoutStatus.minimumPpForSecondPayout", "columnName": "payout_minimumPpForSecondPayout", "affinity": "REAL", "notNull": false}, {"fieldPath": "payoutStatus.minimumPpForThirdPayout", "columnName": "payout_minimumPpForThirdPayout", "affinity": "REAL", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinAppSharesMonthly", "columnName": "payout_payoutMinAppSharesMonthly", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinBroadcasts", "columnName": "payout_payoutMinBroadcasts", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinDears", "columnName": "payout_payoutMinDears", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinHuddles", "columnName": "payout_payoutMinHuddles", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinLikes", "columnName": "payout_payoutMinLikes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinNoFans", "columnName": "payout_payoutMinNoFans", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinNoLikers", "columnName": "payout_payoutMinNoLikers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinPointsForNextReview", "columnName": "payout_payoutMinPointsForNextReview", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinTotalParticipantsInHuddles", "columnName": "payout_payoutMinTotalParticipantsInHuddles", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinTotalPostsByOthersParticipants", "columnName": "payout_payoutMinTotalPostsByOthersParticipants", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.refundablePp", "columnName": "payout_refundablePp", "affinity": "REAL", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["membership"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_task_one", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`about` TEXT, `dob` TEXT, `email` TEXT, `emailVerified` INTEGER, `gender` TEXT, `id` INTEGER NOT NULL, `membership` TEXT, `name` TEXT, `paypalId` TEXT, `paypalVerified` INTEGER, `phone` TEXT, `profileCompletePercentage` INTEGER NOT NULL, `profilePhoto` TEXT, `username` TEXT, `verified` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "about", "columnName": "about", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dob", "columnName": "dob", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "emailVerified", "columnName": "emailVerified", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paypalId", "columnName": "paypalId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paypalVerified", "columnName": "paypalVerified", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileCompletePercentage", "columnName": "profileCompletePercentage", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "profilePhoto", "columnName": "profilePhoto", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": false}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_business_operations_payout_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`forwardedTo` INTEGER, `forwardedToId` INTEGER, `id` INTEGER NOT NULL, `membership` TEXT, `name` TEXT, `profileImage` TEXT, `requestedDate` TEXT, `requestedPointsForReview` REAL, `status` TEXT, `statusId` INTEGER, `timeCreated` TEXT, `timeUpdated` TEXT, `userId` INTEGER, `verified` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "forwardedTo", "columnName": "forwardedTo", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "forwardedToId", "columnName": "forwardedToId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileImage", "columnName": "profileImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "requestedDate", "columnName": "requestedDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "requestedPointsForReview", "columnName": "requestedPointsForReview", "affinity": "REAL", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "statusId", "columnName": "statusId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_notifications", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `action` TEXT, `associateObjId` INTEGER, `body` TEXT, `category` TEXT, `delivered_time` TEXT, `highlightText` TEXT, `iconPath` TEXT, `isDeleted` INTEGER, `readTime` TEXT, `isViewed` TEXT, `normalText` TEXT, `receiverId` INTEGER, `senderId` INTEGER, `status` TEXT, `timeCreated` TEXT, `timeUpdated` TEXT, `title` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "action", "columnName": "action", "affinity": "TEXT", "notNull": false}, {"fieldPath": "associateObjId", "columnName": "associateObjId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "body", "columnName": "body", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deliveredTime", "columnName": "delivered_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "highlightText", "columnName": "highlightText", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iconPath", "columnName": "iconPath", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isDeleted", "columnName": "isDeleted", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "readTime", "columnName": "readTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "normalText", "columnName": "normalText", "affinity": "TEXT", "notNull": false}, {"fieldPath": "receiverId", "columnName": "receiverId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "senderId", "columnName": "senderId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_other_user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `stars` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `about` TEXT, `isSuperstar` INTEGER NOT NULL, `followerType` TEXT, `isStar` INTEGER NOT NULL, `muted` INTEGER, `total_broadcasts` INTEGER, `total_likers` INTEGER, `broadcast_likers_privacy` INTEGER, `last_seen` TEXT, `notification` TEXT, `sound_track` TEXT, `preview` INTEGER, `broadcast_received` INTEGER, `online` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "about", "columnName": "about", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is<PERSON><PERSON><PERSON><PERSON>", "columnName": "is<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "followerType", "columnName": "followerType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isStar", "columnName": "isStar", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalBroadcasts", "columnName": "total_broadcasts", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalLikers", "columnName": "total_likers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "likersPrivacy", "columnName": "broadcast_likers_privacy", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastSeen", "columnName": "last_seen", "affinity": "TEXT", "notNull": false}, {"fieldPath": "notification", "columnName": "notification", "affinity": "TEXT", "notNull": false}, {"fieldPath": "soundTrack", "columnName": "sound_track", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preview", "columnName": "preview", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "broadcastReceived", "columnName": "broadcast_received", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "online", "columnName": "online", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_huddles_post_comments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`commentId` TEXT NOT NULL, `huddleId` INTEGER NOT NULL, `messageId` TEXT NOT NULL, `created` TEXT, `message` TEXT, `isReported` INTEGER, `senderId` INTEGER, `senderDetails` TEXT, PRIMARY KEY(`commentId`))", "fields": [{"fieldPath": "commentId", "columnName": "commentId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddleId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageId", "columnName": "messageId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "created", "columnName": "created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isReported", "columnName": "isReported", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "senderId", "columnName": "senderId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "senderDetails", "columnName": "senderDetails", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["commentId"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '67c9c98838fffbee48d88cd0f676627d')"]}}