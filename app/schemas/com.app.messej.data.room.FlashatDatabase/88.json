{"formatVersion": 1, "database": {"version": 88, "identityHash": "6a85f7b71e494abb307cc24f94064789", "entities": [{"tableName": "fls_remotekeys", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`query` TEXT NOT NULL, `nextPage` TEXT, PRIMARY KEY(`query`))", "fields": [{"fieldPath": "query", "columnName": "query", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextPage", "columnName": "nextPage", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["query"]}}, {"tableName": "fls_huddles", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `about` TEXT, `thumbnail` TEXT, `group_photo` TEXT, `manager_premium_status` INTEGER, `category` TEXT, `created_by` TEXT, `private` INTEGER NOT NULL, `status` TEXT NOT NULL, `admin_status` TEXT, `user_status` TEXT NOT NULL, `total_members` INTEGER NOT NULL, `activity` TEXT, `time_created` TEXT, `time_updated` TEXT, `online_participants` INTEGER NOT NULL, `unread_count` INTEGER NOT NULL, `tribe` INTEGER NOT NULL DEFAULT 0, `updated_by` INTEGER NOT NULL, `participant_share` INTEGER NOT NULL, `invite_link` TEXT, `manager_id` INTEGER NOT NULL, `request_to_join` INTEGER NOT NULL, `requested_invited_count` INTEGER NOT NULL, `muted` INTEGER NOT NULL, `pinned` INTEGER NOT NULL, `role` TEXT, `sender_details` TEXT, `last_message` TEXT, `last_read_message` TEXT, `privacy` TEXT, `empowered_user_blocked` INTEGER NOT NULL DEFAULT 0, `huddle_type` TEXT NOT NULL, `huddle_show_type` TEXT DEFAULT 'NONE', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "about", "columnName": "about", "affinity": "TEXT"}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT"}, {"fieldPath": "groupPhoto", "columnName": "group_photo", "affinity": "TEXT"}, {"fieldPath": "managerPremium", "columnName": "manager_premium_status", "affinity": "INTEGER"}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT"}, {"fieldPath": "created<PERSON>y", "columnName": "created_by", "affinity": "TEXT"}, {"fieldPath": "isPrivate", "columnName": "private", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "adminStatus", "columnName": "admin_status", "affinity": "TEXT"}, {"fieldPath": "userStatus", "columnName": "user_status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalMembers", "columnName": "total_members", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activity", "columnName": "activity", "affinity": "TEXT"}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT"}, {"fieldPath": "onlineParticipants", "columnName": "online_participants", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unread_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isTribe", "columnName": "tribe", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "updatedBy", "columnName": "updated_by", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "participantShare", "columnName": "participant_share", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "inviteLink", "columnName": "invite_link", "affinity": "TEXT"}, {"fieldPath": "managerId", "columnName": "manager_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "requestToJoin", "columnName": "request_to_join", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "requestsAndInvites", "columnName": "requested_invited_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT"}, {"fieldPath": "senderDetails", "columnName": "sender_details", "affinity": "TEXT"}, {"fieldPath": "lastMessageInternal", "columnName": "last_message", "affinity": "TEXT"}, {"fieldPath": "lastReadMessage", "columnName": "last_read_message", "affinity": "TEXT"}, {"fieldPath": "privacy", "columnName": "privacy", "affinity": "TEXT"}, {"fieldPath": "empoweredUserBlocked", "columnName": "empowered_user_blocked", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "huddleType", "columnName": "huddle_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "involvement", "columnName": "huddle_show_type", "affinity": "TEXT", "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_huddle_interventions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `blocked_from_huddle` TEXT NOT NULL, `admin_invited` TEXT NOT NULL, `user_blocked` TEXT NOT NULL, `comment_banned` TEXT NOT NULL DEFAULT '[]', `post_banned` TEXT NOT NULL DEFAULT '[]', `reply_banned` TEXT NOT NULL DEFAULT '[]', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "huddleId", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "blocked_from_huddle", "affinity": "TEXT", "notNull": true}, {"fieldPath": "invitedToBeAdmin", "columnName": "admin_invited", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userBlocked", "columnName": "user_blocked", "affinity": "TEXT", "notNull": true}, {"fieldPath": "commentBanned", "columnName": "comment_banned", "affinity": "TEXT", "notNull": true, "defaultValue": "'[]'"}, {"fieldPath": "postBanned", "columnName": "post_banned", "affinity": "TEXT", "notNull": true, "defaultValue": "'[]'"}, {"fieldPath": "replyBanned", "columnName": "reply_banned", "affinity": "TEXT", "notNull": true, "defaultValue": "'[]'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_messages", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `last_message` TEXT, `last_message_id` TEXT, `sender` INTEGER NOT NULL, `receiver` INTEGER NOT NULL, `receiver_data` TEXT NOT NULL, `room_id` TEXT NOT NULL, `room_name` TEXT, `status` TEXT, `type` TEXT NOT NULL, `unread_count` INTEGER NOT NULL, `updated` TEXT, `is_ignored` INTEGER, `followed_by_each` INTEGER, `private_message_tab` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastMessageInternal", "columnName": "last_message", "affinity": "TEXT"}, {"fieldPath": "lastMessageId", "columnName": "last_message_id", "affinity": "TEXT"}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "receiverDetails", "columnName": "receiver_data", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomName", "columnName": "room_name", "affinity": "TEXT"}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT"}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unread_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updated", "columnName": "updated", "affinity": "TEXT"}, {"fieldPath": "isIgnored", "columnName": "is_ignored", "affinity": "INTEGER"}, {"fieldPath": "followedByMe", "columnName": "followed_by_each", "affinity": "INTEGER"}, {"fieldPath": "privateMessageTab", "columnName": "private_message_tab", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_messages_type", "unique": false, "columnNames": ["type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_type` ON `${TABLE_NAME}` (`type`)"}, {"name": "index_fls_messages_unread_count", "unique": false, "columnNames": ["unread_count"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_unread_count` ON `${TABLE_NAME}` (`unread_count`)"}, {"name": "index_fls_messages_updated", "unique": false, "columnNames": ["updated"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_updated` ON `${TABLE_NAME}` (`updated`)"}, {"name": "index_fls_messages_private_message_tab", "unique": false, "columnNames": ["private_message_tab"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_private_message_tab` ON `${TABLE_NAME}` (`private_message_tab`)"}]}, {"tableName": "fls_private_chat_room_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`canChat` INTEGER, `error` TEXT, `chatBlockedBySender` INTEGER, `userBlockedByReceiver` INTEGER, `userBlockedBySender` INTEGER, `chatDisabledBySender` INTEGER, `chatDisabledByReceiver` INTEGER, `followed_by_each` INTEGER, `is_banned` INTEGER, `is_blacklisted` INTEGER, `localId` TEXT NOT NULL, `participants` TEXT NOT NULL, `creator` INTEGER, `userBlockStatus` TEXT, `followedStatus` TEXT, `threadType` TEXT, `created` TEXT, `id` TEXT NOT NULL, `roomStatus` TEXT, `type` TEXT NOT NULL, `chatType` TEXT, PRIMARY KEY(`localId`))", "fields": [{"fieldPath": "canChat", "columnName": "canChat", "affinity": "INTEGER"}, {"fieldPath": "error", "columnName": "error", "affinity": "TEXT"}, {"fieldPath": "chatBlockedBySender", "columnName": "chatBlockedBySender", "affinity": "INTEGER"}, {"fieldPath": "userBlockedByReceiver", "columnName": "userBlockedByReceiver", "affinity": "INTEGER"}, {"fieldPath": "userBlockedBySender", "columnName": "userBlockedBySender", "affinity": "INTEGER"}, {"fieldPath": "chatDisabledBySender", "columnName": "chatDisabledBySender", "affinity": "INTEGER"}, {"fieldPath": "chatDisabledByReceiver", "columnName": "chatDisabledByReceiver", "affinity": "INTEGER"}, {"fieldPath": "followedByMe", "columnName": "followed_by_each", "affinity": "INTEGER"}, {"fieldPath": "isBannedOrSuspectedBan", "columnName": "is_banned", "affinity": "INTEGER"}, {"fieldPath": "isBlackListed", "columnName": "is_blacklisted", "affinity": "INTEGER"}, {"fieldPath": "localRoomId", "columnName": "localId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.participants", "columnName": "participants", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.creator", "columnName": "creator", "affinity": "INTEGER"}, {"fieldPath": "chatRoom.userBlockStatus", "columnName": "userBlockStatus", "affinity": "TEXT"}, {"fieldPath": "chatRoom.followedStatus", "columnName": "followedStatus", "affinity": "TEXT"}, {"fieldPath": "chatRoom.threadType", "columnName": "threadType", "affinity": "TEXT"}, {"fieldPath": "chatRoom.created", "columnName": "created", "affinity": "TEXT"}, {"fieldPath": "chatRoom.id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.roomStatus", "columnName": "roomStatus", "affinity": "TEXT"}, {"fieldPath": "chatRoom.type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.chatType", "columnName": "chatType", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["localId"]}}, {"tableName": "fls_huddles_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `sent` TEXT, `remover` TEXT, `deleted` INTEGER NOT NULL, `edited` INTEGER, `is_activity` INTEGER NOT NULL, `activity_meta` TEXT, `media` TEXT, `media_meta` TEXT, `message_type` TEXT, `read` TEXT, `receiver` INTEGER, `reply_to` TEXT, `sender` INTEGER NOT NULL, `sender_relation` TEXT, `sender_details` TEXT, `liked` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `huddle_id` TEXT NOT NULL, `huddle_type` TEXT NOT NULL, `total_likes` INTEGER NOT NULL, `total_gifts` INTEGER NOT NULL DEFAULT 0, `total_comments` INTEGER NOT NULL DEFAULT 0, `star_type` TEXT, `pinned` INTEGER, `has_mention` INTEGER NOT NULL DEFAULT 0, `color` TEXT, `mentioned_users` TEXT, `post_type` TEXT, `huddle_name` TEXT, `forward_id` TEXT, `reply_message_id` TEXT, `reply_message_sender_id` INTEGER, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rawMessage", "columnName": "message", "affinity": "TEXT"}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT"}, {"fieldPath": "sentTime", "columnName": "sent", "affinity": "TEXT"}, {"fieldPath": "remover", "columnName": "remover", "affinity": "TEXT"}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "edited", "columnName": "edited", "affinity": "INTEGER"}, {"fieldPath": "isActivity", "columnName": "is_activity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activityMeta", "columnName": "activity_meta", "affinity": "TEXT"}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT"}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT"}, {"fieldPath": "internalMessageType", "columnName": "message_type", "affinity": "TEXT"}, {"fieldPath": "read", "columnName": "read", "affinity": "TEXT"}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER"}, {"fieldPath": "replyTo", "columnName": "reply_to", "affinity": "TEXT"}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderRelation", "columnName": "sender_relation", "affinity": "TEXT"}, {"fieldPath": "senderDetails", "columnName": "sender_details", "affinity": "TEXT"}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleType", "columnName": "huddle_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalLikes", "columnName": "total_likes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalGifts", "columnName": "total_gifts", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "totalComments", "columnName": "total_comments", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "starType", "columnName": "star_type", "affinity": "TEXT"}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER"}, {"fieldPath": "hasMention", "columnName": "has_mention", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "chatTextColor", "columnName": "color", "affinity": "TEXT"}, {"fieldPath": "mentionedUsers", "columnName": "mentioned_users", "affinity": "TEXT"}, {"fieldPath": "postType", "columnName": "post_type", "affinity": "TEXT"}, {"fieldPath": "huddleName", "columnName": "huddle_name", "affinity": "TEXT"}, {"fieldPath": "forwardId", "columnName": "forward_id", "affinity": "TEXT"}, {"fieldPath": "replyMessageId", "columnName": "reply_message_id", "affinity": "TEXT"}, {"fieldPath": "replyMessageSenderId", "columnName": "reply_message_sender_id", "affinity": "INTEGER"}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_chats_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_chats_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}]}, {"tableName": "fls_messages_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `sent` TEXT, `deleted` INTEGER NOT NULL, `is_activity` INTEGER NOT NULL, `activity_meta` TEXT, `media` TEXT, `media_meta` TEXT, `message_type` TEXT, `read` TEXT, `receiver` INTEGER NOT NULL, `reply_to` TEXT, `sender` INTEGER NOT NULL, `liked` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `blocked` INTEGER NOT NULL, `user_id` INTEGER NOT NULL, `color` TEXT, `forward_id` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rawMessage", "columnName": "message", "affinity": "TEXT"}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT"}, {"fieldPath": "sentTime", "columnName": "sent", "affinity": "TEXT"}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActivity", "columnName": "is_activity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activityMeta", "columnName": "activity_meta", "affinity": "TEXT"}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT"}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT"}, {"fieldPath": "internalMessageType", "columnName": "message_type", "affinity": "TEXT"}, {"fieldPath": "read", "columnName": "read", "affinity": "TEXT"}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "replyTo", "columnName": "reply_to", "affinity": "TEXT"}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "blocked", "columnName": "blocked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chatTextColor", "columnName": "color", "affinity": "TEXT"}, {"fieldPath": "forwardId", "columnName": "forward_id", "affinity": "TEXT"}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_messages_chats_room_id", "unique": false, "columnNames": ["room_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_chats_room_id` ON `${TABLE_NAME}` (`room_id`)"}, {"name": "index_fls_messages_chats_created", "unique": false, "columnNames": ["created"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_chats_created` ON `${TABLE_NAME}` (`created`)"}]}, {"tableName": "fls_broadcast_message", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `broadcast_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `deleted` INTEGER NOT NULL, `media` TEXT, `media_meta` TEXT, `message_type` TEXT, `broadcaster` INTEGER NOT NULL, `subscriber` INTEGER NOT NULL, `broadcast_type` TEXT NOT NULL, `liked` INTEGER NOT NULL, `starred` INTEGER NOT NULL, `total_likes` INTEGER NOT NULL, `color` TEXT, `forward_id` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "messageId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "broadcastId", "columnName": "broadcast_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rawMessage", "columnName": "message", "affinity": "TEXT"}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT"}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT"}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT"}, {"fieldPath": "internalMessageType", "columnName": "message_type", "affinity": "TEXT"}, {"fieldPath": "broadcaster", "columnName": "broadcaster", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "subscriber", "columnName": "subscriber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "broadcastMode", "columnName": "broadcast_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "starred", "columnName": "starred", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalLikes", "columnName": "total_likes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chatTextColor", "columnName": "color", "affinity": "TEXT"}, {"fieldPath": "forwardId", "columnName": "forward_id", "affinity": "TEXT"}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_broadcast_message_broadcaster", "unique": false, "columnNames": ["broadcaster"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_broadcaster` ON `${TABLE_NAME}` (`broadcaster`)"}, {"name": "index_fls_broadcast_message_broadcast_type", "unique": false, "columnNames": ["broadcast_type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_broadcast_type` ON `${TABLE_NAME}` (`broadcast_type`)"}, {"name": "index_fls_broadcast_message_created", "unique": false, "columnNames": ["created"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_created` ON `${TABLE_NAME}` (`created`)"}, {"name": "index_fls_broadcast_message_starred", "unique": false, "columnNames": ["starred"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_starred` ON `${TABLE_NAME}` (`starred`)"}, {"name": "index_fls_broadcast_message_message", "unique": false, "columnNames": ["message"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_message` ON `${TABLE_NAME}` (`message`)"}]}, {"tableName": "fls_huddles_reported_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT, `message` TEXT, `created` TEXT NOT NULL, `message_sent` TEXT, `time_updated` TEXT, `media` TEXT, `media_meta` TEXT, `message_type` TEXT, `sender_id` INTEGER NOT NULL, `report_id` INTEGER NOT NULL, `reports_count` INTEGER NOT NULL, `deletedUser` INTEGER NOT NULL, `sender_membership` TEXT, `sender_name` TEXT NOT NULL, `sender_role` TEXT, `sender_username` TEXT, `thumbnail` TEXT, `status` TEXT, `huddle_id` INTEGER NOT NULL, `verified` INTEGER NOT NULL, `color` TEXT, `forward_id` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT"}, {"fieldPath": "rawMessage", "columnName": "message", "affinity": "TEXT"}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sentTime", "columnName": "message_sent", "affinity": "TEXT"}, {"fieldPath": "updatedTime", "columnName": "time_updated", "affinity": "TEXT"}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT"}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT"}, {"fieldPath": "internalMessageType", "columnName": "message_type", "affinity": "TEXT"}, {"fieldPath": "sender", "columnName": "sender_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportId", "columnName": "report_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportsCount", "columnName": "reports_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userDeleted", "columnName": "deletedUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderMembership", "columnName": "sender_membership", "affinity": "TEXT"}, {"fieldPath": "sender<PERSON>ame", "columnName": "sender_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "senderRole", "columnName": "sender_role", "affinity": "TEXT"}, {"fieldPath": "senderUsername", "columnName": "sender_username", "affinity": "TEXT"}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT"}, {"fieldPath": "reportStatus", "columnName": "status", "affinity": "TEXT"}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chatTextColor", "columnName": "color", "affinity": "TEXT"}, {"fieldPath": "forwardId", "columnName": "forward_id", "affinity": "TEXT"}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_reported_chats_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_reported_chats_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}]}, {"tableName": "fls_huddles_reported_comments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `huddle_id` INTEGER NOT NULL, `post_id` TEXT NOT NULL, `created` TEXT NOT NULL, `comment` TEXT, `media` TEXT, `sender_name` TEXT NOT NULL, `report_id` INTEGER NOT NULL, `reports_count` INTEGER NOT NULL, `status` TEXT, `time_updated` TEXT, `deletedUser` INTEGER NOT NULL, `sender_membership` TEXT, `sender_id` INTEGER NOT NULL, `thumbnail` TEXT, PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "commentId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageId", "columnName": "post_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "created", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "comment", "columnName": "comment", "affinity": "TEXT"}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT"}, {"fieldPath": "sender<PERSON>ame", "columnName": "sender_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "reportId", "columnName": "report_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportsCount", "columnName": "reports_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportStatus", "columnName": "status", "affinity": "TEXT"}, {"fieldPath": "updatedTime", "columnName": "time_updated", "affinity": "TEXT"}, {"fieldPath": "userDeleted", "columnName": "deletedUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderMembership", "columnName": "sender_membership", "affinity": "TEXT"}, {"fieldPath": "senderId", "columnName": "sender_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_reported_comments_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_reported_comments_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}]}, {"tableName": "fls_media", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `key` TEXT NOT NULL, `name` TEXT NOT NULL, `path` TEXT NOT NULL, `mediaType` TEXT NOT NULL, `uploadState` TEXT NOT NULL, PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "mediaType", "columnName": "mediaType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uploadState", "columnName": "uploadState", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_media_message_id", "unique": true, "columnNames": ["message_id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_media_message_id` ON `${TABLE_NAME}` (`message_id`)"}, {"name": "index_fls_media_key", "unique": true, "columnNames": ["key"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_media_key` ON `${TABLE_NAME}` (`key`)"}]}, {"tableName": "fls_huddles_stickers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`category_name` TEXT, `id` INTEGER, `media_url` TEXT, `s3_key` TEXT, `time_created` TEXT, `time_updated` TEXT, `unicode` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "categoryName", "columnName": "category_name", "affinity": "TEXT"}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER"}, {"fieldPath": "mediaUrl", "columnName": "media_url", "affinity": "TEXT"}, {"fieldPath": "s3Key", "columnName": "s3_key", "affinity": "TEXT"}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT"}, {"fieldPath": "unicode", "columnName": "unicode", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_flash", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `caption` TEXT, `media_meta` TEXT NOT NULL, `userId` INTEGER NOT NULL, `sender_detail` TEXT, `share_to` TEXT NOT NULL, `video_url` TEXT NOT NULL, `thumbnail` TEXT NOT NULL, `category` TEXT, `category_id` INTEGER, `comment_disabled` INTEGER NOT NULL, `isLiked` INTEGER NOT NULL, `likes_count` INTEGER NOT NULL, `share_count` INTEGER NOT NULL, `views_count` INTEGER NOT NULL, `comments_count` INTEGER NOT NULL, `gifts_count` INTEGER NOT NULL DEFAULT 0, `is_reported` INTEGER NOT NULL, `is_blocked` INTEGER NOT NULL DEFAULT 0, `is_deleted` INTEGER NOT NULL DEFAULT 0, `time_created` TEXT, `time_updated` TEXT, `share_link` TEXT, `flash_type` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "caption", "columnName": "caption", "affinity": "TEXT"}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderDetails", "columnName": "sender_detail", "affinity": "TEXT"}, {"fieldPath": "shareTo", "columnName": "share_to", "affinity": "TEXT", "notNull": true}, {"fieldPath": "videoUrl", "columnName": "video_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnail", "affinity": "TEXT", "notNull": true}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT"}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "INTEGER"}, {"fieldPath": "commentDisabled", "columnName": "comment_disabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isLiked", "columnName": "isLiked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likeCount", "columnName": "likes_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "shareCount", "columnName": "share_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "viewCount", "columnName": "views_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "commentCount", "columnName": "comments_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "giftCount", "columnName": "gifts_count", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isReported", "columnName": "is_reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isBlocked", "columnName": "is_blocked", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isDeleted", "columnName": "is_deleted", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT"}, {"fieldPath": "shareLink", "columnName": "share_link", "affinity": "TEXT"}, {"fieldPath": "flashType", "columnName": "flash_type", "affinity": "TEXT"}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_flash_video", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`flashId` TEXT NOT NULL, `path` TEXT NOT NULL, `key` TEXT NOT NULL, `meta` TEXT NOT NULL, `uploadState` TEXT NOT NULL, PRIMARY KEY(`flashId`))", "fields": [{"fieldPath": "flashId", "columnName": "flashId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "meta", "columnName": "meta", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uploadState", "columnName": "uploadState", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["flashId"]}}, {"tableName": "fls_stars_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `stars` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `superstar_id` INTEGER, `last_broadcast_time` TEXT, `unread_messages_count` INTEGER NOT NULL, `muted` INTEGER NOT NULL, `pinned` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `archived` INTEGER NOT NULL, `archived_pinned` TEXT, `hidden` INTEGER NOT NULL, `citizenship` TEXT, `isSuperStar` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT"}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "superstarId", "columnName": "superstar_id", "affinity": "INTEGER"}, {"fieldPath": "lastBroadcastTime", "columnName": "last_broadcast_time", "affinity": "TEXT"}, {"fieldPath": "unreadMessagesCount", "columnName": "unread_messages_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "archived", "columnName": "archived", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "archivedPinned", "columnName": "archived_pinned", "affinity": "TEXT"}, {"fieldPath": "hidden", "columnName": "hidden", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "citizenship", "columnName": "citizenship", "affinity": "TEXT"}, {"fieldPath": "isSuperStar", "columnName": "isSuperStar", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_stars_list_last_broadcast_time", "unique": false, "columnNames": ["last_broadcast_time"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_last_broadcast_time` ON `${TABLE_NAME}` (`last_broadcast_time`)"}, {"name": "index_fls_stars_list_isSuperStar", "unique": false, "columnNames": ["isSuperStar"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_isSuperStar` ON `${TABLE_NAME}` (`isSuperStar`)"}, {"name": "index_fls_stars_list_membership", "unique": false, "columnNames": ["membership"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_membership` ON `${TABLE_NAME}` (`membership`)"}]}, {"tableName": "fls_broadcast_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `stars` INTEGER NOT NULL, `broadcastLikersPrivacy` INTEGER, `isFollowed` INTEGER, `relative_type` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT"}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "broadcastLikersPrivacy", "columnName": "broadcastLikersPrivacy", "affinity": "INTEGER"}, {"fieldPath": "isFollowed", "columnName": "isFollowed", "affinity": "INTEGER"}, {"fieldPath": "relativeType", "columnName": "relative_type", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_user_nick_names", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `nickName` TEXT, `timeCreated` TEXT, `timeUpdated` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT"}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}}, {"tableName": "fls_recent_search", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `keyword` TEXT NOT NULL, `timeCreated` TEXT, `screenType` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER"}, {"fieldPath": "keyword", "columnName": "keyword", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT"}, {"fieldPath": "screenType", "columnName": "screenType", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}, {"tableName": "fls_polls_participant_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`deletedUser` INTEGER NOT NULL, `membership` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `userId` INTEGER NOT NULL DEFAULT 0, `verified` INTEGER NOT NULL, `poll_id` INTEGER NOT NULL, PRIMARY KEY(`poll_id`))", "fields": [{"fieldPath": "deletedUser", "columnName": "deletedUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT"}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pollID", "columnName": "poll_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["poll_id"]}}, {"tableName": "fls_business_statements", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`accountName` TEXT NOT NULL, `address` TEXT, `availableBalance` REAL, `balanceBroughtForward` REAL, `beneficiaryName` TEXT, `grandTotal` REAL, `grandTotalGenerated` REAL, `grandTotalRefunded` REAL, `grandTotalRewarded` REAL, `grandTotalWithdrawn` REAL, `statementDate` TEXT, `thisMonthGenerated` REAL, `thisMonthNet` REAL, `thisMonthRefunded` REAL, `thisMonthRewarded` REAL, `thisMonthTotal` REAL, `thisMonthWithdrawn` REAL, `totalPendingPP` REAL, `balanceCarryMonth` TEXT, `isFlaxIncrement` INTEGER, `flaxRatePercentage` REAL, `receivedFlax` REAL, `sentFlax` REAL, `purchasedFlax` REAL, `soldGiftFlax` REAL, `purchasedGift<PERSON>lax` REAL, `grandTotalDebit` REAL, `podiumCameraPurchase` REAL, `cancelledDeductions` REAL, `local_id` INTEGER NOT NULL, PRIMARY KEY(`local_id`))", "fields": [{"fieldPath": "accountName", "columnName": "accountName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "address", "columnName": "address", "affinity": "TEXT"}, {"fieldPath": "availableBalance", "columnName": "availableBalance", "affinity": "REAL"}, {"fieldPath": "balanceBroughtForward", "columnName": "balanceBroughtForward", "affinity": "REAL"}, {"fieldPath": "beneficiary<PERSON><PERSON>", "columnName": "beneficiary<PERSON><PERSON>", "affinity": "TEXT"}, {"fieldPath": "grandTotal", "columnName": "grandTotal", "affinity": "REAL"}, {"fieldPath": "grandTotalGenerated", "columnName": "grandTotalGenerated", "affinity": "REAL"}, {"fieldPath": "grandTotalRefunded", "columnName": "grandTotalRefunded", "affinity": "REAL"}, {"fieldPath": "grandTotalRewarded", "columnName": "grandTotalRewarded", "affinity": "REAL"}, {"fieldPath": "grandTotalWithdrawn", "columnName": "grandTotalWithdrawn", "affinity": "REAL"}, {"fieldPath": "statementDate", "columnName": "statementDate", "affinity": "TEXT"}, {"fieldPath": "thisMonthGenerated", "columnName": "thisMonthGenerated", "affinity": "REAL"}, {"fieldPath": "thisMonthNet", "columnName": "thisMonthNet", "affinity": "REAL"}, {"fieldPath": "thisMonthRefunded", "columnName": "thisMonthRefunded", "affinity": "REAL"}, {"fieldPath": "thisMonthRewarded", "columnName": "thisMonthRewarded", "affinity": "REAL"}, {"fieldPath": "thisMonthTotal", "columnName": "thisMonthTotal", "affinity": "REAL"}, {"fieldPath": "thisMonthWithdrawn", "columnName": "thisMonthWithdrawn", "affinity": "REAL"}, {"fieldPath": "totalPendingPP", "columnName": "totalPendingPP", "affinity": "REAL"}, {"fieldPath": "balance<PERSON><PERSON>ry<PERSON><PERSON><PERSON>", "columnName": "balance<PERSON><PERSON>ry<PERSON><PERSON><PERSON>", "affinity": "TEXT"}, {"fieldPath": "isFlaxIncrement", "columnName": "isFlaxIncrement", "affinity": "INTEGER"}, {"fieldPath": "flaxRatePercentage", "columnName": "flaxRatePercentage", "affinity": "REAL"}, {"fieldPath": "receivedFlax", "columnName": "receivedFlax", "affinity": "REAL"}, {"fieldPath": "sentFlax", "columnName": "sentFlax", "affinity": "REAL"}, {"fieldPath": "purchasedFlax", "columnName": "purchasedFlax", "affinity": "REAL"}, {"fieldPath": "soldGiftFlax", "columnName": "soldGiftFlax", "affinity": "REAL"}, {"fieldPath": "purchasedGiftFlax", "columnName": "purchasedGiftFlax", "affinity": "REAL"}, {"fieldPath": "grandTotalDebit", "columnName": "grandTotalDebit", "affinity": "REAL"}, {"fieldPath": "podiumCameraPurchase", "columnName": "podiumCameraPurchase", "affinity": "REAL"}, {"fieldPath": "cancelledDeductions", "columnName": "cancelledDeductions", "affinity": "REAL"}, {"fieldPath": "localId", "columnName": "local_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["local_id"]}}, {"tableName": "fls_business_operations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`appSharesCount` INTEGER, `dears` INTEGER, `dearsToday` INTEGER, `newAppSharePercentage` INTEGER, `newBroadcastPercentage` INTEGER, `newCustomersPercentage` INTEGER, `newFollowersPercentage` INTEGER, `newFansPercentage` INTEGER, `newHuddlePercentage` INTEGER, `newLikesPercentage` INTEGER, `newParticipantPercentage` INTEGER, `fans` INTEGER, `fansToday` INTEGER, `huddlesCount` INTEGER, `likers` INTEGER, `rating` INTEGER, `appReviewStatus` TEXT, `followers` INTEGER, `membership` TEXT NOT NULL, `minimumPpRequired` REAL, `participantCount` INTEGER, `payoutEligiblity` INTEGER, `percentOfAppShares` INTEGER, `percentOfFollowers` INTEGER, `percentOfLikes` INTEGER, `percentOfParticipants` INTEGER, `profileCompletePercentage` INTEGER, `totalBroadcasts` INTEGER, `totalLikes` INTEGER, `appSharesTarget` INTEGER, `broadcastTarget` INTEGER, `customersTarget` INTEGER, `followersTarget` INTEGER, `huddlesTarget` INTEGER, `likesTarget` INTEGER, `participantsTarget` INTEGER, `fansTarget` INTEGER, `payout_minimumPpForFirstPayout` REAL, `payout_minimumPpForFourthPayout` REAL, `payout_minimumPpForSecondPayout` REAL, `payout_minimumPpForThirdPayout` REAL, `payout_payoutMinAppSharesMonthly` INTEGER, `payout_payoutMinBroadcasts` INTEGER, `payout_payoutMinDears` INTEGER, `payout_payoutMinHuddles` INTEGER, `payout_payoutMinLikes` INTEGER, `payout_payoutMinNoFans` INTEGER, `payout_payoutMinNoLikers` INTEGER, `payout_payoutMinPointsForNextReview` INTEGER, `payout_payoutMinTotalParticipantsInHuddles` INTEGER, `payout_payoutMinTotalPostsByOthersParticipants` INTEGER, `payout_refundablePp` REAL, PRIMARY KEY(`membership`))", "fields": [{"fieldPath": "appSharesCount", "columnName": "appSharesCount", "affinity": "INTEGER"}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER"}, {"fieldPath": "dears<PERSON><PERSON>y", "columnName": "dears<PERSON><PERSON>y", "affinity": "INTEGER"}, {"fieldPath": "newAppSharePercentage", "columnName": "newAppSharePercentage", "affinity": "INTEGER"}, {"fieldPath": "newBroadcastPercentage", "columnName": "newBroadcastPercentage", "affinity": "INTEGER"}, {"fieldPath": "newCustomersPercentage", "columnName": "newCustomersPercentage", "affinity": "INTEGER"}, {"fieldPath": "newFollowersPercentage", "columnName": "newFollowersPercentage", "affinity": "INTEGER"}, {"fieldPath": "newFansPercentage", "columnName": "newFansPercentage", "affinity": "INTEGER"}, {"fieldPath": "newHuddlePercentage", "columnName": "newHuddlePercentage", "affinity": "INTEGER"}, {"fieldPath": "newLikesPercentage", "columnName": "newLikesPercentage", "affinity": "INTEGER"}, {"fieldPath": "newParticipantPercentage", "columnName": "newParticipantPercentage", "affinity": "INTEGER"}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER"}, {"fieldPath": "fansToday", "columnName": "fansToday", "affinity": "INTEGER"}, {"fieldPath": "huddlesCount", "columnName": "huddlesCount", "affinity": "INTEGER"}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER"}, {"fieldPath": "rating", "columnName": "rating", "affinity": "INTEGER"}, {"fieldPath": "appReviewStatus", "columnName": "appReviewStatus", "affinity": "TEXT"}, {"fieldPath": "followers", "columnName": "followers", "affinity": "INTEGER"}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "minimumPpRequired", "columnName": "minimumPpRequired", "affinity": "REAL"}, {"fieldPath": "participantCount", "columnName": "participantCount", "affinity": "INTEGER"}, {"fieldPath": "payoutEligiblity", "columnName": "payoutEligiblity", "affinity": "INTEGER"}, {"fieldPath": "percentOfAppShares", "columnName": "percentOfAppShares", "affinity": "INTEGER"}, {"fieldPath": "percentOfFollowers", "columnName": "percentOfFollowers", "affinity": "INTEGER"}, {"fieldPath": "percentOfLikes", "columnName": "percentOfLikes", "affinity": "INTEGER"}, {"fieldPath": "percentOfParticipants", "columnName": "percentOfParticipants", "affinity": "INTEGER"}, {"fieldPath": "profileCompletePercentage", "columnName": "profileCompletePercentage", "affinity": "INTEGER"}, {"fieldPath": "totalBroadcasts", "columnName": "totalBroadcasts", "affinity": "INTEGER"}, {"fieldPath": "totalLikes", "columnName": "totalLikes", "affinity": "INTEGER"}, {"fieldPath": "requirementTargets.appSharesTarget", "columnName": "appSharesTarget", "affinity": "INTEGER"}, {"fieldPath": "requirementTargets.broadcastTarget", "columnName": "broadcastTarget", "affinity": "INTEGER"}, {"fieldPath": "requirementTargets.customersTarget", "columnName": "customersTarget", "affinity": "INTEGER"}, {"fieldPath": "requirementTargets.followersTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER"}, {"fieldPath": "requirementTargets.huddlesTarget", "columnName": "huddles<PERSON><PERSON><PERSON>", "affinity": "INTEGER"}, {"fieldPath": "requirementTargets.likesTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER"}, {"fieldPath": "requirementTargets.participantsTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER"}, {"fieldPath": "requirementTargets.fansTarget", "columnName": "fansTarget", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.minimumPpForFirstPayout", "columnName": "payout_minimumPpForFirstPayout", "affinity": "REAL"}, {"fieldPath": "payoutStatus.minimumPpForFourthPayout", "columnName": "payout_minimumPpForFourthPayout", "affinity": "REAL"}, {"fieldPath": "payoutStatus.minimumPpForSecondPayout", "columnName": "payout_minimumPpForSecondPayout", "affinity": "REAL"}, {"fieldPath": "payoutStatus.minimumPpForThirdPayout", "columnName": "payout_minimumPpForThirdPayout", "affinity": "REAL"}, {"fieldPath": "payoutStatus.payoutMinAppSharesMonthly", "columnName": "payout_payoutMinAppSharesMonthly", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinBroadcasts", "columnName": "payout_payoutMinBroadcasts", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinDears", "columnName": "payout_payoutMinDears", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinHuddles", "columnName": "payout_payoutMinHuddles", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinLikes", "columnName": "payout_payoutMinLikes", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinNoFans", "columnName": "payout_payoutMinNoFans", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinNoLikers", "columnName": "payout_payoutMinNoLikers", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinPointsForNextReview", "columnName": "payout_payoutMinPointsForNextReview", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinTotalParticipantsInHuddles", "columnName": "payout_payoutMinTotalParticipantsInHuddles", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.payoutMinTotalPostsByOthersParticipants", "columnName": "payout_payoutMinTotalPostsByOthersParticipants", "affinity": "INTEGER"}, {"fieldPath": "payoutStatus.refundablePp", "columnName": "payout_refundablePp", "affinity": "REAL"}], "primaryKey": {"autoGenerate": false, "columnNames": ["membership"]}}, {"tableName": "fls_task_one", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`about` TEXT, `dob` TEXT, `email` TEXT, `emailVerified` INTEGER, `gender` TEXT, `id` INTEGER NOT NULL, `membership` TEXT, `name` TEXT, `paypalId` TEXT, `paypalVerified` INTEGER, `phone` TEXT, `profileCompletePercentage` INTEGER NOT NULL, `profilePhoto` TEXT, `username` TEXT, `verified` INTEGER, `totalPayoutsProcessed` INTEGER, `home_timeCreated` TEXT, `home_timeUpdated` TEXT, `home_id` INTEGER, `home_recipientName` TEXT, `home_addressLine1` TEXT, `home_addressLine2` TEXT, `home_addressLine3` TEXT, `home_zipCode` TEXT, `home_city` TEXT, `home_country` TEXT, `home_phone` TEXT, `home_countryCode` TEXT, PRIMARY <PERSON>EY(`id`))", "fields": [{"fieldPath": "about", "columnName": "about", "affinity": "TEXT"}, {"fieldPath": "dob", "columnName": "dob", "affinity": "TEXT"}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT"}, {"fieldPath": "emailVerified", "columnName": "emailVerified", "affinity": "INTEGER"}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT"}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT"}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT"}, {"fieldPath": "paypalId", "columnName": "paypalId", "affinity": "TEXT"}, {"fieldPath": "paypalVerified", "columnName": "paypalVerified", "affinity": "INTEGER"}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT"}, {"fieldPath": "profileCompletePercentage", "columnName": "profileCompletePercentage", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "profilePhoto", "columnName": "profilePhoto", "affinity": "TEXT"}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT"}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER"}, {"fieldPath": "totalPayoutsProcessed", "columnName": "totalPayoutsProcessed", "affinity": "INTEGER"}, {"fieldPath": "homeAddress.timeCreated", "columnName": "home_timeCreated", "affinity": "TEXT"}, {"fieldPath": "homeAddress.timeUpdated", "columnName": "home_timeUpdated", "affinity": "TEXT"}, {"fieldPath": "homeAddress.id", "columnName": "home_id", "affinity": "INTEGER"}, {"fieldPath": "homeAddress.recipientName", "columnName": "home_recipientName", "affinity": "TEXT"}, {"fieldPath": "homeAddress.addressLine1", "columnName": "home_addressLine1", "affinity": "TEXT"}, {"fieldPath": "homeAddress.addressLine2", "columnName": "home_addressLine2", "affinity": "TEXT"}, {"fieldPath": "homeAddress.addressLine3", "columnName": "home_addressLine3", "affinity": "TEXT"}, {"fieldPath": "homeAddress.zipCode", "columnName": "home_zipCode", "affinity": "TEXT"}, {"fieldPath": "homeAddress.city", "columnName": "home_city", "affinity": "TEXT"}, {"fieldPath": "homeAddress.country", "columnName": "home_country", "affinity": "TEXT"}, {"fieldPath": "homeAddress.phone", "columnName": "home_phone", "affinity": "TEXT"}, {"fieldPath": "homeAddress.countryCode", "columnName": "home_countryCode", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_business_operations_payout_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`forwardedTo` INTEGER, `forwardedToId` INTEGER, `id` INTEGER NOT NULL, `membership` TEXT, `name` TEXT, `profileImage` TEXT, `requestedDate` TEXT, `requestedPointsForReview` REAL, `status` TEXT, `statusId` INTEGER, `timeCreated` TEXT, `timeUpdated` TEXT, `userId` INTEGER, `verified` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "forwardedTo", "columnName": "forwardedTo", "affinity": "INTEGER"}, {"fieldPath": "forwardedToId", "columnName": "forwardedToId", "affinity": "INTEGER"}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT"}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT"}, {"fieldPath": "profileImage", "columnName": "profileImage", "affinity": "TEXT"}, {"fieldPath": "requestedDate", "columnName": "requestedDate", "affinity": "TEXT"}, {"fieldPath": "requestedPointsForReview", "columnName": "requestedPointsForReview", "affinity": "REAL"}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT"}, {"fieldPath": "statusId", "columnName": "statusId", "affinity": "INTEGER"}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT"}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER"}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_notifications", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `action` TEXT, `associateObjId` INTEGER, `body` TEXT, `category` TEXT, `delivered_time` TEXT, `highlightText` TEXT, `iconPath` TEXT, `isDeleted` INTEGER, `readTime` TEXT, `isViewed` TEXT, `normalText` TEXT, `receiverId` INTEGER, `senderId` INTEGER, `status` TEXT, `timeCreated` TEXT, `timeUpdated` TEXT, `title` TEXT, `is_private` INTEGER, `associate_data` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "action", "columnName": "action", "affinity": "TEXT"}, {"fieldPath": "associateObjId", "columnName": "associateObjId", "affinity": "INTEGER"}, {"fieldPath": "body", "columnName": "body", "affinity": "TEXT"}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT"}, {"fieldPath": "deliveredTime", "columnName": "delivered_time", "affinity": "TEXT"}, {"fieldPath": "highlightText", "columnName": "highlightText", "affinity": "TEXT"}, {"fieldPath": "iconPath", "columnName": "iconPath", "affinity": "TEXT"}, {"fieldPath": "isDeleted", "columnName": "isDeleted", "affinity": "INTEGER"}, {"fieldPath": "readTime", "columnName": "readTime", "affinity": "TEXT"}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT"}, {"fieldPath": "normalText", "columnName": "normalText", "affinity": "TEXT"}, {"fieldPath": "receiverId", "columnName": "receiverId", "affinity": "INTEGER"}, {"fieldPath": "senderId", "columnName": "senderId", "affinity": "INTEGER"}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT"}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT"}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT"}, {"fieldPath": "isPrivate", "columnName": "is_private", "affinity": "INTEGER"}, {"fieldPath": "assosiateData", "columnName": "associate_data", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_subscription_details", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`currency` TEXT NOT NULL, `payment_mode` TEXT NOT NULL, `price` INTEGER NOT NULL, `restoring_purchases` INTEGER NOT NULL, `timezone` TEXT NOT NULL, `type` TEXT NOT NULL, `id` INTEGER PRIMARY KEY AUTOINCREMENT, `userid` INTEGER NOT NULL, `flax` INTEGER, `coins` INTEGER, `additionalColumn` TEXT, `purchase_type` TEXT, `payload_developerPayloadAndroid` TEXT, `payload_isAcknowledgedAndroid` INTEGER, `payload_obfuscatedAccountIdAndroid` TEXT, `payload_obfuscatedProfileIdAndroid` TEXT, `payload_orderId` TEXT, `payload_packageNameAndroid` TEXT, `payload_productId` TEXT, `payload_purchaseStateAndroid` INTEGER, `payload_purchaseToken` TEXT, `payload_signatureAndroid` TEXT, `payload_transactionDate` INTEGER, `payload_transactionId` TEXT, `payload_receipt_acknowledged` INTEGER, `payload_receipt_orderId` TEXT, `payload_receipt_packageName` TEXT, `payload_receipt_productId` TEXT, `payload_receipt_purchaseState` INTEGER, `payload_receipt_purchaseTime` INTEGER, `payload_receipt_purchaseToken` TEXT)", "fields": [{"fieldPath": "currency", "columnName": "currency", "affinity": "TEXT", "notNull": true}, {"fieldPath": "payment_mode", "columnName": "payment_mode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "price", "columnName": "price", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "restoring_purchases", "columnName": "restoring_purchases", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timezone", "columnName": "timezone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER"}, {"fieldPath": "userid", "columnName": "userid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "flaxRate", "columnName": "flax", "affinity": "INTEGER"}, {"fieldPath": "coins", "columnName": "coins", "affinity": "INTEGER"}, {"fieldPath": "additionalColumn", "columnName": "additionalColumn", "affinity": "TEXT"}, {"fieldPath": "purchase_type", "columnName": "purchase_type", "affinity": "TEXT"}, {"fieldPath": "payload.developerPayloadAndroid", "columnName": "payload_developerPayloadAndroid", "affinity": "TEXT"}, {"fieldPath": "payload.isAcknowledgedAndroid", "columnName": "payload_isAcknowledgedAndroid", "affinity": "INTEGER"}, {"fieldPath": "payload.obfuscatedAccountIdAndroid", "columnName": "payload_obfuscatedAccountIdAndroid", "affinity": "TEXT"}, {"fieldPath": "payload.obfuscatedProfileIdAndroid", "columnName": "payload_obfuscatedProfileIdAndroid", "affinity": "TEXT"}, {"fieldPath": "payload.orderId", "columnName": "payload_orderId", "affinity": "TEXT"}, {"fieldPath": "payload.packageNameAndroid", "columnName": "payload_packageNameAndroid", "affinity": "TEXT"}, {"fieldPath": "payload.productId", "columnName": "payload_productId", "affinity": "TEXT"}, {"fieldPath": "payload.purchaseStateAndroid", "columnName": "payload_purchaseStateAndroid", "affinity": "INTEGER"}, {"fieldPath": "payload.purchaseToken", "columnName": "payload_purchaseToken", "affinity": "TEXT"}, {"fieldPath": "payload.signatureAndroid", "columnName": "payload_signatureAndroid", "affinity": "TEXT"}, {"fieldPath": "payload.transactionDate", "columnName": "payload_transactionDate", "affinity": "INTEGER"}, {"fieldPath": "payload.transactionId", "columnName": "payload_transactionId", "affinity": "TEXT"}, {"fieldPath": "payload.transactionReceipt.acknowledged", "columnName": "payload_receipt_acknowledged", "affinity": "INTEGER"}, {"fieldPath": "payload.transactionReceipt.orderId", "columnName": "payload_receipt_orderId", "affinity": "TEXT"}, {"fieldPath": "payload.transactionReceipt.packageName", "columnName": "payload_receipt_packageName", "affinity": "TEXT"}, {"fieldPath": "payload.transactionReceipt.productId", "columnName": "payload_receipt_productId", "affinity": "TEXT"}, {"fieldPath": "payload.transactionReceipt.purchaseState", "columnName": "payload_receipt_purchaseState", "affinity": "INTEGER"}, {"fieldPath": "payload.transactionReceipt.purchaseTime", "columnName": "payload_receipt_purchaseTime", "affinity": "INTEGER"}, {"fieldPath": "payload.transactionReceipt.purchaseToken", "columnName": "payload_receipt_purchaseToken", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}, {"tableName": "fls_other_user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `stars` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `user_status` TEXT, `about` TEXT, `joinday` TEXT, `isSuperstar` INTEGER NOT NULL, `followerType` TEXT, `isStar` INTEGER NOT NULL, `muted` INTEGER, `broadcast_likers_privacy` INTEGER, `total_broadcasts` INTEGER, `broadcast_received` INTEGER, `total_likers` INTEGER, `total_huddle_owned` INTEGER, `total_huddles_admin` INTEGER, `total_huddles_participant` INTEGER, `last_seen` TEXT, `online` INTEGER NOT NULL, `notification` TEXT, `sound_track` TEXT, `preview` INTEGER, `dage` INTEGER, `flax_increment` INTEGER, `flax_rate` REAL, `flax_rate_percentage` REAL, `blocked_by_leader` INTEGER, `blocked_by_admin` INTEGER, `contributor_level` TEXT, `player_level` TEXT, `subscription_expiry_date` TEXT, `issue_date` TEXT, `total_huddle_joined` INTEGER, `total_received_gifts` INTEGER, `total_flash_published` INTEGER, `total_public_podiums` INTEGER, `userManagedHuddlesParticipants` INTEGER, `tribe_participants_count` INTEGER, `user_empower` TEXT, `countryCode` TEXT, `popularity` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT"}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "citizenship", "columnName": "user_status", "affinity": "TEXT"}, {"fieldPath": "about", "columnName": "about", "affinity": "TEXT"}, {"fieldPath": "joinDay", "columnName": "joinday", "affinity": "TEXT"}, {"fieldPath": "is<PERSON><PERSON><PERSON><PERSON>", "columnName": "is<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "followerType", "columnName": "followerType", "affinity": "TEXT"}, {"fieldPath": "isStar", "columnName": "isStar", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER"}, {"fieldPath": "likersPrivacy", "columnName": "broadcast_likers_privacy", "affinity": "INTEGER"}, {"fieldPath": "totalBroadcasts", "columnName": "total_broadcasts", "affinity": "INTEGER"}, {"fieldPath": "broadcastReceived", "columnName": "broadcast_received", "affinity": "INTEGER"}, {"fieldPath": "totalLikers", "columnName": "total_likers", "affinity": "INTEGER"}, {"fieldPath": "totalHuddleOwned", "columnName": "total_huddle_owned", "affinity": "INTEGER"}, {"fieldPath": "totalHuddlesAdmin", "columnName": "total_huddles_admin", "affinity": "INTEGER"}, {"fieldPath": "totalHuddlesParticipant", "columnName": "total_huddles_participant", "affinity": "INTEGER"}, {"fieldPath": "lastSeen", "columnName": "last_seen", "affinity": "TEXT"}, {"fieldPath": "online", "columnName": "online", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notification", "columnName": "notification", "affinity": "TEXT"}, {"fieldPath": "soundTrack", "columnName": "sound_track", "affinity": "TEXT"}, {"fieldPath": "preview", "columnName": "preview", "affinity": "INTEGER"}, {"fieldPath": "dage", "columnName": "dage", "affinity": "INTEGER"}, {"fieldPath": "flaxIncrement", "columnName": "flax_increment", "affinity": "INTEGER"}, {"fieldPath": "flaxRate", "columnName": "flax_rate", "affinity": "REAL"}, {"fieldPath": "flaxRatePercentage", "columnName": "flax_rate_percentage", "affinity": "REAL"}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "blocked_by_leader", "affinity": "INTEGER"}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "blocked_by_admin", "affinity": "INTEGER"}, {"fieldPath": "contributorLevel", "columnName": "contributor_level", "affinity": "TEXT"}, {"fieldPath": "playerLevel", "columnName": "player_level", "affinity": "TEXT"}, {"fieldPath": "subscriptionExpiryDate", "columnName": "subscription_expiry_date", "affinity": "TEXT"}, {"fieldPath": "issueDate", "columnName": "issue_date", "affinity": "TEXT"}, {"fieldPath": "totalHuddleJoined", "columnName": "total_huddle_joined", "affinity": "INTEGER"}, {"fieldPath": "totalReceivedGifts", "columnName": "total_received_gifts", "affinity": "INTEGER"}, {"fieldPath": "totalFlashPublished", "columnName": "total_flash_published", "affinity": "INTEGER"}, {"fieldPath": "totalPublicPodiums", "columnName": "total_public_podiums", "affinity": "INTEGER"}, {"fieldPath": "userManagedHuddlesParticipants", "columnName": "userManagedHuddlesParticipants", "affinity": "INTEGER"}, {"fieldPath": "tribeParticipantsCount", "columnName": "tribe_participants_count", "affinity": "INTEGER"}, {"fieldPath": "userEmpowerment", "columnName": "user_empower", "affinity": "TEXT"}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT"}, {"fieldPath": "popularity", "columnName": "popularity", "affinity": "INTEGER"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_huddles_post_comments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`commentId` TEXT NOT NULL, `huddleId` INTEGER NOT NULL, `messageId` TEXT NOT NULL, `created` TEXT, `message` TEXT, `isReported` INTEGER, `senderId` INTEGER NOT NULL, `senderDetails` TEXT, `media` TEXT, PRIMARY KEY(`commentId`))", "fields": [{"fieldPath": "commentId", "columnName": "commentId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddleId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageId", "columnName": "messageId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "created", "columnName": "created", "affinity": "TEXT"}, {"fieldPath": "comment", "columnName": "message", "affinity": "TEXT"}, {"fieldPath": "isReported", "columnName": "isReported", "affinity": "INTEGER"}, {"fieldPath": "senderId", "columnName": "senderId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderDetails", "columnName": "senderDetails", "affinity": "TEXT"}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["commentId"]}, "indices": [{"name": "index_fls_huddles_post_comments_huddleId", "unique": false, "columnNames": ["huddleId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_post_comments_huddleId` ON `${TABLE_NAME}` (`huddleId`)"}, {"name": "index_fls_huddles_post_comments_messageId", "unique": false, "columnNames": ["messageId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_post_comments_messageId` ON `${TABLE_NAME}` (`messageId`)"}]}, {"tableName": "fls_transfer_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`flax` REAL, `id` INTEGER NOT NULL, `purpose` TEXT, `purpose_id` INTEGER, `sender_id` INTEGER, `receiver_id` INTEGER, `status` TEXT, `time_created` TEXT, `time_updated` TEXT, `user_id` TEXT, `withdrawn` INTEGER, `name` TEXT, `username` TEXT, `profile_photo` TEXT, `premium` INTEGER, `is_deleted` INTEGER, `received_flax` REAL, `sent_flax` REAL, `membership` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "flax", "columnName": "flax", "affinity": "REAL"}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "purpose", "columnName": "purpose", "affinity": "TEXT"}, {"fieldPath": "purposeId", "columnName": "purpose_id", "affinity": "INTEGER"}, {"fieldPath": "senderId", "columnName": "sender_id", "affinity": "INTEGER"}, {"fieldPath": "receiverId", "columnName": "receiver_id", "affinity": "INTEGER"}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT"}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT"}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT"}, {"fieldPath": "withdrawn", "columnName": "withdrawn", "affinity": "INTEGER"}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT"}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT"}, {"fieldPath": "profilePhoto", "columnName": "profile_photo", "affinity": "TEXT"}, {"fieldPath": "premium", "columnName": "premium", "affinity": "INTEGER"}, {"fieldPath": "isDeleted", "columnName": "is_deleted", "affinity": "INTEGER"}, {"fieldPath": "receivedFlax", "columnName": "received_flax", "affinity": "REAL"}, {"fieldPath": "sentFlax", "columnName": "sent_flax", "affinity": "REAL"}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_polls_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`answers` TEXT NOT NULL, `end_date` TEXT, `huddle_id` INTEGER NOT NULL, `id` INTEGER NOT NULL, `question` TEXT, `start_date` TEXT, `time_created` TEXT NOT NULL, `time_updated` TEXT NOT NULL, `end_date_iso` TEXT, `start_date_iso` TEXT NOT NULL, `visitors_count` INTEGER NOT NULL, `citizens_count` INTEGER NOT NULL, `total_answered` INTEGER NOT NULL, `user_answer` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "answers", "columnName": "answers", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endDate", "columnName": "end_date", "affinity": "TEXT"}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "question", "columnName": "question", "affinity": "TEXT"}, {"fieldPath": "startDate", "columnName": "start_date", "affinity": "TEXT"}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endDateIso", "columnName": "end_date_iso", "affinity": "TEXT"}, {"fieldPath": "startDateIso", "columnName": "start_date_iso", "affinity": "TEXT", "notNull": true}, {"fieldPath": "visitorCount", "columnName": "visitors_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "citizenCount", "columnName": "citizens_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalAnswered", "columnName": "total_answered", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userAnswer", "columnName": "user_answer", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_podium", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `agora_channel_id` TEXT, `name` TEXT NOT NULL, `about` TEXT, `category` TEXT NOT NULL, `thumbnail` TEXT, `profile_pic` TEXT, `invite_code` TEXT, `kind` TEXT, `manager_id` INTEGER NOT NULL, `manager_name` TEXT NOT NULL, `role` TEXT, `is_private` INTEGER NOT NULL DEFAULT 0, `entry` TEXT, `live` INTEGER NOT NULL, `live_users` INTEGER NOT NULL, `total_users` INTEGER NOT NULL, `is_invited` INTEGER, `created` TEXT, `updated` TEXT, `started` TEXT, `last_go_live_time` TEXT, `total_likes` INTEGER NOT NULL, `total_coins` INTEGER, `likes` INTEGER NOT NULL DEFAULT 0, `likes_disabled` INTEGER, `podium_gift_paused` INTEGER, `likes_disabled_by` INTEGER, `is_liked_by_self` INTEGER NOT NULL, `temp_id` TEXT NOT NULL, `share_link` TEXT, `invited_to_be_admin` INTEGER, `chat_disabled` INTEGER, `chat_disabled_by` INTEGER, `mic_disabled` INTEGER, `mic_disabled_by` INTEGER, `gifts_count` INTEGER NOT NULL DEFAULT 0, `hide_live_users` INTEGER, `freeze` INTEGER NOT NULL, `current_live_session_id` TEXT, `manager_profile_thumbnail` TEXT, `mute` INTEGER, `game_type` TEXT, `talk_time_duration` INTEGER, `talk_time_start` INTEGER, `parent_id` TEXT, `competitor_user_id` INTEGER, `competitor_thumbnail` TEXT, `competitor_name` TEXT, `competitor_muted` INTEGER, `maidan_status` TEXT, `competitor_podium_id` TEXT, `challenge_id` TEXT, `stage_fee` INTEGER, `audience_fee` INTEGER, `yalla_challenges_count` INTEGER, `allow_yalla` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "channelId", "columnName": "agora_channel_id", "affinity": "TEXT"}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bio", "columnName": "about", "affinity": "TEXT"}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT"}, {"fieldPath": "profilePic", "columnName": "profile_pic", "affinity": "TEXT"}, {"fieldPath": "inviteCode", "columnName": "invite_code", "affinity": "TEXT"}, {"fieldPath": "kind", "columnName": "kind", "affinity": "TEXT"}, {"fieldPath": "managerId", "columnName": "manager_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "manager_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT"}, {"fieldPath": "isPrivate", "columnName": "is_private", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "entry", "columnName": "entry", "affinity": "TEXT"}, {"fieldPath": "isLive", "columnName": "live", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "liveUsers", "columnName": "live_users", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalUsers", "columnName": "total_users", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "wasInvited", "columnName": "is_invited", "affinity": "INTEGER"}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT"}, {"fieldPath": "updatedTime", "columnName": "updated", "affinity": "TEXT"}, {"fieldPath": "startedTime", "columnName": "started", "affinity": "TEXT"}, {"fieldPath": "lastGoLiveTime", "columnName": "last_go_live_time", "affinity": "TEXT"}, {"fieldPath": "totalLikes", "columnName": "total_likes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalCoins", "columnName": "total_coins", "affinity": "INTEGER"}, {"fieldPath": "likes", "columnName": "likes", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "likesDisabled", "columnName": "likes_disabled", "affinity": "INTEGER"}, {"fieldPath": "podiumGiftPaused", "columnName": "podium_gift_paused", "affinity": "INTEGER"}, {"fieldPath": "likesDisabledBy", "columnName": "likes_disabled_by", "affinity": "INTEGER"}, {"fieldPath": "liked", "columnName": "is_liked_by_self", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tempId", "columnName": "temp_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "shareLink", "columnName": "share_link", "affinity": "TEXT"}, {"fieldPath": "invitedToBeAdmin", "columnName": "invited_to_be_admin", "affinity": "INTEGER"}, {"fieldPath": "chatDisabled", "columnName": "chat_disabled", "affinity": "INTEGER"}, {"fieldPath": "chatDisabledBy", "columnName": "chat_disabled_by", "affinity": "INTEGER"}, {"fieldPath": "requestToSpeakDisabled", "columnName": "mic_disabled", "affinity": "INTEGER"}, {"fieldPath": "micDisabledBy", "columnName": "mic_disabled_by", "affinity": "INTEGER"}, {"fieldPath": "giftCount", "columnName": "gifts_count", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "hideLiveUsers", "columnName": "hide_live_users", "affinity": "INTEGER"}, {"fieldPath": "chatFrozen", "columnName": "freeze", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentLiveSessionId", "columnName": "current_live_session_id", "affinity": "TEXT"}, {"fieldPath": "managerProfileThumbnail", "columnName": "manager_profile_thumbnail", "affinity": "TEXT"}, {"fieldPath": "mute", "columnName": "mute", "affinity": "INTEGER"}, {"fieldPath": "gameType", "columnName": "game_type", "affinity": "TEXT"}, {"fieldPath": "talkTimeDuration", "columnName": "talk_time_duration", "affinity": "INTEGER"}, {"fieldPath": "talkTimeStart", "columnName": "talk_time_start", "affinity": "INTEGER"}, {"fieldPath": "parentId", "columnName": "parent_id", "affinity": "TEXT"}, {"fieldPath": "competitorUserId", "columnName": "competitor_user_id", "affinity": "INTEGER"}, {"fieldPath": "competitor<PERSON><PERSON><PERSON><PERSON>", "columnName": "competitor_thumbnail", "affinity": "TEXT"}, {"fieldPath": "competitorName", "columnName": "competitor_name", "affinity": "TEXT"}, {"fieldPath": "competitorMuted", "columnName": "competitor_muted", "affinity": "INTEGER"}, {"fieldPath": "maidanStatus", "columnName": "maidan_status", "affinity": "TEXT"}, {"fieldPath": "competitorPodiumId", "columnName": "competitor_podium_id", "affinity": "TEXT"}, {"fieldPath": "challengeId", "columnName": "challenge_id", "affinity": "TEXT"}, {"fieldPath": "stageFee", "columnName": "stage_fee", "affinity": "INTEGER"}, {"fieldPath": "audienceFee", "columnName": "audience_fee", "affinity": "INTEGER"}, {"fieldPath": "yallaChallengesCount", "columnName": "yalla_challenges_count", "affinity": "INTEGER"}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "allow_yalla", "affinity": "INTEGER"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_sell_huddle", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`about` TEXT, `flax` REAL, `huddle_id` INTEGER, `huddle_name` TEXT, `id` INTEGER, `owner` INTEGER, `participant_count` INTEGER, `status` TEXT, `time_created` TEXT, `time_updated` TEXT, `huddle_photo` TEXT, `manager_name` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "about", "columnName": "about", "affinity": "TEXT"}, {"fieldPath": "flax", "columnName": "flax", "affinity": "REAL"}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER"}, {"fieldPath": "huddleName", "columnName": "huddle_name", "affinity": "TEXT"}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER"}, {"fieldPath": "owner", "columnName": "owner", "affinity": "INTEGER"}, {"fieldPath": "participantCount", "columnName": "participant_count", "affinity": "INTEGER"}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT"}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT"}, {"fieldPath": "huddleP<PERSON><PERSON>", "columnName": "huddle_photo", "affinity": "TEXT"}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "manager_name", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_gift_video", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`gift_id` INTEGER NOT NULL, `key` TEXT NOT NULL, `name` TEXT NOT NULL, `path` TEXT NOT NULL, PRIMARY KEY(`gift_id`))", "fields": [{"fieldPath": "giftId", "columnName": "gift_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["gift_id"]}, "indices": [{"name": "index_fls_gift_video_gift_id", "unique": true, "columnNames": ["gift_id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_gift_video_gift_id` ON `${TABLE_NAME}` (`gift_id`)"}, {"name": "index_fls_gift_video_key", "unique": true, "columnNames": ["key"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_gift_video_key` ON `${TABLE_NAME}` (`key`)"}]}, {"tableName": "fls_postat", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `postat_type` TEXT NOT NULL, `hide` INTEGER, `userId` INTEGER NOT NULL, `temp_id` TEXT, `time_created` TEXT NOT NULL, `sent` TEXT, `media_count` INTEGER, `turn_off_comments` INTEGER, `is_original_audio` INTEGER NOT NULL, `media` TEXT NOT NULL, `has_mention` INTEGER, `mentioned_users` TEXT NOT NULL, `music_data` TEXT, `message` TEXT, `color` TEXT, `sender_details` TEXT NOT NULL, `total_gift_count` INTEGER NOT NULL, `total_gift_value` REAL NOT NULL, `total_comments` INTEGER NOT NULL, `is_followed` INTEGER, `sendStatus` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "messageId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "postat_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hide", "columnName": "hide", "affinity": "INTEGER"}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tempId", "columnName": "temp_id", "affinity": "TEXT"}, {"fieldPath": "created", "columnName": "time_created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sent", "columnName": "sent", "affinity": "TEXT"}, {"fieldPath": "mediaCount", "columnName": "media_count", "affinity": "INTEGER"}, {"fieldPath": "turnOffComments", "columnName": "turn_off_comments", "affinity": "INTEGER"}, {"fieldPath": "isOriginalAudio", "columnName": "is_original_audio", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hasMention", "columnName": "has_mention", "affinity": "INTEGER"}, {"fieldPath": "mentionedUsers", "columnName": "mentioned_users", "affinity": "TEXT", "notNull": true}, {"fieldPath": "musicData", "columnName": "music_data", "affinity": "TEXT"}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT"}, {"fieldPath": "color", "columnName": "color", "affinity": "TEXT"}, {"fieldPath": "senderDetails", "columnName": "sender_details", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalGiftCount", "columnName": "total_gift_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalGiftValue", "columnName": "total_gift_value", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalComments", "columnName": "total_comments", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFollowed", "columnName": "is_followed", "affinity": "INTEGER"}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "fls_postat_feed", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`postat_tab` TEXT NOT NULL, `id` TEXT NOT NULL, `postat_type` TEXT NOT NULL, `hide` INTEGER, `userId` INTEGER NOT NULL, `temp_id` TEXT, `time_created` TEXT NOT NULL, `sent` TEXT, `media_count` INTEGER, `turn_off_comments` INTEGER, `is_original_audio` INTEGER NOT NULL, `media` TEXT NOT NULL, `has_mention` INTEGER, `mentioned_users` TEXT NOT NULL, `music_data` TEXT, `message` TEXT, `color` TEXT, `sender_details` TEXT NOT NULL, `total_gift_count` INTEGER NOT NULL, `total_gift_value` REAL NOT NULL, `total_comments` INTEGER NOT NULL, `is_followed` INTEGER, `sendStatus` TEXT, PRIMARY KEY(`id`, `postat_tab`))", "fields": [{"fieldPath": "postatTab", "columnName": "postat_tab", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.messageId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.type", "columnName": "postat_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.hide", "columnName": "hide", "affinity": "INTEGER"}, {"fieldPath": "postat.userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postat.tempId", "columnName": "temp_id", "affinity": "TEXT"}, {"fieldPath": "postat.created", "columnName": "time_created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.sent", "columnName": "sent", "affinity": "TEXT"}, {"fieldPath": "postat.mediaCount", "columnName": "media_count", "affinity": "INTEGER"}, {"fieldPath": "postat.turnOffComments", "columnName": "turn_off_comments", "affinity": "INTEGER"}, {"fieldPath": "postat.isOriginalAudio", "columnName": "is_original_audio", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postat.media", "columnName": "media", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.hasMention", "columnName": "has_mention", "affinity": "INTEGER"}, {"fieldPath": "postat.mentionedUsers", "columnName": "mentioned_users", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.musicData", "columnName": "music_data", "affinity": "TEXT"}, {"fieldPath": "postat.message", "columnName": "message", "affinity": "TEXT"}, {"fieldPath": "postat.color", "columnName": "color", "affinity": "TEXT"}, {"fieldPath": "postat.senderDetails", "columnName": "sender_details", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.totalGiftCount", "columnName": "total_gift_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postat.totalGiftValue", "columnName": "total_gift_value", "affinity": "REAL", "notNull": true}, {"fieldPath": "postat.totalComments", "columnName": "total_comments", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postat.isFollowed", "columnName": "is_followed", "affinity": "INTEGER"}, {"fieldPath": "postat.sendStatus", "columnName": "sendStatus", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id", "postat_tab"]}}, {"tableName": "fls_postat_media", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`mediaId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `id` TEXT NOT NULL, `path` TEXT NOT NULL, `key` TEXT NOT NULL, `meta` TEXT NOT NULL, `uploadState` TEXT NOT NULL)", "fields": [{"fieldPath": "mediaId", "columnName": "mediaId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postatId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "meta", "columnName": "meta", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uploadState", "columnName": "uploadState", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["mediaId"]}, "indices": [{"name": "index_fls_postat_media_id", "unique": false, "columnNames": ["id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_postat_media_id` ON `${TABLE_NAME}` (`id`)"}]}, {"tableName": "fls_business_flashat", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`citizenship` TEXT, `nextLevelCitizenShip` TEXT, `eligibility` INTEGER, `premium` INTEGER, `tasksCompleted` INTEGER, `app_rating_isSatisfied` INTEGER, `app_rating_status` TEXT, `cus_currentFans` INTEGER, `cus_customersPercentage` REAL, `cus_isSatisfied` INTEGER, `cus_requiredFans` INTEGER, `elig_days_currentDays` INTEGER, `elig_days_isSatisfied` INTEGER, `elig_days_requiredDays` INTEGER, `flax_balance_currentFlaxBalance` REAL, `flax_balance_isSatisfied` INTEGER, `flax_balance_requiredFlaxBalance` INTEGER, `huddles_currentHuddlesCount` INTEGER, `huddles_currentParticipantCount` INTEGER, `huddles_huddlePercentage` INTEGER, `huddles_isSatisfied` INTEGER, `huddles_requiredHuddleCount` INTEGER, `huddles_requiredParticipantCount` INTEGER, `Pay_exist_timeCreated` TEXT, `Pay_exist_timeUpdated` TEXT, `prof_complecurrentPercentage` INTEGER, `prof_compleisSatisfied` INTEGER, `prof_complemobileVerified` INTEGER, `prof_complerequiredPercentage` INTEGER, `user_rating_currentUserRating` REAL, `user_rating_isSatisfied` INTEGER, `user_rating_requiredUserRating` INTEGER, PRIMARY KEY(`premium`))", "fields": [{"fieldPath": "citizenship", "columnName": "citizenship", "affinity": "TEXT"}, {"fieldPath": "nextLevelCitizenShip", "columnName": "nextLevelCitizenShip", "affinity": "TEXT"}, {"fieldPath": "eligibility", "columnName": "eligibility", "affinity": "INTEGER"}, {"fieldPath": "premium", "columnName": "premium", "affinity": "INTEGER"}, {"fieldPath": "tasksCompleted", "columnName": "tasksCompleted", "affinity": "INTEGER"}, {"fieldPath": "appRating.isSatisfied", "columnName": "app_rating_isSatisfied", "affinity": "INTEGER"}, {"fieldPath": "appRating.status", "columnName": "app_rating_status", "affinity": "TEXT"}, {"fieldPath": "customers.currentFans", "columnName": "cus_currentFans", "affinity": "INTEGER"}, {"fieldPath": "customers.customersPercentage", "columnName": "cus_customersPercentage", "affinity": "REAL"}, {"fieldPath": "customers.isSatisfied", "columnName": "cus_isSatisfied", "affinity": "INTEGER"}, {"fieldPath": "customers.requiredFans", "columnName": "cus_requiredFans", "affinity": "INTEGER"}, {"fieldPath": "eligibilityInDays.currentDays", "columnName": "elig_days_currentDays", "affinity": "INTEGER"}, {"fieldPath": "eligibilityInDays.isSatisfied", "columnName": "elig_days_isSatisfied", "affinity": "INTEGER"}, {"fieldPath": "eligibilityInDays.requiredDays", "columnName": "elig_days_requiredDays", "affinity": "INTEGER"}, {"fieldPath": "flaxBalance.currentFlaxBalance", "columnName": "flax_balance_currentFlaxBalance", "affinity": "REAL"}, {"fieldPath": "flaxBalance.isSatisfied", "columnName": "flax_balance_isSatisfied", "affinity": "INTEGER"}, {"fieldPath": "flaxBalance.requiredFlaxBalance", "columnName": "flax_balance_requiredFlaxBalance", "affinity": "INTEGER"}, {"fieldPath": "huddles.currentHuddlesCount", "columnName": "huddles_currentHuddlesCount", "affinity": "INTEGER"}, {"fieldPath": "huddles.currentParticipantCount", "columnName": "huddles_currentParticipantCount", "affinity": "INTEGER"}, {"fieldPath": "huddles.huddlePercentage", "columnName": "huddles_huddlePercentage", "affinity": "INTEGER"}, {"fieldPath": "huddles.isSatisfied", "columnName": "huddles_isSatisfied", "affinity": "INTEGER"}, {"fieldPath": "huddles.requiredHuddleCount", "columnName": "huddles_requiredHuddleCount", "affinity": "INTEGER"}, {"fieldPath": "huddles.requiredParticipantCount", "columnName": "huddles_requiredParticipantCount", "affinity": "INTEGER"}, {"fieldPath": "payoutExist.timeCreated", "columnName": "Pay_exist_timeCreated", "affinity": "TEXT"}, {"fieldPath": "payoutExist.timeUpdated", "columnName": "Pay_exist_timeUpdated", "affinity": "TEXT"}, {"fieldPath": "profileCompleteness.currentPercentage", "columnName": "prof_complecurrentPercentage", "affinity": "INTEGER"}, {"fieldPath": "profileCompleteness.isSatisfied", "columnName": "prof_compleisSatisfied", "affinity": "INTEGER"}, {"fieldPath": "profileCompleteness.mobileVerified", "columnName": "prof_complemobileVerified", "affinity": "INTEGER"}, {"fieldPath": "profileCompleteness.requiredPercentage", "columnName": "prof_complerequiredPercentage", "affinity": "INTEGER"}, {"fieldPath": "userRating.currentUserRating", "columnName": "user_rating_currentUserRating", "affinity": "REAL"}, {"fieldPath": "userRating.isSatisfied", "columnName": "user_rating_isSatisfied", "affinity": "INTEGER"}, {"fieldPath": "userRating.requiredUserRating", "columnName": "user_rating_requiredUserRating", "affinity": "INTEGER"}], "primaryKey": {"autoGenerate": false, "columnNames": ["premium"]}}, {"tableName": "fls_yalla_live", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`challenge_id` TEXT NOT NULL, `podium_id` TEXT NOT NULL, `game_type` TEXT NOT NULL, `initiator` TEXT NOT NULL, `is_more_participants_allowed` INTEGER NOT NULL, `is_participant` INTEGER NOT NULL, `min_participants_count` INTEGER NOT NULL, `max_participants_count` INTEGER NOT NULL, `queued` INTEGER NOT NULL, `participants_count` INTEGER NOT NULL, `prize` REAL, `time_created` TEXT, `time_updated` TEXT, `participants` TEXT, PRIMARY KEY(`challenge_id`))", "fields": [{"fieldPath": "challengeId", "columnName": "challenge_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "podiumId", "columnName": "podium_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gameType", "columnName": "game_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "initiator", "columnName": "initiator", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isMoreParticipantsAllowed", "columnName": "is_more_participants_allowed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isParticipant", "columnName": "is_participant", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "minParticipantsCount", "columnName": "min_participants_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxParticipantsCount", "columnName": "max_participants_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "queued", "columnName": "queued", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "participantsCount", "columnName": "participants_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "prize", "columnName": "prize", "affinity": "REAL"}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT"}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT"}, {"fieldPath": "participants", "columnName": "participants", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["challenge_id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '6a85f7b71e494abb307cc24f94064789')"]}}