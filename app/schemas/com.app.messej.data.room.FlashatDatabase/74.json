{"formatVersion": 1, "database": {"version": 74, "identityHash": "eb8d86498e25cf1405cdfe1cd43d9723", "entities": [{"tableName": "fls_remotekeys", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`query` TEXT NOT NULL, `nextPage` TEXT, PRIMARY KEY(`query`))", "fields": [{"fieldPath": "query", "columnName": "query", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nextPage", "columnName": "nextPage", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["query"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_huddles", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `about` TEXT, `thumbnail` TEXT, `group_photo` TEXT, `manager_premium_status` INTEGER, `category` TEXT, `created_by` TEXT, `private` INTEGER NOT NULL, `status` TEXT NOT NULL, `admin_status` TEXT, `user_status` TEXT NOT NULL, `total_members` INTEGER NOT NULL, `activity` TEXT, `time_created` TEXT, `time_updated` TEXT, `online_participants` INTEGER NOT NULL, `unread_count` INTEGER NOT NULL, `tribe` INTEGER NOT NULL DEFAULT 0, `updated_by` INTEGER NOT NULL, `participant_share` INTEGER NOT NULL, `invite_link` TEXT, `manager_id` INTEGER NOT NULL, `request_to_join` INTEGER NOT NULL, `requested_invited_count` INTEGER NOT NULL, `muted` INTEGER NOT NULL, `pinned` INTEGER NOT NULL, `role` TEXT, `sender_details` TEXT, `last_message` TEXT, `last_read_message` TEXT, `privacy` TEXT, `empowered_user_blocked` INTEGER NOT NULL DEFAULT 0, `huddle_type` TEXT NOT NULL, `huddle_show_type` TEXT DEFAULT 'NONE', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "about", "columnName": "about", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "groupPhoto", "columnName": "group_photo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "managerPremium", "columnName": "manager_premium_status", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": false}, {"fieldPath": "created<PERSON>y", "columnName": "created_by", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isPrivate", "columnName": "private", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "adminStatus", "columnName": "admin_status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userStatus", "columnName": "user_status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalMembers", "columnName": "total_members", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activity", "columnName": "activity", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "onlineParticipants", "columnName": "online_participants", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unread_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isTribe", "columnName": "tribe", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "updatedBy", "columnName": "updated_by", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "participantShare", "columnName": "participant_share", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "inviteLink", "columnName": "invite_link", "affinity": "TEXT", "notNull": false}, {"fieldPath": "managerId", "columnName": "manager_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "requestToJoin", "columnName": "request_to_join", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "requestsAndInvites", "columnName": "requested_invited_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT", "notNull": false}, {"fieldPath": "senderDetails", "columnName": "sender_details", "affinity": "TEXT", "notNull": false}, {"fieldPath": "_lastMessage", "columnName": "last_message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastReadMessage", "columnName": "last_read_message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "privacy", "columnName": "privacy", "affinity": "TEXT", "notNull": false}, {"fieldPath": "empoweredUserBlocked", "columnName": "empowered_user_blocked", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "huddleType", "columnName": "huddle_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "involvement", "columnName": "huddle_show_type", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_huddle_interventions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `blocked_from_huddle` TEXT NOT NULL, `admin_invited` TEXT NOT NULL, `user_blocked` TEXT NOT NULL, `comment_banned` TEXT NOT NULL DEFAULT '[]', `post_banned` TEXT NOT NULL DEFAULT '[]', `reply_banned` TEXT NOT NULL DEFAULT '[]', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "huddleId", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "blocked_from_huddle", "affinity": "TEXT", "notNull": true}, {"fieldPath": "invitedToBeAdmin", "columnName": "admin_invited", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userBlocked", "columnName": "user_blocked", "affinity": "TEXT", "notNull": true}, {"fieldPath": "commentBanned", "columnName": "comment_banned", "affinity": "TEXT", "notNull": true, "defaultValue": "'[]'"}, {"fieldPath": "postBanned", "columnName": "post_banned", "affinity": "TEXT", "notNull": true, "defaultValue": "'[]'"}, {"fieldPath": "replyBanned", "columnName": "reply_banned", "affinity": "TEXT", "notNull": true, "defaultValue": "'[]'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_messages", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `last_message` TEXT, `last_message_id` TEXT, `sender` INTEGER NOT NULL, `receiver` INTEGER NOT NULL, `receiver_data` TEXT NOT NULL, `room_id` TEXT NOT NULL, `room_name` TEXT, `status` TEXT, `type` TEXT NOT NULL, `unread_count` INTEGER NOT NULL, `updated` TEXT, `is_ignored` INTEGER, `followed_by_each` INTEGER, `private_message_tab` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "_lastMessage", "columnName": "last_message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastMessageId", "columnName": "last_message_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "receiverDetails", "columnName": "receiver_data", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomName", "columnName": "room_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "unreadCount", "columnName": "unread_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updated", "columnName": "updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isIgnored", "columnName": "is_ignored", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "followedByMe", "columnName": "followed_by_each", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "privateMessageTab", "columnName": "private_message_tab", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_messages_type", "unique": false, "columnNames": ["type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_type` ON `${TABLE_NAME}` (`type`)"}, {"name": "index_fls_messages_unread_count", "unique": false, "columnNames": ["unread_count"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_unread_count` ON `${TABLE_NAME}` (`unread_count`)"}, {"name": "index_fls_messages_updated", "unique": false, "columnNames": ["updated"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_updated` ON `${TABLE_NAME}` (`updated`)"}, {"name": "index_fls_messages_private_message_tab", "unique": false, "columnNames": ["private_message_tab"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_private_message_tab` ON `${TABLE_NAME}` (`private_message_tab`)"}], "foreignKeys": []}, {"tableName": "fls_private_chat_room_info", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`canChat` INTEGER, `error` TEXT, `chatBlockedBySender` INTEGER, `userBlockedByReceiver` INTEGER, `userBlockedBySender` INTEGER, `chatDisabledBySender` INTEGER, `chatDisabledByReceiver` INTEGER, `followed_by_each` INTEGER, `localId` TEXT NOT NULL, `participants` TEXT NOT NULL, `creator` INTEGER, `userBlockStatus` TEXT, `followedStatus` TEXT, `threadType` TEXT, `created` TEXT, `id` TEXT NOT NULL, `roomStatus` TEXT, `type` TEXT NOT NULL, `chatType` TEXT, PRIMARY KEY(`localId`))", "fields": [{"fieldPath": "canChat", "columnName": "canChat", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "error", "columnName": "error", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatBlockedBySender", "columnName": "chatBlockedBySender", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userBlockedByReceiver", "columnName": "userBlockedByReceiver", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userBlockedBySender", "columnName": "userBlockedBySender", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatDisabledBySender", "columnName": "chatDisabledBySender", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatDisabledByReceiver", "columnName": "chatDisabledByReceiver", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "followedByMe", "columnName": "followed_by_each", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "localRoomId", "columnName": "localId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.participants", "columnName": "participants", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.creator", "columnName": "creator", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatRoom.userBlockStatus", "columnName": "userBlockStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.followedStatus", "columnName": "followedStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.threadType", "columnName": "threadType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.created", "columnName": "created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.roomStatus", "columnName": "roomStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "chatRoom.type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chatRoom.chatType", "columnName": "chatType", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["localId"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_huddles_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `sent` TEXT, `remover` TEXT, `deleted` INTEGER NOT NULL, `edited` INTEGER, `is_activity` INTEGER NOT NULL, `activity_meta` TEXT, `media` TEXT, `media_meta` TEXT, `message_type` TEXT, `read` TEXT, `receiver` INTEGER, `reply_to` TEXT, `sender` INTEGER NOT NULL, `sender_relation` TEXT, `sender_details` TEXT, `liked` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `huddle_id` TEXT NOT NULL, `huddle_type` TEXT NOT NULL, `total_likes` INTEGER NOT NULL, `total_gifts` INTEGER NOT NULL DEFAULT 0, `total_comments` INTEGER NOT NULL DEFAULT 0, `star_type` TEXT, `pinned` INTEGER, `has_mention` INTEGER NOT NULL DEFAULT 0, `color` TEXT, `mentioned_users` TEXT, `post_type` TEXT, `huddle_name` TEXT, `forward_id` TEXT, `reply_message_id` TEXT, `reply_message_sender_id` INTEGER, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rawMessage", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sentTime", "columnName": "sent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "remover", "columnName": "remover", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "edited", "columnName": "edited", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isActivity", "columnName": "is_activity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activityMeta", "columnName": "activity_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalMessageType", "columnName": "message_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "read", "columnName": "read", "affinity": "TEXT", "notNull": false}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "replyTo", "columnName": "reply_to", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderRelation", "columnName": "sender_relation", "affinity": "TEXT", "notNull": false}, {"fieldPath": "senderDetails", "columnName": "sender_details", "affinity": "TEXT", "notNull": false}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleType", "columnName": "huddle_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalLikes", "columnName": "total_likes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalGifts", "columnName": "total_gifts", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "totalComments", "columnName": "total_comments", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "starType", "columnName": "star_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "hasMention", "columnName": "has_mention", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "chatTextColor", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mentionedUsers", "columnName": "mentioned_users", "affinity": "TEXT", "notNull": false}, {"fieldPath": "postType", "columnName": "post_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "huddleName", "columnName": "huddle_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "forwardId", "columnName": "forward_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "replyMessageId", "columnName": "reply_message_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "replyMessageSenderId", "columnName": "reply_message_sender_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_chats_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_chats_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}], "foreignKeys": []}, {"tableName": "fls_messages_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `sent` TEXT, `deleted` INTEGER NOT NULL, `is_activity` INTEGER NOT NULL, `activity_meta` TEXT, `media` TEXT, `media_meta` TEXT, `message_type` TEXT, `read` TEXT, `receiver` INTEGER NOT NULL, `reply_to` TEXT, `sender` INTEGER NOT NULL, `liked` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `blocked` INTEGER NOT NULL, `user_id` INTEGER NOT NULL, `color` TEXT, `forward_id` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rawMessage", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sentTime", "columnName": "sent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActivity", "columnName": "is_activity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activityMeta", "columnName": "activity_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalMessageType", "columnName": "message_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "read", "columnName": "read", "affinity": "TEXT", "notNull": false}, {"fieldPath": "receiver", "columnName": "receiver", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "replyTo", "columnName": "reply_to", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender", "columnName": "sender", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "blocked", "columnName": "blocked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chatTextColor", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "forwardId", "columnName": "forward_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_messages_chats_room_id", "unique": false, "columnNames": ["room_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_chats_room_id` ON `${TABLE_NAME}` (`room_id`)"}, {"name": "index_fls_messages_chats_created", "unique": false, "columnNames": ["created"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_messages_chats_created` ON `${TABLE_NAME}` (`created`)"}], "foreignKeys": []}, {"tableName": "fls_broadcast_message", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `broadcast_id` TEXT NOT NULL, `message` TEXT, `created` TEXT NOT NULL, `delivered` TEXT, `deleted` INTEGER NOT NULL, `media` TEXT, `media_meta` TEXT, `message_type` TEXT, `broadcaster` INTEGER NOT NULL, `subscriber` INTEGER NOT NULL, `broadcast_type` TEXT NOT NULL, `liked` INTEGER NOT NULL, `starred` INTEGER NOT NULL, `total_likes` INTEGER NOT NULL, `color` TEXT, `forward_id` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "messageId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "broadcastId", "columnName": "broadcast_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rawMessage", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "deliveredTime", "columnName": "delivered", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalMessageType", "columnName": "message_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "broadcaster", "columnName": "broadcaster", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "subscriber", "columnName": "subscriber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "broadcastMode", "columnName": "broadcast_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "liked", "columnName": "liked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "starred", "columnName": "starred", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalLikes", "columnName": "total_likes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chatTextColor", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "forwardId", "columnName": "forward_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_broadcast_message_broadcaster", "unique": false, "columnNames": ["broadcaster"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_broadcaster` ON `${TABLE_NAME}` (`broadcaster`)"}, {"name": "index_fls_broadcast_message_broadcast_type", "unique": false, "columnNames": ["broadcast_type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_broadcast_type` ON `${TABLE_NAME}` (`broadcast_type`)"}, {"name": "index_fls_broadcast_message_created", "unique": false, "columnNames": ["created"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_created` ON `${TABLE_NAME}` (`created`)"}, {"name": "index_fls_broadcast_message_starred", "unique": false, "columnNames": ["starred"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_starred` ON `${TABLE_NAME}` (`starred`)"}, {"name": "index_fls_broadcast_message_message", "unique": false, "columnNames": ["message"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_broadcast_message_message` ON `${TABLE_NAME}` (`message`)"}], "foreignKeys": []}, {"tableName": "fls_huddles_reported_chats", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `room_id` TEXT, `message` TEXT, `created` TEXT NOT NULL, `message_sent` TEXT, `time_updated` TEXT, `media` TEXT, `media_meta` TEXT, `message_type` TEXT, `sender_id` INTEGER NOT NULL, `report_id` INTEGER NOT NULL, `reports_count` INTEGER NOT NULL, `deletedUser` INTEGER NOT NULL, `sender_membership` TEXT, `sender_name` TEXT NOT NULL, `sender_role` TEXT, `sender_username` TEXT, `thumbnail` TEXT, `status` TEXT, `huddle_id` INTEGER NOT NULL, `verified` INTEGER NOT NULL, `color` TEXT, `forward_id` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roomId", "columnName": "room_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "rawMessage", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sentTime", "columnName": "message_sent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedTime", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": false}, {"fieldPath": "internalMessageType", "columnName": "message_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender", "columnName": "sender_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportId", "columnName": "report_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportsCount", "columnName": "reports_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userDeleted", "columnName": "deletedUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderMembership", "columnName": "sender_membership", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender<PERSON>ame", "columnName": "sender_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "senderRole", "columnName": "sender_role", "affinity": "TEXT", "notNull": false}, {"fieldPath": "senderUsername", "columnName": "sender_username", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "reportStatus", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chatTextColor", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "forwardId", "columnName": "forward_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_reported_chats_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_reported_chats_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}], "foreignKeys": []}, {"tableName": "fls_huddles_reported_comments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `huddle_id` INTEGER NOT NULL, `post_id` TEXT NOT NULL, `created` TEXT NOT NULL, `comment` TEXT, `media` TEXT, `sender_name` TEXT NOT NULL, `report_id` INTEGER NOT NULL, `reports_count` INTEGER NOT NULL, `status` TEXT, `time_updated` TEXT, `deletedUser` INTEGER NOT NULL, `sender_membership` TEXT, `sender_id` INTEGER NOT NULL, `thumbnail` TEXT, PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "commentId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageId", "columnName": "post_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "created", "columnName": "created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "comment", "columnName": "comment", "affinity": "TEXT", "notNull": false}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sender<PERSON>ame", "columnName": "sender_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "reportId", "columnName": "report_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportsCount", "columnName": "reports_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reportStatus", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedTime", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userDeleted", "columnName": "deletedUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderMembership", "columnName": "sender_membership", "affinity": "TEXT", "notNull": false}, {"fieldPath": "senderId", "columnName": "sender_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_huddles_reported_comments_huddle_id", "unique": false, "columnNames": ["huddle_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_reported_comments_huddle_id` ON `${TABLE_NAME}` (`huddle_id`)"}], "foreignKeys": []}, {"tableName": "fls_media", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`message_id` TEXT NOT NULL, `key` TEXT NOT NULL, `name` TEXT NOT NULL, `path` TEXT NOT NULL, `mediaType` TEXT NOT NULL, `uploadState` TEXT NOT NULL, PRIMARY KEY(`message_id`))", "fields": [{"fieldPath": "messageId", "columnName": "message_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "mediaType", "columnName": "mediaType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uploadState", "columnName": "uploadState", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["message_id"]}, "indices": [{"name": "index_fls_media_message_id", "unique": true, "columnNames": ["message_id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_media_message_id` ON `${TABLE_NAME}` (`message_id`)"}, {"name": "index_fls_media_key", "unique": true, "columnNames": ["key"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_media_key` ON `${TABLE_NAME}` (`key`)"}], "foreignKeys": []}, {"tableName": "fls_huddles_stickers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`category_name` TEXT, `id` INTEGER, `media_url` TEXT, `s3_key` TEXT, `time_created` TEXT, `time_updated` TEXT, `unicode` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "categoryName", "columnName": "category_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "mediaUrl", "columnName": "media_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s3Key", "columnName": "s3_key", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "unicode", "columnName": "unicode", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_flash", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `caption` TEXT, `media_meta` TEXT NOT NULL, `userId` INTEGER NOT NULL, `sender_detail` TEXT, `share_to` TEXT NOT NULL, `video_url` TEXT NOT NULL, `thumbnail` TEXT NOT NULL, `category` TEXT, `category_id` INTEGER, `comment_disabled` INTEGER NOT NULL, `isLiked` INTEGER NOT NULL, `likes_count` INTEGER NOT NULL, `share_count` INTEGER NOT NULL, `views_count` INTEGER NOT NULL, `comments_count` INTEGER NOT NULL, `gifts_count` INTEGER NOT NULL DEFAULT 0, `is_reported` INTEGER NOT NULL, `is_blocked` INTEGER NOT NULL DEFAULT 0, `time_created` TEXT, `time_updated` TEXT, `share_link` TEXT, `flash_type` TEXT, `sendStatus` TEXT DEFAULT 'NONE', PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "caption", "columnName": "caption", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaMeta", "columnName": "media_meta", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderDetails", "columnName": "sender_detail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "shareTo", "columnName": "share_to", "affinity": "TEXT", "notNull": true}, {"fieldPath": "videoUrl", "columnName": "video_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnailUrl", "columnName": "thumbnail", "affinity": "TEXT", "notNull": true}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categoryId", "columnName": "category_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "commentDisabled", "columnName": "comment_disabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isLiked", "columnName": "isLiked", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likeCount", "columnName": "likes_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "shareCount", "columnName": "share_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "viewCount", "columnName": "views_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "commentCount", "columnName": "comments_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "giftCount", "columnName": "gifts_count", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isReported", "columnName": "is_reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isBlocked", "columnName": "is_blocked", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "shareLink", "columnName": "share_link", "affinity": "TEXT", "notNull": false}, {"fieldPath": "flashType", "columnName": "flash_type", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false, "defaultValue": "'NONE'"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_flash_video", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`flashId` TEXT NOT NULL, `path` TEXT NOT NULL, `key` TEXT NOT NULL, `meta` TEXT NOT NULL, `uploadState` TEXT NOT NULL, PRIMARY KEY(`flashId`))", "fields": [{"fieldPath": "flashId", "columnName": "flashId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "meta", "columnName": "meta", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uploadState", "columnName": "uploadState", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["flashId"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_stars_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `stars` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `superstar_id` INTEGER, `last_broadcast_time` TEXT, `unread_messages_count` INTEGER NOT NULL, `muted` INTEGER NOT NULL, `pinned` INTEGER NOT NULL, `reported` INTEGER NOT NULL, `archived` INTEGER NOT NULL, `archived_pinned` TEXT, `hidden` INTEGER NOT NULL, `isSuperStar` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "superstarId", "columnName": "superstar_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastBroadcastTime", "columnName": "last_broadcast_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "unreadMessagesCount", "columnName": "unread_messages_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pinned", "columnName": "pinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reported", "columnName": "reported", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "archived", "columnName": "archived", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "archivedPinned", "columnName": "archived_pinned", "affinity": "TEXT", "notNull": false}, {"fieldPath": "hidden", "columnName": "hidden", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSuperStar", "columnName": "isSuperStar", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_fls_stars_list_last_broadcast_time", "unique": false, "columnNames": ["last_broadcast_time"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_last_broadcast_time` ON `${TABLE_NAME}` (`last_broadcast_time`)"}, {"name": "index_fls_stars_list_isSuperStar", "unique": false, "columnNames": ["isSuperStar"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_isSuperStar` ON `${TABLE_NAME}` (`isSuperStar`)"}, {"name": "index_fls_stars_list_membership", "unique": false, "columnNames": ["membership"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_stars_list_membership` ON `${TABLE_NAME}` (`membership`)"}], "foreignKeys": []}, {"tableName": "fls_broadcast_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `stars` INTEGER NOT NULL, `broadcastLikersPrivacy` INTEGER, `isFollowed` INTEGER, `relative_type` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "broadcastLikersPrivacy", "columnName": "broadcastLikersPrivacy", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isFollowed", "columnName": "isFollowed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "relativeType", "columnName": "relative_type", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_user_nick_names", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`userId` INTEGER NOT NULL, `nickName` TEXT, `timeCreated` TEXT, `timeUpdated` TEXT, PRIMARY KEY(`userId`))", "fields": [{"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "nick<PERSON><PERSON>", "columnName": "nick<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["userId"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_recent_search", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `keyword` TEXT NOT NULL, `timeCreated` TEXT, `screenType` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "keyword", "columnName": "keyword", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "screenType", "columnName": "screenType", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_polls_participant_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`deletedUser` INTEGER NOT NULL, `membership` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `userId` INTEGER NOT NULL DEFAULT 0, `verified` INTEGER NOT NULL, `poll_id` INTEGER NOT NULL, PRIMARY KEY(`poll_id`))", "fields": [{"fieldPath": "deletedUser", "columnName": "deletedUser", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "pollID", "columnName": "poll_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["poll_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_business_statements", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`accountName` TEXT NOT NULL, `address` TEXT, `availableBalance` REAL, `balanceBroughtForward` REAL, `beneficiaryName` TEXT, `grandTotal` REAL, `grandTotalGenerated` REAL, `grandTotalRefunded` REAL, `grandTotalRewarded` REAL, `grandTotalWithdrawn` REAL, `statementDate` TEXT, `thisMonthGenerated` REAL, `thisMonthNet` REAL, `thisMonthRefunded` REAL, `thisMonthRewarded` REAL, `thisMonthTotal` REAL, `thisMonthWithdrawn` REAL, `totalPendingPP` REAL, `balanceCarryMonth` TEXT, `isFlaxIncrement` INTEGER, `flaxRatePercentage` REAL, `receivedFlax` REAL, `sentFlax` REAL, `purchasedFlax` REAL, `soldGiftFlax` REAL, `purchasedGift<PERSON>lax` REAL, `grandTotalDebit` REAL, `podiumCameraPurchase` REAL, `cancelledDeductions` REAL, `local_id` INTEGER NOT NULL, PRIMARY KEY(`local_id`))", "fields": [{"fieldPath": "accountName", "columnName": "accountName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "address", "columnName": "address", "affinity": "TEXT", "notNull": false}, {"fieldPath": "availableBalance", "columnName": "availableBalance", "affinity": "REAL", "notNull": false}, {"fieldPath": "balanceBroughtForward", "columnName": "balanceBroughtForward", "affinity": "REAL", "notNull": false}, {"fieldPath": "beneficiary<PERSON><PERSON>", "columnName": "beneficiary<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "grandTotal", "columnName": "grandTotal", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalGenerated", "columnName": "grandTotalGenerated", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalRefunded", "columnName": "grandTotalRefunded", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalRewarded", "columnName": "grandTotalRewarded", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalWithdrawn", "columnName": "grandTotalWithdrawn", "affinity": "REAL", "notNull": false}, {"fieldPath": "statementDate", "columnName": "statementDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "thisMonthGenerated", "columnName": "thisMonthGenerated", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthNet", "columnName": "thisMonthNet", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthRefunded", "columnName": "thisMonthRefunded", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthRewarded", "columnName": "thisMonthRewarded", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthTotal", "columnName": "thisMonthTotal", "affinity": "REAL", "notNull": false}, {"fieldPath": "thisMonthWithdrawn", "columnName": "thisMonthWithdrawn", "affinity": "REAL", "notNull": false}, {"fieldPath": "totalPendingPP", "columnName": "totalPendingPP", "affinity": "REAL", "notNull": false}, {"fieldPath": "balance<PERSON><PERSON>ry<PERSON><PERSON><PERSON>", "columnName": "balance<PERSON><PERSON>ry<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isFlaxIncrement", "columnName": "isFlaxIncrement", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "flaxRatePercentage", "columnName": "flaxRatePercentage", "affinity": "REAL", "notNull": false}, {"fieldPath": "receivedFlax", "columnName": "receivedFlax", "affinity": "REAL", "notNull": false}, {"fieldPath": "sentFlax", "columnName": "sentFlax", "affinity": "REAL", "notNull": false}, {"fieldPath": "purchasedFlax", "columnName": "purchasedFlax", "affinity": "REAL", "notNull": false}, {"fieldPath": "soldGiftFlax", "columnName": "soldGiftFlax", "affinity": "REAL", "notNull": false}, {"fieldPath": "purchasedGiftFlax", "columnName": "purchasedGiftFlax", "affinity": "REAL", "notNull": false}, {"fieldPath": "grandTotalDebit", "columnName": "grandTotalDebit", "affinity": "REAL", "notNull": false}, {"fieldPath": "podiumCameraPurchase", "columnName": "podiumCameraPurchase", "affinity": "REAL", "notNull": false}, {"fieldPath": "cancelledDeductions", "columnName": "cancelledDeductions", "affinity": "REAL", "notNull": false}, {"fieldPath": "localId", "columnName": "local_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["local_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_business_operations", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`appSharesCount` INTEGER, `dears` INTEGER, `dearsToday` INTEGER, `newAppSharePercentage` INTEGER, `newBroadcastPercentage` INTEGER, `newCustomersPercentage` INTEGER, `newFollowersPercentage` INTEGER, `newFansPercentage` INTEGER, `newHuddlePercentage` INTEGER, `newLikesPercentage` INTEGER, `newParticipantPercentage` INTEGER, `fans` INTEGER, `fansToday` INTEGER, `huddlesCount` INTEGER, `likers` INTEGER, `rating` INTEGER, `appReviewStatus` TEXT, `followers` INTEGER, `membership` TEXT NOT NULL, `minimumPpRequired` REAL, `participantCount` INTEGER, `payoutEligiblity` INTEGER, `percentOfAppShares` INTEGER, `percentOfFollowers` INTEGER, `percentOfLikes` INTEGER, `percentOfParticipants` INTEGER, `profileCompletePercentage` INTEGER, `totalBroadcasts` INTEGER, `totalLikes` INTEGER, `appSharesTarget` INTEGER, `broadcastTarget` INTEGER, `customersTarget` INTEGER, `followersTarget` INTEGER, `huddlesTarget` INTEGER, `likesTarget` INTEGER, `participantsTarget` INTEGER, `fansTarget` INTEGER, `payout_minimumPpForFirstPayout` REAL, `payout_minimumPpForFourthPayout` REAL, `payout_minimumPpForSecondPayout` REAL, `payout_minimumPpForThirdPayout` REAL, `payout_payoutMinAppSharesMonthly` INTEGER, `payout_payoutMinBroadcasts` INTEGER, `payout_payoutMinDears` INTEGER, `payout_payoutMinHuddles` INTEGER, `payout_payoutMinLikes` INTEGER, `payout_payoutMinNoFans` INTEGER, `payout_payoutMinNoLikers` INTEGER, `payout_payoutMinPointsForNextReview` INTEGER, `payout_payoutMinTotalParticipantsInHuddles` INTEGER, `payout_payoutMinTotalPostsByOthersParticipants` INTEGER, `payout_refundablePp` REAL, PRIMARY KEY(`membership`))", "fields": [{"fieldPath": "appSharesCount", "columnName": "appSharesCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dears<PERSON><PERSON>y", "columnName": "dears<PERSON><PERSON>y", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newAppSharePercentage", "columnName": "newAppSharePercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newBroadcastPercentage", "columnName": "newBroadcastPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newCustomersPercentage", "columnName": "newCustomersPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newFollowersPercentage", "columnName": "newFollowersPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newFansPercentage", "columnName": "newFansPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newHuddlePercentage", "columnName": "newHuddlePercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newLikesPercentage", "columnName": "newLikesPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "newParticipantPercentage", "columnName": "newParticipantPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fansToday", "columnName": "fansToday", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddlesCount", "columnName": "huddlesCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "rating", "columnName": "rating", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "appReviewStatus", "columnName": "appReviewStatus", "affinity": "TEXT", "notNull": false}, {"fieldPath": "followers", "columnName": "followers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "minimumPpRequired", "columnName": "minimumPpRequired", "affinity": "REAL", "notNull": false}, {"fieldPath": "participantCount", "columnName": "participantCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutEligiblity", "columnName": "payoutEligiblity", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "percentOfAppShares", "columnName": "percentOfAppShares", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "percentOfFollowers", "columnName": "percentOfFollowers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "percentOfLikes", "columnName": "percentOfLikes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "percentOfParticipants", "columnName": "percentOfParticipants", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "profileCompletePercentage", "columnName": "profileCompletePercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalBroadcasts", "columnName": "totalBroadcasts", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalLikes", "columnName": "totalLikes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.appSharesTarget", "columnName": "appSharesTarget", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.broadcastTarget", "columnName": "broadcastTarget", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.customersTarget", "columnName": "customersTarget", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.followersTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.huddlesTarget", "columnName": "huddles<PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.likesTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.participantsTarget", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requirementTargets.fansTarget", "columnName": "fansTarget", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.minimumPpForFirstPayout", "columnName": "payout_minimumPpForFirstPayout", "affinity": "REAL", "notNull": false}, {"fieldPath": "payoutStatus.minimumPpForFourthPayout", "columnName": "payout_minimumPpForFourthPayout", "affinity": "REAL", "notNull": false}, {"fieldPath": "payoutStatus.minimumPpForSecondPayout", "columnName": "payout_minimumPpForSecondPayout", "affinity": "REAL", "notNull": false}, {"fieldPath": "payoutStatus.minimumPpForThirdPayout", "columnName": "payout_minimumPpForThirdPayout", "affinity": "REAL", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinAppSharesMonthly", "columnName": "payout_payoutMinAppSharesMonthly", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinBroadcasts", "columnName": "payout_payoutMinBroadcasts", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinDears", "columnName": "payout_payoutMinDears", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinHuddles", "columnName": "payout_payoutMinHuddles", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinLikes", "columnName": "payout_payoutMinLikes", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinNoFans", "columnName": "payout_payoutMinNoFans", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinNoLikers", "columnName": "payout_payoutMinNoLikers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinPointsForNextReview", "columnName": "payout_payoutMinPointsForNextReview", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinTotalParticipantsInHuddles", "columnName": "payout_payoutMinTotalParticipantsInHuddles", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.payoutMinTotalPostsByOthersParticipants", "columnName": "payout_payoutMinTotalPostsByOthersParticipants", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutStatus.refundablePp", "columnName": "payout_refundablePp", "affinity": "REAL", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["membership"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_task_one", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`about` TEXT, `dob` TEXT, `email` TEXT, `emailVerified` INTEGER, `gender` TEXT, `id` INTEGER NOT NULL, `membership` TEXT, `name` TEXT, `paypalId` TEXT, `paypalVerified` INTEGER, `phone` TEXT, `profileCompletePercentage` INTEGER NOT NULL, `profilePhoto` TEXT, `username` TEXT, `verified` INTEGER, `totalPayoutsProcessed` INTEGER, `home_timeCreated` TEXT, `home_timeUpdated` TEXT, `home_id` INTEGER, `home_recipientName` TEXT, `home_addressLine1` TEXT, `home_addressLine2` TEXT, `home_addressLine3` TEXT, `home_zipCode` TEXT, `home_city` TEXT, `home_country` TEXT, `home_phone` TEXT, `home_countryCode` TEXT, PRIMARY <PERSON>EY(`id`))", "fields": [{"fieldPath": "about", "columnName": "about", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dob", "columnName": "dob", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "emailVerified", "columnName": "emailVerified", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paypalId", "columnName": "paypalId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paypalVerified", "columnName": "paypalVerified", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileCompletePercentage", "columnName": "profileCompletePercentage", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "profilePhoto", "columnName": "profilePhoto", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": false}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalPayoutsProcessed", "columnName": "totalPayoutsProcessed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "homeAddress.timeCreated", "columnName": "home_timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.timeUpdated", "columnName": "home_timeUpdated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.id", "columnName": "home_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "homeAddress.recipientName", "columnName": "home_recipientName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.addressLine1", "columnName": "home_addressLine1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.addressLine2", "columnName": "home_addressLine2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.addressLine3", "columnName": "home_addressLine3", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.zipCode", "columnName": "home_zipCode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.city", "columnName": "home_city", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.country", "columnName": "home_country", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.phone", "columnName": "home_phone", "affinity": "TEXT", "notNull": false}, {"fieldPath": "homeAddress.countryCode", "columnName": "home_countryCode", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_business_operations_payout_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`forwardedTo` INTEGER, `forwardedToId` INTEGER, `id` INTEGER NOT NULL, `membership` TEXT, `name` TEXT, `profileImage` TEXT, `requestedDate` TEXT, `requestedPointsForReview` REAL, `status` TEXT, `statusId` INTEGER, `timeCreated` TEXT, `timeUpdated` TEXT, `userId` INTEGER, `verified` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "forwardedTo", "columnName": "forwardedTo", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "forwardedToId", "columnName": "forwardedToId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileImage", "columnName": "profileImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "requestedDate", "columnName": "requestedDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "requestedPointsForReview", "columnName": "requestedPointsForReview", "affinity": "REAL", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "statusId", "columnName": "statusId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_notifications", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `action` TEXT, `associateObjId` INTEGER, `body` TEXT, `category` TEXT, `delivered_time` TEXT, `highlightText` TEXT, `iconPath` TEXT, `isDeleted` INTEGER, `readTime` TEXT, `isViewed` TEXT, `normalText` TEXT, `receiverId` INTEGER, `senderId` INTEGER, `status` TEXT, `timeCreated` TEXT, `timeUpdated` TEXT, `title` TEXT, `is_private` INTEGER, `associate_data` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "action", "columnName": "action", "affinity": "TEXT", "notNull": false}, {"fieldPath": "associateObjId", "columnName": "associateObjId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "body", "columnName": "body", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deliveredTime", "columnName": "delivered_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "highlightText", "columnName": "highlightText", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iconPath", "columnName": "iconPath", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isDeleted", "columnName": "isDeleted", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "readTime", "columnName": "readTime", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "normalText", "columnName": "normalText", "affinity": "TEXT", "notNull": false}, {"fieldPath": "receiverId", "columnName": "receiverId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "senderId", "columnName": "senderId", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "timeUpdated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isPrivate", "columnName": "is_private", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "assosiateData", "columnName": "associate_data", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_subscription_details", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`currency` TEXT NOT NULL, `payment_mode` TEXT NOT NULL, `price` INTEGER NOT NULL, `restoring_purchases` INTEGER NOT NULL, `timezone` TEXT NOT NULL, `type` TEXT NOT NULL, `id` INTEGER PRIMARY KEY AUTOINCREMENT, `userid` INTEGER NOT NULL, `flax` INTEGER, `coins` INTEGER, `additionalColumn` TEXT, `payload_developerPayloadAndroid` TEXT, `payload_isAcknowledgedAndroid` INTEGER, `payload_obfuscatedAccountIdAndroid` TEXT, `payload_obfuscatedProfileIdAndroid` TEXT, `payload_orderId` TEXT, `payload_packageNameAndroid` TEXT, `payload_productId` TEXT, `payload_purchaseStateAndroid` INTEGER, `payload_purchaseToken` TEXT, `payload_signatureAndroid` TEXT, `payload_transactionDate` INTEGER, `payload_transactionId` TEXT, `payload_receipt_acknowledged` INTEGER, `payload_receipt_orderId` TEXT, `payload_receipt_packageName` TEXT, `payload_receipt_productId` TEXT, `payload_receipt_purchaseState` INTEGER, `payload_receipt_purchaseTime` INTEGER, `payload_receipt_purchaseToken` TEXT)", "fields": [{"fieldPath": "currency", "columnName": "currency", "affinity": "TEXT", "notNull": true}, {"fieldPath": "payment_mode", "columnName": "payment_mode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "price", "columnName": "price", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "restoring_purchases", "columnName": "restoring_purchases", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timezone", "columnName": "timezone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userid", "columnName": "userid", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "flaxRate", "columnName": "flax", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "coins", "columnName": "coins", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "additionalColumn", "columnName": "additionalColumn", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.developerPayloadAndroid", "columnName": "payload_developerPayloadAndroid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.isAcknowledgedAndroid", "columnName": "payload_isAcknowledgedAndroid", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.obfuscatedAccountIdAndroid", "columnName": "payload_obfuscatedAccountIdAndroid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.obfuscatedProfileIdAndroid", "columnName": "payload_obfuscatedProfileIdAndroid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.orderId", "columnName": "payload_orderId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.packageNameAndroid", "columnName": "payload_packageNameAndroid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.productId", "columnName": "payload_productId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.purchaseStateAndroid", "columnName": "payload_purchaseStateAndroid", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.purchaseToken", "columnName": "payload_purchaseToken", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.signatureAndroid", "columnName": "payload_signatureAndroid", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.transactionDate", "columnName": "payload_transactionDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.transactionId", "columnName": "payload_transactionId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.transactionReceipt.acknowledged", "columnName": "payload_receipt_acknowledged", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.transactionReceipt.orderId", "columnName": "payload_receipt_orderId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.transactionReceipt.packageName", "columnName": "payload_receipt_packageName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.transactionReceipt.productId", "columnName": "payload_receipt_productId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payload.transactionReceipt.purchaseState", "columnName": "payload_receipt_purchaseState", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.transactionReceipt.purchaseTime", "columnName": "payload_receipt_purchaseTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payload.transactionReceipt.purchaseToken", "columnName": "payload_receipt_purchaseToken", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_other_user", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `thumbnail` TEXT, `username` TEXT NOT NULL, `membership` TEXT NOT NULL, `verified` INTEGER NOT NULL, `stars` INTEGER NOT NULL, `likers` INTEGER NOT NULL, `dears` INTEGER NOT NULL, `fans` INTEGER NOT NULL, `user_status` TEXT, `about` TEXT, `joinday` TEXT, `isSuperstar` INTEGER NOT NULL, `followerType` TEXT, `isStar` INTEGER NOT NULL, `muted` INTEGER, `broadcast_likers_privacy` INTEGER, `total_broadcasts` INTEGER, `broadcast_received` INTEGER, `total_likers` INTEGER, `total_huddle_owned` INTEGER, `total_huddles_admin` INTEGER, `total_huddles_participant` INTEGER, `last_seen` TEXT, `online` INTEGER NOT NULL, `notification` TEXT, `sound_track` TEXT, `preview` INTEGER, `dage` INTEGER, `flax_increment` INTEGER, `flax_rate` REAL, `flax_rate_percentage` REAL, `blocked_by_leader` INTEGER, `blocked_by_admin` INTEGER, `contributor_level` TEXT, `player_level` TEXT, `subscription_expiry_date` TEXT, `issue_date` TEXT, `total_huddle_joined` INTEGER, `total_received_gifts` INTEGER, `total_flash_published` INTEGER, `total_public_podiums` INTEGER, `userManagedHuddlesParticipants` INTEGER, `tribe_participants_count` INTEGER, `user_empower` TEXT, `countryCode` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": true}, {"fieldPath": "verified", "columnName": "verified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stars", "columnName": "stars", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likers", "columnName": "likers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dears", "columnName": "dears", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fans", "columnName": "fans", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "citizenship", "columnName": "user_status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "about", "columnName": "about", "affinity": "TEXT", "notNull": false}, {"fieldPath": "joinDay", "columnName": "joinday", "affinity": "TEXT", "notNull": false}, {"fieldPath": "is<PERSON><PERSON><PERSON><PERSON>", "columnName": "is<PERSON><PERSON><PERSON><PERSON>", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "followerType", "columnName": "followerType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isStar", "columnName": "isStar", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "muted", "columnName": "muted", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "likersPrivacy", "columnName": "broadcast_likers_privacy", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalBroadcasts", "columnName": "total_broadcasts", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "broadcastReceived", "columnName": "broadcast_received", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalLikers", "columnName": "total_likers", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalHuddleOwned", "columnName": "total_huddle_owned", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalHuddlesAdmin", "columnName": "total_huddles_admin", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalHuddlesParticipant", "columnName": "total_huddles_participant", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastSeen", "columnName": "last_seen", "affinity": "TEXT", "notNull": false}, {"fieldPath": "online", "columnName": "online", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notification", "columnName": "notification", "affinity": "TEXT", "notNull": false}, {"fieldPath": "soundTrack", "columnName": "sound_track", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preview", "columnName": "preview", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dage", "columnName": "dage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "flaxIncrement", "columnName": "flax_increment", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "flaxRate", "columnName": "flax_rate", "affinity": "REAL", "notNull": false}, {"fieldPath": "flaxRatePercentage", "columnName": "flax_rate_percentage", "affinity": "REAL", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "blocked_by_leader", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "blocked_by_admin", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "contributorLevel", "columnName": "contributor_level", "affinity": "TEXT", "notNull": false}, {"fieldPath": "playerLevel", "columnName": "player_level", "affinity": "TEXT", "notNull": false}, {"fieldPath": "subscriptionExpiryDate", "columnName": "subscription_expiry_date", "affinity": "TEXT", "notNull": false}, {"fieldPath": "issueDate", "columnName": "issue_date", "affinity": "TEXT", "notNull": false}, {"fieldPath": "totalHuddleJoined", "columnName": "total_huddle_joined", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalReceivedGifts", "columnName": "total_received_gifts", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalFlashPublished", "columnName": "total_flash_published", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "totalPublicPodiums", "columnName": "total_public_podiums", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userManagedHuddlesParticipants", "columnName": "userManagedHuddlesParticipants", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "tribeParticipantsCount", "columnName": "tribe_participants_count", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userEmpowerment", "columnName": "user_empower", "affinity": "TEXT", "notNull": false}, {"fieldPath": "countryCode", "columnName": "countryCode", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_huddles_post_comments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`commentId` TEXT NOT NULL, `huddleId` INTEGER NOT NULL, `messageId` TEXT NOT NULL, `created` TEXT, `message` TEXT, `isReported` INTEGER, `senderId` INTEGER NOT NULL, `senderDetails` TEXT, `media` TEXT, PRIMARY KEY(`commentId`))", "fields": [{"fieldPath": "commentId", "columnName": "commentId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "huddleId", "columnName": "huddleId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "messageId", "columnName": "messageId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "created", "columnName": "created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "comment", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isReported", "columnName": "isReported", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "senderId", "columnName": "senderId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "senderDetails", "columnName": "senderDetails", "affinity": "TEXT", "notNull": false}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["commentId"]}, "indices": [{"name": "index_fls_huddles_post_comments_huddleId", "unique": false, "columnNames": ["huddleId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_post_comments_huddleId` ON `${TABLE_NAME}` (`huddleId`)"}, {"name": "index_fls_huddles_post_comments_messageId", "unique": false, "columnNames": ["messageId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_huddles_post_comments_messageId` ON `${TABLE_NAME}` (`messageId`)"}], "foreignKeys": []}, {"tableName": "fls_transfer_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`flax` REAL, `id` INTEGER NOT NULL, `purpose` TEXT, `purpose_id` INTEGER, `sender_id` INTEGER, `receiver_id` INTEGER, `status` TEXT, `time_created` TEXT, `time_updated` TEXT, `user_id` TEXT, `withdrawn` INTEGER, `name` TEXT, `username` TEXT, `profile_photo` TEXT, `premium` INTEGER, `is_deleted` INTEGER, `received_flax` REAL, `sent_flax` REAL, `membership` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "flax", "columnName": "flax", "affinity": "REAL", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "purpose", "columnName": "purpose", "affinity": "TEXT", "notNull": false}, {"fieldPath": "purposeId", "columnName": "purpose_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "senderId", "columnName": "sender_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "receiverId", "columnName": "receiver_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "withdrawn", "columnName": "withdrawn", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profilePhoto", "columnName": "profile_photo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "premium", "columnName": "premium", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isDeleted", "columnName": "is_deleted", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "receivedFlax", "columnName": "received_flax", "affinity": "REAL", "notNull": false}, {"fieldPath": "sentFlax", "columnName": "sent_flax", "affinity": "REAL", "notNull": false}, {"fieldPath": "membership", "columnName": "membership", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_polls_list", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`answers` TEXT NOT NULL, `end_date` TEXT, `huddle_id` INTEGER NOT NULL, `id` INTEGER NOT NULL, `question` TEXT, `start_date` TEXT, `time_created` TEXT NOT NULL, `time_updated` TEXT NOT NULL, `end_date_iso` TEXT, `start_date_iso` TEXT NOT NULL, `visitors_count` INTEGER NOT NULL, `citizens_count` INTEGER NOT NULL, `total_answered` INTEGER NOT NULL, `user_answer` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "answers", "columnName": "answers", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endDate", "columnName": "end_date", "affinity": "TEXT", "notNull": false}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "question", "columnName": "question", "affinity": "TEXT", "notNull": false}, {"fieldPath": "startDate", "columnName": "start_date", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT", "notNull": true}, {"fieldPath": "endDateIso", "columnName": "end_date_iso", "affinity": "TEXT", "notNull": false}, {"fieldPath": "startDateIso", "columnName": "start_date_iso", "affinity": "TEXT", "notNull": true}, {"fieldPath": "visitorCount", "columnName": "visitors_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "citizenCount", "columnName": "citizens_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalAnswered", "columnName": "total_answered", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userAnswer", "columnName": "user_answer", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_podium", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `about` TEXT, `category` TEXT NOT NULL, `thumbnail` TEXT, `profile_pic` TEXT, `invite_code` TEXT, `kind` TEXT, `manager_id` INTEGER NOT NULL, `manager_name` TEXT NOT NULL, `role` TEXT, `is_private` INTEGER NOT NULL DEFAULT 0, `entry` TEXT, `live` INTEGER NOT NULL, `live_users` INTEGER NOT NULL, `total_users` INTEGER NOT NULL, `is_invited` INTEGER, `created` TEXT, `updated` TEXT, `started` TEXT, `last_go_live_time` TEXT, `total_likes` INTEGER NOT NULL, `likes` INTEGER NOT NULL DEFAULT 0, `likes_disabled` INTEGER, `is_liked_by_self` INTEGER NOT NULL, `temp_id` TEXT NOT NULL, `share_link` TEXT, `invited_to_be_admin` INTEGER, `chat_disabled` INTEGER, `chat_disabled_by` INTEGER, `mic_disabled` INTEGER, `gifts_count` INTEGER NOT NULL DEFAULT 0, `freeze` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bio", "columnName": "about", "affinity": "TEXT", "notNull": false}, {"fieldPath": "category", "columnName": "category", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbnail", "columnName": "thumbnail", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profilePic", "columnName": "profile_pic", "affinity": "TEXT", "notNull": false}, {"fieldPath": "inviteCode", "columnName": "invite_code", "affinity": "TEXT", "notNull": false}, {"fieldPath": "kind", "columnName": "kind", "affinity": "TEXT", "notNull": false}, {"fieldPath": "managerId", "columnName": "manager_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "manager_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isPrivate", "columnName": "is_private", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "entry", "columnName": "entry", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isLive", "columnName": "live", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "liveUsers", "columnName": "live_users", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalUsers", "columnName": "total_users", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "wasInvited", "columnName": "is_invited", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "createdTime", "columnName": "created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedTime", "columnName": "updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "startedTime", "columnName": "started", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastGoLiveTime", "columnName": "last_go_live_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "totalLikes", "columnName": "total_likes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likes", "columnName": "likes", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "likesDisabled", "columnName": "likes_disabled", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "liked", "columnName": "is_liked_by_self", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tempId", "columnName": "temp_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "shareLink", "columnName": "share_link", "affinity": "TEXT", "notNull": false}, {"fieldPath": "invitedToBeAdmin", "columnName": "invited_to_be_admin", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatDisabled", "columnName": "chat_disabled", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chatDisabledBy", "columnName": "chat_disabled_by", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "requestToSpeakDisabled", "columnName": "mic_disabled", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "giftCount", "columnName": "gifts_count", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "chatFrozen", "columnName": "freeze", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_sell_huddle", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`about` TEXT, `flax` REAL, `huddle_id` INTEGER, `huddle_name` TEXT, `id` INTEGER, `owner` INTEGER, `participant_count` INTEGER, `status` TEXT, `time_created` TEXT, `time_updated` TEXT, `huddle_photo` TEXT, `manager_name` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "about", "columnName": "about", "affinity": "TEXT", "notNull": false}, {"fieldPath": "flax", "columnName": "flax", "affinity": "REAL", "notNull": false}, {"fieldPath": "huddleId", "columnName": "huddle_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddleName", "columnName": "huddle_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "owner", "columnName": "owner", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "participantCount", "columnName": "participant_count", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeCreated", "columnName": "time_created", "affinity": "TEXT", "notNull": false}, {"fieldPath": "timeUpdated", "columnName": "time_updated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "huddleP<PERSON><PERSON>", "columnName": "huddle_photo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "manager_name", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_gift_video", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`gift_id` INTEGER NOT NULL, `key` TEXT NOT NULL, `name` TEXT NOT NULL, `path` TEXT NOT NULL, PRIMARY KEY(`gift_id`))", "fields": [{"fieldPath": "giftId", "columnName": "gift_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["gift_id"]}, "indices": [{"name": "index_fls_gift_video_gift_id", "unique": true, "columnNames": ["gift_id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_gift_video_gift_id` ON `${TABLE_NAME}` (`gift_id`)"}, {"name": "index_fls_gift_video_key", "unique": true, "columnNames": ["key"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_fls_gift_video_key` ON `${TABLE_NAME}` (`key`)"}], "foreignKeys": []}, {"tableName": "fls_postat", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `postat_type` TEXT NOT NULL, `hide` INTEGER, `userId` INTEGER NOT NULL, `temp_id` TEXT, `time_created` TEXT NOT NULL, `sent` TEXT, `media_count` INTEGER, `turn_off_comments` INTEGER, `is_original_audio` INTEGER NOT NULL, `media` TEXT NOT NULL, `has_mention` INTEGER, `mentioned_users` TEXT NOT NULL, `music_data` TEXT, `message` TEXT, `color` TEXT, `sender_details` TEXT NOT NULL, `total_gift_count` INTEGER NOT NULL, `total_gift_value` REAL NOT NULL, `total_comments` INTEGER NOT NULL, `is_followed` INTEGER, `sendStatus` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "messageId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "postat_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hide", "columnName": "hide", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tempId", "columnName": "temp_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "created", "columnName": "time_created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sent", "columnName": "sent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "mediaCount", "columnName": "media_count", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "turnOffComments", "columnName": "turn_off_comments", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isOriginalAudio", "columnName": "is_original_audio", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "media", "columnName": "media", "affinity": "TEXT", "notNull": true}, {"fieldPath": "hasMention", "columnName": "has_mention", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "mentionedUsers", "columnName": "mentioned_users", "affinity": "TEXT", "notNull": true}, {"fieldPath": "musicData", "columnName": "music_data", "affinity": "TEXT", "notNull": false}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "color", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "senderDetails", "columnName": "sender_details", "affinity": "TEXT", "notNull": true}, {"fieldPath": "totalGiftCount", "columnName": "total_gift_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalGiftValue", "columnName": "total_gift_value", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalComments", "columnName": "total_comments", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFollowed", "columnName": "is_followed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_postat_feed", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`postat_tab` TEXT NOT NULL, `id` TEXT NOT NULL, `postat_type` TEXT NOT NULL, `hide` INTEGER, `userId` INTEGER NOT NULL, `temp_id` TEXT, `time_created` TEXT NOT NULL, `sent` TEXT, `media_count` INTEGER, `turn_off_comments` INTEGER, `is_original_audio` INTEGER NOT NULL, `media` TEXT NOT NULL, `has_mention` INTEGER, `mentioned_users` TEXT NOT NULL, `music_data` TEXT, `message` TEXT, `color` TEXT, `sender_details` TEXT NOT NULL, `total_gift_count` INTEGER NOT NULL, `total_gift_value` REAL NOT NULL, `total_comments` INTEGER NOT NULL, `is_followed` INTEGER, `sendStatus` TEXT, PRIMARY KEY(`id`, `postat_tab`))", "fields": [{"fieldPath": "postatTab", "columnName": "postat_tab", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.messageId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.type", "columnName": "postat_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.hide", "columnName": "hide", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "postat.userId", "columnName": "userId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postat.tempId", "columnName": "temp_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "postat.created", "columnName": "time_created", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.sent", "columnName": "sent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "postat.mediaCount", "columnName": "media_count", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "postat.turnOffComments", "columnName": "turn_off_comments", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "postat.isOriginalAudio", "columnName": "is_original_audio", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postat.media", "columnName": "media", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.hasMention", "columnName": "has_mention", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "postat.mentionedUsers", "columnName": "mentioned_users", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.musicData", "columnName": "music_data", "affinity": "TEXT", "notNull": false}, {"fieldPath": "postat.message", "columnName": "message", "affinity": "TEXT", "notNull": false}, {"fieldPath": "postat.color", "columnName": "color", "affinity": "TEXT", "notNull": false}, {"fieldPath": "postat.senderDetails", "columnName": "sender_details", "affinity": "TEXT", "notNull": true}, {"fieldPath": "postat.totalGiftCount", "columnName": "total_gift_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postat.totalGiftValue", "columnName": "total_gift_value", "affinity": "REAL", "notNull": true}, {"fieldPath": "postat.totalComments", "columnName": "total_comments", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postat.isFollowed", "columnName": "is_followed", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "postat.sendStatus", "columnName": "sendStatus", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id", "postat_tab"]}, "indices": [], "foreignKeys": []}, {"tableName": "fls_postat_media", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`mediaId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `id` TEXT NOT NULL, `path` TEXT NOT NULL, `key` TEXT NOT NULL, `meta` TEXT NOT NULL, `uploadState` TEXT NOT NULL)", "fields": [{"fieldPath": "mediaId", "columnName": "mediaId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "postatId", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "meta", "columnName": "meta", "affinity": "TEXT", "notNull": true}, {"fieldPath": "uploadState", "columnName": "uploadState", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["mediaId"]}, "indices": [{"name": "index_fls_postat_media_id", "unique": false, "columnNames": ["id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_fls_postat_media_id` ON `${TABLE_NAME}` (`id`)"}], "foreignKeys": []}, {"tableName": "fls_business_flashat", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`citizenship` TEXT, `eligibility` INTEGER, `premium` INTEGER, `app_rating_isSatisfied` INTEGER, `app_rating_status` TEXT, `cus_currentFans` INTEGER, `cus_customersPercentage` REAL, `cus_isSatisfied` INTEGER, `cus_requiredFans` INTEGER, `elig_days_currentDays` INTEGER, `elig_days_isSatisfied` INTEGER, `elig_days_requiredDays` INTEGER, `flax_balance_currentFlaxBalance` REAL, `flax_balance_isSatisfied` INTEGER, `flax_balance_requiredFlaxBalance` INTEGER, `huddles_currentHuddlesCount` INTEGER, `huddles_currentParticipantCount` INTEGER, `huddles_huddlePercentage` INTEGER, `huddles_isSatisfied` INTEGER, `huddles_requiredHuddleCount` INTEGER, `huddles_requiredParticipantCount` INTEGER, `Pay_exist_timeCreated` TEXT, `Pay_exist_timeUpdated` TEXT, `prof_complecurrentPercentage` INTEGER, `prof_compleisSatisfied` INTEGER, `prof_complemobileVerified` INTEGER, `prof_complerequiredPercentage` INTEGER, `user_rating_currentUserRating` REAL, `user_rating_isSatisfied` INTEGER, `user_rating_requiredUserRating` INTEGER, PRIMARY KEY(`premium`))", "fields": [{"fieldPath": "citizenship", "columnName": "citizenship", "affinity": "TEXT", "notNull": false}, {"fieldPath": "eligibility", "columnName": "eligibility", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "premium", "columnName": "premium", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "appRating.isSatisfied", "columnName": "app_rating_isSatisfied", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "appRating.status", "columnName": "app_rating_status", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customers.currentFans", "columnName": "cus_currentFans", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "customers.customersPercentage", "columnName": "cus_customersPercentage", "affinity": "REAL", "notNull": false}, {"fieldPath": "customers.isSatisfied", "columnName": "cus_isSatisfied", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "customers.requiredFans", "columnName": "cus_requiredFans", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "eligibilityInDays.currentDays", "columnName": "elig_days_currentDays", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "eligibilityInDays.isSatisfied", "columnName": "elig_days_isSatisfied", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "eligibilityInDays.requiredDays", "columnName": "elig_days_requiredDays", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "flaxBalance.currentFlaxBalance", "columnName": "flax_balance_currentFlaxBalance", "affinity": "REAL", "notNull": false}, {"fieldPath": "flaxBalance.isSatisfied", "columnName": "flax_balance_isSatisfied", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "flaxBalance.requiredFlaxBalance", "columnName": "flax_balance_requiredFlaxBalance", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddles.currentHuddlesCount", "columnName": "huddles_currentHuddlesCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddles.currentParticipantCount", "columnName": "huddles_currentParticipantCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddles.huddlePercentage", "columnName": "huddles_huddlePercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddles.isSatisfied", "columnName": "huddles_isSatisfied", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddles.requiredHuddleCount", "columnName": "huddles_requiredHuddleCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "huddles.requiredParticipantCount", "columnName": "huddles_requiredParticipantCount", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "payoutExist.timeCreated", "columnName": "Pay_exist_timeCreated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "payoutExist.timeUpdated", "columnName": "Pay_exist_timeUpdated", "affinity": "TEXT", "notNull": false}, {"fieldPath": "profileCompleteness.currentPercentage", "columnName": "prof_complecurrentPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "profileCompleteness.isSatisfied", "columnName": "prof_compleisSatisfied", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "profileCompleteness.mobileVerified", "columnName": "prof_complemobileVerified", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "profileCompleteness.requiredPercentage", "columnName": "prof_complerequiredPercentage", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userRating.currentUserRating", "columnName": "user_rating_currentUserRating", "affinity": "REAL", "notNull": false}, {"fieldPath": "userRating.isSatisfied", "columnName": "user_rating_isSatisfied", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "userRating.requiredUserRating", "columnName": "user_rating_requiredUserRating", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["premium"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'eb8d86498e25cf1405cdfe1cd43d9723')"]}}