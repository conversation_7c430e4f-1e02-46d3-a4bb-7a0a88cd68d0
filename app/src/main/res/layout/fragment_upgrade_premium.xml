<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <import type="android.view.View"/>
        <variable
            name="viewModel"
            type="com.app.messej.ui.premium.UpgradePremiumViewModel" />
    </data>
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_gradient_upgrade_premium"
        tools:context=".ui.premium.UpgradePremiumFragment">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="fitEnd"
            android:src="@drawable/bg_upgrade"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/nestedScrollView" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <Button
                    android:id="@+id/back"
                    style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/activity_margin"
                    android:layout_marginTop="@dimen/double_margin"
                    app:icon="@drawable/ic_search_location_back"
                    app:iconTint="@color/textColorOnPrimary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/flashat_text"
                    style="@style/TextAppearance.Flashat.Headline1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/double_margin"
                    android:layout_marginTop="@dimen/extra_margin"
                    android:text="@string/upgrade_flashat"
                    android:textColor="@color/textColorOnPrimary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/back" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/premium_text"
                    style="@style/TextAppearance.Flashat.Headline4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/double_margin"
                    android:text="@string/upgrade_premium"
                    android:textColor="@color/textColorOnPrimary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/flashat_text" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/double_margin"
                    android:adjustViewBounds="true"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_upgrade_flashat_crown"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/flashat_text" />

                <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
                    android:id="@+id/dot_indicator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/double_margin"
                    android:layout_marginBottom="@dimen/double_margin"
                    app:dotsColor="@color/textColorSecondary"
                    app:dotsCornerRadius="8dp"
                    app:dotsSize="10dp"
                    app:dotsSpacing="4dp"
                    app:dotsWidthFactor="5"
                    app:layout_constraintBottom_toTopOf="@+id/viewPager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/premium_text"
                    app:progressMode="false"
                    app:selectedDotColor="@color/textColorOnPrimary" />

                <androidx.viewpager2.widget.ViewPager2
                    android:id="@+id/viewPager"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/extra_margin"
                    android:layout_marginBottom="@dimen/double_margin"
                    android:paddingStart="30dp"
                    android:paddingEnd="30dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/dot_indicator" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/subscription_title"
                    style="@style/TextAppearance.Flashat.Headline5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/double_margin"
                    android:layout_marginTop="50dp"
                    android:layout_marginEnd="@dimen/extra_margin"
                    android:text="@string/common_upgrade_now"
                    app:textAllCaps="true"
                    android:textColor="@color/textColorOnPrimary"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/viewPager" />


                <RadioGroup
                    android:id="@+id/subscription_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/double_margin"
                    android:layout_marginEnd="@dimen/double_margin"
                    android:layout_marginTop="@dimen/double_margin"
                    app:layout_constraintTop_toBottomOf="@id/subscription_title">

                    <RadioButton
                        android:id="@+id/radio_flix_method"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:text="You have 20 FLiX\nYou need 16 FLiX more to UPGRADE"
                        android:textColor="@color/textColorOnPrimary"
                        android:buttonTint="@color/colorPrimary"  />

                    <RadioButton
                        android:id="@+id/radio_iap_method"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/element_spacing"
                        tools:text="Premium via In-App Purchase\nUpgrade to Premium with 48 USD"
                        android:textColor="@color/textColorOnPrimary"
                        android:buttonTint="@color/colorPrimary" />
                </RadioGroup>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_upgrade"
                    style="@style/Widget.Flashat.SmallRoundedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/extra_margin"
                    android:layout_marginBottom="@dimen/extra_margin"
                    android:background="@drawable/bg_upgrade_button"
                    app:backgroundTint="@null"
                    android:text="@string/common_proceed"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/subscription_type" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>
    </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>