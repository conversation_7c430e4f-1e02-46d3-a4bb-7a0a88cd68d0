package com.app.messej.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.core.app.NotificationManagerCompat

class NotificationCancelReceiver : BroadcastReceiver(){
    override fun onReceive(context: Context?, intent: Intent?) {
        val notificationId = intent?.getIntExtra("notificationId", -1)
        if (notificationId != -1) {
            val notificationManager = context?.let { NotificationManagerCompat.from(it) }
            notificationId?.let { notificationManager?.cancel(it) } // Cancel the notification
            Log.d("Notification", "Notification $notificationId canceled.")
        }
    }

}