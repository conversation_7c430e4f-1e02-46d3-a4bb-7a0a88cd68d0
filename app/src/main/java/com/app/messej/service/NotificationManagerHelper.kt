package com.app.messej.service

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.provider.Settings
import android.util.Log
import com.app.messej.service.NotificationManagerHelper.Constants.PERMISSION_RESULT_ACTION

class NotificationManagerHelper(private val context: Context) {
    object Constants {
        const val PERMISSION_RESULT_ACTION = "com.app.messej.PERMISSION_RESULT_ACTION"
    }


    fun removeNotificationAfterDelay(notificationId: Int) {
        // muted this function as its causing issues with permissions
        return
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && !alarmManager.canScheduleExactAlarms()) {
            requestExactAlarmPermission {
                setExactAlarm(notificationId)
            }
        } else {
            setExactAlarm(notificationId)
        }
    }

    private fun setExactAlarm(notificationId: Int) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val triggerTime = System.currentTimeMillis() + 30_000 // 30 seconds later

        val intent = Intent(context, NotificationCancelReceiver::class.java).apply {
            putExtra("notificationId", notificationId)
        }
        val pendingIntent = PendingIntent.getBroadcast(
            context, notificationId, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
        Log.d("Alarm", "Exact alarm set for $triggerTime")
    }

    private fun requestExactAlarmPermission(onPermissionGranted: () -> Unit) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val intent = Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)

            // Register a receiver to check if permission is granted
            val permissionReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
                    if (alarmManager.canScheduleExactAlarms()) {
                        onPermissionGranted()
                    } else {
                        Log.d("AlarmPermission", "Permission not granted")
                    }
                    context.unregisterReceiver(this)
                }
            }
            context.registerReceiver(permissionReceiver, IntentFilter(PERMISSION_RESULT_ACTION), Context.RECEIVER_NOT_EXPORTED)
        } else {
            onPermissionGranted()
        }
    }
}
