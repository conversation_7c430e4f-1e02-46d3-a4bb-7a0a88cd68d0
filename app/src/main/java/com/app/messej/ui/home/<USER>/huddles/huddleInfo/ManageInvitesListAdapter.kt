package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.view.LayoutInflater
import android.view.MenuItem
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.huddles.HuddleInvitationsResponse
import com.app.messej.databinding.ItemHuddleInvitesListBinding

class ManageInvitesListAdapter(private val mListener: ItemListener) : PagingDataAdapter<HuddleInvitationsResponse.HuddleInvitation, ManageInvitesListAdapter.HuddleInvitesListViewHolder>(
    InvitesDiff
) {

    interface ItemListener {
        fun onItemClick(item: HuddleInvitationsResponse.HuddleInvitation, position: Int, menuItem: MenuItem)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        HuddleInvitesListViewHolder(
            ItemHuddleInvitesListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: HuddleInvitesListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }

    inner class HuddleInvitesListViewHolder(private val binding: ItemHuddleInvitesListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: HuddleInvitationsResponse.HuddleInvitation, position: Int) = with(binding) {
            invitation=item
            invitesActionButton.setOnClickListener {
                val popup = PopupMenu(it.context, it)
                popup.inflate(R.menu.menu_huddle_invites_list)
                popup.setOnMenuItemClickListener { menuItem ->
                    mListener.onItemClick(item, position, menuItem)
                    true
                }
                popup.show()
            }
        }
    }

    object InvitesDiff : DiffUtil.ItemCallback<HuddleInvitationsResponse.HuddleInvitation>() {
        override fun areItemsTheSame(oldItem: HuddleInvitationsResponse.HuddleInvitation, newItem: HuddleInvitationsResponse.HuddleInvitation) =
            oldItem.memberId == newItem.memberId

        override fun areContentsTheSame(oldItem: HuddleInvitationsResponse.HuddleInvitation, newItem: HuddleInvitationsResponse.HuddleInvitation) =
            oldItem == newItem
    }
}