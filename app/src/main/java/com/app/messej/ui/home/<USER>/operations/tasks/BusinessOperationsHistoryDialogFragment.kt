package com.app.messej.ui.home.businesstab.operations.tasks

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.databinding.FragmentBusinessOperationsHistoryDialogBinding
import com.app.messej.ui.customviews.ViewExtensions.setBlurredBackground
import com.app.messej.ui.customviews.ViewExtensions.setBlurredImage
import com.app.messej.ui.home.businesstab.adapter.BusinessOperationPayoutHistoryAdapter
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BusinessOperationsHistoryDialogFragment : BottomSheetDialogFragment() {

    private val viewModel: BusinessOperationsTaskThreeViewModel by activityViewModels()
    private lateinit var binding: FragmentBusinessOperationsHistoryDialogBinding
    private var mAdapter: BusinessOperationPayoutHistoryAdapter? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_operations_history_dialog, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        this.isCancelable = false
        binding.actionCloseBottomSheet.setOnClickListener { dismiss() }
        blurTransform()
    }

    private fun blurTransform() {
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                binding.bgBottomImage.setBlurredBackground(R.drawable.bg_business, requireContext())
            }
        }
        binding.imgHistoryUnderLine.setBlurredImage(R.drawable.bg_task_one_underline, 10, requireContext())
    }

    private fun setup() {
        initAdapter()
    }

    private fun observe() {
        viewModel.payoutHistoryList.observe(viewLifecycleOwner) { pagingData ->
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
        }
    }

    private fun initAdapter() {
        mAdapter = BusinessOperationPayoutHistoryAdapter()
        val layoutMan = LinearLayoutManager(context)
        binding.historyList.apply {
            layoutManager = layoutMan
            setHasFixedSize(false)
            adapter = mAdapter
        }
    }

}