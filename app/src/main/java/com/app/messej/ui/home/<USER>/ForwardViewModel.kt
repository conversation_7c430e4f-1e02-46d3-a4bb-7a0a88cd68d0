package com.app.messej.ui.home.forward

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.liveData
import com.app.messej.data.model.enums.ForwardType
import com.app.messej.data.repository.ForwardRepository
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class ForwardViewModel(application: Application) : AndroidViewModel(application) {

    private val repo=ForwardRepository(application)
    private val _tab = MutableLiveData<ForwardType?>(null)
     val tab: MutableLiveData<ForwardType?> = _tab

    private val _actionLoading:MutableLiveData<Boolean> = MutableLiveData(null)
    val actionLoading: LiveData<Boolean?> = _actionLoading

    val dataLoadingMore = MutableLiveData(false)
    val isAnyOptionSelected = MutableLiveData(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }
    fun setTab(tab: Int?) {
        when (tab) {
            0 -> _tab.value=ForwardType.MESSAGES
            1 -> _tab.value=ForwardType.GROUPS
            2 ->_tab.value=ForwardType.HUDDLES
            else->{
                _tab.value=ForwardType.MESSAGES
            }
        }

    }
    val forwardList = _tab.switchMap {
        it?: return@switchMap null
         return@switchMap repo.getForwardListing(it).liveData
    }

    var searchKeyword = MutableLiveData("")
    private val searchTerm = MutableLiveData("")

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                searchTerm.postValue(it.orEmpty())
            }
        }
    }

    fun setLoading(isLoading: Boolean) {
        _actionLoading.postValue(isLoading)
    }

    fun setOptionSelected(selected: Boolean) {
        isAnyOptionSelected.postValue(selected)
    }
}