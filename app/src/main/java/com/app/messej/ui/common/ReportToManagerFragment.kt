package com.app.messej.ui.common

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.ReportCategoryResponse.ReportCategory
import com.app.messej.data.model.api.ReportToManagerRequest
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.databinding.FragmentReportManagerBinding


class ReportToManagerFragment : Fragment() {

    private val args : ReportToManagerFragmentArgs by navArgs()
    private lateinit var binding: FragmentReportManagerBinding

    private val viewModel : ReportToManagerViewModel by viewModels()

    companion object {
        const val REPORT_REQUEST_KEY = "report_item"
        const val REPORT_TYPE = "report_type_key"
        const val REPORT_DID_REPORT = "report_success_key"

        fun parseResult(bundle: Bundle): Pair<ReportType,Boolean> {
            return Pair(
                ReportType.entries[bundle.getInt(REPORT_TYPE)],
                bundle.getBoolean(REPORT_DID_REPORT),
            )
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_report_manager, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
//        when (args.reportType) {
//            ReportToManagerType.COMMENT -> binding.customActionBar.toolbar.title = resources.getString(R.string.report_comment_title)
//            ReportToManagerType.MESSAGE -> binding.customActionBar.toolbar.title = resources.getString(R.string.report_message_title)
//        }
    }

    private fun setup() {
        when (args.reportType) {
            ReportToManagerType.COMMENT -> {
                viewModel.setReportRequest(ReportToManagerRequest.CommentReportRequest(args.id,args.messageId,args.commentId.orEmpty()))
                binding.reportTitle.text = resources.getString(R.string.report_content_reason_title)
                binding.reportButton.text = resources.getString(R.string.report_comment_title)
            }
            ReportToManagerType.MESSAGE -> {
                viewModel.setReportRequest(ReportToManagerRequest.MessageReportRequest(args.id,args.messageId))
                binding.reportTitle.text = resources.getString(R.string.report_content_reason_title)
                binding.reportButton.text = resources.getString(R.string.report_message_title)
            }
        }

        binding.reportButton.setOnClickListener {
            viewModel.reportMessage()
        }
        binding.reportCancelButton.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun observe() {

        viewModel.commentValid.observe(viewLifecycleOwner) {
            val error = if (it) null else resources.getString(R.string.report_message_comment_error_max, ReportToManagerViewModel.COMMENT_MAX_LENGTH)
            binding.tilComment.isErrorEnabled = (error!=null)
            binding.tilComment.error = error
        }

        viewModel.reportCategories.observe(viewLifecycleOwner){
            initAdapter(it?: emptyList())
        }

        viewModel.onReportMessageComplete.observe(viewLifecycleOwner) {
            Log.w("REPFRAG", "observe: onReportMessageComplete ${args.reportType} $it")
            findNavController().popBackStack()
            requireActivity().supportFragmentManager.setFragmentResult(REPORT_REQUEST_KEY, bundleOf(REPORT_TYPE to args.reportType.ordinal, REPORT_DID_REPORT to true))
        }
    }

    fun initAdapter(cats: List<ReportCategory>) {

        val adapter = object: ArrayAdapter<ReportCategory>(requireContext(), R.layout.item_general_dropdown, cats) {

            private val mCats = cats

            var selectedPos: Int? = null

            override fun getItem(position: Int) = mCats[position]
            override fun getCount() = mCats.size

            override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                var cView = convertView
                if (cView == null) {
                    cView = layoutInflater.inflate(R.layout.item_general_dropdown, parent, false)
                }
                cView!!
                getItem(position).let { cat ->
                    (cView.findViewById<AppCompatTextView>(R.id.text)).text = cat.categoryText
                    (cView.findViewById<AppCompatImageView>(R.id.checked_icon)).visibility = if (position == selectedPos) View.VISIBLE else View.GONE
                }
                return cView
            }
        }
        (binding.categoryDropdown.editText as? AutoCompleteTextView)?.apply {
            setAdapter(adapter)
            setOnItemClickListener { _, _, position, _ ->
                adapter.selectedPos = position
                val item = adapter.getItem(position)
                setText(item.categoryText)
                viewModel.reason.postValue(item)
            }
        }

        viewModel.reason.value?.let { reason ->
            val index = cats.indexOfFirst { reason.categoryId == it.categoryId }
            if(index==-1) return@let
            adapter.selectedPos = index
            (binding.categoryDropdown.editText as? AutoCompleteTextView)?.setText(reason.categoryText)
        }

    }
}