package com.app.messej.ui.home.publictab.common

import android.view.LayoutInflater
import android.view.ViewGroup
import com.app.messej.databinding.ItemChatMessageBroadcastBinding
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.chat.adapter.ChatMessageBroadcastViewHolder

class BroadcastStarredChatAdapter(private val inflater: LayoutInflater, private val userId: Int, private var mListener: ChatClickListener): ChatAdapter(inflater, userId, false, mListener) {

    private var broadcasterImage: String? = null
    fun setBroadCasterImage(image: String?) {
        broadcasterImage = image
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder = when (viewType) {
        ITEM_INCOMING_TEXT_REPLY, ITEM_INCOMING_IMAGE, ITEM_INCOMING_AUDIO, ITEM_OUTGOING_TEXT_REPLY, ITEM_OUTGOING_IMAGE, ITEM_OUTGOING_AUDIO -> {
            ChatMessageBroadcastViewHolder(ItemChatMessageBroadcastBinding.inflate(inflater, parent, false), userId, broadcasterImage, mListener)
        }
        else -> super.onCreateViewHolder(parent, viewType)
    }
}