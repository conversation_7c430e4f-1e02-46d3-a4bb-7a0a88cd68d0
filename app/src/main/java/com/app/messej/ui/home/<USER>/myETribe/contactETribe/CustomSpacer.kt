package com.app.messej.ui.home.publictab.myETribe.contactETribe

import androidx.annotation.DimenRes
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.unit.Dp

@Composable
fun CustomVerticalSpacer(space: Dp) {
    Spacer(modifier = Modifier.padding(top = space))
}

@Composable
fun CustomVerticalSpacer(@DimenRes space: Int) {
    CustomVerticalSpacer(space = dimensionResource(id = space))
}

@Composable
fun CustomHorizontalSpacer(space: Dp) {
    Spacer(modifier = Modifier.width(width = space))
}

@Composable
fun CustomHorizontalSpacer(@DimenRes space: Int) {
    CustomHorizontalSpacer(space = dimensionResource(id = space))
}