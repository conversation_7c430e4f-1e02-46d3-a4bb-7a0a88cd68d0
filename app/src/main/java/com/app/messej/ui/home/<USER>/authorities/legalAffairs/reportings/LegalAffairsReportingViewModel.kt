package com.app.messej.ui.home.publictab.authorities.legalAffairs.reportings

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.data.model.enums.LegalAffairsReporting
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalAffairsRepository

class LegalAffairsReportingViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val legalAffairsRepository = LegalAffairsRepository(application)

    private val _selectedSubTab = MutableLiveData(LegalAffairsReporting.Content)
    val selectedSubTab : LiveData<LegalAffairsReporting> = _selectedSubTab.distinctUntilChanged()

    val investigationBureauCount = MutableLiveData<LegalRecordsResponse?>(null)

    fun setTab(tab: LegalAffairsReporting) {
        _selectedSubTab.value = tab
    }

    val myLegalRecordsReportingList = _selectedSubTab.switchMap {
        legalAffairsRepository.getViolationList(
            recordType = LegalAffairTabs.Reporting.serializedName(),
            reportingType = it.serializedName()
        ).liveData.cachedIn(viewModelScope)
    }

    val investigationReportList = _selectedSubTab.switchMap {
        legalAffairsRepository.getLegalAffairsBoardDetails(
            countCallBack = { legalResponse ->
                investigationBureauCount.postValue(
                    LegalRecordsResponse(
                        banCount = legalResponse?.banCount,
                        reportsCount = legalResponse?.reportsCount,
                        hiddenCount = legalResponse?.hiddenCount
                    )
                )
            },
            recordType = REPORTS,
            tab = LegalAffairsMainTab.InvestigationBureau.serializedName(),
            reportingType = it.serializedName()
        ).liveData.cachedIn(viewModelScope)
    }
}

private const val REPORTS = "reports"