package com.app.messej.ui.home.publictab.authorities

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.repository.AccountRepository

class AuthoritiesViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(application)

    val user: CurrentUser
        get() = accountRepo.user

}