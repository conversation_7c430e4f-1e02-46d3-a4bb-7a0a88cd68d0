package com.app.messej.ui.home.publictab.broadcast

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.enums.BroadcastTab
import com.app.messej.data.model.enums.BroadcastTab.TAB_DEARS
import com.app.messej.data.model.enums.BroadcastTab.TAB_FANS
import com.app.messej.data.model.enums.BroadcastTab.TAB_LIKERS
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class PublicBroadcastSearchViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())

    var searchKeyword = MutableLiveData<String>(null)
    private val searchTerm = MutableLiveData("")

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                it?: return@collect
                if (it.isBlank()){
                    searchTerm.postValue("")
                }else{
                    searchTerm.postValue(it)
                }
            }
        }
    }

    private val userType = MutableLiveData<BroadcastTab>(null)

    fun setUserType(type: BroadcastTab?){
        userType.value = (type?: TAB_DEARS)
    }

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    val user: CurrentUser get() = accountRepo.user

    val searchList = searchTerm.switchMap {
        when(userType.value){
            TAB_DEARS -> { profileRepo.getDearsSearchListPager(it).liveData.cachedIn(viewModelScope) }
            TAB_FANS -> {
                profileRepo.getFansSearchListPager(it).liveData.cachedIn(viewModelScope)
            }
            TAB_LIKERS -> {
                profileRepo.getLikersSearchListPager(it).liveData.cachedIn(viewModelScope)
            }
            null -> {
                null
            }
            else -> {
                null
            }
        }
    }

    fun resetSearch() {
        searchKeyword.postValue("")
    }
}