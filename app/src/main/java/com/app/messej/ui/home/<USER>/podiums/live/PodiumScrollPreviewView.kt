package com.app.messej.ui.home.publictab.podiums.live

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.isVisible
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumFriend
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.databinding.ItemPodiumScrollPreviewBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.displayText
import com.google.android.flexbox.FlexDirection
import com.scwang.smart.refresh.layout.api.RefreshFooter
import com.scwang.smart.refresh.layout.api.RefreshHeader
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle
import kotlin.math.roundToInt


class PodiumScrollPreviewView : FrameLayout, RefreshHeader, RefreshFooter {

    sealed class PodiumPreview(
        val id: String,
        val kind: PodiumKind?,
        val joiningFee: Int?,
        val isAdmin : Boolean?,
        val isJoiningFeePaid:Boolean?
    ) {
        data class LivePodium(val podium: Podium): PodiumPreview(podium.id, podium.kind, podium.joiningFee, podium.isAdmin , podium.joiningFeePaid)
        data class LiveFriend(val friend: PodiumFriend): PodiumPreview(friend.podiumId, friend.podiumKind, friend.joiningFee, false, friend.joiningFeePaid)
    }

    private lateinit var binding: ItemPodiumScrollPreviewBinding

    constructor(context: Context?, attrs: AttributeSet?, defStyle: Int) : super(context!!, attrs, defStyle) {
        initView()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context!!, attrs) {
        initView()
    }

    constructor(context: Context?) : super(context!!) {
        initView()
    }

    private fun initView() {
        binding = ItemPodiumScrollPreviewBinding.inflate(LayoutInflater.from(context), this, false)
        addView(binding.root)
    }

    fun setData(preview: PodiumPreview, isHeader: Boolean) {
        when(preview) {
            is PodiumPreview.LivePodium -> {
                binding.podium = preview.podium
                preview.podium.kind?.let { kind ->
                    binding.kindChip.text = kind.displayText(this.context)
                }
            }
            is PodiumPreview.LiveFriend -> {
                binding.friend = preview.friend
                preview.friend.podiumKind?.let { kind ->
                    binding.friendKindChip.text = kind.displayText(this.context)
                }
            }
        }
        if (isHeader) {
            binding.card.flexDirection = FlexDirection.COLUMN
            binding.prompt.setText(R.string.podium_scroll_down)
        } else {
            binding.card.flexDirection = FlexDirection.COLUMN_REVERSE
            binding.prompt.setText(R.string.podium_scroll_up)
        }

    }

    override fun onStateChanged(refreshLayout: RefreshLayout, oldState: RefreshState, newState: RefreshState) {

        Log.d("PDPresenter", "onStateChanged: oldState: $oldState, newState: $newState")
        when (newState) {
            RefreshState.Refreshing, RefreshState.Loading -> {
                binding.arrow.isVisible = true
            }
            else -> {
                binding.progress.progress = 0
                binding.arrow.isVisible = false
            }
        }
    }

    override fun getView(): View {
        return this
    }

    override fun getSpinnerStyle(): SpinnerStyle {
        return SpinnerStyle.Translate
    }

    override fun setPrimaryColors(vararg colors: Int) { }

    override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {  }

    override fun onMoving(isDragging: Boolean, percent: Float, offset: Int, height: Int, maxDragHeight: Int) {
//        Log.d("PDPresenter", "onMoving: isDragging: $isDragging, percent: $percent, offset: $offset, height: $height, maxDragHeight: $maxDragHeight")
        binding.progress.progress = (percent*100).roundToInt().coerceIn(0..100)
        binding.arrow.isVisible = percent>=1

    }

    override fun onReleased(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {

    }

    override fun onStartAnimator(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
        Log.d("PDPresenter", "onStartAnimator: height: $height, maxDragHeight: $maxDragHeight")
    }

    override fun onFinish(refreshLayout: RefreshLayout, success: Boolean): Int {
        return 100
    }

    override fun onHorizontalDrag(percentX: Float, offsetX: Int, offsetMax: Int) {
    }

    override fun isSupportHorizontalDrag(): Boolean {
        return false
    }

    override fun autoOpen(duration: Int, dragRate: Float, animationOnly: Boolean): Boolean {
        return false
    }

    override fun setNoMoreData(noMoreData: Boolean): Boolean {
        return false
    }
}