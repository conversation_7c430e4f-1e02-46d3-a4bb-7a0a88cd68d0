package com.app.messej.ui.home.deleteaccount

import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentDeleteAccountMobileBinding

class DeleteAccountMobileFragment : Fragment() {

    private lateinit var binding: FragmentDeleteAccountMobileBinding

    val viewModel:DeleteBottomSheetViewModel by viewModels({ requireParentFragment() })

    private val deleteAccountViewModel:DeleteAccountViewModel by activityViewModels()

    private var accountDeletedListener: AccountDeletedListener? = null


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_delete_account_mobile, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    interface AccountDeletedListener {
        fun onAccountDeleted()
        fun onRequestBottomSheetDismiss()
    }


    private fun setup() {
        binding.deleteAccountMobileNumber.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && !this.text.isNullOrEmpty()) {
                    viewModel.didEnterPhoneNumber.postValue(true)
                } else {
                    viewModel.didEnterPhoneNumber.postValue(false)
                }
            }
        }

        binding.deleteAccountCountryPicker.apply {
            setOnCountryChangeListener {
                viewModel.countryCode.postValue(binding.deleteAccountCountryPicker.selectedCountryCodeWithPlus)
            }
            registerCarrierNumberEditText(binding.deleteAccountMobileNumber.editText)
            setPhoneNumberValidityChangeListener { valid ->
                viewModel.setPhoneNumberValid(valid)
            }
        }

        binding.deleteAccountMobileNumber.editText?.setOnEditorActionListener {
                view, actioId, event ->
            binding.deleteAccountMobileNumber.editText!!.clearFocus()
            return@setOnEditorActionListener false
        }

        binding.deleteAccountNext.setOnClickListener{

            if(binding.deleteAccountMobileNumber.editText?.text.toString().isNotEmpty()) {
               viewModel.deleteAccountPhone(binding.deleteAccountMobileNumber.editText?.text.toString(), binding.deleteAccountCountryPicker.selectedCountryCodeWithPlus)
            }

        }

        binding.actionAccountCancel.setOnClickListener {
            deleteAccountViewModel.setCancelClick(true)
            accountDeletedListener?.onRequestBottomSheetDismiss()
        }

        binding.root.rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val r = Rect()
            binding.root.rootView.getWindowVisibleDisplayFrame(r)
            val screenHeight: Int = binding.root.rootView.height
            val keypadHeight: Int = screenHeight - r.bottom
            if (keypadHeight < screenHeight * 0.15) {
                binding.deleteAccountMobileNumber.editText!!.clearFocus()
            }
        }
    }

    private fun observe() {
        viewModel.showPhoneInvalidError.observe(viewLifecycleOwner) {
            if (!binding.deleteAccountMobileNumber.editText?.text.isNullOrEmpty()) {
                binding.deleteAccountMobileError.text = if (it) {
                    binding.deleteAccountNext.isEnabled = false
                    binding.deleteAccountMobileError.text
                    resources.getString(R.string.login_error_mobile_number)
                } else {
                    binding.deleteAccountMobileError.text
                    "";null
                }
            }
        }
        viewModel.isMobileNextButtonVisible.observe(viewLifecycleOwner) {
            if (!binding.deleteAccountMobileNumber.editText?.text.isNullOrEmpty()) {
                binding.deleteAccountNext.isEnabled = it == false
            }
        }


        viewModel.unSelectedPage.observe(viewLifecycleOwner){
            if(it==0){ binding.deleteAccountMobileNumber.editText?.setText("")
                binding.deleteAccountMobileError.text = ""
                binding.deleteAccountNext.isEnabled=false
            }
        }
        viewModel.isAccountDeleted.observe(viewLifecycleOwner){
            it?.let {
                if(it) {
                    deleteAccountViewModel.setAccountDeleted(true)
                }
            }
        }

        viewModel.accountDeleteError.observe(viewLifecycleOwner){
            if(it?.isNotEmpty() == true){
                Toast.makeText(requireContext(),it,Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onAccountDeleted.observe(viewLifecycleOwner){
            if (it){
                deleteAccountViewModel.setCancelClick(true)
                accountDeletedListener?.onRequestBottomSheetDismiss()
                accountDeletedListener?.onAccountDeleted()
                Toast.makeText(requireContext(), getString(R.string.common_account_deleted), Toast.LENGTH_SHORT).show()
            }
        }
    }


    companion object {
        fun newInstance() = DeleteAccountMobileFragment()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        accountDeletedListener = parentFragment as? AccountDeletedListener
    }

}