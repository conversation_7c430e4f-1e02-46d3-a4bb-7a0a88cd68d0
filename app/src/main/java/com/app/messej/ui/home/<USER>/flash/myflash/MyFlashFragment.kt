package com.app.messej.ui.home.publictab.flash.myflash

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import com.afollestad.materialdialogs.MaterialDialog
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.NavMyFlashDirections
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideoWithMedia
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentFlashMineBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureFlashPostingAllowed
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.isTabletScreen
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView

class MyFlashFragment : Fragment(), MenuProvider {

    private var mAdapter: MyFlashAdapter? = null

    private val viewModel: MyFlashViewModel by navGraphViewModels(R.id.nav_my_flash)

    private lateinit var binding: FragmentFlashMineBinding

    private var apiLoader : MaterialDialog? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_mine, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onResume() {
        super.onResume()
        if ((mAdapter?.itemCount ?: 0) > 0) {
            mAdapter?.refresh()
        }
    }

    private fun setup() {
        initAdapter()

        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            image = R.drawable.im_eds_my_flash,
            message = if(viewModel.user.premium) R.string.flash_my_flash_eds else R.string.flash_my_flash_free_eds
        )

        binding.flashCreateLayout.addFlashButton.setOnClickListener {
            viewModel.getFlashEligibilityDetails()
        }
    }

    private fun observe() {
        viewModel.flashList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }
        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            it?.let { vs ->
                binding.multiStateView.viewState = vs
            }
        }

        viewModel.onFlashArchived.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_action_archive_toast)
            mAdapter?.refresh()
        }

        viewModel.onFlashDeleted.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_action_delete_toast)
            mAdapter?.refresh()
        }

        viewModel.actionLoading.observe(viewLifecycleOwner) {
            if (it) showAPILoader() else hideAPILoader()
        }

        viewModel.isEligibilityLoaded.observe(viewLifecycleOwner) {
            ensureFlashPostingAllowed {
                if (viewModel.user.isFlashBlocked == true) {
                    MaterialAlertDialogBuilder(requireContext())
                        .setMessage(R.string.flash_blocked_dialog)
                        .setPositiveButton(R.string.common_close) { dialog, which ->
                            dialog.dismiss()
                        }.show()
                    return@ensureFlashPostingAllowed
                }
                if (viewModel.flashEligibility.value?.flashEligibility == false) {
                    showFlashPostLimitingAlert(
                        citizenship = viewModel.user.citizenship,
                        enabledFlashCount = viewModel.flashEligibility.value?.enabledFlashCount,
                        onConfirmButtonClick = {
                            findNavController().navigateSafe(
                                MyFlashFragmentDirections.actionGlobalUpgradePremiumFragment()
                            )
                        }
                    )
                    return@ensureFlashPostingAllowed
                }
                viewModel.flashEligibility.value?.enabledFlashDuration?.let { duration ->
                    findNavController().navigateSafe(
                        NavGraphHomeDirections.actionGlobalFlashRecordFragment(
                            drafId = null, flashDuration = duration)
                    )
                }
            }
        }
    }

    private fun showAPILoader() {
        apiLoader = showLoader()
        apiLoader?.show()
    }

    private fun hideAPILoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = MyFlashAdapter(object : MyFlashAdapter.FlashClickListener {
            override fun onFlashClicked(pos: Int, flash: FlashVideoWithMedia) {
                if (flash.media!=null) {
                    viewModel.triggerSend(flash)
                } else {
                    viewModel.setActiveItem(pos)
                    findNavController().navigateSafe(MyFlashFragmentDirections.actionMyFlashFragmentToMyFlashPlayerFragment())
                }
            }
            override fun onFlashLongPressed(pos: Int, flash: FlashVideoWithMedia, view: View) {
                showLongPressMenu(flash,view)
            }
        })

        binding.flashList.apply {
            layoutManager = GridLayoutManager(requireContext(), if(isTabletScreen) 5 else 3)
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                viewModel.setViewState(state)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_flash_mine,menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    private fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_flash_mine_more, popup.menu)
        popup.setForceShowIcon(false)

        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_archived-> findNavController().navigateSafe(NavMyFlashDirections.actionGlobalNavFlashArchive())
                R.id.action_drafts -> findNavController().navigateSafe(NavMyFlashDirections.actionGlobalMyFlashDraftFragment())
                R.id.action_reported_comments-> findNavController().navigateSafe(NavMyFlashDirections.actionGlobalReportedCommentsFragment())
                R.id.action_saved-> findNavController().navigateSafe(NavMyFlashDirections.actionGlobalNavFlashSaved())
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun showLongPressMenu(flash: FlashVideoWithMedia, view: View) {
        if (flash.media!=null) return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_flash_mine_select, popup.menu)
        popup.setForceShowIcon(true)

        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_archive-> viewModel.archiveFlash(flash)
                R.id.action_delete -> showRecordingDeleteAlert {
                    viewModel.deleteFlash(flash.flash)
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun showRecordingDeleteAlert(onConfirm: () -> Unit) {
        MaterialAlertDialogBuilder(requireContext()).setTitle(R.string.flash_my_flash_delete_confirm_title)
            .setMessage(getText(R.string.flash_my_flash_delete_confirm_message))
            .setPositiveButton(getText(R.string.common_delete)) { dialog, _ ->
                dialog.dismiss()
                onConfirm.invoke()
            }.setNegativeButton(getText(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }

    companion object {

        fun UserCitizenship.getFlashPostLimitingAlertMessage(context: Context, enabledFlashCount: Int?): String? {
            return when (this) {
                UserCitizenship.VISITOR, UserCitizenship.RESIDENT -> context.getString(R.string.flash_upload_limit_free_user_alert_content, "${enabledFlashCount ?: 0}")
                UserCitizenship.GOLDEN-> context.getString(R.string.flash_upload_limit_golden_user_alert_content, "${enabledFlashCount ?: 0}")
                UserCitizenship.CITIZEN, UserCitizenship.OFFICER -> context.getString(R.string.flash_upload_limit_premium_user_alert_content, "${enabledFlashCount ?: 0}")
                else -> null //Will not reach here
            }
        }

        fun Fragment.showFlashPostLimitingAlert(
            citizenship: UserCitizenship,
            enabledFlashCount: Int?,
            onConfirmButtonClick: () -> Unit,
        ) {
            showFlashatDialog {
                setMessage(citizenship.getFlashPostLimitingAlertMessage(context = requireContext(), enabledFlashCount = enabledFlashCount).orEmpty())
                setConfirmButton(R.string.common_upgrade, R.drawable.ic_promo_upgrade) {
                    onConfirmButtonClick()
                    true
                }
                setConfirmButtonVisible(visible = !citizenship.isCitizen && !citizenship.isOfficer && !citizenship.isGolden)
            }
        }
    }
}

