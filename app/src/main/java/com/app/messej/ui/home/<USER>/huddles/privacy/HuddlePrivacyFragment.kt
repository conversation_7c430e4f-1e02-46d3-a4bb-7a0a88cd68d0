package com.app.messej.ui.home.publictab.huddles.privacy

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.HuddlePrivacyStatus
import com.app.messej.databinding.FragmentHuddlePrivacyBinding

class HuddlePrivacyFragment : Fragment() {
    private lateinit var binding: FragmentHuddlePrivacyBinding
    private val viewModel: HuddlePrivacyViewModel by viewModels()
    private val args: HuddlePrivacyFragmentArgs by navArgs()

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_privacy, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.onPrivacySettingUpdated.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
    }

    private fun setup() {

        viewModel.getHuddlePrivacySettings(args.huddleId,args.isTribe)
        // Init the who can post block
        binding.whoCanPost.huddleSettingsTitle = getString(R.string.title_who_can_post)
        binding.whoCanPost.huddleSettingsSubTitle = getString(R.string.subtitle_who_can_post)

        binding.whoCanPost.radioHuddlePrivacyOptions.setOnCheckedChangeListener { _, id ->
            viewModel.setPostPrivacyStratus(
                when (id) {
                    R.id.radioHuddlePrivacyOptionOne -> HuddlePrivacyStatus.NO_ONE
                    R.id.radioHuddlePrivacyOptionTwo -> HuddlePrivacyStatus.DEARS
                    R.id.radioHuddlePrivacyOptionThree -> HuddlePrivacyStatus.CITIZENS
                    R.id.radioHuddlePrivacyOptionFour -> HuddlePrivacyStatus.ANYONE
                    else -> HuddlePrivacyStatus.NO_ONE
                }
            )
        }

        // Init the who can reply block
        binding.whoCanReply.huddleSettingsTitle = getString(R.string.title_who_can_reply)
        binding.whoCanReply.huddleSettingsSubTitle = getString(R.string.subtitle_who_can_reply)


        binding.whoCanReply.radioHuddlePrivacyOptions.setOnCheckedChangeListener { _, id ->
            viewModel.setReplyPrivacyStatus(
                when (id) {
                    R.id.radioHuddlePrivacyOptionOne -> HuddlePrivacyStatus.NO_ONE
                    R.id.radioHuddlePrivacyOptionTwo -> HuddlePrivacyStatus.DEARS
                    R.id.radioHuddlePrivacyOptionThree -> HuddlePrivacyStatus.CITIZENS
                    R.id.radioHuddlePrivacyOptionFour -> HuddlePrivacyStatus.ANYONE
                    else -> HuddlePrivacyStatus.NO_ONE
                }
            )
        }

        // Init the who can comment block
        binding.whoCanComment.huddleSettingsTitle = getString(R.string.title_who_can_comment)
        binding.whoCanComment.huddleSettingsSubTitle = getString(R.string.subtitle_who_can_comment)


        binding.whoCanComment.radioHuddlePrivacyOptions.setOnCheckedChangeListener { _, id ->
            viewModel.setCommentPrivacyStratus(
                when (id) {
                    R.id.radioHuddlePrivacyOptionOne -> HuddlePrivacyStatus.NO_ONE
                    R.id.radioHuddlePrivacyOptionTwo -> HuddlePrivacyStatus.DEARS
                    R.id.radioHuddlePrivacyOptionThree -> HuddlePrivacyStatus.CITIZENS
                    R.id.radioHuddlePrivacyOptionFour -> HuddlePrivacyStatus.ANYONE
                    else -> HuddlePrivacyStatus.NO_ONE
                }
            )
        }

        binding.btnHuddlePrivacyUpdate.setOnClickListener {
            viewModel.updateHuddlePrivacy(args.huddleId)
        }
    }
}