package com.app.messej.ui.premium

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.subscription.FlixSubscriptionDetails
import com.app.messej.data.model.api.subscription.PremiumResponse
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.util.TimeZone

class UpgradePremiumViewModel (application: Application): AndroidViewModel(application)  {
    private var profileRepo: ProfileRepository = ProfileRepository(application)
    private var accountRepo: AccountRepository = AccountRepository(application)
    val subscription = LiveEvent<Boolean?>()
    private val _subscriptionError = MutableLiveData<String?>(null)
    val subscriptionError: LiveData<String?> = _subscriptionError
    val productDetails= MutableLiveData<ProductDetails>()
    val alreadySubscribed=MutableLiveData<Boolean?>(false)
    val user: CurrentUser get() = AccountRepository(getApplication()).user

    private val _disableFlixButton=MutableLiveData<Boolean>(true)
    val disableFlixButton: LiveData<Boolean> = _disableFlixButton

    private val _pricingPhase = MutableLiveData<ProductDetails.PricingPhase?>(null)
    val pricingPhase: LiveData<ProductDetails.PricingPhase?> = _pricingPhase
    fun setPurchase(purchase: Purchase) {

        viewModelScope.launch(Dispatchers.IO) {
            //logic to update subscription
            Log.d("UpgradePremiumViewModel", "setPurchase: ${purchase.orderId} ${purchase.purchaseToken} ${purchase.products}")
            when (val result: ResultOf<PremiumResponse> =
                profileRepo.updateSubscription(purchase,_pricingPhase.value)) {
                is ResultOf.Success -> {
                    subscription.postValue(result.value.success)
                    //log subscription
                    Log.d("UpgradePremiumViewModel", "Subscription updated successfully: ${result.value.success}")
                }
                is ResultOf.APIError -> {
                    _subscriptionError.postValue(result.error.message)
                    Log.e("UpgradePremiumViewModel", "API Error: ${result.error.message}")
                }
                is ResultOf.Error -> {
                }
            }
        }
    }

    fun setPricingPhase(pricing:ProductDetails.PricingPhase){
        _pricingPhase.postValue(pricing)
    }

    fun setAlreadySubscribed(isSubscribed: Boolean) {
        alreadySubscribed.postValue(isSubscribed)

    }

    private val _flixSubscriptionDetails = MutableLiveData<FlixSubscriptionDetails>(null)
    val flixSubscriptionDetails: LiveData<FlixSubscriptionDetails> = _flixSubscriptionDetails

     fun flixSubscriptionDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<FlixSubscriptionDetails> = profileRepo.getFlixSubscriptionDetails()) {
                is ResultOf.Success -> {
                    _flixSubscriptionDetails.postValue(result.value)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }
    private val _handleVisibility = MutableLiveData<Boolean>(false)
    val handleVisibility: LiveData<Boolean> = _handleVisibility

    fun handleVisibility(visibility: Boolean) {
        _handleVisibility.postValue(visibility)
    }

    val hasRequiredFlix: Boolean
        get() {
            val activePoints =user.activePoints?: return false
            val amount = _flixSubscriptionDetails.value?.amount?: return false
            return activePoints >= amount
        }

    val startDate =MutableLiveData<String?>(null)
    val expiryDate =MutableLiveData<String?>(null)

    fun selectDates(){
            startDate.postValue(DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.FORMAT_DDMMYYYY_SLASHED))
            expiryDate.postValue(DateTimeUtils.format(LocalDateTime.now().plusMonths(12).minusDays(1), DateTimeUtils.FORMAT_DDMMYYYY_SLASHED))
    }


    val isSubscribedByFlix =LiveEvent<Boolean?>()

    fun subscribeByFlix(){
        viewModelScope.launch(Dispatchers.IO) {
            _disableFlixButton.postValue(false)
            when (val result: ResultOf<FlixSubscriptionDetails> = profileRepo.subscribeByFlix(FlixSubscriptionDetails(userid = user.id, type = "android", amount = _flixSubscriptionDetails.value!!.amount, currency = _flixSubscriptionDetails.value!!.currency , timezone = TimeZone.getDefault().id))) {
                is ResultOf.Success -> {
                    isSubscribedByFlix.postValue(result.value.success)
                    if(result.value.success == true){
                        val user = accountRepo.user
                        user.premium = true
                        accountRepo.updateUser(user)
                        profileRepo.getAccountDetails()
                        _disableFlixButton.postValue(true)
                    }
                }
                is ResultOf.APIError -> { _disableFlixButton.postValue(true)}
                is ResultOf.Error -> { _disableFlixButton.postValue(true)}
            }
        }
    }

}

