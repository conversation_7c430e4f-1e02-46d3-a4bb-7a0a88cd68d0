package com.app.messej.ui.home.publictab.huddles.privacy

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.entity.HuddlePrivacySettings
import com.app.messej.data.model.entity.HuddlePrivacyStatus
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.net.HttpURLConnection

class HuddlePrivacyViewModel(application: Application) : AndroidViewModel(application) {

    val huddleRepo=HuddlesRepository(application)
    private val _huddleId = MutableLiveData<Int?>(null)
    val huddleId: LiveData<Int?> = _huddleId

    private val _didLoading = MutableLiveData<Boolean?>(null)
    val didLoading: LiveData<Boolean?> = _didLoading

    private val _requestError = MutableLiveData<String?>(null)
    val requestError: LiveData<String?> = _requestError

    private val _commentPrivacyStatus = MutableLiveData<HuddlePrivacyStatus?>(null)
    val commentPrivacyStatus: LiveData<HuddlePrivacyStatus?> = _commentPrivacyStatus

    private val _isTribe= MutableLiveData<Boolean?>(false)
    val isTribe: LiveData<Boolean?> = _isTribe

    private val _replyPrivacyStatus = MutableLiveData<HuddlePrivacyStatus?>(null)
    val replyPrivacyStatus: LiveData<HuddlePrivacyStatus?> = _replyPrivacyStatus

    private val _postPrivacyStatus = MutableLiveData<HuddlePrivacyStatus?>(null)
    val postPrivacyStatus: LiveData<HuddlePrivacyStatus?> = _postPrivacyStatus


    val onPrivacySettingUpdated=LiveEvent<Boolean>()

    fun setPostPrivacyStratus(status: HuddlePrivacyStatus) {
        _postPrivacyStatus.postValue(status)
    }

    fun setCommentPrivacyStratus(status: HuddlePrivacyStatus) {
        _commentPrivacyStatus.postValue(status)
    }

    fun setReplyPrivacyStatus(status: HuddlePrivacyStatus) {
        _replyPrivacyStatus.postValue(status)
    }
    fun setTribe(tribe: Boolean) {
        _isTribe.postValue(tribe)
    }
    fun getHuddlePrivacySettings(huddleId:Int,isTribe:Boolean) {
        _isTribe.postValue(isTribe)
        viewModelScope.launch(Dispatchers.IO) {
            _didLoading.postValue(true)
            when (val result: ResultOf<HuddlePrivacySettings> = huddleRepo.getHuddlePrivacy(huddleId = huddleId)) {
                is ResultOf.Success -> {
                    _didLoading.postValue(false)
                    _commentPrivacyStatus.postValue(result.value.commentSettings?.privacy)
                    _postPrivacyStatus.postValue(result.value.postSettings?.privacy)
                    _replyPrivacyStatus.postValue(result.value.replySettings?.privacy)
                }
                is ResultOf.APIError -> {
                    if (result.code== HttpURLConnection.HTTP_BAD_REQUEST)
                        _requestError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _didLoading.postValue(false)
        }
    }

    fun updateHuddlePrivacy(huddleId:Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<HuddlePrivacySettings> = huddleRepo.updateHuddlePrivacy(huddleId,_replyPrivacyStatus.value!!,_commentPrivacyStatus.value!!,_postPrivacyStatus.value)) {
                is ResultOf.Success -> {
                    onPrivacySettingUpdated.postValue(true)
                }
                is ResultOf.APIError -> {
                    if (result.code== HttpURLConnection.HTTP_BAD_REQUEST)
                        _requestError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
        }
    }



}
