package com.app.messej.ui.home.businesstab.customers

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.databinding.ItemBusinessCustomerListBinding

class BusinessCustomerListAdapter(private val listener: ActionListener) :
    PagingDataAdapter<UserRelative, BusinessCustomerListAdapter.BusinessCustomersViewHolder>(DearsDiff) {

    interface ActionListener {
        fun onProfileClick(item: UserRelative)
        fun onPrivateMessageClick(item: UserRelative)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        BusinessCustomersViewHolder(
            ItemBusinessCustomerListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: BusinessCustomersViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class BusinessCustomersViewHolder(private val binding: ItemBusinessCustomerListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: UserRelative) = with(binding) {
            model = item
            binding.source = getPerfoImage(item, binding.root.context)
            binding.privateMessageImage.setOnClickListener { listener.onPrivateMessageClick(item) }
            binding.layoutProfileInfo.setOnClickListener { listener.onProfileClick(item) }
        }
    }

    object DearsDiff : DiffUtil.ItemCallback<UserRelative>() {
        override fun areItemsTheSame(oldItem: UserRelative, newItem: UserRelative) =
            oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: UserRelative, newItem: UserRelative) =
            oldItem == newItem
    }

    fun getPerfoImage(item: UserRelative, context: Context): Drawable? {
        return if ((item.fans ?: 0) > 0 && item.dears == 0) {
            ResourcesCompat.getDrawable(context.resources, R.drawable.ic_business_orange_perfo, null)
        } else if (((item.fans ?: 0) > 0) && (item.dears ?: 0) > 0) {
            ResourcesCompat.getDrawable(context.resources, R.drawable.ic_business_green_perfo, null)
        } else if (((item.fans ?: 0) == 0) && (item.dears ?: 0) > 0) {
            ResourcesCompat.getDrawable(context.resources, R.drawable.ic_business_green_perfo, null)
        }else {
            ResourcesCompat.getDrawable(context.resources, R.drawable.ic_business_red_perfo, null)
        }
    }
}