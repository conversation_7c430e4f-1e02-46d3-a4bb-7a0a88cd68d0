package com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight.Companion.W700
import androidx.compose.ui.tooling.preview.Preview
import com.app.messej.R

@Composable
fun PayFineView(
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(
                topStart = 50F, bottomStart = 50F
            ))
            .clickable { onClick() }
            .background(color = Color(0xFFFF0000))
            .padding(all = dimensionResource(id = R.dimen.line_spacing)),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(modifier = Modifier
            .clip(shape = CircleShape)
            .size(size = dimensionResource(id = R.dimen.extra_margin))
            .background(brush = Brush.linearGradient(colors = listOf(Color(0xFFD90010), Color(0xFFF0313F))))
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cash_outline),
                tint = Color.White,
                contentDescription = null
            )
        }
        Text(
            text = stringResource(id = R.string.common_pay_fine),
            modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.element_spacing)),
            style = TextStyle(
                color = Color.White,
                fontWeight = W700
            )
        )
    }
}

@Preview
@Composable
fun PayFinePreview() {
    PayFineView(onClick = {})
}