package com.app.messej.ui.home.businesstab

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AlreadyFlaxAppliedViewModel (application: Application) : AndroidViewModel(application) {
    private var businessRepo: BusinessRepository = BusinessRepository(application)

    private val _cancelPayoutStatus = MutableLiveData<Boolean>(false)
    val cancelPayoutStatus: LiveData<Boolean> = _cancelPayoutStatus

    //livedata for loader
    private val _loader = MutableLiveData(false)
    val loader: LiveData<Boolean> = _loader



    fun cancelPaymentRequest(paymentRequestId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            _loader.postValue(true)
            when (val result: ResultOf<String> = businessRepo.rejectPayoutRequest(paymentRequestId)) {
                is ResultOf.Success -> {
                    _cancelPayoutStatus.postValue(true)
                    _loader.postValue(false)
                }

                is ResultOf.APIError -> {
                    _loader.postValue(false)
                }

                is ResultOf.Error -> {
                    _loader.postValue(false)
                }
            }
        }


    }



}