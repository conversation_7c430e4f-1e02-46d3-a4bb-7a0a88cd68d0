package com.app.messej.ui.home.publictab.broadcast

import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.BroadcastTab
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.databinding.FragmentPublicBroadcastHeaderBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences

class PublicBroadcastStandaloneFragment : PublicBroadcastBaseFragment() {
    private lateinit var outerBinding: FragmentPublicBroadcastHeaderBinding

    private val args: PublicBroadcastStandaloneFragmentArgs by navArgs()

    override val tabList: List<BroadcastTab> = BroadcastTab.entries.except(BroadcastTab.TAB_STARS).toList()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_broadcast_header, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        binding = outerBinding.layout
        binding.viewModel = viewModel
        return outerBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.btnFollowing.isVisible = false
        val tab = BroadcastTab.entries.toTypedArray().getOrElse(args.tab) { BroadcastTab.TAB_DEARS }
        viewModel.setCurrentTab(tab,false)
        observe()
        viewModel.getProfileDetails()
    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(outerBinding.appbar.toolbar)
        bindFlaxRateToolbarChip(outerBinding.appbar.flaxRateChip)
        outerBinding.appbar.toolBarTitle.apply {

            val getFormattedFollowersCount = getString(R.string.home_private_tab_followers_count, viewModel.user.totalFollowersCount.toString()).highlightOccurrences(viewModel.user.totalFollowersCount.toString()) {
                ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorSecondary))
            }
            text = getFormattedFollowersCount
        }
    }

    private fun observe() {
        viewModel.enableSearch.observe(viewLifecycleOwner) {
            activity?.invalidateOptionsMenu()
        }
    }
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_home_search,menu)
    }

    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        menu.findItem(R.id.action_search).isVisible = viewModel.enableSearch.value==true
    }

    override fun toSearch() {
        val tab = viewModel.currentTab.value?: return
        findNavController().navigateSafe(PublicBroadcastStandaloneFragmentDirections.actionPublicBroadcastStandaloneFragmentToHomePublicBroadcastSearchFragment(tab))
    }

    override fun toBroadcast(broadcastMode: BroadcastMode, char: Char?) {
        findNavController().navigateSafe(PublicBroadcastStandaloneFragmentDirections.actionGlobalNavigationChatBroadcast(broadcastMode,null))
    }
}