package com.app.messej.ui.home.publictab.podiums.challenges.penalty

import android.content.Context
import android.graphics.BitmapFactory
import android.util.Log
import androidx.annotation.RawRes
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PenaltyKickTarget
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore.PlayerColor
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumPenaltyChallengePresenter.PenaltyState
import com.google.android.filament.Engine
import com.google.android.filament.Texture
import io.github.sceneview.Scene
import io.github.sceneview.material.setBaseColorMap
import io.github.sceneview.math.Position
import io.github.sceneview.math.Rotation
import io.github.sceneview.math.Scale
import io.github.sceneview.math.Transform
import io.github.sceneview.node.ModelNode
import io.github.sceneview.rememberCameraNode
import io.github.sceneview.rememberEngine
import io.github.sceneview.rememberModelLoader
import io.github.sceneview.rememberNode
import io.github.sceneview.rememberNodes
import java.nio.ByteBuffer

object PenaltyViewer {

    private val playerTextureClothTargets = listOf("pant", "shirt", "socks")
    private val playerTextureShoeTargets = listOf("shoe-l","shoe-r")
    private val playerTextureGloveTargets = listOf("gloves")

    private val PlayerColor.kickerJersey: Int
        @RawRes
        get() = when(this) {
            PlayerColor.PURPLE -> R.raw.penalty_jersey_kicker_primary
            PlayerColor.YELLOW -> R.raw.penalty_jersey_kicker_secondary
        }

    private val PlayerColor.goalieJersey: Int
        @RawRes
        get() = when(this) {
            PlayerColor.PURPLE -> R.raw.penalty_jersey_goalie_primary
            PlayerColor.YELLOW -> R.raw.penalty_jersey_goalie_secondary
        }

    private val PlayerColor.gloves: Int
        @RawRes
        get() = when(this) {
            PlayerColor.PURPLE -> R.raw.penalty_glove_primary
            PlayerColor.YELLOW -> R.raw.penalty_glove_secondary
        }

    private val PlayerColor.shoes: Int
        @RawRes
        get() = when(this) {
            PlayerColor.PURPLE -> R.raw.penalty_shoes_primary
            PlayerColor.YELLOW -> R.raw.penalty_shoes_secondary
        }

    interface ObjectAnimation

    private enum class AnimationSource {
        KICKER, GOALIE, BALL
    }

    private enum class KickerAnimation(val key: String): ObjectAnimation {
        IDLE("anim-idle"),
        KICK("anim-kick"),
        POST_IDLE("anim-post-idle");

        companion object {
            fun fromKey(key: String) = entries.find { it.key == key }
        }
    }

    private enum class GoalieAnimation(val key: String): ObjectAnimation {
        IDLE("anim_idle"),
        DIVE_BOTTOM_LEFT("anim_BL_deflect"),
        DIVE_BOTTOM_RIGHT("anim_BR_deflect"),
        DIVE_TOP_LEFT("anim_TL_deflect"),
        DIVE_TOP_RIGHT("anim_TR_deflect"),
        DIVE_CENTER("anim_C_deflect"),
        MISS_CENTER_LEFT("anim_CL_miss"),
        MISS_CENTER_RIGHT("anim_CR_miss");

        companion object {
            fun fromKey(key: String) = entries.find { it.key == key }
        }
    }

    private enum class BallAnimation(val key: String): ObjectAnimation {
        DEFLECT_BOTTOM_LEFT("ball-bottom-left-deflect"),
        GOAL_BOTTOM_LEFT("ball-bottom-left-success"),
        DEFLECT_BOTTOM_RIGHT("ball-bottom-right-deflect"),
        GOAL_BOTTOM_RIGHT("ball-bottom-right-success"),
        DEFLECT_TOP_LEFT("ball-top-left-deflect"),
        GOAL_TOP_LEFT("ball-top-left-success"),
        DEFLECT_TOP_RIGHT("ball-top-right-deflect"),
        GOAL_TOP_RIGHT("ball-top-right-success"),
        DEFLECT_CENTER("ball-center-deflect"),
        GOAL_CENTER("ball-center-success");

        companion object {
            fun fromKey(key: String) = entries.find { it.key == key }
        }
    }

    private fun getBallAnimation(kick: PenaltyKickTarget, goalie: PenaltyKickTarget?): BallAnimation {
        val didCatch = kick == goalie
        return when(kick) {
            PenaltyKickTarget.TOP_LEFT -> if (didCatch) BallAnimation.DEFLECT_TOP_LEFT else BallAnimation.GOAL_TOP_LEFT
            PenaltyKickTarget.TOP_RIGHT -> if (didCatch) BallAnimation.DEFLECT_TOP_RIGHT else BallAnimation.GOAL_TOP_RIGHT
            PenaltyKickTarget.BOTTOM_LEFT -> if (didCatch) BallAnimation.DEFLECT_BOTTOM_LEFT else BallAnimation.GOAL_BOTTOM_LEFT
            PenaltyKickTarget.BOTTOM_RIGHT -> if (didCatch) BallAnimation.DEFLECT_BOTTOM_RIGHT else BallAnimation.GOAL_BOTTOM_RIGHT
            PenaltyKickTarget.CENTER -> if (didCatch) BallAnimation.DEFLECT_CENTER else BallAnimation.GOAL_CENTER
        }
    }

    private fun getGoalieAnimation(goalie: PenaltyKickTarget, kick: PenaltyKickTarget?): GoalieAnimation {
        return when(goalie) {
            PenaltyKickTarget.TOP_LEFT -> GoalieAnimation.DIVE_TOP_LEFT
            PenaltyKickTarget.TOP_RIGHT -> GoalieAnimation.DIVE_TOP_RIGHT
            PenaltyKickTarget.BOTTOM_LEFT -> GoalieAnimation.DIVE_BOTTOM_LEFT
            PenaltyKickTarget.BOTTOM_RIGHT -> GoalieAnimation.DIVE_BOTTOM_RIGHT
            PenaltyKickTarget.CENTER -> return when(kick) {
                PenaltyKickTarget.TOP_RIGHT, PenaltyKickTarget.BOTTOM_RIGHT -> GoalieAnimation.MISS_CENTER_RIGHT
                PenaltyKickTarget.TOP_LEFT, PenaltyKickTarget.BOTTOM_LEFT -> GoalieAnimation.MISS_CENTER_LEFT
                else -> GoalieAnimation.DIVE_CENTER
            }
        }
    }

    @Composable
    fun PenaltyScene(
        kickerColor: PlayerColor,
        goalieColor: PlayerColor,
        state: PenaltyState,
        onStateAnimationEnd: (PenaltyState) -> Unit
    ) {
        val engine = rememberEngine()
        val modelLoader = rememberModelLoader(engine)
//        val materialLoader = rememberMaterialLoader(engine)
//        val environmentLoader = rememberEnvironmentLoader(engine)

        val cameraNode = rememberCameraNode(engine).apply {
            position = Position(y = 8.0f, z = 8.0f)
            transform = Transform(position = Position(0.0f, 0.0f, 4.0f))
            far = 100f
        }
        val centerNode = rememberNode(engine).addChildNode(cameraNode)

        val cachedState = rememberUpdatedState(newValue = state)
        val animationStates = rememberUpdatedState(mutableMapOf(
                AnimationSource.GOALIE to false,
        AnimationSource.KICKER to false,
        AnimationSource.BALL to false
        ).apply {
            if (state is PenaltyState.Penalty) {
                if (state.kickerDirection == null) {
                    this[AnimationSource.KICKER] = true
                    this[AnimationSource.BALL] = true
                }
                if (state.goalieDirection == null) {
                    this[AnimationSource.GOALIE] = true
                }
            }
        })
        val animationEndCallback = rememberUpdatedState(newValue = onStateAnimationEnd)
        fun checkAnimationState(justEnded: AnimationSource? = null) {
            Log.d("PCPPT", "checkAnimationState: $animationStates")
            justEnded?.let {
                val alreadyNotified = animationStates.value.all { it.value }
                if (alreadyNotified) return
                animationStates.value[it] = true
            }
            if (animationStates.value.all { it.value }) {
                Log.w("PCPPT", "checkAnimationState: Animation ended for ${cachedState.value}")
                animationEndCallback.value.invoke(cachedState.value)
            }
        }
        checkAnimationState()

        val kickerNode: ModelNode = remember {
            AModelNode(
                modelInstance = modelLoader.createModelInstance(
                    rawResId = R.raw.penalty_kicker
                )
            ) { name ->
                Log.w("PCPPT", "Kicker animation complete: [$name]")
                KickerAnimation.fromKey(name)?.let { anim ->
                    if (anim == KickerAnimation.KICK) {
                        checkAnimationState(AnimationSource.KICKER)
                        playAnimation(KickerAnimation.POST_IDLE.key, loop = true)
                    }
                }
            }.apply {
                Log.w("PCPPT", "Kicker Nodes: [${renderableNodes.map { it.name }.joinToString(",")}]")
                rotation = Rotation(x = 0f, y = 180f, z = 0f)
            }
        }

        val goalieNode: ModelNode = remember {
            AModelNode(
                modelInstance = modelLoader.createModelInstance(
                    rawResId = R.raw.penalty_goalie
                )
            ) { name ->
                Log.w("PCPPT", "Goalie animation complete: [$name]")
                GoalieAnimation.fromKey(name)?.let { anim ->
                    if (anim != GoalieAnimation.IDLE) {
                        checkAnimationState(AnimationSource.GOALIE)
                    }
                }
            }.apply {
                Log.w("PCPPT", "Goalie Nodes: [${renderableNodes.map { it.name }.joinToString(",")}]")
                position = Position(x = 0f, y = 0f, z = -11f)
            }
        }

        val ballNode: ModelNode = remember {
            AModelNode(
                modelInstance = modelLoader.createModelInstance(
                    rawResId = R.raw.penalty_ball
                )
            ) { name ->
                Log.w("PCPPT", "Ball animation complete: [$name]")
                BallAnimation.fromKey(name)?.let { anim ->
                    checkAnimationState(AnimationSource.BALL)
                }
            }
        }

        val context = LocalContext.current

        LaunchedEffect(kickerColor, goalieColor) {
            playerTextureClothTargets.forEach { target ->
                kickerNode.renderableNodes.find { it.name == target }?.apply {
                    materialInstance.setBaseColorMap(createTextureFromBitmap(context, engine, kickerColor.kickerJersey))
                }
                goalieNode.renderableNodes.find { it.name == target }?.apply {
                    materialInstance.setBaseColorMap(createTextureFromBitmap(context, engine, goalieColor.goalieJersey))
                }
            }

            playerTextureShoeTargets.forEach { target ->
                kickerNode.renderableNodes.find { it.name == target }?.apply {
                    materialInstance.setBaseColorMap(createTextureFromBitmap(context, engine, kickerColor.shoes))
                }
                goalieNode.renderableNodes.find { it.name == target }?.apply {
                    materialInstance.setBaseColorMap(createTextureFromBitmap(context, engine, goalieColor.shoes))
                }
            }

            playerTextureGloveTargets.forEach { target ->
                goalieNode.renderableNodes.find { it.name == target }?.apply {
                    materialInstance.setBaseColorMap(createTextureFromBitmap(context, engine, goalieColor.gloves))
                }
            }
        }

        val worldNode: ModelNode = remember {
            AModelNode(
                modelInstance = modelLoader.createModelInstance(
                    rawResId = R.raw.penalty_environment
                )
            ).apply {
                position = Position(x = 0f, y = 0f, z = 0f)
                rotation = Rotation(x = 0f, y = 0f, z = 0f)
            }
        }

        val flagNode: ModelNode = remember {
            AModelNode(
                modelInstance = modelLoader.createModelInstance(
                    rawResId = R.raw.penalty_flag
                )
            ).apply {
                position = Position(x = 0f, y = 0f, z = -20f)
                scale = Scale(0.75f)
            }
        }

        fun clearAnimations() {
            kickerNode.playingAnimations.map { it.key }.forEach {
                kickerNode.stopAnimation(it)
            }
            ballNode.playingAnimations.map { it.key }.forEach {
                ballNode.stopAnimation(it)
            }
            ballNode.animator.resetBoneMatrices()
            goalieNode.playingAnimations.map { it.key }.forEach {
                goalieNode.stopAnimation(it)
            }
        }

        fun animatePenalty(kickerDirection: PenaltyKickTarget?, goalieDirection: PenaltyKickTarget?) {
            clearAnimations()
            kickerDirection?.let { kd ->
                kickerNode.playAnimation(KickerAnimation.KICK.key, loop = false)
                val ballAnim = getBallAnimation(kd, goalieDirection)
                ballNode.playAnimation(ballAnim.key, loop = false)
            }
            goalieDirection?.let { gd ->
                val goalieAnim = getGoalieAnimation(gd, kickerDirection)
                goalieNode.playAnimation(goalieAnim.key, loop = false)
            }
        }

        fun animateIdle() {
            clearAnimations()
            kickerNode.playAnimation(KickerAnimation.IDLE.key, loop = true)
            goalieNode.playAnimation(GoalieAnimation.IDLE.key, loop = true)
        }

        Scene(modifier = Modifier.fillMaxSize(), engine = engine, modelLoader = modelLoader, cameraNode = cameraNode, childNodes = rememberNodes {
            add(centerNode)
            add(kickerNode)
            add(ballNode)
            add(goalieNode)
            add(worldNode)
            add(flagNode)
        },
//                environment = environmentLoader.createHDREnvironment(
//                    assetFileLocation = "environments/sky_2k.hdr"
//                )!!,
              onFrame = {
//                    centerNode.rotation = cameraRotation
//                    cameraNode.position = Position(x = 0f,y = 3f, z = -11.0f)
//                    cameraNode.rotation =Rotation(x = 0f, y = 180f, z = 0f)

                  // X: Moves Right, Y: Moves Up, Z: Moves Backward
                  cameraNode.position = Position(x = 0f, y = 3f, z = 8.0f)
                  // X: Rotates Up, Y: Rotates Left, Z: Rotates Counter Clockwise
                  cameraNode.rotation = Rotation(x = -10f, y = 0f, z = 0f)
//                    Log.w("3DT", "SceneHolder: ${cameraNode.rotation}")
              })

        LaunchedEffect(state) {
            when (state) {
                is PenaltyState.Idle -> animateIdle()
                is PenaltyState.Penalty -> animatePenalty(state.kickerDirection, state.goalieDirection)
            }
        }
    }

    private fun createTextureFromBitmap(context: Context, engine: Engine, @RawRes resource: Int): Texture {

        val inputStream = context.resources.openRawResource(resource)
        val bitmap = BitmapFactory.decodeStream(inputStream)

        val texture = Texture.Builder()
            .width(bitmap.width)
            .height(bitmap.height)
            .levels(1)
            .sampler(Texture.Sampler.SAMPLER_2D)
            .format(Texture.InternalFormat.SRGB8_A8)
            .build(engine)

        val buffer = ByteBuffer.allocateDirect(bitmap.byteCount)
        bitmap.copyPixelsToBuffer(buffer)
        buffer.flip()
        texture.setImage(
            engine, 0, Texture.PixelBufferDescriptor(
                buffer,
                Texture.Format.RGBA,
                Texture.Type.UBYTE
            )
        )
        buffer.clear()
        return texture
    }
}