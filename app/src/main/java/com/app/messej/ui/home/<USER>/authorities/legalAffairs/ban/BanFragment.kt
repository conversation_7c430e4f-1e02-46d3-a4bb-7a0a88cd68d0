package com.app.messej.ui.home.publictab.authorities.legalAffairs.ban

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.databinding.FragmentViolationsListBinding
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils
import com.app.messej.ui.home.publictab.authorities.legalAffairs.CaseDetailsBottomSheet.Companion.CASE_DETAIL_REQUEST_KEY
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsCommonViewModel
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsFragmentDirections
import com.app.messej.ui.home.publictab.authorities.legalAffairs.violations.LegalAffairsCaseListAdapter
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils

class BanFragment : Fragment() {

    private lateinit var binding: FragmentViolationsListBinding
    private lateinit var mAdapter: LegalAffairsCaseListAdapter
    private val legalAffairsMainViewModel: LegalAffairsCommonViewModel by activityViewModels()
    private val viewModel: BanViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_violations_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        initAdapter()
        observe()
    }

    private fun initAdapter() {

        mAdapter = LegalAffairsCaseListAdapter(object : LegalAffairsCaseListAdapter.ItemClickListener {
            override fun getUserId() = legalAffairsMainViewModel.user.id
            override fun showStatus() = legalAffairsMainViewModel.legalAffairsMainTab.value == LegalAffairsMainTab.MyLegalRecords
            override fun onClick(case: LegalRecordsResponse.ReportCase) {
                findNavController().navigateSafe(LegalAffairsFragmentDirections.actionLegalAffairsFragmentToCaseDetailBottomSheet(case.id))
            }

            override fun displayMode(): LegalAffairsCaseListAdapter.DisplayMode {
                return if (legalAffairsMainViewModel.legalAffairsMainTab.value == LegalAffairsMainTab.InvestigationBureau) LegalAffairsCaseListAdapter.DisplayMode.CASE
                else LegalAffairsCaseListAdapter.DisplayMode.USER
            }
        })

        val linearLayoutManager = LinearLayoutManager(context)
        binding.recyclerView.apply {
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }

        mAdapter.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
            }
        }

        AuthoritiesUtils.setupListEmptyView(multiStateView = binding.multiStateView)
        AuthoritiesUtils.setupListErrorView(
            multiStateView = binding.multiStateView,
            onRetryButtonClick = { mAdapter.retry() }
        )

    }

    private fun observe() {

        legalAffairsMainViewModel.legalAffairsMainTab.observe(viewLifecycleOwner) {
            setUpDataInListView(tab = it)
        }

        setFragmentResultListenerOnActivity(CASE_DETAIL_REQUEST_KEY) { _, _ ->
            mAdapter.refresh()
        }

        viewModel.investigationBureauCount.observe(viewLifecycleOwner) { count ->
            count?.let {
                legalAffairsMainViewModel.setInvestigationBureauCount(it)
            }
        }

    }

    private fun setUpDataInListView(tab: LegalAffairsMainTab) {
        when(tab) {
            LegalAffairsMainTab.MyLegalRecords -> {
                viewModel.homeBanList.observe(viewLifecycleOwner) {
                    mAdapter.submitData(viewLifecycleOwner.lifecycle, it)
                }
            }
            LegalAffairsMainTab.InvestigationBureau -> {
                viewModel.investigationBureauBanList.observe(viewLifecycleOwner) {
                    mAdapter.submitData(viewLifecycleOwner.lifecycle, it)
                }
            }
            else -> return
        }
    }
}