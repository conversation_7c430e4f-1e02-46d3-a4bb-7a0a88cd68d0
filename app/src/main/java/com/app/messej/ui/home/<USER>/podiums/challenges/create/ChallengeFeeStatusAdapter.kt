package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.databinding.ItemChallengeFeeStatusBinding

class ChallengeFeeStatusAdapter : PagingDataAdapter<PodiumChallenge.ChallengeUser, ChallengeFeeStatusAdapter.PodiumListViewHolder>(PodiumUserDiff)  {

    inner class PodiumListViewHolder(private val binding: ItemChallengeFeeStatusBinding): RecyclerView.ViewHolder(binding.root){
        fun bind(item: PodiumChallenge.ChallengeUser) = with(binding) {
            user = item
        }
    }
    object PodiumUserDiff : DiffUtil.ItemCallback<PodiumChallenge.ChallengeUser>(){
        override fun areItemsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: PodiumListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumListViewHolder {
        val binding = ItemChallengeFeeStatusBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumListViewHolder(binding)
    }
}