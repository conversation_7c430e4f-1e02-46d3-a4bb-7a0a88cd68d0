package com.app.messej.ui.home.gift

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.profile.BirthdayUser
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentTodaysBirthdayBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.gift.adapter.TodaysBirthdaysAdapter
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class TodaysBirthdayFragment : Fragment() {

    private lateinit var binding: FragmentTodaysBirthdayBinding
    private val viewModel: CommonHomeViewModel by activityViewModels()

    var mAdapter: TodaysBirthdaysAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_todays_birthday, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.title_todays_birthday)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
    }


    fun observe() {
        viewModel.birthdaysList.observe(viewLifecycleOwner) {
            Log.d("bbbbb",""+it.toString())

            val layoutManager = LinearLayoutManager(requireContext())
            mAdapter= TodaysBirthdaysAdapter(it,object : TodaysBirthdaysAdapter.ActionItemListener{
                override fun onItemClick(item: BirthdayUser) {
                    viewModel.user?.let {
                        if (it.citizenship == UserCitizenship.VISITOR) {
                            findNavController().navigateSafe(TodaysBirthdayFragmentDirections.actionGlobalUpgradePremiumFragment())
                        } else {
                            item.userId?.let { userId ->
                                findNavController().navigateSafe(TodaysBirthdayFragmentDirections.actionGlobalGiftFragment(userId, giftContext = GiftContext.BIRTHDAY, birthday = true))
                            }
                        }
                    }
                }

                override fun onUserClick(item: BirthdayUser) {
                    findNavController().navigateSafe(TodaysBirthdayFragmentDirections.actionGlobalPublicUserProfileFragment(item.userId?:return))
                }
            })
            binding.rvBirthdayList.layoutManager = layoutManager
            binding.rvBirthdayList.adapter = mAdapter
        }
    }
}