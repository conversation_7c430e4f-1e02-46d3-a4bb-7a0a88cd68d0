package com.app.messej.ui.home.publictab.authorities.legalAffairs.investigationBureau

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import com.app.messej.R
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.databinding.FragmentBaseLegalAffairsBinding
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsBaseFragment
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsCommonViewModel

class InvestigationFragment : LegalAffairsBaseFragment() {

    override lateinit var commonBinding: FragmentBaseLegalAffairsBinding
    override val viewModel: LegalAffairsCommonViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        commonBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_base_legal_affairs, container, false)
        return commonBinding.root
    }

    override fun setupCount() {
        viewModel.investigationBureauCount.observe(viewLifecycleOwner) {
            commonBinding.buttonBan.count = "${it?.banCount ?: 0}"
            commonBinding.buttonReporting.count = "${it?.reportsCount ?: 0}"
            commonBinding.buttonHidden.count = "${it?.hiddenCount ?: 0}"
        }
    }

    override fun setupVerticalDottedLinesVisibility(currentTab: LegalAffairTabs) {
        commonBinding.apply {
            verticalDottedLineOne.visibility = View.GONE
            verticalDottedLineSecond.visibility = if (currentTab == LegalAffairTabs.Bans || currentTab == LegalAffairTabs.Reporting) View.INVISIBLE else View.VISIBLE
            verticalDottedLineThird.visibility = if (currentTab == LegalAffairTabs.Reporting || currentTab == LegalAffairTabs.Hidden) View.INVISIBLE else View.VISIBLE
        }
    }

}