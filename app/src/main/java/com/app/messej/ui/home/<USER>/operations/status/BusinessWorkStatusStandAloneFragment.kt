package com.app.messej.ui.home.businesstab.operations.status

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.business.PayoutData
import com.app.messej.databinding.FragmentBusinessStatusBinding
import com.app.messej.databinding.FragmentBusinessWorkStatusStandAloneBinding
import com.app.messej.ui.common.PolicyDocumentFragment
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BusinessWorkStatusStandAloneFragment:BusinessWorkStatusBaseFragment() {

    override lateinit var binding: FragmentBusinessStatusBinding
    private lateinit var outerBinding: FragmentBusinessWorkStatusStandAloneBinding
    private val withDrawViewModel: BusinessWithDrawViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_work_status_stand_alone, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        binding = outerBinding.baseLayout
        binding.viewModel = viewModel
        return outerBinding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(outerBinding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(outerBinding.customActionBar.flaxRateChip)
        outerBinding.customActionBar.toolBarTitle.text = getString(R.string.title_business_status_eligibility)


    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    fun observe() {
        withDrawViewModel.payoutEligibility.observe(viewLifecycleOwner) {
            Log.i("observe: ", "log to print entered BusinessDealsViewPagerFragment")
            if (it?.eligibility == true && it.payoutData != null) {
                when (it.payoutData.status) {
                    PayoutData.PayoutStatus.INCOMPLETE -> {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBusinessPointsReviewDialogFragment())
                    }

                    else -> {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalFlaxSellingConfirmationFragment())
                    }
                }
            } else if (it?.eligibility == false && it.payoutData != null) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalFlaxSellingConfirmationFragment())
            } else {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBusinessPointsReviewDialogFragment())
            }
        }
        viewModel.areYouEligible.observe(viewLifecycleOwner) {
            Log.w("TAG", "Are You Eligible: $it")
        }
    }

    fun setup() {
        withDrawViewModel.isEligibilityLoading.observe(viewLifecycleOwner) {
            if (it) showAPILoader() else hideAPILoader()
        }

        setFragmentResultListener(STATUS_CHANGE_REQUEST_KEY) { _, bundle ->
            val isUpdate = bundle.getBoolean(STATUS_CHANGE_RESULT_KEY)
            if (isUpdate) {
                viewModel.setOperations()
            }
        }

        setFragmentResultListener(PolicyDocumentFragment.REGISTER_DOCUMENT_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(PolicyDocumentFragment.REGISTER_DOCUMENT_RESULT_KEY)
            if (result) {
                withDrawViewModel.tncAccepted.postValue(result)
            }
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBusinessPointsReviewDialogFragment())
        }

//        binding.continueButton.setOnClickListener {
//            ensureInteractionAllowed {
//                if (viewModel.areYouEligible.value == true) {
//                    checkEligibility(body = getString(R.string.work_tab_new_eligibility_criteria))
//                } else {
//                    withDrawViewModel.eligibilityCheck()
//                }
//            }
//        }
    }

    override fun onOfferYourFlixForSaleClick() {
        ensureInteractionAllowed {
//            if (viewModel.areYouEligible.value == true) {
//                checkEligibility(body = getString(R.string.work_tab_new_eligibility_criteria))
//            } else {
                withDrawViewModel.eligibilityCheck()
//            }
        }
    }

}

