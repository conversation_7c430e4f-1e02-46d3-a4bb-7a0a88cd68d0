package com.app.messej.ui.chat.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.ColorRes
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.ItemChatMessageBroadcastBinding
import com.app.messej.databinding.ItemChatMessageMediaImageSmallBinding
import com.app.messej.ui.chat.ChatMessageUIModel

class ChatMessageBroadcastViewHolder(val binding: ItemChatMessageBroadcastBinding, userId: Int,private val broadcasterImage: String?, private var mListener
: ChatAdapter.ChatClickListener) : ChatMessageViewHolder(binding.root, userId, false, mListener) {

    override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
        super.bind(item)
        val cm = (item as ChatMessageUIModel.ChatMessageModel)
        message = cm.message as BroadcastMessage
        selected = item.selected
        dp = broadcasterImage

        val messageSpan = formatAndHighlightText(chatMessage.context, cm)
        chatMessage.setText(messageSpan?:"", TextView.BufferType.SPANNABLE)
        chatMessage.setExpanded(false)
        chatMessage.apply {
            setOnStateChangeListener { expanded ->
                cm.expanded = expanded
            }
            setExpanded(cm.expanded)
        }
        val color = setChatBubbleColor(binding.chatBubble, cm)
        loadMedia(mediaHolder, cm, color)

        chatHolder.setOnClickListener { mListener.onItemClick(item.message, layoutPosition) }
        chatHolder.setOnLongClickListener {
            mListener.onItemLongClick(item.message, layoutPosition)
            true
        }

        chatMessage.setOnClickListener {
            if(mListener.onItemClick(item.message, layoutPosition)) return@setOnClickListener
            if(chatMessage.isExpanded()) return@setOnClickListener
            chatMessage.setExpanded(true)
        }
        chatMessage.setOnLongClickListener {
            mListener.onItemLongClick(item.message, layoutPosition)
            true
        }
    }

    override fun loadMedia(mediaHolder: ViewGroup, cm: ChatMessageUIModel.ChatMessageModel,@ColorRes bubbleColor: Int) {
        if (cm.message.hasMedia) {
            if (cm.message.mediaMeta!!.mediaType == MediaType.IMAGE) {
                if (imageBinding == null) {
                    mediaHolder.removeAllViews()
                    val ib = ItemChatMessageMediaImageSmallBinding.inflate(LayoutInflater.from(mediaHolder.context), mediaHolder, false)
                    mediaHolder.addView(ib.root)
                    mediaHolder.visibility = View.VISIBLE
                    imageBinding = ib.actualMediaLayout
                }
            }
            // TODO "handle video case"
        }
        super.loadMedia(mediaHolder, cm, bubbleColor)
    }

    override fun getHighlightView() = null
}