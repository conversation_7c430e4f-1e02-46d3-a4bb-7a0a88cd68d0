package com.app.messej.ui.home.businesstab.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.ItemBusinesDealsListBinding
import com.bumptech.glide.Glide

class DealsTransactionsPagerAdapter(private val c: Context,private val listener: DealsTransactionListener, private val isSentFlax: Boolean) :
    PagingDataAdapter<DealsTransferHistory, DealsTransactionsPagerAdapter.DealsTransactionListViewHolder>(TransactionsDiff) {

     interface DealsTransactionListener {
         fun onUserClick(item: DealsTransferHistory)
     }

    override fun onBindViewHolder(holder: DealsTransactionListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = DealsTransactionListViewHolder(
        ItemBusinesDealsListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    inner class DealsTransactionListViewHolder(private val binding: ItemBusinesDealsListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: DealsTransferHistory) {
            Glide.with(c).load(item.profilePhoto) // Replace with the actual URL from your API
                .placeholder(R.drawable.im_user_placeholder_opaque).into(binding.imagePersonPhoto)
            binding.textViewPersonName.text = if (item.isDeleted == false) item.name else binding.root.context.getString(R.string.chat_deleted_user)

            if(item.membership.equals("Free")){
                binding.badgePremium.visibility=View.GONE
            }else{
                binding.badgePremium.visibility=View.VISIBLE
            }



            binding.flaxAmount.text = when (item.status) {
                DealsTransferHistory.FlaxStatus.Received -> {
                    item.receivedFlax.toString()
                }

                DealsTransferHistory.FlaxStatus.Sent -> {
                    item.sentFlax.toString()
                }

                DealsTransferHistory.FlaxStatus.Credited -> {
                    item.receivedFlax.toString()
                }
                DealsTransferHistory.FlaxStatus.Debited -> {
                    item.receivedFlax.toString()
                }

                else->"0.0"
            }


            binding.imagePersonPhoto.setOnClickListener {
                listener.onUserClick(item)
            }

            binding.textViewDate.text = DateTimeUtils.format(item.parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")
            binding.textViewGift.text = item.purpose

            if (isSentFlax) {
                binding.textViewGift.visibility = View.VISIBLE
                binding.flaxSendReceive.visibility = View.GONE

            } else {
                binding.textViewGift.visibility = View.GONE
                binding.flaxSendReceive.visibility = View.VISIBLE
                when (item.status) {
                    DealsTransferHistory.FlaxStatus.Received -> {
                        binding.flaxSendReceive.text = binding.root.context.getString(R.string.received_flax)
                        binding.flaxSendReceive.setTextColor(Color.GREEN)
                    }

                    DealsTransferHistory.FlaxStatus.Sent -> {
                        binding.flaxSendReceive.text = binding.root.context.getString(R.string.sent_flax)
                        binding.flaxSendReceive.setTextColor(Color.RED)
                    }

                    DealsTransferHistory.FlaxStatus.Credited -> {
                        binding.flaxSendReceive.text = binding.root.context.getString(R.string.purchased_flax)
                        binding.flaxSendReceive.setTextColor(Color.GREEN)
                    }
                    DealsTransferHistory.FlaxStatus.Debited -> {
                        binding.flaxSendReceive.text = binding.root.context.getString(R.string.debited_flax)
                        binding.flaxSendReceive.setTextColor(Color.RED)
                    }

                    else -> binding.flaxSendReceive.setTextColor(Color.BLACK)
                }
            }
        }
    }


    object TransactionsDiff : DiffUtil.ItemCallback<DealsTransferHistory>() {
        override fun areItemsTheSame(oldItem: DealsTransferHistory, newItem: DealsTransferHistory) = oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: DealsTransferHistory, newItem: DealsTransferHistory) = oldItem == newItem
    }

}