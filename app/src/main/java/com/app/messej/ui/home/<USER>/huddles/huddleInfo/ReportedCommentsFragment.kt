package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.HuddleReportedComment
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.model.enums.ReportedTab
import com.app.messej.databinding.FragmentReportedCommentsBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.chat.BaseChatDisplayFragment
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView

class ReportedCommentsFragment : BaseChatDisplayFragment() {

    private lateinit var binding: FragmentReportedCommentsBinding

    override val viewModel : ReportedMessagesViewModel by viewModels()

    companion object {
        const val HUDDLE_ID = "huddleId"

        fun getBundle(huddleId: Int) = Bundle().apply {
            putInt(HUDDLE_ID, huddleId)
        }

        fun parseBundle(bundle: Bundle?): Int? {
            val huddleId = bundle?.getInt(HUDDLE_ID)
            return huddleId
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_reported_comments, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setEmptyView()
        setup()
        observe()
    }

    private fun setup() {
        viewModel.setHuddleId(parseBundle(arguments), ReportedTab.TAB_COMMENTS)
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            message = R.string.chat_eds_message_reported_comment
        )
    }

    private fun observe() {
        viewModel.onDeleteComment.observe(viewLifecycleOwner) {
            if (it) {
                mAdapter?.refresh()
            }
        }
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.commentsList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView
        get() = binding.multiStateView

    override val reversedLayout: Boolean
        get() = false

    override val provideAdapter: ChatAdapter
        get() = ReportedCommentsListAdapter(layoutInflater, viewModel.user.id, this, object: ReportedCommentsListAdapter.ReportActionListener {
            override fun onDeleteAction(item: HuddleReportedComment, position: Int) {
                showDeleteAlertDialog(item)
            }

            override fun onViewComment(item: HuddleReportedComment, position: Int) {
                //findNavController().navigateSafe(BaseReportedFragmentDirections.actionReportedCommentsFragmentToHuddlePostCommentsFragment(item.messageId, item.huddleId))
            }

            override fun onViewReporters(item: HuddleReportedComment, position: Int) {
                findNavController().navigateSafe(HuddleReportsFragmentDirections.actionReportedMessagesFragmentToReportedParticipantsFragment(item.huddleId, item.reportId, item.reportsCount,
                                                                                                                                              ReportToManagerType.COMMENT))
            }

        })

    override fun showSelectionMode(show: Boolean) {

    }

    /** senderBlocked is false by default because the option to block the commented
     * user from huddle is not needed with reported comment delete action as per the
     * current requirement.
     */
    @SuppressLint("MissingInflatedId")
    private fun showDeleteAlertDialog(item: HuddleReportedComment) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.huddle_reported_comment_delete_confirmation_title))
            .setMessage(getString(R.string.huddle_reported_comment_delete_confirmation_message))
            .setCancelable(false)
            .setPositiveButton(getString(R.string.common_delete)) { _, _ ->
                viewModel.deleteReportedComment(item.huddleId, item.messageId, item.commentId, false)
            }
            .setNegativeButton(getString(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}