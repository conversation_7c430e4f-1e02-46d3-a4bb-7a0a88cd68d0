package com.app.messej.ui.home.publictab.huddles.create

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.Category
import com.app.messej.data.model.api.huddles.HuddleLanguage
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.common.BaseProfilePicAttachViewModel
import com.github.f4b6a3.uuid.UuidCreator
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.UUID

class CreateHuddleViewModel(application: Application): BaseProfilePicAttachViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())
    private val accountRepo = AccountRepository(getApplication())

    companion object {
        enum class NameError {
            NONE, LT_MIN, GT_MAX
        }
        private const val NAME_MAX_LENGTH = 50
    }

    val huddleType = MutableLiveData<HuddleType>()
    val huddleTypeIsPrivate = huddleType.map { it == HuddleType.PRIVATE }
    val userIsPremium = accountRepo.user.premium
    fun setHuddleType(type: HuddleType) {
        huddleType.postValue(type)
        getHuddleCategoriesList(type)
        getHuddleLanguageList()
    }

    val huddleUniqueKey: UUID = UuidCreator.getTimeBased()

    val name = MutableLiveData<String>()
    val bio = MutableLiveData<String>()
    val category = MutableLiveData<Int?>()
    val huddleLanguage = MutableLiveData<String?>()
    val requestToJoin = MutableLiveData<Boolean>(false)
    val didEnterName = MutableLiveData<Boolean>(false)

    val ruleCharacterMin = name.map { it.isNotEmpty() }
    val ruleCharacterMax = name.map { it.length <= NAME_MAX_LENGTH }

    private val _huddleNameValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun checkHuddleName() {
            _huddleNameValid.postValue(
                name.value.orEmpty().isNotBlank() &&
                        ruleCharacterMax.value == true &&
                        ruleCharacterMin.value == true
            )
        }
        med.addSource(ruleCharacterMin) { checkHuddleName() }
        med.addSource(ruleCharacterMax) {checkHuddleName()}
        med
    }

    val huddleNameValid: LiveData<Boolean> = _huddleNameValid

    private val _nameError: MediatorLiveData<NameError> by lazy {
        val med: MediatorLiveData<NameError> = MediatorLiveData(NameError.NONE)
        fun check() {
            if (didEnterName.value == false) {
                med.postValue(NameError.NONE)
            }
            else if (ruleCharacterMin.value == false) {
                med.postValue(NameError.LT_MIN)
            }
            else if (ruleCharacterMax.value == false) {
                med.postValue(NameError.GT_MAX)
            }
            else med.postValue(NameError.NONE)

        }
        med.addSource(ruleCharacterMin) { check() }
        med.addSource(ruleCharacterMax) { check() }
        med.addSource(didEnterName) { check() }
        med
    }
    val nameError: LiveData<NameError> = _nameError

    private val _createHuddleLoading = MutableLiveData(false)
    val createHuddleLoading: LiveData<Boolean> = _createHuddleLoading

    val createButtonEnable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            med.postValue(_createHuddleLoading.value==false && _languageLoading.value==false &&_huddleNameValid.value==true && category.value != null && huddleLanguage.value !=null)
        }
        med.addSource(_createHuddleLoading) { check() }
        med.addSource(_huddleNameValid) { check() }
        med.addSource(category) {check() }
        med.addSource(huddleLanguage){check()}
        med
    }

    val createHuddleCompleted = LiveEvent<PublicHuddle?>()

    private val _huddleCategoryList : MutableLiveData<MutableList<Category>> = MutableLiveData(null)
    val huddleCategoryList: LiveData<MutableList<Category>> = _huddleCategoryList

    private val _huddleLanguageList : MutableLiveData<MutableList<HuddleLanguage>> = MutableLiveData(null)
    val huddleLanguageList: LiveData<MutableList<HuddleLanguage>> = _huddleLanguageList

    private val _getCategoryListLoading = MutableLiveData(false)
    val getCategoryListLoading: LiveData<Boolean> = _getCategoryListLoading

    private val _languageLoading = MutableLiveData(false)
    val languageLoading: LiveData<Boolean> = _languageLoading

    private fun getHuddleCategoriesList(type: HuddleType){
        _getCategoryListLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO){
            when(val result = huddleRepo.getHuddleCategories(type)){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    _huddleCategoryList.postValue(result.value.categories)
                }
            }
            _getCategoryListLoading.postValue(false)
        }
    }

    private fun getHuddleLanguageList() {
        _languageLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO){
            when(val result = huddleRepo.getLanguages()){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {
                }
                is ResultOf.Success -> {
                    _huddleLanguageList.postValue(result.value.toMutableList())
                }
            }
            _languageLoading.postValue(false)
        }
    }

    fun createHuddle() {
        _createHuddleLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO){
            val compressed = finalImage?.let { huddleRepo.compressImage(it) }
            val private = huddleType.value==HuddleType.PRIVATE
            val language = huddleLanguage.value?: return@launch
            when(val result = huddleRepo.createHuddle(
                uuid = huddleUniqueKey,
                file = compressed, name = name.value!!, bio = bio.value,
                category = category.value, requestToJoin = if(private) true else (requestToJoin.value?:false), isPrivate = private, language = language)){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    createHuddleCompleted.postValue(result.value)
                }
            }
            _createHuddleLoading.postValue(false)
        }
    }

    fun setCategoryId(categoryId: Int?) {
        category.value = categoryId
    }

    fun setLanguage(language: HuddleLanguage?) {
     huddleLanguage.value=language?.englishName
    }
}