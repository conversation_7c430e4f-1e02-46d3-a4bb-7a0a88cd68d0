package com.app.messej.ui.home.publictab.flash.comments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.databinding.ItemHuddlePostCommentBinding
import com.app.messej.ui.chat.ChatMessageUIModel

class FlashCommentsAdapter(private val inflater: LayoutInflater, private val mListener: ChatCommentListener) :
    PagingDataAdapter<ChatMessageUIModel, FlashCommentsAdapter.CommentViewHolder>(CommentDiff) {

    interface ChatCommentListener {
        fun onCommentOptionsClick(msg: PostCommentItem, position: Int, view: View)
        fun onClickUser(msg: PostCommentItem)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CommentViewHolder {
        return CommentViewHolder(ItemHuddlePostCommentBinding.inflate(inflater, parent, false), mListener)
    }

    override fun onBindViewHolder(holder: CommentViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class CommentViewHolder(private val binding: ItemHuddlePostCommentBinding, private var mListener: ChatCommentListener) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ChatMessageUIModel) = with(binding) {
            val dataModel = (item as ChatMessageUIModel.PostCommentModel)
            comment = dataModel.comment as PostCommentItem
            chatFlag.setImageResource(dataModel.countryFlag ?: 0)
            commentActions.setOnClickListener {
                mListener.onCommentOptionsClick(dataModel.comment, layoutPosition, commentActions)
            }
            userDp.setOnClickListener {
                mListener.onClickUser(dataModel.comment)
            }

        }
    }

    object CommentDiff : DiffUtil.ItemCallback<ChatMessageUIModel>() {
        override fun areItemsTheSame(oldItem: ChatMessageUIModel, newItem: ChatMessageUIModel): Boolean {
            return oldItem is ChatMessageUIModel.PostCommentModel && newItem is ChatMessageUIModel.PostCommentModel && oldItem.comment.commentId == newItem.comment.commentId
        }

        override fun areContentsTheSame(oldItem: ChatMessageUIModel, newItem: ChatMessageUIModel) = oldItem == newItem
    }

}