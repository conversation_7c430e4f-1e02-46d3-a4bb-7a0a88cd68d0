package com.app.messej.ui.home.publictab.postat

import android.os.Bundle
import android.view.View
import androidx.annotation.CallSuper
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.data.model.enums.PostatTab
import com.app.messej.databinding.FragmentPublicPostatBaseBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.google.android.material.button.MaterialButton

abstract class PublicPostatBaseFragment : Fragment() {

    protected lateinit var binding: FragmentPublicPostatBaseBinding

    protected lateinit var mPostatPagerAdapter: FragmentStateAdapter

    protected val viewModel: PublicPostatViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    @CallSuper
    protected open fun setup() {
        binding.postatPager.apply {
            isUserInputEnabled = false
            adapter = mPostatPagerAdapter
        }

        binding.btnMe.setOnClickListener {
            viewModel.setCurrentTab(PostatTab.ME)
            (it as MaterialButton).isChecked = true
        }

        binding.btnStars.setOnClickListener {
            viewModel.setCurrentTab(PostatTab.STARS)
            (it as MaterialButton).isChecked = true
        }

        binding.btnAll.setOnClickListener {
            viewModel.setCurrentTab(PostatTab.ALL)
            (it as MaterialButton).isChecked = true
        }

        viewModel.currentTab.value?.let {
            binding.postatPager.setCurrentItem(it.ordinal, false)
        }
    }

    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it ?: return@observe
            if (binding.postatPager.currentItem == it.ordinal) return@observe
            binding.postatPager.setCurrentItem(it.ordinal, false)
        }
    }
}