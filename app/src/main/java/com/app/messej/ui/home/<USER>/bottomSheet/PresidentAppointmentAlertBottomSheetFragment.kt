package com.app.messej.ui.home.gift.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.databinding.FragmentPresidentAppointementAlertBottomSheetBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class PresidentAppointmentAlertBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentPresidentAppointementAlertBottomSheetBinding
    private val commonHomeViewModel: CommonHomeViewModel by activityViewModels()
    private val viewModel: PresidentAppointmentAlertBottomSheetViewModel by viewModels()
    private val args: PresidentAppointmentAlertBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_president_appointement_alert_bottom_sheet, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setUp()
    }

    private fun setUp() {
        if(args.fromSocket) viewModel.setArgs(commonHomeViewModel.onNewpPresidentCrowned.value) else  viewModel.setArgs(commonHomeViewModel.onUserBirthday.value)
        binding.btnBirthdayClose.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.buttonSendGift.setOnClickListener {
            viewModel.PresidentAppointment.value?.currentPresidentId?.let { userId ->
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalGiftFragment(userId, giftContext = GiftContext.PRESIDENT, birthday = false))
            }
        }
    }
}