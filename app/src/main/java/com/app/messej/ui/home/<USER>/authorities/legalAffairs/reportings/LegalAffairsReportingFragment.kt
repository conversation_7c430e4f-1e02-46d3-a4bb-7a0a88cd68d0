package com.app.messej.ui.home.publictab.authorities.legalAffairs.reportings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.data.model.enums.LegalAffairsReporting
import com.app.messej.databinding.FragmentLegalAffairsReportingBinding
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils
import com.app.messej.ui.home.publictab.authorities.legalAffairs.CaseDetailsBottomSheet.Companion.CASE_DETAIL_REQUEST_KEY
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsCommonViewModel
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsFragmentDirections
import com.app.messej.ui.home.publictab.authorities.legalAffairs.violations.LegalAffairsCaseListAdapter
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils

class LegalAffairsReportingFragment : Fragment() {

    private lateinit var binding: FragmentLegalAffairsReportingBinding
    private val viewModel : LegalAffairsReportingViewModel by viewModels()
    private val legalAffairsMainViewModel : LegalAffairsCommonViewModel by activityViewModels()
    private lateinit var mAdapter: LegalAffairsCaseListAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_legal_affairs_reporting, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }
    
    private fun setup() {
        setUpButtonClickListeners()
        initAdapter()
    }

    private fun setUpButtonClickListeners() {
        binding.apply {

            btnContent.cardView.setOnClickListener {
                viewModel.setTab(tab = LegalAffairsReporting.Content)
            }

            btnUsers.cardView.setOnClickListener {
                viewModel.setTab(tab = LegalAffairsReporting.Users)
            }
        }
    }

    private fun initAdapter() {

        mAdapter = LegalAffairsCaseListAdapter(object : LegalAffairsCaseListAdapter.ItemClickListener {
            override fun getUserId() = viewModel.user.id
            override fun showStatus() = legalAffairsMainViewModel.legalAffairsMainTab.value != LegalAffairsMainTab.InvestigationBureau
            override fun onClick(case: LegalRecordsResponse.ReportCase) {
                findNavController().navigateSafe(LegalAffairsFragmentDirections.actionLegalAffairsFragmentToCaseDetailBottomSheet(case.id))
            }

            override fun displayMode(): LegalAffairsCaseListAdapter.DisplayMode {
                return if (legalAffairsMainViewModel.legalAffairsMainTab.value == LegalAffairsMainTab.InvestigationBureau) LegalAffairsCaseListAdapter.DisplayMode.CASE
                else if (viewModel.selectedSubTab.value == LegalAffairsReporting.Users) LegalAffairsCaseListAdapter.DisplayMode.USER
                else LegalAffairsCaseListAdapter.DisplayMode.CASE
            }
        })

        val linearLayoutManager = LinearLayoutManager(context)
        binding.reportingList.recyclerView.apply {
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }

        mAdapter.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.reportingList.multiStateView.viewState = state
            }
        }

        AuthoritiesUtils.setupListEmptyView(
            multiStateView = binding.reportingList.multiStateView
        )
    }

    private fun observe() {
        viewModel.selectedSubTab.observe(viewLifecycleOwner) { tab ->
            binding.apply {
                btnContent.isSelected = tab == LegalAffairsReporting.Content
                btnUsers.isSelected = tab == LegalAffairsReporting.Users
            }
        }

        legalAffairsMainViewModel.legalAffairsMainTab.observe(viewLifecycleOwner) {
            setUpDataInListView(tab = it)
        }

        setFragmentResultListenerOnActivity(CASE_DETAIL_REQUEST_KEY) { _, _ ->
            mAdapter.refresh()
        }

        viewModel.investigationBureauCount.observe(viewLifecycleOwner) { count ->
            count?.let {
                legalAffairsMainViewModel.setInvestigationBureauCount(it)
            }
        }
    }


    private fun setUpDataInListView(tab: LegalAffairsMainTab) {
        when(tab) {
            LegalAffairsMainTab.MyLegalRecords -> {
                viewModel.myLegalRecordsReportingList.observe(viewLifecycleOwner) {
                    mAdapter.submitData(viewLifecycleOwner.lifecycle, it)
                }
            }
            LegalAffairsMainTab.InvestigationBureau -> {
                viewModel.investigationReportList.observe(viewLifecycleOwner) {
                    mAdapter.submitData(viewLifecycleOwner.lifecycle, it)
                }
            }
            else -> return
        }
    }
}