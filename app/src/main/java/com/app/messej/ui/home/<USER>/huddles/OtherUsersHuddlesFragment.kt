package com.app.messej.ui.home.publictab.huddles

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.huddles.OtherHuddlesList
import com.app.messej.databinding.FragmentUserHuddlesBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class OtherUsersHuddlesFragment : Fragment(), OtherUserHuddlePagerAdapter.ActionListener {
    private val viewModel: OtherUserHuddleListViewModel by viewModels()
    private lateinit var binding: FragmentUserHuddlesBinding

    private var otherUserHuddleAdapter: OtherUserHuddlePagerAdapter?=null
    private val args: OtherUsersHuddlesFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_user_huddles, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d("USERID", "${args.userId} ${args.userName}")
        viewModel.setUserId(args.userId)
        setUP()
        observe()

    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.other_user_huddle_header,args.userName)
    }

    fun setUP() {
        initAdapter()
    }
    fun observe(){
        viewModel.otherHuddlesList.observe(viewLifecycleOwner) {
            Log.d("AAAS", "INSIDE")
            otherUserHuddleAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun initAdapter() {
        otherUserHuddleAdapter = OtherUserHuddlePagerAdapter(this)

        val layoutManParticipant = LinearLayoutManager(context)

        binding.otherHuddleList.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = otherUserHuddleAdapter
        }

        otherUserHuddleAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = "Empty huddles"
        }
    }

    override fun goToHuddleClick(item: OtherHuddlesList) {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChatHuddle(item.id))

    }

}