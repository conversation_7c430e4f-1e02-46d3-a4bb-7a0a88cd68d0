package com.app.messej.ui.home.publictab.postat.mypostat

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.entity.Postat
import com.app.messej.databinding.ItemPostatAddBinding
import com.app.messej.databinding.ItemPostatListMineBinding
import com.app.messej.ui.home.publictab.postat.mypostat.MyPostatViewModel.MyPostatUIModel

class MyPostatListAdapter(private val listener: PostatClickListener) : PagingDataAdapter<MyPostatUIModel, RecyclerView.ViewHolder>(PostatDiff) {


    companion object {
        private const val VIEW_TYPE_ADD_BUTTON = 0
        private const val VIEW_TYPE_POSTAT = 1
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is MyPostatUIModel.SeparatorItem -> VIEW_TYPE_ADD_BUTTON
            is MyPostatUIModel.PostatItem -> VIEW_TYPE_POSTAT
            else -> throw IllegalStateException("Unknown view type")
        }
    }

    interface PostatClickListener {
        fun onMyPostatClicked(pos: Int, postat: Postat)
        fun onAddPostatClicked()
        fun onUploadPostatClicked()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_ADD_BUTTON -> AddButtonViewHolder(ItemPostatAddBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            VIEW_TYPE_POSTAT -> PostatViewHolder(ItemPostatListMineBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            else -> throw IllegalStateException("Unknown view type")
        }
    }
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is MyPostatUIModel.PostatItem -> (holder as PostatViewHolder).bind(item.postat)
            is MyPostatUIModel.SeparatorItem -> (holder as AddButtonViewHolder).bind()
            else -> throw IllegalStateException("Unknown view type")
        }
    }

    inner class PostatViewHolder(private val binding: ItemPostatListMineBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: Postat) = with(binding) {

            binding.postatItem = item
            binding.postatImageUrl = item.media.getOrNull(0)?.thumbnail
            binding.isMultiPostat = (item.mediaCount ?: 1) > 1

            postatCard.setOnClickListener {
                listener.onMyPostatClicked(bindingAdapterPosition, item)
            }
            postatUpload.setOnClickListener {
                listener.onUploadPostatClicked()
            }
        }
    }

    inner class AddButtonViewHolder(private val binding: ItemPostatAddBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind() {
            binding.postatCard.setOnClickListener {
                listener.onAddPostatClicked()
            }
        }
    }

    object PostatDiff : DiffUtil.ItemCallback<MyPostatUIModel>() {
        override fun areItemsTheSame(oldItem: MyPostatUIModel, newItem: MyPostatUIModel): Boolean {
            return (oldItem is MyPostatUIModel.PostatItem && newItem is MyPostatUIModel.PostatItem && oldItem.postat.messageId == newItem.postat.messageId) || (oldItem is MyPostatUIModel.SeparatorItem && newItem is MyPostatUIModel.SeparatorItem)
        }

        override fun areContentsTheSame(oldItem: MyPostatUIModel, newItem: MyPostatUIModel): Boolean {
            return oldItem == newItem
        }
    }
}
