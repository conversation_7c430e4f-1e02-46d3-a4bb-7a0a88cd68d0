package com.app.messej.ui.home.publictab.flash.comments

import android.util.Log
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.core.content.ContextCompat
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.PostatCommentLikePayload
import com.app.messej.data.model.api.postat.PostComment
import com.app.messej.data.model.entity.PostReply
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.composeComponents.UserBadge
import com.app.messej.ui.utils.TextFormatUtils
import com.app.messej.ui.utils.TextFormatUtils.bidiFormatMentions

@Composable
fun CommentsReplySingleItem(
    @DrawableRes flag: Int? = null,
    isFlash:Boolean,
    currentUser: Int,

    listener: CommentsListener,
    selectedItem:String?=null,

    replyComment: PostReply,
    comment:PostComment,
    onSelectedItemClick:() -> Unit,
    onDeleted:() -> Unit,
    ) {
    val context = LocalContext.current
    val commentTime =
        DateTimeUtils.durationToNowFromPast(replyComment.parsedCreatedTime)?.let { dur ->
            DateTimeUtils.formatDurationToSingleUnit(dur, context)
        }
    Log.d("MentionedDetails","$replyComment")
    val type = if (isFlash) replyComment.mentionedUserDetails else replyComment.mentionedUser
    val list = listOf(type as AbstractUser)

    val message = UserInfoUtil.decodeMentions(replyComment.comment.orEmpty(), list).bidiFormatMentions(list)
    val messageText = TextFormatUtils.highlightMentionsForEdit(context, message, list)

    val isUserDeleted = replyComment.senderDetails?.deletedAccount == true

    val alpha  = if ( selectedItem == replyComment.commentId || selectedItem == null ) 1f else 0.3f

    Box {
        if (selectedItem == replyComment.commentId) {
            Popup(
                alignment = Alignment.BottomEnd,
                properties = PopupProperties(dismissOnClickOutside = true), // Changed to true
                onDismissRequest = {
                    // Call a function to clear selection when popup is dismissed
                    onSelectedItemClick()
                }
            ) {
                DeleteCommentButton(
                    onDeleteClicked = {
                        onDeleted()
                        if (replyComment.type == CommentType.FLASH) {
                            listener.onDeleteFlashClicked(replyComment.commentId, true, replyComment.senderDetails?.id ?: 0)
                        } else {
                            listener.onDeletePostatClicked(replyComment.parentCommentId ?: "", replyComment.commentId, true, replyComment.senderDetails?.id ?: 0)
                        }
                    },
                )
            }
        }
        Row(
            modifier = Modifier
                .alpha(alpha)
                .padding(horizontal = 20.dp)
                .fillMaxWidth()
                .pointerInput(Unit) {
                    detectTapGestures(onLongPress = {
                        Log.d("SameUser","${replyComment.senderDetails?.id} ,  ${currentUser}")
                        Log.d("SameUser","${replyComment}")
                        if (replyComment.senderDetails?.id != currentUser) return@detectTapGestures
                        onSelectedItemClick()
                    })
                }
            ,
        ) {
            Box {
                // User Image View
                AsyncImage(
                    model = ImageRequest.Builder(context).crossfade(enable = true)
                        .data(data = replyComment.senderDetails?.thumbnail).build(),
                    placeholder = painterResource(R.drawable.im_user_placeholder_square),
                    error = painterResource(R.drawable.im_user_placeholder_square),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .size(size = 20.dp)
                        .clip(CircleShape)
                        .clickable {
                            if (selectedItem != null) return@clickable
                            if (!isUserDeleted) {
                                listener.onProfileImageClicked(replyComment.senderDetails?.id ?: 0)
                            }
                        }
                )
                // User Badge View
                UserBadge(
                    modifier = Modifier.align(alignment = Alignment.TopStart),
                    userType = if (replyComment.senderDetails?.premium == true) com.app.messej.data.model.enums.UserBadge.PREMIUM else null
                )
            }
            Column(
                modifier = Modifier
                    .padding(start = 8.dp)
                    .fillMaxWidth()
                    .weight(weight = 1f)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = replyComment.senderDetails?.name.toString(),
                        style = FlashatComposeTypography.defaultType.subtitle2,
                        color = colorResource(id = R.color.textColorPrimary),
                        maxLines = 1,
                        minLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f, fill = false)
                    )
                    //Used for flag
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .crossfade(enable = true)
                            .data(data = flag).build(),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .padding(start = dimensionResource(id = R.dimen.element_spacing))
                            .size(width = 20.dp, height = 13.dp)
                    )

                    commentTime?.let {
                        Text(
                            modifier = Modifier
                                .padding(start = dimensionResource(id=R.dimen.line_spacing)),
                            text = it,
                            style = FlashatComposeTypography.overLineSmaller,
                            color = colorResource(id = R.color.textColorPrimary)
                        )
                    }
                }
//Text(buildAnnotatedString { append(messageText) })

                AndroidView(
                    factory = { context ->
                        TextView(context).apply {
                            text = messageText // This is your styled CharSequence
                            setTextAppearance(R.style.TextAppearance_Flashat_Label)
                            setTextColor(ContextCompat.getColor(context, R.color.textColorPrimary))

                        }
                    },
                    modifier = Modifier.padding(4.dp)
                )

            }
            if (!isUserDeleted) {
                ButtonActions(
                    onClicked = {
                        if (selectedItem != null) return@ButtonActions
                        listener.onReplyClicked(null,replyComment)}
                    , icon = R.drawable.ic_reply, R.color.colorSecondary
                )
                ButtonActions(
                    onClicked = {
                        if (selectedItem != null || (replyComment.senderDetails?.id == currentUser)) return@ButtonActions
                        if (replyComment.type == CommentType.FLASH) {
                            listener.onFlashLikeClicked(null, replyComment.commentId)
                        } else {
                            listener.onPostatLikeClicked(
                                PostatCommentLikePayload(
                                    postId = comment.messageId,
                                    id = comment.commentId,
                                    replyId = replyComment.commentId,
                                    replyOwnerId = replyComment.senderDetails?.id,
                                    isLiked = true,
                                    postOwnerId = comment.contentOwnerId
                                )
                            )
                        }
                    },

                    icon = if(comment.senderDetails?.id == currentUser) R.drawable.ic_podium_like_disable  else R.drawable.ic_podium_like, totalCount = replyComment.totalLikeCount
                )
            }

        }
    }



}

