package com.app.messej.ui.home.businesstab

import android.content.ActivityNotFoundException
import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.content.res.ResourcesCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.databinding.FragmentBusinessCustomersBinding
import com.app.messej.ui.home.businesstab.customers.BusinessCustomersListFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import jp.wasabeef.transformers.glide.BlurTransformation

class BusinessCustomersFragment : Fragment() {

    companion object {
        fun newInstance() = BusinessCustomersFragment()
    }

    private lateinit var binding: FragmentBusinessCustomersBinding
    private lateinit var businessCustomersListFragment: BusinessCustomersListFragment

    private val viewModel: BusinessCustomersViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_customers, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
        blurBackgroundImage()
        initialiseFragment()
        navigateToYoutube()
        buttonActions()
    }

    private fun buttonActions() {
        binding.swipeButtonShare.setOnClickListener {
            viewModel.shareReferralLink()
            viewModel.countAppShare()
        }
        binding.swipeButtonBroadcast.setOnClickListener {
            if ((viewModel.businessOperation.value?.fans ?: 0) <= 0) {
                Toast.makeText(requireContext(), getString(R.string.business_broadcast_warning_message), Toast.LENGTH_SHORT).show()
            } else {
                val action = HomeBusinessFragmentDirections.actionGlobalNavigationChatBroadcast(BroadcastMode.ALL_FANS, getString(R.string.broadcast_msg_from_business_customers))
                findNavController().navigateSafe(action)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.getAppLanguage()
    }

    private fun navigateToYoutube() {
        binding.actionYoutubeView.setOnClickListener {
            try {
                val intent: Intent = if (AppCompatDelegate.getApplicationLocales().toLanguageTags() == AppLocale.ARABIC.isoCode){
                    Intent(Intent.ACTION_VIEW, Uri.parse(Constants.YOUTUBE_ARABIC_FLASHAT_LINK))
                }else{
                    Intent(Intent.ACTION_VIEW, Uri.parse(Constants.YOUTUBE_FLASHAT_LINK))
                }
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.addCategory(Intent.CATEGORY_BROWSABLE)
                startActivity(intent)
            } catch (e: ActivityNotFoundException) {
                Toast.makeText(requireContext(), e.message, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun initialiseFragment() {
        val fragment = childFragmentManager.findFragmentById(R.id.customers_tab_layout)
        if (fragment is BusinessCustomersListFragment) {
            businessCustomersListFragment = fragment
        }
    }

    private fun blurBackgroundImage() {
        Glide.with(requireContext()).asDrawable().load(ResourcesCompat.getDrawable(resources, R.drawable.bg_business, null))
            .apply(RequestOptions.bitmapTransform(BlurTransformation(requireContext(), 25, 10, true))).into(object : CustomTarget<Drawable>() {
                override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                    binding.layoutBusiness.background = resource
                }

                override fun onLoadCleared(placeholder: Drawable?) {

                }
            })
    }

    private fun replaceFragment() {
        val fm: FragmentManager = childFragmentManager
        val ft: FragmentTransaction = fm.beginTransaction()
        ft.replace(R.id.customer_list_layout, BusinessCustomersListFragment.newInstance(true))
        ft.commit()
    }

    private fun observe() {
        viewModel.customerDetailsError.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onCustomerListViewAll.observe(viewLifecycleOwner) {
            if (it) replaceFragment()
        }

        viewModel.user.observe(viewLifecycleOwner) {
            it?.let {
                val sendIntent: Intent = Intent().apply {
                    action = Intent.ACTION_SEND
                    putExtra(
                        Intent.EXTRA_TEXT, getString(R.string.title_operations_share_message, it.username, it.profile.referralLink)
                    )
                    type = "text/plain"
                }
                val shareIntent = Intent.createChooser(sendIntent, null)
                startActivity(shareIntent)
            }
        }

    }

}