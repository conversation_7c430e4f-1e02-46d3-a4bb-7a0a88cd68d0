package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.huddles.HuddleForSale
import com.app.messej.databinding.FragmentSellHuddlesListBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class SellHuddlesListFragment : Fragment(), SellHuddlesListPagerAdapter.ActionListener {

    private lateinit var binding: FragmentSellHuddlesListBinding
    private val viewModel: SellHuddlesListViewModel by viewModels()
    private var sellHuddlesAdapter: SellHuddlesListPagerAdapter? = null


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_sell_huddles_list, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.title_huddles_for_sale)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
        setUp()
    }

    private fun setUp() {
        initAdapter()
    }

    private fun observe() {

        viewModel.sellHuddlesList.observe(viewLifecycleOwner) {
            Log.d("AAAS","INSIDE")
            sellHuddlesAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        setFragmentResultListener(BuySellHuddleFragment.BUY_HUDDLE_RESULT_KEY) { _, bundle ->
            val success = bundle.getInt(BuySellHuddleFragment.BUY_HUDDLE_RESPONSE)

            viewModel.refreshHuddleList(success)

        }
    }


    private fun initAdapter() {
        sellHuddlesAdapter = SellHuddlesListPagerAdapter(this)

        val layoutManParticipant = LinearLayoutManager(context)

        binding.sellHuddlesList.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = sellHuddlesAdapter
        }

        sellHuddlesAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.no_huddles_for_sale)
        }
    }

    override fun buyNowClick(item: HuddleForSale) {
        findNavController().navigateSafe(SellHuddlesListFragmentDirections.actionSellHuddleListFragmentToBuySellHuddleFragment(item.huddleId!!))
    }

    override fun goToHuddleClick(item: HuddleForSale) {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChatHuddle(item.huddleId!!))
    }


}


