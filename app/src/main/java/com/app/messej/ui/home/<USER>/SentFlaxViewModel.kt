package com.app.messej.ui.home.businesstab

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.DealsBeneficiary
import com.app.messej.data.model.api.DealsBeneficiaryListResponse
import com.app.messej.data.model.api.DealsPurpose
import com.app.messej.data.model.api.DealsTransferPurposeResponse
import com.app.messej.data.model.api.business.SendFlaxResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SentFlaxViewModel(application: Application) : AndroidViewModel(application) {

    val businessRepo = BusinessRepository(application)
    val accountRepo = AccountRepository(application)
    val profRepo = ProfileRepository(application)

    val showSuccess = LiveEvent<String>()
    val errorMessage = LiveEvent<String>()
    val isReceiverBanned = LiveEvent<Boolean>()
    val noFlaxBalanceError = LiveEvent<Boolean>()


    var flaxAmountValue: Double? = null

    private val _dealsTransferPurpose = MutableLiveData<List<DealsPurpose>>(null)
    val dealsTransferPurpose: LiveData<List<DealsPurpose>> = _dealsTransferPurpose

    private val _dealsBeneficiaryList = MutableLiveData<List<DealsBeneficiary>>(null)
    val dealsBeneficiaryList: LiveData<List<DealsBeneficiary>> = _dealsBeneficiaryList

    private val _sendFlax = MutableLiveData<SendFlaxResponse?>(null)
    val sendFlax: LiveData<SendFlaxResponse?> = _sendFlax

    private val _sentFlaxStageValid = MutableLiveData<Boolean>()
    val sentFlaxStageValid: LiveData<Boolean> = _sentFlaxStageValid

    private val _availableBalance = MutableLiveData<Double>()
    val availableBalance: LiveData<Double> = _availableBalance

    private val _sentFlaxLoading = MutableLiveData<Boolean>(false)
    val sentFlaxLoading: LiveData<Boolean> = _sentFlaxLoading


    val otherUser= LiveEvent<String?>()


    val isSentFlaxValid = LiveEvent<Boolean>()


    val beneficiary = MutableLiveData<Int>(null)
    val flaxAmount = MutableLiveData<String>(null)
    val purpose = MutableLiveData<Int>(null)


    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    init {
        loadBusinessStatement()
    }


    companion object {
        enum class FlaxError {
            NONE, EMPTY, LIMIT_EXCEEDS, NO_FLAX_BALANCE, MIN_FLAX, MAX_FLAX
        }
        private const val FLAX_MINIMUM = 1.0
        private const val FLAX_MAXIMUM = 999999.0
    }

    val flaxRate: MediatorLiveData<Int> by lazy {
        val med = MediatorLiveData<Int>()
        fun combineSources() {
            if(_accountDetails.value!=null && isSentFlaxValid.value==true){
                med.postValue(accountDetails.value?.flaxRatePercentage)
            }
        }
        med.addSource(accountDetails) { combineSources() }
        med.addSource(isSentFlaxValid) { combineSources() }
        med
    }

    private fun loadBusinessStatement() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<BusinessStatement> = businessRepo.getBusinessStatements()) {
                is ResultOf.Success -> {
                    _availableBalance.postValue(result.value.availableBalance ?: 0.0)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
        }
    }


    private val _beneficiaryError = MediatorLiveData<Boolean>(false)
    val beneficiaryError: LiveData<Boolean> = _beneficiaryError

    fun validateBeneficiary(isError: Boolean) {
        _beneficiaryError.postValue(isError)
    }

    private val _flaxAmountError = MediatorLiveData(FlaxError.NONE)
    val flaxAmountError: LiveData<FlaxError> = _flaxAmountError

    fun validateFlaxAmount(bool: Boolean) {
        if (availableBalance.value != null && flaxAmount.value != "") {
            if (bool) {
                _flaxAmountError.postValue(FlaxError.EMPTY)
            } else {
                _flaxAmountError.postValue(FlaxError.NONE)

            }
        }
    }

    private val _purposeError = MediatorLiveData<Boolean>(false)
    val purposeError: LiveData<Boolean> = _purposeError

    fun validatePurpose(isError: Boolean) {
        _purposeError.postValue(isError)
    }

    fun getDealsPurposes() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<DealsTransferPurposeResponse> = businessRepo.getDealsTransferPurpose()) {
                is ResultOf.Success -> {
                    _dealsTransferPurpose.postValue(result.value.result)
                }

                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    fun getBeneficiaryList(keyword: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<DealsBeneficiaryListResponse> = businessRepo.getBeneficiaryList(keyword =  keyword, page = "1")) {
                is ResultOf.Success -> {
                    _dealsBeneficiaryList.postValue(result.value.users)
                }

                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    fun sentFlax() {
        viewModelScope.launch(Dispatchers.IO) {
            _sentFlaxLoading.postValue(true)
            when (val result: ResultOf<String> = businessRepo.sentFlax(
                beneficiary.value?.toInt() ?: 0, purpose.value?.toInt() ?: 0, flaxAmount.value?.toDouble() ?: 0.0
            )) {
                is ResultOf.Success -> {
                    _sentFlaxLoading.postValue(false)
                    showSuccess.postValue(result.value.toString())
                    isSentFlaxValid.postValue(false)
                    profRepo.getAccountDetails()
                }

                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                    _sentFlaxLoading.postValue(false)
                }

                is ResultOf.Error -> {
                    _sentFlaxLoading.postValue(false)
                }
            }
        }
    }

     fun getSendFlaxDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            if (flaxAmount.value?.isNotEmpty() == true) {
                when (val result: ResultOf<SendFlaxResponse> = businessRepo.getFlaxSentDetails(
                    beneficiary.value?.toInt() ?: 0, purpose.value?.toInt() ?: 0, flaxAmount.value?.toDouble() ?: 0.0
                )) {
                    is ResultOf.Success -> {
                        _sendFlax.postValue(result.value)
                    }
                    is ResultOf.APIError -> {
                        if (result.code == 423) {
                            isReceiverBanned.postValue(true)
                            return@launch
                        }
                        errorMessage.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {
                    }
                }
            }

        }
    }

    fun validateSendFlax() {
        if (flaxAmount.value.isNullOrEmpty()) {
            _flaxAmountError.value = FlaxError.EMPTY
        } else {
            flaxAmountValue = flaxAmount.value?.toDouble() ?: 0.0

            if (availableBalance.value != null && flaxAmountValue!! > (availableBalance.value ?: 0.0)) {
                if (availableBalance.value!! == 0.0) {
                    noFlaxBalanceError.postValue(true)
                    _flaxAmountError.value = FlaxError.NO_FLAX_BALANCE
                } else {
                    _flaxAmountError.value = FlaxError.LIMIT_EXCEEDS
                }
            } else if (flaxAmount.value.toString().isNullOrEmpty()) {
                _flaxAmountError.value = FlaxError.EMPTY
            } else if (flaxAmount.value?.toDouble() == 0.0) {
                _flaxAmountError.value = FlaxError.EMPTY
            } else if (flaxAmount.value == "") {
            } else {
                if (flaxAmountValue!! < FLAX_MINIMUM) {
                    _flaxAmountError.value = FlaxError.MIN_FLAX
                } else if (flaxAmountValue!! >= FLAX_MAXIMUM) {
                    _flaxAmountError.value = FlaxError.MAX_FLAX
                } else {
                    _flaxAmountError.value = FlaxError.NONE
                }

            }
        }
        _beneficiaryError.value = beneficiary.value?.toInt() == null
        _purposeError.value = purpose.value?.toInt() == null



        if (_flaxAmountError.value == FlaxError.NONE && _beneficiaryError.value == false && _purposeError.value == false) {
            isSentFlaxValid.postValue(true)
        } else {
            isSentFlaxValid.postValue(false)
        }
    }

    fun setDataFromPodium(receiverId: Int) {
        if (receiverId != -1) {
            fetchOtherUserDetails(receiverId)
        }
    }

    private fun fetchOtherUserDetails(receiverId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when(val result = profRepo.getPublicUserDetails(receiverId)) {
                is ResultOf.Success -> {
                    otherUser.postValue(result.value.name)
                    beneficiary.postValue(result.value.id)
                }
                else->{}
            }
        }
    }
}