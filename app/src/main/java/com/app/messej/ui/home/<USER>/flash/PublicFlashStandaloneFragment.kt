package com.app.messej.ui.home.publictab.flash

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.FragmentNavigator
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.databinding.FragmentPublicFlashBinding
import com.app.messej.databinding.LayoutPublicFlashBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.SubscriptionStatusViewModel
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindGiftRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindLegalAffairsPayFineButton
import com.app.messej.ui.utils.FragmentExtensions.bindMaidanToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.setBadgeNumber
import com.app.messej.ui.utils.FragmentExtensions.setupPayFineIcon
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils

class PublicFlashStandaloneFragment : PublicFlashBaseFragment() {

    override lateinit var binding: LayoutPublicFlashBinding

    private lateinit var outerBinding: FragmentPublicFlashBinding

    private val homeViewModel: CommonHomeViewModel by activityViewModels()
    private val subscriptionStatusViewModel: SubscriptionStatusViewModel by activityViewModels()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_flash, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        binding = outerBinding.layout
        binding.viewModel = viewModel
        return outerBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        outerBinding.interactionBanner.upgradeTitle.setOnClickListener{
            upgradeToPremium()
        }
    }
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_home_notifications,menu)
        super.onCreateMenu(menu, menuInflater)
    }

    private var notificationBadge: BadgeDrawable? = null

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        notificationBadge = BadgeDrawable.create(requireContext())
        setBadgeNumber(notificationBadge,homeViewModel.unreadNotifications.value)
        notificationBadge?.apply {
            adjustForNotifications(requireContext())
            BadgeUtils.attachBadgeDrawable(this, outerBinding.appbar.toolbar, R.id.action_notifications)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_notifications -> findNavController().navigateSafe(PublicFlashStandaloneFragmentDirections.actionGlobalNotificationFragment())
            R.id.action_search -> toSearch()
            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    fun setup() {
        outerBinding.upgradeBanner.upgradeBannerLayout.setOnClickListener {
            upgradeToPremium()
        }

        outerBinding.upgradeBanner.dismissUpgradeBannerBtn.setOnClickListener {
            homeViewModel.onDismissUpgradeBanner()
        }

        setupPromoBoard(outerBinding.promoBar)
        setupPayFineIcon(composeView = outerBinding.payFine)

    }

    private fun upgradeToPremium() {
        subscriptionStatusViewModel.isActive.value?.let { isActive ->
            when (isActive) {
                UserSubscriptionStatus.ACTIVE, UserSubscriptionStatus.EXPIRED -> {
                    findNavController().navigateSafe(
                        PublicFlashStandaloneFragmentDirections.actionGlobalAlreadySubscribedFragment(false)
                    )
                }
                else -> {
                    findNavController().navigateSafe(PublicFlashStandaloneFragmentDirections.actionGlobalUpgradePremiumFragment())
                }
            }
        }
    }

    fun observe() {
        homeViewModel.accountDetails.observe(viewLifecycleOwner){
            outerBinding.citizenship = it?.citizenship
            outerBinding.isPremium = it?.isPremium
            outerBinding.daysLeft = it?.remainingDaysForResident
        }
        homeViewModel.didDismissUpgradeBannerToday.observe(viewLifecycleOwner) {
            outerBinding.showUpgradeBanner = !it
        }

        subscriptionStatusViewModel.subscriptionLoaded.observe(viewLifecycleOwner) {
            it?.let { clickable ->
                outerBinding.upgradeBanner.clickable = clickable
                outerBinding.interactionBanner.clickable = clickable
            }
        }

    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(outerBinding.appbar.toolbar, customBackButton = false)
        bindGiftRateToolbarChip(outerBinding.appbar.giftChip)
        bindFlaxRateToolbarChip(outerBinding.appbar.flaxRateChip)
        bindMaidanToolbarChip(outerBinding.appbar.maidanChip)
        bindLegalAffairsPayFineButton(outerBinding.appbar.payFineChip)
    }

    override fun toPlayer(pos: Int, extras: FragmentNavigator.Extras) {
        findNavController().navigateSafe(PublicFlashStandaloneFragmentDirections.actionPublicFlashStandaloneFragmentToFlashPlayerFragment(viewModel.currentTab.value?: return),extras)
    }

    override fun toSearch() {
        findNavController().navigateSafe(PublicFlashStandaloneFragmentDirections.actionPublicFlashStandaloneFragmentToFlashSearchFragment())
    }

    override fun toMyFlash() {
        findNavController().navigateSafe(PublicFlashStandaloneFragmentDirections.actionPublicFlashStandaloneFragmentToMyFlashFragment())
    }

}