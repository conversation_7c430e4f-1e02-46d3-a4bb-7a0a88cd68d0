package com.app.messej.ui.home.gift

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentInsufficentBalanceBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class GiftBalanceBottomSheet: BottomSheetDialogFragment() {

    lateinit var binding: FragmentInsufficentBalanceBinding
    private val viewModel: GiftCommonViewModel by viewModels()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_insufficent_balance, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.Widget_Flashat_StickerBottomSheet)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {

    }

    private fun setup() {
    }
    override fun getTheme(): Int {
        return R.style.Widget_Flashat_Tribe_BottomSheet
    }

}