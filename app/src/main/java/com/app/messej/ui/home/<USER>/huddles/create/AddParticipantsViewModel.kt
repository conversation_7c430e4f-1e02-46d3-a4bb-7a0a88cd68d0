package com.app.messej.ui.home.publictab.huddles.create

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.filter
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.huddles.AddParticipantsResponse
import com.app.messej.data.model.api.huddles.HuddleInfo
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.google.gson.annotations.SerializedName
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class AddParticipantsViewModel(application: Application) : AndroidViewModel(application) {

    val showCompactLoading = MutableLiveData(false)

    private val huddleID = MutableLiveData<Int?>(null)

    private val accountRepo = AccountRepository(getApplication())
    private val huddleRepo = HuddlesRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())

    private val justInvitedList = arrayListOf<Int>()

    val huddle: LiveData<PublicHuddle?> = huddleID.switchMap { id ->
        id ?: return@switchMap null
        huddleRepo.getPublicHuddleFlow(id).asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    }
    var searchKeywordDears = MutableLiveData<String>(null)
    private val searchTermDears = MutableLiveData<String>("")

    var searchKeywordFans = MutableLiveData<String>(null)
    private val searchTermFans = MutableLiveData<String>("")

    var searchKeywordLikers = MutableLiveData<String>(null)
    private val searchTermLikers = MutableLiveData<String>("")

    private var huddleInfo  = MutableLiveData<HuddleInfo>(null)

    init {
        viewModelScope.launch {
            searchKeywordDears.asFlow().debounce(500L).collect {
                it ?: return@collect
                if (it.isEmpty()) {
                    searchTermDears.postValue("")
                } else {
                    searchTermDears.postValue(it)
                }
            }

        }
        viewModelScope.launch {
            searchKeywordFans.asFlow().debounce(500L).collect {
                it ?: return@collect
                if (it.isEmpty()) {
                    searchTermFans.postValue("")
                } else {
                    searchTermFans.postValue(it)
                }
            }
        }
        viewModelScope.launch {
            searchKeywordLikers.asFlow().debounce(500L).collect {
                it ?: return@collect
                if (it.isEmpty()) {
                    searchTermLikers.postValue("")
                } else {
                    searchTermLikers.postValue(it)
                }
            }
        }
    }

    fun resetSearch() {
        searchKeywordDears.postValue("")
        searchKeywordFans.postValue("")
        searchKeywordLikers.postValue("")
    }

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val _participantsDearsList = searchTermDears.switchMap {
        huddleID.value ?: return@switchMap null
        profileRepo.getAddParticipantsList(ParticipantsTab.DEARS.value, huddleID.value!!, it).liveData.cachedIn(viewModelScope)
    }
    private val _participantsFansList = searchTermFans.switchMap {
        huddleID.value ?: return@switchMap null
        profileRepo.getAddParticipantsList(ParticipantsTab.FANS.value, huddleID.value!!, it).liveData.cachedIn(viewModelScope)
    }
    private val _participantsLikersList = searchTermLikers.switchMap {
        huddleID.value ?: return@switchMap null
        profileRepo.getAddParticipantsList(ParticipantsTab.LIKERS.value, huddleID.value!!, it).liveData.cachedIn(viewModelScope)
    }


    val participantsDearsList: MediatorLiveData<PagingData<AddParticipantsListAdapter.AddParticipantsUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<AddParticipantsListAdapter.AddParticipantsUIModel>?>(null)
        fun updateMembersList() {
            val list = _participantsDearsList.value?.map { item ->
                _nickNames.value?.findNickName(item.memberId)?.let {
                    item.name = it
                }
                AddParticipantsListAdapter.AddParticipantsUIModel(item, _selectedMembersList.find { return@find it.memberId == item.memberId } != null)
            }?.filter {
                !justInvitedList.contains(it.members.memberId)
            }
            participantsDearsList.postValue(list)
        }
        med.addSource(_participantsDearsList) { updateMembersList() }
        med.addSource(_selectedMembers) { updateMembersList() }
        med.addSource(_nickNames) { updateMembersList() }
        med
    }

    val participantsFansList: MediatorLiveData<PagingData<AddParticipantsListAdapter.AddParticipantsUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<AddParticipantsListAdapter.AddParticipantsUIModel>?>(null)
        fun updateMembersList() {
            val list = _participantsFansList.value?.map { item ->
                _nickNames.value?.findNickName(item.memberId)?.let {
                    item.name = it
                }
                AddParticipantsListAdapter.AddParticipantsUIModel(item, _selectedMembersList.find { return@find it.memberId == item.memberId } != null)
            }?.filter {
                !justInvitedList.contains(it.members.memberId)
            }
            participantsFansList.postValue(list)
        }
        med.addSource(_participantsFansList) { updateMembersList() }
        med.addSource(_selectedMembers) { updateMembersList() }
        med.addSource(_nickNames) { updateMembersList() }
        med
    }

    val participantsLikersList: MediatorLiveData<PagingData<AddParticipantsListAdapter.AddParticipantsUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<AddParticipantsListAdapter.AddParticipantsUIModel>?>(null)
        fun updateMembersList() {
            val list = _participantsLikersList.value?.map { item ->
                _nickNames.value?.findNickName(item.memberId)?.let {
                    item.name = it
                }
                AddParticipantsListAdapter.AddParticipantsUIModel(item, _selectedMembersList.find { return@find it.memberId == item.memberId } != null)
            }?.filter {
                !justInvitedList.contains(it.members.memberId)
            }
            participantsLikersList.postValue(list)
        }
        med.addSource(_participantsLikersList) { updateMembersList() }
        med.addSource(_selectedMembers) { updateMembersList() }
        med.addSource(_nickNames) { updateMembersList() }
        med
    }

    val user: CurrentUser get() = accountRepo.user

    val currentTab = MutableLiveData(ParticipantsTab.DEARS)

    enum class ParticipantsTab(val value: String) {
        @SerializedName("dears")
        DEARS("dears"),

        @SerializedName("fans")
        FANS("fans"),

        @SerializedName("likers")
        LIKERS("likers")
    }

    fun setCurrentTab(tab: ParticipantsTab) {
        currentTab.postValue(tab)
    }

    fun setHuddleId(huddleId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            getHuddleInfo()
            huddleID.postValue(huddleId)
            searchTermDears.postValue("") //To trigger pagingSource
            searchTermFans.postValue("") //To trigger pagingSource
            searchTermLikers.postValue("") //To trigger pagingSource
        }
    }

    //Selection Mode
    private var _selectedMembersList: MutableList<AddParticipantsResponse.Members> = mutableListOf()
    private val _selectedMembers = MutableLiveData<List<AddParticipantsResponse.Members>>(listOf())
    val selectedMembers: LiveData<Int> = _selectedMembers.map { return@map it.size }

    val onRemoveSelection = LiveEvent<Boolean>()

    val invitationSent = LiveEvent<Boolean>()

    fun selectedItemCount(tabType: ParticipantsTab) = _selectedMembers.value?.filter { it.relationship == tabType }?.size

//    fun enterSelectionMode(member: AddParticipantsResponse.Members, position: Int) {
//        _selectedMembersList = mutableListOf()
//        selectMembers(member, position)
//    }

    fun selectMember(member: AddParticipantsResponse.Members, position: Int): Boolean {
        _selectedMembersList.apply {
            if (!removeIf { member.memberId == it.memberId }) {
                add(member)
            } else {
                onRemoveSelection.postValue(true)
            }
            _selectedMembers.value = toList()
        }
        return true
    }


    fun selectMembers(members: List<AddParticipantsResponse.Members>) {
        members.forEach { member ->
            _selectedMembersList.apply {
                if (find { member.memberId == it.memberId } == null) {
                    add(member)
                }
            }
        }
        _selectedMembers.value = _selectedMembersList

    }

    fun unSelectMembers(members: List<AddParticipantsResponse.Members>) {
        members.forEach { item ->
            _selectedMembersList.removeIf {
                it.memberId == item.memberId
            }
        }
        _selectedMembers.value = _selectedMembersList
    }

    private val _inviting = MutableLiveData(false)
    val inviting: LiveData<Boolean> = _inviting

    val onInviteError = LiveEvent<String>()
    val onParticipantsLimitExceedError = LiveEvent<Boolean>()

    fun sendInvitations() {
        huddleInfo.value?.let {
            if (!it.canAddParticipant) {
                onParticipantsLimitExceedError.postValue(true)
                return
            }
        }

        viewModelScope.launch(Dispatchers.IO) {
            _inviting.postValue(true)
            val members = _selectedMembersList
            val dearsList = arrayListOf<Int>()
            val fansList = arrayListOf<Int>()
            val likersList = arrayListOf<Int>()
            members.onEach {
                when (it.relationship) {
                    ParticipantsTab.DEARS -> dearsList.add(it.memberId)
                    ParticipantsTab.FANS -> fansList.add(it.memberId)
                    ParticipantsTab.LIKERS -> likersList.add(it.memberId)
                    null -> {}
                }
                justInvitedList.add(it.memberId)
            }
            when (val result = huddleRepo.sendInvitations(huddleID.value!!, dearsList, fansList, likersList)) {
                is ResultOf.APIError -> {
                    _inviting.postValue(false)
                    if(result.code==400) {
                        onInviteError.postValue(result.error.message)
                    }
                    justInvitedList.clear()
                }
                is ResultOf.Error -> {
                    _inviting.postValue(false)
                    justInvitedList.clear()
                }
                is ResultOf.Success -> {
                    delay(500)
                    searchTermDears.postValue("") //To trigger pagingSource
                    searchTermFans.postValue("") //To trigger pagingSource
                    searchTermLikers.postValue("") //To trigger pagingSource
                    delay(1500)
                    searchTermDears.postValue("") //To trigger pagingSource
                    searchTermFans.postValue("") //To trigger pagingSource
                    searchTermLikers.postValue("") //To trigger pagingSource

                    _selectedMembersList.apply {
                        clear()
                        _selectedMembers.postValue(this)
                    }
                    invitationSent.postValue(true)
                    _inviting.postValue(false)
                    getHuddleInfo()
                }
            }
        }

    }

    private suspend fun getHuddleInfo() {
        showCompactLoading.postValue(true)
            when (val result: ResultOf<HuddleInfo> = huddleRepo.getFullHuddleInfo(huddleID.value?: return)) {
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {
                }
                is ResultOf.Success -> {
                    val huddle = result.value
                    huddleInfo.postValue(huddle)
                }
            }
        showCompactLoading.postValue(false)
    }

}