package com.app.messej.ui.home.publictab.postat.create

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.OptIn
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.AbstractUriMedia
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.ItemPostatMediaViewerBinding
import com.google.android.material.button.MaterialButton

class PostatViewerViewPagerAdapter(private val listener: PlayerActionListener, private val mediaList: MutableList<AbstractUriMedia>) :
    RecyclerView.Adapter<PostatViewerViewPagerAdapter.ViewPagerViewHolder>() {
    
    interface PlayerActionListener {
        fun registerForFuturePlayback(obj: FuturePlaybackObject)
        fun detachFuturePlayback(pos: Int)
    }

    data class FuturePlaybackObject(
        val pos: Int,
        val media: AbstractUriMedia,
        val onPlay: (Player) -> Unit,
        val onStop: () -> Unit
    )

    inner class ViewPagerViewHolder(val binding: ItemPostatMediaViewerBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun setData(media: AbstractUriMedia) {
            binding.media = media
        }

        @OptIn(UnstableApi::class)
        fun bindPlayer(): FuturePlaybackObject? = with(binding) {
            media?.let {
                if (it.mediaType == MediaType.IMAGE) return@with null
                Log.w("POSTATF", "preparePlayerForSetup: ${it.thumbnail}")
                val fpo = FuturePlaybackObject(bindingAdapterPosition, it, { player ->
                    Log.w("POSTATF", "binding player: ${it.thumbnail}")
                    player.apply {
                        binding.playerView.player = this
                        val playButton = binding.playerView.findViewById<MaterialButton>(R.id.exo_play_pause_custom)
                        playButton.setOnClickListener {
                            if (isPlaying) {
                                pause()
                            } else {
                                seekTo(0)
                                play()
                            }
                        }
                        addListener(object : Player.Listener {
                            override fun onIsPlayingChanged(isPlaying: Boolean) {
                                Log.w("FREF", "onIsPlayingChanged: $isPlaying")
                                if (isPlaying) {
                                    playButton.setIconResource(R.drawable.ic_media_stop_large)
                                } else {
                                    playButton.setIconResource(R.drawable.ic_media_play_large)
                                }
                            }
                        })
                    }
                }, {
                       cleanup()
                   })
                listener.registerForFuturePlayback(fpo)
                return@with fpo
            }
        }

        fun cleanup() = with(binding) {
            playerView.player = null
            listener.detachFuturePlayback(bindingAdapterPosition)
        }
    }
    override fun getItemCount(): Int = mediaList.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewPagerViewHolder {

        val binding = ItemPostatMediaViewerBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )

        return ViewPagerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewPagerViewHolder, position: Int) {
        holder.setData(mediaList[position])
    }

    override fun onViewDetachedFromWindow(holder: ViewPagerViewHolder) {
        super.onViewDetachedFromWindow(holder)
        Log.w("POSTATF", "onViewDetachedFromWindow: $holder")
        holder.cleanup()
    }

    override fun onViewAttachedToWindow(holder: ViewPagerViewHolder) {
        super.onViewAttachedToWindow(holder)
        Log.w("POSTATF", "onViewAttachedToWindow: $holder")
        holder.bindPlayer()
    }

    fun updateData(newMedia: List<AbstractUriMedia>) {
        val diffResult = DiffUtil.calculateDiff(DiffCallback(mediaList, newMedia))
        mediaList.clear()
        mediaList.addAll(newMedia)
        diffResult.dispatchUpdatesTo(this)
    }

    class DiffCallback(
        private val oldList: List<AbstractUriMedia>,
        private val newList: List<AbstractUriMedia>
    ) : DiffUtil.Callback() {
        override fun getOldListSize() = oldList.size
        override fun getNewListSize() = newList.size
        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldMedia = oldList[oldItemPosition]
            val newMedia = newList[newItemPosition]
            return if (oldMedia is PostatDeviceMedia && newMedia is PostatDeviceMedia) oldMedia.uri == newMedia.uri
            else if (oldMedia is PostatMedia && newMedia is PostatMedia) oldMedia.s3Key == newMedia.s3Key
            else false
        }
        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldMedia = oldList[oldItemPosition]
            val newMedia = newList[newItemPosition]
            return oldMedia == newMedia
        }
    }

}