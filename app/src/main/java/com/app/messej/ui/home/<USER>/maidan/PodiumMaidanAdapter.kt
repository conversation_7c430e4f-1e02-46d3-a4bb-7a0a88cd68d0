package com.app.messej.ui.home.publictab.maidan

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.entity.Podium
import com.app.messej.databinding.ItemPodiumListMaidanBinding

class PodiumMaidanAdapter( private val listener: PodiumActionListener): PagingDataAdapter<Podium,PodiumMaidanAdapter.PodiumListViewHolder>(PodiumDiff) {

    interface PodiumActionListener {
        fun onPodiumClicked(pod: Podium)
    }

    inner class PodiumListViewHolder(private val binding:ItemPodiumListMaidanBinding):RecyclerView.ViewHolder(binding.root){
        fun bind(item: Podium) = with(binding) {
            podium = item
            
            if (item.hasCompetitor) {
                competitorName.text = item.competitorName
                managerName.text = item.managerName
                vsText.isVisible = true
                competitorName.isVisible = true
            } else {
                managerName.text = item.managerName
                vsText.isVisible = false
                competitorName.isVisible = false
            }

            card.setOnClickListener {
                listener.onPodiumClicked(item)
            }
            userAction.setOnClickListener {
                listener.onPodiumClicked(item)
            }
        }
    }
    object PodiumDiff : DiffUtil.ItemCallback<Podium>(){
        override fun areItemsTheSame(oldItem: Podium, newItem: Podium) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: Podium, newItem: Podium) = oldItem == newItem
    }

    override fun onBindViewHolder(holder: PodiumListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumListViewHolder {
        val binding = ItemPodiumListMaidanBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumListViewHolder(binding)
    }
}