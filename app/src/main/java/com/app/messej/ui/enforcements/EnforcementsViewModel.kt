package com.app.messej.ui.enforcements

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.UserEnforcements
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn

class EnforcementsViewModel(application: Application): AndroidViewModel(application) {

    private val accountRepo = AccountRepository(application)

    val user: CurrentUser
        get() = accountRepo.user

    val enforcementFLow = accountRepo.getEnforcementsFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = null
    )

    val enforcementsLiveData = enforcementFLow.asLiveData()

    val enforcements: UserEnforcements
        get() = enforcementFLow.value?: UserEnforcements(
            userId = accountRepo.user.id,
            enforcementsStatus = UserEnforcements.EnforcementStatus(),
            enforcementsMeta = UserEnforcements.EnforcementMeta()
        )

    val userBanned: Boolean
        get() = enforcements.enforcementsStatus.banned

    val userBlacklisted: Boolean
        get() = enforcements.enforcementsStatus.blacklisted

    val userIsSuspectedToBan: Boolean
        get() = enforcements.enforcementsStatus.suspectedBan

    val userIsPendingBlackListed: Boolean
        get() = enforcements.enforcementsStatus.pendingBlacklist

}