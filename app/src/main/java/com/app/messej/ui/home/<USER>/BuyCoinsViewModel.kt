package com.app.messej.ui.home.businesstab

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.app.messej.data.model.BuyFlaxRates
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.DealsBuyFlaxRatesResponse
import com.app.messej.data.model.api.gift.GiftResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.api.subscription.PremiumResponse
import com.app.messej.data.model.entity.SubscriptionPurchaseRequest
import com.app.messej.data.model.enums.PurchaseItem
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

class BuyCoinsViewModel(application: Application) : AndroidViewModel(application) {
    val businessRepo = BusinessRepository(application)
    private var profileRepo: ProfileRepository = ProfileRepository(application)
    private var accountRepo: AccountRepository = AccountRepository(application)
    private val datastore: FlashatDatastore = FlashatDatastore()

    val user: CurrentUser get() = AccountRepository(getApplication()).user

    val accessTokens = accountRepo.tokens?.accessToken

    val selectedProduct = MutableLiveData<ProductDetails?>(null)

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    private val _processInProgress = MutableLiveData<Boolean>(false)
    val processInProgress: LiveData<Boolean> = _processInProgress

    private val _buyFlaxLoading = MutableLiveData<Boolean>(false)
    val buyFlaxLoading: LiveData<Boolean> = _buyFlaxLoading

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    val _purchaseItem = MutableLiveData<PurchaseItem?>(PurchaseItem.BUY_COIN)
    val purchaseItem: LiveData<PurchaseItem?> = _purchaseItem.distinctUntilChanged()

    private val _currentlySelectedItem = MutableLiveData<FlaxRatesAndProductDetails?>(null)
    val currentlySelectedItem : LiveData<FlaxRatesAndProductDetails?> = _currentlySelectedItem.distinctUntilChanged()

    val retryBuyFlax = LiveEvent<Boolean>()
    val maxRetryReached = LiveEvent<Boolean>()
    private var retryCount = 0

    private val giftList = MutableLiveData<GiftResponse?>(null)

    companion object {
        private const val MAX_RETRY_REACHED = 3
    }

    private val _inAppPurchaseRequests = MutableLiveData<List<SubscriptionPurchaseRequest>>()
    val inAppPurchaseRequests: LiveData<List<SubscriptionPurchaseRequest>> = _inAppPurchaseRequests
    val hasLocalData = LiveEvent<Boolean>()

    init {
        // Retrieve in-app purchase requests from the database and set the value in init
        viewModelScope.launch {

            val list = profileRepo.getInAppPurchaseRequestsFromDB()
            _inAppPurchaseRequests.value = list
            if (list.isEmpty()) {
                hasLocalData.postValue(false)
            } else {
                hasLocalData.postValue(true)
            }
        }
    }

    val successMessage = LiveEvent<String?>()

    private val _dealsFlaxProductsLoading = MutableLiveData<Boolean>(null)
    val dealsFlaxProductsLoading: LiveData<Boolean> = _dealsFlaxProductsLoading

    private val _pricingPhase = MutableLiveData<ProductDetails.OneTimePurchaseOfferDetails?>(null)
    val pricingPhase: LiveData<ProductDetails.OneTimePurchaseOfferDetails?> = _pricingPhase

    private val _dealsFlaxPurchaseList = MutableLiveData<List<BuyFlaxRates>>(listOf())
    val dealsFlaxPurchaseList: LiveData<List<BuyFlaxRates>> = _dealsFlaxPurchaseList


    private val _playStoreProductsList = MutableLiveData<List<ProductDetails>>(listOf())


    data class FlaxRatesAndProductDetails(val flaxRates: BuyFlaxRates, val productDetail: ProductDetails){
        val flaxName: String
            get()= productDetail.name
                .replace("FLiX","", ignoreCase = true)
                .replace("COiNS", "", ignoreCase = true)
        val price:String?
                get() = productDetail.oneTimePurchaseOfferDetails?.formattedPrice
        val flax: Double
            get()=flaxRates.flax?:0.0
        val coin: Double
            get()=flaxRates.coins?:0.0
        val disCount: Int?
            get() = flaxRates.discountPercentage?:0
    }

    val productDetailsList: MediatorLiveData<List<FlaxRatesAndProductDetails>> by lazy {
        val med = MediatorLiveData<List<FlaxRatesAndProductDetails>>()
        fun update() {
            _dealsFlaxProductsLoading.postValue(false)
            val list = mutableListOf<FlaxRatesAndProductDetails>()
            _dealsFlaxPurchaseList.value?.forEach{ product->
                _playStoreProductsList.value?.find {
                    product.androidProductId == it.productId

                }?.let {
                    list.add(FlaxRatesAndProductDetails(product,it))
                }
            }
              med.postValue(list)
        }
        _dealsFlaxProductsLoading.postValue(true)
        med.addSource(_playStoreProductsList) { update() }
        med.addSource(_dealsFlaxPurchaseList.distinctUntilChanged()) { update() }
        med
    }
     val errorMessage = LiveEvent<String>()

    fun getDealsBuyCoinRates() {
        _dealsFlaxProductsLoading.postValue(false)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<DealsBuyFlaxRatesResponse> = businessRepo.getDealsBuyCoinRates()) {
                is ResultOf.Success -> {
                    _dealsFlaxPurchaseList.postValue(result.value.rates)
                }

                is ResultOf.APIError -> {
//                    _dealsFlaxProductsLoading.postValue(false)
                    errorMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    fun getDealsBuyFlixRates() {
        _dealsFlaxProductsLoading.postValue(false)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<DealsBuyFlaxRatesResponse> = businessRepo.getDealsBuyFlixRates()) {
                is ResultOf.Success -> {
                    _dealsFlaxPurchaseList.postValue(result.value.rates)
                }

                is ResultOf.APIError -> {
//                    _dealsFlaxProductsLoading.postValue(false)
                    errorMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    fun setPurchase(purchase: Purchase, value: Int,purchaseType: PurchaseItem) {
        viewModelScope.launch(Dispatchers.IO) {
            val oneTimeOfferDetails = datastore.getOneTimeOfferDetailsFlow().first()
            Log.d("FROMDBONE","${oneTimeOfferDetails?.priceAmountMicros?.div(1000000)}")

            val updatedPrice = _pricingPhase.value?.priceAmountMicros ?: oneTimeOfferDetails?.priceAmountMicros
            val updatedCurrency = _pricingPhase.value?.priceCurrencyCode ?: oneTimeOfferDetails?.priceCurrencyCode


            val price = updatedPrice?.div(1000000)?:0.0
            val currency = updatedCurrency?:"AED"

            profileRepo.updateToLocalDb(purchase, price.toLong(),currency, value)
            _processInProgress.postValue(false)
            when (val result: ResultOf<PremiumResponse> = profileRepo.updateInAppPurchase(purchase, value,purchaseType,price.toLong(),currency)) {
                is ResultOf.Success -> {
                    retryBuyFlax.postValue(false)
                    result.value.orderID?.let {
//                        _buyFlaxLoading.postValue(false)
                        successMessage.postValue(result.value.purchasedFlax)
                        profileRepo.deletePurchaseFromDB(it)
                        profileRepo.getAccountDetails()
                        datastore.clearOnetimeOfferKey()
                    }
                }

                is ResultOf.APIError -> {
                    _buyFlaxLoading.postValue(false)
                    if (retryCount > MAX_RETRY_REACHED) {
                        maxRetryReached.postValue(true)
                        _buyFlaxLoading.postValue(false)
                        retryBuyFlax.postValue(false)
                        errorMessage.postValue(result.error.message)
                    } else {
                        maxRetryReached.postValue(false)
                        _buyFlaxLoading.postValue(false)
                        retryBuyFlax.postValue(true)
                        retryCount++
                        errorMessage.postValue(result.error.message)
                    }
                }

                is ResultOf.Error -> {
                    _buyFlaxLoading.postValue(false)
                    errorMessage.postValue(result.exception.localizedMessage)
                }
            }
         setLoader(false)
        }
    }

    private suspend fun processInAppPurchasesInParallel(requests: List<SubscriptionPurchaseRequest>) {
        coroutineScope {
            val deferredResults = requests.map { request ->
                async(Dispatchers.IO) {
                    profileRepo.updateInAppPurchaseFromLocal(request)
                }
            }

            deferredResults.awaitAll().forEach { result ->
                when (result) {
                    is ResultOf.Success -> {
                        result.value.orderID?.let { orderId ->
                            _buyFlaxLoading.postValue(false)
                            retryBuyFlax.postValue(false)
                            successMessage.postValue(result.value.purchasedFlax)
                            profileRepo.deletePurchaseFromDB(orderId)
                        }
                    }

                    is ResultOf.APIError -> {
                        _buyFlaxLoading.postValue(false)
                        if (retryCount >= MAX_RETRY_REACHED) {
                            maxRetryReached.postValue(true)
                            _buyFlaxLoading.postValue(false)
                            retryBuyFlax.postValue(false)
                            errorMessage.postValue(result.error.message)
                        } else {
                            maxRetryReached.postValue(false)
                            _buyFlaxLoading.postValue(false)
                            retryBuyFlax.postValue(true)
                            retryCount++
                            errorMessage.postValue(result.error.message)
                        }
                    }

                    is ResultOf.Error -> {
                        // Handle error if needed
                        errorMessage.postValue(result.exception.localizedMessage)
                        _buyFlaxLoading.postValue(false)
                    }
                }
            }
        }
    }

    // Usage
    fun loadPendingRequestToServer() {
        _inAppPurchaseRequests.value?.let { requests ->
            viewModelScope.launch(Dispatchers.IO) {
                _buyFlaxLoading.postValue(true)
                processInAppPurchasesInParallel(requests)
                _buyFlaxLoading.postValue(false)
            }
        }
    }


    fun setPricingPhase(pricing: ProductDetails.OneTimePurchaseOfferDetails) {
        if (_processInProgress.value == true) return
        viewModelScope.launch { datastore.saveOneTimeOfferDetails(pricing) }
        _pricingPhase.postValue(pricing)
    }

    fun setProductDetails(productDetails: MutableList<ProductDetails>) {
        _playStoreProductsList.postValue(productDetails)
    }


    fun setSelectedProductDetails(productDetail: ProductDetails) {
        selectedProduct.postValue(productDetail)
    }

    fun setIsProcessInProgress(b: Boolean) {
        _processInProgress.postValue(b)
    }

    fun setLoader(buyFlax: Boolean) {
        _isLoading.postValue(buyFlax)
        Log.d("qaz","CALLED ${buyFlax}")
    }

    fun setLoading(loading: Boolean) {
        _buyFlaxLoading.postValue(loading)
    }

    fun setPurchaseType(purchaseType: PurchaseItem) {
        Log.d("PurchaseTYPEE",""+purchaseType)
        purchaseType.let {
            _purchaseItem.postValue(purchaseType)
        }


    }

    private val _currentProductSelected = MutableLiveData<BuyCoinsViewModel.FlaxRatesAndProductDetails>()
    val currentProductSelected: LiveData<BuyCoinsViewModel.FlaxRatesAndProductDetails> = _currentProductSelected.distinctUntilChanged()


    fun setPurchaseData(purchase: Purchase) {

        val input = purchase.products.first()
        val result = input.substringAfterLast("_")
        setLoader(true)
        if (input.contains("coin")) {
            setPurchaseType(PurchaseItem.BUY_COIN)
            Log.d("PurchaseDATAAA", " data value from purchase $result")
            setPurchase(purchase, result.toInt(), purchaseType = PurchaseItem.BUY_COIN)
        } else {
            setPurchaseType(PurchaseItem.BUY_FLIX)
            setPurchase(purchase, result.toInt(), purchaseType = PurchaseItem.BUY_FLIX)
        }
    }

    fun setCurrentSelectedItem(selectedItem : FlaxRatesAndProductDetails) {
        _currentlySelectedItem.value = selectedItem
    }

    fun setGiftResponse(giftResponse: GiftResponse?) {
        giftList.value = giftResponse
    }

    val calculatedNewValues: MediatorLiveData<CalculatedCoinAndFlixValues> by lazy {
        val med = MediatorLiveData<CalculatedCoinAndFlixValues>()
        med.addSource(_accountDetails) {
            calculateConvertedValues()
        }
        med.addSource(giftList) {
            calculateConvertedValues()
        }
        med
    }

    private fun calculateConvertedValues() {
        val giftData = giftList.value?.userGiftData ?: return
        val accountDetails = accountDetails.value ?: return

        val minimumFlixConversionValue = giftData.minimumFlaxConversion
        val minimumCoinConversionValue = giftData.minimumCoinConversion
        val coinToFlaxConversionValue = giftData.coinFlaxConversionValue
        val flixBalance = accountDetails.currentFlix
        val coinBalance = accountDetails.currentCoin

        val coinAmountInHundreds = if (coinBalance >= 100.0) {
            val reminder = coinBalance % 100.0
            coinBalance - reminder
        } else null

        val calculatedCoinAmount = flixBalance.takeIf { it >= minimumFlixConversionValue }?.times(coinToFlaxConversionValue)
        val calculatedFlixAmount = coinAmountInHundreds.takeIf { (it ?: 0.0) >= minimumCoinConversionValue }?.div(coinToFlaxConversionValue)

        calculatedNewValues.value = CalculatedCoinAndFlixValues(
            calculatedCoinAmount = calculatedCoinAmount,
            calculatedFlixAmount = calculatedFlixAmount,
            coinAmountInHundreds = coinAmountInHundreds,
            currentCoinAmount = coinBalance,
            currentFlixAmount = flixBalance
        )
    }

    data class CalculatedCoinAndFlixValues(
        val currentCoinAmount: Double? = null,
        val currentFlixAmount: Double? = null,
        val calculatedCoinAmount: Double? = null,
        val calculatedFlixAmount: Double? = null,
        val coinAmountInHundreds: Double? = null
    )
}