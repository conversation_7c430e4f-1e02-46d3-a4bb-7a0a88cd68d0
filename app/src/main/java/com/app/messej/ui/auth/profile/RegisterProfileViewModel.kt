package com.app.messej.ui.auth.profile

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.SetProfileRequest
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalDocumentRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.LocalDate

class RegisterProfileViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        enum class NameError {
            NONE, LT_MIN, GT_MAX
        }
        private const val NAME_MIN_LENGTH = 3
        private const val NAME_MAX_LENGTH = 50

        private const val DOB_MIN_AGE = 16
    }

    private var docRepo = LegalDocumentRepository()
    private var profileRepo = ProfileRepository(application)
    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    val policyAccepted= MutableLiveData(false)

    val name = MutableLiveData<String?>(null)
    val about = MutableLiveData<String?>(null)
    val didEnterName = MutableLiveData<Boolean>(false)

    val gender = MutableLiveData<Gender?>(null)

    private val _dateOfBirth = MutableLiveData<LocalDate?>(null)
    val dateOfBirth: LiveData<LocalDate?> = _dateOfBirth

    val dateOfBirthFormatted: LiveData<String?> = _dateOfBirth.map {
        it?.let { dt ->
            return@map DateTimeUtils.format(dt, DateTimeUtils.FORMAT_DDMMYYYY_DASHED)
        }
        return@map null
    }

    fun setDob(date: LocalDate) {
        _dateOfBirth.postValue(date)
    }

    private val _nameError: MediatorLiveData<NameError> by lazy {
        val med: MediatorLiveData<NameError> = MediatorLiveData(NameError.NONE)
        med.addSource(name) { validateName() }
        med.addSource(didEnterName) { validateName() }
        med
    }
    val nameError: LiveData<NameError> = _nameError

    private fun validateName() {
        if(didEnterName.value==true) {
            name.value.orEmpty().let {
                if (it.length < NAME_MIN_LENGTH)  _nameError.postValue(NameError.LT_MIN)
                else if (it.length > NAME_MAX_LENGTH) _nameError.postValue(NameError.GT_MAX)
                else _nameError.postValue(NameError.NONE)
            }
        } else _nameError.postValue(NameError.NONE)
    }

    val dobError: LiveData<Boolean> = _dateOfBirth.map {
        dateOfBirth.value?.let {
            if (DateTimeUtils.periodToNow(it).years < DOB_MIN_AGE) {
                return@map true
            }
        }
        return@map false
    }

    private val _profileStageValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun validateProfileStage() {
            _profileStageValid.postValue(
                name.value.orEmpty().isNotBlank() && _nameError.value == NameError.NONE &&
                        gender.value!=null &&
                        dateOfBirth.value != null && dobError.value==false &&
                        policyAccepted.value == true
            )
        }
        med.addSource(name) { validateProfileStage() }
        med.addSource(_nameError) { validateProfileStage() }
        med.addSource(gender) { validateProfileStage() }
        med.addSource(dateOfBirth) { validateProfileStage() }
        med.addSource(dobError) { validateProfileStage() }
        med.addSource(policyAccepted) { validateProfileStage() }
        med
    }
    val profileStageValid: LiveData<Boolean> = _profileStageValid

    private val _setProfileLoading = MutableLiveData(false)
    val setProfileLoading: LiveData<Boolean> = _setProfileLoading

    private val _setProfileError = MutableLiveData<String?>(null)
    val setProfileError: LiveData<String?> = _setProfileError

    val onSetProfileComplete = LiveEvent<Boolean>()

    fun setProfile() {
        _setProfileLoading.postValue(true)
        _setProfileError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            val countryCode=accountRepo.getCountryCode()?.countryCode
            if(_profileStageValid.value!=true) return@launch
            val req = SetProfileRequest(
                name = name.value!!,
                gender = gender.value!!,
                dateOfBirth = DateTimeUtils.format(dateOfBirth.value!!),
                long = accountRepo.getCountryCode()?.longitude!!,
                lat = accountRepo.getCountryCode()?.latitude!!
            )
            when(val result: ResultOf<CurrentUser> = profileRepo.setProfile(req)){
                is ResultOf.Success -> {
                    onSetProfileComplete.postValue(true)
                }
                is ResultOf.APIError -> {
                    _setProfileError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _setProfileLoading.postValue(false)
        }
    }

}