package com.app.messej.ui.auth.common

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import com.app.messej.R

import com.app.messej.data.model.api.huddles.HuddleLanguage

class HuddleLanguageDropDownAdapter(c: Context, languages: MutableList<HuddleLanguage>):
    ArrayAdapter<HuddleLanguage>(c, R.layout.item_general_dropdown, languages) {

    private val mContext = c
    private val mLanguages = languages

    private var selectedPos: Int? = null
    fun setSelectedPos(pos: Int) {
        selectedPos = pos
    }

    override fun getItem(position: Int): HuddleLanguage {
        return mLanguages[position]
    }

    override fun getCount(): Int {
        return mLanguages.size
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var cView = convertView
        if (cView == null) {
            val inflater = (mContext as Activity).layoutInflater
            cView = inflater.inflate(R.layout.item_general_dropdown, parent, false)
        }
        try {
            getItem(position).let { category ->
                cView?.apply {
                    val text: AppCompatTextView = findViewById(R.id.text)
                    text.text = category.name
                    val color = if(selectedPos == position) R.color.textColorSecondary else R.color.textColorSecondaryLight
                    text.setTextColor(ContextCompat.getColor(mContext, color))
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return cView!!
    }
}