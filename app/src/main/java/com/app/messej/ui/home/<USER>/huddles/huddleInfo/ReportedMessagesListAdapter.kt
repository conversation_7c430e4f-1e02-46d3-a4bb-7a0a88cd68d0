package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.view.LayoutInflater
import android.view.ViewGroup
import com.app.messej.data.model.entity.HuddleReportedMessage
import com.app.messej.databinding.ItemHuddleReportedMessageBinding
import com.app.messej.ui.chat.adapter.ChatAdapter

class ReportedMessagesListAdapter(
    private val inflater: LayoutInflater,
    private val userId: Int,
    private var mListener: ChatClickListener,
    private val reportListener: ReportActionListener
    ): ChatAdapter(inflater, userId, false, mListener) {

    interface ReportActionListener {
        fun onDeleteAction(item: HuddleReportedMessage, position: Int)
        fun onViewReporters(item: HuddleReportedMessage, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        return if(isOfMessageType(viewType)) {
            ReportedMessageViewHolder(ItemHuddleReportedMessageBinding.inflate(inflater, parent, false), userId, mListener, reportListener)
        }
        else super.onCreateViewHolder(parent, viewType)
    }
}