package com.app.messej.ui.home.publictab.maidan

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import com.app.messej.databinding.FragmentPodiumMaidanStatsBinding
import com.app.messej.databinding.ItemMaidanChallengeHistoryBinding
import com.app.messej.databinding.ItemMaidanCompetitorStatsBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.kennyc.view.MultiStateView

class PodiumMaidanStatsFragment : Fragment() {

    private lateinit var binding: FragmentPodiumMaidanStatsBinding
    private val viewModel: PodiumMaidanStatsViewModel by viewModels()

    private var mChallengeHistoryAdapter: BaseQuickAdapter<PodiumMaidanSupporter, BaseDataBindingHolder<ItemMaidanChallengeHistoryBinding>>? = null
    private var mCompetitorStatsAdapter: BaseQuickAdapter<PodiumMaidanSupporter, BaseDataBindingHolder<ItemMaidanCompetitorStatsBinding>>? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_maidan_stats, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        initAdapter()
        observe()
    }

    private fun setUp() {
        viewModel.getMaidanStatsSummary()

        binding.tvViewAllChallengeHistory.setOnClickListener {
            findNavController().navigateSafe(PublicMaidanFragmentDirections.actionPublicMaidanFragmentToPodiumMaidanChallengeHistoryFragment())
        }

        binding.tvViewAllCompetitorStats.setOnClickListener {
            findNavController().navigateSafe(PublicMaidanFragmentDirections.actionPublicMaidanFragmentToPodiumMaidanCompetitorStatsFragment())
        }
    }

    private fun initAdapter() {
        val participantsDiffChallengeHistory = object : DiffUtil.ItemCallback<PodiumMaidanSupporter>() {
            override fun areItemsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter) = oldItem.id == newItem.id
            override fun areContentsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter) = oldItem == newItem
        }

        mChallengeHistoryAdapter = object : BaseQuickAdapter<PodiumMaidanSupporter, BaseDataBindingHolder<ItemMaidanChallengeHistoryBinding>>(R.layout.item_maidan_challenge_history, mutableListOf()) {
            override fun convert(holder: BaseDataBindingHolder<ItemMaidanChallengeHistoryBinding>, item: PodiumMaidanSupporter) {
                holder.dataBinding?.apply {
                    speaker = item
                }
            }
        }

        binding.rvChallengeHistory.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(false)
            adapter = mChallengeHistoryAdapter
        }

        mChallengeHistoryAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(participantsDiffChallengeHistory)
        }

        val participantsDiffCompetitorStats = object : DiffUtil.ItemCallback<PodiumMaidanSupporter>() {
            override fun areItemsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter) = oldItem.id == newItem.id
            override fun areContentsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter) = oldItem == newItem
        }


        mCompetitorStatsAdapter = object : BaseQuickAdapter<PodiumMaidanSupporter, BaseDataBindingHolder<ItemMaidanCompetitorStatsBinding>>(R.layout.item_maidan_competitor_stats, mutableListOf()) {
            override fun convert(holder: BaseDataBindingHolder<ItemMaidanCompetitorStatsBinding>, item: PodiumMaidanSupporter) {
                holder.dataBinding?.apply {
                    speaker = item
                }
            }
        }

        binding.rvCompetitorStats.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(false)
            adapter = mCompetitorStatsAdapter
        }

        mCompetitorStatsAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(participantsDiffCompetitorStats)
        }
    }

    private fun observe() {

        viewModel.challengeHistory.observe(viewLifecycleOwner) {

            mChallengeHistoryAdapter?.apply {
                if (data.size == 0 || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        viewModel.competitorStats.observe(viewLifecycleOwner) {
            mCompetitorStatsAdapter?.apply {
                if (data.size == 0 || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        viewModel.maidanStatsLoading.observe(viewLifecycleOwner) {
            if (it) {
                binding.multiStateViewMaidanStats.viewState = MultiStateView.ViewState.LOADING
            } else {
                binding.multiStateViewMaidanStats.viewState = MultiStateView.ViewState.CONTENT
            }
        }
    }
}