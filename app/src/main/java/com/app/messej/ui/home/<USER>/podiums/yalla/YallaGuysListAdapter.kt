package com.app.messej.ui.home.publictab.podiums.yalla

import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.YallaGuysChallenge
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.databinding.ItemPodiumYallaGuysListBinding
import com.app.messej.databinding.ItemPodiumYallaGuysListThumbnailBinding
import com.app.messej.ui.utils.AvatarGenerator
import com.app.messej.ui.utils.DataFormatHelper.dpToPx
import com.bumptech.glide.Glide

class YallaGuysListAdapter(private val listener: YallaGuysListListener) : PagingDataAdapter<Ya<PERSON><PERSON>uys<PERSON>hallenge, YallaGuysListAdapter.ViewHolder>(DiffCallback) {

    abstract class YallaGuysListListener {
        abstract fun getChallengeIcon(type: ChallengeType?): Drawable?
        abstract fun getParticipantCount(data: YallaGuysChallenge): String
        abstract fun canJoin(data: YallaGuysChallenge): Boolean
        abstract fun onJoinClick(data: YallaGuysChallenge)
        abstract fun statusText(data: YallaGuysChallenge): String
    }

    inner class ViewHolder(private val binding: ItemPodiumYallaGuysListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: YallaGuysChallenge) = with(binding) {
            challenge = item
            presenter = listener
            joinButton.setOnClickListener {
                listener.onJoinClick(item)
            }
            participantThumbs.removeAllViews()
            item.participants.orEmpty().forEach { ptc ->
                val binding = DataBindingUtil.inflate<ItemPodiumYallaGuysListThumbnailBinding>(LayoutInflater.from(root.context),R.layout.item_podium_yalla_guys_list_thumbnail,participantThumbs,false)
                val avatar = AvatarGenerator.AvatarBuilder(root.context)
                    .setLabel(ptc.name.orEmpty())
                    .setAvatarSize(16.dpToPx(root.context))
                    .setTextSize(3.dpToPx(root.context))
                    .setBackgroundColor(ContextCompat.getColor(root.context,R.color.colorPrimaryLightest))
                    .setTextColor(ContextCompat.getColor(root.context,R.color.colorPrimaryDark))
                    .toSquare()
                    .build()
                Glide.with(root.context).load(ptc.thumbnail).placeholder(avatar).error(avatar).into(binding.imageView)
                participantThumbs.addView(binding.root)
            }
        }
    }

    object DiffCallback : DiffUtil.ItemCallback<YallaGuysChallenge>() {
        override fun areItemsTheSame(oldItem: YallaGuysChallenge, newItem: YallaGuysChallenge): Boolean {
            return oldItem.challengeId == newItem.challengeId
        }

        override fun areContentsTheSame(
            oldItem: YallaGuysChallenge,
            newItem: YallaGuysChallenge,
        ): Boolean {
            return oldItem == newItem
        }
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemPodiumYallaGuysListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ViewHolder(binding)
    }
}