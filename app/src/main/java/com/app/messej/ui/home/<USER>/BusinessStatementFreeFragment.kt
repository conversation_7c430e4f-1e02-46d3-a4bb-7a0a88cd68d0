package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.business.PayoutData
import com.app.messej.databinding.FragmentBusinessStatementFreeBinding
import com.app.messej.databinding.LayoutIdCardToolTipInformationBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.businesstab.operations.status.BusinessWorkStatusViewModel
import com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BusinessStatementFreeFragment: Fragment(){

    private val viewModel: BusinessStatementViewModel by viewModels()
    private val businessWithDrawViewModel: BusinessWithDrawViewModel by activityViewModels()
    private val workStatusViewModel: BusinessWorkStatusViewModel by activityViewModels()

    private lateinit var binding: FragmentBusinessStatementFreeBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_statement_free, container, false)
        binding.lifecycleOwner=viewLifecycleOwner
        binding.viewModel=viewModel
        return binding.root
    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.title_home_business_tab_statements)
        workStatusViewModel.setOperations()
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        businessWithDrawViewModel.payoutEligibility.observe(viewLifecycleOwner) {
            
            if (it?.eligibility == true && it.payoutData != null) {
                when (it.payoutData.status) {
                    PayoutData.PayoutStatus.INCOMPLETE -> {
                        findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalBusinessPointsReviewDialogFragment())
                    }

                    else -> {
                        findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalFlaxSellingConfirmationFragment())
                    }
                }
            } else if (it?.eligibility == false && it.payoutData != null) {
                findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalFlaxSellingConfirmationFragment())
            } else {
                findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalBusinessPointsReviewDialogFragment())
            }
        }
        businessWithDrawViewModel.isEligibilityLoading.observe(viewLifecycleOwner) {
            binding.buttonBusinessSellFlax.isClickable = !it
        }
        workStatusViewModel.areYouEligible.observe(viewLifecycleOwner){ eligible->
            Log.d("TAG", "Are You Eligible: $eligible")
            eligible?.let {
                binding.buttonBusinessSellFlax.isEnabled = it
            }
        }
    }

    private fun setup() {
        binding.buttonBusinessSellFlax.setOnClickListener {
            ensureInteractionAllowed {
                    businessWithDrawViewModel.eligibilityCheck()
            }
        }
    }

    fun checkEligibility(header: String?=null,body:String?=null,action:Boolean = false) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutIdCardToolTipInformationBinding>(layoutInflater, R.layout.layout_id_card_tool_tip_information, null, false)
            view.textHeader = header
            view.textBodys = body
            view.actionVisibility = action
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
            view.actionCloseOthers.setOnClickListener {
                dismiss()
            }
        }
    }
}