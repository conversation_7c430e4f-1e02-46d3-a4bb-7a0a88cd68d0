package com.app.messej.ui.home.gift.bottomSheet

import android.app.Application
import android.util.Log
import androidx.annotation.DrawableRes
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.CountryListUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class LevelUpgradationBottomSheetViewModel(application: Application) : AndroidViewModel(application) {
    private val profileRepo = ProfileRepository(getApplication())
    private val accountRepo: AccountRepository = AccountRepository(application)
    private val accountRepository = AccountRepository(getApplication())

    private val userId = MutableLiveData<Int?>(null)
    val nickNameInput = MutableLiveData("")
    val isSelf: Boolean
        get() = accountRepo.user.id==userId.value

    @DrawableRes
    var countryFlag: Int? = null

    fun setArgs(id:Int) {
        userId.value = id
        getUserProfile(id)
    }

    val user = accountRepository.user
    val accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val _profile = userId.switchMap {
        it ?: return@switchMap MutableLiveData<OtherUser?>(null)
        profileRepo.getLocalOtherUserProfile(it)
    }
    val profile: LiveData<OtherUser?> = _profile

    val isCurrentUser = userId.map { it == user.id }
    val currentUserProfile: LiveData<CurrentUser.Profile?> = accountRepo.userProfileFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)



    val userNickName: LiveData<NickName?> = userId.switchMap { userId ->
        profileRepo.getNickNamesLiveData(true, viewModelScope).map {
            return@map it.find { user -> user.userId == userId }
        }
    }

    val nickNameOrName: MediatorLiveData<String?> by lazy {
        val med = MediatorLiveData<String?>()
        fun update() {
            med.postValue(userNickName.value?.nickName ?: profile.value?.name)
        }
        med.addSource(_profile) { update() }
        med.addSource(userNickName) { update() }
        med
    }
    var _countryList = MutableLiveData<Map<String,Int>>()

    init {
        viewModelScope.launch {
            userNickName.asFlow().collect {
                nickNameInput.postValue(it?.nickName)
            }
        }
        _countryList.postValue(CountryListUtil.getCustomCountryMap())
    }
    fun getFlag(map: Map<String, Int>,flag:String?) {
        val flags = map
        Log.d("COUNTRYFLAG", "countryFlag1: $flag")
        countryFlag = if(isSelf)
            flags.get(user.countryCodeIso.toString()) else
            flags.get(flag)
    }


//    private val _animationUrl = MutableLiveData<String>()
//    val animationUrl: LiveData<String> = _animationUrl
//
//
//    private val _citizenship = MutableLiveData<UserCitizenship>()
//    val citizenship: LiveData<UserCitizenship> = _citizenship

    private fun getUserProfile(id: Int) {
//        _dataLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = profileRepo.getPublicUserDetails(id)) {
                is ResultOf.Success -> {
//                    _dataLoading.postValue(false)
                }

                is ResultOf.APIError -> {
//                    _dataLoading.postValue(false)
                }

                is ResultOf.Error -> {
//                    _dataLoading.postValue(false)
                }
            }
        }
    }
}