package com.app.messej.ui.home.publictab.postat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.PostatTab
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.databinding.FragmentPublicPostatBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.SubscriptionStatusViewModel
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.home.publictab.postat.mypostat.MyPostatListFragment
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindGiftRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindLegalAffairsPayFineButton
import com.app.messej.ui.utils.FragmentExtensions.bindMaidanToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.setBadgeNumber
import com.app.messej.ui.utils.FragmentExtensions.setupPayFineIcon
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils


class PublicPostatStandaloneFragment : PublicPostatBaseFragment(), MenuProvider {

    private lateinit var outerBinding: FragmentPublicPostatBinding

    private val homeViewModel: CommonHomeViewModel by activityViewModels()
    private val subscriptionStatusViewModel: SubscriptionStatusViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_postat, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        binding = outerBinding.layout
        binding.viewModel = viewModel
        return outerBinding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(outerBinding.appbar.toolbar, customBackButton = false)
        bindGiftRateToolbarChip(outerBinding.appbar.giftChip)
        bindFlaxRateToolbarChip(outerBinding.appbar.flaxRateChip)
        bindMaidanToolbarChip(outerBinding.appbar.maidanChip)
        bindLegalAffairsPayFineButton(outerBinding.appbar.payFineChip)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        outerBinding.interactionBanner.upgradeTitle.setOnClickListener {
            upgradeToPremium()
        }
    }
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_home_notifications,menu)
    }

    private var notificationBadge: BadgeDrawable? = null

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        notificationBadge = BadgeDrawable.create(requireContext())
        setBadgeNumber(notificationBadge,homeViewModel.unreadNotifications.value)
        notificationBadge?.apply {
            adjustForNotifications(requireContext())
            BadgeUtils.attachBadgeDrawable(this, outerBinding.appbar.toolbar, R.id.action_notifications)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_notifications -> findNavController().navigateSafe(PublicPostatStandaloneFragmentDirections.actionGlobalNotificationFragment())
            else -> return false
        }
        return true
    }

    override fun setup() {
        mPostatPagerAdapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = /*1*/ PostatTab.entries.size

            override fun createFragment(position: Int): Fragment {
               /* return PostatFullFeedFragment()*/
                val tab = PostatTab.entries[position]

                return when (tab) {
                    PostatTab.ME -> MyPostatListFragment()
                    PostatTab.STARS -> PostatStarsFeedFragment()
                    PostatTab.ALL -> PostatFullFeedFragment()
                    else -> throw Exception("Tab is UNSET. This should not happen")
                }
            }
        }
//        binding.layoutTabs.isVisible = false

        super.setup()

        outerBinding.upgradeBanner.upgradeBannerLayout.setOnClickListener {
            upgradeToPremium()
        }

        outerBinding.upgradeBanner.dismissUpgradeBannerBtn.setOnClickListener {
            homeViewModel.onDismissUpgradeBanner()
        }

        setupPromoBoard(outerBinding.promoBar)
        setupPayFineIcon(composeView = outerBinding.payFine)
    }

    private fun upgradeToPremium() {
        subscriptionStatusViewModel.isActive.value?.let { isActive ->
            when (isActive) {
                UserSubscriptionStatus.ACTIVE, UserSubscriptionStatus.EXPIRED -> {
                    findNavController().navigateSafe(
                        PublicPostatStandaloneFragmentDirections.actionGlobalAlreadySubscribedFragment(false)
                    )
                }
                else -> {
                    findNavController().navigateSafe(PublicPostatStandaloneFragmentDirections.actionGlobalUpgradePremiumFragment())
                }
            }
        }
    }

    fun observe() {
        homeViewModel.accountDetails.observe(viewLifecycleOwner) {
            outerBinding.citizenship = it?.citizenship
            outerBinding.isPremium = it?.isPremium
            outerBinding.daysLeft = it?.remainingDaysForResident
        }
        homeViewModel.didDismissUpgradeBannerToday.observe(viewLifecycleOwner) {
            outerBinding.showUpgradeBanner = !it && homeViewModel.isPremiumUser.value==false
        }
        subscriptionStatusViewModel.subscriptionLoaded.observe(viewLifecycleOwner) {
            it?.let { clickable ->
                outerBinding.upgradeBanner.clickable = clickable
                outerBinding.interactionBanner.clickable = clickable
            }
        }
     }

}
