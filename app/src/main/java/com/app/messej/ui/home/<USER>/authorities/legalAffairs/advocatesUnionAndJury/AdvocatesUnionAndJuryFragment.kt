package com.app.messej.ui.home.publictab.authorities.legalAffairs.advocatesUnionAndJury

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.AdvocatesUnionFilter
import com.app.messej.data.model.enums.JuryFilter
import com.app.messej.databinding.FragmentAdvocatesUnionBinding
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils
import com.app.messej.ui.home.publictab.authorities.legalAffairs.CaseDetailsBottomSheet.Companion.CASE_DETAIL_REQUEST_KEY
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsFragmentDirections
import com.app.messej.ui.home.publictab.authorities.legalAffairs.violations.LegalAffairsCaseListAdapter
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils

class AdvocatesUnionAndJuryFragment : Fragment() {

    private lateinit var binding: FragmentAdvocatesUnionBinding
    private lateinit var mAdapter: LegalAffairsCaseListAdapter
    private val viewModel: AdvocatesUnionViewModel by viewModels()
    private var isAdvocatesUnionView: Boolean = false
    private var isJuryClosedCaseView: Boolean = false
    private var isAdvocateUnionDefendView: Boolean = false

    companion object {
        private const val IS_ADVOCATES_UNION_VIEW = "is_advocates_union_view"
        private const val IS_JURY_CLOSED_CASE_VIEW = "is_jury_closed_view"
        private const val IS_ADVOCATE_UNION_DEFEND_VIEW = "is_advocate_union_defend_view"
        fun setArguments(isAdvocatesUnionView: Boolean, isJuryClosedCaseView: Boolean = false, isAdvocateUnionDefendView: Boolean = false): Bundle {
            return Bundle().apply {
                putBoolean(IS_ADVOCATES_UNION_VIEW, isAdvocatesUnionView)
                putBoolean(IS_JURY_CLOSED_CASE_VIEW, isJuryClosedCaseView)
                putBoolean(IS_ADVOCATE_UNION_DEFEND_VIEW, isAdvocateUnionDefendView)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_advocates_union, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addAsMenuHost()
    }

    private fun setup() {
        isAdvocatesUnionView = arguments?.getBoolean(IS_ADVOCATES_UNION_VIEW) == true
        isJuryClosedCaseView = arguments?.getBoolean(IS_JURY_CLOSED_CASE_VIEW) == true
        isAdvocateUnionDefendView = arguments?.getBoolean(IS_ADVOCATE_UNION_DEFEND_VIEW) == true
        if (isJuryClosedCaseView) { viewModel.setJuryFilter(filter = JuryFilter.ClosedCases) }
        else if (isAdvocateUnionDefendView) { viewModel.setAdvocatesUnionFilter(filter = AdvocatesUnionFilter.Defended) }
        initAdapter()
        observe()
        binding.filter.setOnClickListener {
            showPopupMenu(it)
        }
    }

    private fun initAdapter() {
        mAdapter = LegalAffairsCaseListAdapter(
            listener = object : LegalAffairsCaseListAdapter.ItemClickListener {
                override fun getUserId() = viewModel.user.id
                override fun showStatus(): Boolean = isAdvocatesUnionView
                override fun onClick(case: LegalRecordsResponse.ReportCase) {
                    findNavController().navigateSafe(LegalAffairsFragmentDirections.actionLegalAffairsFragmentToCaseDetailBottomSheet(case.id))
                }

                override fun displayMode(): LegalAffairsCaseListAdapter.DisplayMode {
                    return if (isAdvocatesUnionView) LegalAffairsCaseListAdapter.DisplayMode.USER
                    else LegalAffairsCaseListAdapter.DisplayMode.CASE
                }
        })

        val linearLayoutManager = LinearLayoutManager(context)
        binding.recyclerView.apply {
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }

        mAdapter.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                //Added post delayed because,
                // there is flickering of list when moving from "EMPTY" to "CONTENT" state
                binding.multiStateView.postDelayed({
                    binding.multiStateView.viewState = state
                }, 50)
            }
        }

        AuthoritiesUtils.setupListEmptyView(
            multiStateView = binding.multiStateView,
            image = if (isAdvocatesUnionView) R.drawable.ic_advocates_union_empty_view
            else R.drawable.ic_jury_empty_view
        )
    }


    private fun observe() {
        if (isAdvocatesUnionView) {
            viewModel.advocatesUnionList.observe(viewLifecycleOwner) {
                mAdapter.submitData(viewLifecycleOwner.lifecycle, it)
            }
            return
        }

        viewModel.juryList.observe(viewLifecycleOwner) {
            mAdapter.submitData(viewLifecycleOwner.lifecycle, it)
        }

        setFragmentResultListenerOnActivity(CASE_DETAIL_REQUEST_KEY) { _, _ ->
            mAdapter.refresh()
        }

        if (!isAdvocatesUnionView) {
            viewModel.juryFilter.observe(viewLifecycleOwner) {
                binding.textViewCases.text = getString(
                    when(it) {
                        JuryFilter.OpenCases -> R.string.legal_affairs_open_cases
                        JuryFilter.ClosedCases -> R.string.legal_affairs_closed_cases
                        null -> R.string.legal_affairs_cases
                    }
                )
            }
        }

    }

    private fun showPopupMenu(view: View) {
        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(if (isAdvocatesUnionView) R.menu.menu_advocates_union else R.menu.menu_jury_filter, popup.menu)

        popup.setOnMenuItemClickListener { item: MenuItem ->
            when(item.itemId) {
                //Advocates Menu Click
                R.id.defended -> { viewModel.setAdvocatesUnionFilter(filter = AdvocatesUnionFilter.Defended) }
                R.id.in_jury -> { viewModel.setAdvocatesUnionFilter(filter = AdvocatesUnionFilter.InJury) }
                R.id.no_advocates -> { viewModel.setAdvocatesUnionFilter(filter = null) }

                // Jury Menu Click
                R.id.jury_open_cases -> viewModel.setJuryFilter(filter = JuryFilter.OpenCases)
                R.id.jury_closed_cases -> viewModel.setJuryFilter(filter = JuryFilter.ClosedCases)
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

}