package com.app.messej.ui.home.publictab.huddles.chat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.ui.PlayerView
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.paging.CombinedLoadStates
import androidx.paging.LoadState
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.databinding.FragmentHuddleMessagesSearchBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.chat.BaseChatDisplayFragment
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.bumptech.glide.Glide
import com.kennyc.view.MultiStateView
import com.makeramen.roundedimageview.RoundedImageView
import com.stfalcon.imageviewer.StfalconImageViewer
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch


class HuddleMessagesSearchFragment : BaseChatDisplayFragment(),HuddleInnerSearchAdapter.HuddleInnerSearchClickListener {
    private lateinit var binding:FragmentHuddleMessagesSearchBinding
    override val viewModel: HuddleMessagesSearchViewModel by viewModels()
    private val args: HuddleMessagesSearchFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_messages_search, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                setNavigationIcon(R.drawable.ic_close)
                setNavigationIconTint(R.attr.toolbarTextColor)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }
    private fun setup() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            image = R.drawable.im_eds_chat,
            message = R.string.chat_huddle_search_eds
        )
        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                viewModel.setViewState(state)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
        binding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
            }
        }
        viewModel.setArgs(args.huddleId)
        binding.customActionBar.apply {
            keyword = viewModel.searchKeyword
            showKeyboard(searchBox)
        }
    }

    private fun observe() {
        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            it?.let { vs ->
                binding.multiStateView.viewState = vs
            }
        }
    }


    override val chatRecyclerView: RecyclerView
        get() = binding.messagesList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView
        get() = binding.multiStateView

    override val reversedLayout: Boolean
        get() = false
    override val provideAdapter: ChatAdapter
        get() = HuddleInnerSearchAdapter(layoutInflater, viewModel.user.id, this,viewModel.user.citizenship)

    override fun onResume() {
        super.onResume()
        if ((mAdapter?.itemCount ?: 0) > 0) {
            mAdapter?.refresh()
        }
    }

    override fun processLoadStates(loadState: CombinedLoadStates) {
        super.processLoadStates(loadState)
        if (loadState.refresh !is LoadState.Loading) {
            binding.swipeRefresh.isRefreshing = false
        }
        if (loadState.refresh is LoadState.Loading) {
            binding.swipeRefresh.isRefreshing = true
        }
    }

    override fun onPostClick(msg: HuddleChatMessage) {
        val options = NavOptions.Builder()
            .setPopUpTo(R.id.nav_chat_huddle, inclusive = true)
            .build()
        val action = (if(msg.senderDetails?.premium==true) msg.messageId else null)?.let { HuddleMessagesSearchFragmentDirections.actionHuddleMessagesSearchFragmentToHuddlePostCommentsFragment(it, msg.huddleIdInt) }
        if (action != null) {
            findNavController().navigateSafe(action,options)
        }
    }

    override fun onImageTap(view: View,message: HuddleChatMessage) {
        message.mediaMeta?.thumbnail?.let { media ->
            StfalconImageViewer
                    .Builder(context, listOf(media)) { imageView, image ->
                        Glide.with(requireContext())
                            .load(image)
                            .into(imageView)
                    }
                    .withTransitionFrom(view as RoundedImageView)
                    .withHiddenStatusBar(false)
                    .show()
            }
    }

    override fun onUpgradeClick() { }

    override fun onFollowClick(msg: HuddleChatMessage) { }

    override fun onMessageOptionsClick(msg: HuddleChatMessageWithMedia, position: Int, view: View) {

    }

    override fun onClickOnMention(user: MentionedUser) {


    }

    override fun onGiftClick(msg: HuddleChatMessage) {

    }

    override fun goToGiftFile(isSelf: Boolean) {

    }

    override fun goToIdCard(msg: HuddleChatMessage) {
    }

    override fun onItemLongClick(item: AbstractChatMessage, position: Int) { }

    private var streamPrepareJob: Job? = null

    override fun onViewHolderCleanup(messageId: String) {
        super.onViewHolderCleanup(messageId)
        streamPrepareJob?.cancel()
        streamPrepareJob = null
    }

    override fun onStreamMedia(view: PlayerView, msg: AbstractChatMessageWithMedia, position: Int): Boolean {
        streamPrepareJob?.let {
            it.cancel()
            streamPrepareJob = null
        }
        streamPrepareJob = viewLifecycleOwner.lifecycleScope.launch {
            try {
                val url = viewModel.getVideoStreamingURL(msg, position) ?: return@launch
                if (!isActive) return@launch
                val media = MediaItem.fromUri(url)
                viewModel.onStreamingVideo(msg, url, position)
                playVideo(view, media)
            } finally {
                streamPrepareJob = null
            }
        }
        return true
    }

}
