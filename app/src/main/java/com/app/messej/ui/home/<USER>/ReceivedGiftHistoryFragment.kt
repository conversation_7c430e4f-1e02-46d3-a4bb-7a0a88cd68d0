package com.app.messej.ui.home.gift

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.paging.LoadState
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.gift.Sender
import com.app.messej.databinding.FragmentReceivedGiftHistoryBinding
import com.app.messej.databinding.ItemGiftReceivedHistoryLayoutBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class ReceivedGiftHistoryFragment : Fragment() {
    private lateinit var binding: FragmentReceivedGiftHistoryBinding
    private val viewModel: ReceivedGiftHistoryViewModel by viewModels()
    private val args: ReceivedGiftHistoryFragmentArgs by navArgs()

    private var receivedAdapter: GiftReceivedHistoryPagerAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_received_gift_history, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.title_gift_received_history)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        viewModel.setArgs(args.giftId)
        initAdapter()
    }

    private fun observe() {
        viewModel.giftReceivedValue.observe(viewLifecycleOwner) {
            receivedAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun initAdapter() {
        receivedAdapter = GiftReceivedHistoryPagerAdapter(object : GiftReceivedHistoryPagerAdapter.UserActionListener {
            override fun navigateToIdCard(receiverId: Int) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(receiverId))
            }

        })

        val layoutManParticipant = LinearLayoutManager(context)

        binding.receivedGiftHistoryList.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = receivedAdapter
        }

        receivedAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
//            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.no_gift_received_found)
        }
    }

    class GiftReceivedHistoryPagerAdapter(private val listener: UserActionListener) : PagingDataAdapter<Sender, GiftReceivedHistoryPagerAdapter.GiftReceivedHistoryViewHolder>(TransactionsDiff) {

        interface UserActionListener {
            fun navigateToIdCard(receiverId: Int)
        }

        override fun onBindViewHolder(holder: GiftReceivedHistoryViewHolder, position: Int) {
            getItem(position)?.let { holder.bind(it) }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = GiftReceivedHistoryViewHolder(
            ItemGiftReceivedHistoryLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        )

        inner class GiftReceivedHistoryViewHolder(private val binding: ItemGiftReceivedHistoryLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
            fun bind(item: Sender) = with(binding) {
                binding.apply {
                    giftReceivedHistory = item
                    imagePersonPhoto.setOnClickListener {
                        listener.navigateToIdCard(item.senderId)
                    }
                }
            }
        }

        object TransactionsDiff : DiffUtil.ItemCallback<Sender>() {
            override fun areItemsTheSame(oldItem: Sender, newItem: Sender) = oldItem.id == newItem.id

            override fun areContentsTheSame(oldItem: Sender, newItem: Sender) = oldItem == newItem
        }

    }
}