package com.app.messej.ui.home.publictab.flash.userflash

import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerFragment

class UserFlashPlayerFragment : BaseFlashPlayerFragment() {
    override val viewModel: UserFlashViewModel by navGraphViewModels(R.id.nav_flash_user)

    override fun canPlayFlashVideo(flash: FlashVideo): Boolean {
        return flash.isAvailable
    }
}