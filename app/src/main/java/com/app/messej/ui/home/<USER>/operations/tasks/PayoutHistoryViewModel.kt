package com.app.messej.ui.home.businesstab.operations.tasks

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.repository.BusinessRepository

class PayoutHistoryViewModel(application: Application) : AndroidViewModel(application) {
    val repository = BusinessRepository(application)
    val history = repository.getPayoutHistory().liveData.cachedIn(viewModelScope)
}