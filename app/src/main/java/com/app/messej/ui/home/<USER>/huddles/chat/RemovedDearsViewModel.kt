package com.app.messej.ui.home.publictab.huddles.chat

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.huddles.AddRemovedUserRequest
import com.app.messej.data.model.api.huddles.AddRemovedUserResponse
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class RemovedDearsViewModel(application: Application) : AndroidViewModel(application) {
    private val huddlesRepository = HuddlesRepository(application)
    private val _huddleID = MutableLiveData<Int?>(null)
    val huddleID: LiveData<Int?> = _huddleID

    private val _isRemoved = MutableLiveData<Boolean?>(null)
    private val isRemoved: LiveData<Boolean?> = _isRemoved

    private val isRemoveChecked: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            med.postValue(true)
        }
        med.addSource(huddleID) { update() }
        med.addSource(isRemoved) { update() }
        med
    }

    val removedDears = isRemoveChecked.switchMap {
        huddlesRepository.getRemovedDears(_huddleID.value!!).liveData.cachedIn(viewModelScope)
    }

    fun setHuddleId(huddleId: Int) {
        _huddleID.postValue(huddleId)
    }

    fun addRemovedUser(huddleId: Int, userId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            val addRemovedUserRequest = AddRemovedUserRequest(tribeId = huddleId, userId = userId)

            when (val result: ResultOf<APIResponse<AddRemovedUserResponse>> = huddlesRepository.addRemovedUser(addRemovedUserRequest)) {
                is ResultOf.Success -> {
                    _isRemoved.postValue(true)
                }


                is ResultOf.APIError -> {

                }

                is ResultOf.Error -> {
                    // Handle other errors
                }
            }
        }

    }
}