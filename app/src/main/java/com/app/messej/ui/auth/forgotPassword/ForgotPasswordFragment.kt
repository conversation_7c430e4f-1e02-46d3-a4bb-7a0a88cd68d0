package com.app.messej.ui.auth.forgotPassword

import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.databinding.FragmentForgotPasswordBinding
import com.app.messej.ui.auth.AuthOTPFragment
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ForgotPasswordFragment:Fragment(),MenuProvider {

    private var recoveryType: OTPRequestMode?=null
    private lateinit var binding: FragmentForgotPasswordBinding
    private lateinit var mLoginPagerAdapter: FragmentStateAdapter
    private val viewModel: ForgotPasswordViewModel by viewModels()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_forgot_password,container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.forgotPasswordActionbar.toolbar)
    }

    private fun setup() {
        addAsMenuHost()
        mLoginPagerAdapter = object : FragmentStateAdapter(this) {

            override fun getItemCount(): Int = 2

            override fun createFragment(position: Int): Fragment {
                // Return a NEW fragment instance in createFragment(int)
                val fragment = when (position) {
                    0 -> ForgotPasswordUserEmailFragment.newInstance(OTPRequestMode.RESET_EMAIL)
                    1 -> ForgotPasswordMobileFragment.newInstance(OTPRequestMode.RESET_MOBILE)
                    else -> throw java.lang.IllegalArgumentException("There should only be 2 tabs")
                }
                return fragment
            }
        }
        binding.forgotPasswordPager.adapter = mLoginPagerAdapter
        binding.forgotPasswordPager.isUserInputEnabled = true

        TabLayoutMediator(binding.forgotPasswordTab, binding.forgotPasswordPager) { tab, position ->
            when(position) {
                0 -> tab.text = resources.getString(R.string.forgot_password_mode_email)
                1 -> tab.text = resources.getString(R.string.forgot_password_mode_mobile)
            }
        }.attach()

       binding.forgotPasswordTab.addOnTabSelectedListener(object :TabLayout.OnTabSelectedListener{
            override fun onTabSelected(tab: TabLayout.Tab?) {

            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.position?.let { viewModel.setUnSelectedPage(it) }
            }
            override fun onTabReselected(tab: TabLayout.Tab?) {

            }
        })

        recoveryType = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getSerializable(RECOVERY_TYPE,OTPRequestMode::class.java)
        } else{
            arguments?.getSerializable(RECOVERY_TYPE)?.let {
                it as OTPRequestMode
            }
        }
        recoveryType?.let {
            if(it==OTPRequestMode.RESET_EMAIL) {
                selectEmailTab()
            }
        }?:run{
            Log.d(ForgotPasswordFragment::class.java.name,"recovery type is null")
        }

    }
    private fun observe() {
        setFragmentResultListener(AuthOTPFragment.OTP_REQUEST_KEY) { _, bundle ->
            val result = bundle.getString(AuthOTPFragment.OTP_RESULT_KEY)
            val mode = bundle.getString(AuthOTPFragment.OTP_RESULT_MODE)?.let { OTPRequestMode.valueOf(it) }?: return@setFragmentResultListener
            val countryCode=bundle.getString(AuthOTPFragment.OTP_RESULT_COUNTRY_CODE)?: return@setFragmentResultListener
            val phone=bundle.getString(AuthOTPFragment.OTP_RESULT_PHONE_NO)?: return@setFragmentResultListener
            val email=bundle.getString(AuthOTPFragment.OTP_RESULT_EMAIL)?: return@setFragmentResultListener
            if (result!= AuthOTPFragment.OTP_RESULT_SUCCESS) return@setFragmentResultListener
            when(mode) {
                OTPRequestMode.RESET_EMAIL -> {
                    findNavController().navigateSafe(ForgotPasswordFragmentDirections.actionForgotPasswordFragmentToCreatePasswordFragment(countryCode, phone, email, mode))
                }

                OTPRequestMode.RESET_MOBILE ->{
                   findNavController().navigateSafe(ForgotPasswordFragmentDirections.actionForgotPasswordFragmentToCreatePasswordFragment(countryCode, phone, email, mode))
                }
                else -> {}
            }
        }

        setFragmentResultListener(AuthOTPFragment.OTP_CHANGE_MODE){_,bundle->
            when(bundle.getString(AuthOTPFragment.OTP_RESULT_MODE)?.let { OTPRequestMode.valueOf(it) }){
               OTPRequestMode.RESET_MOBILE -> selectEmailTab()
               OTPRequestMode.RESET_EMAIL -> selectMobileTab()
                else->{}
            }
        }

        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<OTPRequestMode>(RECOVERY_TYPE)
            ?.observe(viewLifecycleOwner) {
                selectRecoveryModeTab(it)
            }
    }
    private fun selectEmailTab() {
        lifecycleScope.launch {
            delay(100)
            withContext(Dispatchers.Main){
                binding.forgotPasswordPager.setCurrentItem(0,true)
            }
        }
    }

    private fun selectMobileTab() {
        lifecycleScope.launch {
            delay(100)
            withContext(Dispatchers.Main){
                binding.forgotPasswordPager.setCurrentItem(1,true)
            }
        }
    }
    private fun selectRecoveryModeTab(OTPRequestMode: OTPRequestMode) {
        lifecycleScope.launch {
            delay(100)
            withContext(Dispatchers.Main){
                when(OTPRequestMode){
                    com.app.messej.data.model.enums.OTPRequestMode.RESET_MOBILE ->binding.forgotPasswordPager.setCurrentItem(1,true)

                    com.app.messej.data.model.enums.OTPRequestMode.RESET_EMAIL -> binding.forgotPasswordPager.setCurrentItem(0,true)

                    else -> {}
                }
            }
        }
    }
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when(menuItem.itemId){
            android.R.id.home -> {
                findNavController().popBackStack()
                return true
            }
        }
        return true
    }

    companion object
    {
        const val RECOVERY_TYPE = "recoveryType"
    }
}