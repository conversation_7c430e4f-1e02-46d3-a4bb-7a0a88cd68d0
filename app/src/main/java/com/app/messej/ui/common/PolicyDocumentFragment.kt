package com.app.messej.ui.common

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentRegisterTermsBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip

class PolicyDocumentFragment : Fragment() {

    private lateinit var binding: FragmentRegisterTermsBinding
    private val viewModel: PolicyDocumentViewModel by viewModels()
    private val documentArgs: PolicyDocumentFragmentArgs by navArgs()

    companion object {
        const val REGISTER_DOCUMENT_REQUEST_KEY = "tncKey"
        const val REGISTER_DOCUMENT_RESULT_KEY = "tncAccepted"
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_terms, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getLegalDocument(documentArgs.documentType)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolbar.apply {
            setNavigationOnClickListener {
                setFragmentResult(REGISTER_DOCUMENT_REQUEST_KEY, bundleOf(REGISTER_DOCUMENT_RESULT_KEY to false))
                findNavController().popBackStack()
            }
        }
        setPageTitle()
    }

    private fun setup() {
        setupExitWatcher()
        viewModel.setButtonVisibility(documentArgs.isButtonVisible)
        binding.acceptButton.setOnClickListener {
            setFragmentResult(REGISTER_DOCUMENT_REQUEST_KEY, bundleOf(REGISTER_DOCUMENT_RESULT_KEY to true))
            findNavController().popBackStack()
        }
    }

    private fun setupExitWatcher() {
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                setFragmentResult(REGISTER_DOCUMENT_REQUEST_KEY, bundleOf(REGISTER_DOCUMENT_RESULT_KEY to false))
                findNavController().popBackStack()
            }
        })
    }

    override fun onResume() {
        super.onResume()
    }

    private fun observe() {
        viewModel.policyData.observe(viewLifecycleOwner) {
            binding.policyWebView.visibility = View.VISIBLE
            val html = it?.legalDocument?.description
            if (!html.isNullOrEmpty()) {
                binding.policyWebView.loadData(html, "text/html; charset=utf-8", "UTF-8")
            }
        }
        viewModel.tncError.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
            binding.policyWebView.visibility = View.GONE
            binding.acceptButton.visibility = View.GONE
        }
    }

    private fun setPageTitle() {

        binding.customActionBar.toolBarTitle.text = when (documentArgs.documentType) {
            DocumentType.PRIVACY_POLICY -> getString(R.string.document_privacy_policy)
            DocumentType.TERMS_OF_USE -> getString(R.string.document_terms_of_use)
            DocumentType.COOKIES_POLICY -> getString(R.string.document_cookies_policy)
            DocumentType.COMMUNITY_GUIDE_LINES -> getString(R.string.document_community_guide_lines)
            DocumentType.HUDDLE_POLICY -> getString(R.string.settings_title_huddle_policy)
            DocumentType.GROUP_POLICY -> getString(R.string.settings_title_groups_policy)
            DocumentType.USERNAME_GUIDE_LINES -> getString(R.string.document_username_guide_lines)
            DocumentType.HOME_APP_BUSINESS -> getString(R.string.document_home_app_policy)
            DocumentType.PP_RULES_REGULATIONS -> getString(R.string.document_pp_regulation)
            DocumentType.PODIUM_POLICY->getString(R.string.settings_title_podium_policy)
            DocumentType.HUDDLE_SELLING_PROCEDURE->getString(R.string.document_huddle_selling_procedure)
            DocumentType.GIFT_POLICY -> getString(R.string.settings_help_flahat_gifts)
            DocumentType.FLAX_POLICY->getString(R.string.document_flax_policy)
            DocumentType.PODIUM_ABOUT_MAIDAN_CHALLENGE_DETAILS -> getString(R.string.document_about_maidan_challenge)
            DocumentType.LEGAL_AFFAIRS_ABOUT -> getString(R.string.document_about_legal_affairs)
            DocumentType.STATE_AFFAIRS_ABOUT -> getString(R.string.document_about_state_affairs)
            DocumentType.E_TRIBE_ABOUT -> getString(R.string.e_tribe_about_flashat_tribe_title)

            DocumentType.ABOUT_FLASHAT -> getString(R.string.settings_title_about_flashat)
            DocumentType.FLIX_AND_COINS -> getString(R.string.settings_title_flix_and_coins)
            DocumentType.USERS_LEVELS -> getString(R.string.settings_title_users_level)
            DocumentType.USERS_STRENGTH ->getString( R.string.settings_title_users_strength)
            DocumentType.PERSONAL_DATA ->getString(R.string.settings_title_personal_data)
            DocumentType.ID_CARD -> getString(R.string.settings_title_id_card)

            DocumentType.ABOUT_BUDDIES ->getString( R.string.settings_title_about_buddies)
            DocumentType.ABOUT_INTRUDERS ->getString( R.string.settings_title_about_intruders)

            DocumentType.ABOUT_FLASH ->getString(R.string.settings_title_about_flash)
            DocumentType.ABOUT_POSTAT ->getString( R.string.settings_title_about_postat)

            DocumentType.ABOUT_TASKS->getString(R.string.settings_title_about_tasks)
            DocumentType.ABOUT_DEALS -> getString(R.string.settings_title_about_deals)
            DocumentType.ABOUT_STATEMENTS ->getString( R.string.settings_title_about_statements)
            else -> ""
        }
    }
}