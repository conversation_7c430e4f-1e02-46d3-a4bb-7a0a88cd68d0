package com.app.messej.ui.home.publictab.podiums.manage

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.databinding.ItemPodiumLiveUserBinding

class PodiumLiveUsersListAdapter(private val listener: PodiumActionListener): PagingDataAdapter<PodiumParticipant, PodiumLiveUsersListAdapter.PodiumListViewHolder>(PodiumUserDiff)  {

    interface PodiumActionListener {
        fun isNotSelf(item: PodiumParticipant): Boolean = false
        fun canInviteUser(item: PodiumParticipant): Boolean
        fun onMenuClick(view: View, item: PodiumParticipant)
        fun isInvited(item: PodiumParticipant): Boolean = false
        fun isSpeaker(item: PodiumParticipant): Boolean = false
        fun shouldShowFollowButton(item: PodiumParticipant): Boolean = false
        fun shouldShowLikesAndRating(item: PodiumParticipant): Boolean = false
        fun onFollowClick(item: PodiumParticipant)
        fun navigateToIdCard(item: PodiumParticipant)
    }

    inner class PodiumListViewHolder(private val binding: ItemPodiumLiveUserBinding): RecyclerView.ViewHolder(binding.root){
        fun bind(item: PodiumParticipant) = with(binding) {
            user = item
            showUsername = false
            isFollowed = item.isFollowed
            showUserActionMenu =  listener.isNotSelf(item)
            shouldShowFollowButton = listener.shouldShowFollowButton(item)
            showLikesAndRating = listener.shouldShowLikesAndRating(item)
            if(listener.isInvited(item)) {
                statusChip.isVisible = true
                statusChip.setText(R.string.common_invited)
            } else if(listener.isSpeaker(item)) {
                statusChip.isVisible = true
                statusChip.setText(R.string.podium_speaker)
            } else {
                statusChip.isVisible = false
            }
            usersActionButton.setOnClickListener {
                listener.onMenuClick(it,item)
            }

            userActionFollow.setOnClickListener {
                listener.onFollowClick(item)
            }
            superstarDp.setOnClickListener {
                listener.navigateToIdCard(item)
            }
        }
    }
    object PodiumUserDiff : DiffUtil.ItemCallback<PodiumParticipant>(){
        override fun areItemsTheSame(oldItem: PodiumParticipant, newItem: PodiumParticipant) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumParticipant, newItem: PodiumParticipant) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: PodiumListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumListViewHolder {
        val binding = ItemPodiumLiveUserBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumListViewHolder(binding)
    }
}