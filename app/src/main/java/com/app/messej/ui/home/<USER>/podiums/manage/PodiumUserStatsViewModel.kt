package com.app.messej.ui.home.publictab.podiums.manage

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.podium.PodiumUserLikesCoinsResponse
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PodiumUserStatsViewModel(application: Application) : AndroidViewModel(application) {

    val podiumRepository = PodiumRepository(application)

    private val _userStats = MutableLiveData<PodiumUserLikesCoinsResponse?>(null)
    val userStats: LiveData<PodiumUserLikesCoinsResponse?> = _userStats

    val selectedSpeakerLikes = _userStats.map { it?.totalLikes }

    val selectedSpeakerCoins = _userStats.map { it?.coinsReceived }

    val followStatus = _userStats.map { it?.isFollowed }

    fun setFollowed(followed: Boolean) {
        _userStats.postValue(_userStats.value?.copy(isFollowed = followed))
    }

    fun setUserAndPodium(userId: Int, podiumId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.getPodiumUserLikesCoins(podiumId, userId)) {
                is ResultOf.Success -> {
                    _userStats.postValue(result.value)
                }
                else -> {}
            }
        }
    }
}