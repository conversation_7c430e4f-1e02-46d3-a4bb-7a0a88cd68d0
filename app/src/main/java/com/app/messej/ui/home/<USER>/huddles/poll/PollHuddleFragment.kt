package com.app.messej.ui.home.publictab.huddles.poll

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.poll.Answer
import com.app.messej.data.model.enums.PollType
import com.app.messej.databinding.EndPollDialogLayoutBinding
import com.app.messej.databinding.FragmentPollBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.kennyc.view.MultiStateView

class PollHuddleFragment: Fragment() {

    private var mAdapter: HuddlePollAdapter?=null
    private lateinit var binding:FragmentPollBinding
    private val viewModel: PollViewModel by viewModels()
    private val args:PollHuddleFragmentArgs by navArgs()

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.common_polls)
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_poll, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel=viewModel
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {

        viewModel.isEditMode.observe(viewLifecycleOwner){
            it?.let {
                mAdapter?.setEditMode(it)
            }
        }

        viewModel.poll.observe(viewLifecycleOwner){
            it?.let {poll->
                mAdapter?.apply {
                    Log.d("SUG", "observe: list go ${data.size} to ${poll.answers.size}")
                    if (data.size == 0 || poll.answers.isEmpty()) {
                       setNewInstance(poll.answers.toMutableList())
                    } else {
                        setDiffNewData(poll.answers.toMutableList())
                    }
                }
            }
            }
        viewModel.isPollUpdated.observe(viewLifecycleOwner){
            it.let {
                if (it) {
                    mAdapter?.setCastVote()
                    Toast.makeText(context, getString(R.string.your_response_has_been_recorded), Toast.LENGTH_SHORT).show()
                }
            }

        }
        viewModel.isPollLoading.observe(viewLifecycleOwner){
            it?.let {
                if (it) binding.multiStateView.viewState = MultiStateView.ViewState.LOADING
                else{
                    binding.multiStateView.viewState = MultiStateView.ViewState.CONTENT
                }
            }
        }

        viewModel.isEndPollSuccess.observe(viewLifecycleOwner){
            if (it){
                findNavController().popBackStack()
            }
        }

    }

    private fun showPollMenu(view: View) {
        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(R.menu.menu_chat_poll_actions, popup.menu)
        popup.setForceShowIcon(true)
        popup.menu.apply {
            findItem(R.id.action_result_details).isVisible =args.isUserManager==true
            findItem(R.id.action_end_poll).isVisible =args.isUserManager==true
            findItem(R.id.action_send_invitations).isVisible =false
            findItem(R.id.action_hide_poll).isVisible=false
            findItem(R.id.action_edit_answer).isVisible = viewModel.isEditMode.value!=true
        }
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_end_poll ->{
                    confirmEndPoll()
                }
                R.id.action_result_details -> {
                    findNavController().navigateSafe(PollHuddleFragmentDirections.actionHuddlePollFragmentToPollResultFragment(viewModel.poll.value?.answers?.get(0)?.pollId!!))
                }
                R.id.action_edit_answer->{
                    viewModel.setEditMode(true)
                    mAdapter?.setEditMode(true)
                }
                R.id.action_send_invitations->{

                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }

        popup.show()
    }

    private fun setup() {
        viewModel.setArgs(args.huddleId,args.selectedIndex,args.isUserManager,args.isEditMode)
        initAdapter()
        binding.actionPoll.setOnClickListener {
            showPollMenu(it)
        }
    }
    private fun initAdapter() {
        mAdapter = HuddlePollAdapter(mutableListOf(),object : HuddlePollAdapter.ItemClickListener{
            override fun onClick(answer: Answer, poistion:Int) {
                viewModel.setSelectedAnswer(answer)
            }
        },args.selectedIndex,args.isEditMode,PollType.SCHEDULE_POLL)
        val layoutMan = LinearLayoutManager(context)
        binding.pollAnswers.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true
            setDiffCallback(HuddlePollAdapter.DiffCallback())
        }

    }
    private fun confirmEndPoll() {

        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<EndPollDialogLayoutBinding>(layoutInflater, R.layout.end_poll_dialog_layout, null, false)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.nickNameTitle.text = getString(R.string.poll_end_title)
            view.actionYes.setOnClickListener {
                viewModel.endPoll(viewModel.poll.value?.id!!, args.huddleId)
                dismiss()
            }
            view.actionCancel.setOnClickListener {
                dismiss()
            }
        }
    }
}