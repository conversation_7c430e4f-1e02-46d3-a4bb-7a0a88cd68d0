package com.app.messej.ui.home.publictab.authorities.legalAffairs

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import com.app.messej.R
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.databinding.FragmentBaseLegalAffairsBinding

class LegalAffairsMainBottomViewFragment : LegalAffairsBaseFragment() {

    override lateinit var commonBinding: FragmentBaseLegalAffairsBinding
    override val viewModel : LegalAffairsCommonViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        commonBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_base_legal_affairs, container, false)
        return commonBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {

    }

    override fun setupCount() {
        viewModel.myLegalRecordsCount.observe(viewLifecycleOwner) {
            commonBinding.buttonViolations.count = "${it?.violationsCount ?: 0}"
            commonBinding.buttonBan.count = "${it?.banCount ?: 0}"
            commonBinding.buttonReporting.count = "${it?.reportingCount ?: 0}"
        }
    }

    override fun setupVerticalDottedLinesVisibility(currentTab: LegalAffairTabs) {
        commonBinding.apply {
            if (<EMAIL>) {
                verticalDottedLineOne.visibility = if (currentTab == LegalAffairTabs.Violations || currentTab == LegalAffairTabs.Bans) View.INVISIBLE else View.VISIBLE
                verticalDottedLineSecond.visibility = if (currentTab == LegalAffairTabs.Bans || currentTab == LegalAffairTabs.Reporting) View.INVISIBLE else View.VISIBLE
                verticalDottedLineThird.visibility = View.GONE
            } else {
                verticalDottedLineSecond.visibility = View.GONE
                verticalDottedLineThird.visibility = View.GONE
            }
        }
    }

}