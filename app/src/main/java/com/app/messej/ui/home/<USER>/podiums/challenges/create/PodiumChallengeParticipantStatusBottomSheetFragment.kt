package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentChallengeConfourParticipantStatusBottomSheetBinding
import com.app.messej.databinding.ItemConfourParticipantStatusBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.ScaleInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach

class PodiumChallengeParticipantStatusBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private lateinit var binding: FragmentChallengeConfourParticipantStatusBottomSheetBinding
    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)
    private var mParticipantAdapter: BaseQuickAdapter<PodiumChallenge.ChallengeUser, BaseDataBindingHolder<ItemConfourParticipantStatusBinding>>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_challenge_confour_participant_status_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        observe()
    }

    fun setup() {
        viewModel.refreshPodiumDetails()
        checkAndStartTimer()
        initAdapter()
        binding.actionChooseMore.setOnClickListener {
            //ToDo need to update screen from BE also,to participation screen
            val podiumId = viewModel.podiumId.value?: return@setOnClickListener
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(podiumId, viewModel.activeChallengeId.value?.first))
        }

        binding.actionNext.setOnClickListener {
            //ToDo need to update screen from BE also, to prize distribution screen
            val podiumId = viewModel.podiumId.value ?: return@setOnClickListener
            val minReached = if (viewModel.activeChallenge.value?.minParticipantsCountReached == true) "true" else null
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(podiumId, viewModel.activeChallengeId.value?.first, minReached))
        }

    }

    fun observe() {
        viewModel.challengeParticipantsList.observe(viewLifecycleOwner){
            mParticipantAdapter?.apply {
                if (data.isEmpty() || it.isEmpty()) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
            binding.actionNext.invalidate()
        }

        viewModel.onChallengeStarted.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
    }

    private fun initAdapter() {
        mParticipantAdapter = object : BaseQuickAdapter<PodiumChallenge.ChallengeUser, BaseDataBindingHolder<ItemConfourParticipantStatusBinding>>(R.layout.item_confour_participant_status, mutableListOf()) {
            override fun convert(
                holder: BaseDataBindingHolder<ItemConfourParticipantStatusBinding>,
                item: PodiumChallenge.ChallengeUser,
            ) {
                holder.dataBinding?.apply {
                    lifecycleOwner = viewLifecycleOwner
                    user = item
                    clickable = false
                }
            }
        }.apply {
            animationEnable = true
            adapterAnimation = ScaleInAnimation()
            isAnimationFirstOnly = true
            setDiffCallback(ParticipantsDiffCallback)
        }


        binding.participantsList.apply {
            layoutManager = GridLayoutManager(context, 2)
            setHasFixedSize(true)
            adapter = mParticipantAdapter
        }
    }

    object ParticipantsDiffCallback : DiffUtil.ItemCallback<PodiumChallenge.ChallengeUser>(){
        override fun areItemsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser) = oldItem == newItem
    }

    private fun checkAndStartTimer() {
        val remainingTime = viewModel.activeChallenge.value?.participantsInvitedTimeRemaining ?: return
         if (remainingTime > 0 ){
             DateTimeUtils.countDownTimerFlow(remainingTime * 1000).onEach {
                 Log.d("timertime", "startTimerForConFourParticipantStatus: $it")
             }.onCompletion {
                 viewModel.refreshPodiumDetails()
             }.launchIn(lifecycleScope)
         } else {
             viewModel.refreshPodiumDetails()
         }
    }
}