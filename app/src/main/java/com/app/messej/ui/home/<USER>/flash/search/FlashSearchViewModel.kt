package com.app.messej.ui.home.publictab.flash.search

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.api.huddles.FlashSearchResponse
import com.app.messej.data.model.enums.FlashSearchTab
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerViewModel
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class FlashSearchViewModel(application: Application):  BaseFlashPlayerViewModel(application) {

    private val _currentTab = MutableLiveData<FlashSearchTab?>(null)
    val currentTab: LiveData<FlashSearchTab?> = _currentTab.distinctUntilChanged()

    fun setCurrentTab(tab: FlashSearchTab, skipIfSet: Boolean = false) {
        Log.w("FSVM", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
    }

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    var searchKeyword = MutableLiveData<String>("")
    private val searchTerm = MutableLiveData("")

    init {
        setCurrentTab(FlashSearchTab.TAB_FOR_YOU, true)
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                searchTerm.postValue(it.orEmpty())
            }
        }
    }
    fun resetSearch() {
        searchKeyword.postValue("")
    }

    override val _flashFeedList = searchTerm.switchMap {
        flashRepo.getFlashSearchPager(it).liveData.cachedIn(viewModelScope)
    }

    private val _flashAccountsList = searchTerm.switchMap {
        flashRepo.getFlashAccountsSearchPager(it).liveData.cachedIn((viewModelScope))
    }

    val flashAccountsList: MediatorLiveData<PagingData<FlashSearchResponse.FlashUser>> by lazy {
        val med = MediatorLiveData<PagingData<FlashSearchResponse.FlashUser>>()
        fun combine() {
            var list = _flashAccountsList.value
            val nickNames = _nickNames.value
            list = list?.map { item ->
                val name = nickNames?.find { nn -> nn.userId == item.id }?.nickName?:item.name
                item.copy(name = name)
            }
            med.postValue(list)
        }
        med.addSource(_flashAccountsList) { combine() }
        med.addSource(_nickNames) { combine() }
        med
    }

}