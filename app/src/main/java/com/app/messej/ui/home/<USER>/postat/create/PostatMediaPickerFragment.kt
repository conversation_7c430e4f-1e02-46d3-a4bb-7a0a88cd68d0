package com.app.messej.ui.home.publictab.postat.create

import android.Manifest.permission.READ_EXTERNAL_STORAGE
import android.Manifest.permission.READ_MEDIA_IMAGES
import android.Manifest.permission.READ_MEDIA_VISUAL_USER_SELECTED
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager.PERMISSION_GRANTED
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.R
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.data.model.enums.AttachmentSource
import com.app.messej.data.model.enums.PostatMediaTab
import com.app.messej.databinding.FragmentPostatMediaPickerBinding
import com.app.messej.ui.chat.ChatAttachSourceFragment
import com.app.messej.ui.chat.GroupChatFragmentDirections
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

class PostatMediaPickerFragment : Fragment(), PostatViewerViewPagerAdapter.PlayerActionListener {

    private lateinit var binding: FragmentPostatMediaPickerBinding
    val viewModel: CreatePostatViewModel by navGraphViewModels(R.id.nav_create_postat)

    val args: PostatMediaPickerFragmentArgs by navArgs()

    private lateinit var mPostatMediaPickerPagerAdapter: FragmentStateAdapter
    private lateinit var mPostatViewerViewPagerAdapter: PostatViewerViewPagerAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_media_picker, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    override fun onResume() {
        super.onResume()
        viewModel.setOnMediaPickerPage(true)
        playMediaIfReady()
    }

    override fun onPause() {
        super.onPause()
        stopMediaPlayback()
    }

    private fun setup() {

        args.postatId?.let {
            viewModel.setPostatDraft(it)
            findNavController().navigateSafe(PostatMediaPickerFragmentDirections.actionPostatMediaPickerFragmentToCreatePostatFragment())
        }
        if(viewModel.user.premium) {
            if (checkPermissionStateAndRequest()) {
                viewModel.setPermissionGranted(true)
            }
        }
         if (viewModel.user.premium) {
             binding.holder.isVisible = true
        } else {
             takeImage()
             binding.holder.isVisible = false
        }
//        setupPlayer()

        mPostatMediaPickerPagerAdapter = object: FragmentStateAdapter(this) {
            override fun getItemCount(): Int = PostatMediaTab.entries.size

            override fun createFragment(position: Int): Fragment {
                return when(position) {
                    PostatMediaTab.TAB_DRAFT.ordinal -> {
                        PostatDraftMediaFragment()
                    }
                    PostatMediaTab.TAB_RECENT.ordinal -> {
                        PostatRecentMediaFragment()
                    }
                    else -> throw IllegalStateException("Invalid position")
                }
            }
        }.apply {  }

        mPostatViewerViewPagerAdapter = PostatViewerViewPagerAdapter(this, mutableListOf()).apply {
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeRemoved(positionStart: Int, itemCount: Int) {
                    super.onItemRangeRemoved(positionStart, itemCount)
                    Log.w("POSTATF", "onItemRangeRemoved: $positionStart - ${positionStart+itemCount-1} | ${futurePlaybackObject?.get()?.pos}")
                    futurePlaybackObject?.get().let { fpo ->
                        if (fpo!=null) {
                            if (fpo.pos in positionStart until positionStart + itemCount) {
                                Log.w("POSTATF", "onItemRangeRemoved: stopping ${fpo.pos}")
                                player?.stop()
                                fpo.onStop.invoke()
                            }
                        } else stopPlayer()
                    }
                }
            })
        }
        binding.selectedMediaViewPager.apply {
            adapter = mPostatViewerViewPagerAdapter
            orientation = ViewPager2.ORIENTATION_HORIZONTAL
            currentItem = 1
            registerOnPageChangeCallback(object: ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    Log.w("POSTATF", "onPageSelected: $position")
                    futurePlaybackObject?.get().let { fpo ->
                        if (fpo!=null) {
                            if (fpo.pos != position) {
                                Log.w("POSTATF", "onPageSelected: stopping ${fpo.pos}")
                                player?.stop()
                                fpo.onStop.invoke()
                            }
                        } else stopPlayer()
                    }
                }
            })
        }

        binding.dotsIndicator.attachTo(binding.selectedMediaViewPager)



        viewModel.setCurrentTab(PostatMediaTab.TAB_RECENT)

        binding.mediaPager.apply {
            isUserInputEnabled = false
            adapter = mPostatMediaPickerPagerAdapter
        }

        binding.btnDraft.setOnClickListener {
            viewModel.setCurrentTab(PostatMediaTab.TAB_DRAFT)
            (it as MaterialButton).isChecked = true
        }

        binding.btnRecents.setOnClickListener {
            viewModel.setCurrentTab(PostatMediaTab.TAB_RECENT)
            (it as MaterialButton).isChecked = true
        }

        viewModel.currentTab.value?.let {
            binding.mediaPager.setCurrentItem(it.ordinal, false)
        }

        binding.nextButton.setOnClickListener {
            releasePlayer()
            findNavController().navigateSafe(PostatMediaPickerFragmentDirections.actionPostatMediaPickerFragmentToCreatePostatFragment())
        }

        binding.closeButton.setOnClickListener {
            releasePlayer()
            findNavController().popBackStack()
        }

        binding.btnManagePermission.setOnClickListener {
            manageUserSelectPermission()
        }

        binding.cameraButton.setOnClickListener {
//            showAttachDialog()
            takeImage()
        }

        binding.multiButton.setOnClickListener {
            viewModel.toggleSelectionMode()
        }
    }

    private fun manageUserSelectPermission() {
        requestPermissions.launch(getMediaPermissions())
    }

    private fun observer(){
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            Log.w("PHBF", "observe currentTab: $it")
            if (binding.mediaPager.currentItem==it.ordinal) return@observe
            binding.mediaPager.setCurrentItem(it.ordinal,false)
        }

        viewModel.maxMediaSelected.observe(viewLifecycleOwner) {
            if (it) Toast.makeText(requireContext(), "Maximum count is 6", Toast.LENGTH_SHORT).show()
        }

        viewModel.postatMedia.observe(viewLifecycleOwner) {
            it?: return@observe
            mPostatViewerViewPagerAdapter.updateData(it)
        }

        viewModel.onMediaRemoved.observe(viewLifecycleOwner) {
            futurePlaybackObject?.get()?.let { fpo ->
                if ((fpo.media as PostatDeviceMedia).uri == it.uri) {
                    player?.stop()
                    fpo.onStop.invoke()
                }
            }
        }

        setFragmentResultListener(ChatAttachSourceFragment.ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when(ChatAttachSourceFragment.getSource(bundle)) {
                AttachmentSource.CAMERA_PHOTO -> takeImage()
//                AttachmentSource.CAMERA_VIDEO -> takeVideo()
                else -> {}
            }
        }

        viewModel.onAddCapturedMedia.observe(viewLifecycleOwner) {
            releasePlayer()
            findNavController().navigateSafe(PostatMediaPickerFragmentDirections.actionPostatMediaPickerFragmentToCreatePostatFragment())
        }
    }

    private fun getMediaPermissions() = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
        arrayOf(READ_MEDIA_IMAGES, /*READ_MEDIA_VIDEO,*/ READ_MEDIA_VISUAL_USER_SELECTED)
    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        arrayOf(READ_MEDIA_IMAGES/*, READ_MEDIA_VIDEO*/)
    } else {
        arrayOf(READ_EXTERNAL_STORAGE)
    }

    // Register ActivityResult handler
    private val requestPermissions = registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { results ->
        // Handle permission requests results
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            val check = results.getOrDefault(READ_MEDIA_IMAGES,false) /*&& results.getValue(READ_MEDIA_VIDEO)*/ || results.getOrDefault(READ_MEDIA_VISUAL_USER_SELECTED,false)
            if (check) {
                //load image and videos
                viewModel.setPermissionGranted(true)
            }
            Log.d("POSTAT", "android 14 permission : $check")
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val check = results.getOrDefault(READ_MEDIA_IMAGES,false) /*&& results.getValue(READ_MEDIA_VIDEO)*/
            if (check) {
                //load image and videos
                viewModel.setPermissionGranted(true)
            }
            Log.d("POSTAT", "android 13 above permission : $check")
        } else {
            val check = results.getOrDefault(READ_EXTERNAL_STORAGE,false)
            if (check) {
                //load image and videos
                viewModel.setPermissionGranted(true)
            }
            Log.d("POSTAT", "other android version permission : $check")
        }
    }

    private fun checkPermissionStateAndRequest() : Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
            && (ContextCompat.checkSelfPermission(requireContext(), READ_MEDIA_IMAGES) == PERMISSION_GRANTED
                    /*|| ContextCompat.checkSelfPermission(requireContext(),READ_MEDIA_VIDEO) == PERMISSION_GRANTED*/)
        ) {
            // Full access on Android 13 (API level 33) or higher
            return true
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
            && ContextCompat.checkSelfPermission(requireContext(), READ_MEDIA_VISUAL_USER_SELECTED) == PERMISSION_GRANTED) {

            viewModel.allowUserSelectedMediaPermission()
            // Partial access on Android 14 (API level 34) or higher
            return true
        } else if (ContextCompat.checkSelfPermission(requireContext(), READ_EXTERNAL_STORAGE) == PERMISSION_GRANTED) {
            // Full access up to Android 12 (API level 32)
            return true
        } else {
            // Access denied
            requestPermissions.launch(getMediaPermissions())
            return false
        }
    }

    private var futurePlaybackObject: WeakReference<PostatViewerViewPagerAdapter.FuturePlaybackObject>? = null

    override fun registerForFuturePlayback(obj: PostatViewerViewPagerAdapter.FuturePlaybackObject) {
        futurePlaybackObject = WeakReference(obj)
        Log.w("POSTATF", "registerForFuturePlayback: ${obj.pos}")
        playMediaIfReady()
    }

    override fun detachFuturePlayback(pos: Int) {
        Log.w("POSTATF", "detachFuturePlayback: $pos | current - ${futurePlaybackObject?.get()?.pos}")
        if (futurePlaybackObject?.get()?.pos == pos) {
            stopPlayer()
        }
    }

    private fun playMediaIfReady() {
        val videoPlayer = player?: return
        fun play(fpo: PostatViewerViewPagerAdapter.FuturePlaybackObject) {
            videoPlayer.setupPlayerWithMedia((fpo.media as PostatDeviceMedia).mediaItem, viewModel.useOriginalAudio.value != true)
            fpo.onPlay.invoke(videoPlayer)
        }
        futurePlaybackObject?.get()?.also { fpo ->
            Log.w("POSTATF", "playMediaIfReady: found FPO: ${fpo.pos}")
            play(fpo)
        }?: run {
//                Log.w("POSTATF", "playMediaIfReady: trying to find current FPO")
            val vh = (binding.selectedMediaViewPager[0] as RecyclerView).findViewHolderForAdapterPosition(binding.selectedMediaViewPager.currentItem)
//                Log.w("POSTATF", "playMediaIfReady: found ViewHolder for pos ${binding.mediaViewPager.currentItem}")
            val fpo = (vh as? PostatViewerViewPagerAdapter.ViewPagerViewHolder)?.bindPlayer()?: return@run
            Log.w("POSTATF", "playMediaIfReady: found current FPO | ${fpo.pos}")
            play(fpo)
        }
    }

    private fun stopMediaPlayback() {
        futurePlaybackObject?.get()?.let { fpo ->
            Log.w("POSTATF", "stopMediaPlayback: found FPO: ${fpo.pos}")
            fpo.onStop.invoke()
        }
    }

    private var player: ExoPlayer? = null

    private fun releasePlayer() {
//        stopMediaPlayback()
//        futurePlaybackObject = null
//        player?.apply {
//            stop()
//            release()
//            player = null
//        }
    }

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    private fun setupPlayer() {
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build()
        }
        player?.apply {
            repeatMode = Player.REPEAT_MODE_ONE
            playWhenReady = false
            prepare()
        }
    }

    private fun stopPlayer() {
        futurePlaybackObject = null
        player?.stop()
    }

    private fun ExoPlayer.setupPlayerWithMedia(media: MediaItem?, mute: Boolean = false) {
        clearMediaItems()
        media?: return
        setMediaItem(media)
        prepare()
        volume = if(mute) 0f else 1f
        play()
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    private fun showAttachDialog() {
        val src = arrayOf(AttachmentSource.CAMERA_PHOTO/*,AttachmentSource.CAMERA_VIDEO*/)
        val action = GroupChatFragmentDirections.actionGlobalImageAttachSourceFragment(src)
        findNavController().navigateSafe(action)
    }

    private val takeVideoResult =
        registerForActivityResult(object: ActivityResultContracts.CaptureVideo() {
            override fun createIntent(context: Context, input: Uri): Intent {
                val intent = super.createIntent(context, input)
                intent.putExtra(MediaStore.EXTRA_DURATION_LIMIT, CreatePostatViewModel.VIDEO_DURATION_LIMIT) // Duration in Seconds
//                intent.putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 0); // Quality Low
//                intent.putExtra(MediaStore.EXTRA_SIZE_LIMIT, 5491520L); // 5MB
                return intent
            }
        }) { isSuccess ->
            if (isSuccess) {
                viewModel.addCapturedVideo()
            }
        }
    private fun takeVideo() {
        checkCameraPermission(binding.root) {
            lifecycleScope.launch {
                val uri = viewModel.getVideoUriForCapture()
                takeVideoResult.launch(uri)
            }
        }
    }

    private val takeImageResult =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { isSuccess ->
            if (isSuccess) {
                viewModel.addCapturedImage()
            }
            if(!viewModel.user.premium){
                if (isSuccess){
                    findNavController().navigateSafe(PostatMediaPickerFragmentDirections.actionPostatMediaPickerFragmentToCreatePostatFragment())
                }else{
                    findNavController().popBackStack()
                }
            }
        }

    private fun takeImage() {
        checkCameraPermission(binding.root) {
            lifecycleScope.launch {
                val uri = viewModel.getImageUriForCapture()
                takeImageResult.launch(uri)
            }
        }
    }
}