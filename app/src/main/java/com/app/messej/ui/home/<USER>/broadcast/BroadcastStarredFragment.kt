package com.app.messej.ui.home.publictab.broadcast

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.core.os.bundleOf
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.NavChatBroadcastDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.enums.BroadcastAction
import com.app.messej.databinding.FragmentBroadcastStarredBinding
import com.app.messej.ui.chat.BaseChatDisplayFragment
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.home.publictab.common.BroadcastStarredChatAdapter
import com.app.messej.ui.home.publictab.common.BroadcastStarredViewModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class BroadcastStarredFragment : BaseChatDisplayFragment(), MenuProvider {

    companion object {
        const val SELECTED_MESSAGE_KEY = "select_message"
    }

    private lateinit var binding: FragmentBroadcastStarredBinding

    private val args: BroadcastStarredFragmentArgs by navArgs()

    override val viewModel: BroadcastStarredViewModel by navGraphViewModels(R.id.nav_chat_broadcast)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_broadcast_starred, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.chatList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView?
        get() = null

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(binding.customActionBar.toolbar)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_broadcast_starred_options, menu)
        menu.findItem((R.id.action_unstar_all)).apply {
            isVisible = mAdapter?.snapshot()?.isEmpty()==false
            actionView?.setOnClickListener {
                viewModel.unstarAll()
            }
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem) = false

    private fun setup() {
        viewModel.setBroadcastMode(args.mode)

        mAdapter?.addOnPagesUpdatedListener {
            (activity as MenuHost).invalidateMenu()
        }
    }

    private fun observe() {
        viewModel.canForwardSelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_forward)?.isVisible = it
        }
        viewModel.actionIsStar.observe(viewLifecycleOwner) { star ->
            actionMode?.menu?.findItem(R.id.action_star)?.setIcon(if(star) R.drawable.ic_star else R.drawable.ic_unstar)
        }
        viewModel.onStarAction.observe(viewLifecycleOwner){
            when(it){
                BroadcastAction.STAR -> Toast.makeText(requireContext(), resources.getString(R.string.broadcast_toast_starred), Toast.LENGTH_SHORT).show()
                BroadcastAction.UNSTAR -> Toast.makeText(requireContext(), resources.getString(R.string.broadcast_toast_unstarred), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onMessageForwarded.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), R.string.broadcast_forward_success, Toast.LENGTH_SHORT).show()
            if (it.size==1) {
                val action = NavChatBroadcastDirections.actionNavigationChatBroadcastSelf(it[0], null)
                findNavController().navigateSafe(action)
            }
        }

        viewModel.onCannotForward.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(),R.string.broadcast_forward_media_not_offline,Toast.LENGTH_SHORT).show()
        }

        viewModel.broadcasterImage.observe(viewLifecycleOwner) {
            (mAdapter as BroadcastStarredChatAdapter).setBroadCasterImage(it)
        }

        setFragmentResultListener(BroadcastForwardFragment.FORWARD_MODES_KEY) { _, bundle ->
            val modes = BroadcastForwardFragment.parseBundle(bundle)
            viewModel.confirmForward(modes)
        }
    }

    override val provideAdapter: ChatAdapter
        get() = BroadcastStarredChatAdapter(layoutInflater, viewModel.user.id, this)

    override fun onItemClick(item: AbstractChatMessage, position: Int): Boolean {
        if (viewModel.selectMessage(item,position)) return true
        findNavController().popBackStack()
        setFragmentResult(SELECTED_MESSAGE_KEY, bundleOf(SELECTED_MESSAGE_KEY to item.messageId))
        return true
    }

    override fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_broadcast_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_star -> viewModel.toggleStar()
                        R.id.action_forward -> {
                            val action = BroadcastStarredFragmentDirections.actionStarredBroadcastFragmentToBroadcastForwardFragment()
                            findNavController().navigateSafe(action)
                        }
                        R.id.action_copy -> viewModel.copySelection()
                        R.id.action_delete -> confirmDelete(R.string.broadcast_delete_confirm_title, R.string.broadcast_delete_confirm_message, true,
                                                            viewModel.canDeleteSelectionForEveryone.value == true) {
                            viewModel.deleteSelection(it)
                        }
                        else -> return false
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }
}