package com.app.messej.ui.home.publictab.huddles.chat

import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.ItemChatMessageHuddleBinding
import com.app.messej.ui.chat.ChatMessageUIModel

open class HuddleInnerSearchAdapter(
    private val inflater: LayoutInflater,
    private val userId: Int,
    private var mListener: HuddleInnerSearchClickListener,
    private var userCitizenship : UserCitizenship?
):HuddleChatAdapter(inflater, userId, mListener, userCitizenship) {

    interface HuddleInnerSearchClickListener: <PERSON>ddleChatClickListener {
        fun onPostClick(msg: <PERSON>ddleChatMessage)
        fun onImageTap(view: View,msg: HuddleChatMessage)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        return if (isOfMessageType(viewType)) {
            HuddleInnerSearchViewHolder(ItemChatMessageHuddleBinding.inflate(inflater, parent, false), userId, mListener,userCitizenship)
        } else super.onCreateViewHolder(parent, viewType)
    }


    class HuddleInnerSearchViewHolder(binding: ItemChatMessageHuddleBinding, userId: Int, private val mListener: HuddleInnerSearchClickListener,private var userCitizenship : UserCitizenship?) : HuddleMessageViewHolder(binding, userId, mListener, userCitizenship) {

        override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
            super.bind(item)
            val hcm = (item as ChatMessageUIModel.ChatMessageModel).message as HuddleChatMessage
            chatActions.isVisible = false
            commentsLayout.isEnabled = false
            commentsLayout.alpha = 0.6f
//            replyIcon.isEnabled = false
//            likeButton.isEnabled = false
            showUpgrade = false
            showFollow = false
            chatBubble.apply {
                isClickable = true
                with(TypedValue()) {
                    root.context.theme.resolveAttribute(android.R.attr.selectableItemBackgroundBorderless, this, true)
                    foreground = ContextCompat.getDrawable(root.context, resourceId)
                }
                setOnClickListener {
                    mListener.onPostClick(hcm)
                }
                imageBinding?.chatImage?.setOnClickListener{
                    mListener.onImageTap(it,hcm)
                }
            }
        }

    }
}