package com.app.messej.ui.home.publictab.myETribe

import androidx.annotation.ColorRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.data.model.enums.ETribeContactOptions
import com.app.messej.data.model.enums.ETribeTabs
import com.app.messej.data.model.enums.UserCitizenship

@Composable
fun Modifier.setupCitizenshipTextBackground(citizenShip: UserCitizenship): Modifier {
    val backgroundColor = when (citizenShip) {
        // Not returning single color because president have gradient color
        UserCitizenship.VISITOR -> List(2) { (colorResource(R.color.textColorOnSecondary)) }
        UserCitizenship.RESIDENT -> List(2) { (colorResource(R.color.colorPodiumSpeakerResident)) }
        UserCitizenship.CITIZEN -> List(2) { (colorResource(R.color.colorPodiumSpeakerCitizen)) }
        UserCitizenship.OFFICER -> List(2) { (colorResource(R.color.colorPodiumSpeakerOfficer)) }
        UserCitizenship.AMBASSADOR -> List(2) { (colorResource(R.color.colorPodiumSpeakerAmbassador)) }
        UserCitizenship.MINISTER -> List(2) { colorResource(R.color.colorPrimary) }
        UserCitizenship.PRESIDENT -> listOf(Color(0xFFA37A1E), Color(0xFFE6BE69), Color(0xFF956E13))
        UserCitizenship.GOLDEN -> listOf(Color(0xFFA37A1E), Color(0xFFE6BE69), Color(0xFF956E13))
    }
    return background(
        brush = Brush.linearGradient(colors = backgroundColor), shape = RoundedCornerShape(size = 3.dp)
    )
}

object ETribeUtil {

    @ColorRes
    fun UserCitizenship.setupETribeCitizenshipTextColor() : Int {
        return when(this) {
            UserCitizenship.VISITOR -> R.color.colorAlwaysLightSurfaceSecondaryDarker
            UserCitizenship.RESIDENT -> R.color.textColorOnPrimary
            UserCitizenship.CITIZEN -> R.color.colorPrimaryColorDarkest1
            UserCitizenship.OFFICER -> R.color.colorPrimaryColorDarkest1
            UserCitizenship.AMBASSADOR -> R.color.colorPrimaryColorDarkest1
            UserCitizenship.MINISTER -> R.color.textColorOnPrimary
            UserCitizenship.PRESIDENT -> R.color.colorPrimary
            UserCitizenship.GOLDEN -> R.color.white
        }
    }

    @StringRes
    fun ETribeTabs.setupText() : Int {
        return when(this) {
            ETribeTabs.All -> R.string.flash_tab_all
            ETribeTabs.Active -> R.string.common_active
            ETribeTabs.Inactive -> R.string.user_citizenship_inactive
        }
    }

    @StringRes
    fun ETribeContactOptions.setupText() : Int {
        return when(this) {
            ETribeContactOptions.Popup -> R.string.e_tribe_contact_options_popum
            ETribeContactOptions.Notification -> R.string.e_tribe_contact_options_notification
            ETribeContactOptions.PrivateMessage -> R.string.e_tribe_contact_options_private_message
            ETribeContactOptions.Email -> R.string.e_tribe_contact_options_email
        }
    }
}