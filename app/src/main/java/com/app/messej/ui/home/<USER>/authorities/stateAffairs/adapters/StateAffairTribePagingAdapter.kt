package com.app.messej.ui.home.publictab.authorities.stateAffairs.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.databinding.ItemStateAffairTribeLayoutBinding
import com.app.messej.ui.home.publictab.authorities.stateAffairs.UserStateAffair

class StateAffairTribePagingAdapter(private val isLargeScreen: Boolean? = false,private val listener: stateAffairActionListener) : PagingDataAdapter<UserStateAffair, TribeViewHolder>(TransactionsDiff) {

    interface stateAffairActionListener{
        fun onProfileClick(item: UserStateAffair)
    }

    override fun onBindViewHolder(holder: TribeViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it,isLargeScreen,listener) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TribeViewHolder {
        val binding = ItemStateAffairTribeLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return TribeViewHolder(binding)
        }
    }


    class TribeViewHolder(private val binding: ItemStateAffairTribeLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: UserStateAffair, isLargeScreen: Boolean?,listener:StateAffairTribePagingAdapter.stateAffairActionListener) {
            binding.stateAffair = item

            val layoutParams = binding.cardLayout.layoutParams
            layoutParams.width = if (isLargeScreen == false)  300  else ViewGroup.LayoutParams.MATCH_PARENT
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
            binding.cardLayout.layoutParams = layoutParams
            binding.cardLayout.setOnClickListener {
//                listener.onProfileClick(item)
            }

        }
    }



object TransactionsDiff : DiffUtil.ItemCallback<UserStateAffair>() {
    override fun areItemsTheSame(oldItem: UserStateAffair, newItem: UserStateAffair) = oldItem.userId == newItem.userId

    override fun areContentsTheSame(oldItem: UserStateAffair, newItem: UserStateAffair) = oldItem == newItem
}

