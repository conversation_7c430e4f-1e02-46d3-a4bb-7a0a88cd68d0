package com.app.messej.ui.home.privatetab.messages

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.huddles.PrivateMessagesSuggestionResponse
import com.app.messej.databinding.ItemPrivateMessageSearchRecommendedHeaderBinding
import com.app.messej.databinding.ItemUserWithActionStatsBinding
import com.app.messej.ui.home.privatetab.messages.PrivateMessageSearchListAdapter.PrivateMessagesSearchUIModel.RecommendationModel
import com.app.messej.ui.home.privatetab.messages.PrivateMessageSearchListAdapter.PrivateMessagesSearchUIModel.UserModel

class PrivateMessageSearchListAdapter(private val inflater: LayoutInflater, private val userId: Int, private val mListener: ItemListener): PagingDataAdapter<PrivateMessageSearchListAdapter.PrivateMessagesSearchUIModel, PrivateMessageSearchListAdapter.PrivateMessagesSearchListViewHolder>(
    SearchDiff
) {

    sealed class PrivateMessagesSearchUIModel {
        object RecommendationModel : PrivateMessagesSearchUIModel()
        class UserModel(val user: PrivateMessagesSuggestionResponse.User) : PrivateMessagesSearchUIModel()
    }

    companion object {
        const val ITEM_USER = 1
        const val ITEM_RECOMMENDATION = 5
    }

    interface ItemListener {
        fun onItemClick(item: PrivateMessagesSuggestionResponse.User)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        when(viewType){
        ITEM_RECOMMENDATION -> RecommendedTextViewHolder(ItemPrivateMessageSearchRecommendedHeaderBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        ITEM_USER -> PrivateMessageViewHolder(ItemUserWithActionStatsBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        else -> throw IllegalStateException("Unknown view type")
    }

    override fun onBindViewHolder(holder: PrivateMessagesSearchListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it,position) }
    }

    abstract inner class PrivateMessagesSearchListViewHolder(view: View): RecyclerView.ViewHolder(view) {
        abstract fun bind(item: PrivateMessagesSearchUIModel, position: Int)
    }

    override fun getItemViewType(position: Int): Int {
        return when(peek(position)) {
            is RecommendationModel -> ITEM_RECOMMENDATION
            is UserModel -> ITEM_USER
            null -> throw IllegalStateException("Unknown view")
        }
    }

    inner class PrivateMessageViewHolder(private val binding: ItemUserWithActionStatsBinding) : PrivateMessagesSearchListViewHolder(binding.root) {
        override fun bind(item: PrivateMessagesSearchUIModel, position: Int) = with(binding) {
            val data  = (item as UserModel).user
            user = data
            clickable = true
            showUsername = true
            root.setOnClickListener { mListener.onItemClick(data) }
        }
    }

    inner class RecommendedTextViewHolder(private val binding: ItemPrivateMessageSearchRecommendedHeaderBinding) : PrivateMessagesSearchListViewHolder(binding.root) {
        override fun bind(item: PrivateMessagesSearchUIModel, position: Int) = with(binding) {

        }
    }

    object SearchDiff : DiffUtil.ItemCallback<PrivateMessagesSearchUIModel>() {
        override fun areItemsTheSame(oldItem: PrivateMessagesSearchUIModel, newItem: PrivateMessagesSearchUIModel): Boolean {
            return if (oldItem is UserModel && newItem is UserModel) {
                oldItem.user.id == newItem.user.id
            } else oldItem is RecommendationModel && newItem is RecommendationModel
        }

        override fun areContentsTheSame(oldItem: PrivateMessagesSearchUIModel, newItem: PrivateMessagesSearchUIModel) =
            oldItem == newItem
    }
}