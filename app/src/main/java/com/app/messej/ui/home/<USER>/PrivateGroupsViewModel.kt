package com.app.messej.ui.home.privatetab

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.enums.HuddleAction
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.common.BaseHuddleListViewModel
import com.app.messej.ui.home.publictab.huddles.PublicHuddlesAdapter.HuddleUIModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

class PrivateGroupsViewModel(application: Application) : BaseHuddleListViewModel(application) {
    init {
        huddleType.postValue(HuddleType.PRIVATE)
        viewModelScope.launch {
            try {
                eventRepo.huddleUpdateFlow.filter { it.userStatus == AbstractHuddle.HuddleUserStatus.INVITE_RECEIVED }.collect {
                    onHuddleListRequiresUpdate.postValue(Unit)
                }
            } finally {
            }
        }
    }

    val onHuddleListRequiresUpdate = LiveEvent<Unit>()

    private val _huddleList = huddleRepo.getPrivateGroupsPager().liveData.cachedIn(viewModelScope)
    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    val huddleList: MediatorLiveData<PagingData<HuddleUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<HuddleUIModel>?>(null)
        fun updateHuddleList() {
            val list: PagingData<HuddleUIModel>? = _huddleList.value?.map { item ->
                item.lastMessage?.sender?.let {
                    _nickNames.value?.findNickName(it)?.let { nickName ->
                        item.lastMessage?.senderDetails?.name = nickName
                        item.senderDetails?.name = nickName
                    }
                }
                HuddleUIModel.LocalHuddleUIModel(item,_selectedHuddleList.find { return@find it.id==item.id }!=null)
            }
            med.postValue(list)
        }
        med.addSource(_huddleList) { updateHuddleList() }
        med.addSource(_selectedHuddles) { updateHuddleList() }
        med.addSource(_nickNames) { updateHuddleList() }
        med
    }

    override suspend fun performHuddleAction(ids: List<Int>, action: HuddleAction, type: HuddleType, involvement: HuddleInvolvement?): ResultOf<String> {
        return huddleRepo.performHuddleAction(ids, action, type)
    }

}