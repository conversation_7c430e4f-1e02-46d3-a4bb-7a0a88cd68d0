package com.app.messej.ui.home.gift.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.databinding.FragmentBirthdayAlertBottomSheetBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

abstract class BirthdayBaseBottomSheetFragment : BottomSheetDialogFragment() {


    protected lateinit var binding: FragmentBirthdayAlertBottomSheetBinding
    protected val viewModel: BirthdayBottomSheetViewModel by viewModels()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_birthday_alert_bottom_sheet, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setUp()
    }

    private fun setUp() {
        binding.btnBirthdayClose.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.buttonSendBirthdayGift.setOnClickListener {
                  viewModel.firstBirthday.value?.userId?.let { userId ->
                      findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalGiftFragment(userId, giftContext = GiftContext.BIRTHDAY, birthday = true))
                  }
        }
    }

}