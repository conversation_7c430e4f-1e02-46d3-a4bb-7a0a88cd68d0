package com.app.messej.ui.auth.onboarding

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.WindowCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.R
import com.app.messej.databinding.FragmentOnboardingBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class OnboardingFragment : Fragment() {

    private lateinit var binding: FragmentOnboardingBinding

    private val viewModel: OnboardViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater,R.layout.fragment_onboarding,container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if(viewModel.onboardingCompleted.value==true) proceed()
        else {
            setup()
            observe()
        }
    }

    fun setup() {
        activity?.window?.let {
            WindowCompat.getInsetsController(it, it.decorView).isAppearanceLightStatusBars = true
        }
        binding.viewPager.apply {
            Log.d("OBD", "setup: before possible crash: $this")
            adapter = OnboardingViewPagerAdapter()

            Log.d("OBD", "setup: after possible crash")
            binding.dotIndicator.attachTo(this)
            offscreenPageLimit = 1
            
        }
        binding.skipButton.setOnClickListener {
            viewModel.finishOnboarding()
        }

        binding.nextButton.setOnClickListener {
            if (binding.viewPager.currentItem > binding.viewPager.childCount) {
                viewModel.finishOnboarding()
            } else {
                binding.viewPager.setCurrentItem(binding.viewPager.currentItem + 1, true)
            }
        }

        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback(){
            override fun onPageSelected(position: Int) {
                if (position == 0) {
                    binding.skipButton.visibility = View.VISIBLE
                } else if (position == 1) {
                    binding.skipButton.visibility = View.VISIBLE
                } else {
                    binding.skipButton.visibility = View.GONE
                }
            }
        })
    }

    fun observe() {
        viewModel.onboardingCompleted.observe(viewLifecycleOwner) {
            Log.d("OBD", "observe: onboardingCompleted $it")
            if(it) {
                proceed()
            }
        }
    }

    private fun proceed() {
        findNavController().navigateSafe(OnboardingFragmentDirections.actionGlobalNavGraphLogin())
    }
}