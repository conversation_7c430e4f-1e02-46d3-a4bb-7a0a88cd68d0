package com.app.messej.ui.home.businesstab.operations.tasks

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.ActivityNotFoundException
import android.content.Intent
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.BusinessTaskActivity
import com.app.messej.databinding.FragmentBusinessOperationTaskThreeBinding
import com.app.messej.ui.customviews.CustomBalloonFactory
import com.app.messej.ui.customviews.ViewExtensions.setBlurredBackground
import com.app.messej.ui.customviews.ViewExtensions.setBlurredImage
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.businesstab.adapter.BusinessOperationPayoutHistoryAdapter
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.skydoves.balloon.Balloon
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BusinessOperationTaskThreeFragment : Fragment() {

    private lateinit var binding: FragmentBusinessOperationTaskThreeBinding
    private val viewModel: BusinessOperationsTaskThreeViewModel by activityViewModels()
    private lateinit var textView: AppCompatTextView
    private lateinit var profileBalloon: Balloon
    private var mAdapter: BusinessOperationPayoutHistoryAdapter? = null
    private val commonViewModel: CommonHomeViewModel by activityViewModels()
    private val mViewModel: BusinessOperationsTaskViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_operation_task_three, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mViewModel.getBusinessOperations()
        val layout = LayoutInflater.from(context).inflate(R.layout.layout_task_information, null)
        val balloonFactory = CustomBalloonFactory.withLayout(layout)
        profileBalloon = balloonFactory.create(requireContext(), viewLifecycleOwner)
        textView = profileBalloon.getContentView().findViewById(R.id.field_likes_value)

        val ambassador = ContextCompat.getString(requireActivity(), R.string.user_citizenship_ambassador)
        binding.titleComplete.text = ContextCompat.getString(requireActivity(), R.string.bussiness_task_three_complete_title)
            .highlightOccurrences(ambassador) {
                StyleSpan(Typeface.BOLD)
            }.highlightOccurrences(ambassador) {
                ForegroundColorSpan(ContextCompat.getColor(view.context,R.color.colorBusinessOperationTaskActivityOne))
            }
        binding.actionRequestHistory.setOnClickListener { findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToBusinessOperationsHistoryDialogFragment()) }
        blurTransform()
        onClickListeners()
        observe()
    }

    private fun blurTransform() {
        viewModel.isButtonEnable()
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                binding.layoutBusiness.setBlurredBackground(R.drawable.bg_business, requireContext())
            }
        }
        binding.imgTaskThreeUnderLine.setBlurredImage(R.drawable.bg_task_one_underline, 10, requireContext())
    }

    private fun observe() {
        viewModel.payoutHistoryList.observe(viewLifecycleOwner) { pagingData ->
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
        }

        viewModel.apiResponse.observe(viewLifecycleOwner) {
             binding.actionRequestHistory.isEnabled=true
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            mViewModel.setSubmitDialogDismiss()

        }

        viewModel.isScreenShotUploaded.observe(viewLifecycleOwner){
            if (it){
                Toast.makeText(requireContext(), getString(R.string.business_screenshot_add_success_message), Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(requireContext(), getString(R.string.business_screenshot_added_failed_message), Toast.LENGTH_SHORT).show()
            }
        }
        viewModel.onTriggerCrop.observe(viewLifecycleOwner){
            imageCropResult.launch(it.getIntent(requireContext()))
        }
        viewModel.payoutEligibility.observe(viewLifecycleOwner) {
            if (it?.daysPending == 0) {
                binding.buttonTextOne.visibility = View.VISIBLE
                binding.buttonTextTwo.visibility = View.GONE
            } else {
                binding.buttonTextOne.visibility = View.VISIBLE
                binding.buttonTextTwo.visibility = View.VISIBLE
                binding.buttonTextTwo.text = (resources.getString(R.string.bussiness_eligible_after_this_days, it?.daysPending.toString()))
            }
        }
    }

    @SuppressLint("StringFormatInvalid")
    fun onClickListeners() {
        binding.actionRequestPointsReview.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToBusinessPointsReviewDialogFragment())
        }

        binding.actionCustomerCompleted.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToTaskInformativeMessageDialogFragment(BusinessTaskActivity.CUSTOMERS))
        }
        binding.actionHuddleCompleted.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToTaskInformativeMessageDialogFragment(BusinessTaskActivity.HUDDLES))
        }
        binding.actionBroascastsCompleted.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToTaskInformativeMessageDialogFragment(BusinessTaskActivity.BROADCASTS))
        }
        binding.imageShareInfo.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToTaskInformativeMessageDialogFragment(BusinessTaskActivity.SHARE))
        }
        binding.imageParticipantsInfo.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToTaskInformativeMessageDialogFragment(BusinessTaskActivity.PARTICIPANTS))
        }
        binding.imageFollowersInfo.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToTaskInformativeMessageDialogFragment(BusinessTaskActivity.FOLLOWERS))
        }
        binding.imageLikesInfo.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToTaskInformativeMessageDialogFragment(BusinessTaskActivity.LIKES))
        }
        binding.actionLearnMore.setOnClickListener {
            commonViewModel.learnMoreClicked.postValue(true)
            mViewModel.taskThreeLearnMoreClicked.postValue(true)}

        binding.imageArrowCircle.setOnClickListener {
            showAppReviewAlertDialog()
        }
        binding.actionRateAppCompleted.setOnClickListener {
           viewModel.setAppRateEditToggle()
        }

        binding.actionEditRating.setOnClickListener {
            showAppReviewAlertDialog()
        }

    }

    override fun onResume() {
        super.onResume()
        mViewModel.taskThreeLearnMoreClicked.postValue(false)
        viewModel.checkIfReviewAccepted()
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                binding.layoutBusiness.setBlurredBackground(R.drawable.bg_business, requireContext())
            }
        }
    }

    @SuppressLint("StringFormatInvalid")
    private fun showBroadcastBadge(it: View) {
        if ((viewModel.businessOperation.value?.totalBroadcasts ?: 0) < (viewModel.businessOperation.value?.payoutStatus?.payoutMinBroadcasts ?: 0)) {
            profileBalloon.showAtCenter(anchor = it, xOff = it.x.toInt(), yOff = it.y.toInt())
            textView.text = getString(R.string.business_operation_task_broadcast_badge_msg, viewModel.businessOperation.value?.payoutStatus?.payoutMinBroadcasts)
        }
    }

    @SuppressLint("StringFormatInvalid")
    private fun showHuddleBadge(it: View) {
        if ((viewModel.businessOperation.value?.huddlesCount ?: 0) < (viewModel.businessOperation.value?.payoutStatus?.payoutMinHuddles ?: 0)) {
            profileBalloon.showAtCenter(anchor = it, xOff = it.x.toInt(), yOff = it.y.toInt())
            textView.text = getString(R.string.business_operation_task_huddle_badge_msg, viewModel.businessOperation.value?.payoutStatus?.payoutMinHuddles)
        }
    }

    @SuppressLint("StringFormatInvalid")
    private fun showCustomerBadge(it: View) {
        if ((viewModel.businessOperation.value?.dears ?: 0) < (viewModel.businessOperation.value?.payoutStatus?.payoutMinDears ?: 0)) {
            profileBalloon.showAtCenter(anchor = it, xOff = it.x.toInt(), yOff = it.y.toInt())
            textView.text = getString(R.string.business_operation_task_customer_badge_msg, viewModel.businessOperation.value?.payoutStatus?.payoutMinDears)
        }
    }

    private val selectImageFromGalleryResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                viewModel.addImage(uri)
            }
        }
    }
    private fun showAppReviewAlertDialog() {
        val dialog = Dialog(requireContext())
        val customLayout = layoutInflater.inflate(R.layout.item_app_rating_alert_dialog, null);
        val attachImage = customLayout.findViewById<Button>(R.id.action_attach_photo)
        val reviewApp = customLayout.findViewById<Button>(R.id.action_edit_rating)
        val closeDialog = customLayout.findViewById<AppCompatImageView>(R.id.btn_dialog_close)
        val background = customLayout.findViewById<ConstraintLayout>(R.id.alert_layout)
        dialog.setContentView(customLayout)
        dialog.setCancelable(false)
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                background.setBlurredBackground(R.drawable.bg_operations_otp, requireContext())
            }
        }
        attachImage.setOnClickListener {
            dialog.dismiss()
            val galleryIntent = Intent(Intent.ACTION_GET_CONTENT)
            galleryIntent.type = "image/*"
            selectImageFromGalleryResult.launch(galleryIntent)
        }
        closeDialog.setOnClickListener { dialog.dismiss() }
        val packageName = requireContext().packageName
        reviewApp.setOnClickListener {
            dialog.dismiss()
            try {
                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName")))
            } catch (e: ActivityNotFoundException) {
                startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$packageName")))
            }
        }
        dialog.show()
    }

    private val imageCropResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let {
                val resultUri = UCrop.getOutput(it)?: return@let
                viewModel.getImageUriForCapture(resultUri)
            }
        } else {
           viewModel.onCropCancelled()
        }
    }
}
