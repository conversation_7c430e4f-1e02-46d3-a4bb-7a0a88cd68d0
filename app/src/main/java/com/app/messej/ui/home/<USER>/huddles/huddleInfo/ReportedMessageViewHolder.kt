package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.entity.HuddleReportedMessage
import com.app.messej.data.model.entity.HuddleReportedMessageWithMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.ItemChatMessageMediaImageBinding
import com.app.messej.databinding.ItemChatMessageMediaImageSmallBinding
import com.app.messej.databinding.ItemHuddleReportedMessageBinding
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.chat.adapter.ChatMessageViewHolder
import com.app.messej.ui.utils.TextFormatUtils.setFormattedText
import com.bumptech.glide.Glide

class ReportedMessageViewHolder(
    val binding: ItemHuddleReportedMessageBinding,
    userId: Int,
    private var mListener : ChatAdapter.ChatClickListener,
    private val reportListener: ReportedMessagesListAdapter.ReportActionListener
    ) : ChatMessageViewHolder(binding.root, userId, false, mListener) {

    override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
        super.bind(item)
        val cm = (item as ChatMessageUIModel.ChatMessageModel)
        val hrm = cm.message as HuddleReportedMessage
        message = hrm
        senderName = (cm.chat as HuddleReportedMessageWithMedia).senderNickNameOrName

        statusText = when(hrm.reportStatus) {
            HuddleReportedMessage.ReportStatus.ADMIN_DELETED -> root.context.getString(R.string.huddle_info_report_status_admin_deleted)
            HuddleReportedMessage.ReportStatus.ADMIN_DELETED_USER_BLOCKED -> root.context.getString(R.string.huddle_info_report_status_admin_deleted_user_blocked)
            HuddleReportedMessage.ReportStatus.MANAGER_DELETED -> root.context.getString(R.string.huddle_info_report_status_manager_deleted)
            HuddleReportedMessage.ReportStatus.MANAGER_DELETED_USER_BLOCKED -> root.context.getString(R.string.huddle_info_report_status_manager_deleted_user_blocked)
            HuddleReportedMessage.ReportStatus.USER_DELETED -> root.context.getString(R.string.huddle_info_report_status_user_deleted)
            else -> ""
        }
        val messageSpan = formatAndHighlightText(chatMessage.context, cm)
        chatMessage.setText(messageSpan?:"", TextView.BufferType.SPANNABLE)
        chatMessage.setExpanded(false)
        chatMessage.apply {
            setOnStateChangeListener { expanded ->
                cm.expanded = expanded
            }
            setExpanded(cm.expanded)
        }

        loadMedia(mediaHolder,cm,R.color.colorChatDefault)

        topCard.setOnClickListener { mListener.onItemClick(item.message, layoutPosition) }
        topCard.setOnLongClickListener {
            mListener.onItemLongClick(item.message, layoutPosition)
            true
        }

        chatMessage.setOnClickListener {
            if(mListener.onItemClick(item.message, layoutPosition)) return@setOnClickListener
            if(chatMessage.isExpanded()) return@setOnClickListener
            chatMessage.setExpanded(true)
        }
        chatMessage.setOnLongClickListener {
            mListener.onItemLongClick(item.message, layoutPosition)
            true
        }

        deleteMessageButton.setOnClickListener {
            reportListener.onDeleteAction(hrm,layoutPosition)
        }
        reportedParticipantsButton.setOnClickListener {
            reportListener.onViewReporters(hrm,layoutPosition)
        }
        chatMessage.setFormattedText(cm.message.displayMessage?:"", cm.message.chatTextColor)

    }

    override fun loadMedia(mediaHolder: ViewGroup, cm: ChatMessageUIModel.ChatMessageModel, bubbleColor: Int) {
        if (cm.message.messageType==AbstractChatMessage.MessageType.MEDIA) {
            if (cm.message.mediaMeta!!.mediaType == MediaType.IMAGE) {
                if (imageBinding == null) {
                    mediaHolder.removeAllViews()
                    val ib = ItemChatMessageMediaImageSmallBinding.inflate(LayoutInflater.from(mediaHolder.context), mediaHolder, false)
                    mediaHolder.addView(ib.root)
                    mediaHolder.visibility = View.VISIBLE
                    imageBinding = ib.actualMediaLayout
                }
            }
            /*TODO "handle video case if required"*/
        }
        super.loadMedia(mediaHolder, cm, bubbleColor)
    }

    override fun loadMediaImage(binding: ItemChatMessageMediaImageBinding, msg: ChatMessageUIModel.ChatMessageModel, pos: Int) {
        val message = msg.message as HuddleReportedMessage
        if (message.deleted && msg.chat.offlineMedia==null) {
            val meta = msg.message.mediaMeta!!
            val ratio = meta.imageWidth / meta.imageHeight
            binding.chatImage.apply {
                binding.showLoading = false
                binding.loadProgress = -1
                binding.showDownload = false
                (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = ratio.coerceIn(IMAGE_ASPECT_MIN, IMAGE_ASPECT_MAX).toString()
                Glide.with(context).load(meta.thumbnail).placeholder(R.drawable.im_chat_image_placeholder).error(R.drawable.im_chat_image_placeholder).into(this)
            }
            binding.chatImage.apply {
                setOnClickListener { mListener.onMediaImageTap(this,msg.chat, pos) }
                setOnLongClickListener {
                    mListener.onItemLongClick(msg.message, pos)
                    true
                }
            }
        } else {
            super.loadMediaImage(binding, msg, pos)
        }
    }

    override fun getHighlightView() = null
}