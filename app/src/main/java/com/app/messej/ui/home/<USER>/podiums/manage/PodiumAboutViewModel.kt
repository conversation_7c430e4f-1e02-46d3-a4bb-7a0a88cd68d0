package com.app.messej.ui.home.publictab.podiums.manage

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.podium.PodiumAbout
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class PodiumAboutViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)
    private val profileRepo = ProfileRepository(application)

    private val accountRepo = AccountRepository(application)

    val user: CurrentUser get() = accountRepo.user

    private val _podiumId = MutableLiveData<String?>(null)
    val podiumId: LiveData<String?> = _podiumId

    private val _iAmElevated = MutableLiveData<Boolean?>(null)
    val iAmElevated: LiveData<Boolean?> = _iAmElevated

    private val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )

    private val nickNamesLiveData = nickNames.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    fun setPodiumIdAndRole(id: String, elevated: Boolean) {
        _iAmElevated.value = elevated
        if (_podiumId.value == id) return
        _podiumId.value = id
        loadPodiumDetails(id)
    }

    private val _podium = MutableLiveData<PodiumAbout?>(null)
    val podium: LiveData<PodiumAbout?> = _podium

    val podiumDetailsLoading = MutableLiveData(false)
    val onPodiumLoadError = LiveEvent<String>()

    val adminListExpanded = MutableLiveData(false)

    val podiumManager: MediatorLiveData<PodiumSpeaker?> by lazy {
        val med = MediatorLiveData<PodiumSpeaker?>(null)
        fun update() {
            val manager = _podium.value?.manager
            med.postValue(
                manager?.copy(
                    name = nickNames.nickNameOrName(manager)
                )
            )
        }
        med.addSource(_podium) { update() }
        med.addSource(nickNamesLiveData) { update() }
        med
    }
    val userManager: Boolean
        get() = _podium.value?.manager?.id == user.id


    val admins: MediatorLiveData<MutableList<PodiumSpeaker>?> by lazy {
        val med = MediatorLiveData<MutableList<PodiumSpeaker>?>(null)
        fun update() {
            val admins = _podium.value?.admins?.map { ps ->
                ps.copy(
                    name = nickNames.nickNameOrName(ps)
                )
            }?.toMutableList()
            med.postValue(admins)
        }
        med.addSource(_podium) { update() }
        med.addSource(nickNamesLiveData) { update() }
        med
    }

    val showAdminList: LiveData<Boolean> = _podium.map {
        it?.admins ?: return@map false
        it.admins.isNotEmpty()
    }

    private fun loadPodiumDetails(id: String) {
        podiumDetailsLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            getPodiumDetails(id)?.also { pod ->
                _podium.postValue(pod)
                podiumDetailsLoading.postValue(false)
            } ?: run {

            }
        }
    }

    private suspend fun getPodiumDetails(id: String): PodiumAbout? {
        when (val result = podiumRepository.getPodiumAbout(id)) {
            is ResultOf.APIError -> {
                onPodiumLoadError.postValue(result.error.toString())
            }

            is ResultOf.Error -> {
                onPodiumLoadError.postValue(result.exception.toString())
            }

            is ResultOf.Success -> {
                return result.value
            }
        }
        return null
    }

    fun toggleAdminView() {
        adminListExpanded.postValue(!(adminListExpanded.value ?: false))
    }

    val recordsList = _podiumId.switchMap {
        it ?: return@switchMap null
        if (_iAmElevated.value == false) return@switchMap null
        podiumRepository.getPodiumRecordsPager(it).liveData.cachedIn(viewModelScope)
    }
}