package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentFlaxSellingConfirmationBinding
import com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import java.math.BigDecimal
import java.math.RoundingMode


class FlaxSellingConfirmationFragment : Fragment() {
    private val viewModel: AlreadyFlaxAppliedViewModel by viewModels()
    private val businessWithDrawViewModel: BusinessWithDrawViewModel by activityViewModels ()

    private lateinit var binding: FragmentFlaxSellingConfirmationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.flax_selling_unit_title)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flax_selling_confirmation, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = businessWithDrawViewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()

    }

    private fun observe() {
        viewModel.cancelPayoutStatus.observe(viewLifecycleOwner) {
            if (it) {
                findNavController().popBackStack()
            }
        }
        viewModel.loader.observe(viewLifecycleOwner) {
            binding.loaderLiveData = it
        }
    }

    private fun setup() {
        binding.cancelButton.setOnClickListener {
            val paymentRequestId = businessWithDrawViewModel.payoutEligibility.value?.payoutData?.id
            val refundAmount =
                (businessWithDrawViewModel.payoutEligibility.value?.payoutData?.requestedPointsForReview)?.minus(businessWithDrawViewModel.payoutEligibility.value?.payoutData?.processingFee?:0.0)
                    ?.let { amount -> BigDecimal(amount).setScale(2, RoundingMode.HALF_UP).toDouble() }
            confirmAction(
                message = getString(R.string.cancel_payout_dialog,refundAmount.toString())
            ) { viewModel.cancelPaymentRequest(paymentRequestId.toString()) }
        }
    }
}
