package com.app.messej.ui.home.publictab.huddles.poll

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.AcceptedMemberType
import com.app.messej.databinding.FragmentSendPollInvitationBinding
import com.kennyc.view.MultiStateView


class SendPollInvitationFragment : Fragment() {
    private lateinit var binding: FragmentSendPollInvitationBinding
    private val viewModel: ScheduledPollsViewModel by viewModels()
    private val args:SendPollInvitationFragmentArgs by navArgs()
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_send_poll_invitation, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }


    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.allSelected.observe(viewLifecycleOwner){
            viewModel.setAllOption(it)
        }
        viewModel.isPollInvited.observe(viewLifecycleOwner){
            it?.let {
                if (it){
                    findNavController().popBackStack()
                }
            }
        }
        viewModel.isInviteLoading.observe(viewLifecycleOwner){
            it?.let {
                if (it) binding.multiStateView.viewState = MultiStateView.ViewState.LOADING
                else{
                    binding.multiStateView.viewState = MultiStateView.ViewState.CONTENT
                }
            }
        }
    }

    private fun setup() {
        viewModel.seTribeArgs(args.isTribe)
        binding.allCheckBox.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleAllChecked(isChecked)
        }

        binding.invitationSubmitButton.setOnClickListener {

            if (getAcceptedMemberArray().isNotEmpty()) {
                viewModel.pollInvite(args.pollId, getAcceptedMemberArray())
            }else{
                Toast.makeText(requireContext(), getString(R.string.title_select_one_option), Toast.LENGTH_SHORT).show()
            }
        }
        binding.invitationCancelButton.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun getAcceptedMemberArray(): List<String> {
        val invitedMemberList = mutableListOf<String>()

        if (viewModel.huddleMemberAccepted.value == true) {
            invitedMemberList.add(AcceptedMemberType.HUDDLE_MEMBERS.value)
        }

        if (viewModel.myDearAccepted.value == true) {
            invitedMemberList.add(AcceptedMemberType.DEARS.value)
        }

        if (viewModel.myFansAccepted.value == true) {
            invitedMemberList.add(AcceptedMemberType.FANS.value)
        }

        if (viewModel.myLikersAccepted.value == true) {
            invitedMemberList.add(AcceptedMemberType.LIKERS.value)
        }

        return invitedMemberList
    }
}