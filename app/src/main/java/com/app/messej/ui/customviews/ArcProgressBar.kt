package com.app.messej.ui.customviews
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Paint.Cap.ROUND
import android.graphics.Paint.Style.STROKE
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.app.messej.R

class ArcProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : View(context, attrs, defStyleAttr, defStyleRes) {
    private val startAngle = 270F + ARC_GAP_ANGLE
    private val sweepAngleFull = 360F - (ARC_GAP_ANGLE * 2F)
    private val typedArray = context.obtainStyledAttributes(attrs, R.styleable.ArcProgressBar)
    private var arcProgressColor = typedArray.getColor(R.styleable.ArcProgressBar_arc_progress_color,
        ContextCompat.getColor(context, R.color.colorBusinessArcProgressColor))
    private var progress = 0F

    private val paintBackground = Paint().apply {
        isAntiAlias = true
        color = ContextCompat.getColor(context, R.color.colorBusinessGrey)
        style = STROKE
        strokeWidth = ARC_STROKE_WIDTH
        strokeCap = ROUND
    }

    private val paintProgress = Paint().apply {
        isAntiAlias = true
        color =arcProgressColor
        style = STROKE
        strokeWidth = ARC_STROKE_WIDTH
        strokeCap = ROUND
    }

    init {
        typedArray.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        canvas.let {
            it.drawArc(createOval(), startAngle, sweepAngleFull, false, paintBackground)
            it.drawArc(createOval(), sweepAngleFull-progress, progress, false, paintProgress)
        }
    }

    fun setPercent(percent: Int) {
        require(percent in 0..100) { "Percent must be between 0 and 100, percent = [$percent]" }
        progress = sweepAngleFull * (percent / 100F)
        invalidate()
    }

    fun setProgressStrokeColor(color: Int) {
        paintProgress.color = color
        invalidate()
    }

    private fun createOval() = RectF(PADDING, PADDING, width - PADDING, height - PADDING)

    companion object {
        private const val PADDING = 24F
        private const val ARC_STROKE_WIDTH = 22F
        private const val ARC_GAP_ANGLE = 90F
    }
}
