package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.huddles.ReportedByResponse
import com.app.messej.databinding.ItemHuddleReportedByListBinding

class ReportedParticipantsListAdapter(private val mListener: ItemListener) : PagingDataAdapter<ReportedByResponse.ReportedParticipant, ReportedParticipantsListAdapter.ReportedParticipantsListViewHolder>(
    ReportDiff
) {

    interface ItemListener {
        fun onItemClick(item: ReportedByResponse.ReportedParticipant, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewG<PERSON>, viewType: Int) =
        ReportedParticipantsListViewHolder(
            ItemHuddleReportedByListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: ReportedParticipantsListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }

    inner class ReportedParticipantsListViewHolder(private val binding: ItemHuddleReportedByListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ReportedByResponse.ReportedParticipant, position: Int) = with(binding) {
            user=item

            binding.root.setOnClickListener {
                mListener.onItemClick(item, position)
            }
        }
    }

    object ReportDiff : DiffUtil.ItemCallback<ReportedByResponse.ReportedParticipant>() {
        override fun areItemsTheSame(oldItem: ReportedByResponse.ReportedParticipant, newItem: ReportedByResponse.ReportedParticipant) =
            oldItem.participantId == newItem.participantId

        override fun areContentsTheSame(oldItem: ReportedByResponse.ReportedParticipant, newItem: ReportedByResponse.ReportedParticipant) =
            oldItem == newItem
    }
}