package com.app.messej.ui.composeComponents

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.app.messej.R

@Composable
fun HorizontalDotIndicator(
    modifier: Modifier = Modifier,
    selectedItemPosition: Int,
    totalCount: Int,
    selectedItemColor: Color = colorResource(id = R.color.colorPrimary),
    unSelectedItemColor: Color = colorResource(id = R.color.colorSurfaceSecondaryDarker)
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.line_spacing))
    ) {
        repeat(times = totalCount) { currentItemPosition ->
            Box(
                modifier = Modifier
                    .clip(shape = CircleShape)
                    .background(color = if (currentItemPosition == selectedItemPosition) selectedItemColor else unSelectedItemColor)
                    .size(size = 10.dp)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun HorizontalDotPreview() {
    HorizontalDotIndicator(
        selectedItemPosition = 2,
        totalCount = 8
    )
}