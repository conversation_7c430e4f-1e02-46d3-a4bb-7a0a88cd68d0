package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentHuddleParticipantFilterMenuBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class HuddleParticipantFilterMenuFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentHuddleParticipantFilterMenuBinding

    private val viewModel : HuddleParticipantsViewModel by navGraphViewModels(R.id.navigation_huddle_participants)

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_participant_filter_menu, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {

    }

    private fun setup() {

    }
}