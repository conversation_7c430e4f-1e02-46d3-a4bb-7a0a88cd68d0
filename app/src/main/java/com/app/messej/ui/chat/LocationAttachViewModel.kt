package com.app.messej.ui.chat

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.google.android.libraries.places.api.model.Place

class LocationAttachViewModel(application: Application) : AndroidViewModel(application) {

    private val _nearbyPlaces: MutableLiveData<List<Place>> = MutableLiveData(listOf())
    val nearbyPlaces: MutableLiveData<List<Place>> = _nearbyPlaces

    val searchLocationName = MutableLiveData<String?>(null)

    fun setNearbyPlaces(list: List<Place>) {
        _nearbyPlaces.postValue(list)
    }
}