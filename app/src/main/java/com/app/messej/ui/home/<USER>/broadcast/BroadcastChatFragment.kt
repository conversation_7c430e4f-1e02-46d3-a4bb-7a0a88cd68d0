package com.app.messej.ui.home.publictab.broadcast

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.MainActivity
import com.app.messej.NavChatBroadcastDirections
import com.app.messej.R
import com.app.messej.data.model.enums.AttachmentSource
import com.app.messej.data.model.enums.BroadcastAction
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.databinding.ItemCustomActionBarBroadcastChatBinding
import com.app.messej.databinding.LayoutActionModeSearchBinding
import com.app.messej.databinding.LayoutChatSearchFooterBinding
import com.app.messej.ui.chat.BaseChatFragment
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BroadcastChatFragment : BaseChatFragment(), MenuProvider {

    private val args: BroadcastChatFragmentArgs by navArgs()

    override val viewModel: BroadcastChatViewModel by navGraphViewModels(R.id.nav_chat_broadcast)

    private lateinit var actionBarBinding: ItemCustomActionBarBroadcastChatBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        val root = super.onCreateView(inflater, container, savedInstanceState)
        inflateActionBar()
        return root
    }

    private fun inflateActionBar() {
        binding.actionBarStub.apply {
            viewStub?.apply {
                setOnInflateListener { _, inflated ->
                    actionBarBinding = ItemCustomActionBarBroadcastChatBinding.bind(inflated)
                }
                layoutResource = R.layout.item_custom_action_bar_broadcast_chat
                inflate()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(actionBarBinding.toolbar)
        }
    }

    private fun setup() {
        if (!viewModel.loggedIn) {
            findNavController().popBackStack()
        }
        viewModel.setBroadcastMode(args.mode)
        binding.chatBackground.visibility = View.GONE
        args.chatText?.let { viewModel.prefillChatInput(it) }

        actionBarBinding.toolbarLayout.setOnClickListener {
            if (viewModel.broadcastMode.value?.isOfLikerType==true) {
                showBroadcastModeMenu()
            }
        }

        binding.actionDecorHolder.apply {
            removeAllViews()
            val binding = DataBindingUtil.inflate<LayoutChatSearchFooterBinding>(layoutInflater, R.layout.layout_chat_search_footer, this, false)
            binding.lifecycleOwner = viewLifecycleOwner
            binding.viewModel = viewModel
            addView(binding.root)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_broadcast_options,menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> showSearchMode(true)
            R.id.action_more -> {
                findNavController().navigateSafe(BroadcastChatFragmentDirections.actionBroadcastChatFragmentToBroadcastOptionsMenuFragment(viewModel.broadcastMode.value==BroadcastMode.ALL_LIKERS))
            }
            else -> return false
        }
        return true
    }

    override val edsProvider: EDSProvider = object: EDSProvider {
        override val message = R.string.chat_eds_message_broadcast
        override val image = R.drawable.im_eds_chat
        override val action = null
    }

    override fun allowReplySwipe() = false

    private fun showBroadcastModeMenu() {
        val popup = PopupMenu(requireContext(), actionBarBinding.toolbarLayout)
        popup.menuInflater.inflate(R.menu.menu_broadcast_mode, popup.menu)

        popup.setOnMenuItemClickListener { menuItem: MenuItem ->
            val mode = when(menuItem.itemId) {
                R.id.action_all_likers -> BroadcastMode.ALL_LIKERS
                R.id.action_all_premium_likers -> BroadcastMode.ALL_PREMIUM_LIKERS
                else -> return@setOnMenuItemClickListener false
            }
            if (mode!=viewModel.broadcastMode.value) {
                val action = NavChatBroadcastDirections.actionNavigationChatBroadcastSelf(mode, null)
                findNavController().navigateSafe(action)
            }
            return@setOnMenuItemClickListener true
        }
        popup.setOnDismissListener {
            // Respond to popup being dismissed.
        }
        // Show the popup menu.
        popup.show()
    }

    private fun observe() {
        viewModel.broadcastMode.observe(viewLifecycleOwner) {
            it?: return@observe
            actionBarBinding.hasDropdown = (it.isOfLikerType)
            val subText =  when(it) {
                BroadcastMode.ALL_DEARS -> R.string.broadcast_all_dears
                BroadcastMode.ALL_FANS -> R.string.broadcast_all_fans
                BroadcastMode.ALL_LIKERS -> R.string.broadcast_all_likers
                BroadcastMode.ALL_PREMIUM_LIKERS -> R.string.broadcast_all_premium_likers
                else -> return@observe
            }
            actionBarBinding.subtitle = resources.getString(R.string.broadcast_sending_to, resources.getString(subText))
        }

        viewModel.canForwardSelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_forward)?.isVisible = it
        }
        viewModel.actionIsStar.observe(viewLifecycleOwner) { star ->
            actionMode?.menu?.findItem(R.id.action_star)?.setIcon(if(star) R.drawable.ic_star else R.drawable.ic_unstar)
        }
        viewModel.onStarAction.observe(viewLifecycleOwner){
            when(it){
                BroadcastAction.STAR -> Toast.makeText(requireContext(), resources.getString(R.string.broadcast_toast_starred), Toast.LENGTH_SHORT).show()
                BroadcastAction.UNSTAR -> Toast.makeText(requireContext(), resources.getString(R.string.broadcast_toast_unstarred), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onMessageForwarded.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(),R.string.broadcast_forward_success,Toast.LENGTH_SHORT).show()
            if (it.size==1) {
                val action = NavChatBroadcastDirections.actionNavigationChatBroadcastSelf(it[0],null)
                findNavController().navigateSafe(action)
            }
        }

        viewModel.onCannotForward.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(),R.string.broadcast_forward_media_not_offline,Toast.LENGTH_SHORT).show()
        }

        setFragmentResultListener(BroadcastOptionsMenuFragment.SELECTED_OPTION_KEY) { _, bundle ->
            when(bundle.getString(BroadcastOptionsMenuFragment.SELECTED_OPTION_KEY)) {
                BroadcastOptionsMenuFragment.SELECTED_OPTION_STARRED -> {
                    viewModel.broadcastMode.value?.let {
                        findNavController().navigateSafe(BroadcastChatFragmentDirections.actionBroadcastChatFragmentToStarredBroadcastFragment(it))
                    }
                }
                BroadcastOptionsMenuFragment.SELECTED_OPTION_PRIVACY -> {
                    findNavController().navigateSafe(BroadcastChatFragmentDirections.actionGlobalBlockedBroadcast())
                }
            }
        }

        setFragmentResultListener(BroadcastStarredFragment.SELECTED_MESSAGE_KEY) { _, bundle ->
            bundle.getString(BroadcastStarredFragment.SELECTED_MESSAGE_KEY)?.let {
                viewModel.postPendingScrollToMessage(it)
            }
        }

        setFragmentResultListener(BroadcastForwardFragment.FORWARD_MODES_KEY) { _, bundle ->
            val modes = BroadcastForwardFragment.parseBundle(bundle)
            viewModel.confirmForward(modes)
        }

        viewModel.canDeleteSelectionForEveryone.observe(viewLifecycleOwner){
            if (it) 1 else 0
        }
    }

    override fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_broadcast_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_star -> viewModel.toggleStar()
                        R.id.action_forward -> {
                            val action = BroadcastChatFragmentDirections.actionBroadcastChatFragmentToBroadcastForwardFragment()
                            findNavController().navigateSafe(action)
                        }
                        R.id.action_copy -> viewModel.copySelection()
                        R.id.action_delete -> confirmDelete(R.string.broadcast_delete_confirm_title, R.string.broadcast_delete_confirm_message, true,
                        viewModel.canDeleteSelectionForEveryone.value == true) {
                            viewModel.deleteSelection(it)
                        }
                        else -> return false
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }

    override fun showAttachDialog() {
        val action = BroadcastChatFragmentDirections.actionGlobalImageAttachSourceFragment(AttachmentSource.entries.except(AttachmentSource.DOCUMENT, AttachmentSource.LOCATION))
        findNavController().navigateSafe(action)
    }

    override fun showImagePreview() {
        findNavController().navigateSafe(BroadcastChatFragmentDirections.actionBroadcastChatFragmentToBroadcastChatAttachImageFragment(null))
    }

    override fun showVideoPreview() {
        findNavController().navigateSafe(BroadcastChatFragmentDirections.actionBroadcastChatFragmentToBroadcastChatAttachVideoFragment(null))
    }

    override fun navigateToLocationSelect() {
        findNavController().navigateSafe(BroadcastChatFragmentDirections.actionBroadcastChatFragmentToBroadcastChatAttachLocationFragment())
    }

    override fun showDocumentPreview() {

    }

    var searchBinding: LayoutActionModeSearchBinding? = null
    private fun showSearchMode(show: Boolean) {
        if(show) {
            val callback = object:ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?,menu: Menu?): Boolean {
                    val viewB: LayoutActionModeSearchBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_action_mode_search, null, false)
                    viewB.lifecycleOwner = viewLifecycleOwner
                    mode?.customView = viewB.root
                    searchBinding = viewB

                    viewB.apply {
                        keyword = viewModel.searchText
                        showKeyboard(searchBox)
                    }
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?,menu: Menu?): Boolean {
                    searchBinding?.apply {
                        showKeyboard(searchBox)
                    }
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?,item: MenuItem?): Boolean {
                    return false
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.clearSearch()
                }
            }
            actionMode?.finish()
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }
}