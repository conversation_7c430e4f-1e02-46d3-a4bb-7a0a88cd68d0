package com.app.messej.ui.home.publictab.podiums.live

import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner

object ActivePodiumTracker {

    sealed class ActivePodiumScreen(open val podiumId: String) {
        data class PodiumRoom(override val podiumId: String, val anthemPlaying: Boolean = false): ActivePodiumScreen(podiumId)
        data class MaidanRoom(override val podiumId: String, val parentId: String): ActivePodiumScreen(podiumId)
    }

    private var activeScreen: ActivePodiumScreen? = null

    private var activeLifecycle: Lifecycle? = null

    fun registerActiveScreen(screen: ActivePodiumScreen, owner: LifecycleOwner) {
        Log.w("ACTIVE_PODIUM", "registerActiveScreen: $screen")
        activeScreen = screen
        activeLifecycle = owner.lifecycle.apply {
            addObserver(object: LifecycleEventObserver {
                override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                    if (!event.targetState.isAtLeast(Lifecycle.State.INITIALIZED)) {
                        removeActiveScreen()
                    }
                }
            })
        }
    }

    val isInPodiumScreen: Boolean
        get() = getActiveScreen()!=null

    val isAnthemPlaying: Boolean
        get() = getActiveScreen().let {  it is ActivePodiumScreen.PodiumRoom && it.anthemPlaying }

    fun isActive(screen: ActivePodiumScreen): Boolean {
        val active = getActiveScreen()?: return false
        Log.w("ACTIVE_PODIUM", "isActive: $active | $screen | ${if (active is ActivePodiumScreen.MaidanRoom && active.parentId == screen.podiumId) true else active.podiumId == screen.podiumId}")
        return if (active is ActivePodiumScreen.MaidanRoom && active.parentId == screen.podiumId) true
            else active.podiumId == screen.podiumId
    }

    private fun getActiveScreen(): ActivePodiumScreen? {
        Log.w("ACTIVE_PODIUM", "getActiveScreen: $activeScreen | ${activeLifecycle?.currentState}")
        activeScreen?.let { screen ->
            if (activeLifecycle?.currentState?.isAtLeast(Lifecycle.State.STARTED)==true) {
                return screen
            }
        }
        if (activeLifecycle?.currentState== Lifecycle.State.DESTROYED) {
            removeActiveScreen()
        }
        return null
    }

    fun removeActiveScreen() {
        Log.w("ACTIVE_PODIUM", "removeActiveScreen: $activeScreen")
        activeScreen = null
        activeLifecycle = null
    }
}