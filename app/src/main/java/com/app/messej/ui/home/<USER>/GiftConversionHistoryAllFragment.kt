package com.app.messej.ui.home.gift

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.databinding.FragmentGiftConversionHistoryAllBinding
import com.app.messej.ui.home.gift.adapter.GiftConversionHistoryPagerAdapter
import com.kennyc.view.MultiStateView

class GiftConversionHistoryAllFragment : Fragment() {
    private lateinit var binding: FragmentGiftConversionHistoryAllBinding
    private val viewModel: GiftConversionHistoryAllViewModel by viewModels()
    private var conversionAdapter: GiftConversionHistoryPagerAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_conversion_history_all, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }
    private fun setUp() {
        initAdapter()
    }

    private fun observe() {
        viewModel.giftConversionHistory.observe(viewLifecycleOwner) {
            conversionAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }
    private fun initAdapter() {
        conversionAdapter = GiftConversionHistoryPagerAdapter()

        val layoutManParticipant = LinearLayoutManager(context)

        binding.giftConversioHistory.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = conversionAdapter
        }

        conversionAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.eds_empty_message_transaction).text = context.getString(R.string.no_conversion_made)
            findViewById<AppCompatImageView>(R.id.eds_empty_image_transaction).setImageResource(R.drawable.bg_gift_empty_conversion)
        }
    }

}