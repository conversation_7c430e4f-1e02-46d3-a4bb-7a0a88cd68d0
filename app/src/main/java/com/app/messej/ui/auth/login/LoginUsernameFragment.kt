package com.app.messej.ui.auth.login

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.core.widget.addTextChangedListener
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.databinding.LayoutLoginUsernameBinding
import com.app.messej.ui.auth.forgotPassword.ForgotPasswordFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe


class LoginUsernameFragment : Fragment() {

    private lateinit var binding: LayoutLoginUsernameBinding

    private val viewModel: LoginViewModel by viewModels({ requireParentFragment()})

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater,R.layout.layout_login_username,container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        binding.textInputMobileNumber.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && !this.text.isNullOrEmpty()) {
                    viewModel.didEnterUserName.postValue(true)
                }
                else {
                    viewModel.didEnterUserName.postValue(false)
                }
            }
            addTextChangedListener { viewModel.clearLoginError() }
        }
        binding.textInputPassword.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus) {
                    viewModel.didEnterPassword.postValue(true)
                }
            }
            addTextChangedListener { viewModel.clearLoginError() }
        }
        binding.loginButton.setOnClickListener {
            viewModel.loginUser()
        }

        binding.forgotPasswordButton.setOnClickListener {
            findNavController().navigateSafe(R.id.action_global_forgot_password, bundleOf(ForgotPasswordFragment.RECOVERY_TYPE to OTPRequestMode.RESET_EMAIL))
        }
        binding.createAccountButton.setOnClickListener { findNavController().navigateSafe(LoginFragmentDirections.actionGlobalNavGraphRegister()) }

        binding.root.rootView.apply {
            setOnTouchListener { v, _ ->
                binding.textInputMobileNumber.editText!!.clearFocus()
                binding.textInputPassword.editText!!.clearFocus()
                v.performClick()
            }
        }
    }

    private fun observe() {
        viewModel.dataLoading.observe(viewLifecycleOwner) {
            binding.loginButton.isEnabled =
                (viewModel.dataLoading.value == false) && (viewModel.loginValid.value == true)

        }
        viewModel.loginValid.observe(viewLifecycleOwner) {
            binding.loginButton.isEnabled =
                (viewModel.dataLoading.value == false) && (viewModel.loginValid.value == true)
        }
        viewModel.showPasswordInvalidError.observe(viewLifecycleOwner) {
            updatePasswordFieldError()
        }
        viewModel.loginError.observe(viewLifecycleOwner) {
            updatePasswordFieldError()
        }
        viewModel.showUserNameInvalidError.observe(viewLifecycleOwner) {
                binding.textInputMobileNumber.error = if (it) {
                    binding.textInputMobileNumber.isErrorEnabled = true; resources.getString(R.string.login_error_user_name)
                } else {
                    binding.textInputMobileNumber.isErrorEnabled = false; null
                }
        }
    }

    private fun updatePasswordFieldError() {
        binding.textInputPassword.error = if(!viewModel.loginError.value.isNullOrEmpty()) {
            binding.textInputPassword.isErrorEnabled = true
            viewModel.loginError.value
        } else if(viewModel.showPasswordInvalidError.value==true){
            binding.textInputPassword.isErrorEnabled = true
            resources.getString(R.string.login_error_password)
        } else {
            binding.textInputPassword.isErrorEnabled = true
            null
        }
    }
}