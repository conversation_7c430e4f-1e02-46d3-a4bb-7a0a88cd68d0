package com.app.messej.ui.home.publictab.huddles.poll

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.enums.PollType
import com.app.messej.data.repository.PollsRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.launch

class ScheduledPollsViewModel(application: Application): AndroidViewModel(application) {

    private val _huddleId = MutableLiveData<Int>()
    val huddleId: LiveData<Int> = _huddleId

    private var _isTribed = MutableLiveData<Boolean>()
    val isTribe: LiveData<Boolean> = _isTribed

    private val _pollType = MutableLiveData<PollType>()
    val pollType: LiveData<PollType> = _pollType

    private val _pollId = MutableLiveData<Int>()
    val pollId: LiveData<Int> = _pollId

    private val _isPollInvited = MutableLiveData<Boolean?>()
    val isPollInvited: LiveData<Boolean?> = _isPollInvited

    private val _isUserIsManager = MutableLiveData<Boolean>(true)
    val isUserIsManager: LiveData<Boolean> = _isUserIsManager

    private val _isInviteLoading = MutableLiveData<Boolean>(false)
    val isInviteLoading: LiveData<Boolean> = _isInviteLoading

    val huddleMemberAccepted = MutableLiveData(false)
    val myDearAccepted = MutableLiveData(false)
    val myFansAccepted = MutableLiveData(false)
    val myLikersAccepted = MutableLiveData(false)
    val allAccepted = MutableLiveData<Boolean>()

    private val pollsRepository = PollsRepository(getApplication())
    fun seArgs(huddleId: Int, type: PollType, isUserIsManager: Boolean) {
        _huddleId.value = huddleId
        _pollType.value = type
        _isUserIsManager.value = isUserIsManager
    }

    val allSelected: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            if (huddleMemberAccepted.value == true && myDearAccepted.value == true && myLikersAccepted.value == true && myFansAccepted.value == true) {
                med.postValue(true)
            } else {
                med.postValue(false)
            }
        }
        med.addSource(huddleMemberAccepted) { update() }
        med.addSource(myDearAccepted) { update() }
        med.addSource(myFansAccepted) { update() }
        med.addSource(myLikersAccepted) { update() }
        med
    }


    val polls = _huddleId.switchMap {
        pollsRepository.getPollsListingPager(it, pollType.value!!).liveData.cachedIn(viewModelScope)
    }

    fun setPollData(pollId: Int) {
        _pollId.postValue(pollId)
    }

    val selectedPoll = _pollId.switchMap {
        pollsRepository.getSelectedPoll(pollID = it)
    }

    fun pollInvite(pollId: Int, memberList: List<String>) {
        _isInviteLoading.postValue(true)
        viewModelScope.launch {

            when (val result = pollsRepository.pollInviteByMember(pollId, memberList)) {
                is ResultOf.Success -> {
                    _isPollInvited.postValue(true)
                    _isInviteLoading.postValue(false)
                }

                is ResultOf.Error -> {
                    _isPollInvited.postValue(false)
                    _isInviteLoading.postValue(false)
                }

                is ResultOf.APIError -> {
                    _isPollInvited.postValue(false)
                    _isInviteLoading.postValue(false)
                }
            }
        }
    }

    fun handleAllChecked(checked: Boolean) {
        if (checked) {
            myFansAccepted.postValue(true)
            myLikersAccepted.postValue(true)
            myDearAccepted.postValue(true)
            huddleMemberAccepted.postValue(true)
        } else {
            if (allSelected.value==true) {
                myFansAccepted.postValue(false)
                myLikersAccepted.postValue(false)
                myDearAccepted.postValue(false)
                huddleMemberAccepted.postValue(false)
            }
        }
    }

    fun setAllOption(checked: Boolean) {
        allAccepted.postValue(checked)
    }
    fun refreshPolls(id: Int) {
        _huddleId.postValue(id)
    }

    fun seTribeArgs(tribe: Boolean) {
        _isTribed.value = tribe
    }

}