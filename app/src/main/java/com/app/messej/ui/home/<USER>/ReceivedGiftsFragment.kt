package com.app.messej.ui.home.gift

import GiftListAdapter
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.enums.GiftType
import com.app.messej.databinding.FragmentReceivedGiftsBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView

class ReceivedGiftsFragment : Fragment() {

    lateinit var binding: FragmentReceivedGiftsBinding
    private val viewModel: GiftReceivedViewModel by viewModels()
    private val giftCommonViewModel: GiftCommonViewModel by activityViewModels()
    private var mAdapter: GiftListAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_received_gifts, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        binding.isPremium = giftCommonViewModel.isPremiumUser
        binding.isResident = giftCommonViewModel.isResidentUser
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            if (it != null) {
                viewModel.giftType.postValue(it)
            }
        }
        viewModel.accountDetails.observe(viewLifecycleOwner){
        }
        viewModel.giftReceived.observe(viewLifecycleOwner) {
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        giftCommonViewModel.giftListData.observe(viewLifecycleOwner) {
            binding.gift = it?.userGiftData
        }

        viewModel.giftType.observe(viewLifecycleOwner) {
//            val layoutManager = LinearLayoutManager(requireContext())
            val layoutManager =  GridLayoutManager(binding.root.context,3)
            mAdapter = GiftListAdapter(it,object : GiftListAdapter.ActionListener {
                override fun onItemClick(item: GiftItem) {
                    if(item.count==null) return
                    if (item.count.toInt() > 0) {
                        findNavController().navigateSafe(GiftFragmentDirections.actionGiftFragmentToReceivedGiftHistoryFragment(item.id))
                    }
                }

                override fun availableFLixBalance(): Double {
                    return viewModel.accountDetails.value?.currentFlix?:0.0
                }

                override fun availableCoinBalance(): Double {
                    return viewModel.accountDetails.value?.currentCoin?:0.0
                }
            },)

            mAdapter?.apply {
                addLoadStateListener { loadState ->
                    binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                    else if (loadState.append.endOfPaginationReached) {
                        if (itemCount < 1) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
                    } else MultiStateView.ViewState.CONTENT

                }
                registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                    override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                        super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                        if ((fromPosition == 0 || toPosition == 0) && layoutManager.findFirstCompletelyVisibleItemPosition() == 0) {
                            layoutManager.scrollToPosition(0)
                        }
                    }
                })
            }

            binding.list.layoutManager = layoutManager
            binding.list.adapter = mAdapter
        }
    }

    private fun setup() {
//        viewModel.setCurrentTab(GiftType.PERSONAL)

//        binding.btnFun.setOnClickListener {
//            viewModel.setCurrentTab(GiftType.FUN)
//            (it as MaterialButton).isChecked = true
//        }
        binding.btnPersonal.setOnClickListener {
            viewModel.setCurrentTab(GiftType.PERSONAL)
            (it as MaterialButton).isChecked = true
        }
        binding.btnVip.setOnClickListener {
            viewModel.setCurrentTab(GiftType.VIP)
            (it as MaterialButton).isChecked = true
        }

        binding.btnBank.setOnClickListener {
            viewModel.setCurrentTab(GiftType.BANK)
            (it as MaterialButton).isChecked = true
        }

        viewModel.currentTab.value?.let {
            viewModel.giftType.postValue(it)
        }

        binding.buttonCoinToFlax.setOnClickListener {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
        }

    }

}