package com.app.messej.ui.home.publictab.podiums.create

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PodiumInviteViewModel(application: Application): AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)

    private val _podiumId = MutableLiveData<String?>(null)
    val podiumId: LiveData<String?> = _podiumId

    fun setPodiumId(id: String) {
        _podiumId.value =id
    }

    val invitationSent = LiveEvent<Boolean>()

    val inviteDears = MutableLiveData(false)
    val inviteFans = MutableLiveData(false)
    val inviteLikers = MutableLiveData(false)

    val enableSendButton: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            val hasShareToData = inviteDears.value == true || inviteFans.value == true || inviteLikers.value == true
            med.postValue(hasShareToData)
        }
        med.addSource(inviteDears) { update() }
        med.addSource(inviteFans) { update() }
        med.addSource(inviteLikers) { update() }
        med
    }

    fun clearSelection() {
        inviteDears.postValue(false)
        inviteFans.postValue(false)
        inviteLikers.postValue(false)
    }

    fun sendPodiumInvitation() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val result = podiumRepository.sendPodiumInvitation(podiumId.value!!, inviteDears = inviteDears.value == true, inviteFans = inviteFans.value == true,
                                                                   inviteLikers = inviteLikers.value == true)
                when(result){
                    is ResultOf.APIError -> {
                        invitationSent.postValue(false)
                    }
                    is ResultOf.Error -> {
                        invitationSent.postValue(false)
                    }
                    is ResultOf.Success -> {
                        invitationSent.postValue(true)
                    }
                }
            } catch (e: Exception){
                Log.d("PodiumLVM", "sendPodiumInvitation: error: ${e.message}")
            }
        }
    }
}