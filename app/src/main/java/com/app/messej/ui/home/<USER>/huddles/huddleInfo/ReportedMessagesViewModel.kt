package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.entity.HuddleReportedComment
import com.app.messej.data.model.entity.HuddleReportedMessage
import com.app.messej.data.model.entity.HuddleReportedMessageWithMedia
import com.app.messej.data.model.enums.ReportedTab
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.socket.repository.HuddleChatEventRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.chat.BaseChatDisplayViewModel
import com.app.messej.ui.chat.ChatMessageUIModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ReportedMessagesViewModel(application: Application): BaseChatDisplayViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())
    private val chatEventRepo = HuddleChatEventRepository

    private val _huddleId = MutableLiveData<Int?>()
    val huddleId: LiveData<Int?> = _huddleId

    fun setHuddleId(id: Int?, tab: ReportedTab) {
        _huddleId.value = id
        _currentTab.value = tab
    }

    private val _currentTab = MutableLiveData(ReportedTab.TAB_MESSAGES)
    val currentTab: LiveData<ReportedTab> = _currentTab.distinctUntilChanged()

    fun setCurrentTab(tab: ReportedTab) {
        _currentTab.value = tab
    }

    override val showChats = MutableLiveData<Boolean>(true)

    override val _chatList: LiveData<PagingData<ChatMessageUIModel>> = _currentTab.switchMap {
        it ?: return@switchMap null
        val huddleId = _huddleId.value?: return@switchMap null
        return@switchMap when (it) {
            ReportedTab.TAB_MESSAGES -> {
                huddleRepo.getHuddleReportedMessagePager(huddleId).liveData.map { pagingData: PagingData<HuddleReportedMessageWithMedia> ->
                    pagingData.map { msg ->
                        ChatMessageUIModel.ChatMessageModel(msg, false) as ChatMessageUIModel
                    }
                }.cachedIn(viewModelScope)
            }
            ReportedTab.TAB_COMMENTS -> {
                huddleRepo.getHuddleReportedCommentPager(huddleId).liveData.map { pagingData: PagingData<HuddleReportedComment> ->
                    pagingData.map { comment ->
                        ChatMessageUIModel.PostCommentModel(comment) as ChatMessageUIModel
                    }
                }.cachedIn(viewModelScope)
            }
        }

    }


    override fun getSenderForReply(msg: AbstractChatMessage): SenderDetails? {
        return null
    }

    val onDeleteMessage = LiveEvent<Boolean>()

    fun deleteMessage(msg: AbstractChatMessage,block: Boolean){
        viewModelScope.launch(Dispatchers.IO) {
            val result = chatEventRepo.deleteHuddleReportedMessage(listOf(msg as HuddleReportedMessage))
            if (block) {
                when (val result = huddleRepo.blockOrUnblockHuddleUser(msg.huddleId, Participant.ParticipantsActionTypes.BLOCK_HUDDLE_PARTICIPANT, arrayListOf(msg.sender))) {
                    is ResultOf.Success -> {
                        Log.d("API Status", "Success")
                    }
                    is ResultOf.APIError -> {
                        Log.d("API Status", "Error")
                    }
                    is ResultOf.Error -> {
                        Log.d("API Status", "Error")
                    }
                }
            }
            delay(500)
            onDeleteMessage.postValue(result)
        }
    }

    val onDeleteComment = LiveEvent<Boolean>()

    fun deleteReportedComment(huddleId: Int, postId: String?, commentId: String, senderBlocked: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.deleteReportedComment(huddleId, postId, commentId, senderBlocked, user.id)) {
                is ResultOf.Success -> {
                    Log.d("API Status", "Success")
                }
                is ResultOf.APIError -> {
                    Log.d("API Status", "Error")
                }

                is ResultOf.Error -> {
                    Log.d("API Status", "Error")
                }
            }
            delay(500)
            onDeleteComment.postValue(true)
        }
    }
}