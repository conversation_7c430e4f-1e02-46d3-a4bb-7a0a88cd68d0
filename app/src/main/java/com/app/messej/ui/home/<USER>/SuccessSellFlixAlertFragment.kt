package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentSuccessSellFlixAlertBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class SuccessSellFlixAlertFragment : BottomSheetDialogFragment() {
    private lateinit var binding: FragmentSuccessSellFlixAlertBinding
    private val args: SuccessSellFlixAlertFragmentArgs by navArgs()
    private val viewModel: SuccessSellFlixAlertViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_success_sell_flix_alert, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        // Check if payment method is for MENA region
        val isMenaRegion = args.paymentMethod.equals(getString(R.string.business_mena_office_identifier), ignoreCase = true)

        if (isMenaRegion) {
            // Display MENA region specific message
            binding.paymentMessage.text = getString(
                R.string.business_payout_mena_message,
                args.payoutDate
            )
        } else {
            // Display default message
            binding.paymentMessage.text = getString(R.string.sell_flax_applied_dialog)
        }

        binding.btnBottomSheetClose.setOnClickListener {
            dismiss()
            if (viewModel.user.premiumUser) {
                findNavController().navigateSafe(SuccessSellFlixAlertFragmentDirections.actionGlobalHomeBusinessFragment(destination = HomeBusinessFragment.TAB_STATEMENT))
            } else {
                val navOptions = NavOptions.Builder().setPopUpTo(R.id.nav_graph_home, inclusive = false) // Clear back stack to home graph root
                    .build()

                findNavController().navigateSafe(
                    SuccessSellFlixAlertFragmentDirections.actionSuccessSellFlixAlertFragmentToDealsStandAloneFragment(), navOptions = navOptions
                )
            }
        }
    }


}