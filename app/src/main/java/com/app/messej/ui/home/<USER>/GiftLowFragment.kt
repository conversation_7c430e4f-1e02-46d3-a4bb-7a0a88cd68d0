package com.app.messej.ui.home.gift

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.enums.CurrencyType
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentGiftLowBalancePremiumBinding
import com.app.messej.ui.home.HomeFragmentDirections
import com.app.messej.ui.home.settings.about.SettingsAboutFragmentDirections
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class GiftLowFragment : BottomSheetDialogFragment() {


    lateinit var binding: FragmentGiftLowBalancePremiumBinding
    private val giftCommonViewModel: GiftCommonViewModel by activityViewModels()
    val args: GiftLowFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_FlashCommentsBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_low_balance_premium, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = giftCommonViewModel
        binding.currencyType = args.currencyType
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        observe()
    }


    override fun onStart() {
        super.onStart()
        dialog?.let { dl ->
            dl.setCanceledOnTouchOutside(true)
        }
    }
    private fun observe() {

        giftCommonViewModel.giftListData.observe(viewLifecycleOwner){
            if(args.currencyType == CurrencyType.COINS)
                binding.coinBalance = it?.userGiftData?.totalPointsBalance.toString()
            else binding.coinBalance = it?.flax.toString()
        }
    }
    private fun setup() {
        giftCommonViewModel.getGiftList()
        binding.upgradeButton.setOnClickListener {
            if (args.currencyType == CurrencyType.COINS) findNavController().navigateSafe(HomeFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = true))
            else findNavController().navigateSafe(HomeFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = false))
        }
        binding.flashHatLabel.setOnClickListener {

            if (args.currencyType == CurrencyType.COINS) findNavController().navigateSafe(
                SettingsAboutFragmentDirections.actionGlobalPolicyFragment(DocumentType.GIFT_POLICY, false)
            )
            else
                findNavController().navigateSafe(
                    SettingsAboutFragmentDirections.actionGlobalPolicyFragment(DocumentType.PP_RULES_REGULATIONS, false)
                )
        }

    }

    override fun getTheme(): Int {
        return R.style.Widget_Flashat_Tribe_BottomSheet
    }

}