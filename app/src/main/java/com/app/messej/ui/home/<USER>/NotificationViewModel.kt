package com.app.messej.ui.home.notification

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.NotificationUpdateRequest
import com.app.messej.data.model.api.ReadNotificationResponse
import com.app.messej.data.model.entity.Notification
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.NotificationRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class NotificationViewModel(application: Application) : AndroidViewModel(application) {
    private var notificationRepo: NotificationRepository = NotificationRepository(application)
    private val dataRepo: FlashatDatastore = FlashatDatastore()

    val notificationList = notificationRepo.getNotificationListPager().liveData.cachedIn(viewModelScope)

    val apiResponse = LiveEvent<String?>()
    val user: CurrentUser get() = AccountRepository(getApplication()).user
    fun updateUnreadNotificationCount(){
        viewModelScope.launch {
            dataRepo.setNotificationCount(0)
        }
    }

    fun updateNotification(action: Boolean, id: Int, path: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<Notification> = notificationRepo.updateNotification(NotificationUpdateRequest(accept = action, associateObjId = id), path)) {
                is ResultOf.Success -> {
                }
                is ResultOf.APIError -> {
                    apiResponse.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
        }
    }

    fun deleteNotification(path: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<String> = notificationRepo.deleteNotification(path)) {
                is ResultOf.Success -> {
                    apiResponse.postValue(result.value)
                }
                is ResultOf.APIError -> {
                    apiResponse.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
        }
    }

    fun readNotification(path: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<ReadNotificationResponse> = notificationRepo.readNotification(path)) {
                is ResultOf.Success -> {

                }
                is ResultOf.APIError -> {
                    apiResponse.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
        }
    }

}