
package com.app.messej.ui.home.publictab.huddles.poll


import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.enums.PollType
import com.app.messej.databinding.ItemSchedulePollListBinding

class ScheduledPollsListAdapter(val listener: ActionListener, val pollsType: PollType, val  userManager: Boolean) : PagingDataAdapter<Poll, ScheduledPollsListAdapter.SchedulePollListViewHolder>(PollDiff) {


    interface ActionListener {
        fun onViewMoreClick(poll: Int)
        fun onEditClick(poll: Poll)
    }
    inner class SchedulePollListViewHolder(private val binding: ItemSchedulePollListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(poll: Poll) = with(binding) {
            model =poll
            pollType = pollsType
            isUserManager=userManager
            viewMore.setOnClickListener {
                listener.onViewMoreClick(poll.id)
            }
            imageRemoveOption.setOnClickListener {
                listener.onEditClick(poll)
            }
        }
    }

    override fun onBindViewHolder(holder: SchedulePollListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SchedulePollListViewHolder {
        val binding = ItemSchedulePollListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return SchedulePollListViewHolder(binding)
    }

    object PollDiff : DiffUtil.ItemCallback<Poll>(){
        override fun areItemsTheSame(oldItem: Poll, newItem: Poll) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: Poll, newItem: Poll) = oldItem == newItem

    }
}