package com.app.messej.ui.home.publictab.podiums.live

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewPropertyAnimator
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.appcompat.widget.PopupMenu
import androidx.compose.ui.platform.ComposeView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumChallengeSetupEvent
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.KnowledgeRaceData
import com.app.messej.data.model.api.podium.challenges.PenaltyKickTarget
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.AcceptDecline
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.ConFourGameStatus
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.TheaterCharge
import com.app.messej.data.utils.BeepUtils.playTone
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentPodiumLiveLectureBinding
import com.app.messej.databinding.ItemPodiumLikesContainerBinding
import com.app.messej.databinding.ItemPodiumSpeakerBinding
import com.app.messej.databinding.ItemPodiumSpeakerHeaderBinding
import com.app.messej.databinding.ItemPodiumSpeakerMainBinding
import com.app.messej.databinding.ItemPodiumWaitingSpeakerBinding
import com.app.messej.databinding.LayoutBoxGameInfoBinding
import com.app.messej.databinding.LayoutConfourGameInfoBinding
import com.app.messej.databinding.LayoutKnowledgeGameInfoBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutPenaltyGameInfoBinding
import com.app.messej.databinding.LayoutPodiumChatPausedEmptyBinding
import com.app.messej.databinding.LayoutStartChallengePopupBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPodiumPromoBoard
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumCoinInfoDialog
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumLikeInfoDialog
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumBoxChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumConFourChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumFlagChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumGiftChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumKnowledgeRaceChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLikesChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.awaitEnd
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumPenaltyChallengePresenter
import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoardModel
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard
import com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumChallengeParticipantFragment
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.CHALLENGE_RUNNING
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_ADMIN
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_MANAGER
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.UNKNOWN
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowChallengeResultVideo
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkAudioPermission
import com.app.messej.ui.utils.ViewUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.ScaleInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.google.gson.Gson
import com.kennyc.view.MultiStateView
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException
import kotlin.math.roundToInt


class PodiumLiveLectureFragment : PodiumLiveAbstractFragment() {
    override val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    val args: PodiumLiveLectureFragmentArgs by navArgs()

    override val podiumIdArg: String
        get() = args.podiumId

    override val enableScrollForTab: PodiumTab?
        get() = PodiumTab.fromInt(args.enableScrollForTab)

    private var mSpeakerAdapter: PodiumLiveSpeakersAdapter? = null
    private var mWaitingListAdapter: BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumWaitingSpeakerBinding>>? = null

    override lateinit var binding: FragmentPodiumLiveLectureBinding

    override val liveBindingElements = object: LiveBindingElements {

        override val liveChat: RecyclerView
            get() = binding.liveChat

        override fun showLocalVideoSurface(show: Boolean) {
            binding.mainScreen.showVideo = show
        }

        override fun onPodiumLoading(loading: Boolean) {
            binding.speakerListMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
            binding.header.podiumHeaderMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }

        override val likesContainer: ItemPodiumLikesContainerBinding
            get() = binding.likesContainer

        override val chatSend: MaterialButton
            get() = binding.chatSendButton

        override val actionPaidLike: MaterialButton
            get() = binding.actionLike

        override val actionShare: MaterialButton? = null

        override val actionDecorHolderTop: LinearLayoutCompat
            get() = binding.actionDecorHolderTop

        override val liveCounter: ViewGroup
            get() = binding.header.liveCounter

        override val scrollerLayout: SmartRefreshLayout
            get() = binding.refreshLayout

        override val anthemOverlay: ComposeView?
            get() = binding.anthemOverlay
    }

    private var challengeParticipantRequestTimer : Job? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_lecture, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initAdapter()
        setup()
        addObservers()
    }

    override fun onSecondResume() {
        viewModel.mainScreenSpeakerId.value?.let { mss ->
            Log.w("BANUBAPOD", "setupMainScreen from onResume")
            binding.mainScreen.setupMainScreen(mss)
        }
    }

    private fun setup() {
        setEmptyView()
        binding.header.podiumDp.setOnClickListener {
            ensureAnthemNotPlaying {
                showMoreMenu(it)
            }
        }
        binding.actionSpeak.setOnClickListener {
            ensureAnthemNotPlaying {
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (!checkUserCanSpeakByRating()) return@ensurePodiumCreateAllowed
                    val coinBalance = viewModel.podium.value?.audienceFee ?: 0
                    val haveEmpowermentToSpeak= viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true
                    val message = if (coinBalance == 0 || viewModel.podium.value?.isAdmin == true || viewModel.podium.value?.audienceFeePaid == true || haveEmpowermentToSpeak) getString(R.string.podium_action_request_to_speak_confirm_message)
                    else getString(R.string.podium_speaking_paid_confirmation_free, "$coinBalance")

                    showSpeakingConfirmationAlert(
                        haveEmpowermentToSpeak = haveEmpowermentToSpeak,
                        confirmMessage = message,
                        userCoins = viewModel.user.coinBalance.toInt(),
                        podiumSpeakingFee = coinBalance
                    ) {
                        checkAudioPermission(binding.root) {
                            viewModel.requestToSpeak()
                        }
                    }
                }
            }
        }
//        binding.actionSpeakActive.setOnClickListener {
//            confirmAction(
//                title = null, message = R.string.podium_action_cancel_speak_request_confirm_message
//            ) {
//                viewModel.cancelRequestToSpeak()
//            }
//        }
//        binding.header.exitButton.setOnClickListener {
//            onExit()
//        }

        binding.viewAllButton.setOnClickListener {
            ensureAnthemNotPlaying {
                val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumWaitingListAllFragment()
                findNavController().navigateSafe(action)
            }
        }

        binding.mainScreen.clickTarget.setOnClickListener {
            ensureAnthemNotPlaying {
                val item = viewModel.mainScreenSpeaker.value ?: return@ensureAnthemNotPlaying
                onSpeakerItemClick(item, it, true)
            }
        }

        binding.header.likeCounter.setOnClickListener {
            showPodiumLikeInfoDialog()
        }

        binding.header.coinCounter.setOnClickListener {
            showPodiumCoinInfoDialog()
        }

        setFragmentResultListener(PodiumChallengeParticipantFragment.CHALLENGE_INVITE_REQUEST_KEY) { _, bundle ->
            val time = bundle.getLong(PodiumChallengeParticipantFragment.CHALLENGE_INVITE_REQUEST_PAYLOAD)
            startTimerForConFourParticipants(time)
        }

        setupPodiumPromoBoard(binding.ticker)

        binding.header.yallaBtn.setOnClickListener {
            ensureAnthemNotPlaying {
                if (viewModel.podium.value?.allowYallaGuys != true || viewModel.iAmPartOfRunningChallenge.value == true) {
                    return@ensureAnthemNotPlaying
                }
                findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveLectureFragmentToYallaGuysListBottomSheetFragment(args.podiumId))
            }
        }

        binding.inputComment.apply {
            setOnClickListener {
                if (!checkUserCanCommentByRating()) {
                    clearFocus()
                    hideKeyboard()
                }
            }
        }

        binding.header.coinCounter.setOnClickListener {
            ensureAnthemNotPlaying {
                if (viewModel.canShowTheaterCharges()) {
                    findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionGlobalPodiumChargesBottomSheetFragment(args.podiumId, TheaterCharge.SPEAKER))
                } else {
                    showPodiumCoinInfoDialog()
                }
            }
        }
    }

    private fun onSpeakerItemClick(item: ActiveSpeakerUIModel, view: View, mainScreen: Boolean = false) {
        ensureAnthemNotPlaying {
            if (challengePresenter?.onSpeakerClick(item, view, mainScreen) == true) return@ensureAnthemNotPlaying
            val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumSpeakerActionsBottomSheetFragment(item.speaker.id)
            findNavController().navigateSafe(action)
        }
    }

    override fun ItemPodiumSpeakerMainBinding.onMainScreenSetup() {
        showCameraToggle = true
    }

    private fun initAdapter() {
        Log.d("PLF", "initAdapter: create new adapter")

        mSpeakerAdapter = PodiumLiveSpeakersAdapter(layoutInflater, mutableListOf(), object : PodiumLiveSpeakersAdapter.SpeakerListener {
            override fun onEmptySpeakZoneClick() {
                ensureAnthemNotPlaying {
                    ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                        if (viewModel.showRequestToSpeak.value == true) {
                            if (!checkUserCanSpeakByRating()) return@ensurePodiumCreateAllowed
                            val coinBalance = viewModel.podium.value?.audienceFee ?: 0
                            val isPaidSpeakingFee = viewModel.podium.value?.audienceFeePaid == true
                            val haveEmpowermentToSpeak= viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true
                            val message = if (coinBalance == 0 || viewModel.podium.value?.isAdmin == true || isPaidSpeakingFee || haveEmpowermentToSpeak) getString(R.string.podium_action_request_to_speak_confirm_message)
                            else getString(R.string.podium_speaking_paid_confirmation_free, "$coinBalance")

                            showSpeakingConfirmationAlert(
                                haveEmpowermentToSpeak = haveEmpowermentToSpeak,
                                confirmMessage = message,
                                userCoins = viewModel.user.coinBalance.toInt(),
                                podiumSpeakingFee = coinBalance
                            ) {
                                checkAudioPermission(binding.root) {
                                    viewModel.requestToSpeak()
                                }
                            }
                        }
                    }
                }
            }

            override fun decorateSpeakerTile(item: ActiveSpeakerUIModel, binding: ItemPodiumSpeakerBinding) {
                val didDecorate = challengePresenter?.decorateSpeakerTile(item, binding.challengeDecoration, false)==true
                binding.challengeDecoration.isVisible = didDecorate
                binding.userBubble.isVisible = !didDecorate
            }

            override fun onActiveSpeakerClick(activeSpeaker: ActiveSpeakerUIModel, view: View) {
                onSpeakerItemClick(activeSpeaker, view)
            }

            override fun setSpeakerTitle(speakerHeader: ItemPodiumSpeakerHeaderBinding, item: ActiveSpeakerUIModel) {
               speakerHeader.setSpeakerTitle(item)
            }

            override fun showAudioControls(speakerId: Int): Boolean {
                return viewModel.user.id == speakerId && viewModel.challengeRunning.value != true
            }

            override fun toggleMic(speakerId: Int, isMuted: Boolean) {
                viewModel.muteToggleSelf()
            }
        })

        binding.speakerList.apply {
            layoutManager = GridLayoutManager(context, 2).apply {}
            setHasFixedSize(true)
            adapter = mSpeakerAdapter
        }

        mWaitingListAdapter = object : BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumWaitingSpeakerBinding>>(R.layout.item_podium_waiting_speaker, mutableListOf()) {
            override fun convert(holder: BaseDataBindingHolder<ItemPodiumWaitingSpeakerBinding>, item: PodiumSpeaker) {
                holder.dataBinding?.apply {
                    speaker = item
                }
            }
        }.apply {
            animationEnable = true
            adapterAnimation = ScaleInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(PodiumSpeaker.getDiffer())
            setOnItemClickListener { adapter, _, position ->
                ensureAnthemNotPlaying {
                    val item = adapter.getItem(position) as PodiumSpeaker
                    val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumWaitListActionsBottomSheetFragment(item.id)
                    findNavController().navigateSafe(action)
                }
            }
        }
        binding.waitingList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mWaitingListAdapter
        }
    }

    @SuppressLint("RestrictedApi")
    private fun addObservers() {
        viewModel.mainScreenSpeakerId.observe(viewLifecycleOwner) {
            it?: return@observe
            binding.mainScreen.setupMainScreen(it)
        }
        viewModel.mainScreenSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: mainScreenSpeaker $it")
            setMainScreenSpeaker(binding.mainScreen, it)
        }
        viewModel.myVideoIsTurnedOn.observe(viewLifecycleOwner) {
            it?: return@observe
            Log.w("BANUBAPOD", "setupMainScreen from myVideoIsTurnedOn observe")
            if (!viewModel.iAmOnMainScreen()) return@observe
            binding.mainScreen.setupMainScreen(viewModel.user.id)
        }

        viewModel.speakersWithPlaceholders.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: speakersFullList ${it.size}")
            mSpeakerAdapter?.updateData(it)
        }

        viewModel.waiters.observe(viewLifecycleOwner) {
            mWaitingListAdapter?.apply {
                if (data.isEmpty() || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }
        viewModel.showRequestToSpeak.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: showRequestToSpeak $it")
        }

        viewModel.onAdminMuted.observe(viewLifecycleOwner) {
            //If muted
            if (it.first == true) {
                when (it.second) {
                    true -> showToast(R.string.podium_muted_by_manager_message)
                    false -> showToast(R.string.podium_muted_by_admin_message)
                    else -> {}
                }
            }
        }
        viewModel.chatDisabled.observe(viewLifecycleOwner) {
            binding.liveChatMultiStateView.viewState = if (it == null) MultiStateView.ViewState.CONTENT
            else when (it) {
                CHALLENGE_RUNNING -> {
                    liveChatEmptyView.edsEmptyMessage.setText(R.string.podium_challenge_disabled_text)
                    MultiStateView.ViewState.EMPTY
                }

                else -> MultiStateView.ViewState.CONTENT
            }

            binding.chatTextBoxMultiStateView.viewState = if (it == null) MultiStateView.ViewState.CONTENT
            else if (viewModel.iAmManager.value == true) MultiStateView.ViewState.CONTENT
            else when (it) {
                CHALLENGE_RUNNING -> MultiStateView.ViewState.CONTENT
                DISABLED_BY_MANAGER -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled_manager)
                    MultiStateView.ViewState.EMPTY
                }

                DISABLED_BY_ADMIN -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled_admin)
                    MultiStateView.ViewState.EMPTY
                }

                UNKNOWN -> {
                    chatTextBoxEmptyView.edsEmptyMessage.setText(R.string.podium_comments_disabled)
                    MultiStateView.ViewState.EMPTY
                }
            }
        }
        viewModel.waitListLoading.observe(viewLifecycleOwner) {
            binding.waitingListMultiStateView.viewState = if (it) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }

        viewModel.onNewSpeakRequest.observe(viewLifecycleOwner) {
            playTone(R.raw.podium_sound_two)
        }
        viewModel.onChallengeFacilitatorRequest.observe(viewLifecycleOwner) {
            showFacilitatorActionAlert(it.first,it.second) //pass the remaining time to show alert
        }

        viewModel.onChallengeContributorRequest.observe(viewLifecycleOwner) { pl ->
            viewModel.activeChallenge.value?.let { challenge ->
                val isContributorInSpeakers = viewModel.speakers.value?.any { it.speaker.id == pl.second.id }
                showContributorActionAlert(pl.first.contributorRequestedTimeRemaining.seconds, pl.second.coinsSpent?.roundToInt()?: 0, challenge.challengeType, challenge.contributorType ,isContributorInSpeakers==true)
            }
        }

        viewModel.onContributorRequestToSpeakersTimedOut.observe(viewLifecycleOwner) {
            navigateToFeeStatusBottomSheet()
        }

        viewModel.onContributorRequestResponded.observe(viewLifecycleOwner) {
            if (it.second) Toast.makeText(requireContext(), getString(R.string.podium_challenge_contributor_coins_debited, it.first.roundToInt().toString()), Toast.LENGTH_SHORT).show()
            else showToast(R.string.common_rejected)
        }

        viewModel.onContributorInsufficientBalance.observe(viewLifecycleOwner) {
            showInsufficientBalanceAlert(R.string.podium_challenge_insufficient_balance_text)
        }

        viewModel.onChallengeSetupEvent.observe(viewLifecycleOwner) {

            when(it) {
                is PodiumChallengeSetupEvent.FacilitatorDeclined -> {
                    showFacilitatorDeclinedAlert()
                }
                is PodiumChallengeSetupEvent.ContributorDeclined -> {
                    if (it.contributorType == ChallengeContributionType.CONTRIBUTOR) {
                        showContributorDeclinedAlert(it.contributor.name)
                    }
                }
                is PodiumChallengeSetupEvent.ContributorConfirmed -> {
                    if (it.contributorType == ChallengeContributionType.CONTRIBUTOR
                        && viewModel.activeChallenge.value?.facilitator?.id == viewModel.user.id) {
                        showContributorConfirmedAlert()
                    }
                }
                else -> {

                }
            }
            challengePresenter?.onSetupEvent(it)
        }

        viewModel.onSingleContributorTimedOut.observe(viewLifecycleOwner) {
            if (it) showContributorTimedOutAlert()
        }

        viewModel.activeChallengeStatus.observe(viewLifecycleOwner) {
            it?: return@observe
            Log.w("PCP", "$challengePresenter observe PLF: activeChallengeStatus $it")
            challengePresenter?.onChallengeStatusChange(it)
        }

        viewModel.onChallengeScoreUpdate.observe(viewLifecycleOwner) {
            challengePresenter?.onScoreUpdate(it)
        }
        viewModel.flagChallengeQuestions.observe(viewLifecycleOwner) {
            Log.w("PCP", "observe: flagChallengeQuestions ${it.size} | ${challengePresenter?.javaClass?.simpleName}")
            if (it.isNotEmpty()) {
                (challengePresenter as? PodiumFlagChallengePresenter)?.apply {
                    onFlagChallengeQuestions(it)
                }
            }
        }

        viewModel.onChallengeParticipantExit.observe(viewLifecycleOwner) {
            challengePresenter?.setParticipantExit(it)
        }

        viewModel.activeChallengeId.observe(viewLifecycleOwner) {
            viewModel.activeChallenge.value?.also { cl ->
                setupChallenge(cl)
            } ?: run {
                cleanupChallenge()
            }
        }

        viewModel.challengeRunning.observe(viewLifecycleOwner) {
            Log.w("PLF", "observe:challengeRunning: $it ")
        }

        viewModel.iAmPartOfRunningChallenge.observe(viewLifecycleOwner) {
            Log.w("PLF", "observe:iAmPartOfRunningChallenge: $it ")
        }

        viewModel.navigateToChallengeSetup.observe(viewLifecycleOwner) {
            if (it) findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(args.podiumId, null))
            else {
                val activeChallenge = viewModel.activeChallenge.value?: return@observe
                if (activeChallenge.challengeType in listOf(ChallengeType.CONFOUR,ChallengeType.PENALTY,ChallengeType.BOXES, ChallengeType.KNOWLEDGE) && activeChallenge.challengeInvitationStatus == PodiumChallenge.ChallengeScreenStatus.WAITING){
                    findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToConFourParticipantStatusBottomSheetFragment())
                } else findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumChallengeInfoFragment())
            }
        }
//        viewModel.onChallengeClosed.observe(viewLifecycleOwner) {
//            showToast(R.string.podium_challenge_deleted_text)
//        }

        viewModel.onFacilitatorRequestResponded.observe(viewLifecycleOwner) {
            if (it) {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(args.podiumId, viewModel.activeChallengeId.value?.first))
                }
        }

        viewModel.onChallengeSpeakerInvite.observe(viewLifecycleOwner) {
            confirmChallengeSpeakerInvite(it)
        }

        viewModel.onChallengeSpeakerInviteResponse.observe(viewLifecycleOwner) {
            if (it.first == AcceptDecline.DECLINE) {
                when(viewModel.activeChallenge.value?.challengeType) {
                    ChallengeType.CONFOUR -> {
                        Toast.makeText(requireContext(), getString(R.string.podium_challenge_invite_declined_confour_message, it.second), Toast.LENGTH_SHORT).show()
                    }
                    else -> {
                        Toast.makeText(requireContext(), getString(R.string.podium_challenge_invite_declined_message, it.second), Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }

        viewModel.onChallengeStarted.observe(viewLifecycleOwner) {
            if (it) showToast(R.string.podium_challenge_started)
        }

        viewModel.participantsCountNotMet.observe(viewLifecycleOwner) {
//            Toast.makeText(
//                requireContext(), resources.getString(
//                    R.string.podium_challenge_participants_count_not_met,it.first.toString(),it.last.toString()
//                ), Toast.LENGTH_SHORT
//            ).show()
            showToast(R.string.podium_challenge_minimum_participant_not_met)
        }

        viewModel.startTimerForConFourParticipantsSheet.observe(viewLifecycleOwner) {
            startTimerForConFourParticipants(it)
        }

        viewModel.onConnectFourChallengeCoinDropped.observe(viewLifecycleOwner) {
            (challengePresenter as? PodiumConFourChallengePresenter)?.onCoinDropped(it)
        }
        viewModel.onBoxChallengeLineDrawn.observe(viewLifecycleOwner) {
            (challengePresenter as? PodiumBoxChallengePresenter)?.onLineDrawn(it)
        }

        viewModel.onUserJoined.observe(viewLifecycleOwner) {
            challengePresenter?.onUserJoined(it)
        }

        viewModel.onMinimumParticipantsAccepted.observe(viewLifecycleOwner) {
            if (it) {
                challengeParticipantRequestTimer?.let { timerJob->
                    if(timerJob.isActive) {
                        timerJob.cancel()
                    }
                }
                findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToConFourParticipantStatusBottomSheetFragment())
            }
        }

        viewModel.onPenaltyPlayerReady.observe(viewLifecycleOwner) {
            (challengePresenter as? PodiumPenaltyChallengePresenter)?.onPlayerReady(it)
        }

        viewModel.onPenaltyStartTurn.observe(viewLifecycleOwner) {
            (challengePresenter as? PodiumPenaltyChallengePresenter)?.onStartTurn(it)
        }

        viewModel.onPenaltySelectTarget.observe(viewLifecycleOwner) {
            (challengePresenter as? PodiumPenaltyChallengePresenter)?.onSelectTarget(it)
        }

        viewModel.onPenaltyKickResult.observe(viewLifecycleOwner) {
            (challengePresenter as? PodiumPenaltyChallengePresenter)?.onKickResult(it)
        }

        viewModel.onYallaCompetitorRequest.observe(viewLifecycleOwner) {
            yallaPopup = showYallaGuysPopup(getString(R.string.podium_yalla_guys_competitor_request,it.player.name,getString(it.challenge.gameType.resId),it.challenge.prize.toString()))
            yallaPopupChallengeId = it.challenge.challengeId
        }

        viewModel.activeChallenge.observe(viewLifecycleOwner) {
            it?: return@observe
            challengePresenter?.onUpdateChallengeAndScores(it)
            if (it.challengeId == yallaPopupChallengeId && it.hasStarted) {
                clearYallaPopup()
            }
        }

        viewModel.onYallaNotification.observe(viewLifecycleOwner) {
            showYallaGuysPopup(getString(R.string.podium_yalla_guys_notification))
        }

        viewModel.networkError.observe(viewLifecycleOwner) {
            showToast(R.string.connection_error)
        }
        viewModel.isLikeLoading.observe(viewLifecycleOwner){
            Log.d("LIKE_ENABLED", "observe: $it")
        }
        viewModel.knowledgeRaceSettings.observe(viewLifecycleOwner) {
            (challengePresenter as? PodiumKnowledgeRaceChallengePresenter)?.updateKnowledgeRaceSettings(it)
        }

        viewModel.onKnowledgeRaceElimination.observe(viewLifecycleOwner) {
            (challengePresenter as? PodiumKnowledgeRaceChallengePresenter)?.onUserEliminated(it)
        }
    }

    private var yallaPopup: AlertDialog? = null
    private var yallaPopupChallengeId: String? = null

    private fun clearYallaPopup() {
        if (yallaPopup?.isShowing==true) {
            yallaPopup?.dismiss()
        }
        yallaPopup = null
        yallaPopupChallengeId = null
    }

    private fun showYallaGuysPopup(message: String): AlertDialog {
        return showFlashatDialog {
            setMessage(message)
            setConfirmButton(R.string.podium_yalla_guys, R.drawable.ic_yalla_gamepad_square, tint = false, iconPadding = false) {
                findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveLectureFragmentToYallaGuysListBottomSheetFragment(args.podiumId))
                true
            }
        }
    }

    override fun setMainScreenSpeaker(binding: ItemPodiumSpeakerMainBinding, item: ActiveSpeakerUIModel?) {
        super.setMainScreenSpeaker(binding, item)
        item?.let {
            (challengePresenter?.decorateSpeakerTile(it, binding.challengeDecoration, true) ==true).apply {
                binding.challengeDecoration.isVisible = this
                binding.userBubble.isVisible = !this
            }
        }
    }

    private lateinit var liveChatEmptyView: LayoutListStateEmptyBinding
    private lateinit var chatTextBoxEmptyView: LayoutPodiumChatPausedEmptyBinding

    fun setEmptyView() {
        val emptyView: View = binding.liveChatMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        liveChatEmptyView = LayoutListStateEmptyBinding.bind(emptyView).apply {
            prepare(message = R.string.podium_comments_disabled)
        }

        val textBoxEmptyView: View = binding.chatTextBoxMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        chatTextBoxEmptyView = LayoutPodiumChatPausedEmptyBinding.bind(textBoxEmptyView)
    }

    private fun showAlertOnLiveChallenge(res: Int) {
        MaterialAlertDialogBuilder(requireContext()).setMessage(getString(res)).setCancelable(false).setPositiveButton(getString(R.string.common_ok)) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }

    override fun confirmLeave(confirm: () -> Unit) {
        if (viewModel.iAmPartOfRunningChallenge.value == true) {
            showAlertOnLiveChallenge(R.string.podium_live_challenge_leave_alert)
        } else super.confirmLeave(confirm)
    }

    override fun canExit(): Boolean {
        return if (viewModel.iAmPartOfRunningChallenge.value == true) {
            showAlertOnLiveChallenge(R.string.podium_live_challenge_exit_alert)
            false
        } else true
    }

    override fun onExit() {
        if (viewModel.challengeActive.value == true && viewModel.iAmPartOfRunningChallenge.value == true) {
            showAlertOnLiveChallenge(R.string.podium_live_challenge_close_alert)
            return
        }
        super.onExit()
    }

    override fun confirmClose(confirm: () -> Unit) {
        if (viewModel.challengeActive.value == true) {
            showAlertOnLiveChallenge(R.string.podium_live_challenge_close_alert)
        } else super.confirmClose(confirm)
    }

    override fun showMoreMenu(v: View): PopupMenu {
        viewModel.activeChallenge.value?.let { ch ->
            if (ch.challengeType in setOf(ChallengeType.CONFOUR, ChallengeType.PENALTY,ChallengeType.BOXES) && ch.hasStarted && viewModel.iAmPartOfRunningChallenge.value == true) {
                return showPlayerChallengeMenu(v)
            }
        }

        val iAmFacilitator = viewModel.activeChallenge.value?.facilitator?.id == viewModel.user.id && viewModel.activeChallenge.value?.facilitator?.requestAccepted == true

        return super.showMoreMenu(v).apply {
            menu.apply {
                findItem(R.id.action_challenges).isVisible = (viewModel.iAmManager.value == true || iAmFacilitator) && viewModel.podium.value?.isPrivate != true
                findItem(R.id.action_yalla_pause).apply {
                    Log.w("PLLF", "showMoreMenu: allowYallaGuys: ${viewModel.podium.value?.allowYallaGuys}")
                    isVisible = viewModel.iAmElevated.value == true && viewModel.podium.value?.isPrivate!=true
                    setTitle(
                        if (viewModel.podium.value?.allowYallaGuys == false) R.string.podium_action_yalla_resume
                        else R.string.podium_action_yalla_pause
                    )
                }

                findItem(R.id.action_yalla_guys).isVisible =
                        viewModel.podium.value?.allowYallaGuys == true &&
                        viewModel.podium.value?.isPrivate!=true &&
                        viewModel.iAmPartOfRunningChallenge.value!=true
            }
        }
    }

    override fun onMenuClick(menuItem: MenuItem): Boolean {
        return when (menuItem.itemId) {
            R.id.action_yalla_pause -> {
                val pod = viewModel.podium.value?: return false
                Log.w("PLLF", "onMenuClick: allowYallaGuys: ${pod.allowYallaGuys}")
                ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    if (pod.allowYallaGuys == false) {
                        showFlashatDialog {
                            setMessage(R.string.podium_yalla_guys_resume_confirm)
                            setConfirmButton(R.string.common_confirm) {
                                viewModel.pauseResumeYallaGuys(false)
                                true
                            }
                        }
                    } else {
                        showFlashatDialog {
                            setMessage(R.string.podium_yalla_guys_pause_confirm)
                            setConfirmButton(R.string.common_confirm) {
                                viewModel.pauseResumeYallaGuys(true)
                                true
                            }
                        }
                    }
                }
                true
            }
            R.id.action_yalla_guys -> {
                viewModel.podiumId.value?.let {
                    findNavController().navigate(PodiumLiveLectureFragmentDirections.actionPodiumLiveLectureFragmentToYallaGuysListBottomSheetFragment(it))
                }
                true
            }
            else -> super.onMenuClick(menuItem)
        }
    }

    private fun showPlayerChallengeMenu(v: View): PopupMenu {
        val popup = PopupMenu(requireContext(), v)
        popup.menuInflater.inflate(R.menu.menu_podium_live_player_challenge_actions, popup.menu)
        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_exit_challenge -> {
                    confirmAction(
                        message = getString(R.string.podium_confour_exit_challenge_confirm_message),
                        positiveTitle = R.string.common_confirm,
                        negativeTitle = R.string.common_cancel
                    ) {
                        viewModel.onExitChallenge()
                    }
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
        this.popup = popup
        return popup
    }

    override fun toLiveChatActions(userId: Int) {
        val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumLiveChatActionsBottomSheetFragment(userId)
        findNavController().navigateSafe(action)
    }

    override fun toLiveUsersList() {
        val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumLiveUsersListBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toAdminList() {
        val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumAdminsBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toRestrictedUsers() {
        val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumBlockedUsersBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override fun toInvitations() {
        val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumInviteBottomSheetFragment(podiumId = podiumIdArg)
        findNavController().navigateSafe(action)
    }

    private fun showFacilitatorActionAlert(challengeType: ChallengeType, timeRemaining: Long) {
        if (timeRemaining <= 0L) return
        var timerJob: Job? = null
        playTone(R.raw.podium_sound_two)
        val message = when (challengeType) {
            ChallengeType.MAIDAN -> "" //Not applicable
            ChallengeType.LIKES -> getString(R.string.podium_challenge_facilitator_alert_likes)
            ChallengeType.GIFTS -> getString(R.string.podium_challenge_facilitator_alert_gifts)
            ChallengeType.PENALTY -> getString(R.string.podium_challenge_facilitator_alert_penalty)
            ChallengeType.CONFOUR -> getString(R.string.podium_challenge_facilitator_alert_confour)
            ChallengeType.FLAGS -> getString(R.string.podium_challenge_facilitator_alert_flags)
            ChallengeType.BOXES -> getString(R.string.podium_challenge_facilitator_alert_box)
            ChallengeType.KNOWLEDGE -> getString(R.string.podium_challenge_facilitator_alert_knowledge)
        }
        val alert = showFlashatDialog {
            setMessage(message)
            setCancelable(false)
            setConfirmButton(R.string.podium_set_challenge,R.drawable.ic_trophy, iconPadding = false) {
                timerJob?.cancel()
                viewModel.respondToFacilitatorRequest(true)
                true
            }
            setCloseButton(R.string.common_cancel, R.drawable.ic_close) {
                timerJob?.cancel()
                viewModel.respondToFacilitatorRequest(false)
                true
            }
        }

        timerJob = DateTimeUtils.countDownTimerFlow(timeRemaining*1000)
            .onEach {  }
            .onCompletion { cause ->
                if (cause is CancellationException) {
                    // Flow was canceled
                    println("Flow was canceled")
                } else {
                    // Flow completed normally
                    if (alert.isShowing) {
                        alert.dismiss()
                        viewModel.respondToFacilitatorRequest(false)
                    }
                }
            }
            .launchIn(lifecycleScope)
    }

    private fun showFacilitatorDeclinedAlert() {
        if(viewModel.iAmManager.value != true) return
        confirmAction(
            message = R.string.podium_challenge_facilitator_declined,
            positiveTitle = R.string.common_proceed,
            negativeTitle = R.string.common_cancel
        ) {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(args.podiumId, viewModel.activeChallengeId.value?.first))
        }
    }

    private fun showContributorActionAlert(timeRemaining: Long, coins: Int, challengeType: ChallengeType, contributionType: ChallengeContributionType?, isContributorInSpeakers: Boolean) {
        if (timeRemaining <= 0L) return
        var timerJob: Job? = null
        playTone(R.raw.podium_sound_two)
        val message = when (contributionType) {
            ChallengeContributionType.CONTRIBUTOR -> getString(R.string.podium_challenge_single_contributor_alert_text, coins, getString(challengeType.resId))
            else -> if (isContributorInSpeakers) getString(R.string.podium_challenge_contributor_alert_text, coins, getString(challengeType.resId))
            else getString(R.string.podium_challenge_contributor_invite_live_users, getString(challengeType.resId), coins)
        }
        val alert = MaterialAlertDialogBuilder(requireContext()).setMessage(message).setCancelable(false).setPositiveButton(getString(R.string.common_accept)) { dialog, _ ->
                viewModel.respondToContributorRequest(true)
                dialog.dismiss()
                timerJob?.cancel()
            }.setNegativeButton(getString(R.string.common_cancel)) { dialog, _ ->
                viewModel.respondToContributorRequest(false)
                dialog.dismiss()
                timerJob?.cancel()
            }.show()

        timerJob = DateTimeUtils.countDownTimerFlow(timeRemaining * 1000).onCompletion { cause ->
            if (cause is CancellationException) {
                // Flow was canceled
                println("Flow was canceled")
            } else {
                // Flow completed normally
                if (alert.isShowing) {
                    alert.dismiss()
                }
            }
        }.launchIn(lifecycleScope)
    }

    private fun showContributorDeclinedAlert(name: String) {
        if (viewModel.activeChallenge.value?.facilitator?.id == viewModel.user.id) {
            MaterialAlertDialogBuilder(requireContext())
                .setMessage(getString(R.string.podium_challenge_contributor_request_declined, name))
                .setCancelable(false)
                .setPositiveButton(getString(R.string.common_proceed)) { dialog, _ ->
                    viewModel.podium.value?.let {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(it.id, viewModel.activeChallengeId.value?.first))
                    }
                    dialog.dismiss()
                }
                .setNegativeButton(getString(R.string.common_cancel)) { dialog, _ ->
                    dialog.dismiss()
                }.show()
        }
    }

    private fun showContributorTimedOutAlert() {
        if (viewModel.activeChallenge.value?.facilitator?.id == viewModel.user.id) {
            MaterialAlertDialogBuilder(requireContext())
                .setMessage(R.string.podium_challenge_contributor_request_time_out)
                .setCancelable(false)
                .setPositiveButton(getString(R.string.common_proceed)) { dialog, _ ->
                    viewModel.podium.value?.let {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(it.id, viewModel.activeChallengeId.value?.first))
                    }
                    dialog.dismiss()
                }
                .setNegativeButton(getString(R.string.common_cancel)) { dialog, _ ->
                    dialog.dismiss()
                }.show()
        }
    }

    private fun showContributorConfirmedAlert() {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutStartChallengePopupBinding>(layoutInflater, R.layout.layout_start_challenge_popup, null, false)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.actionStart.setOnClickListener {
                viewModel.startPodiumChallenge()
                dismiss()
            }
        }
    }

    private fun navigateToFeeStatusBottomSheet() {
        if (viewModel.activeChallenge.value?.contributorType == ChallengeContributionType.SPEAKERS)
            findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToChallengeFeeStatusBottomSheetFragment())
    }

    private var challengePresenter: PodiumChallengePresenter? = null

    private fun setupChallenge(challenge: PodiumChallenge) {
        try {
            challengePresenter?.cleanup()
            binding.challengeHolder.isVisible = true
//            binding.collapseActions = true
            Log.w("PCP", "observe: setting up challenge: ${challenge.challengeType}")
            val holder = binding.layoutPodiumChallengeBoard.contentHolder

            val listener = object : PodiumChallengePresenter.ChallengeEventListener {
                override fun allowCustomBoard(allow: Boolean) {
                    binding.layoutPodiumChallengeBoard.allowCustomBoard = allow
                }

                override fun getLiveViewModel() = viewModel
                override fun getLayoutInflater(): LayoutInflater = layoutInflater
                override fun getResources() = resources
                override fun getContext() = requireContext()

                @SuppressLint("NotifyDataSetChanged")
                override fun onRequireSpeakerTileDecor() {
                    mSpeakerAdapter?.notifyDataSetChanged()
                    viewModel.mainScreenSpeaker.value?.let {
                        setMainScreenSpeaker(binding.mainScreen,it)
                    }
                }
                override fun showChallengeResultVideo(winner: Boolean) {
                    downloadAndShowChallengeResultVideo(winner)
                }
                override fun onCloseChallenge() {
                    viewModel.closePodiumChallenge()
                }
                override fun getChatOverlay(): ConstraintLayout {
                    return binding.chatOverlay
                }
                override fun showSpeakerOverlay(show: Boolean, aspect: Float): ConstraintLayout {
                    return toggleSpeakerOverlay(show,aspect)
                }
                override fun showChatOverlay(show: Boolean): ConstraintLayout {
                    return binding.chatOverlay.apply {
                        isVisible = show
                        if (!show) removeAllViews()
                    }
                }

                override fun showChatInputOverlay(show: Boolean): ConstraintLayout {
                    return binding.chatInputOverlay.apply {
                        isVisible = show
                        if (!show) removeAllViews()
                    }
                }
            }
            listener.allowCustomBoard(false)
            val presenter = when (challenge.challengeType) {
                ChallengeType.LIKES -> PodiumLikesChallengePresenter(holder, challenge, listener)
                ChallengeType.GIFTS -> {
                    viewModel.enableVideo(false)
                    PodiumGiftChallengePresenter(holder, challenge, listener, object : PodiumGiftChallengePresenter.GiftChallengeEventListener {
                        override fun onSendGift(speaker: PodiumSpeaker) {
                            ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.ParticipateInChallenge) {
                                if(viewModel.user.citizenship.isGolden) return@ensurePodiumCreateAllowed
                                viewModel.activeChallenge.value?.let { pChallenge->
                                    findNavController().navigateSafe(
                                        NavGraphHomeDirections.actionGlobalGiftFragment(
                                            receiverId = speaker.id,
//                                        podiumId = pChallenge.podiumId,
                                            giftContext = GiftContext.GIFT_PODIUM,
                                            challengeId = pChallenge.challengeId,
                                            giftContextId = pChallenge.podiumId,
                                            challengeEndTimeStampUTC = pChallenge.parsedEndTime?.toEpochSecond()?: 0L
                                        )
                                    )
                                }
                            }
                        }
                        override fun onRequireUpgrade() {
                            val builder = MaterialAlertDialogBuilder(requireContext()).apply {
                                setMessage(R.string.podium_challenge_send_gift_upgrade_prompt)
                                setPositiveButton(getString(R.string.common_upgrade)) { dialog, _ ->
                                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
                                    dialog.dismiss()
                                }
                                setNegativeButton(R.string.common_cancel) { dialog, _ ->
                                    dialog.dismiss()
                                }
                            }
                            builder.show()
                        }
                        override fun onShowSupporters() {
                            val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToPodiumGiftChallengeSupportersBottomSheetFragment(challenge.podiumId, challenge.challengeId)
                            findNavController().navigateSafe(action)
                        }

                    })
                }

                ChallengeType.FLAGS -> PodiumFlagChallengePresenter(holder, challenge, listener).apply {
                    viewModel.enableVideo(false)
                    onFlagChallengeQuestions(viewModel.flagChallengeQuestions.value.orEmpty())
                }
                ChallengeType.CONFOUR -> {
                    viewModel.enableVideo(false)
                    PodiumConFourChallengePresenter(holder, challenge, listener, object : PodiumConFourChallengePresenter.ConFourChallengeEventListener {
                        override fun showHowToPlayOverlay(millis: Long) {
                            val binding = LayoutConfourGameInfoBinding.inflate(layoutInflater,null,false)
                            showHowToPlayOverlay(binding.root,millis,binding.confourTimerTv)
                        }
                        override fun submitCoinDrop(slot: ConnectFourBoard.DropPoint, player: ChallengePlayer, gameStatus: ConFourGameStatus?) {
                            viewModel.dropConFourToken(slot, player, gameStatus)
                        }
                        override fun sendTimeOut(player: ChallengePlayer, currentPlayerId: Int) {
                            viewModel.sendConFourTimedOut(player, currentPlayerId)
                        }
                        override fun playDropSound() {
                            playTone(R.raw.confour_token_drop)
                        }
                    })
                }
                ChallengeType.PENALTY -> {
                    viewModel.enableVideo(false)
                    PodiumPenaltyChallengePresenter(holder, challenge, listener, object: PodiumPenaltyChallengePresenter.PenaltyChallengeEventListener {
                        override fun showHowToPlayOverlay(millis: Long) {
                            val binding = LayoutPenaltyGameInfoBinding.inflate(layoutInflater,null,false)
                            showHowToPlayOverlay(binding.root,millis,binding.confourTimerTv)
                        }
                        override fun onSelectTarget(userId: Int, target: PenaltyKickTarget) {
                            viewModel.setPenaltyTarget(userId, target)
                        }
                        override fun setPlayerReady(userId: Int) {
                            viewModel.setPenaltyPlayerReady(userId)
                        }
                    })
                }
                ChallengeType.BOXES -> PodiumBoxChallengePresenter(holder, challenge, listener, object: PodiumBoxChallengePresenter.BoxChallengeEventListener {
                    override fun showHowToPlayOverlay(millis: Long) {
                        val binding = LayoutBoxGameInfoBinding.inflate(layoutInflater,null,false)
                        showHowToPlayOverlay(binding.root,millis,binding.confourTimerTv)
                    }
                    override fun submitLine(
                        line: BoxChallengeBoardModel.Line,
                        player: ChallengePlayer,
                        gameStatus: ConFourGameStatus?,
                    ) {
                        viewModel.drawBoxChallengeLine(line,player,gameStatus)
                    }
                    override fun sendTimeOut(player: ChallengePlayer, currentPlayerId: Int) {
                        viewModel.sendBoxChallengeTimedOut(player,currentPlayerId)
                    }

                })
                ChallengeType.KNOWLEDGE -> PodiumKnowledgeRaceChallengePresenter(holder, challenge, listener, object: PodiumKnowledgeRaceChallengePresenter.KnowledgeRaceEventListener {
                    override fun showHowToPlayOverlay(millis: Long) {
                        val binding = LayoutKnowledgeGameInfoBinding.inflate(layoutInflater,null,false)
                        showHowToPlayOverlay(binding.root,millis,binding.confourTimerTv)
                    }
                    override fun submitAnswer(question: KnowledgeRaceData.Question, answer: KnowledgeRaceData.Option) {
                        viewModel.submitKnowledgeRaceAnswer(question, answer)
                    }

                    override fun showSettingsBottomSheet(knowledgeRaceSettings: PodiumKnowledgeRaceChallengePresenter.KnowledgeRaceSettings) {
                        viewModel.updateSettings(
                            language = knowledgeRaceSettings.language,
                            music = knowledgeRaceSettings.music,
                            soundEffects = knowledgeRaceSettings.soundEffects
                        )
                        val action = PodiumLiveLectureFragmentDirections.actionPodiumLiveLectureFragmentToKnowledgeRaceSettingsBottomSheetFragment()
                        findNavController().navigateSafe(action)
                    }

                    override fun showEliminationWarning() {
                        showFlashatDialog {
                            setMessage(R.string.podium_knowledge_race_elimination_warning)
                            setConfirmButtonVisible(false)
                        }
                    }
                })
                ChallengeType.MAIDAN -> return //Not Applicable
            }

            challengePresenter = presenter
            Log.d("PCP", "setupChallenge $challengePresenter")

        } catch (e: Exception) {
            Firebase.crashlytics.log(challenge.toString())
            Firebase.crashlytics.recordException(e)
            Log.d("PLF", "setupChallenge exception: ${e.message}")
        }
    }

    private fun showHowToPlayOverlay(view: View, millis: Long, timerText: AppCompatTextView) {
        val materialAlertDialog =
            MaterialAlertDialogBuilder(requireContext(), android.R.style.Theme_DeviceDefault_NoActionBar_Fullscreen).setView(view).setCancelable(true).create()
        materialAlertDialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        materialAlertDialog.window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT)
        materialAlertDialog.show()
        DateTimeUtils.countDownTimerFlow(millis).onEach {
            timerText.text = it.toString()
        }.onCompletion {
            if (materialAlertDialog.isShowing) materialAlertDialog.dismiss()
        }.launchIn(lifecycleScope)
    }

    private fun toggleSpeakerOverlay(show: Boolean, aspect: Float): ConstraintLayout {
        Log.d("PCP", "toggleSpeakerOverlay: show: $show, aspect: $aspect")

        viewModel.setShowingSpeakerOverlay(show)

        fun View.animateTranslation(to: Float): ViewPropertyAnimator {
            this.animate().apply {
                interpolator = AccelerateDecelerateInterpolator()
                duration = 400
                translationX(ViewUtils.localeAwareTranslation(to))
                start()
                return this
            }
        }
        val translationX = resources.displayMetrics.widthPixels.toFloat()/2
        binding.speakersGroup.apply {
            animateTranslation(if (show) translationX else 0f)
            isVisible = !show
        }
        binding.hasChallengeOverlay = show
        binding.speakersCollapsed = show
        binding.actionSpeakersToggle.apply {
            animateTranslation(if (show) translationX else 0f)
            isVisible = show
            setOnClickListener {
                binding.speakersCollapsed?.let { was ->
                    val collapse = !was
                    binding.speakersCollapsed = collapse
                    if (collapse) {
                        lifecycleScope.launch {
                            binding.speakersGroup.animateTranslation(translationX)
                            animateTranslation(translationX).awaitEnd()
                            binding.speakersGroup.isVisible = false
                        }
                    } else {
                        binding.speakersGroup.isVisible = true
                        binding.speakersGroup.animateTranslation(0f)
                        animateTranslation(0f)
                    }

                }
            }
        }

        return binding.speakerChallengeOverlay.apply {
            if (show && aspect > 0f) {
                (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = (aspect).toString()
                isVisible = true
            } else {
                removeAllViews()
                isVisible = false
            }
        }
    }

    /*
    All references to the fragment's view should be removed at onDestroyView().
    onDestroy() is called only if the FragmentManager is destroyed(which does not happen in case of navigation to ChallengeInfoFragment).
    Therefore cleanup challenge presenter in onDestroyView().
    https://developer.android.com/guide/fragments/lifecycle#fragment_created_and_view_destroyed
     */
    override fun onDestroyView() {
        super.onDestroyView()
        Log.w("PCP", "$challengePresenter onDestroyView")
        cleanupChallenge()
    }

    private fun cleanupChallenge() {
        Log.w("PCP", "$challengePresenter cleanupChallenge")
        binding.challengeHolder.isVisible = false
//        binding.collapseActions = false
        challengePresenter?.cleanup()
        challengePresenter = null
    }

    override fun toBuyCamera(buy: Boolean) {
        findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToBuyCameraBottomSheetFragment(buy))
    }

    private fun confirmChallengeSpeakerInvite(challengeData: Pair<ChallengeType, Long>) {
        try {
            val message = when (challengeData.first) {
                ChallengeType.CONFOUR -> getString(R.string.podium_challenge_invite_message_confour)
                ChallengeType.PENALTY -> getString(R.string.podium_challenge_invite_message_penalty)
                ChallengeType.BOXES -> getString(R.string.podium_challenge_invite_message_box)
                ChallengeType.KNOWLEDGE -> getString(R.string.podium_challenge_invite_message_knowledge)
                ChallengeType.LIKES -> getString(R.string.podium_challenge_invite_message_likes)
                ChallengeType.GIFTS -> getString(R.string.podium_challenge_invite_message_gifts)
                ChallengeType.FLAGS -> getString(R.string.podium_challenge_invite_message_flags)
                else -> getString(R.string.podium_challenge_confirm_invite_message)
            }

            val dialog = showFlashatDialog {
                setMessage(message)
                setCancelable(false)
                setConfirmButton(getString(R.string.common_accept),R.drawable.ic_chat_liked) {
                    viewModel.replyChallengeInvite(accept = true)
                    true
                }
                setCloseButton(R.string.common_cancel) {
                    viewModel.replyChallengeInvite(accept = false)
                    true
                }
            }

            if (challengeData.first in listOf(ChallengeType.CONFOUR, ChallengeType.PENALTY, ChallengeType.BOXES, ChallengeType.KNOWLEDGE)) {
                startTimerForChallengeParticipantConfirmPopup(challengeData.second, dialog)
            }
        } catch (e: Exception) {
            Firebase.crashlytics.log(Gson().toJson(challengeData))
            Firebase.crashlytics.recordException(e)
        }
    }

    private fun startTimerForConFourParticipants(time: Long) {
        challengeParticipantRequestTimer?.let {
            if (it.isActive) it.cancel()
        }

        if (time <= 0L) {
            if (viewModel.activeChallenge.value?.challengeType in listOf(ChallengeType.CONFOUR,ChallengeType.PENALTY,ChallengeType.BOXES, ChallengeType.KNOWLEDGE) && viewModel.activeChallengeStatus.value != PodiumChallenge.ChallengeStatus.LIVE) {
                findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToConFourParticipantStatusBottomSheetFragment())
            }
            return
        }

        challengeParticipantRequestTimer = DateTimeUtils.countDownTimerFlow(time * 1000).onEach {
            Log.d("timertime", "startTimerForConFourParticipantStatus: $it")
        }.onCompletion { cause ->
            if (cause is CancellationException) {
                // Flow was canceled
                println("Flow was canceled")
            } else {
                // Flow completed normally
                if (viewModel.activeChallenge.value?.challengeType in listOf(
                        ChallengeType.CONFOUR,
                        ChallengeType.PENALTY,
                        ChallengeType.BOXES,
                        ChallengeType.KNOWLEDGE
                    ) && viewModel.activeChallengeStatus.value != PodiumChallenge.ChallengeStatus.LIVE
                ) {
                    findNavController().navigateSafe(PodiumLiveLectureFragmentDirections.actionPodiumLiveFragmentToConFourParticipantStatusBottomSheetFragment())
                }
            }
            challengeParticipantRequestTimer = null
        }.launchIn(lifecycleScope)

    }

    private fun startTimerForChallengeParticipantConfirmPopup(time: Long, dialog: AlertDialog) {
        if (time <= 0L) {
            if (dialog.isShowing) {
                dialog.dismiss()
            }
            return
        }

        DateTimeUtils.countDownTimerFlow(time * 1000).onEach {
            Log.d("timertime", "startTimerForConFourParticipantInvite: $it")
        }.onCompletion {
            if (dialog.isShowing) {
                dialog.dismiss()
            }
        }.launchIn(lifecycleScope)
    }

}