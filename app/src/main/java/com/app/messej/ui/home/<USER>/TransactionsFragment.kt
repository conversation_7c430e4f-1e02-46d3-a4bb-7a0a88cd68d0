package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.TransactionTab
import com.app.messej.databinding.FragmentTransactionsBinding
import com.app.messej.ui.home.gift.GiftConversionHistoryAllFragment
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.google.android.material.button.MaterialButton


class TransactionsFragment : Fragment() {
private lateinit var binding: FragmentTransactionsBinding
    private val viewModel: BusinessDealsListViewModel by viewModels()
    private val args: TransactionsFragmentArgs by navArgs()
    private lateinit var mTransactionPagerAdapter: FragmentStateAdapter
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?, ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_transactions, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    override fun onStart() {
        super.onStart()
//        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
//        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
//        binding.customActionBar.toolBarTitle.text = getString(R.string.transactions)


        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                binding.customActionBar.toolBarTitle.text = getString(R.string.transactions)
                bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
            }
        }
    }
    private fun setUp() {
        initAdapter()
        viewModel.setCurrentTab(args.setDefaultTab)
        binding.transactionListPager.apply {
            isUserInputEnabled = false
            adapter = mTransactionPagerAdapter
        }


        binding.btnFlax.setOnClickListener {
            viewModel.setCurrentTab(TransactionTab.TAB_FLAX)
            (it as MaterialButton).isChecked = true
        }
        binding.btnCoins.setOnClickListener {
            viewModel.setCurrentTab(TransactionTab.TAB_COINS)
            (it as MaterialButton).isChecked = true
        }
        viewModel.currentTab.value?.let {
            binding.transactionListPager.setCurrentItem(it.ordinal, false)
        }

    }
    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it ?: return@observe
            Log.w("TABTRANSACTION", "observe currentTab: $it")
            if (binding.transactionListPager.currentItem == it.ordinal) return@observe
            binding.transactionListPager.setCurrentItem(it.ordinal, false)
        }
    }

    private fun initAdapter() {
        mTransactionPagerAdapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = TransactionTab.values().size
            override fun createFragment(position: Int): Fragment {
                val tab = TransactionTab.values()[position]
                return when (tab) {
                    TransactionTab.TAB_FLAX -> FlaxTransactionListFragment()
                    TransactionTab.TAB_COINS -> GiftConversionHistoryAllFragment()

                    else -> throw IllegalArgumentException("Invalid position: $position")
                }
            }

        }

    }

}