package com.app.messej.ui.home.publictab.flash.search

import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerFragment

class FlashSearchPlayerFragment : BaseFlashPlayerFragment() {
    override val viewModel: FlashSearchViewModel by navGraphViewModels(R.id.nav_flash_search)

    override fun canPlayFlashVideo(flash: FlashVideo): Boolean {
        return flash.isAvailable
    }
}