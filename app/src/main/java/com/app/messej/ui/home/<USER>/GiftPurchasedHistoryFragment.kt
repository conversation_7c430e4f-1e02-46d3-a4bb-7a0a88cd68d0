package com.app.messej.ui.home.gift

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.paging.LoadState
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.gift.PointsPurchase
import com.app.messej.databinding.FragmentGiftPurchasedHistoryBinding
import com.app.messej.databinding.ItemGiftPurchaseHistoryLayoutBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.kennyc.view.MultiStateView

class GiftPurchasedHistoryFragment : Fragment() {
    private val viewModel: GiftPurchasedHistoryViewModel by viewModels()
    private lateinit var binding: FragmentGiftPurchasedHistoryBinding

    private var purchaseAdapter: GiftPurchaseHistoryPagerAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_purchased_history, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }


    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.gift_title_purchase_history)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        initAdapter()
    }

    private fun observe() {
        viewModel.giftPurchaseHistory.observe(viewLifecycleOwner) {
            purchaseAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }


    private fun initAdapter() {
        purchaseAdapter = GiftPurchaseHistoryPagerAdapter()

        val layoutManParticipant = LinearLayoutManager(context)

        binding.giftPurchaseHistoryList.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = purchaseAdapter
        }

        purchaseAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.gift_no_gift_purchases_found)
        }
    }

    class GiftPurchaseHistoryPagerAdapter : PagingDataAdapter<PointsPurchase, GiftPurchaseHistoryPagerAdapter.GiftPurchaseHistoryViewHolder>(TransactionsDiff) {

        override fun onBindViewHolder(holder: GiftPurchaseHistoryViewHolder, position: Int) {
            getItem(position)?.let { holder.bind(it) }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = GiftPurchaseHistoryViewHolder(
            ItemGiftPurchaseHistoryLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        )

        inner class GiftPurchaseHistoryViewHolder(private val binding: ItemGiftPurchaseHistoryLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
            fun bind(item: PointsPurchase) = with(binding) {
                binding.apply {
                    giftPurchaseHistory = item
                    hideDate = false
                    hideButton = true
                }
            }
        }

        object TransactionsDiff : DiffUtil.ItemCallback<PointsPurchase>() {
            override fun areItemsTheSame(oldItem: PointsPurchase, newItem: PointsPurchase) = oldItem.id == newItem.id

            override fun areContentsTheSame(oldItem: PointsPurchase, newItem: PointsPurchase) = oldItem == newItem
        }

    }

}