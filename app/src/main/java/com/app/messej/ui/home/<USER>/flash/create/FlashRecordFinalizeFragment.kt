package com.app.messej.ui.home.publictab.flash.create

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.NavFlashRecordDirections
import com.app.messej.R
import com.app.messej.data.utils.MediaUtils
import com.app.messej.databinding.FragmentFlashRecordFinalizeBinding
import com.app.messej.ui.common.CategoryDropdownAdapter
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.button.MaterialButton
import kotlin.math.roundToInt

class FlashRecordFinalizeFragment : Fragment() {

    private lateinit var binding: FragmentFlashRecordFinalizeBinding

    private val viewModel: FlashRecordViewModel by navGraphViewModels(R.id.nav_flash_record)

    private var mCategoryAdapter: CategoryDropdownAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_record_finalize, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setup() {
        viewModel.getCategories()
        viewModel.preProcessVideo()
        binding.closeButton.setOnClickListener { findNavController().popBackStack() }
        binding.postButton.setOnClickListener {
            releasePlayer()
            viewModel.postFlashVideo()
        }
        binding.draftButton.setOnClickListener {
            releasePlayer()
            viewModel.postFlashVideo(true)
        }

        //Set video player if the video is already processed(back press case)
        if(viewModel.processedVideo.value != null) {
            setupPlayer()
        }
        binding.switchPauseComments.setOnCheckedChangeListener { _, isChecked ->
            Log.d("HEEEINDNDND", "Entered${isChecked}")
            if (viewModel.isCommentsDisabled.value == isChecked){ viewModel.handleCommentsSwitch(!isChecked)}
        }


    }

    private fun observe() {
        viewModel.flashRecording.observe(viewLifecycleOwner) { media ->
            media?: return@observe
            Log.w("FREF", "observe: flashRecording ${media.file.absolutePath} of ${media.meta.durationMs}ms")
            binding.playerView.apply {
                layoutParams = (layoutParams).apply {
                    val playerH = resources.displayMetrics.heightPixels*0.4
                    val aspect = if (media.isFromGallery) (9f/16) else media.meta.resolution.aspectRatio
                    val playerW = playerH * aspect
                    height = playerH.roundToInt()
                    width = playerW.roundToInt()
                }
            }
        }

        viewModel.flashVideoProgress.observe(viewLifecycleOwner) { progress ->
            binding.encodeProgress.progress = progress
            if (progress == null || progress < 0) {
                binding.encodeProgress.text = resources.getString(R.string.chat_video_encoding)
            } else {
                binding.encodeProgress.text = resources.getString(R.string.chat_video_encoding_progress, progress)
            }
        }

        viewModel.flashCategoryList.observe(viewLifecycleOwner) { cats ->
            if (cats != null) {
                val adapter = CategoryDropdownAdapter(requireContext(), cats)
                (binding.categoryDropdown.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.flashCategory.postValue(item)
                    }
                }
                mCategoryAdapter = adapter
            }
        }
        viewModel.flashCategory.observe(viewLifecycleOwner) { v ->
            v?.let { cat ->
                val cats = viewModel.flashCategoryList.value.orEmpty()
                val index = cats.indexOfFirst { it.categoryId == cat.categoryId }
                if (index == -1) return@let
                mCategoryAdapter?.setSelectedPos(index)
                (binding.categoryDropdown.editText as? AutoCompleteTextView)?.apply {
                    setText(cats[index].name)
                }
            }
        }
        viewModel.onFlashSavedToDraft.observe(viewLifecycleOwner) {
            findNavController().popBackStack(R.id.nav_flash_record,true)
            showSnackbar(R.string.flash_draft_toast)
        }
        viewModel.onFlashCreate.observe(viewLifecycleOwner) {
            findNavController().navigateSafe(NavFlashRecordDirections.actionGlobalMyFlashFragment())
        }

        viewModel.onVideoProcessed.observe(viewLifecycleOwner) {
            setupPlayer()
        }

        viewModel.flashSharePublic.observe(viewLifecycleOwner) {
            if(it) {
                viewModel.apply {
                    flashShareDears.value = false
                    flashShareFans.value = false
                    flashShareLikers.value = false
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    private var player: ExoPlayer? = null

    private fun releasePlayer() {
        player?.apply {
            stop()
            release()
            player = null
        }
    }

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    private fun setupPlayer() {
        val file = viewModel.processedVideo.value?: return
        Log.w("FREF", "setupPlayer: ${file.path} | ${MediaUtils.getDuration(file,true)}", )
        var med = MediaItem.Builder()
            .setUri(file.path)
            .build()
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build().apply {
                binding.playerView.player = this
                binding.playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
                val playButton = binding.playerView.findViewById<MaterialButton>(R.id.exo_play_pause_custom)
                playButton.setOnClickListener {
                    if (isPlaying) {
                        pause()
                    } else {
                        seekTo(0)
                        play()
                    }
                }
//                binding.playerView.findViewById<View>(androidx.media3.ui.R.id.exo_settings).isVisible = false
                addListener(object : Player.Listener {
                    override fun onIsPlayingChanged(isPlaying: Boolean) {
                        Log.w("FREF", "onIsPlayingChanged: $isPlaying")
                        if (isPlaying) {
                            playButton.setIconResource(R.drawable.ic_media_stop_large)
                        } else {
                            playButton.setIconResource(R.drawable.ic_media_play_large)
                        }
                    }
                })
            }

        }
        player?.apply {
            clearMediaItems()
            setMediaItem(med)
            prepare()
        }
    }
}