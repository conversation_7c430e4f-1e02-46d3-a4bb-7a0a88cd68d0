package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Activity
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.core.widget.doOnTextChanged
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.TribeEditType
import com.app.messej.databinding.LayoutTribeEditBinding
import com.app.messej.ui.auth.common.HuddleLanguageDropDownAdapter
import com.app.messej.ui.common.CategoryDropdownAdapter
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.ATTACH_SOURCE_RESULT_KEY
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_CAMERA
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_GALLERY
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.launch


class TribeEditDialogueFragment: BottomSheetDialogFragment()  {

    private lateinit var binding: LayoutTribeEditBinding
    private val args: TribeEditDialogueFragmentArgs by navArgs()
    private val viewModel: HuddleEditViewModel by viewModels()
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_tribe_edit, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        this.isCancelable = false
        viewModel.setHuddleId(args.huddleId)
        binding.tribeDp.setOnClickListener {
            val action = TribeEditDialogueFragmentDirections.actionGlobalProfileImageAttachSourceFragment()
            findNavController().navigateSafe(action)
        }

        viewModel.isTribeUserProfilePhoto.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(requireContext(), getString(R.string.title_tribe_photo_as_profilr_pic), Toast.LENGTH_SHORT).show()
            }
        }

        binding.tribeEditCloseBtn.setOnClickListener {
            dismiss()
        }
        binding.textInputName.editText?.apply {
            addTextChangedListener {

                doOnTextChanged { text, _, _, _ ->
                    if (text?.isNotEmpty() == true) {
                        viewModel.setTribeButtonEnabled(true)
                    } else {
                        viewModel.setTribeButtonEnabled(false)
                    }
                }
            }
        }
    }

    private fun removeCharAtIndex(inputString: String, index: Int): String {
        if (index < 0 || index >= inputString.length) {
            // Invalid index, return the original string unchanged
            return inputString
        }

        return inputString.substring(0, index) + inputString.substring(index + 1)
    }

    private val selectImageFromGalleryResult =
        registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
            uri?.let {
                viewModel.addImage(uri)
            }
        }

    private fun observe() {
        viewModel.onTribeDialogueDismiss.observe(viewLifecycleOwner) {
            this.dialog?.dismiss()
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChatHuddle(args.huddleId))
        }

        viewModel.tribeEditType.observe(viewLifecycleOwner){
            when(it){
                TribeEditType.EDIT_NAME -> binding.tribeTitle.setText(R.string.tribe_dialogue_title_one)
                TribeEditType.EDIT_DP -> binding.tribeTitle.setText(R.string.tribe_dialogue_title_two)
                TribeEditType.EDIT_CATEGORY ->  binding.tribeTitle.setText(R.string.tribe_dialogue_title_three)
            }
        }
        viewModel.huddleCategoryList.observe(viewLifecycleOwner) { cats ->
            if (cats != null) {
                val adapter = CategoryDropdownAdapter(requireContext(), cats)
                (binding.textInputTribeCategoryDropdown.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.setCategoryId(item.categoryId)
                    }
                    viewModel.category.value?.let { cat ->
                        val index = cats.indexOfFirst { it.categoryId == cat }
                        if (index == -1) return@let
                        adapter.setSelectedPos(index)
                        setText(cats[index].name)
                    }
                }
            }
        }


        viewModel.huddleLanguageList.observe(viewLifecycleOwner) { languages ->
            if (languages != null) {
                val adapter = HuddleLanguageDropDownAdapter(requireContext(), languages)
                (binding.languageDropdown.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.setLanguage(item)
                    }
                    viewModel.huddleLanguage.value?.let { lang ->
                        val index = languages.indexOfFirst { it.englishName == lang }
                        if (index==-1) return@let
                        adapter.setSelectedPos(index)
                        setText(languages[index].name)
                    }
                }
            }

        }
        viewModel.huddle.observe(viewLifecycleOwner){
            binding.textInputName.editText?.setText("")
            binding.tribeAboutInput.editText?.setText("")
        }

        viewModel.onTriggerCrop.observe(viewLifecycleOwner) {
            val options = UCrop.Options().apply {
                setCircleDimmedLayer(true)
                val color = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                setToolbarColor(color)
                setStatusBarColor(color)
                setToolbarTitle(resources.getString(R.string.common_crop))
            }
            val crop = UCrop.of(it.first, it.second)
                .withAspectRatio(1f,1f)
                .withOptions(options)
            imageCropResult.launch(crop.getIntent(requireContext()))
        }

        setFragmentResultListener(ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when (bundle.getString(ATTACH_SOURCE_RESULT_KEY)) {
                SRC_GALLERY -> selectImageFromGallery()
                SRC_CAMERA -> takeImage()
            }
        }
    }
    private val imageCropResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let {
                val resultUri = UCrop.getOutput(it)?: return@let
                viewModel.addCroppedImage(resultUri)
            }
        } else {
            viewModel.onCropCancelled()
        }
    }

    private fun selectImageFromGallery() = selectImageFromGalleryResult.launch("image/*")

    private fun takeImage() {
        checkCameraPermission(binding.root) {
            lifecycleScope.launch {
                val uri = viewModel.getImageUriForCapture()
                takeImageResult.launch(uri)
            }
        }
    }

    private val takeImageResult =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { isSuccess ->
            if (isSuccess) {
                viewModel.addCapturedImage()
            }
        }

    //    override fun getTheme()=R.style.Widget_Flashat_Tribe_BottomSheet

}