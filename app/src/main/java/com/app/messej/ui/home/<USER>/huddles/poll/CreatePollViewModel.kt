package com.app.messej.ui.home.publictab.huddles.poll

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.ObservableOptionsModel
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.poll.CreatePollRequest
import com.app.messej.data.model.api.poll.CreatePollResponse
import com.app.messej.data.model.entity.Poll

import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.util.TimeZone

class CreatePollViewModel(application: Application) : AndroidViewModel(application) {
    private val huddleRepo = HuddlesRepository(application)
    private val mutableStringList: MutableList<String> = mutableListOf()
    private val _isOpenChecking = MutableLiveData<Boolean>(false)
    private val _updatedOptionPosition = MutableLiveData<Int>(null)


    var huddleId: Int? = null
        private set
    var pollId: Int? = null
        private set


    fun setHuddleId(huddle: Int, poll: Int?) {
        huddleId = huddle
        pollId = poll
        poll?.let { getPOllDetails(it) }
    }

    private val _optionsList = MutableLiveData<MutableList<ObservableOptionsModel>>(mutableListOf(ObservableOptionsModel(1, ""), ObservableOptionsModel(2, "")))
    val optionsList: LiveData<MutableList<ObservableOptionsModel>> = _optionsList

    val createPollSuccess = LiveEvent<String>()
    val apiResponseError = LiveEvent<String>()
    val createPollDateError = LiveEvent<DateError>()
    val maxLimit = LiveEvent<Boolean>()
    val isOpenNull = LiveEvent<Boolean>()
    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading


    companion object {
        enum class PollOptionError {
            NONE, AT_LEAST_ONE, SAME_OPTION, LIMIT_REACHED
        }

        enum class DateError {
            PAST, START_NULL, NONE, PAST_START
        }
    }

    private val _startDate = MutableLiveData<LocalDate?>(null)
    val startDate: LiveData<LocalDate?> = _startDate
    val startDateFormatted: LiveData<String?> = _startDate.map {
        it?.let { dt ->
            return@map DateTimeUtils.format(dt, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)
        }
        return@map null
    }


    private val _endDate = MutableLiveData<LocalDate?>(null)
    val endDate: LiveData<LocalDate?> = _endDate
    val endDateFormatted: LiveData<String?> = _endDate.map {
        it?.let { dt ->
            return@map DateTimeUtils.format(dt, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)
        }
        return@map null
    }

    private val _startNow = MutableLiveData<LocalDate?>(null)
    private val _isOpen = MutableLiveData<LocalDate?>(null)


    val isStartNow: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun combineSources() {
            if (_startNow.value == null && _startDate.value == null) med.postValue(true)
            else if (_startNow.value != null) med.postValue(true)
            else if(_startDate.value!!.equals(LocalDate.now())) med.postValue(true)
            else med.postValue(false)
        }
        med.addSource(_startDate) { combineSources() }
        med.addSource(_startNow) { combineSources() }
        med
    }


//    val isStartNow = _startNow.map {
//        it != null && it.isEqual(LocalDate.now())
//    }


    val question = MutableLiveData<String>(null)

    private val _questionValid = MutableLiveData<Boolean?>(null)
    val questionValid: LiveData<Boolean?> = _questionValid

    fun validateQuestion(isError: Boolean) {
        _questionValid.postValue(isError)
    }

    private val _optionError = MediatorLiveData(PollOptionError.NONE)
    val optionError: LiveData<PollOptionError> = _optionError


    fun validateCreatePollStage(): Boolean {

        val questionValid = question.value.orEmpty().isNotBlank()
        _questionValid.postValue(questionValid)

        val list = _optionsList.value.orEmpty().filter {
            !it.text.isNullOrBlank()
        }.toMutableList()


        if (list.size <= 1) {
            _optionError.value = PollOptionError.AT_LEAST_ONE
        } else if (hasDuplicates(list)) {
            _optionError.value = PollOptionError.SAME_OPTION
        } else {
            _optionError.value = PollOptionError.NONE
        }

        /**Date validation*/

        val start = _startDate.value
        val end = _endDate.value

        if (_startNow.value == null) {
            if ((_startDate.value == null && _endDate.value == null) || (_startDate.value == null && _isOpen.value == null) ) {
                createPollDateError.value = DateError.START_NULL
            } else if (end != null && end.isBefore(start)) {
                createPollDateError.value = DateError.PAST
            } else if (start!!.isBefore(LocalDate.now())) {
                createPollDateError.value = DateError.PAST_START
            }else if((_isOpen.value==null &&_isOpenChecking.value==false) && _endDate.value==null){
                createPollDateError.value = DateError.START_NULL
            } else {
                createPollDateError.value = DateError.NONE
            }
        } else {
            if (_isOpenChecking.value == false && _endDate.value == null) {
                createPollDateError.value = DateError.START_NULL
            } else {
                createPollDateError.value = DateError.NONE
            }
        }

        /** Overall Poll Validation*/
        val validation = _optionError.value == PollOptionError.NONE && questionValid && createPollDateError.value == DateError.NONE
        return validation
        Log.d("VALDATION", validation.toString())
    }


    fun addOption() {
        val list = _optionsList.value?.toMutableList() ?: mutableListOf()

        if (list.size >= 5) {
            _optionError.postValue(PollOptionError.LIMIT_REACHED)
        } else if (list[0].text == "" || list[1].text == "") {
            _optionError.postValue(PollOptionError.AT_LEAST_ONE)
        } else if (!hasDuplicates(list)) {
            val maxId = list.maxOfOrNull { it -> it.id } ?: 0
            list.add(ObservableOptionsModel(maxId + 1))
            _optionsList.postValue(list)
        }


    }

    fun removeItem(pos: Int) {
        if (_updatedOptionPosition.value!! >2){
            val list = _optionsList.value?.toMutableList() ?: mutableListOf()
            list.removeAt(pos)
            _optionsList.postValue(list)
        }
    }

    fun setStartDate(date: LocalDate?) {
        _startNow.postValue(null)
        _startDate.postValue(date)
    }

    fun setEndDate(date: LocalDate?) {
        _endDate.postValue(date)
        _isOpen.postValue(null)
    }


    fun setStartNow() {
        _startNow.postValue(LocalDate.now())
        _startDate.postValue(null)
    }

    fun clearStartNow() {
        _startNow.postValue(null)
    }
    fun setOpen() {
        _isOpen.postValue(null)
        _endDate.postValue(null)
    }

    fun submitPoll() {
        val huddle = huddleId ?: return
        viewModelScope.launch(Dispatchers.IO) {
            _optionsList.value?.map {
                if (it.text.trim() != "") {
                    mutableStringList.add(it.text)
                }
            }
            Log.d("OPTIONLIST", "${mutableStringList.toMutableList()}")

            val selectedStartDate = if (_startDate.value != null) DateTimeUtils.format(_startDate.value, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED) else DateTimeUtils.format(
                _startNow.value, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED
            )
            val selectedEndDate =
                if (_endDate.value != null) DateTimeUtils.format(_endDate.value, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED) else DateTimeUtils.format(_isOpen.value, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)

            val createPollRequest = CreatePollRequest(
                answers = mutableStringList.toMutableList(),
                endDate = selectedEndDate,
                question = question.value,
                huddleId = huddle,
                startDate = selectedStartDate,
                tz =TimeZone.getDefault().id,
                startNow = isStartNow.value
            )

            // Creating a new poll
            when (val result: ResultOf<APIResponse<CreatePollResponse>> = if (pollId != -1) huddleRepo.editPoll(createPollRequest.copy(pollId = pollId))
            else huddleRepo.createPoll(createPollRequest)) {
                is ResultOf.Success -> {
                    createPollSuccess.postValue(result.value.message)
                }

                is ResultOf.APIError -> {
                    apiResponseError.postValue(result.error.message)
                    mutableStringList.clear()
                }

                is ResultOf.Error -> {
                    // Handle other errors
                }
            }

        }
    }

    private fun hasDuplicates(list: MutableList<ObservableOptionsModel>): Boolean {

        val cleanList = list.map {it.text.trim()}.filter {it.isNotEmpty()}
        val hasDuplicates = cleanList.size != cleanList.distinct().size
        Log.d("DUPLI", hasDuplicates.toString())

        return if (hasDuplicates) {
            _optionError.postValue(PollOptionError.SAME_OPTION)
            true
        } else {
            _optionError.postValue(PollOptionError.NONE)
            false
        }
    }

    fun isDataAvailable(): Boolean {
        return !question.value.isNullOrEmpty() || !_optionsList.value.isNullOrEmpty() || _startDate.value != null || _endDate.value != null
    }

    private fun getPOllDetails(pollId: Int) {
        _dataLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<Poll> = huddleRepo.getPollById(pollId)) {
                is ResultOf.Success -> {
                    _dataLoading.postValue(false)
                    question.postValue(result.value.question?:"")
                    val observableOptionList: MutableList<ObservableOptionsModel> = result.value.answers.mapIndexed { index, text ->
                        ObservableOptionsModel(index, text.answer)
                    }.toMutableList()
                    _optionsList.postValue(observableOptionList)
                    Log.d("QASDF", "" + DateTimeUtils.parseZonedDateTimeWithoutZ(result.value.startDateIso).run {
                        DateTimeUtils.getLocalDate(this!!)
                    })
                    DateTimeUtils.parseZonedDateTimeWithoutZ(result.value.startDateIso).run {
                        _startDate.postValue(DateTimeUtils.getLocalDate(this!!))
                    }

//                    _startDate.postValue(DateTimeUtils.parseDate(result.value.startDateIso, DateTimeUtils.FORMAT_ISO_DATE_TIME))

                    if (result.value.endDate != null) {
                        DateTimeUtils.parseZonedDateTimeWithoutZ(result.value.endDateIso).run {
                            _endDate.postValue(DateTimeUtils.getLocalDate(this!!))
                        }
//                        _endDate.postValue(DateTimeUtils.parseDate(result.value.endDateIso, DateTimeUtils.FORMAT_ISO_DATE_TIME))
                        isOpenNull.postValue(false)
                    } else {
                        isOpenNull.postValue(true)
                    }


                }

                is ResultOf.APIError -> {

                }

                is ResultOf.Error -> {

                }
            }
        }
    }

    fun isOpenChecked(isOpen: Boolean) {
        _isOpenChecking.postValue(isOpen)
    }

    fun updatedOptionPosition(size: Int) {
        _updatedOptionPosition.postValue(size)
        if (size == 5) {
            maxLimit.postValue(true)
        } else {
            maxLimit.postValue(false)
        }
    }



}

