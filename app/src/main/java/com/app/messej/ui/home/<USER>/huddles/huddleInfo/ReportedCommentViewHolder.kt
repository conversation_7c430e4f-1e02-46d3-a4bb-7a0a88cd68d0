package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.view.View
import com.app.messej.R
import com.app.messej.data.model.entity.HuddleReportedComment
import com.app.messej.databinding.ItemHuddleReportedCommentBinding
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.chat.adapter.ChatMessageViewHolder

class ReportedCommentViewHolder(
    val binding: ItemHuddleReportedCommentBinding,
    userId: Int,
    private var mListener : ChatAdapter.ChatClickListener,
    private val reportListener: ReportedCommentsListAdapter.ReportActionListener
) : ChatMessageViewHolder(binding.root, userId, false, mListener) {

    override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
        super.bind(item)
        val cm = (item as ChatMessageUIModel.PostCommentModel)
        val hrm = cm.comment as HuddleReportedComment
        comment = hrm

        adminActionText.text = when(hrm.reportStatus) {
            HuddleReportedComment.ReportStatus.ADMIN_DELETED -> root.context.getString(R.string.huddle_info_report_status_admin_deleted)
            HuddleReportedComment.ReportStatus.ADMIN_DELETED_USER_BLOCKED -> root.context.getString(R.string.huddle_info_report_status_admin_deleted_user_blocked)
            HuddleReportedComment.ReportStatus.MANAGER_DELETED -> root.context.getString(R.string.huddle_info_report_status_manager_deleted)
            HuddleReportedComment.ReportStatus.MANAGER_DELETED_USER_BLOCKED -> root.context.getString(R.string.huddle_info_report_status_manager_deleted_user_blocked)
            HuddleReportedComment.ReportStatus.USER_DELETED -> root.context.getString(R.string.huddle_info_report_status_user_deleted)
            else -> ""
        }

        deleteMessageButton.setOnClickListener {
            reportListener.onDeleteAction(hrm,layoutPosition)
        }
        chatMessage.setOnClickListener {
            reportListener.onViewComment(hrm,layoutPosition)
        }
        reportedParticipantsButton.setOnClickListener {
            reportListener.onViewReporters(hrm,layoutPosition)
        }
    }

    override fun getHighlightView(): View? {
        TODO("Not yet implemented")
    }
}