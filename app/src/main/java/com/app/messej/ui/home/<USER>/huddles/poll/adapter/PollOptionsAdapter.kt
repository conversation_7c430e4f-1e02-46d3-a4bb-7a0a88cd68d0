package com.app.messej.ui.home.publictab.huddles.poll.adapter

import android.view.View
import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.ObservableOptionsModel
import com.app.messej.databinding.LayoutSingleCreatePollItemBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class PollOptionsAdapter( items: MutableList<ObservableOptionsModel>) :
    BaseQuickAdapter<ObservableOptionsModel, BaseDataBindingHolder<LayoutSingleCreatePollItemBinding>>(R.layout.layout_single_create_poll_item, items) {


init {
    addChildClickViewIds(R.id.image_remove_option)
}

    override fun convert(holder: BaseDataBindingHolder<LayoutSingleCreatePollItemBinding>, item: ObservableOptionsModel) {
        holder.dataBinding?.apply {
            optionsModel = item
           position = holder.bindingAdapterPosition + 1

            imageRemoveOption.visibility =if (data.size > 2) View.VISIBLE else View.INVISIBLE

            textSingleEdittext.setText(item.text)

            // Move the cursor to the end of the text
            textSingleEdittext.text?.let { textSingleEdittext.setSelection(it.length) }
        }
    }

    object pollDiff : DiffUtil.ItemCallback<ObservableOptionsModel>() {
        override fun areItemsTheSame(oldItem: ObservableOptionsModel, newItem: ObservableOptionsModel) = oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: ObservableOptionsModel, newItem: ObservableOptionsModel): Boolean {
            return false
        }
    }


}
