package com.app.messej.ui.home.publictab.podiums.videoGift

import android.content.ContentResolver
import android.content.Context
import android.graphics.SurfaceTexture
import android.net.Uri
import android.util.Log
import android.view.Surface
import androidx.annotation.OptIn
import androidx.annotation.RawRes
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import com.app.messej.MainApplication
import com.app.messej.data.utils.MediaUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel.Factory.UNLIMITED
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import java.io.File

class TransparentVideoViewModel(
    private val scope: CoroutineScope,
    private val context: Context,
) {
    private val onFrameAvailable = MutableSharedFlow<Unit>(extraBufferCapacity = UNLIMITED)
    private var mediaPlayer: ExoPlayer? = null
    lateinit var state: State

    fun initialiseMediaPlayer(@RawRes videoRes: Int, looping: Boolean = false) {
        val resolution = MediaUtils.getVideoResolution(MainApplication.applicationContext(),videoRes)
        Log.d("TVVM","Video resolution: $resolution | ${resolution.aspectRatio}")
        val uri = Uri.Builder().scheme(ContentResolver.SCHEME_ANDROID_RESOURCE).path(videoRes.toString()).build()
        initialiseMediaPlayer(MediaItem.fromUri(uri),resolution.aspectRatio * 2,looping)
    }

    fun initialiseMediaPlayer(videoFile: File, looping: Boolean = false) {
        val uri = Uri.fromFile(videoFile)
        val aspect = MediaUtils.getVideoResolution(uri).aspectRatio * 2
        Log.d("TVVM","Video aspect: $aspect")
        initialiseMediaPlayer(MediaItem.fromUri(uri),aspect,looping)
    }

    private fun initialiseMediaPlayer(media: MediaItem, aspect: Float, looping: Boolean = false) {
        onMediaFinished = false
        state = State(
            aspectRatio = aspect, onFrameAvailable = onFrameAvailable
        )
//        if (mediaPlayer==null) {
        mediaPlayer = createMediaPlayer(context).apply {
            this.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    if (playbackState == Player.STATE_ENDED) {
                        // Media playback has ended
                        // Post to your LiveData or trigger any other action as needed
                        if (!looping) {
                            seekTo(duration - 5)
                            onMediaFinished = true
                        }
                    }
                }
            })
        }
//        }
        mediaPlayer?.apply {
            setMediaItem(media)
            if (looping) {
                repeatMode = Player.REPEAT_MODE_ONE
            }
            prepare()
            play()
        }
    }

    fun stopPlayback() {
        Log.d("TVVM", "playAnthem: stopPlayback")
        mediaPlayer?.apply {
            if (isPlaying) {
                stop()
            }
        }
    }

    fun replayVideo() {
        mediaPlayer?.apply {
            seekTo(0)
            play()
        }
    }

    var onMediaFinished by mutableStateOf(false)

    private var mediaPlayerSurface: Surface? = null

    fun onDispose() {
        mediaPlayer?.apply {
            stop()
            release()
        }
        mediaPlayerSurface?.release()
        Log.d("TVVM", "Media player released")
    }

    fun onSurfaceTextureCreated(surfaceTexture: SurfaceTexture) {
        surfaceTexture.setOnFrameAvailableListener { onFrameAvailable.tryEmit(Unit) }
        val surface = Surface(surfaceTexture).also { mediaPlayerSurface = it }
        scope.launch(Dispatchers.Main) {
            mediaPlayer?.apply {
                setVideoSurface(surface)
//                prepare()
//                play()
            }
        }
    }

    class State(
        val aspectRatio: Float,
        val onFrameAvailable: Flow<Unit>,
    )

    @OptIn(UnstableApi::class)
    private fun createMediaPlayer(
        context: Context,
    ): ExoPlayer {
        return ExoPlayer.Builder(
            context,
        ).build()
    }
}
