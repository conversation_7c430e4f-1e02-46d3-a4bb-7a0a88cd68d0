package com.app.messej.ui.home.publictab.myETribe

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.eTribe.ETribeResponse
import com.app.messej.data.model.api.eTribe.ETribeResponse.Companion.INACTIVE
import com.app.messej.data.model.enums.ETribeBannerView
import com.app.messej.data.model.enums.ETribeTabs
import com.app.messej.data.repository.ETribeRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.composeComponents.ComposeTextFieldState
import com.app.messej.ui.utils.CountryListUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class MyETribeViewModel(application: Application) : AndroidViewModel(application) {

    private val eTribeRepository = ETribeRepository(application)
    private val huddleRepo = HuddlesRepository(getApplication())

    private val _selectedTab = MutableLiveData(ETribeTabs.All)
    val selectedTab: LiveData<ETribeTabs> = _selectedTab

    val tabs = ETribeTabs.entries.toList()

    private val _tribeDetail = MutableLiveData<ETribeResponse?>(null)
    val tribeDetail: LiveData<ETribeResponse?> = _tribeDetail

    private val _isEditETribeAlertVisible = MutableLiveData(false)
    val isEditETribeAlertVisible : LiveData<Boolean> = _isEditETribeAlertVisible

    private val _eTribeBannerType = MutableLiveData<ETribeBannerView?>(null)
    val eTribeBannerType : LiveData<ETribeBannerView?> = _eTribeBannerType

    val editTribeTextFieldState = ComposeTextFieldState()

    val countryFlagList = MutableLiveData<Map<String, Int>>()

    val errorMessage = LiveEvent<String?>()

    init {
        viewModelScope.launch {
            countryFlagList.postValue(CountryListUtil.getCustomCountryMap())
        }
    }

    fun setTribeDetails(item: ETribeResponse?) {
        _tribeDetail.postValue(item)
        editTribeTextFieldState.text = item?.tribeName
        setTribeBannerView(type =
            when {
                item?.superStarTribeDetail?.superStarTribeId == null || item.superStarTribeDetail.superStarTribeStatus == INACTIVE -> ETribeBannerView.SuperStarFreeUserView
                item.superStarTribeDetail.superStarTribeStatus == ETribeResponse.ADMIN_DELETED -> ETribeBannerView.SuperStarTribeDeletedByAdminView
                else -> ETribeBannerView.SuperStarTribeView
            }
        )
    }

    fun setTab(tab: ETribeTabs) {
        if (tab == _selectedTab.value) return
        _selectedTab.postValue(tab)
    }

    fun setTribeBannerView(type: ETribeBannerView?) {
        _eTribeBannerType.postValue(type)
    }

    fun setEditTribeAlertBoxVisibility() {
        viewModelScope.launch {
            // Adding slight delay because, the keyboard not hiding on save, cancel button click when the text field have focus.
            // It ensure that dialog only dismissed after 100ms delay. The keyboard hide and focus clearing performed from the alert dialog
            // Keyboard hiding and alert dismiss take place simultaneously so that text field not hiding.
            delay(100)
            _isEditETribeAlertVisible.postValue(!(_isEditETribeAlertVisible.value ?: false))
        }
    }

    fun resetTribeName() {
        editTribeTextFieldState.text = _tribeDetail.value?.tribeName
    }

    val eTribeList = _selectedTab.switchMap {
        eTribeRepository.getETribeList(
            tab = it, tribeDetailCallBack = ::setTribeDetails
        ).liveData.cachedIn(viewModelScope)
    }.asFlow()

    fun editTribeName() {
        if (editTribeTextFieldState.text == _tribeDetail.value?.tribeName) {
            setEditTribeAlertBoxVisibility()
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            val result = eTribeRepository.editTribeName(
                tribeId = _tribeDetail.value?.tribeId,
                tribeName = editTribeTextFieldState.text
            )
            when(result) {
                is ResultOf.Success -> {
                    setUpdatedTribeName()
                    setEditTribeAlertBoxVisibility()
                }
                is ResultOf.APIError -> {
                    errorMessage.postValue(result.errorMessage())
                }
                is ResultOf.Error -> {
                    errorMessage.postValue(result.errorMessage())
                }
            }
        }
    }

    suspend fun setUpdatedTribeName() {
        _tribeDetail.postValue(
            _tribeDetail.value?.copy(tribeName = editTribeTextFieldState.text)
        )
        _tribeDetail.value?.let {
            it.tribeId ?: return@let
            huddleRepo.updateHuddleName(id = it.tribeId, newName = editTribeTextFieldState.text ?: "")
        }
    }
}