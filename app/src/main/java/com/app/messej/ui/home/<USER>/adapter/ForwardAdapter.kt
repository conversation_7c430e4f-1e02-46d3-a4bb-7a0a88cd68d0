package com.app.messej.ui.home.forward.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.forward.Forward
import com.app.messej.databinding.ItemForwardListBinding

class ForwardAdapter(private val listener:ActionListener): PagingDataAdapter<Forward, ForwardAdapter.ForwardViewHolder>(ForwardDiff) {

    interface ActionListener {
        fun onItemClick(item: Forward,position: Int)
    }
    inner class ForwardViewHolder(private val binding: ItemForwardListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: Forward, position: Int) = with(binding) {
            binding.chat=item
            binding.layoutHolder.setOnClickListener{
                listener.onItemClick(item,position)
            }
        }
    }
    object ForwardDiff : DiffUtil.ItemCallback<Forward>() {
        override fun areItemsTheSame(oldItem: Forward, newItem: Forward) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: Forward, newItem: Forward) = oldItem == newItem
    }

    override fun onBindViewHolder(holder: ForwardViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it,position) }
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ForwardViewHolder {
        val binding = ItemForwardListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ForwardViewHolder(binding)
    }
}