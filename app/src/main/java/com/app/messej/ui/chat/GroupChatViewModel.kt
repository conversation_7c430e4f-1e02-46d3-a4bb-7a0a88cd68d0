package com.app.messej.ui.chat

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.TerminalSeparatorType
import androidx.paging.cachedIn
import androidx.paging.filter
import androidx.paging.insertSeparators
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.AbstractHuddle.HuddleUserStatus
import com.app.messej.data.model.AttachLocation
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.api.ReportToManagerRequest
import com.app.messej.data.model.api.huddles.HuddleInfo
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.entity.PublicHuddleInterventions
import com.app.messej.data.model.entity.PublicHuddleWithInterventions
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.GroupChatStatus
import com.app.messej.data.model.enums.HuddleActionType
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.socket.repository.HuddleChatEventRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.CountryListUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

open class GroupChatViewModel(application: Application) : BaseChatViewModel(application) {

    protected val huddleRepo = HuddlesRepository(application)
    private val settingsRepo = SettingsRepository(application)



    protected val eventRepo = HuddleChatEventRepository

    protected var huddleID = MutableLiveData<Int?>(null)

    private var isOfflineHuddle = MutableLiveData<Boolean>(false)

    protected var cachedHuddle: PublicHuddle? = null

    // TODO need to wrap this into the other API call
    protected var _huddleInfo = MutableLiveData<HuddleInfo>(null)


    private val huddleWithInterventions: LiveData<PublicHuddleWithInterventions?> = isOfflineHuddle.switchMap {
        Log.d("GCVM", "huddleWithInterventions: isOfflineHuddle $it")
        val id = huddleID.value?: return@switchMap null
        val cached = cachedHuddle
        if(it==true) huddleRepo.getPublicHuddleWithInterventionsFlow(id).asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
        else if(cached!=null) MutableLiveData(PublicHuddleWithInterventions(cached))
        else MutableLiveData(null)
    }


    val huddle: LiveData<PublicHuddle?> = huddleWithInterventions.map {
        Log.d("ECI", "huddle: ${it?.huddle}")
        it?.huddle
    }

    val huddleStatus = huddle.map {
        Log.d("GCVM", "huddle status: ${it?.huddleStatus}")
        it?.huddleStatus
    }.distinctUntilChanged()

    val adminStatus = huddle.map {
        Log.d("GCVM", "huddle admin status: ${it?.adminStatus}")
        Pair(it?.adminStatus,it?.userStatus)
    }.distinctUntilChanged()

    fun getInterventionForUser(id: Int): PublicHuddleInterventions.UserIntervention? {
        return huddleWithInterventions.value?.interventions?.forUser(id)
    }

    override val canInteractWithChat = huddle.map {
        Log.w("ECI","canInteractWithChat: ${it?.canSendChats}")
        return@map it?.canSendChats?: false
    }.distinctUntilChanged()

    override val showChats = huddle.map {
        return@map it?.canReadChats?:false
    }.distinctUntilChanged()

    val userHasElevatedRole = huddle.map {
        return@map it?.role?.isElevated==true
    }.distinctUntilChanged()

    val userIsManager = huddle.map {
        return@map it?.role==UserRole.MANAGER
    }.distinctUntilChanged()

    val canShareHuddleLink = huddle.map {
        it?: return@map false
        // Only enabled when Public Huddle and Request to join is disabled
        if (it.huddleType == HuddleType.PUBLIC && !it.requestToJoin) {
            if (it.inviteLink ==null) false
            // either has to be admin or sharing needs to be open
            else it.role?.isElevated == true || it.participantShare
        } else false
    }.distinctUntilChanged()

    val canSendInvites = huddle.map {
        it?: return@map false
        // enabled to private and request enabled public huddles
        if (it.huddleType == HuddleType.PRIVATE || it.requestToJoin) {
            // has to be admin
            return@map it.role?.isElevated == true
        } else false
    }.distinctUntilChanged()

    override val canDeleteSelection  = _selectedChats.map { it.isNotEmpty() && it.all { ch -> !ch.deleted } }

    override val deleteTimeout = accountRepo.getAccountDetailsFlow().map {
        Log.w("BCVM", "delete timeout map: ${it?.huddleMessageDeleteTimeoutInSeconds}")
        it?.huddleMessageDeleteTimeoutInSeconds
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = null
    )

    override val canDeleteSelectionForEveryone = _selectedChats.map {
        return@map if (huddle.value?.role?.isElevated == true) {
            Log.w("BCVM", "delete timeout: admin")
            true
        } else {
            val timeout = deleteTimeout.value
            Log.w("BCVM","delete timeout: $timeout")
            if (timeout != null) {
                it.all { ch ->
                    Log.w("BCVM","delete timeout: age:  ${DateTimeUtils.durationToNowFromPast(ch.parsedCreatedTime)?.seconds}")
                    (DateTimeUtils.durationToNowFromPast(ch.parsedCreatedTime)?.seconds ?: 0) < timeout&& ch.sender == user.id
                }
            } else true
        }
    }

    override fun canDeleteMessageForEveryOne(msg: AbstractChatMessageWithMedia): Boolean {
        //Check the given message, which can be deleted for everyone
        msg.apply {
            return if (huddle.value?.role?.isElevated == true) {
                //Case Manager or Admin
                Log.w("BCVM", "delete timeout: admin")
                true
            } else {
                val timeout = deleteTimeout.value
                Log.w("BCVM", "delete timeout: $timeout")
                if (timeout != null) {
                    Log.w("BCVM", "delete timeout: age:  ${DateTimeUtils.durationToNowFromPast(message.parsedCreatedTime)?.seconds}")
                    (DateTimeUtils.durationToNowFromPast(message.parsedCreatedTime)?.seconds ?: 0) < timeout && message.sender == user.id
                } else true
            }
        }
    }

    override fun canDeleteMessage(msg: AbstractChatMessageWithMedia) = !msg.message.deleted

    sealed class ChatExitReason {
        data class UserAction(val action: HuddleUserStatus): ChatExitReason()
        object HuddleDeleted: ChatExitReason()
        object LoadFailed: ChatExitReason()
        data class WrongHuddleType(val actual: HuddleType): ChatExitReason()
        data class HuddleAction(val action: AbstractHuddle.HuddleAction): ChatExitReason()
    }

    val onExitChat = LiveEvent<ChatExitReason>()

    val onExitHuddle = LiveEvent<Boolean>()

    private fun verifyHuddleType(expected: HuddleType?, actual: HuddleType): Int? {
        return if (expected!=null && actual!=expected) {
            onExitChat.postValue(ChatExitReason.WrongHuddleType(actual))
            null
        } else 1
    }

    protected suspend fun loadHuddleInfo(id: Int, expectedType: HuddleType? = null) {
        var offline = false
        // attempt to load the huddle from local DB
        val offlineHuddle = huddleRepo.getPublicHuddle(id)
        if (offlineHuddle != null) {
            // we have an offline copy of the huddle
            verifyHuddleType(expectedType, offlineHuddle.huddleType) ?: return
            // TODO need to speed up loading by refactoring the API calls. Interventions and additional info needs to be one API
            if (offlineHuddle.status != AbstractHuddle.HuddleStatus.ADMIN_BLOCKED && offlineHuddle.userStatus != HuddleUserStatus.BLOCKED_BY_ADMIN) {
                //Get the interventions
                val response = huddleRepo.getHuddleInterventions(id)
                if (response is ResultOf.APIError && response.code == 400) {
                    // Assuming 400 is for non-existant huddle
                    onExitChat.postValue(ChatExitReason.HuddleDeleted)
                    return
                }
            }
            offline = true
            //count unread messages from local DB
            val unread = huddleRepo.getUnreadCount(offlineHuddle)
            cachedHuddle = offlineHuddle.copy(
                unreadCount = unread
            )
        } else {
            // we need to fetch the huddle details from the API
            val onlineHuddle = checkOnlineHuddleInfo(id)
            if (onlineHuddle==null) {
                onExitChat.postValue(ChatExitReason.LoadFailed)
                return
            }
            verifyHuddleType(expectedType,onlineHuddle.huddleType)?: return
            if (onlineHuddle.canSendChats) {
                // make the huddle offline
                huddleRepo.saveHuddle(onlineHuddle)
                huddleRepo.getHuddleInterventions(id)
                offline = true
            }
            cachedHuddle = onlineHuddle
        }
        withContext(Dispatchers.Main) {
            if (offline) {
                updateReadStatus()
                cachedHuddle?.apply{
                    if(unreadCount>0) {
                        _scrollToMessage.postValue(lastReadMessage)
                    }
                }
            }
            huddleID.value = id
            isOfflineHuddle.value = offline
            _dataLoading.value =false
        }
        getFullHuddleInfo(id)
    }


    fun setHuddleId(id: Int, expectedType: HuddleType? = null) {
        _dataLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            loadHuddleInfo(id, expectedType)
            _dataLoading.postValue(false)
        }
    }

    private suspend fun checkOnlineHuddleInfo(huddleId: Int): PublicHuddle? {
        return when (val result: ResultOf<PublicHuddle> = huddleRepo.getHuddleInfo(huddleId)) {
            is ResultOf.APIError -> {
                if(result.code == 400) {
                    onExitChat.postValue(ChatExitReason.HuddleDeleted)
                }
                null
            }
            is ResultOf.Error -> {
                null
            }
            is ResultOf.Success -> {
                result.value
            }
        }
    }

    protected fun getFullHuddleInfo(huddleId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<HuddleInfo> = huddleRepo.getFullHuddleInfo(huddleId)) {
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    val huddle = result.value
                    _huddleInfo.postValue(huddle)
                }
            }
        }
    }

    private fun updateReadStatus() {
        viewModelScope.launch(Dispatchers.IO) {
            cachedHuddle?.let {
//                if (it.huddleType == HuddleType.PRIVATE) {
//                    eventRepo.setRead(roomId = HuddleChatMessage.prefixHuddleId(it.id), it.lastMessage?.messageId ?: "")
//                }
                eventRepo.announceChatEnter(it.id)
                huddleRepo.resetUnreadCount(it.id)
            }
        }
    }

    fun sendChatExit(){
        huddleID.value?.let { eventRepo.announceChatExit(it) }
    }

    private data class ChatListParams(
        val huddleId: Int,
        val huddleType: HuddleType,
        var canReceiveNewChats: Boolean
    )

    protected val _countryList = MutableLiveData<Map<String,Int>>()

    init {
        _countryList.postValue(CountryListUtil.getCustomCountryMap())
    }

    private val chatListParams = huddle.map {
        it ?: return@map null
        return@map ChatListParams(it.id, it.huddleType, it.canReceiveNewChats).apply {
            if (user.userEmpowerment?.canEnterAnyHuddle == true && huddleStatus.value !in GroupChatStatus.entryBlocked() && it.huddleType == HuddleType.PUBLIC) canReceiveNewChats = true
        }
    }.distinctUntilChanged()

    override val _chatList = chatListParams.switchMap { params ->
        Log.d("CHATLIST", "observe: huddle switchmap triggered: $params")
        params ?: return@switchMap null
        val type = params.huddleType
        val flags = _countryList.value
        huddleRepo.getHuddleChatsPager(params.huddleId, type, params.canReceiveNewChats).liveData.map { pagingData: PagingData<HuddleChatMessageWithMedia> ->
                val pgData = pagingData.map { msg ->
                val state = msg.offlineMedia?.uploadState
                if (state is MediaUploadState.Uploading) {
                    Log.w("ENCODE", "chatlist map: ${state.progress}")
                }
                ChatMessageUIModel.ChatMessageModel(msg).apply {
                    if (type == HuddleType.PUBLIC && flags!=null) {
                        msg.message.senderDetails?.countryCode?.let { cc ->
                            countryFlag = flags[cc]
                        }
                    }
                }
            }

            checkIfNeedsSending(pgData)
            val pgd2 = if (type == HuddleType.PRIVATE) {
                pgData.setShowName().insertDateSeparators()
            } else {
                pgData.filter {
                    val msg = (it.message as HuddleChatMessage)
                    return@filter !msg.deleted && !msg.isActivity

                    // Commented this line because of FLS-3456
                    // return@filter !msg.deleted && !msg.isActivity && msg.senderDetails?.deletedAccount!=true
                }
            }
            // insert unread header
            return@map pgd2.insertSeparators(TerminalSeparatorType.SOURCE_COMPLETE) { before, after ->
                if ((cachedHuddle?.unreadCount ?: 0) > 0 && after != null && after is ChatMessageUIModel.ChatMessageModel && before != null && (after.chat.message.messageId == lastReadMessage)) {
                    ChatMessageUIModel.UnreadSeparatorModel(cachedHuddle?.unreadCount ?: 0)
                } else {
                    null
                }
            }
        }.cachedIn(viewModelScope)
    }

    private val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )

    private val lastReadMessage: String?
        get() = cachedHuddle?.lastReadMessage

    val typingInfo = eventRepo.huddleTyping.map {
        val info = it.getOrDefault(huddleID.value, null)

        return@map if (huddleStatus.value==GroupChatStatus.ACTIVE && info?.typing == true && info.senderId!=user.id) info.copy(
            senderName = nickNames.nickNameOrName(info.senderId?:0,info.senderName.orEmpty())
        ) else null

    }.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val onlineCountFromFlow = eventRepo.onlineCountFlow.map {
        val huddle = huddleID.value?:0
        it[huddle]
    }.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val connectionStateFlow = ChatSocketRepository.connectionStateFlow
        .asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val onlineCount: MediatorLiveData<Int> by lazy {
        val med = MediatorLiveData(0)
        fun combine() {
            val count = if (connectionStateFlow.value == false) 0
            else if(huddleStatus.value == GroupChatStatus.RESTRICTED || huddleStatus.value == GroupChatStatus.BLOCKED || huddleStatus.value == GroupChatStatus.ADMIN_BLOCKED) 0
            else onlineCountFromFlow.value ?: huddle.value?.onlineParticipants ?: 0
            Log.w("SCTREP", "combine: $count")
            med.postValue(count)
        }
        med.addSource(onlineCountFromFlow) { combine() }
        med.addSource(connectionStateFlow) { combine() }
        med.addSource(huddle) { combine() }
        med.addSource(huddleStatus) { combine() }
        med
    }

    private suspend fun performMessageUpload(msg: HuddleChatMessageWithMedia) = chatRepo.sendGroupChatMessage(msg)

    override fun likeItem(item: AbstractChatMessage) {
        if(eventRepo.likeChatMessage(item)) {
            viewModelScope.launch(Dispatchers.IO) {
                huddleRepo.updateHuddleMessageLike(item.messageId, !item.liked)
            }
        }
    }

    override suspend fun sendMessage(message: String, media: TempMedia?, replyTo: ReplyTo?, location: AttachLocation?, color: ChatTextColor?): AbstractChatMessage? {
        val huddle = huddle.value ?: return null
        val msg = chatRepo.createChatMessage(huddle.id, huddle.huddleType, message, media, replyTo, location, color = color)
        chatRepo.resetHuddleUnreadCount(huddle.id)
        if (msg.message.messageType.canSendInstantly()) {
            chatRepo.sendGroupChatMessage(msg)
        }
        return msg.message
    }

    override fun onTriggerUpload(msg: AbstractChatMessageWithMedia) {
        viewModelScope.launch(Dispatchers.IO) {
            performMessageUpload(msg as HuddleChatMessageWithMedia)
        }
    }

    override val typingListener = object: TypingListener {
        override fun onTyping(typing: Boolean) {
            huddleID.value?.let { eventRepo.announceChatTyping(it, typing) }
        }
    }

    fun deleteSelection(forEveryone: Boolean) {
        val list = _selectedChatsList.map{ it as HuddleChatMessage }
        if (list.isEmpty()) {
            exitSelectionMode()
            return
        }
        eventRepo.deleteHuddleChatMessage(list,forEveryone)
        exitSelectionMode()
    }

    fun deleteMessage(msg: AbstractChatMessage,forEveryone: Boolean) {
        eventRepo.deleteHuddleChatMessage(listOf(msg as HuddleChatMessage), forEveryone)
    }

    // Join actions

    private val _joinActionLoading = MutableLiveData(false)
    val joinActionLoading: LiveData<Boolean> = _joinActionLoading

    val onJoinActionError = LiveEvent<String>()
    data class LimitExceedParams(
        val premium: Boolean,
        val limit: Int
    )
    val onJoinLimitExceeded = LiveEvent<LimitExceedParams>()

    fun joinHuddle() {
        if(_huddleInfo.value?.canAddParticipant == true){
            _joinActionLoading.postValue(true)
            viewModelScope.launch(Dispatchers.IO){
                val huddle = huddle.value?: return@launch
                when(val result = huddleRepo.huddleRequestJoinAction(huddle)){
                    is ResultOf.APIError -> {
                        Log.d("GCVM", "joinHuddle error: $result")
                        if(result.code==400) {
                            onJoinActionError.postValue(result.error.message)
                        }
                    }
                    is ResultOf.Error -> {

                    }
                    is ResultOf.Success -> {
                        setHuddleId(huddle.id,huddle.huddleType)
                    }
                }
                _joinActionLoading.postValue(false)
            }
        }else {
            onJoinLimitExceeded.postValue(
                LimitExceedParams(
                    _huddleInfo.value?.managerPremium == true, _huddleInfo.value?.huddleParticipantsLimitForFree!!
                )
            )
        }

    }
    fun instantJoinHuddle() {
        if(_huddleInfo.value?.canAddParticipant == true) {
            viewModelScope.launch(Dispatchers.IO) {
                val huddle = huddle.value ?: return@launch
                when (val result = huddleRepo.huddleInstantJoinAction(huddle)) {
                    is ResultOf.APIError -> {
                        Log.d("GCVM", "joinHuddle error: $result")
                        if (result.code == 400) {
                            onJoinActionError.postValue(result.error.message)
                        }
                    }

                    is ResultOf.Error -> {

                    }

                    is ResultOf.Success -> {
                        setHuddleId(huddle.id, huddle.huddleType)
                    }
                }
                _joinActionLoading.postValue(false)
            }

        }
        else{
            onJoinLimitExceeded.postValue(
                LimitExceedParams(
                    _huddleInfo.value?.managerPremium == true, _huddleInfo.value?.huddleParticipantsLimitForFree?:0
                )
            )
        }
    }
    fun cancelJoinRequest() {
        _joinActionLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO){
            val huddle = huddle.value?: return@launch
            when(val result = huddleRepo.handleHuddleCancelAction(huddle = huddle, memberId = user.id)){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {
                }
                is ResultOf.Success -> {
                    onExitChat.postValue(ChatExitReason.UserAction(HuddleUserStatus.CANCELLED))
                }
            }
            _joinActionLoading.postValue(false)
        }
    }

    private val _huddleActionLoading: MutableLiveData<HuddleActionType?> = MutableLiveData(null)
    val huddleActionLoading: LiveData<HuddleActionType?> = _huddleActionLoading

    private fun performHuddleInviteAction(action: HuddleActionType, onSuccess: () -> Unit) {
        _huddleActionLoading.postValue(action)
        viewModelScope.launch(Dispatchers.IO) {
            val huddle = huddle.value ?: return@launch
            when (val result = huddleRepo.handleHuddleAdminRequestActions(action, huddle)) {
                is ResultOf.APIError -> {
                    onJoinActionError.postValue(result.error.message)
                }

                is ResultOf.Error -> {}
                is ResultOf.Success -> {
                    onSuccess.invoke()
                }
            }
            _huddleActionLoading.postValue(null)
        }
    }
    fun acceptInvite() = performHuddleInviteAction(HuddleActionType.ACCEPT_HUDDLE_INVITE) {
        huddleID.value?.let { setHuddleId(it) }
    }
    fun declineInvite() = performHuddleInviteAction(HuddleActionType.DECLINE_HUDDLE_INVITE) {
        onExitChat.postValue(ChatExitReason.UserAction(HuddleUserStatus.INVITE_DECLINED))
    }
    fun blockInvite() = performHuddleInviteAction(HuddleActionType.BLOCK_HUDDLE_INVITE) {
        onExitChat.postValue(ChatExitReason.UserAction(HuddleUserStatus.JOIN_REQUEST_BLOCKED))
    }

    val onFailedToAcceptAdminInvite = LiveEvent<Boolean>()

    fun canAcceptAdminRequest(action: HuddleActionType) {
        if (_huddleInfo.value?.canSendOrAcceptAdminInvite == true) {
            adminInviteAction(action)
            onFailedToAcceptAdminInvite.postValue(false)
        } else onFailedToAcceptAdminInvite.postValue(true)
    }

    private fun adminInviteAction(action: HuddleActionType, onSuccess: () -> Unit = {}) {
        _huddleActionLoading.postValue(action)
        viewModelScope.launch(Dispatchers.IO){
            val huddle = huddle.value?: return@launch
            when(val result = huddleRepo.respondToAdminInvite(huddle,action)){
                is ResultOf.APIError -> { }
                is ResultOf.Error -> { }
                is ResultOf.Success -> {
                    getFullHuddleInfo(huddle.id)
                    onSuccess.invoke()
                }
            }
            _huddleActionLoading.postValue(null)
        }

    }

    fun acceptAdminInvite() = canAcceptAdminRequest(HuddleActionType.ACCEPT_ADMIN_REQUEST)
    fun declineAdminInvite() = adminInviteAction(HuddleActionType.DECLINE_ADMIN_REQUEST)
    fun ignoreAdminInvite() = adminInviteAction(HuddleActionType.IGNORE_ADMIN_REQUEST)

    override fun getSenderForReply(msg: AbstractChatMessage): SenderDetails? {
        return (msg as HuddleChatMessage).senderDetails
    }

    val onNavigateToPrivateMessage = LiveEvent<Pair<String,Int>>()

    fun navigateToPrivateMessage(receiver: Int) {
        viewModelScope.launch {
            val roomId = HuddlesRepository(getApplication()).getPrivateChatRoomId(receiver)
            onNavigateToPrivateMessage.postValue(Pair(roomId,receiver))
        }
    }

    val onReportMessage = LiveEvent<AbstractChatMessage>()
    val onReportMessageCancel = LiveEvent<AbstractChatMessage>()

    fun reportSelection() {
        if (_selectedChatsList.isNotEmpty()) {
            reportMessage(_selectedChatsList[0])
        }
        exitSelectionMode()
    }

    fun reportMessage(msg: AbstractChatMessage) {
        if (msg.reported) {
            viewModelScope.launch(Dispatchers.IO){
                val req = ReportToManagerRequest.MessageReportCancelRequest(huddleID.value?: return@launch,msg.messageId)
                when(chatRepo.reportMessageCancel(req)){
                    is ResultOf.APIError -> {

                    }
                    is ResultOf.Error -> {

                    }
                    is ResultOf.Success -> {
                        onReportMessageCancel.postValue(msg)
                    }
                }
            }
        } else onReportMessage.postValue(msg)
    }





    init {
        viewModelScope.launch(Dispatchers.IO) {
            settingsRepo.getPrivacyStatus(force = false)
        }
        viewModelScope.launch {
            settingsRepo.privacySettingsFlow().collect {
                voiceRecordLimit = it?.huddleVoiceMessageLengthInSeconds
            }
        }

        viewModelScope.launch {
            //To trigger chat read event
            eventRepo.groupChatMessageReceived.filter { it.roomId == HuddleChatMessage.prefixHuddleId(huddleID.value?:0) }.collect {
                eventRepo.setRead(it.roomId, it.messageId)
            }
        }

    }

    private val _huddleLeaving = MutableLiveData(false)
    val huddleLeaving: LiveData<Boolean> = _huddleLeaving

    val onHuddleLeft = LiveEvent<Boolean>()

    fun leaveHuddle() {
        val huddle = _huddleInfo.value
        _huddleLeaving.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            if (huddle != null) {
                when (huddleRepo.leaveHuddle(huddle.id)) {
                    is ResultOf.APIError -> { }
                    is ResultOf.Error -> { }
                    is ResultOf.Success -> {
                        onHuddleLeft.postValue(true)
                    }
                }
            }
            _huddleLeaving.postValue(false)
        }
    }

    fun entryBlocked() = huddleStatus.value in GroupChatStatus.entryBlocked()
}