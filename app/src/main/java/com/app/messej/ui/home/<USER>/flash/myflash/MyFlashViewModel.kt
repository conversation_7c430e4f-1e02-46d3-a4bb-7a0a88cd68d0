package com.app.messej.ui.home.publictab.flash.myflash

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.api.FlashEligibility
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.FlashVideoWithMedia
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DDMMYYYY_DASHED
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerViewModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.ZoneId
import java.time.ZonedDateTime

class MyFlashViewModel(application: Application) : BaseFlashPlayerViewModel(application) {

    val showCompactLoading = MutableLiveData<Boolean>(false)

    private val _mediaTransfers: MutableList<MediaTransfer> = mutableListOf()
    private val onMediaTransfersChanged = MutableLiveData<Boolean>(true)

    private var currentDateZonedTime = ZonedDateTime.now(ZoneId.systemDefault())
    private val currentDate = DateTimeUtils.format(currentDateZonedTime, FORMAT_DDMMYYYY_DASHED)

    private fun MutableList<MediaTransfer>.setProgress(id: String, progress: Int) {
//            Log.d("VDOWNLOAD", "setProgress: $progress to ${this.find { it.messageId == id }}")
        this.find { it.messageId == id }?.progress = progress
    }

    private fun MutableList<MediaTransfer>.contains(id: String): Boolean {
        return this.find { it.messageId == id } != null
    }

    private fun MutableList<MediaTransfer>.add(id: String, silent: Boolean = false) {
        this.add(MediaTransfer(id))
        if (silent) return
        onMediaTransfersChanged.postValue(true)
    }

    private fun MutableList<MediaTransfer>.remove(id: String) {
        this.removeIf { it.messageId == id }
        onMediaTransfersChanged.postValue(true)
    }

    init {
        viewModelScope.launch {
            try {
                MediaUploadListener.uploadProgressFlow.collect {
                    Log.d("ENCODE", "MUL collected $it")
                    Log.d("ENCODE", "existing transfers $it")
                    it.forEach { (id, progress) ->
                        _mediaTransfers.setProgress(id, progress)
                    }
                }
            } finally { }
        }
    }

    private val _flashList = flashRepo.getMyFlashPager().liveData.cachedIn(viewModelScope)

    val flashList: MediatorLiveData<PagingData<MyFlashAdapter.MyFlashUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<MyFlashAdapter.MyFlashUIModel>?>(null)
        fun updateFlashList() {
            val list = _flashList.value?.map { item ->
                if (item.media?.uploadState is MediaUploadState.Uploading) {
                    if (!_mediaTransfers.contains(item.flash.id)) {
                        _mediaTransfers.add(item.flash.id)
                    }
                }
                MyFlashAdapter.MyFlashUIModel(flash = item, transfer = if (item.media?.uploadState is MediaUploadState.Uploading) _mediaTransfers.find { it.messageId == item.flash.id } else null)
            }
            med.postValue(list)
        }

        med.addSource(_flashList) { updateFlashList() }
        med.addSource(onMediaTransfersChanged) { updateFlashList() }
        med
    }

    override val _flashFeedList: LiveData<PagingData<FlashVideo>> = _flashList.map { pg ->
        pg.map {
            it.flash
        }
    }

    fun triggerSend(flash: FlashVideoWithMedia) {
        if (flash.media!=null) {
            viewModelScope.launch(Dispatchers.IO) {
                flashRepo.sendFlash(flash)
            }
        }
    }

    val onFlashArchived = LiveEvent<Boolean>()

    fun archiveFlash(flash: FlashVideoWithMedia) {
        viewModelScope.launch(Dispatchers.IO){
            when(flashRepo.archiveFlash(flash.flash.id)) {
                is ResultOf.Success -> {
                    onFlashArchived.postValue(true)
                }
                else -> {}
            }
        }
    }

    private val _flashEligibility = MutableLiveData<FlashEligibility?>(null)
    val flashEligibility : LiveData<FlashEligibility?> = _flashEligibility

    private val _actionLoading = MutableLiveData(false)
    val actionLoading : LiveData<Boolean> = _actionLoading

    val isEligibilityLoaded = LiveEvent<Boolean>()

    fun getFlashEligibilityDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            _actionLoading.postValue(true)
            val result = flashRepo.getFlashEligibilityDetails(currentDate = currentDate)
            when(result) {
                is ResultOf.Success -> {
                    _flashEligibility.postValue(result.value)
                    isEligibilityLoaded.postValue(true)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
            _actionLoading.postValue(false)
        }
    }

}