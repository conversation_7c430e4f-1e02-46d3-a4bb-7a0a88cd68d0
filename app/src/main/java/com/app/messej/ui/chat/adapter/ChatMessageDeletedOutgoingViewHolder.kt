package com.app.messej.ui.chat.adapter

import com.app.messej.R
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.databinding.ItemChatMessageOutgoingDeletedBinding
import com.app.messej.ui.chat.ChatMessageUIModel

class ChatMessageDeletedOutgoingViewHolder(val binding: ItemChatMessageOutgoingDeletedBinding, userId: Int,mListener: ChatAdapter.ChatClickListener)
    : ChatMessageViewHolder(binding.root,userId,false,mListener) {

    override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
        super.bind(item)
        val cm = (item as ChatMessageUIModel.ChatMessageModel)
        message = cm.message
        deletedText = root.context.getString(
            when (val msg = cm.message) {
                is BroadcastMessage -> R.string.broadcast_deleted
                is HuddleChatMessage -> {
                    if (cm.message.reported) R.string.chat_message_reported
                    else if (msg.remover?.idInt == userId) R.string.chat_message_deleted_manager
                    else R.string.chat_message_deleted_user
                }
                else -> if (cm.message.reported) R.string.chat_message_reported else R.string.chat_message_deleted
            }
        )
        setChatBubbleColor(chatBubble, cm)
    }

    override fun getHighlightView() = null
}