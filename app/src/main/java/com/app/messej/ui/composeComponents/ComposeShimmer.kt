package com.app.messej.ui.composeComponents

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.app.messej.R

@Composable
fun ComposeShimmerListLayout(
    modifier: Modifier = Modifier,
    itemCount: Int = 6,
    verticalSpace: Dp = dimensionResource(id = R.dimen.activity_margin),
    shimmerColors: List<Color> = listOf(colorResource(id = R.color.shimmerColor), colorResource(id = R.color.shimmerColorLight)),
    content: @Composable (ColumnScope. (Brush) -> Unit)
) {
    ComposeShimmerLayout(
        modifier = modifier,
        verticalSpace = verticalSpace,
        shimmerColors = shimmerColors
    ) { brush ->
        repeat(times = itemCount) {
            content(brush)
        }
    }
}

@Composable
fun ComposeShimmerLayout(
    modifier: Modifier = Modifier,
    verticalSpace: Dp = 0.dp,
    shimmerColors: List<Color> = listOf(colorResource(id = R.color.shimmerColor), colorResource(id = R.color.shimmerColorLight)),
    content: @Composable (ColumnScope. (Brush) -> Unit)
) {
    val repeatable = rememberInfiniteTransition(label = "Shimmer Effect Repeatable")
    val animatedShimmerEffect by repeatable.animateFloat(
        initialValue = 0F,
        targetValue = 1000F,
        animationSpec = infiniteRepeatable(
            animation = tween(easing = LinearEasing, durationMillis = 1300),
            repeatMode = RepeatMode.Reverse
        ), label = "Shimmer Animation"
    )
    val brush = Brush.linearGradient(
        colors = shimmerColors,
        start = Offset.Zero,
        end = Offset(x = animatedShimmerEffect, y = animatedShimmerEffect)
    )
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(space = verticalSpace)
    ) {
        content(brush)
    }
}

@Composable
fun ShimmerDefaultItem(
    modifier: Modifier = Modifier,
    height: Dp = 12.dp,
    fraction: Float = 0.3F,
    cornerSize: Dp = 4.dp,
    brush: Brush
) {
    Box(
        modifier = modifier
            .height(height = height)
            .fillMaxWidth(fraction = fraction)
            .clip(shape = RoundedCornerShape(size = cornerSize))
            .background(brush = brush)
    )
}

@Composable
fun ShimmerDefaultItem(
    modifier: Modifier = Modifier,
    height: Dp = 12.dp,
    shape: Shape,
    fraction: Float = 0.3F,
    brush: Brush
) {
    Box(
        modifier = modifier
            .height(height = height)
            .fillMaxWidth(fraction = fraction)
            .clip(shape = shape)
            .background(brush = brush)
    )
}

@Composable
fun ShimmerDefaultCircleItem(
    modifier: Modifier = Modifier,
    size: Dp = 12.dp,
    brush: Brush
) {
    Box(
        modifier = modifier
            .size(size = size)
            .clip(shape = CircleShape)
            .background(brush = brush)
    )
}