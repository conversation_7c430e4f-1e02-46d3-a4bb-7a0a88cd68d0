package com.app.messej.ui.home.publictab.huddles.poll

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.PollType
import com.app.messej.databinding.FragmentAllSchedulePollBinding
import com.chad.library.adapter.base.animation.AlphaInAnimation

class AllSchedulePollFragment : Fragment() {
    private lateinit var binding: FragmentAllSchedulePollBinding
    private var mAdapter: ItemAnswerPollAdapter? = null
    private val viewModel: ScheduledPollsViewModel by viewModels()
    private val args: AllSchedulePollFragmentArgs by navArgs()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_all_schedule_poll, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }


    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        when (args.pollType) {
            PollType.SCHEDULE_POLL -> binding.customActionBar.toolbar.title = resources.getString(R.string.title_schedule_poll)
            PollType.PAST_POLL -> binding.customActionBar.toolbar.title = resources.getString(R.string.title_past_poll)
            else -> {}
        }

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.selectedPoll.observe(viewLifecycleOwner) {
            mAdapter?.apply {
                if (data.size == 0 || it?.answers?.size == 0) {
                    setNewInstance(it?.answers?.toMutableList())
                } else {
                    setDiffNewData(it?.answers?.toMutableList())
                }
            }

        }
    }

    private fun setup() {
        args.pollId.let { viewModel.setPollData(it) }
        initAdapter()
    }

    private fun initAdapter() {
        mAdapter = ItemAnswerPollAdapter(mutableListOf(),args.pollType)

        val layoutMan = LinearLayoutManager(context)

        binding.listAllPollsScheduled.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }
        mAdapter!!.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true
            setDiffCallback(ItemAnswerPollAdapter.PollDiff)
        }
    }
}