package com.app.messej.ui.home.businesstab.operations.tasks

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentBusinessOperationsPremiumBinding
import com.app.messej.ui.customviews.ViewExtensions.setBlurredBackground
import com.app.messej.ui.home.businesstab.HomeBusinessFragmentDirections
import com.app.messej.ui.home.businesstab.HomeBusinessViewModel
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BusinessOperationsPremiumFragment : Fragment() {

    private lateinit var binding: FragmentBusinessOperationsPremiumBinding
    private val mViewModel: HomeBusinessViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_operations_premium, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                binding.layoutPremiumTop.setBlurredBackground(R.drawable.bg_business, requireContext())
            }
        }

        binding.actionClose.setOnClickListener { findNavController().popBackStack() }
        binding.actionPremiumUpgrade.setOnClickListener {
            val action = HomeBusinessFragmentDirections.actionGlobalUpgradePremiumFragment()
            (activity as MainActivity).navController.navigateSafe(action)
        }
    }
}