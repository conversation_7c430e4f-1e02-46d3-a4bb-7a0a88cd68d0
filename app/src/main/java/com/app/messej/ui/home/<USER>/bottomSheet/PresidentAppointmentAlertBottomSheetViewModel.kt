package com.app.messej.ui.home.gift.bottomSheet

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.UserBirthdayResponse
import com.app.messej.data.repository.AccountRepository

class PresidentAppointmentAlertBottomSheetViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo: AccountRepository = AccountRepository(application)

    private val _PresidentAppointment = MutableLiveData<UserBirthdayResponse?>()
    val PresidentAppointment: MutableLiveData<UserBirthdayResponse?> = _PresidentAppointment

    val user: CurrentUser?
        get() = if (accountRepo.loggedIn) accountRepo.user else null

    fun setArgs(value: UserBirthdayResponse?) {
        _PresidentAppointment.postValue(value)
    }
}