package com.app.messej.ui.home.publictab.huddles.poll

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.CreatePollLayoutRemoveOptionBinding
import com.app.messej.databinding.FragmentCreatePollBinding
import com.app.messej.databinding.LayoutConfirmCancelBinding
import com.app.messej.ui.home.publictab.huddles.poll.adapter.PollOptionsAdapter
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.DateValidatorPointForward
import com.google.android.material.datepicker.MaterialDatePicker
import java.time.Instant
import java.time.ZoneId


class CreatePollFragment : Fragment() {

    private lateinit var binding: FragmentCreatePollBinding
    private val viewModel: CreatePollViewModel by viewModels()
    private val args: CreatePollFragmentArgs by navArgs()
    var mAdapter: PollOptionsAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_create_poll, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUP()
        observe()
    }

    companion object {
        const val CREATE_POLL_SUCCESS = "poll_success"
        const val POLL_REQUEST_KEY = "poll_result"
        const val POLL_RESULT_SUCCESS = "poll_verified"
        const val POLL_CREATE_MODE = "poll_create_mode"
        const val POLL_SELECT_RESULT_KEY = "pollResultKey"
        const val POLL_RESPONSE = "pollResponse"
    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.createPollActionbar.toolbar)
        bindFlaxRateToolbarChip(binding.createPollActionbar.flaxRateChip)
        binding.createPollActionbar.toolBarTitle.text = if (args.type == "Edit") resources.getString(R.string.title_edit_poll) else resources.getString(R.string.title_create_poll)

    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setUP() {
        viewModel.setHuddleId(args.huddleId, args.pollId)
        if (args.pollId <= 0) {
            binding.actionPublish.text = getString(R.string.create_poll_publish)
        } else {
            binding.actionPublish.text = getString(R.string.common_update)
        }

        binding.textEnterQuestion.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    viewModel.validateQuestion(true)
                } else {
                    hideKeyboard()
                }
            }
        }

        val layoutManager = LinearLayoutManager(requireContext())
        mAdapter = PollOptionsAdapter(mutableListOf()).apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = false

            setOnItemChildClickListener { adapter, view, position ->

                if (args.type != "-1") {
                    MaterialDialog(requireContext()).show {
                        val view = DataBindingUtil.inflate<CreatePollLayoutRemoveOptionBinding>(layoutInflater, R.layout.create_poll_layout_remove_option, null, false)
                        customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                        cancelable(false)
                        view.nickNameTitle.text = getString(R.string.create_poll_are_you_sure_you_want_to_remove_this_option)
                        view.actionYes.setOnClickListener {
                            viewModel.removeItem(position)
                            dismiss()
                        }
                        view.actionCancel.setOnClickListener {
                            dismiss()
                        }
                    }
                } else {
                    viewModel.removeItem(position)
                }
            }
            setDiffCallback(PollOptionsAdapter.pollDiff)
        }

        binding.recyclerViewOption.layoutManager = layoutManager
        binding.recyclerViewOption.adapter = mAdapter

        binding.actionStartNow.setOnClickListener {

            if (binding.actionStartNow.isChecked) {
                viewModel.setStartNow()
            } else {
                viewModel.clearStartNow()
            }
        }
        binding.actionEndNow.setOnClickListener {
            if (binding.actionEndNow.isChecked) {
                viewModel.setOpen()
                binding.actionEndNow.isChecked = true
                viewModel.isOpenChecked(true)
            } else {
                viewModel.isOpenChecked(false)
                binding.actionEndNow.isChecked = false
            }
        }
        binding.textEnterDate1.apply {
            setOnKeyListener(null)
            editText?.setOnTouchListener { _, event ->
                if (event.action == MotionEvent.ACTION_UP) {
                    binding.actionStartNow.isChecked=false
                    val initialStartDate = viewModel.startDate.value?.atStartOfDay(ZoneId.systemDefault())?.toInstant()?.toEpochMilli()
                    context?.let {
                        showDatePicker(requireActivity().supportFragmentManager, it.getString(R.string.create_poll_label_start_date), initialStartDate) { it ->
                            viewModel.setStartDate(DateTimeUtils.parseMillisToDate(it))
                        }
                    }
                }
                return@setOnTouchListener false
            }
        }
        binding.textEnterDate2.apply {
            setOnKeyListener(null)
            editText?.setOnTouchListener { _, event ->
                if (event.action == MotionEvent.ACTION_UP) {
                    binding.actionEndNow.isChecked=false
                    val initialEndDate = viewModel.endDate.value?.atStartOfDay(ZoneId.systemDefault())?.toInstant()?.toEpochMilli()
                    context?.let {
                        showDatePicker(requireActivity().supportFragmentManager, it.getString(R.string.create_poll_label_end_date), initialEndDate) { it ->
                            viewModel.setEndDate(DateTimeUtils.parseMillisToDate(it))
                        }
                    }
                }
                return@setOnTouchListener false
            }
        }
        binding.actionAddMoreOption.setOnClickListener {
            viewModel.addOption()
        }
        binding.actionPublish.setOnClickListener {
            if (viewModel.validateCreatePollStage()) {
                viewModel.submitPoll()
            } else {
                Log.d("QWERTY", "ELSEEE")
            }

        }
        binding.actionCancel.setOnClickListener {

            if (viewModel.isDataAvailable()) {
                MaterialDialog(requireContext()).show {
                    val view = DataBindingUtil.inflate<LayoutConfirmCancelBinding>(layoutInflater, R.layout.layout_confirm_cancel, null, false)
                    customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                    cancelable(false)
                    view.nickNameTitle.text = resources.getString(R.string.sure_you_want_to_cancel)
                    view.actionConfirm.setOnClickListener {

                        if (args.pollId > 0){
                            findNavController().popBackStack()
                            setFragmentResult(POLL_SELECT_RESULT_KEY, bundleOf(POLL_RESPONSE to args.huddleId ))
                            dismiss()
                        }
                        else{
                            findNavController().popBackStack()
                            dismiss()
                        }
                    }
                    view.actionCancel.setOnClickListener {
                        dismiss()
                    }
                }
            } else {
                if (args.pollId > 0){
                    findNavController().popBackStack()
                    setFragmentResult(POLL_SELECT_RESULT_KEY, bundleOf(POLL_RESPONSE to args.huddleId ))
                }
                else{
                    findNavController().popBackStack()
                }
            }
        }
    }

    private fun observe() {
        viewModel.questionValid.observe(viewLifecycleOwner) {

                binding.textEnterQuestion.error = when (it) {
                    true -> {
                        binding.textEnterQuestion.isErrorEnabled = false
                        null
                    }
                    false -> {
                        binding.textEnterQuestion.isErrorEnabled = true
                        getString(R.string.create_poll_add_a_question)

                    }

                    null -> {
                        binding.textEnterQuestion.isErrorEnabled = false
                        null
                    }
                }
            }

            viewModel.createPollSuccess.observe(viewLifecycleOwner) {
                Toast.makeText(requireContext(), "$it", Toast.LENGTH_SHORT).show()
                findNavController().popBackStack()
                setFragmentResult(
                    POLL_CREATE_MODE, bundleOf(
                        POLL_REQUEST_KEY to CREATE_POLL_SUCCESS,
                        POLL_RESULT_SUCCESS to true
                ))

            }
            viewModel.apiResponseError.observe(viewLifecycleOwner) {
                binding.labelApiError.visibility = View.VISIBLE
                binding.labelApiError.text = it
            }


            viewModel.createPollDateError.observe(viewLifecycleOwner) {


                when (it) {
                    CreatePollViewModel.Companion.DateError.PAST -> {
                        binding.labelApiError.visibility = View.VISIBLE
                        binding.labelApiError.text = getString(R.string.create_poll_error_end_date_passed)
                    }

                    CreatePollViewModel.Companion.DateError.START_NULL -> {
                        binding.labelApiError.visibility = View.VISIBLE
                        binding.labelApiError.text = getString(R.string.create_poll_select_date)
                    }

                    CreatePollViewModel.Companion.DateError.PAST_START -> {
                        binding.labelApiError.visibility = View.VISIBLE
                        binding.labelApiError.text = getString(R.string.create_poll_error_start_date_passed)
                    }

                    else -> {
                        binding.labelApiError.visibility = View.GONE
                    }
                }

            }




        viewModel.isStartNow.observe(viewLifecycleOwner) {
            binding.actionPublish.text = if (it) resources.getString(R.string.create_poll_publish)
            else if (viewModel.pollId != -1) resources.getString(R.string.common_update)
            else resources.getString(R.string.create_poll_schedule)
        }


        viewModel.optionError.observe(viewLifecycleOwner) {
            if (it.equals(CreatePollViewModel.Companion.PollOptionError.SAME_OPTION)) {
                binding.labelError.visibility = View.VISIBLE
                binding.labelError.text = getString(R.string.create_poll_there_is_already_an_option)
            } else if (it.equals(CreatePollViewModel.Companion.PollOptionError.AT_LEAST_ONE)) {
                binding.labelError.visibility = View.VISIBLE
                binding.labelError.text = getString(R.string.create_poll_at_least_two_options_required)
            } else if (it.equals(CreatePollViewModel.Companion.PollOptionError.LIMIT_REACHED)) {
                binding.labelError.visibility = View.VISIBLE
                binding.labelError.text = "Limit reached.."
            } else {
                binding.labelError.visibility = View.GONE
            }
        }


        viewModel.optionsList.observe(viewLifecycleOwner) {

            mAdapter?.apply {
                Log.d("SUG", "observe: list go ${data.size} to ${it?.size}")
                viewModel.updatedOptionPosition(it.size)
                if (data.size == 0 || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }
        viewModel.isOpenNull.observe(viewLifecycleOwner) {
            binding.actionEndNow.isChecked = it
            viewModel.isOpenChecked(it)
        }

    }

    private fun showDatePicker(fragmentManager: FragmentManager, title: String, initialDate: Long?, onDateSelected: (Long) -> Unit) {
        val constraintsBuilder = CalendarConstraints.Builder().setValidator(DateValidatorPointForward.now())
        val datePicker = MaterialDatePicker.Builder.datePicker().setTitleText(title).setSelection(initialDate ?: Instant.now().toEpochMilli()).setInputMode(MaterialDatePicker.INPUT_MODE_CALENDAR)
            .setCalendarConstraints(constraintsBuilder.build()).build()

        datePicker.isCancelable = false
        datePicker.addOnPositiveButtonClickListener { selection ->
            onDateSelected(selection)
        }
        datePicker.show(fragmentManager, title)
    }



}