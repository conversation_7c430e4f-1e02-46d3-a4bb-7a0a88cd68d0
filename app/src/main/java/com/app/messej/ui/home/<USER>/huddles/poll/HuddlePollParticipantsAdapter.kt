package com.app.messej.ui.home.publictab.huddles.poll

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.entity.PollParticipant
import com.app.messej.databinding.ItemPollParticipantBinding


class HuddlePollParticipantsAdapter: PagingDataAdapter<PollParticipant, HuddlePollParticipantsAdapter.HuddlePollParticipantsViewHolder>(Diff) {
    inner class HuddlePollParticipantsViewHolder(private val binding: ItemPollParticipantBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(pollParticipant: PollParticipant) = with(binding) {
           binding.participant=pollParticipant
        }
    }

    override fun onBindViewHolder(holder: HuddlePollParticipantsViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HuddlePollParticipantsViewHolder {
        val binding = ItemPollParticipantBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return HuddlePollParticipantsViewHolder(binding)
    }


    object Diff : DiffUtil.ItemCallback<PollParticipant>() {
        override fun areItemsTheSame(oldItem: PollParticipant, newItem: PollParticipant) = oldItem == newItem
        override fun areContentsTheSame(oldItem: PollParticipant, newItem: PollParticipant) = oldItem == newItem
    }

}


