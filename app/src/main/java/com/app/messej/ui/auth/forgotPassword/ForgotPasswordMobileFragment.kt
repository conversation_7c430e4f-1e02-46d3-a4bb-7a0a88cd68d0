package com.app.messej.ui.auth.forgotPassword

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.databinding.LayoutForgotPasswordMobileBinding
import com.app.messej.ui.auth.forgotPassword.ForgotPasswordFragment.Companion.RECOVERY_TYPE
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class ForgotPasswordMobileFragment : Fragment() {
    private lateinit var binding: LayoutForgotPasswordMobileBinding

    private val viewModel: ForgotPasswordViewModel by viewModels({ requireParentFragment() })

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_forgot_password_mobile, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {

        binding.textInputMobileNumber.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && !this.text.isNullOrEmpty()) {
                    viewModel.didEnterPhoneNumber.postValue(true)
                } else {
                    viewModel.didEnterPhoneNumber.postValue(false)
                }
            }
        }

        binding.forgotPasswordCountryPicker.apply {
            setOnCountryChangeListener {
                viewModel.countryCode.postValue(binding.forgotPasswordCountryPicker.selectedCountryCodeWithPlus)
            }
            registerCarrierNumberEditText(binding.textInputMobileNumber.editText)
            setPhoneNumberValidityChangeListener { valid ->
                viewModel.setPhoneNumberValid(valid)
            }
        }

        binding.textInputMobileNumber.editText?.setOnEditorActionListener {
                view, actioId, event ->
            binding.textInputMobileNumber.editText!!.clearFocus()
            return@setOnEditorActionListener false
        }

        binding.forgotPasswordNext.apply {
            setOnClickListener {
                if(binding.textInputMobileNumber.editText?.text.toString().isNotEmpty()) {
                    binding.forgotPasswordMobileError.text = ""
                    viewModel.verifyMobileAccount(binding.textInputMobileNumber.editText?.text.toString(), binding.forgotPasswordCountryPicker.selectedCountryCodeWithPlus, OTPRequestMode = OTPRequestMode.RESET_MOBILE)
                }
            }
        }
    }

    private fun observe() {
        viewModel.showPhoneInvalidError.observe(viewLifecycleOwner) {
            if (!binding.textInputMobileNumber.editText?.text.isNullOrEmpty()) {
                binding.forgotPasswordMobileError.text = if (it) {
                    binding.forgotPasswordNext.isEnabled = false
                    binding.forgotPasswordMobileError.text
                    resources.getString(R.string.login_error_mobile_number)
                } else {
                    binding.forgotPasswordMobileError.text
                    "";null
                }
            }
        }
        viewModel.isMobileNextButtonVisible.observe(viewLifecycleOwner) {
            if (!binding.textInputMobileNumber.editText?.text.isNullOrEmpty()) {
                binding.forgotPasswordNext.isEnabled = it == false
            }
        }
        viewModel.isMobileVerified.observe(viewLifecycleOwner){
           if(it == true){
             val phoneNo=binding.textInputMobileNumber.editText?.text.toString()
             if(phoneNo.isNotEmpty()) {
                 findNavController().navigateSafe(ForgotPasswordFragmentDirections.actionGlobalAuthOTPFragment(OTPRequestMode.RESET_MOBILE, binding.forgotPasswordCountryPicker.selectedCountryCode, binding.textInputMobileNumber.editText?.text.toString(),"" ))
               }
           }
      }

        viewModel.verifyMobileError.observe(viewLifecycleOwner){
            binding.forgotPasswordNext.isEnabled = false
            binding.forgotPasswordMobileError.text=it
        }
        viewModel.unSelectedPage.observe(viewLifecycleOwner){
            if(it==0){ binding.textInputMobileNumber.editText?.setText("")
                binding.forgotPasswordMobileError.text = ""
                binding.forgotPasswordNext.isEnabled=false
            }
        }
    }

    companion object {

        fun newInstance( type: OTPRequestMode) = ForgotPasswordMobileFragment().apply {
            arguments = bundleOf(
                RECOVERY_TYPE to type)
        }
    }
}