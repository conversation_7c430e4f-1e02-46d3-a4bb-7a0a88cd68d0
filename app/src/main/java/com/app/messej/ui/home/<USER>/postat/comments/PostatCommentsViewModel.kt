package com.app.messej.ui.home.publictab.postat.comments

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.PostAtCommentPayload
import com.app.messej.data.model.PostatCommentLikePayload
import com.app.messej.data.model.PostatReplyCommentPayload
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.PostatRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.common.BaseCommentsViewModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PostatCommentsViewModel(application: Application) : BaseCommentsViewModel<PostCommentWithReplies>(application) {

    private val postatRepo = PostatRepository(getApplication())

    private val postId = MutableLiveData<String?>(null)
    val repository = FlashatDatastore()
    var undoPressed = false

    fun setPostId(id: String) {
        postId.postValue(id)
    }

    override val _commentList = postId.switchMap { id ->
        if (id != null) postatRepo.getPostatComments(id,commentCountCallback).liveData.cachedIn(viewModelScope) else null
    }

    override fun replaceSenderDetails(obj: PostCommentWithReplies, senderDetails: SenderDetails?): PostCommentWithReplies {
        return obj.copy(
            commentItem = obj.commentItem.copy(senderDetails = senderDetails)
        )
    }

    override suspend fun executeWriteComment(comment: String): ResultOf<Unit> {
        return postatRepo.writePostatComment(postId.value.toString(), PostAtCommentPayload(comment))
    }

    override suspend fun executeReplayComment(comment: String, mentionedUserId: Int): ResultOf<Unit> {
        val cleanedComment =  /*UserInfoUtil.encodeMentions(comment, listOf(mentionedUserDetails.value as AbstractUser))*/
            comment.replace(mentionedUser.value?:"", "").trim()
        Log.d("ENCODED_CMT","$cleanedComment")
        Log.d("ENCODED_CMT"," Reply Comment ID"+commentID.value)
        return postatRepo.writePostatReplyComment(postId.value.toString(), PostatReplyCommentPayload(reply = cleanedComment, replyTo =commentID.value , mentionedUserId = mentionedUserId))
    }

    val onNavigateToPrivateMessage = LiveEvent<Pair<String, Int>>()
    fun navigateToPrivateMessage(senderId: Int) {
        viewModelScope.launch {
            val roomId = HuddlesRepository(getApplication()).getPrivateChatRoomId(senderId)
            onNavigateToPrivateMessage.postValue(Pair(roomId, senderId))
        }
    }

    private val _onCommentDeleted = MutableLiveData<Boolean>()
    val onCommentDeleted: LiveData<Boolean> = _onCommentDeleted

    fun deletePostatComment(postId: String, commentId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (postatRepo.deletePostatComment(postId, commentId)) {
                is ResultOf.Success -> {
                    conNewCommendAdded.emit(true)
                    replyDeleted.emit(true)
                    setSelectedCommentId(null)
                    _onCommentDeleted.postValue(false)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    fun deletePostatCommentsFromDB(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            postatRepo.deletePostatCommentFromDB(id)
            _onCommentDeleted.postValue(true)
        }
    }

    fun deletePostatCommentReply(commentId: String,replyId:String) {
        viewModelScope.launch(Dispatchers.IO){
            when (val result: ResultOf<Boolean> = postatRepo.deletePostatReplyComment(commentId = commentId, replyId = replyId)) {
                is ResultOf.Success -> {
                    postatRepo.deleteReplyById(replyId,CommentType.POSTAT)
                    replyDeleted.emit(true)
                    _onCommentDeleted.postValue(false)
                    setSelectedReplyCommentId(null)
                }
                else -> {}
            }
        }
    }

    fun deletePostatCommentsReplyFromDB(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            postatRepo.deletePostatCommentReplyFromDB(id)
            _onCommentDeleted.postValue(true)
        }
    }

    private var commentID = MutableLiveData<String?>(null)
    private var mentionedUser = MutableLiveData<String?>(null)

    private val _isLikeLoading = MutableLiveData<Boolean>(false)
    val isLikeLoading: LiveData<Boolean> = _isLikeLoading

    val onPaidLikeInsufficientBalance = LiveEvent<Boolean>()
    val onPaidLikeError = LiveEvent<String>()

    fun sendCommentPaidLike(postatCommentLikePayload: PostatCommentLikePayload) {
        viewModelScope.launch(Dispatchers.IO) {
            _isLikeLoading.postValue(true)
            try {
                when (val result = postatRepo.sendPostatCommentLike(postatCommentLikePayload)) {
                    is ResultOf.Success -> {
                        conNewCommendAdded.emit(value = true)
                    }
                    is ResultOf.APIError -> {
                        if (result.code == 400) {
                            onPaidLikeInsufficientBalance.postValue(true)
                        } else {
                            onPaidLikeError.postValue(result.error.message)
                        }
                    }
                    is ResultOf.Error -> {
                        onPaidLikeError.postValue(result.errorMessage())
                    }
                }
                _isLikeLoading.postValue(false)

            } catch (e: Exception) {
                Log.d("PodiumLVM", "sendLikeByOthers: erPodiumActionTyperor: ${e.message}")
            }
        }
    }

    fun callReplies(commentId: String) {
        viewModelScope.launch {
            postatRepo.getPostatReplies(
                postatId = postId.value.toString(), page = 1, pageSize = 1, parentCommentId = commentId
            )
        }
    }

    fun setCommentId(commentId: String) {
        commentID.postValue(commentId)
    }

    fun setMentionedUser(user: String) {
        mentionedUser.postValue(user)
    }

    private val _selectedCommentId = MutableLiveData<String?>(null)
    val selectedCommentId: LiveData<String?> = _selectedCommentId

    private val _selectedReplyCommentId = MutableLiveData<String?>(null)
    val selectedReplyCommentId: LiveData<String?> = _selectedReplyCommentId

    fun setSelectedCommentId(id: String?) {
        Log.d("RePLY2", "setSelectedCommentId: $id")
        _selectedCommentId.postValue(id)
    }

    fun setSelectedReplyCommentId(id: String?) {
        Log.d("RePLY2", "setSelectedCommentId: $id")
        _selectedReplyCommentId.postValue(id)
    }

    fun validateDeleteAction(reply: Boolean,messageId: String, commentId: String) {
        viewModelScope.launch {
            undoPressed = false
            if (reply) {
                deletePostatCommentsReplyFromDB(commentId)
            } else {
                deletePostatCommentsFromDB(commentId)
            }

            delay(5000L) // Wait for 5 seconds

            if (!undoPressed) {
                if (reply) {
                  deletePostatCommentReply(commentId = messageId, replyId = commentId)
                } else {
                   deletePostatComment(postId = messageId ,commentId = commentId)
                }
            } else {
                conNewCommendAdded.emit(value = true)
            }
        }
    }

    fun undoComment() {
        undoPressed = true
        _onCommentDeleted.postValue(false)
        setSelectedCommentId(null)
        setSelectedReplyCommentId(null)
    }

    val isSendButtonEnabled: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun combineSources() {
            val isPosting = postingComment.value ?: false
            val text = commentText.value?.trim().orEmpty()
            val mention = mentionedUser.value?.toString().orEmpty()
            // Check if text contains @mention and also has other characters
            val hasMention = mention.isNotBlank() && text.contains(mention)
            val onlyMention = text == mention

            val value = !isPosting && text.isNotEmpty() && (/*!hasMention ||*/ !onlyMention)

            med.postValue(value)
        }
        med.addSource(postingComment) { combineSources() }
        med.addSource(commentText) { combineSources() }
        med.addSource(mentionedUser) { combineSources() }
        med
    }

}
