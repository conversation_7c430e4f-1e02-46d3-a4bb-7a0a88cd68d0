package com.app.messej.ui.home.businesstab.operations.tasks

import android.app.Application
import android.net.Uri
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.R
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.business.PayoutEligibility
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class BusinessRateViewModel(application: Application) : AndroidViewModel(application) {


    private val accountRepo = AccountRepository(application)
    private var businessRepo: BusinessRepository = BusinessRepository(application)
    private val chatRepo = ChatRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())
    val businessOperation: LiveData<BusinessOperation?> = businessRepo.getOperations()
    val businessStatement: LiveData<BusinessStatement?> = businessRepo.getStatements()

    private val _user = MutableLiveData<CurrentUser>()
    val user: LiveData<CurrentUser> = _user

    private val _apiResponse = MutableLiveData<String?>()
    val apiResponse: LiveData<String?> = _apiResponse

    private val _isUpdateMode = MutableLiveData<Boolean>(false)
    val isUpdateMode: LiveData<Boolean> = _isUpdateMode

    fun setUpdateMode(isUpdate: Boolean) {
        _isUpdateMode.value = isUpdate
    }

    var payoutEligibility = MutableLiveData<PayoutEligibility?>(null)
    private val _eligibilityCheckApiFail = MutableLiveData<String?>(null)
    private val eligibilityCheckApiFail: LiveData<String?> = _eligibilityCheckApiFail

    private val _isEditVisible = MutableLiveData(false)
    val isEditVisible: LiveData<Boolean?> = _isEditVisible

    private val _uri: MutableLiveData<Uri?> = MutableLiveData(null)
    val uri: LiveData<Uri?> = _uri
    private var imageCaptureTempFile: File? = null

    var screenshotUploading = LiveEvent<Boolean>()

    var isScreenShotUploaded = LiveEvent<Boolean>()


    init {
        getUserAccount()
        loadBusinessStatement()
    }

    fun addImage(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            imageCaptureTempFile = file
            cropAttachedMedia()

        }
    }

    fun onCropCancelled() {
        imageCaptureTempFile = null
    }

    val onTriggerCrop = LiveEvent<UCrop>()


    private fun cropAttachedMedia() {
        viewModelScope.launch(Dispatchers.IO) {
            val media = imageCaptureTempFile ?: return@launch
            val src = chatRepo.getUriForFile(media)
            val dest = chatRepo.getUriForFile(chatRepo.createTempImageFile())

            val options = UCrop.Options().apply {
                setCircleDimmedLayer(false)
                val color = ContextCompat.getColor(getApplication(), R.color.colorPrimary)
                setToolbarColor(color)
                setStatusBarColor(color)
                setToolbarTitle(getApplication<Application>().applicationContext.getString(R.string.common_crop))
            }
            val crop = UCrop.of(src, dest).withAspectRatio(1f, 1f).withOptions(options)
            onTriggerCrop.postValue(crop)
        }
    }

    private fun loadBusinessStatement() {
        viewModelScope.launch(Dispatchers.IO) {
            businessRepo.getBusinessStatements()
        }
    }

    private fun loadBusinessOperations() {
        viewModelScope.launch {
            businessRepo.getFlashAtActivityDetails()
        }
    }

    private fun getUserAccount() {
        viewModelScope.launch {
            _user.postValue(accountRepo.user)
        }
    }

    fun getImageUriForCapture() {
        viewModelScope.launch(Dispatchers.IO) {
            _uri.value?.let {
                val file = chatRepo.storeImageUriToTempFile(it)
                uploadAppReviewImage(file)
            }
        }
    }

    private fun uploadAppReviewImage(image: File) {
        screenshotUploading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val compressed = profileRepo.compressImage(image)
            when (businessRepo.uploadAppReviewImage(
                file = compressed
            )) {
                is ResultOf.APIError -> {
                    isScreenShotUploaded.postValue(false)
                }

                is ResultOf.Error -> {
                    isScreenShotUploaded.postValue(false)
                }

                is ResultOf.Success -> {
                    loadBusinessOperations()
                    _isEditVisible.postValue(false)
                    isScreenShotUploaded.postValue(true)
                }

                else -> {}
            }
            screenshotUploading.postValue(false)
        }
    }

    fun setCropUri(resultUri: Uri) {
        _uri.postValue(resultUri)
    }


}