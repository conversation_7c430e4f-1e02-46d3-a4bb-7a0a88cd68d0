package com.app.messej.ui.auth.profile

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.ui.AppBarConfiguration
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DDMMYYYY_SLASHED
import com.app.messej.databinding.FragmentRegisterCreateProfileBinding
import com.app.messej.ui.auth.common.GenderUIPackage
import com.app.messej.ui.common.GenderDropdownAdapter
import com.app.messej.ui.common.PolicyDocumentFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.DateValidatorPointBackward
import com.google.android.material.datepicker.MaterialDatePicker
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Locale


class RegisterProfileFragment : Fragment() {

    private lateinit var binding: FragmentRegisterCreateProfileBinding
    private val viewModel: RegisterProfileViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_create_profile, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        binding.privacyPolicyButton.setOnClickListener { findNavController().navigateSafe(RegisterProfileFragmentDirections.actionGlobalPolicyFragment(DocumentType.PRIVACY_POLICY,true)) }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar, AppBarConfiguration(setOf(R.id.createProfileFragment)))
    }


    @SuppressLint("ClickableViewAccessibility")
    fun setup() {

        val adapter = GenderDropdownAdapter(context?:return)
        (binding.genderDropdown.editText as? AutoCompleteTextView)?.apply {
            setAdapter(adapter)
            setOnItemClickListener { _, _, position, _ ->
                adapter.setSelectedPos(position)
                val item = adapter.getItem(position)
                onGenderSelected(item)
            }
            viewModel.gender.value?.let { gender ->
                when (gender) {
                    Gender.FEMALE -> onGenderSelected(adapter.getItem(0))
                    Gender.MALE -> onGenderSelected(adapter.getItem(1))
                    Gender.NOT_SAID -> onGenderSelected(adapter.getItem(2))
                }
            }
        }

        binding.textInputName.editText?.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                viewModel.didEnterName.postValue(true)
            }
        }

        binding.textInputDob.apply {
            setOnKeyListener(null)
            editText?.setOnTouchListener { _, event ->
                if (event.action == MotionEvent.ACTION_UP) {
                    val constraintsBuilder = CalendarConstraints.Builder().setValidator(DateValidatorPointBackward.now())
                    val datePicker = MaterialDatePicker.Builder
                        .datePicker()
                        .setTextInputFormat(SimpleDateFormat(FORMAT_DDMMYYYY_SLASHED, Locale.getDefault()))
                        .setTitleText(resources.getString(R.string.register_create_profile_dob_prompt))
                        .setSelection(
                            if (viewModel.dateOfBirth.value == null)
                                DateTimeUtils.getZonedDateTimeNow().toInstant().toEpochMilli()
                            else {
                                val date = viewModel.dateOfBirth.value?.atStartOfDay(ZoneId.of("GMT"))?.toInstant()?.toEpochMilli()
                                Log.i("TAG", "dateOUT: ${viewModel.dateOfBirth.value}: $date")
                                date
                            }
                        )
                        .setInputMode(MaterialDatePicker.INPUT_MODE_CALENDAR)
                        .setCalendarConstraints(constraintsBuilder.build()).build()

                    datePicker.isCancelable=false
                    datePicker.addOnPositiveButtonClickListener {
                        val date = ZonedDateTime.ofInstant(Instant.ofEpochMilli(it),ZoneId.of("GMT"))
                        val localDate = date.toLocalDate()
                        Log.i("TAG", "dateOUT: set $it: $date $localDate")
                        viewModel.setDob(localDate)
                    }
                    datePicker.show(requireActivity().supportFragmentManager, "dob")

                }
                return@setOnTouchListener false
            }
        }

        binding.createProfileNextButton.setOnClickListener { viewModel.setProfile() }
    }

    private fun updateNextButton() {
        binding.createProfileNextButton.isEnabled = (viewModel.setProfileLoading.value == false) && (viewModel.profileStageValid.value == true)
    }

    private fun observe() {
        viewModel.setProfileLoading.observe(viewLifecycleOwner) {
            binding.loginProgress.visibility = if (it) View.VISIBLE else View.GONE
            updateNextButton()
        }
        viewModel.profileStageValid.observe(viewLifecycleOwner) {
            updateNextButton()
        }
        viewModel.nameError.observe(viewLifecycleOwner) {
            binding.textInputName.error = when (it) {
                RegisterProfileViewModel.Companion.NameError.NONE -> {
                    binding.textInputName.isErrorEnabled = false
                    null
                }
                RegisterProfileViewModel.Companion.NameError.LT_MIN -> {
                    binding.textInputName.isErrorEnabled = true
                    resources.getString(R.string.register_create_profile_error_name_min)
                }
                RegisterProfileViewModel.Companion.NameError.GT_MAX -> {
                    binding.textInputName.isErrorEnabled = true
                    resources.getString(R.string.register_create_profile_error_name_max)
                }
                null -> {
                    binding.textInputName.isErrorEnabled = false
                    null
                }
            }
        }

        viewModel.dobError.observe(viewLifecycleOwner) {
            binding.textInputDob.apply {
                error = if (it) resources.getString(R.string.register_create_profile_error_min_age) else null
                isErrorEnabled = it
            }
        }

        viewModel.setProfileError.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(activity, it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onSetProfileComplete.observe(viewLifecycleOwner) {
            Toast.makeText(activity, resources.getString(R.string.register_create_profile_success_toast), Toast.LENGTH_SHORT).show()
            val action = RegisterProfileFragmentDirections.actionCreateProfileFragmentToCreateUsernameFragment()
            findNavController().navigateSafe(action)
        }

        setFragmentResultListener(PolicyDocumentFragment.REGISTER_DOCUMENT_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(PolicyDocumentFragment.REGISTER_DOCUMENT_RESULT_KEY)
            viewModel.policyAccepted.postValue(result)
        }
    }

    private fun onGenderSelected(item: GenderUIPackage) {
        binding.genderDropdown.editText?.setText(item.name)
        viewModel.gender.postValue(item.code)
        binding.genderDropdown.startIconDrawable = context?.let { ContextCompat.getDrawable(it, item.image) }
    }
}