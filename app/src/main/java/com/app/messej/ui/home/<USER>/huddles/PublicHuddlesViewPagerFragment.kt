package com.app.messej.ui.home.publictab.huddles

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.HuddleTab
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.databinding.LayoutPublicHuddlesBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureHuddlePostingAllowed
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PublicHuddlesViewPagerFragment : PublicHuddlesBaseFragment() {
    override lateinit var binding: LayoutPublicHuddlesBinding

    companion object {
        const val ARG_TAB = "tab"

        fun getTabBundle(tab: HuddleTab) = Bundle().apply {
            putInt(ARG_TAB,tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): HuddleTab {
            val tabInt = bundle?.getInt(ARG_TAB)?:0
            return HuddleTab.entries[tabInt]
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val tab = parseTabBundle(arguments)
        viewModel.setCurrentTab(tab,true)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_public_huddles, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun navigateToSearch() {
        if (viewModel.currentTab.value== HuddleTab.TAB_SUGGESTED) {
            findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToPublicHuddleSuggestionSearchFragment())
        } else {
            viewModel.currentTab.value?.let {tab->
                findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToPublicHuddleSearchFragment(tab))
            }
        }
    }

    override fun navigateToCreate() {
        ensureHuddlePostingAllowed {
            findNavController().navigateSafe(HomePublicFragmentDirections.actionGlobalCreateHuddleFragment(HuddleType.PUBLIC))
        }
    }

}