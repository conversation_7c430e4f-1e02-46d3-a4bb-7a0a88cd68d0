package com.app.messej.ui.home.businesstab.operations

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.PaymentMethodResponse
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


class PaymentMethodDetailViewModel(application: Application) : AndroidViewModel(application) {
    private var businessRepo = BusinessRepository(application)

    private val _paymentMethods = MutableLiveData<List<PaymentMethodResponse.PaymentMethod>>()
    val paymentMethods: LiveData<List<PaymentMethodResponse.PaymentMethod>> = _paymentMethods

    private val _selectedPaymentMethods = MutableLiveData<PaymentMethodResponse.PaymentMethod?>()
    val selectedPaymentMethods: LiveData<PaymentMethodResponse.PaymentMethod?> = _selectedPaymentMethods

    private val _dataLoading = MutableLiveData(false)
    val dataLoading: LiveData<Boolean> = _dataLoading

    /**
     * Fetches available payment methods from the repository
     */
    fun getPaymentMethods() {
        viewModelScope.launch(Dispatchers.IO) {
            _dataLoading.postValue(true)
            when (val result: ResultOf<PaymentMethodResponse> = businessRepo.getPaymentMethods()) {
                is ResultOf.Success -> {
                    _paymentMethods.postValue(result.value.paymentMethods)
                    _dataLoading.postValue(false)
                }
                is ResultOf.APIError -> {
                    _dataLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    _dataLoading.postValue(false)
                }
            }
        }
    }

    /**
     * Sets the selected payment method
     */
    fun setPaymentMethod(paymentMethod: PaymentMethodResponse.PaymentMethod) {
        _selectedPaymentMethods.postValue(paymentMethod)
    }

}
