package com.app.messej.ui.home.businesstab

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.repository.AccountRepository

class SuccessSellFlixAlertViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user
}
