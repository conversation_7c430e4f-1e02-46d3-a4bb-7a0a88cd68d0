package com.app.messej.ui.home.businesstab.adapter

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.appcompat.widget.AppCompatTextView
import com.app.messej.R
import com.app.messej.data.model.api.DealsPurpose

class DealsPurposeDropdownAdapter(c: Context, purpose: List<DealsPurpose>):
    ArrayAdapter<DealsPurpose>(c, R.layout.item_deals_purpose_dropdown,purpose) {

    private val mContext = c
    val mPurpose = purpose

    private var selectedPos: Int? = null
    fun setSelectedPos(pos: Int) {
        selectedPos = pos
    }

    override fun getItem(position: Int): DealsPurpose {
        return mPurpose[position]
    }

    override fun getCount(): Int {
        return mPurpose.size
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var cView = convertView
        if (cView == null) {
            val inflater = (mContext as Activity).layoutInflater
            cView = inflater.inflate(R.layout.item_deals_purpose_dropdown, parent, false)
        }
        try {

            getItem(position).let {
                cView?.apply {
                    val text: AppCompatTextView = findViewById(R.id.purpose_name)
                    text.text = it.purpose
                }
            }


        } catch (e: Exception) {
            e.printStackTrace()
        }
        return cView!!
    }
}