package com.app.messej.ui.home.publictab.huddles

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.databinding.FragmentPublicHuddlesSearchBinding
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils


class PublicHuddlesSearchFragment : Fragment() {

    private lateinit var binding: FragmentPublicHuddlesSearchBinding

    private var mAdapter: PublicHuddlesAdapter? = null

    private val viewModel: PublicHuddlesSearchViewModel by viewModels()
    private val args: PublicHuddlesSearchFragmentArgs by navArgs()


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_huddles_search, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                setNavigationIcon(R.drawable.ic_close)
                setNavigationIconTint(resources.getColor(R.color.white))
            }
        }
    }

    private fun observe() {
        viewModel.huddleList.observe(viewLifecycleOwner){
            it?:return@observe
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun setup() {
        viewModel.setTab(args.tab)
        initAdapter()

        binding.customActionBar.apply {
            keyword = viewModel.searchKeyword
            showKeyboard(searchBox)
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = PublicHuddlesAdapter(layoutInflater,viewModel.user.id, object: PublicHuddlesAdapter.ItemListener {
            override fun onItemClick(item: AbstractHuddle, position: Int) {
                findNavController().navigateSafe(HomePublicFragmentDirections.actionGlobalNavChatHuddle(item.id))
            }

            override fun onItemLongClick(item: PublicHuddle, position: Int) {

            }

            override fun isVisitor(): Boolean {
              return true
            }

        })

        val layoutMan = LinearLayoutManager(context)

        binding.huddleList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition()==0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }
}