package com.app.messej.ui.home.publictab.huddles.comments

import android.app.Application
import android.view.View
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.TerminalSeparatorType
import androidx.paging.cachedIn
import androidx.paging.insertHeaderItem
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.PostCommentPayload
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.api.ReportToManagerRequest
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.entity.Sticker
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.home.publictab.huddles.chat.HuddleChatViewModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class HuddlePostCommentsViewModel(application: Application) : HuddleChatViewModel(application) {

    private var postID = MutableLiveData<String?>(null)
    var commentText = MutableLiveData("")

    var postingComment = MutableLiveData(false)
    var hasEditTextFocused = MutableLiveData(false)

    var onDeleteComment = LiveEvent<Boolean>()
    var onAddComment = LiveEvent<Boolean>()

    var onCommentReportCancel = LiveEvent<Boolean>()

    fun setPost(huddle: Int, postId: String) {
        huddleID.postValue(huddle)
        postID.postValue(postId)
        setHuddleId(huddle)
    }

    val post = postID.switchMap {
        return@switchMap it?.let { id ->
            huddleRepo.getHuddleChatWithMediaLiveData(id)
        }
    }

    val postHasComments = post.map {
        return@map it?.message?.senderDetails?.premium == true
    }

    val canPostComments: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            med.postValue(canInteractWithChat.value==true && huddlePrivacy.value?.canComment==true && post.value?.message?.reported == false)
        }
        med.addSource(huddlePrivacy) { update() }
        med.addSource(canInteractWithChat) { update() }
        med.addSource(post) { update() }
        med
    }

    fun onTargetFocusChanged(view: View, hasFocus: Boolean){
        hasEditTextFocused.postValue(hasFocus)
    }

    override val _chatList: LiveData<PagingData<ChatMessageUIModel>> = post.switchMap { post ->
        post ?: return@switchMap null
        val huddleId = huddleID.value ?: return@switchMap null
        val postId = postID.value ?: return@switchMap null

        huddleRepo.getHuddlePostComments(huddleId, postId).liveData.map { pagingData ->
            val flags = _countryList.value
            var chatData: PagingData<ChatMessageUIModel> = pagingData.map { comment ->
                ChatMessageUIModel.PostCommentModel(comment).apply {
                    comment.senderDetails?.countryCode?.let { cc ->
                        countryFlag = flags?.get(cc)
                    }
                }
            }
            if (post.message.totalComments == 0 && post.message.senderDetails?.premium==true) {
                chatData = chatData.insertHeaderItem(
                    item = ChatMessageUIModel.PostCommentEmptyModel, terminalSeparatorType = TerminalSeparatorType.SOURCE_COMPLETE
                )
            }
            chatData = chatData.insertHeaderItem(item = ChatMessageUIModel.PostCommentHeaderModel(post.message.totalComments), terminalSeparatorType = TerminalSeparatorType.SOURCE_COMPLETE)
                .insertHeaderItem(item = ChatMessageUIModel.ChatMessageModel(post).apply {
                    post.message.senderDetails?.countryCode?.let { cc ->
                        countryFlag = flags?.get(cc)
                    }
                }, terminalSeparatorType = TerminalSeparatorType.SOURCE_COMPLETE)
            return@map chatData
        }.cachedIn(viewModelScope)
    }

    override val showChats: LiveData<Boolean> = MutableLiveData(true)

    private fun sendComment(payload: PostCommentPayload) {
        val huddleId = huddleID.value ?: return
        val postId = postID.value ?: return
        postingComment.postValue(true)
        viewModelScope.launch {
            when (val result: ResultOf<Unit> = huddleRepo.writeComment(huddleId, postId, payload)) {
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
                is ResultOf.Success -> {
                    commentText.postValue("")
                    onAddComment.postValue(true)
                }
            }
            postingComment.postValue(false)
        }
    }

    fun writeComment() {
        val comment = commentText.value?: return
        sendComment(PostCommentPayload(comment))
    }

    fun postSticker(sticker: Sticker) {
        val payload = PostCommentPayload(null)
        payload.setMedia(sticker.mediaMeta)
        sendComment(payload)
    }

    fun deleteComment(commentId: String) {
        val huddleId = huddleID.value ?: return
        val postId = postID.value ?: return
        viewModelScope.launch {
            when (val result: ResultOf<Unit> = huddleRepo.deleteComment(huddleId, postId, commentId)) {
                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }

                is ResultOf.Success -> {
                    result.value
                    onDeleteComment.postValue(true)
                    huddleRepo.deletePostSingleComment(commentId)
                }
            }
        }
    }

    fun cancelReportComment(msg: PostCommentItem) {
        if (msg.isReported == true) {
            val huddleId = huddleID.value ?: return
            val postId = postID.value ?: return

            viewModelScope.launch(Dispatchers.IO){
                val req = ReportToManagerRequest.CommentReportCancelRequest(huddleId, postId, msg.commentId)
                when(chatRepo.cancelReportComment(req)){
                    is ResultOf.APIError -> {

                    }
                    is ResultOf.Error -> {

                    }
                    is ResultOf.Success -> {
                        onCommentReportCancel.postValue(true)
                    }
                }
            }
        }
    }

    override fun getSenderForReply(msg: AbstractChatMessage): SenderDetails? = null
}