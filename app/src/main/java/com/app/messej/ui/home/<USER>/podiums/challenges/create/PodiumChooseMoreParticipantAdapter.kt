package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.databinding.ItemChooseMoreContributorsListBinding

class PodiumChooseMoreParticipantAdapter(private val mListener: ItemListener) : PagingDataAdapter<PodiumParticipant, PodiumChooseMoreParticipantAdapter.ParticipantsListViewHolder>(
    PodiumUserDiff
) {

    interface ItemListener {
        fun onItemCheckChanged(item: PodiumParticipant, onChecked: Boolean)
        fun isAlreadySelected(item: PodiumParticipant): Boolean
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = ParticipantsListViewHolder(
        ItemChooseMoreContributorsListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun onBindViewHolder(holder: ParticipantsListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }

    inner class ParticipantsListViewHolder(private val binding: ItemChooseMoreContributorsListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumParticipant, position: Int) = with(binding) {
            Log.d("LiveSpeakers", "bind: $item")

            user = item
            checkbox.isChecked = mListener.isAlreadySelected(item)
            checkbox.setOnCheckedChangeListener { _, isChecked ->
                mListener.onItemCheckChanged(item, isChecked)
            }
        }
    }

    object PodiumUserDiff : DiffUtil.ItemCallback<PodiumParticipant>() {
        override fun areItemsTheSame(oldItem: PodiumParticipant, newItem: PodiumParticipant) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumParticipant, newItem: PodiumParticipant) = oldItem == newItem
    }
}