package com.app.messej.ui.home.publictab.maidan

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.repository.PodiumRepository

class TopSupportersViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)

    private val _podiumId = MutableLiveData<String>()
    val podiumId: LiveData<String> = _podiumId

    private val _challengeId = MutableLiveData<String>()
    val challengeId: LiveData<String> = _challengeId

    fun setParams(podiumId: String, challengeId: String) {
        _challengeId.value = challengeId
        _podiumId.value = podiumId
    }

    val topSupportersList = podiumId.switchMap {
        podiumRepository.getMaidanTopSupportersListPager(podiumId.value.orEmpty(), challengeId.value.orEmpty()).liveData.cachedIn(viewModelScope)
    }

    private val _liveUsersListLoading = MutableLiveData(false)
    val liveUsersListLoading: LiveData<Boolean> = _liveUsersListLoading

}