package com.app.messej.ui.home.gift

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.airbnb.lottie.LottieCompositionFactory
import com.app.messej.R
import com.app.messej.data.model.enums.GiftTabs
import com.app.messej.data.model.enums.GiftThumbnail
import com.app.messej.databinding.FragmentGiftAlertBottomSheetBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.bumptech.glide.Glide
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class GiftAlertBottomSheetFragment : BottomSheetDialogFragment() {
    private lateinit var binding: FragmentGiftAlertBottomSheetBinding
    private val viewModel: GiftNotificationViewModel by viewModels()
    private val navArgs: GiftAlertBottomSheetFragmentArgs by navArgs()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_alert_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        observe()
    }

    private fun observe() {
        viewModel.onDialogueTimeOver.observe(viewLifecycleOwner) {
            if (it) {
                dialog?.dismiss()
            }
        }

        viewModel.onGiftReplySend.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(requireContext(), getString(R.string.title_reply_sent_successfully), Toast.LENGTH_SHORT).show()
            }

        }


        viewModel.gift.observe(viewLifecycleOwner) {
            it?.let { response ->
                when (val thumb = response.giftThumbnail) {
                    is GiftThumbnail.GifImage -> {
                        Glide.with(requireContext()).asGif().load(thumb.url).into(binding.imgStaticGift)
                    }

                    is GiftThumbnail.StaticImage -> {
                        Glide.with(requireContext()).asBitmap().load(thumb.url).into(binding.imgStaticGift)
                    }

                    is GiftThumbnail.LottieAnimation -> {
                        if (!response.animationUrl.isNullOrBlank()) {
                            LottieCompositionFactory.fromUrl(requireContext(), thumb.url).addListener { composition ->
                                binding.lottieView.setComposition(composition)
                            }.addFailureListener {
                                Log.d("ErrorInLoadingLottie", it.message.toString())
                            }
                        }
                    }

                    else -> {}
                }
            }

        }
    }

    private fun setup() {
        viewModel.setArgs(navArgs.giftId, navArgs.senderId, navArgs.fromSocket)
        binding.btnGiftClose.setOnClickListener {
            dialog?.dismiss()
        }
        binding.checkMyGift.setOnClickListener {
            findNavController().navigateSafe(GiftListBottomSheetFragmentDirections.actionGlobalGiftFileFragment(GiftTabs.GIFT_RECEIVED.ordinal))
        }
    }
}