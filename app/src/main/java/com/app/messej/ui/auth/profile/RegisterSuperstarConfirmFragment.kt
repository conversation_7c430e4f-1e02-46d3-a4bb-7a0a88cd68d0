package com.app.messej.ui.auth.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterSuperstarConfirmBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class RegisterSuperstarConfirmFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentRegisterSuperstarConfirmBinding

    private val args: RegisterSuperstarConfirmFragmentArgs by navArgs()

    private val viewModel: RegisterSuperstarViewModel by navGraphViewModels(R.id.navigation_register_superstar)

    companion object {
        const val SUPERSTAR_CONFIRM_REQUEST_KEY = "confirmSuperstar"
        const val SUPERSTAR_CONFIRM_RESULT_KEY = "confirmSuperstarPair"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL,R.style.Widget_Flashat_SuperstarConfirm_BottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_superstar_confirm, container, false)
        binding.apply {
            lifecycleOwner = viewLifecycleOwner
            user = viewModel.selectedSuperstar.value
        }
        return binding.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        binding.confirmButton.setOnClickListener {
            setFragmentResult(SUPERSTAR_CONFIRM_REQUEST_KEY, bundleOf(SUPERSTAR_CONFIRM_RESULT_KEY to true))
            findNavController().popBackStack()
        }

        binding.changeButton.setOnClickListener {
            setFragmentResult(SUPERSTAR_CONFIRM_REQUEST_KEY, bundleOf(SUPERSTAR_CONFIRM_RESULT_KEY to false))
            findNavController().popBackStack()
        }
    }


}