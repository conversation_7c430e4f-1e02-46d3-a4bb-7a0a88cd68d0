package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumTheaterLikesBottomSheetBinding
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.ViewUtils


class PodiumTheaterLikesBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private var mTheaterLikesAdapter: PodiumTheaterLikesAdapter? = null
    private val viewModel: PodiumTheaterChargesViewModel by viewModels()
    private lateinit var binding: FragmentPodiumTheaterLikesBottomSheetBinding
    private val args: PodiumTheaterLikesBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_theater_likes_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addObservers()
    }

    private fun setup() {
        viewModel.setPodiumId(args.podiumId)

        mTheaterLikesAdapter = PodiumTheaterLikesAdapter()

        binding.actionRefreshList.setOnClickListener {
            it.animate().rotation(it.rotation + 360f).setInterpolator(AccelerateDecelerateInterpolator()) //to animate the refresh button for UX
            mTheaterLikesAdapter?.refresh()
        }

        binding.likedUsers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mTheaterLikesAdapter
        }

        mTheaterLikesAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
            }
        }
    }

    private fun addObservers() {
        viewModel.theaterLikesPager.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mTheaterLikesAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }
    }
}