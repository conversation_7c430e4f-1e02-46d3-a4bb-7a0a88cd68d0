package com.app.messej.ui.auth

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Typeface
import android.os.Bundle
import android.text.BidiFormatter
import android.text.style.StyleSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.ui.AppBarConfiguration
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.utils.OTPUtil
import com.app.messej.databinding.FragmentRegisterOtpBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.android.gms.common.api.Status
import com.google.android.gms.tasks.Task
import `in`.aabhasjindal.otptextview.OTPListener

class AuthOTPFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentRegisterOtpBinding

    private var mBroadcastReceiver: BroadcastReceiver? = null

    private val viewModel: AuthOTPViewModel by viewModels()
    private val commonHomeViewModel: CommonHomeViewModel by activityViewModels()

    private val args: AuthOTPFragmentArgs by navArgs()

    companion object {
        const val OTP_REQUEST_KEY = "otp_request"
        const val OTP_RESULT_KEY = "otp_result"
        const val OTP_RESULT_MODE = "otp_result_mode"
        const val OTP_RESULT_PHONE_NO = "otp_result_phone"
        const val OTP_RESULT_EMAIL = "otp_result_email"
        const val OTP_RESULT_COUNTRY_CODE = "otp_result_country_code"

        const val OTP_RESULT_SUCCESS = "otp_verified"
        const val OTP_RESULT_SWITCH_MODE = "otp_switch_mode"
        const val OTP_CHANGE_MODE = "otp_change_mode"
        const val OTP_RESULT_CHANGE_PARAM = "otp_change_param"

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_otp, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setMode(args.requestMode,args.countryCode,args.phoneNumber,args.email)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar, AppBarConfiguration(setOf(R.id.authOTPFragment)))
        activity?.actionBar?.setDisplayHomeAsUpEnabled(false)
        mBroadcastReceiver?.let {
            ContextCompat.registerReceiver(requireActivity(),it, IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION), SmsRetriever.SEND_PERMISSION, null, ContextCompat.RECEIVER_EXPORTED)
        }
    }

    override fun onStop() {
        super.onStop()
        mBroadcastReceiver?.let {
            activity?.unregisterReceiver(it)
        }
    }

    private fun setup() {

        addAsMenuHost()

        captureSMS()
        commonHomeViewModel.pendingAppInvite.value?.let {
            viewModel.setReferralCode(it)
        }
        viewModel.requestOTP()

        binding.resendOtpButton.setOnClickListener {
            viewModel.requestOTP(resend = true)
        }

        binding.otpNextButton.setOnClickListener {
            viewModel.verifyOTP()
        }

        binding.passwordEmailSwitchButton.setOnClickListener {
            findNavController().popBackStack()
            setFragmentResult(
                OTP_CHANGE_MODE, bundleOf(
                OTP_RESULT_KEY to OTP_RESULT_SUCCESS,
                OTP_RESULT_MODE to viewModel.otpRequestMode.value!!.name
            ))
        }
        when(args.requestMode) {
            OTPRequestMode.REGISTER_MOBILE, OTPRequestMode.RESET_MOBILE, OTPRequestMode.VERIFY_MOBILE -> {
                binding.apply {
                    otpImage.setImageResource(R.drawable.im_register_verify_otp)
                    changeNumberButton.text = getString(R.string.register_button_change_number)
                    labelOtpPrompt.text = getString(R.string.register_otp_label_prompt)
                }
            }
            OTPRequestMode.RESET_EMAIL,OTPRequestMode.REGISTER_EMAIL, OTPRequestMode.VERIFY_EMAIL -> {
                binding.apply {
                    otpImage.setImageResource(R.drawable.im_email_verify_otp)
                    changeNumberButton.text = getString(R.string.forgot_password_change_email)
                    labelOtpPrompt.text = getString(R.string.forgot_password_email_propt)
                }
            }
        }

        when(args.requestMode) {
            OTPRequestMode.REGISTER_MOBILE,OTPRequestMode.REGISTER_EMAIL,OTPRequestMode.VERIFY_MOBILE, OTPRequestMode.VERIFY_EMAIL -> {
                binding.OTPModeSwitchGroup.visibility=View.GONE
            }
            OTPRequestMode.RESET_MOBILE -> {
                binding.apply {
                    OTPModeSwitchGroup.visibility=View.VISIBLE
                    passwordEmailSwitchButton.visibility=View.VISIBLE
                    passwordEmailSwitchButton.text = getString(R.string.forgot_password_use_email)

                }
            }
            OTPRequestMode.RESET_EMAIL -> {
                binding.apply {
                    OTPModeSwitchGroup.visibility=View.VISIBLE
                    passwordEmailSwitchButton.visibility=View.VISIBLE
                    passwordEmailSwitchButton.text = getString(R.string.forgot_password_use_mobile)
                }
            }
        }

        binding.changeNumberButton.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun observe() {
        viewModel.activeOTPDestination.observe(viewLifecycleOwner) {
            it?.let {phoneNo->
                val text=String.format( getString(R.string.register_otp_label_info),  BidiFormatter.getInstance().unicodeWrap(phoneNo))
                binding.labelOtpInfo.text = text.highlightOccurrences(phoneNo){StyleSpan(Typeface.BOLD)}
            }
        }

        viewModel.didRequestOTP.observe(viewLifecycleOwner) {
            if(it==true) {
                if(args.requestMode.isEmailType){
                    Toast.makeText(activity, resources.getString(R.string.register_otp_label_info_toast, viewModel.email.value), Toast.LENGTH_SHORT).show()
                }else{
                    Toast.makeText(activity, resources.getString(R.string.register_otp_label_info_toast, viewModel.phoneNumber.value), Toast.LENGTH_SHORT).show()
                }
            }
        }

        viewModel.requestOTPError.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(activity, it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.otpSubmitValid.observe(viewLifecycleOwner) {
            binding.otpNextButton.isEnabled = shouldEnableNextButton()
        }

        binding.otpView.otpListener = object: OTPListener {
            override fun onInteractionListener() {
                viewModel.setOTP(binding.otpView.otp.orEmpty())
            }

            override fun onOTPComplete(otp: String) {
                viewModel.setOTP(otp)
            }
        }

        viewModel.otp.observe(viewLifecycleOwner) {
            binding.otpView.setOTP(it)
        }

        // Verify OTP
        viewModel.verifyOTPLoading.observe(viewLifecycleOwner) {
            binding.otpNextButton.isEnabled = shouldEnableNextButton()
        }

        viewModel.verifyOTPError.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(activity, it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onOTPVerified.observe(viewLifecycleOwner){
            if (it) {
                findNavController().popBackStack()
                setFragmentResult(OTP_REQUEST_KEY, bundleOf(
                    OTP_RESULT_KEY to OTP_RESULT_SUCCESS,
                    OTP_RESULT_MODE to viewModel.otpRequestMode.value!!.name,
                    OTP_RESULT_PHONE_NO to viewModel.phoneNumber.value.toString(),
                    OTP_RESULT_COUNTRY_CODE to viewModel.countryCode.value.toString(),
                    OTP_RESULT_EMAIL to viewModel.email.value.toString(),
                    OTP_RESULT_SWITCH_MODE to viewModel.otpRequestMode.value
                ))
            }
        }
    }

    private fun shouldEnableNextButton(): Boolean {
        return viewModel.verifyOTPLoading.value==false && viewModel.otpSubmitValid.value==true
    }

    private fun captureSMS() {

        mBroadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (SmsRetriever.SMS_RETRIEVED_ACTION == intent?.action) {
                    val extras = intent.extras
                    val status = extras!!.get(SmsRetriever.EXTRA_STATUS) as Status

                    when (status.statusCode) {
                        CommonStatusCodes.SUCCESS -> {
                            // Get SMS message contents
                            val message: String = extras.getString(SmsRetriever.EXTRA_SMS_MESSAGE,"")
                            val otp = OTPUtil.parseOTP(message).orEmpty()
                            Log.d("OTP", otp)
                            viewModel.setOTP(otp)
                        }

                        CommonStatusCodes.TIMEOUT -> {
                            // Waiting for SMS timed out (5 minutes)
                            // Handle the error ...
                        }
                    }
                }
            }
        }


        val client = SmsRetriever.getClient(requireActivity() /* context */)

        val task: Task<Void> = client.startSmsRetriever()

        task.addOnSuccessListener {
            // Successfully started retriever, expect broadcast intent
        }

        task.addOnFailureListener {
            // Failed to start retriever, inspect Exception for more details
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_cancel, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when(menuItem.itemId){
            R.id.item_cancel->{
                when(args.requestMode) {
                    OTPRequestMode.REGISTER_MOBILE ,OTPRequestMode.REGISTER_EMAIL-> {
                        findNavController().navigateSafe(AuthOTPFragmentDirections.actionGlobalNavGraphLogin())
                    }
                    OTPRequestMode.VERIFY_MOBILE, OTPRequestMode.VERIFY_EMAIL,OTPRequestMode.RESET_MOBILE,OTPRequestMode.RESET_EMAIL -> {
                        findNavController().popBackStack()
                    }
                }
            }
        }
        return true
    }
}