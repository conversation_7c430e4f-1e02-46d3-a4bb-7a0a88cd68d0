package com.app.messej.ui.home.publictab.podiums.create

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentEditTheaterFeeBinding
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showToast

class AdvancedSettingsFragment : Fragment() {

    private lateinit var binding: FragmentEditTheaterFeeBinding
    private val viewModel: CreatePodiumViewModel by viewModels()
    private val args: AdvancedSettingsFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_edit_theater_fee, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {

        super.onStart()

        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed:")
                showBackPressAlert()
            }
        })
        (activity as MainActivity).setupActionBar(binding.toolbar)
        binding.toolbar.apply {
            setNavigationOnClickListener {
                showBackPressAlert()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        viewModel.setMode(args.podiumId)

        binding.btnUpdate.setOnClickListener {
            confirmAction(
                message = R.string.podium_advanced_settings_edit__confirm
            ) {
                viewModel.editPodium(false)
            }
        }

        binding.btnCancel.setOnClickListener {
            showBackPressAlert()
        }
    }

    private fun observe(){
        viewModel.onPodiumCreated.observe(viewLifecycleOwner){
            showToast(message = R.string.podium_advanced_settings_edit_success)
            findNavController().popBackStack()
        }

        viewModel.podiumDetailMultiStateView.observe(viewLifecycleOwner) {
            binding.multiStateView.viewState = it
        }
    }

    private fun showBackPressAlert() {
        confirmAction(
            message = R.string.podium_advanced_settings_confirm_cancel
        ) {
            if (view != null && viewLifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                findNavController().popBackStack()
            }
        }
    }
}