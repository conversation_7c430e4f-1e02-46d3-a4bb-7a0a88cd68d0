package com.app.messej.ui.home.publictab.huddles.create

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.databinding.FragmentHuddleAddParticipantsBinding
import com.app.messej.ui.home.publictab.huddles.create.AddParticipantsViewModel.ParticipantsTab
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class AddHuddleParticipantsFragment : Fragment() {
    private lateinit var binding: FragmentHuddleAddParticipantsBinding
    private lateinit var mPagerAdapter: FragmentStateAdapter

    private val args: AddHuddleParticipantsFragmentArgs by navArgs()
    private val viewModel: AddParticipantsViewModel by viewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_add_participants, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        setTabNumber()
    }

    private fun observe() {
        viewModel.selectedMembers.observe(viewLifecycleOwner){
            binding.sendInvitationButton.text = resources.getString(R.string.add_participants_invite_button, it)
        }

        viewModel.invitationSent.observe(viewLifecycleOwner){
            Toast.makeText(requireContext(), getString(R.string.add_participants_invite_send), Toast.LENGTH_SHORT).show()
        }

        viewModel.huddle.observe(viewLifecycleOwner) {
            binding.shareHuddleInviteLayout.visibility = View.VISIBLE
            when(it?.huddleType){
                HuddleType.PRIVATE, HuddleType.PUBLIC -> binding.huddleShareLink.text=getString(R.string.private_and_public_send_invitation)
                else -> binding.shareHuddleInviteLayout.visibility = View.GONE
            }
        }

        viewModel.onInviteError.observe(viewLifecycleOwner){
//            Toast.makeText(context, it, Toast.LENGTH_LONG).show()
            Snackbar.make(binding.root, it, Snackbar.LENGTH_LONG).show()
        }

        viewModel.onParticipantsLimitExceedError.observe(viewLifecycleOwner) {
            Snackbar.make(binding.root, resources.getString(R.string.huddle_participant_request_limit_exceed), Snackbar.LENGTH_LONG).show()
        }
    }

    private fun switchToTab(number: Int) {
        lifecycleScope.launch {
            delay(50)
            withContext(Dispatchers.Main) {
                binding.participantsPager.setCurrentItem(number, true)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.private_and_public_send_invitation)
        if (args.showFinish) {
            (activity as MainActivity?)?.supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(false)
                setHomeButtonEnabled(false)
            }

            (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    showBackPressAlert()
                }
            })
        }

    }

    private fun setup() {
        binding.shareHuddleInviteLayout.visibility=View.GONE
        binding.finishButton.visibility = if(args.showFinish) View.VISIBLE else View.GONE
        viewModel.setHuddleId(args.huddleId)
        mPagerAdapter = object : FragmentStateAdapter(childFragmentManager,viewLifecycleOwner.lifecycle) {

            override fun getItemCount(): Int = ParticipantsTab.values().size

            override fun createFragment(position: Int): Fragment {
                val tab = ParticipantsTab.values().getOrNull(position) ?: throw java.lang.IllegalArgumentException("There should only be 3 tabs")
                return AddParticipantsListFragment().apply {
                    arguments = AddParticipantsListFragment.getTabBundle(tab)
                }
            }
        }

        binding.participantsPager.apply {
            isUserInputEnabled = false
            adapter = mPagerAdapter
            
        }

        binding.participantsTab.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                viewModel.resetSearch()
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }
        })

        TabLayoutMediator(binding.participantsTab, binding.participantsPager) { tab, position ->
            when (position) {
                0 -> {
                    tab.text = resources.getString(R.string.profile_dears)
                    tab.setIcon(R.drawable.ic_user_dears)
                }
                1 -> {
                    tab.text = resources.getString(R.string.profile_fans)
                    tab.setIcon(R.drawable.ic_user_fans)
                }
                2 -> {
                    tab.text = resources.getString(R.string.profile_likers)
                    tab.setIcon(R.drawable.ic_user_likers)
                }
            }
        }.attach()

        //The share link feature should be removed as per the new requirements.
        binding.shareHuddleInviteLayout.setOnClickListener {
            val inviteText = if (viewModel.huddle.value!!.isPrivate) resources.getString(R.string.huddle_info_share_text_group, viewModel.huddle.value?.name, viewModel.huddle.value?.inviteLink)
            else resources.getString(R.string.huddle_info_share_text, viewModel.huddle.value?.name, viewModel.huddle.value?.inviteLink)

            if (!inviteText.isEmpty()) {
                val sendIntent: Intent = Intent().apply {
                    action = Intent.ACTION_SEND
                    putExtra(Intent.EXTRA_TEXT, inviteText)
                    type = "text/plain"
                }
                val shareIntent = Intent.createChooser(sendIntent, null)
                startActivity(shareIntent)
            }
        }

        binding.sendInvitationButton.setOnClickListener {
            viewModel.sendInvitations()
        }

        binding.finishButton.setOnClickListener {
            goToChatPage()
        }
    }

    private fun goToChatPage() {
        val type = viewModel.huddle.value?.huddleType?: return
        val action = if(type==HuddleType.PRIVATE) AddHuddleParticipantsFragmentDirections.actionGlobalNavigationChatGroup(args.huddleId)
        else AddHuddleParticipantsFragmentDirections.actionGlobalNavChatHuddle(args.huddleId)
        findNavController().navigateSafe(action,navOptions = NavOptions.Builder()
            .setPopUpTo(R.id.addParticipantsFragment, inclusive = true)
            .build())
    }

    private fun setTabNumber() {
        if (arguments?.isEmpty == false) {
            arguments?.let { 0 }?.let { switchToTab(it) }
        }
    }

    private fun showBackPressAlert() {
        confirmAction(
            message = R.string.add_participants_alert_text,
            positiveTitle = R.string.add_participants_finish_button_text,
            negativeTitle = R.string.common_yes
        ) {
            goToChatPage()
        }
    }
}