package com.app.messej.ui.home.gift.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.databinding.FragmentReceivedFlaxBottomSheetBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.businesstab.HomeBusinessFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class ReceivedFlaxBottomSheet : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentReceivedFlaxBottomSheetBinding

    private val viewModel: ReceivedFlaxBottomSheetViewModel by viewModels()
    private val commonHomeViewModel: CommonHomeViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_received_flax_bottom_sheet, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
    }


    private fun setUp() {
        binding.btnReceivedGiftClose.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.layoutDetails.setOnClickListener {
            viewModel.setVisibility()
        }
        viewModel.setArgs(commonHomeViewModel.flaxTransfer.value)

        binding.gotoStatementView.setOnClickListener {
            findNavController().navigateSafe(ReceivedFlaxBottomSheetDirections.actionGlobalHomeBusinessFragment(destination = HomeBusinessFragment.TAB_STATEMENT))
        }

    }

}