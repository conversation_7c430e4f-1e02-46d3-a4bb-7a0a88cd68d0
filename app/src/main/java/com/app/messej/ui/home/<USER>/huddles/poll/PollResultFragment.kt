package com.app.messej.ui.home.publictab.huddles.poll

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.poll.Answer
import com.app.messej.data.model.enums.PollType
import com.app.messej.databinding.FragmentPollResultBinding
import com.app.messej.databinding.LayoutEmptyPollsBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.ViewUtils
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.kennyc.view.MultiStateView

class PollResultFragment:Fragment() {

    private lateinit var binding: FragmentPollResultBinding
    private val viewModel: PollViewModel by viewModels()
    private val args:PollResultFragmentArgs by navArgs()
    private var mAdapter: HuddlePollAdapter?=null
    private var huddleParticipantAdapter: HuddlePollParticipantsAdapter?=null

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text =resources.getString(R.string.poll_result_details)
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_poll_result, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel=viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }


    private fun setParticipantEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?: return
        val emptyViewBinding = LayoutEmptyPollsBinding.bind(emptyView)
        emptyViewBinding.apply {
            pollsEmptyTitle.text = resources.getString(R.string.polls_participants_empty)
        }
    }

    private fun observe() {
        viewModel.selectedPoll.observe(viewLifecycleOwner){
            it?.let {poll->
                mAdapter?.apply {
                    Log.d("SUG", "observe: list go ${data.size} to ${poll.answers.size}")
                    if (data.size == 0 || poll.answers.isEmpty()) {
                        setNewInstance(poll.answers.toMutableList())
                    } else {
                        setDiffNewData(poll.answers.toMutableList())
                    }
                }
            }
        }

        viewModel.participantsList.observe(viewLifecycleOwner){
            huddleParticipantAdapter?.submitData(viewLifecycleOwner.lifecycle,it)
        }
    }

    private fun setup() {
        viewModel.setPollID(args.pollId)
        initAdapter()
        setParticipantEmptyView()
    }

    private fun initAdapter() {
        mAdapter = HuddlePollAdapter(mutableListOf(), object : HuddlePollAdapter.ItemClickListener {
            override fun onClick(answer: Answer, poistion: Int) {
                viewModel.setSelectedAnswer(answer)
            }

        }, -1, false,PollType.POLL_RESULT)

        huddleParticipantAdapter = HuddlePollParticipantsAdapter()
        val layoutMan = LinearLayoutManager(context)
        binding.optionsList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true
            setDiffCallback(HuddlePollAdapter.DiffCallback())
        }

        val layoutManParticipant = LinearLayoutManager(context)

        binding.participantsList.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = huddleParticipantAdapter
        }

        huddleParticipantAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)

            }
        }

    }

}