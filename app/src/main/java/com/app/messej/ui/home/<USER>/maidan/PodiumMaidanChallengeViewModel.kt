package com.app.messej.ui.home.publictab.maidan

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.PodiumJoinErrorResponse
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.PodiumKickReason
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class PodiumMaidanChallengeViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)
    private val accountRepo = AccountRepository(application)
    private val profileRepo = ProfileRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )

    private val nickNamesLiveData = nickNames.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val _dataLoading = MutableLiveData(false)
    val dataLoading: LiveData<Boolean> = _dataLoading

    val onPodiumLoadError = LiveEvent<String>()

    private val _podiumId = MutableLiveData<String>()
    val podiumId: LiveData<String> = _podiumId

    fun setParams(pId: String) {
        _podiumId.value = pId
        loadPodiumDetails(pId)
    }
    
    private val _podium = MutableLiveData<Podium>()
    val podium: LiveData<Podium> = _podium

    val userNameOrNickName: MediatorLiveData<String> by lazy {
        val med = MediatorLiveData<String>()
        fun combine() {
            val pod = _podium.value?:return
            val name = nickNames.nickNameOrName(pod.managerId,pod.managerName)
            med.postValue(name)
        }
        med.addSource(_podium) { combine() }
        med.addSource(nickNamesLiveData) { combine() }
        med
    }

    sealed class ChallengeEligibilityStatus(
        open var name: String,
        open val challengeFee: Double,
        open val prize: Double
    ) {
        data class Eligible(
            override var name: String,
            override val challengeFee: Double,
            override val prize: Double
        ) : ChallengeEligibilityStatus(name, challengeFee, prize)

        data class NotEnoughCoins(
            override var name: String,
            override val challengeFee: Double,
            override val prize: Double,
            val coins: Double,
            val flix: Double
        ) : ChallengeEligibilityStatus(name, challengeFee, prize)

        data class NotEnoughCoinsNorFlix(
            override var name: String,
            override val challengeFee: Double,
            override val prize: Double,
            val coins: Double,
            val flix: Double
        ) : ChallengeEligibilityStatus(name, challengeFee, prize)

        data object Error : ChallengeEligibilityStatus("", 0.0, 0.0)
    }

    private val _challengeEligibilityStatus = MutableLiveData<ChallengeEligibilityStatus?>(null)
    val challengeEligibilityStatus: LiveData<ChallengeEligibilityStatus?> by lazy {
        val med = MediatorLiveData<ChallengeEligibilityStatus?>()
        fun combine() {
            med.postValue(_challengeEligibilityStatus.value?.apply {
                name = userNameOrNickName.value?:name
            })
        }
        med.addSource(userNameOrNickName) { combine() }
        med.addSource(_challengeEligibilityStatus) { combine() }
        med
    }

    private fun loadPodiumDetails(id: String) {
        _dataLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            profileRepo.refreshAccountDetails(false)
            when (val result = podiumRepository.getPodiumDetails(id)) {
                is ResultOf.APIError -> {
                    onPodiumLoadError.postValue(result.error.toString())
                }
                is ResultOf.Error -> {
                    onPodiumLoadError.postValue(result.exception.toString())
                }
                is ResultOf.Success -> {
                    _podium.postValue(result.value)
                    _challengeEligibilityStatus.postValue(checkStatus(result.value))
                }
            }
            _dataLoading.postValue(false)
        }
    }

    private suspend fun checkStatus(pod: Podium): ChallengeEligibilityStatus {
        val account = accountRepo.getAccountDetailsFlow().firstOrNull()?: return ChallengeEligibilityStatus.Error
        val fee = pod.challenge?.competitorFee?:return ChallengeEligibilityStatus.Error
        val prize = pod.challenge?.prize?:0.0
        return if (account.currentCoin>=fee) {
            ChallengeEligibilityStatus.Eligible(pod.managerName,fee,prize)
        } else if (account.currentFlix>=fee) {
            ChallengeEligibilityStatus.NotEnoughCoins(pod.managerName,fee,prize,account.currentCoin,account.currentFlix)
        } else {
            ChallengeEligibilityStatus.NotEnoughCoinsNorFlix(pod.managerName,fee, prize, account.currentCoin, account.currentFlix)
        }
    }

    private val _challengeMaidanLoading = MutableLiveData(false)
    val challengeMaidanLoading: LiveData<Boolean> = _challengeMaidanLoading

    val onMaidanJoined = LiveEvent<Podium>()
    val onMaidanJoinError = LiveEvent<String>()
    val onMaidanAnotherUserJoinedError = LiveEvent<String>()
    val onLiveInAnotherPodium = LiveEvent<PodiumKickReason.LiveInAnotherPodium>()

    fun joinMaidanChallenge() {
        _challengeMaidanLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.joinMaidanChallengeAsCompetitor(podiumId.value?: return@launch)) {
                is PodiumRepository.JoinResultOf.Success -> {
                    onMaidanJoined.postValue(result.value)
                }
                is PodiumRepository.JoinResultOf.APIError -> {
                    if (result.error.result?.reason== PodiumJoinErrorResponse.PodiumJoinErrorReason.LIVE_IN_OTHER_PODIUM) {
                        result.error.result.podiumId?.let { id ->
                            result.error.result.podiumName?.let { name ->
                                onLiveInAnotherPodium.postValue(PodiumKickReason.LiveInAnotherPodium(result.error.message, id, name, result.error.result.canLeave?:false))
                                return@launch
                            }
                        }
                    }
                    if (result.error.result?.reason == PodiumJoinErrorResponse.PodiumJoinErrorReason.COMPETITOR_ALREADY_JOINED) {
                        onMaidanAnotherUserJoinedError.postValue(result.error.message)
                        return@launch
                    }
                    onMaidanJoinError.postValue(result.error.message)
                }
                is PodiumRepository.JoinResultOf.Error -> {
                    Firebase.crashlytics.recordException(Exception("Failed to Join Maidan: ${result.exception}"))
                }

            }
            _challengeMaidanLoading.postValue(false)
        }
    }

    val onLeftOtherPodium = LiveEvent<Boolean>()

    fun leaveOtherPodium(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                Log.d("PodiumLVM", "trying to leave")
                when (val result = podiumRepository.leavePodium(id)) {
                    is ResultOf.Success -> {
                        Log.d("PodiumLVM", "leave success")
                        podiumRepository.checkExistingAgoraSession("0")
                        onLeftOtherPodium.postValue(true)
                    }

                    is ResultOf.APIError -> {}

                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "leave: error: ${e.message}")
            }
        }
    }

}