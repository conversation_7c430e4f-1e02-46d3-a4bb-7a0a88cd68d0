package com.app.messej.ui.home.publictab.authorities.stateAffairs

import com.app.messej.data.model.enums.AffairDataType
import com.app.messej.data.model.enums.StateAffairsTypes
import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName

data class UserStateAffair(
    // Existing fields (make them nullable)
    val dataType: AffairDataType,
    @SerializedName("username")val userName: String? = null,
    @SerializedName("citizenship")val citizenship: UserCitizenship? = null,
    val isTribe: Boolean? = false,
    val tribeName: String? = null,
    val tribeCount: Int? = null,
    @SerializedName("is_premium")val isPremium: Boolean? = null,
    @SerializedName("skill") val skill: String? = null,
    @SerializedName("gnr") val gnr: String? = null,
    val rating: Int? = null,
    val listType: StateAffairsTypes? = null,

    // New Presidential Race fields (nullable)
    val profileImageUrl: String? = null,
    val countryFlagUrl: String? = null,
    val flixContribution: String? = null, // Or Int?
    val tribeSize: String? = null, // Or Int?


    /** Updated new fields for StateAffairs  from BE **/

    @SerializedName("huddle_name") val huddleName: String? = null,
    @SerializedName("huddle_thumbnail_url") val huddleThumbnailUrl: String? = null,
    @SerializedName("participant_count") val participantCount: Int? = null,
    @SerializedName("user_id") val userId: Int? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("profile_url") val profileUrl: String? = null,
    @SerializedName("thumbnail") val thumbnail: String? = null,
    @SerializedName("verified") val verified: Boolean? = null,
    @SerializedName("country_name") val countryName: String? = null,
    @SerializedName("country_code") val countryCode: String,
    @SerializedName("user_rating") val userRating: Double?  = 0.0,


    @SerializedName("dear_count") val dearCount: Int? = null,
    @SerializedName("id") val id: Int? = null,
    @SerializedName("rank") val rank: Int? = null,
    ){

    companion object {

        const val CROWN_THRESHOLD = 10000
    }

    val showCrownForSkill : Boolean
        get() = (skill?.toInt() ?: 0) > CROWN_THRESHOLD  /*return true if skill lvl grater than or equal to 10001*/

    val showCrownForGenerosity : Boolean
        get() = (gnr?.toInt() ?: 0) > CROWN_THRESHOLD  /*return true if genoricity lvl grater than or equal to 10001*/

}
