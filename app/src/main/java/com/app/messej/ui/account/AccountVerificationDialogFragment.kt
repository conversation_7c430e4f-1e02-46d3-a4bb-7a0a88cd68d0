package com.app.messej.ui.account

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.api.profile.VerifyAccountResponse
import com.app.messej.databinding.FragmentAccountVerificationDialogBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class AccountVerificationDialogFragment : BottomSheetDialogFragment() {

    private val viewModel: AccountVerificationDialogViewModel by viewModels()
    private lateinit var binding: FragmentAccountVerificationDialogBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_account_verification_dialog, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        this.isCancelable = false
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.actionDoItLater.setOnClickListener { dismiss() }
        binding.actionClose.setOnClickListener { dismiss() }
        binding.actionCompleteProfile.setOnClickListener {
            findNavController().navigateSafe(AccountVerificationDialogFragmentDirections.actionGlobalProfileFragment())
        }
        observe()
    }

    fun observe() {
        viewModel.verifyAccountResponse.observe(viewLifecycleOwner) {
            if (it) {
                binding.accountNotVerifiedLayout.visibility = View.GONE
                binding.verifyProcessingLayout.visibility = View.VISIBLE
            }
        }
        viewModel.accountDetails.observe(viewLifecycleOwner){
            it?.let {
                if (it.verificationStatus == VerifyAccountResponse.VerificationStatus.PROCESSING.toString()){
                    binding.accountNotVerifiedLayout.visibility = View.GONE
                    binding.verifyProcessingLayout.visibility = View.VISIBLE
                }else{
                    binding.accountNotVerifiedLayout.visibility = View.VISIBLE
                    binding.verifyProcessingLayout.visibility = View.GONE
                }
            }
        }
    }

}