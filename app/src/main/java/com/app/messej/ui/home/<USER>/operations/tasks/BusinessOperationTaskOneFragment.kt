package com.app.messej.ui.home.businesstab.operations.tasks

import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.widget.addTextChangedListener
import androidx.core.widget.doOnTextChanged
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.TaskOneEmptyMode
import com.app.messej.databinding.FragmentBussinessOperationTaskOneBinding
import com.app.messej.ui.home.businesstab.HomeBusinessFragmentDirections
import com.app.messej.ui.home.businesstab.HomeBusinessViewModel
import com.app.messej.ui.home.businesstab.operations.tasks.bottomsheet.TaskOneErrorBottomSheet
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BusinessOperationTaskOneFragment : BottomSheetDialogFragment(){

    lateinit var binding: FragmentBussinessOperationTaskOneBinding

    val viewModel: BusinessOperationTaskOneViewModel  by navGraphViewModels(R.id.navigation_task_one)
    private val commonViewModel: BusinessOperationsTaskViewModel by activityViewModels()
    private val mViewModel: HomeBusinessViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_bussiness_operation_task_one, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }



    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        commonViewModel.getBusinessOperations()
    }


    override fun onPause() {
        super.onPause()
        hideKeyboard()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_Tribe_BottomSheet)
    }

    private fun setup() {
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                delay(200)
            }
        }

        binding.txtOperationsEmail.text = setSpannableString(getString(R.string.business_operations_spannable_string_text), "")
        binding.businessVerifiedEmailTitle.text = setSpannableString(getString(R.string.business_operations_spannable_string_text), "")

        viewModel.setNextButtonEnabled()
        binding.taskOneNextButton.setOnClickListener {
             hideKeyboard()
            viewModel.requestOtp()
        }
        binding.btnClose.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.btnTaskOneEdit.setOnClickListener {
            val action = HomeBusinessFragmentDirections.actionGlobalProfileFragment()
            (activity as MainActivity).navController.navigateSafe(action)
        }

        binding.operationCheckBox.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleCheckBox(isChecked)
        }


        binding.businessVerifiedEmailCard.setOnClickListener {
            viewModel.setEmailEdit(false)
            binding.txtInputEmail.setText(viewModel.taskOneData.value?.paypalId)
        }
        binding.actionEmailEditClose.setOnClickListener {
            if (viewModel.isPaypalIsVerified.value == true) {
                viewModel.setEmailEdit(true)
                binding.txtInputEmail.text = null
            }
        }

        binding.taskOneCancelButton.setOnClickListener{
            dialog?.dismiss()
        }

        binding.businessEditLayout.txtEditEmailTaskOne.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus) {
                    viewModel.didShowEmailError(true)
                }
                addTextChangedListener { _ ->
                    viewModel.didShowEmailError(false)
                    viewModel.checkEmailValidity()
                    doOnTextChanged{
                            text, _, _, _ ->
                        if (text.isNullOrEmpty()) {
                            viewModel.setEmailHelperText(true)
                        }else{
                            viewModel.setEmailHelperText(true)
                        }
                    }
                }
            }
        }
        binding.businessEditLayout.txtEditMobileTaskOne.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus) {
                    viewModel.didShowMobileError(true)
                }
                addTextChangedListener { _ ->
                    doOnTextChanged { text, _, _, _ ->
                        if (text.isNullOrEmpty()) {
                            viewModel.clearPhoneError()
                            viewModel.setMobileHelperText(true)
                        }else{
                            viewModel.setMobileHelperText(false)
                        }
                    }
                }
            }
        }

        binding.root.rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val r = Rect()
            binding.root.rootView.getWindowVisibleDisplayFrame(r)
            val screenHeight: Int = binding.root.rootView.height
            val keypadHeight: Int = screenHeight - r.bottom
            if (keypadHeight < screenHeight * 0.15) {
                viewModel.didShowEmailError(true)
                viewModel.didShowMobileError(true)
            }
        }

        viewModel.setCountryCode(binding.businessEditLayout.businessPasswordCountryPicker.selectedCountryCode)
        binding.businessEditLayout.businessPasswordCountryPicker.apply {
            setOnCountryChangeListener {
                viewModel.setCountryCode(selectedCountryCode)
            }
            registerCarrierNumberEditText(binding.businessEditLayout.txtEditMobileTaskOne.editText)
            setPhoneNumberValidityChangeListener { valid ->
                viewModel.setPhoneNumberValid(valid)
            }
        }

        binding.imgBusinessDp.setOnClickListener {
            mViewModel.profileDpClicked.postValue(true)
        }
        binding.businessEditLayout.txtEditAboutTaskOne.editText?.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {}
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                viewModel.handleAboutTextChange(s.toString())
            }
        })

        binding.businessEditLayout.ivEditAddress.setOnClickListener {
            findNavController().navigateSafe(BusinessOperationsTaskFragmentDirections.actionBusinessTaskOneFragmentToHomeAddressBottomSheet2(true))
        }

    }

    private fun observe() {
        viewModel.taskOneData.observe(viewLifecycleOwner) {
            it?.let {
                val options: RequestOptions = RequestOptions().centerCrop().placeholder(R.drawable.bg_business_image_place_holder).error(R.drawable.bg_business_image_place_holder)
                binding.imgBusinessDp.let { image -> Glide.with(this).load(it.profilePhoto).apply(options).into(image) }
                binding.circularProgressBar.setPercent(it.profileCompletePercentage ?: 0)
                if (it.profileCompletePercentage == 100) {
                    binding.circularProgressBar.setProgressStrokeColor(ContextCompat.getColor(requireContext(), R.color.colorBusinessArcProgressCompleteColor))
                }
            }
        }
        viewModel.emailData.observe(viewLifecycleOwner) {
            if (it?.isNotEmpty() == true) {
                binding.txtInputEmail.setText(it)
                binding.txtInputEmail.setSelection(binding.txtInputEmail.text.length)
                binding.txtInputEmail.requestFocus()
            } else {
                binding.txtInputEmail.setText(it)
            }
        }

        viewModel.isCheckedLiveData.observe(viewLifecycleOwner) {
            binding.operationCheckBox.isChecked = it
        }

        viewModel.requestOTPError.observe(viewLifecycleOwner){
            Toast.makeText(requireContext(),it,Toast.LENGTH_SHORT).show()
        }

        viewModel.otpRequestComplete.observe(viewLifecycleOwner) {
            it?.let {
                if (viewModel.taskOneEmptyMode.value != TaskOneEmptyMode.EMPTY_ABOUT) {
                    findNavController().navigateSafe(BusinessOperationTaskOneFragmentDirections.actionTaskThreeDialogToFragmentBusinessOtp(it))

                } else {
                    Toast.makeText(requireContext(), getString(R.string.profile_update_success_toast), Toast.LENGTH_SHORT).show()
                }
            } ?: run {
                viewModel.setPaypalVerified(true)
                viewModel.setNextButtonState()
            }
            }

        viewModel.showEmailInvalidError.observe(viewLifecycleOwner) {
            it?.let {isShow->
                if(isShow) {
                    updateEmailFieldError()
                }
            }

        }

        viewModel.showPhoneInvalidError.observe(viewLifecycleOwner){
            it?.let {
                if(it){
                    updatePhoneFieldError()
                }else{
                    binding.businessEditLayout.businessMobileError.apply {
                        visibility=View.GONE
                        text=""
                    }
                }
            }

        }

        mViewModel.uploadDpCompleted.observe(viewLifecycleOwner) {
            it?.let {
                if (it) {
                   commonViewModel.getBusinessOperations()
                }
            }
        }
        mViewModel.dpUploading.observe(viewLifecycleOwner) {
            if (it) {
                binding.dpUloadingIndicator.visibility = View.VISIBLE
            } else {
                binding.dpUloadingIndicator.visibility = View.GONE
            }
        }

        viewModel.isRequestOtp.observe(viewLifecycleOwner){
            it?.let {
                commonViewModel.getBusinessOperations()
            }
        }

      commonViewModel.taskOneData.observe(viewLifecycleOwner){
          it?.let {
             if(it.profileCompletePercentage==100){
                 commonViewModel.setVerificationCompleted(true)
                 viewModel.setNextButtonEnabled()
             }
             else{
                  commonViewModel.setVerificationCompleted(false)
                 if(it.email.isNullOrEmpty() && it.about?.isNotEmpty() == true && it.phone?.isNotEmpty() == true){
                     viewModel.setEmptyMode(TaskOneEmptyMode.EMPTY_EMAIL)
                     viewModel.setEmailHelperText(true)
                 }else if(it.phone.isNullOrEmpty() && it.about?.isNotEmpty() == true && it.email?.isNotEmpty() == true){
                     viewModel.setEmptyMode(TaskOneEmptyMode.EMPTY_MOBILE)
                     viewModel.setMobileHelperText(true)
                 }else if(it.about.isNullOrEmpty() && it.phone?.isNotEmpty() == true && it.email?.isNotEmpty() == true){
                     viewModel.setEmptyMode(TaskOneEmptyMode.EMPTY_ABOUT)
                 }else if(it.about.isNullOrEmpty() && it.email.isNullOrEmpty() && it.phone?.isNotEmpty() == true){
                     viewModel.setEmptyMode(TaskOneEmptyMode.EMPTY_EMAIL_AND_ABOUT)
                     viewModel.setEmailHelperText(true)
                 }else if (it.phone.isNullOrEmpty() && it.about.isNullOrEmpty() && it.email?.isNotEmpty() == true) {
                     viewModel.setEmptyMode(TaskOneEmptyMode.EMPTY_MOBILE_AND_ABOUT)
                     viewModel.setMobileHelperText(true)
                 }
             }
              if(it.profilePhoto.isNullOrEmpty()){
                  viewModel.setProfilePhotoUploadStatus(false)
              }else{
                  viewModel.setProfilePhotoUploadStatus(true)
              }
          }


      }

        viewModel.alreadyVerified.observe(viewLifecycleOwner){
            if(it==true){
                commonViewModel.setVerificationCompleted(true)
            }
        }

        viewModel.showErrorDialogue.observe(viewLifecycleOwner){
           it?.let {
               if(it){
                   val dialogFragment = TaskOneErrorBottomSheet()
                   dialogFragment.show(requireActivity().supportFragmentManager, "BottomDialog")
               }
           }
        }

        viewModel.verifyApiMessage.observe(viewLifecycleOwner){
            it?.let {
                Toast.makeText(requireContext(),it,Toast.LENGTH_SHORT).show()
            }
        }

    }

    private fun setSpannableString(startText: String, endText: String): SpannableStringBuilder {
        val ss = SpannableString(startText)
        val d = ContextCompat.getDrawable(requireContext(), R.drawable.img_paypal_operartions)
        d?.setBounds(0, 0, d.intrinsicWidth, d.intrinsicHeight)
        val span = ImageSpan(d!!, ImageSpan.ALIGN_BOTTOM)
        ss.setSpan(span, startText.length - 1, startText.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        return SpannableStringBuilder().append(startText).append(" ").append(" ", span, 0).append(" $endText")
    }

    private fun updateEmailFieldError() {
        binding.businessEditLayout.txtEditEmailTaskOne.isErrorEnabled = true
        binding.businessEditLayout.txtEditEmailTaskOne.error = if (!viewModel.emailAvailabilityError.value.isNullOrEmpty()) {
            viewModel.setEmailHelperText(false)
            binding.businessEditLayout.txtEditEmailTaskOne.isErrorEnabled = true
            viewModel.emailAvailabilityError.value
        } else if (viewModel.showEmailInvalidError.value == true) {
            viewModel.setEmailHelperText(false)
            binding.businessEditLayout.txtEditEmailTaskOne.isErrorEnabled = true
            resources.getString(R.string.forgot_password_error_email)
        } else {
            binding.businessEditLayout.txtEditEmailTaskOne.isErrorEnabled = false
            null
        }
    }


    private fun updatePhoneFieldError() {
      binding.businessEditLayout.businessMobileError.visibility=View.VISIBLE
        binding.businessEditLayout.businessMobileError.text = if (!viewModel.phoneAvailabilityError.value.isNullOrEmpty()) {
            viewModel.setMobileHelperText(false)
            viewModel.phoneAvailabilityError.value
        } else if (viewModel.showPhoneInvalidError.value == true) {
            viewModel.setMobileHelperText(false)
            resources.getString(R.string.login_error_mobile_number)

        } else {
            ""
            binding.businessEditLayout.businessMobileError.visibility=View.GONE
            null
        }
    }
}
