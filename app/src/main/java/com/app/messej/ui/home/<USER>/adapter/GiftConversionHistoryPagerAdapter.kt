package com.app.messej.ui.home.gift.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.gift.CoinsConversion
import com.app.messej.databinding.ItemGiftConversionHistoryLayoutBinding

class GiftConversionHistoryPagerAdapter : PagingDataAdapter<CoinsConversion, GiftConversionHistoryPagerAdapter.GiftConversionHistoryViewHolder>(TransactionsDiff) {

    override fun onBindViewHolder(holder: GiftConversionHistoryViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = GiftConversionHistoryViewHolder(
        ItemGiftConversionHistoryLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    inner class GiftConversionHistoryViewHolder(private val binding: ItemGiftConversionHistoryLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: CoinsConversion) = with(binding) {
            binding.apply {
                giftCoinConversion = item
            }
        }
    }

    object TransactionsDiff : DiffUtil.ItemCallback<CoinsConversion>() {
        override fun areItemsTheSame(oldItem: CoinsConversion, newItem: CoinsConversion) = oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: CoinsConversion, newItem: CoinsConversion) = oldItem == newItem
    }

}