package com.app.messej.ui.home.publictab.broadcast

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.enums.BroadcastTab
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class PublicBroadcastViewModel(application: Application): AndroidViewModel(application) {

    val showCompactLoading = MutableLiveData<Boolean>(false)

    private val accountRepo = AccountRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())

    val user: CurrentUser get() = accountRepo.user

    val currentTab = MutableLiveData<BroadcastTab?>(null)

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val itemCounts = MutableLiveData<Map<BroadcastTab,Int>>(mapOf())

    private fun setItemCount(tab: BroadcastTab, count: Int) {
        val map = itemCounts.value.orEmpty().toMutableMap()
        map[tab] = count
        itemCounts.postValue(map)
    }

    val enableSearch: LiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            med.postValue(itemCounts.value.orEmpty().getOrDefault(currentTab.value,0) > 0)
        }
        med.addSource(itemCounts) { update() }
        med.addSource(currentTab) { update() }
        med.distinctUntilChanged()
    }

    private val _broadcastDearsList = profileRepo.getDearsListPager {
        setItemCount(BroadcastTab.TAB_DEARS, it)
    }.liveData.cachedIn(viewModelScope)

    val broadcastDearsList: MediatorLiveData<PagingData<UserRelative>?> by lazy {
        val med = MediatorLiveData<PagingData<UserRelative>?>(null)
        fun updateMembersList() {
            val list = _broadcastDearsList.value?.map { item ->
                _nickNames.value?.findNickName(item.id)?.let { item.name = it }
                item
            }
            broadcastDearsList.postValue(list)

        }
        med.addSource(_broadcastDearsList) { updateMembersList() }
        med.addSource(_nickNames) { updateMembersList() }
        med
    }

    private val _broadcastFansList = profileRepo.getFansListPager {
        setItemCount(BroadcastTab.TAB_FANS, it)
    }.liveData.cachedIn(viewModelScope)

    val broadcastFansList: MediatorLiveData<PagingData<UserRelative>?> by lazy {
        val med = MediatorLiveData<PagingData<UserRelative>?>(null)
        fun updateMembersList() {
            val list = _broadcastFansList.value?.map { item ->
                _nickNames.value?.findNickName(item.id)?.let { item.name = it }
                item
            }
            broadcastFansList.postValue(list)
        }
        med.addSource(_broadcastFansList) { updateMembersList() }
        med.addSource(_nickNames) { updateMembersList() }
        med
    }

    private val _broadcastLikersList = profileRepo.getLikersListPager {
        setItemCount(BroadcastTab.TAB_LIKERS, it)
    }.liveData.cachedIn(viewModelScope)

    val broadcastLikersList: MediatorLiveData<PagingData<UserRelative>?> by lazy {
        val med = MediatorLiveData<PagingData<UserRelative>?>(null)
        fun updateMembersList() {
            val list = _broadcastLikersList.value?.map { item ->
                _nickNames.value?.findNickName(item.id)?.let { item.name = it }
                item
            }
            broadcastLikersList.postValue(list)
        }
        med.addSource(_broadcastLikersList) { updateMembersList() }
        med.addSource(_nickNames) { updateMembersList() }
        med
    }

    private val _canBroadcastToDears = MutableLiveData(false)
    private val _canBroadcastToFans = MutableLiveData(false)
    private val _canBroadcastToLikers = MutableLiveData(false)

    val canBroadcast = currentTab.switchMap {
        if (!user.premium) return@switchMap MutableLiveData(false)
        return@switchMap when(it) {
            BroadcastTab.TAB_STARS -> MutableLiveData(false)
            BroadcastTab.TAB_DEARS -> _canBroadcastToDears
            BroadcastTab.TAB_FANS -> _canBroadcastToFans
            BroadcastTab.TAB_LIKERS -> _canBroadcastToLikers
            null -> null
        }
    }.distinctUntilChanged()

    fun setCanBroadcast(tab: BroadcastTab, canBroadcast: Boolean) {
        when(tab) {
            BroadcastTab.TAB_DEARS -> _canBroadcastToDears.postValue(canBroadcast)
            BroadcastTab.TAB_FANS -> _canBroadcastToFans.postValue(canBroadcast)
            BroadcastTab.TAB_LIKERS -> _canBroadcastToLikers.postValue(canBroadcast)
            else -> null
        }
    }

    fun setCurrentTab(tab: BroadcastTab, skipIfSet: Boolean = false) {
        if (skipIfSet && currentTab.value!=null) return
        currentTab.postValue(tab)
    }

    fun getProfileDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<AccountDetailsResponse> =
                profileRepo.getAccountDetails()) {
                is ResultOf.Success -> {
                    withContext(Dispatchers.Main) {
                        accountRepo.updateUser(
                            user.copy(dears = result.value.dears, fans = result.value.fans, likers = result.value.likers))
                    }
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }

}