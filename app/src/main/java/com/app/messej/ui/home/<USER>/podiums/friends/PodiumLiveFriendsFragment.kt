package com.app.messej.ui.home.publictab.podiums.friends

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumFriend
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.databinding.DialogGenderPodiumBinding
import com.app.messej.databinding.FragmentPodiumListFriendsBinding
import com.app.messej.databinding.LayoutPodiumLiveFriendsActionBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showJoinPodiumWithCoinsAlert
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showJoinPodiumWithInsufficientCoinsAlert
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showUserJoinByRatingError
import com.app.messej.ui.home.publictab.podiums.PublicPodiumViewModel
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class PodiumLiveFriendsFragment : Fragment(), PodiumLiveFriendsAdapter.UserActionListener {

    private lateinit var binding: FragmentPodiumListFriendsBinding
    private val viewModel: PublicPodiumViewModel by activityViewModels()
    private var liveFriendsAdapter: PodiumLiveFriendsAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_list_friends, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        initAdapter()
        binding.swipeRefresh.apply {
            setOnRefreshListener {
                liveFriendsAdapter?.refresh()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        liveFriendsAdapter?.refresh()
    }

    private fun observe() {
        viewModel.liveFriendsList.observe(viewLifecycleOwner) {
            Log.d("LIVE_FRIENDS", "$it")
            liveFriendsAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.onUnfollowedUser.observe(viewLifecycleOwner){
            if(it) {
                liveFriendsAdapter?.refresh()
            }
        }
        viewModel.onFollowedUser.observe(viewLifecycleOwner){
            if(it) {
                liveFriendsAdapter?.refresh()
            }
        }
    }

    private fun initAdapter() {
        liveFriendsAdapter = PodiumLiveFriendsAdapter(this)

        val layoutManParticipant = LinearLayoutManager(context)

        binding.podiumLiveFriends.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = liveFriendsAdapter
        }

        liveFriendsAdapter?.apply {
            addLoadStateListener { loadState ->
                if (loadState.refresh !is LoadState.Loading) {
                    binding.swipeRefresh.isRefreshing = false
                }
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.im_eds_podium)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.podium_empty_live_friends)
        }

    }

    override fun onUserClick(item: PodiumFriend,view:View) {
       /* when (item.role) {
            LiveFriendsRole.FRIENDS -> {
                unfollowFriendAlert(getString(R.string.unfollow_alert_friend, item.name, item.name),item.userId)
            }

            LiveFriendsRole.FOLLOWING -> {
                unfollowFriendAlert(getString(R.string.unfollow_alert_follower, item.name, item.name),item.userId)
            }

            else -> {}
        }*/
        val popup = PopupMenu(requireContext(), view)
        popup.inflate(R.menu.menu_live_friends_actions)
        popup.menu.apply {
            findItem(R.id.action_info).isVisible = true
            findItem(R.id.action_live_list).isVisible = item.hideLiveUsers == false
            findItem(R.id.action_goto_id_card).isVisible = true
            findItem(R.id.action_unfollow).apply {
                isVisible = item.isSuperStar==false
                setTitle(
                    if (item.isDearOrFan) R.string.user_action_follow
                    else R.string.user_action_unfollow
                )

            }
            findItem(R.id.action_join).isVisible = true
        }
        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem?.itemId) {
                R.id.action_info -> {
                    item.podiumId?.let { podiumId ->
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavAboutPodium(podiumId, true,true))
                    }
                }
                R.id.action_live_list -> {
                    item.podiumId?.let{podiumId->
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicLiveUsersListBottomSheet(podiumId = podiumId))
                    }
                }

                R.id.action_goto_id_card ->{
                    item.userId?.let { userId ->
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(userId,false))
                    }
                }
                R.id.action_unfollow -> {
                    if (item.isDearOrFan){
                        item.userId?.let { viewModel.followUser(it) }
                    }else{
                        unfollowFriendAlert(getString(R.string.unfollow_alert_follower, item.name, item.name),item.userId)
                    }
                }


                R.id.action_join -> joinPodium(item)
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()



    }

    override fun onItemClicked(item: PodiumFriend) {
        joinPodium(item)
    }

    private fun joinPodium(item: PodiumFriend) {
        val gender = viewModel.user.profile.gender
        val joiningFeeValue = item.joiningFee?:0
        val isJoiningFeePaid = item.joiningFeePaid ?: false
        val userCoinBalance = viewModel.user.coinBalance
//edge cases occurs toast or any thing to indicate deduction

        // User is Male and trying to join a Women-only podium
         if (item.podiumEntry == PodiumEntry.WOMEN_ONLY && gender != Gender.FEMALE) {
            showCustomisedDialog(R.string.podium_gender_alert_female, Gender.FEMALE)
            return
        }
        // User is Female and trying to join a Men-only podium
        else if (item.podiumEntry == PodiumEntry.MEN_ONLY && gender != Gender.MALE) {
            showCustomisedDialog(R.string.podium_gender_alert_male, Gender.MALE)
            return
        }
        else if (item.isManagerOrAdmin) {
            proceedToNavigate(item)
        }
         else if (item.requiredUserRating == 100 && viewModel.user.userRatingPercent.toInt() != 100) {
             showUserJoinByRatingError(
                 message = getString(R.string.podium_join_with_rating_with_hundred),
                 userRating = viewModel.user.userRatingPercent,
                 isPremium = viewModel.user.premium
             )
         } else if ((item.requiredUserRating ?: 0) > viewModel.user.userRatingPercent.toInt()) {
             showUserJoinByRatingError(
                 message = getString(R.string.podium_join_with_rating_ninety_or_above),
                 userRating = viewModel.user.userRatingPercent,
                 isPremium = viewModel.user.premium
             )
         }
        else if (viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true) {
             proceedToNavigate(item)
        }
        else if (joiningFeeValue > 0 && !isJoiningFeePaid) {
            if (joiningFeeValue > userCoinBalance) {
                showJoinPodiumWithInsufficientCoinsAlert(coinBalance = userCoinBalance, joiningFee = joiningFeeValue)
            } else {
                showJoinPodiumWithCoinsAlert(joiningFee = item.joiningFee, onProceed = {
                    proceedToNavigate(item)
                })
            }
        }

//        else if (item.requiredUserRating == 100 && viewModel.user.userRatingPercent.toInt() != 100) {
//            showUserJoinByRatingError(
//                message = getString(R.string.podium_join_with_rating_with_hundred),
//                userRating = viewModel.user.userRatingPercent,
//                isPremium = viewModel.user.premium
//            )
//            return
//        } else if ((item.requiredUserRating ?: 0) > viewModel.user.userRatingPercent.toInt()) {
//            showUserJoinByRatingError(
//                message = getString(R.string.podium_join_with_rating_ninety_or_above),
//                userRating = viewModel.user.userRatingPercent,
//                isPremium = viewModel.user.premium
//            )
//            return
//        }
//        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(item.podiumId, kind = item.podiumKind?.ordinal ?: -1, enableScrollForTab = PodiumTab.LIVE_FRIENDS.ordinal))
        else  {
            proceedToNavigate(item)
        }
    }

    private fun proceedToNavigate(item: PodiumFriend) {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(item.podiumId, kind = item.podiumKind?.ordinal ?: -1, enableScrollForTab = PodiumTab.LIVE_FRIENDS.ordinal))
    }

    private fun unfollowFriendAlert(header: String,userId:Int?=null) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumLiveFriendsActionBinding>(layoutInflater, R.layout.layout_podium_live_friends_action, null, false)
            view.textHeader = header
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
            view.actionFriendUnfollow.setOnClickListener {
                userId?.let { it1 -> viewModel.unFollowUser(it1) }
                dismiss()
            }
        }
    }

    private fun showCustomisedDialog(content: Int,podiumGender: Gender) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<DialogGenderPodiumBinding>(layoutInflater, R.layout.dialog_gender_podium, null, false)
            view.content = getString(content)
            view.gender = podiumGender
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.actionCancel.setOnClickListener {
                dismiss()
            }
        }
    }
}