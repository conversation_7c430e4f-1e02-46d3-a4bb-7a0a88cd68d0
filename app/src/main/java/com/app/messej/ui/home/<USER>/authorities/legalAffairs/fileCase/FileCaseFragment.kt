package com.app.messej.ui.home.publictab.authorities.legalAffairs.fileCase

import android.content.DialogInterface
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.AutoCompleteTextView
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.ReportCategoryResponse.ReportCategory
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.FragmentLegalAffairsFileCaseBinding
import com.app.messej.databinding.ItemReportProofBinding
import com.app.messej.ui.home.businesstab.adapter.DealsBeneficiaryAdapter
import com.app.messej.ui.legal.report.ReportViewModel.ProofMediaUIModel
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.setAsMandatory
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class FileCaseFragment : Fragment() {

    private lateinit var binding: FragmentLegalAffairsFileCaseBinding
    private val viewModel: FileCaseViewModel by viewModels()
    private var mProofAdapter: BaseQuickAdapter<ProofMediaUIModel, BaseDataBindingHolder<ItemReportProofBinding>>? = null

    private val selectImageOrVideoResult =
        registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
            uri?.let {
                viewModel.addProofFile(uri = uri)
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_legal_affairs_file_case, container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.legal_affairs_file_a_case)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        initAdapter()
    }

    private fun setup() {
        binding.apply {
            textAgainst.setAsMandatory()
            textViewSelectReason.setAsMandatory(isSpaceBetweenText = false)
            textUploadProof.setAsMandatory()
            textYourIndicement.setAsMandatory()
        }
        setupClickListeners()
        observe()
    }

    private fun setupClickListeners() {
        binding.apply {
            uploadFile.setOnClickListener { uploadFromGallery() }
            btnSubmit.setOnClickListener {
                if ((viewModel?.accountDetails?.value?.currentCoin ?: 0.0) < 100.0 ) {
                    showInsufficientBalanceDialog()
                } else {
                    showFileCaseAlertDialog()
                }
            }
            btnCancel.setOnClickListener {
                findNavController().navigateUp()
            }
        }
    }

    private fun observe() {
        val textInputEditText = binding.editTextLayoutUserName.editText as AutoCompleteTextView
        textInputEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val searchText = s.toString()
                if (searchText.isNotEmpty()) {
                    viewModel.getUsersList(keyword = searchText)
                    viewModel.userName.value = searchText
                } else {
                    viewModel.setSelectedUser(user = null)
                }
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })

        viewModel.usersList.observe(viewLifecycleOwner) {
            if (it != null) {
                val adapter = DealsBeneficiaryAdapter(requireContext(), it)
                (binding.editTextLayoutUserName.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    adapter.notifyDataSetChanged()
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.setSelectedUser(user = item)
                    }
                }
            }
        }

        viewModel.reportCategories.observe(viewLifecycleOwner) { categoryList ->
            setupReportCategoriesAdapter(categories = categoryList ?: emptyList())
        }

        viewModel.isCaseFileSuccessfully.observe(viewLifecycleOwner) {
            showToast(message = getString(R.string.file_a_case_case_success_message))
            findNavController().navigateUp()
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) {
            showToast(message = it)
        }

        viewModel.fileUploadErrorMessage.observe(viewLifecycleOwner) { stringRes ->
            showToast(message = getString(stringRes))
        }

        viewModel.proofMedia.observe(viewLifecycleOwner){
            mProofAdapter?.apply {
                if (data.size == 0 || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        viewModel.accountDetails.observe(viewLifecycleOwner) {
            Log.d("FileCase", "Coin Balance -> ${it?.currentCoin}")
        }
    }

    private fun setupReportCategoriesAdapter(categories: List<ReportCategory>) {
        val adapter = object: ArrayAdapter<ReportCategory>(requireContext(), R.layout.item_general_dropdown, categories) {
            private val mCats = categories
            var selectedPos: Int? = null
            override fun getItem(position: Int) = mCats[position]
            override fun getCount() = mCats.size
            override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                var cView = convertView
                if (cView == null) {
                    cView = layoutInflater.inflate(R.layout.item_general_dropdown, parent, false)
                }
                cView!!
                getItem(position).let { cat ->
                    (cView.findViewById<AppCompatTextView>(R.id.text)).text = cat.categoryText
                    (cView.findViewById<AppCompatImageView>(R.id.checked_icon)).visibility = if (position == selectedPos) View.VISIBLE else View.GONE
                }
                return cView
            }
        }

        (binding.reasonDropdown.editText as? AutoCompleteTextView)?.apply {
            setAdapter(adapter)
            setOnItemClickListener { _, _, position, _ ->
                adapter.selectedPos = position
                val item = adapter.getItem(position)
                setText(item.categoryText)
                viewModel.setCategory(category = item)
            }
        }
    }

    private fun uploadFromGallery() {
        selectImageOrVideoResult.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageAndVideo))
    }

    private fun showInsufficientBalanceDialog() {
        showAlertDialog(
            message = R.string.file_a_case_insufficient_coin_message,
            negativeButtonText = R.string.common_close,
            positiveButtonText = R.string.title_buy_coins,
            positiveButtonClick = {
                findNavController().navigateSafe(
                    FileCaseFragmentDirections.actionGlobalBuyflaxFragment(
                        hideActionBar = false, isBuyCoin = true
                    )
                )
            }
        )
    }

    private fun showFileCaseAlertDialog() {
        showAlertDialog(
            message = R.string.file_a_case_coin_deduction_message,
            negativeButtonText = R.string.common_cancel,
            positiveButtonText = R.string.common_confirm,
            positiveButtonClick = {
                viewModel.submitCase()
            }
        )
    }

    private fun showAlertDialog(message: Int, negativeButtonText: Int, positiveButtonText: Int, positiveButtonClick: () -> Unit) {
        val alertDialog = MaterialAlertDialogBuilder(requireContext())
            .setMessage(getString(message))
            .setPositiveButton(getString(positiveButtonText)) { _, _ ->
                positiveButtonClick()
            }
            .setNegativeButton(getString(negativeButtonText)) { dialog, _ -> dialog.dismiss() }
            .create()
        alertDialog.show()

        val negativeButton = alertDialog.getButton(DialogInterface.BUTTON_NEGATIVE)
        negativeButton.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorError))
    }

    private fun initAdapter() {
        val differ = object : DiffUtil.ItemCallback<ProofMediaUIModel>() {
            override fun areItemsTheSame(oldItem: ProofMediaUIModel, newItem: ProofMediaUIModel): Boolean {
                return oldItem.proof.uuid == newItem.proof.uuid
            }

            override fun areContentsTheSame(oldItem: ProofMediaUIModel, newItem: ProofMediaUIModel): Boolean {
                return oldItem == newItem
            }
        }

        mProofAdapter = object: BaseQuickAdapter<ProofMediaUIModel, BaseDataBindingHolder<ItemReportProofBinding>>(R.layout.item_report_proof, mutableListOf()) {
            override fun convert(holder: BaseDataBindingHolder<ItemReportProofBinding>, item: ProofMediaUIModel) {
                holder.dataBinding?.apply {
                    model = item
                    fileType.setImageResource(
                        when (item.proof.mediaType) {
                            MediaType.IMAGE -> R.drawable.ic_attach_gallery
                            MediaType.VIDEO -> R.drawable.ic_attach_video
                            else -> 0
                        }
                    )
                    deleteButton.setOnClickListener {
                        viewModel.deleteFile(item.proof)
                    }
                }
            }
        }
        binding.proofList.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(false)
            adapter = mProofAdapter
        }
        mProofAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(differ)
        }
    }
}