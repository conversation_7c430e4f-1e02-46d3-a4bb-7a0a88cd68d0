package com.app.messej.ui.home.publictab.podiums.yalla

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavLivePodiumDirections
import com.app.messej.R
import com.app.messej.databinding.FragmentYallaGuysCreateBinding
import com.app.messej.databinding.ItemYallaCreateChallengeTypeBinding
import com.app.messej.ui.home.publictab.podiums.yalla.YallaGuysCreateViewModel.SelectableChallengeType
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.SlideInBottomAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class YallaGuysCreateFragment : Fragment() {

    private lateinit var binding: FragmentYallaGuysCreateBinding
    val args: YallaGuysCreateFragmentArgs by navArgs()
    private val viewModel: YallaGuysCreateViewModel by viewModels()

    private var mAdapter: BaseQuickAdapter<SelectableChallengeType, BaseDataBindingHolder<ItemYallaCreateChallengeTypeBinding>>? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_yalla_guys_create, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun setup() {
        viewModel.setParams(args.podiumId)
        initAdapter()

        binding.btnCreateChallenge.setOnClickListener {
            Log.w("SUG", "btnCreateChallenge")
            if (!canCreateYallaGuysAsParticipant()) return@setOnClickListener

            val canJoinPodiumWithEmpowerment = viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true
            val challengeFee = viewModel.challengePrizeString.value.orEmpty()
            val totalFee = args.joiningFee + (challengeFee.toIntOrNull() ?: 0)

            val message = if (args.joiningFee == 0 || viewModel.isParticipant.value != true || canJoinPodiumWithEmpowerment) getString(R.string.podium_yalla_guys_create_confirm, challengeFee)
            else getString(R.string.podium_yalla_guys_create_confirm_with_joining_fee, "${args.joiningFee}", challengeFee, "$totalFee")

            showFlashatDialog {
                setMessage(message)
                setConfirmButton(R.string.common_confirm) {
                    viewModel.createChallenge()
                    true
                }
            }
        }
    }

    private fun canCreateYallaGuysAsParticipant() : Boolean {
        val isParticipant = viewModel.isParticipant.value ?: false
        val isUserRatingLess = args.podiumRating > viewModel.user.userRatingPercent.toInt()
        val challengeFee = viewModel.challengePrizeString.value.orEmpty()
        val totalFee = args.joiningFee + (challengeFee.toIntOrNull() ?: 0)
        val isAdmin = args.isAdmin
        Log.d("ISADMIN","${isAdmin}")
        val canJoinPodiumWithEmpowerment = viewModel.user.userEmpowerment?.allowJoinSpeakPodiumForFree == true

        val isUserCoinBalanceLess = totalFee.toDouble() > viewModel.user.coinBalance

        if (isParticipant) {
            if (isUserRatingLess && isUserCoinBalanceLess && !isAdmin && !canJoinPodiumWithEmpowerment) {
                showToast(message = getString(R.string.podium_unable_to_join_due_to_low_rating_and_balance))
                return false
            }
            else if (isUserRatingLess && !isAdmin) {
                showToast(message = getString(R.string.podium_unable_to_join_due_to_low_rating))
                return false
            } else if (canJoinPodiumWithEmpowerment) {
                return true
            }
            else if (isUserCoinBalanceLess && !isAdmin) {
                showToast(message = getString(R.string.podium_unable_to_join_due_to_low_balance))
                return false
            }
        }
        return true
    }

    private fun observer() {
        viewModel.challengeTypes.observe(viewLifecycleOwner) {
            mAdapter?.apply {
                Log.d("SUG", "observe: list go $it")
                if (data.isEmpty() || it?.size==0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        viewModel.challengeCreated.observe(viewLifecycleOwner) {
            showToast(getString(R.string.podium_yalla_guys_create_success,it))
            if (viewModel.isParticipant.value == true && args.fromLive==false) {
                val action = NavLivePodiumDirections.actionGlobalNavLivePodium(podiumId = args.podiumId)
                val options = NavOptions.Builder().setPopUpTo(R.id.YallaGuysCreateFragment, inclusive = true).build()
                findNavController().navigateSafe(action, options)
            } else {
                findNavController().popBackStack()
            }
        }

        viewModel.onInsufficientBalance.observe(viewLifecycleOwner) {
            showInsufficientBalanceAlert(R.string.podium_yalla_guys_no_balance)
        }

        viewModel.onChallengeCreateError.observe(viewLifecycleOwner) {
            showToast(it)
        }
    }

    private fun initAdapter() {
        mAdapter = object: BaseQuickAdapter<SelectableChallengeType, BaseDataBindingHolder<ItemYallaCreateChallengeTypeBinding>>(
            R.layout.item_yalla_create_challenge_type,
            mutableListOf()
        ) {
            override fun convert(holder: BaseDataBindingHolder<ItemYallaCreateChallengeTypeBinding>, item: SelectableChallengeType ) {
                holder.dataBinding?.apply {
                    challenge = item.type
                    selected = item.isSelected
                    icon = ContextCompat.getDrawable(requireContext(),item.type.iconRes)
                }
            }
        }
        binding.typeList.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(false)
            adapter = mAdapter
        }
        mAdapter!!.apply {
            animationEnable = true
            adapterAnimation = SlideInBottomAnimation()
            isAnimationFirstOnly = true

            setDiffCallback(object : DiffUtil.ItemCallback<SelectableChallengeType>() {
                override fun areItemsTheSame(oldItem: SelectableChallengeType, newItem: SelectableChallengeType): Boolean {
                    return oldItem.type == newItem.type
                }

                override fun areContentsTheSame(oldItem: SelectableChallengeType, newItem: SelectableChallengeType): Boolean {
                    Log.d("SUG", "areContentsTheSame: $oldItem | $newItem")
                    return oldItem.isSelected == newItem.isSelected
                }
            })

            setOnItemClickListener { _, _, position ->
                val type = mAdapter?.data?.get(position)?:return@setOnItemClickListener
                viewModel.selectChallengeType(type.type)
            }
        }
    }
}