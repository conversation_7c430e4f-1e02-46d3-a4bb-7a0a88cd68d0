package com.app.messej.ui.home.gift.composeScreen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.ui.composeComponents.ComposeShimmerLayout
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.gift.GiftCommonViewModel
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros

interface onGiftFileItemListener{
    fun onDonateAction()
    fun onBuyCoinAction()
    fun onBuyFlixAction()
    fun onConvertAction()
}
@Composable
fun GiftFileComposeScreen(viewModel: GiftCommonViewModel,listener: onGiftFileItemListener) {

    val giftFileResponse by viewModel.giftListData.observeAsState()
    val isLoading by viewModel.purchaseHistoryLoading.observeAsState()

    val state = rememberScrollState()
    Column(
        modifier = Modifier
            .verticalScroll(state = state)
            .fillMaxWidth()
            .background(if (viewModel.user.premium) colorResource(R.color.colorPrimary) else colorResource(R.color.textColorAlwaysLightSecondaryLight)),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        val totalFlix = giftFileResponse?.flax.formatDecimalWithRemoveTrailingZeros()
        val totalCoins = giftFileResponse?.receivedCoins.formatDecimalWithRemoveTrailingZeros()

        /** Balance Screen **/
        BalanceScreen(totalFlix, totalCoins)

        CustomVerticalSpacer(R.dimen.chat_huddle_post_sheet_offset)
        Box(
            modifier = Modifier.background(colorResource(R.color.textInputBackground), shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
        ) {
            if (isLoading == true) {
                ComposeShimmerLayout { brush ->
                    ShimmerGiftFileLayout(brush = brush)
                }
            } else {
                Column(
                    modifier = Modifier
                        .offset(y = (-70).dp)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                ) {
                    GenerosityScreen(
                        generosityStatusSection = giftFileResponse?.generosityStatusSection,
                        isPremium = viewModel.user.premium,
                        isVisiter = viewModel.user.citizenship.isVisitor,
                        actionDonate = { listener.onDonateAction() })
                    CustomVerticalSpacer(R.dimen.element_spacing)
                    Text(
                        modifier = Modifier
                            .padding(all = dimensionResource(id = R.dimen.element_spacing))
                            .fillMaxWidth(),
                        text = stringResource(R.string.title_quick_actions),
                        style = FlashatComposeTypography.defaultType.body1,
                        color = colorResource(id = R.color.textColorPrimary)
                    )
                    QuickActionScreen(totalFlix, totalCoins, actionBuyCoin = {
                        listener.onBuyCoinAction()
                    }, actionBuyFlix = {
                        listener.onBuyFlixAction()
                    }, actionConvert = {
                        listener.onConvertAction()
                    }, isPremium = viewModel.user.premium)
                    CustomVerticalSpacer(R.dimen.element_spacing)
                    Text(
                        modifier = Modifier
                            .padding(all = dimensionResource(id = R.dimen.element_spacing))
                            .fillMaxWidth(),
                        text = stringResource(R.string.title_this_month),
                        style = FlashatComposeTypography.defaultType.body1,
                        color = colorResource(id = R.color.textColorPrimary)
                    )
                    ThisMonthGiftCountScreen(monthlySection = giftFileResponse?.monthlySection, isPremium = viewModel.user.premium)
                    CustomVerticalSpacer(R.dimen.element_spacing)
                    Text(
                        modifier = Modifier
                            .padding(all = dimensionResource(id = R.dimen.element_spacing))
                            .fillMaxWidth(),
                        text = stringResource(R.string.title_grant_totals),
                        style = FlashatComposeTypography.defaultType.body1,
                        color = colorResource(id = R.color.textColorPrimary)
                    )
                    TotalGiftCountScreen(totalSection = giftFileResponse?.totalSection, isPremium = viewModel.user.premium)

                }
            }
        }

    }

}


