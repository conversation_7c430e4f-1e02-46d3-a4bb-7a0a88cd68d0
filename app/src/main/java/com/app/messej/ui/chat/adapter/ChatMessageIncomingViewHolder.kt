package com.app.messej.ui.chat.adapter

import android.widget.TextView
import com.app.messej.data.model.entity.BroadcastChatMessageWithMedia
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.entity.PrivateChatMessageWithMedia
import com.app.messej.databinding.ItemChatMessageIncomingBinding
import com.app.messej.ui.chat.ChatMessageUIModel

class ChatMessageIncomingViewHolder(val binding: ItemChatMessageIncomingBinding, userId: Int, allowReplySwipe: Boolean, private var mListener: ChatAdapter.ChatClickListener)
    : ChatMessageViewHolder(binding.root,userId, allowReplySwipe, mListener) {

    override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
        super.bind(item)
        val cm = (item as ChatMessageUIModel.ChatMessageModel)
        message = cm.message
        selected = item.selected
        val messageSpan = formatAndHighlightText(chatMessage.context, cm)
        chatMessage.setText(messageSpan?:"", TextView.BufferType.SPANNABLE)
        chatMessage.setExpanded(false)
        chatMessage.apply {
            setOnStateChangeListener { expanded ->
                cm.expanded = expanded
            }
            setExpanded(cm.expanded)

        }
        when (val msg = cm.chat) {
            is HuddleChatMessageWithMedia -> {
                showName = cm.showName
                senderName = msg.senderNickNameOrName
                showLikes = true
                totalLikes = if (msg.message.totalLikes>=0) msg.message.totalLikes else 0
                starred = false
            }
            is PrivateChatMessageWithMedia -> {
                showName = false
                senderName = ""
                showLikes = false
                totalLikes = 0
                starred = false
            }
            is BroadcastChatMessageWithMedia -> {
                showName = false
                senderName = ""
                showLikes = true
                totalLikes = if (msg.message.totalLikes>=0) msg.message.totalLikes else 0
                starred = msg.message.starred
            }
        }
        val color = setChatBubbleColor(binding.chatBubble, cm)
        loadMedia(mediaHolder, cm, color)
        loadReply(replyHolder, cm, color)

        btnIncomingForward.setOnClickListener {
            mListener.onForwardClick(item.message,layoutPosition)
        }

        chatHolder.setOnClickListener {
            mListener.onItemClick(item.message, layoutPosition)
        }
        chatHolder.setOnLongClickListener {
            mListener.onItemLongClick(item.message, layoutPosition)
            true
        }
        chatMessage.setOnClickListener {
            if (mListener.onItemClick(item.message, layoutPosition)) return@setOnClickListener
            if (chatMessage.isExpanded()) return@setOnClickListener
            chatMessage.setExpanded(true)
        }

        chatMessage.setOnLongClickListener {
            mListener.onItemLongClick(item.message, layoutPosition)
            true
        }
        likesLayout.setOnClickListener {
            mListener.onItemLike(item.message, layoutPosition)
        }
        setSwipeListener(swipeLayout,item.message,layoutPosition)
    }

    override fun getHighlightView() = binding.highlightView
}