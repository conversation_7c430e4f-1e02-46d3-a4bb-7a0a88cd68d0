package com.app.messej.ui.home.businesstab.operations.tasks.bottomsheet

import android.app.Application
import android.location.Address
import android.location.Geocoder
import android.os.Build
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.GeoLocationResponse
import com.app.messej.data.model.api.auth.HomeAddress
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.auth.common.BaseLocationSetViewModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class HomeAddressViewModel(application: Application) : BaseLocationSetViewModel(application) {
    private val businessRepo = BusinessRepository(application)
    override fun confirmLocation() {
        TODO("Not yet implemented")
    }
    companion object {

        private const val NAME_MIN_LENGTH = 3
        private const val NAME_MAX_LENGTH = 50
        private const val DEFAULT_LANGUAGE = "english"
    }

    private var previousAddress: HomeAddress? = null

    val name = MutableLiveData<String>()
    val addressOne = MutableLiveData<String>()
    val addressTwo = MutableLiveData<String>()
    val addressThree = MutableLiveData<String>()
    var zipCode = MutableLiveData<String>()
    val city = MutableLiveData<String?>()
    val countryName = MutableLiveData<String?>(null)
    val enteredMobile = MutableLiveData<String>()
    val countryCode = MutableLiveData<String?>()

    val addressCompleted = LiveEvent<Boolean>()

    private val _homeAddressLoading = MutableLiveData<Boolean>(false)
    val homeAddressLoading: LiveData<Boolean> = _homeAddressLoading

    private val _getHomeAddressShimmerLoading = MutableLiveData<Boolean>(false)
    val getHomeAddressShimmerLoading: LiveData<Boolean> = _getHomeAddressShimmerLoading

    private val _isEditMode = MutableLiveData<Boolean>(false)
    val isEditMode: LiveData<Boolean> = _isEditMode

    private val _nameValid = MutableLiveData<Boolean?>(null)
    val nameValid: LiveData<Boolean?> = _nameValid

    fun clearNameError() {
        _nameValid.postValue(true)
    }

    private val _addressValid = MutableLiveData<Boolean?>(null)
    val addressValid: LiveData<Boolean?> = _addressValid

    fun clearAddressOneError() {
        _addressValid.postValue(true)
    }

    private val _zipCodeValid = MutableLiveData<Boolean?>(null)
    val zipCodeValid: LiveData<Boolean?> = _zipCodeValid

    fun clearZipCodeError() {
        _zipCodeValid.postValue(true)
    }

    private val _phoneNumberValid = MutableLiveData<Boolean?>(null)
    val phoneNumberValid: LiveData<Boolean?> = _phoneNumberValid

    fun clearPhoneNumberValidError() {
        _phoneNumberValid.postValue(true)
    }

    private val _cityValid = MutableLiveData<Boolean?>(null)
    val cityValid: LiveData<Boolean?> = _cityValid

    fun clearCityValidError() {
        _cityValid.postValue(true)
    }

    fun setMode(edit: Boolean, code: String) {
        _isEditMode.value = edit
        countryCode.value = code

        if (!edit) {
            fetchCurrentLocation()
        } else {
            getHomeAddress()
        }
    }

    private val _homeAddressStageValid = MutableLiveData<Boolean>()
    val homeAddressStageValid: LiveData<Boolean> = _homeAddressStageValid


    private fun validateHomeAddressStage(): Boolean {
        val nameValid = name.value.orEmpty().isNotBlank()
        val addressValid = addressOne.value.orEmpty().isNotBlank()
        val zipCodeValid = zipCode.value.orEmpty().isNotBlank()
        val phoneNumber = enteredMobile.value.orEmpty().isNotBlank()
        val city = city.value.orEmpty().isNotBlank()
        _nameValid.postValue(nameValid)
        _addressValid.postValue(addressValid)
        _zipCodeValid.postValue(zipCodeValid)
        _phoneNumberValid.postValue(phoneNumber)
        _cityValid.postValue(city)
        val stageValid = nameValid && addressValid && zipCodeValid && phoneNumber && city
        _homeAddressStageValid.postValue(stageValid)
        return stageValid
    }

    fun setCountryCode(selectedCountryCode: String) {
        countryCode.postValue(selectedCountryCode)
    }

    fun setCountryName(selectedCountry: String) {
        countryName.postValue(selectedCountry)
    }

    fun isDataAvailable(): Boolean {
        return !name.value.isNullOrEmpty() || !addressOne.value.isNullOrEmpty() || !zipCode.value.isNullOrEmpty() || enteredMobile.value.isNullOrEmpty() || city.value.isNullOrEmpty()
    }


    private fun fetchCurrentLocation() {
        Log.d("BLSF", "getFallBackLocation")
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<GeoLocationResponse> =
                profileRepo.getGeoLocation()) {
                is ResultOf.Success -> {
                    setAddressFromGeoCoder(result.value.location.lat, result.value.location.lng) { loc ->
                        city.postValue(loc.name.toString())
                    }
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
     }

    fun saveHomeAddress() {
        viewModelScope.launch(Dispatchers.IO) {
            if (!validateHomeAddressStage()) return@launch
            _homeAddressLoading.postValue(true)
            when (val result: ResultOf<String> = profileRepo.addHomeAddress(
                HomeAddress(
                    recipientName = name.value,
                    addressLine1 = addressOne.value,
                    addressLine2 = addressTwo.value,
                    addressLine3 = addressThree.value,
                    zipCode = zipCode.value,
                    city = city.value,
                    country = countryName.value,
                    phone = enteredMobile.value,
                    countryCode=countryCode.value,
                ),previousAddress
            )) {
                is ResultOf.Success -> {
                    businessRepo.getBusinessTaskOne()
                    _homeAddressLoading.postValue(false)
                    addressCompleted.postValue(true)
                }

                is ResultOf.APIError -> {
                    _homeAddressLoading.postValue(false)
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    private fun getHomeAddress() {
        _getHomeAddressShimmerLoading.postValue(false)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<HomeAddress> = profileRepo.getHomeAddress()) {
                is ResultOf.Success -> {
                    _getHomeAddressShimmerLoading.postValue(true)
                    val addr = result.value
                    previousAddress = addr
                    name.postValue(addr.recipientName ?: "")
                    addressOne.postValue(addr.addressLine1 ?: "")
                    addressTwo.postValue(addr.addressLine2 ?: "")
                    addressThree.postValue(addr.addressLine3 ?: "")
                    zipCode.postValue(addr.zipCode ?: "")
                    city.postValue(addr.city ?: "")
                    countryName.postValue(addr.country ?: "")
                    enteredMobile.postValue(addr.phone ?: "")
                    countryCode.postValue(addr.countryCode ?: "")
                }

                is ResultOf.APIError -> {
                    Log.d("ASDF2", result.error.message)
                    _getHomeAddressShimmerLoading.postValue(false)
                }

                is ResultOf.Error -> {
                    Log.d("ASDF2", result.exception.message.toString())
                }
            }

        }
    }

    @Suppress("DEPRECATION")
    private fun Geocoder.getAddress(
        latitude: Double,
        longitude: Double,
        address: (Address?) -> Unit
    ) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                getFromLocation(latitude, longitude, 1) { address(it.firstOrNull()) }
            } else {
                address(getFromLocation(latitude, longitude, 1)?.firstOrNull())
            }
        } catch(e: Exception) {
            address(null)
        }
    }

}