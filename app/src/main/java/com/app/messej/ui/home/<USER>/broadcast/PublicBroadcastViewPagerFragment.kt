package com.app.messej.ui.home.publictab.broadcast

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.BroadcastTab

class PublicBroadcastViewPagerFragment : PublicBroadcastBaseFragment() {

    companion object {
        const val ARG_TAB = "tab"

        fun getTabBundle(tab: BroadcastTab) = Bundle().apply {
            putInt(ARG_TAB,tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): BroadcastTab {
            val tabInt = bundle?.getInt(ARG_TAB)?:0
            return BroadcastTab.values()[tabInt]
        }
    }

    override val tabList: List<BroadcastTab> = BroadcastTab.values().toList()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_broadcast, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val tab = parseTabBundle(arguments)
        viewModel.setCurrentTab(tab,true)
    }

    override fun toSearch() {
//        val action = if (viewModel.currentTab.value==BroadcastTab.TAB_STARS)HomePublicFragmentDirections.actionHomePublicFragmentToPublicStarsSearchFragment()
//        else HomePublicFragmentDirections.actionHomePublicFragmentToHomePublicBroadcastSearchFragment(viewModel.currentTab.value!!)
//        findNavController().navigateSafe(action)
    }

    override fun toBroadcast(broadcastMode: BroadcastMode, char: Char?) {
//        findNavController().navigateSafe(HomePublicFragmentDirections.actionGlobalNavigationChatBroadcast(broadcastMode,null))
    }



}