package com.app.messej.ui.home.publictab.postat.create

import android.app.Application
import android.net.Uri
import android.util.Log
import androidx.core.net.toUri
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractUriMedia
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.PostatMentionedUser
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.api.postat.MusicFile
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.entity.Postat.PostatType
import com.app.messej.data.model.entity.PostatWithMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.PostatMediaTab
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PostatRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.repository.worker.PostatUploadWorker
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import com.app.messej.ui.home.publictab.postat.mypostat.MyPostatViewModel
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

@OptIn(FlowPreview::class)
class CreatePostatViewModel(application: Application) : AndroidViewModel(application) {

    private val postatRepo = PostatRepository(application)

    private val profileRepo = ProfileRepository(application)

    private val accountRepo: AccountRepository = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    var searchKeyword = MutableLiveData("")
    private val searchTerm = MutableLiveData("")

    private val _postatId = MutableLiveData<String?>(null)

    private val _multipleSelectionMode = MutableLiveData(false)
    val multipleSelectionMode: LiveData<Boolean> = _multipleSelectionMode

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                searchTerm.postValue(it.orEmpty())
            }
        }

        getAudioCookies()
    }

    companion object {
        const val VIDEO_DURATION_LIMIT = 60
    }

    private var wasOnMediaPickerPage = true

    fun setOnMediaPickerPage(isOnMediaPickerPage: Boolean) {
        if (isOnMediaPickerPage && !wasOnMediaPickerPage) {
            //remove captured media
            _selectedMedia.postValue(_selectedMedia.value.orEmpty().filter { !it.isCaptured }.toMutableList())
        }
        wasOnMediaPickerPage = isOnMediaPickerPage
    }

    /**
     * Permission logic
     */
    private val _permissionGranted = MutableLiveData<Boolean>()

    fun setPermissionGranted(permissionGranted: Boolean) {
        _permissionGranted.postValue(permissionGranted)
    }

    /**
     * Draft and Recent tab logic
     */
    private val _currentTab = MutableLiveData<PostatMediaTab?>(null)
    val currentTab: LiveData<PostatMediaTab?> = _currentTab.distinctUntilChanged()

    fun setCurrentTab(tab: PostatMediaTab, skipIfSet: Boolean = false) {
        Log.w("PHVM", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
    }

    /**
     * Media list logic
     */
    private val _mediaList = _permissionGranted.switchMap {
        if (!it) return@switchMap null
        postatRepo.getPostatMediaListPager().liveData.cachedIn(viewModelScope)
    }

    val mediaList : MediatorLiveData<PagingData<PostatDeviceMedia>> by lazy {
        val med = MediatorLiveData<PagingData<PostatDeviceMedia>>()
        fun update() {
            val list: PagingData<PostatDeviceMedia>? = _mediaList.value?.map {
                it.mediaSelected = _selectedMedia.value.orEmpty().find { m -> m.uri == it.uri } != null
                it
            }
            med.postValue(list)
        }
        med.addSource(_mediaList) { update() }
        med.addSource(_selectedMedia) { update() }
        med.addSource(_multipleSelectionMode) { update() }
        med
    }

    private val _selectedMedia = MutableLiveData<MutableList<PostatDeviceMedia>>()

    private fun List<PostatDeviceMedia>.updateOrder() {
        forEachIndexed { index, media ->
            media.selectionOrder = if(_multipleSelectionMode.value==true) index + 1 else 0
        }
    }

    private val _draftMedia = MutableLiveData<List<PostatMedia>>()

    private val _isDraftMode = MutableLiveData(false)
    val isDraftMode : LiveData<Boolean> = _isDraftMode

    val postatMedia: MediatorLiveData<List<AbstractUriMedia>> by lazy {
        val med = MediatorLiveData<List<AbstractUriMedia>>()
        fun update() {
            if (_isDraftMode.value == true) {
                med.postValue(_draftMedia.value)
            } else {
                med.postValue(_selectedMedia.value)
            }
        }
        med.addSource(_selectedMedia) { update() }
        med.addSource(_draftMedia) { update() }
        med.addSource(_isDraftMode) { update() }
        med
    }

    val selectedMediaCount = _selectedMedia.map {
        it.size
    }

    val maxMediaSelected = LiveEvent<Boolean>()

    val onMediaRemoved = LiveEvent<PostatDeviceMedia>()

    fun toggleSelectMedia(media: PostatDeviceMedia) {

        val selectedMedias = _selectedMedia.value.orEmpty().toMutableList()

        if (selectedMedias.contains(media)) {
            selectedMedias.remove(media)
            onMediaRemoved.postValue(media)
            media.mediaSelected = false
        } else {
            if (_multipleSelectionMode.value==true) {
                if (selectedMedias.size < 6) {
                    selectedMedias.add(media)
                    media.mediaSelected = true
                    media.selectionOrder = selectedMedias.size
                } else {
                    maxMediaSelected.postValue(true)
                    return
                }
            } else {
                selectedMedias.clear()
                selectedMedias.add(media)
            }
        }
        selectedMedias.updateOrder()
        _selectedMedia.postValue(selectedMedias)
    }

    fun toggleSelectionMode() {
        if (_multipleSelectionMode.value==true) {
            val medias = _selectedMedia.value.orEmpty()
            _multipleSelectionMode.value = false
            medias.updateOrder()
            _selectedMedia.postValue(medias.drop((medias.size - 1).coerceAtLeast(0)).toMutableList())
        } else {
            _multipleSelectionMode.value = true
            _selectedMedia.value?.updateOrder()
        }
    }

    private var imageCaptureTempFile: File? = null
    private var videoCaptureTempFile: File? = null

    suspend fun getImageUriForCapture(): Uri = withContext(Dispatchers.IO) {
        clearSelectedMedia()
        val file = postatRepo.createTempImageFile()
        imageCaptureTempFile = file
        postatRepo.getUriForFile(file)
    }

    suspend fun getVideoUriForCapture(): Uri = withContext(Dispatchers.IO) {
        clearSelectedMedia()
        val file = postatRepo.createTempVideoFile()
        videoCaptureTempFile = file
        postatRepo.getUriForFile(file)
    }

    private fun clearSelectedMedia() {
        _selectedMedia.postValue(mutableListOf())
    }

    val onAddCapturedMedia = LiveEvent<MediaType>()

    fun addCapturedImage() {
        viewModelScope.launch(Dispatchers.IO) {
            clearSelectedMedia()
            imageCaptureTempFile?.let { file ->
                imageCaptureTempFile = null
                val mime = MediaUtils.getMIME(file)
                _selectedMedia.postValue(mutableListOf(PostatDeviceMedia(file.toUri(),mime, isCaptured = true)))
                onAddCapturedMedia.postValue(MediaType.IMAGE)
            }
        }
    }

    fun addCapturedVideo() {
        viewModelScope.launch(Dispatchers.IO) {
            clearSelectedMedia()
            videoCaptureTempFile?.let { file ->
                videoCaptureTempFile = null
                val mime = MediaUtils.getMIME(file)
                val duration = MediaUtils.getDuration(file, true)
                if (duration < 1000) {
                    // TODO video is too short
                }
                _selectedMedia.postValue(mutableListOf(PostatDeviceMedia(file.toUri(), mime, duration, isCaptured = true)))
                onAddCapturedMedia.postValue(MediaType.VIDEO)
            }
        }
    }

    /**
     * Audio selection logic
     */

    data class SelectableAudioUIModel(
        var audio: MusicFile,
    ) : BaseObservable() {

        @get:Bindable
        var audioSelected: Boolean = false
            set(value) {
                field = value
                notifyPropertyChanged(BR.audioSelected)
            }
    }

    private var selectedAudioIndex: Int? = null

    private val _selectedAudio = MutableLiveData<MusicFile?>(null)
    val selectedAudio : LiveData<MusicFile?> = _selectedAudio

    val useOriginalAudio = MutableLiveData(true)

    val audioSelected : MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(useOriginalAudio.value == true || _selectedAudio.value != null)
        }
        med.addSource(_selectedAudio) { update() }
        med.addSource(useOriginalAudio) { update() }
        med
    }

    private var _selectedAudioRef: SelectableAudioUIModel? = null

    private val _audioList = searchTerm.switchMap { searchString ->
        postatRepo.getPostatAudioListPager(searchString).liveData.cachedIn(viewModelScope)
    }

    val audioList : MediatorLiveData<PagingData<SelectableAudioUIModel>> by lazy {
        val med = MediatorLiveData<PagingData<SelectableAudioUIModel>>()
        fun update() {
            val selectedId = _selectedAudio.value?.mediaMeta?.musicId
            val list: PagingData<SelectableAudioUIModel>? = _audioList.value?.map {
                SelectableAudioUIModel(it).apply {
                    if(selectedId == it.mediaMeta.musicId) {
                        audioSelected = true
                        _selectedAudioRef = this
                    }
                }
            }
            med.postValue(list)
        }
        med.addSource(_audioList) { update() }
        med.addSource(_selectedAudio) { update() }
        med
    }

    fun selectAudio(music: SelectableAudioUIModel, pos: Int?) {
        useOriginalAudio.postValue(false)
        _selectedAudio.postValue(music.audio)
        music.audioSelected = true
        selectedAudioIndex = pos
    }

    val onDeselectAudio = LiveEvent<Int>()

    fun useOriginalAudio(use: Boolean) {
        if (use == useOriginalAudio.value) return
        useOriginalAudio.postValue(use)
        _selectedAudio.postValue(null)
        Log.w("PALF", "useOriginalAudio: $use | selectAudio: $selectedAudioIndex")
        if (use) {
            _selectedAudioRef?.let {
                it.audioSelected = false
                _selectedAudioRef = null

            }
            selectedAudioIndex?.let {
                onDeselectAudio.postValue(it)
            }
            selectedAudioIndex = null
        }
    }

    private val _cookie = MutableLiveData<VideoPlaybackCookie>()
    val cookie: LiveData<VideoPlaybackCookie> = _cookie

    private fun getAudioCookies() {
        viewModelScope.launch {
            when(val result = postatRepo.getPlaybackCookie()) {
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
                is ResultOf.Success -> {
                    _cookie.postValue(result.value)
                }
            }
        }
    }

    /**
     * Mention Users
     */

    val mentionedUserRemoved = LiveEvent<PostatMentionedUser>()

    private val _mentionedUsers = MutableLiveData<List<PostatMentionedUser>>(emptyList())
    val mentionedUsers: LiveData<List<PostatMentionedUser>> = _mentionedUsers

    private val _mentionedUsersTemp = MutableLiveData<List<PostatMentionedUser>>(emptyList())
    val mentionedUsersTemp: LiveData<List<PostatMentionedUser>> = _mentionedUsersTemp

    fun initMentionsWorkingCopy() {
        _mentionedUsersTemp.postValue(_mentionedUsers.value.orEmpty().toMutableList())
    }

    fun commitMentions() {
        _mentionedUsers.postValue(_mentionedUsersTemp.value.orEmpty().toMutableList())
    }

     val nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    fun getNickName(user: PostatMentionedUser): String? {
        return nickNames.value.nickNameOrName(user)
    }

    fun addMentionedUser(mentionedUser: PostatMentionedUser) {
        val newList = _mentionedUsersTemp.value.orEmpty().toMutableList()
        newList.add(mentionedUser)
        _mentionedUsersTemp.value = newList
    }

    fun removeMentionedUser(mentionedUser: PostatMentionedUser) {
        val newList = _mentionedUsersTemp.value.orEmpty().toMutableList()
        newList.remove(mentionedUser)
        _mentionedUsersTemp.value = newList
        mentionedUserRemoved.postValue(mentionedUser)
    }
    fun removeKeywordFromSearchBox() {
        searchKeyword.postValue("")
    }

    /**
     * Post upload and processing logic
     */

    val postatCaption = MutableLiveData("")
    val enableComments = MutableLiveData(true)

    val onStartVideoEncode = LiveEvent<Unit>()

    private val _postatVideoProgress = MutableLiveData<Int?>(null)
    val postatVideoProgress: LiveData<Int?> = _postatVideoProgress
    private var postatVideoOngoingJob: Job? = null

    private val _postatSending = MutableLiveData(false)
    val postatSending: LiveData<Boolean> = _postatSending

    val onPostatCreate = LiveEvent<String>()
    val onPostatPosted = LiveEvent<Boolean>()
    val onPostatSavedToDraft = LiveEvent<String>()

    val enablePostButton: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            med.postValue(
                (_selectedMedia.value != null || _draftMedia.value != null) && _postatSending.value != true && mediaTransfer.value == null
                        && _postatVideoProgress.value == null && postatCaption.value != ""
            )
        }
        med.addSource(_selectedMedia) { update() }
        med.addSource(_postatSending) { update() }
        med.addSource(mediaTransfer) { update() }
        med.addSource(_postatVideoProgress) { update() }
        med.addSource(_draftMedia) { update() }
        med.addSource(postatCaption){ update() }
        med
    }

    fun postPostat(saveAsDraft: Boolean = false) {
        postatVideoOngoingJob?.let {
            if (it.isActive) {
                Log.w("PASM", "flashVideoOngoingJob is active")
                // TODO toast
                return
            }
        }
        _postatSending.postValue(true)

        viewModelScope.launch(Dispatchers.IO) {
            try {
                val musicData = if (useOriginalAudio.value == true) null else selectedAudio.value
                val mentionedUsersIdList = mentionedUsers.value?.map {
                    it.id
                }
                val postat = if (_isDraftMode.value == true) {
                    val medias = _draftMedia.value?: return@launch
                    val postatId = _postatId.value?: return@launch
                    postatRepo.createPostatFromDraft(medias, caption = postatCaption.value.orEmpty(),
                                                     enableComments = enableComments.value?: false,
                                                     mentionedUsers = mentionedUsersIdList,
                                                     hasMention = (mentionedUsersIdList?.size ?: 0) > 0,
                                                     isOriginalAudio = musicData == null,
                                                     music = musicData, postatId = postatId)
                } else {
                    val localMedia = _selectedMedia.value?: return@launch
                    processMedia(localMedia)
                    postatRepo.createPostat(localMedia, caption = postatCaption.value.orEmpty(),
                                                         enableComments = enableComments.value?: false,
                                                         mentionedUsers = mentionedUsersIdList,
                                                         hasMention = (mentionedUsersIdList?.size ?: 0) > 0,
                                                         isOriginalAudio = musicData == null ,
                                                         musicData = musicData, draft = saveAsDraft
                    )
                }

                currentlyTransferringMedia = postat

                if (saveAsDraft) onPostatSavedToDraft.postValue(postat.postat.messageId)
                else onPostatCreate.postValue(postat.postat.messageId)
                if (_isDraftMode.value == true) {
                    val result = postatRepo.sendPostat(postat, fromDraft = true)
                    if(result is ResultOf.Success) {
                        onPostatPosted.postValue(true)
                    } else onPostatPosted.postValue(false)
                } else
                    triggerPostatSync()
            } catch (e: Exception) {
                currentlyTransferringMedia = null
                Log.d("POSTAT", "postPostat: ${e.message}")
            }
            _postatSending.postValue(false)
        }
    }

    val mentionableUsersList = searchTerm.switchMap {
        postatRepo.getPostatMentionPager(it).liveData.cachedIn(viewModelScope)
    }
    private suspend fun triggerPostatSync() = PostatUploadWorker.startIfNotRunning()

    private suspend fun processMedia(medias: MutableList<PostatDeviceMedia>) = withContext(Dispatchers.IO) {
        postatVideoOngoingJob?.let {
            postatVideoOngoingJob = null
            if (it.isActive) it.cancelAndJoin()
        }
        onStartVideoEncode.postValue(Unit)
        _postatVideoProgress.postValue(-1)

        val ogAudio: Boolean = useOriginalAudio.value==true

        fun calculateProgress(index: Int, progress: Int = 100) = ((100*index)+progress)/medias.size
        launch {
            try {
                medias.forEachIndexed { index, med ->
                    med.processedFile = when (med.mediaType) {
                        MediaType.IMAGE -> {
                            _postatVideoProgress.postValue(calculateProgress(index))
                            processImage(med)
                        }

                        MediaType.VIDEO -> processVideo(med, object : VideoEncoderUtil.MediaProcessingListener {
                            override fun onProgress(progress: Int) {
                                Log.d("ENCODE", "Progress: $progress%")
                                _postatVideoProgress.postValue(calculateProgress(index, progress))
                            }

                            override fun onProcessingFinished(success: Boolean) {
                                Log.d("ENCODE", "Encode Done ($success) as per VM")
                                _postatVideoProgress.postValue(calculateProgress(index))
                            }
                        },ogAudio)

                        else -> null
                    }
                }
                _postatVideoProgress.postValue(null)
            }catch (e : Throwable){
                Log.w("ENCODE", "ProcessVideo cancelled", e)
                Firebase.crashlytics.recordException(e)
                postatVideoOngoingJob = null
                _postatVideoProgress.postValue(null)
                throw Exception("media processing cancelled")
            }
        }.apply {
            postatVideoOngoingJob = this
            join()
        }
    }

    private suspend fun processVideo(med: PostatDeviceMedia, listener: VideoEncoderUtil.MediaProcessingListener, useOriginalAudio: Boolean = true): File = withContext(Dispatchers.IO) {

        Firebase.crashlytics.log("Starting Video compress")
        Log.d("ENCODE", "Starting Video compress")
        try {
            val result = postatRepo.processVideo(med, listener)
            Log.d("ENCODE", "processed ${med.uri} to ${result.path}")
            Firebase.crashlytics.log("processed ${med.uri} to ${result.path}")
            return@withContext result
        } catch (e: Throwable) {
            Log.w("ENCODE", "ProcessVideo cancelled", e)
            Firebase.crashlytics.recordException(e)
            throw Exception("Video processing cancelled")
        }
    }

    private suspend fun processImage(med: PostatDeviceMedia): File = withContext(Dispatchers.IO) {
        Firebase.crashlytics.log("Starting image compress")
        Log.d("ENCODE", "Starting image compress")
        try {
            val imageFile = postatRepo.storeImageUriToTempFile(med.uri)
            val result = postatRepo.compressImage(imageFile)
            Log.d("ENCODE", "processed ${med.uri} to ${result.path}")
            Firebase.crashlytics.log("processed ${med.uri} to ${result.path}")
            return@withContext result
        } catch (e: Throwable) {
            Log.w("ENCODE", "Process image cancelled", e)
            Firebase.crashlytics.recordException(e)
            throw Exception("image processing cancelled")
        }

    }

    private var currentlyTransferringMedia: PostatWithMedia? = null

    val uploadCompleted = LiveEvent<PostatType>()

    val mediaTransfer: LiveData<MediaTransfer?> = MediaUploadListener.uploadProgressFlow.map {
        val id = currentlyTransferringMedia?.postat?.messageId?: return@map null
        it[id]?.let { progress ->
            if (progress >= 100) {
                uploadCompleted.postValue(currentlyTransferringMedia?.postat?.type)
            }
            return@map MediaTransfer(id).apply {
                this.progress = progress
            }
        }
        return@map null
    }.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    /**
     * Draft logic
     */

    private val _draftPostatList = postatRepo.getMyPostatPager(Postat.PostatType.DRAFT).liveData.cachedIn(viewModelScope)

    val draftPostatList = _draftPostatList.map { pagingData ->
        pagingData.map {
            MyPostatViewModel.MyPostatUIModel.PostatItem(it) as MyPostatViewModel.MyPostatUIModel
        }
    }

    private val _userSelectedMediaPermission = MutableLiveData(false)
    val userSelectedMediaPermission: LiveData<Boolean> = _userSelectedMediaPermission

    fun allowUserSelectedMediaPermission() {
        _userSelectedMediaPermission.value = true
    }

    fun setPostatDraft(postatId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val draft = postatRepo.getDraftPostat(postatId)?: return@launch
            _postatId.postValue(postatId)
            _isDraftMode.postValue(true)
            postatCaption.postValue(draft.postat.message)
            enableComments.postValue(draft.postat.turnOffComments == false)
            _draftMedia.postValue(draft.postat.media)
            draft.postat.mentionedUsers.let {
                if (it.isNotEmpty()) loadMentions(it)
            }
            if (draft.postat.isOriginalAudio) useOriginalAudio(true)
            else selectAudio(SelectableAudioUIModel(
                MusicFile(
                    songName = draft.postat.musicData?.mediaName.orEmpty(),
                    composerName = draft.postat.musicData?.composer,
                    mediaMeta = MusicFile.MediaMeta(
                        mediaUrl = draft.postat.musicData?.mediaUrl.orEmpty(),
                        musicId = draft.postat.musicData?.mediaId.orEmpty()
                    )
                )
            ).apply { audioSelected = true },null)
        }
    }

    private suspend fun loadMentions(ids : List<Int>) {
        val list = mutableListOf<PostatMentionedUser>()
        ids.forEach { id ->
            val result = profileRepo.getPublicUserDetails(id, true)
            if (result is ResultOf.Success) {
                list.add(PostatMentionedUser.from(result.value).apply {
                })
            } else return
        }
        _mentionedUsers.postValue(list)
    }

    /**
     * Delete postat logic
     */

    val onPostatDeleted = LiveEvent<Boolean>()

    fun deletePost() {
        viewModelScope.launch(Dispatchers.IO) {
            val id = _postatId.value?: return@launch
            when (postatRepo.deletePostat(id)) {
                is ResultOf.Success -> {
                    onPostatDeleted.postValue(true)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    val canSelectCarouselImageUsers = listOf(UserCitizenship.OFFICER,UserCitizenship.AMBASSADOR,UserCitizenship.MINISTER,UserCitizenship.PRESIDENT)
    val canSelectMusicUsers = listOf(UserCitizenship.AMBASSADOR,UserCitizenship.MINISTER,UserCitizenship.PRESIDENT)

    val hideCarouselImage:Boolean
    get() = user.citizenship !in canSelectCarouselImageUsers

    val hideMusic:Boolean
    get() = user.citizenship !in canSelectMusicUsers

    val isPremium:Boolean
        get() = user.premium

}