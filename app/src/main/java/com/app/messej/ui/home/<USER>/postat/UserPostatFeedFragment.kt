package com.app.messej.ui.home.publictab.postat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.Postat
import com.app.messej.databinding.FragmentPublicPostatUserListBinding
import com.app.messej.ui.chat.PrivateChatFragmentDirections
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoinFromGreenDot
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import kotlinx.coroutines.launch

class UserPostatFeedFragment: PostatFeedBaseFragment() {

    private lateinit var outerBinding: FragmentPublicPostatUserListBinding
    private val args: UserPostatFeedFragmentArgs by navArgs()

    override val showIgnoreUserOption: Boolean
        get() = false
    override val viewModel: UserPostatFeedViewModel by viewModels()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_postat_user_list, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        innerBinding = outerBinding.feedLayout
        outerBinding.viewModel = viewModel
        return outerBinding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(outerBinding.customActionBar.toolbar)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setUserIdAndGreenDotStatus(args.userId,args.podiumId)
        outerBinding.customActionBar.chatDp.setOnClickListener {
            if (viewModel.livePodiumId.value?.isNotEmpty() == true) {
                val podiumId = viewModel.livePodiumId.value?: return@setOnClickListener
                validateAndConfirmJoinFromGreenDot(podiumId, viewModel.profile.value?.name?:return@setOnClickListener, viewModel.user)
            }
            else {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(args.userId))
            }
        }
    }
    override fun onUserClick(item: Postat) {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(item.userId))
    }

    //prevent menu
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return
    }
}
