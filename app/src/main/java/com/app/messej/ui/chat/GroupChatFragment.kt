package com.app.messej.ui.chat

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.enums.AttachmentSource
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.GroupChatStatus
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.databinding.ItemCustomActionBarHuddleChatBinding
import com.app.messej.databinding.LayoutImageViewerHeaderBinding
import com.app.messej.databinding.LayoutJoinErrorBinding
import com.app.messej.databinding.LayoutMessageBannedUserBinding
import com.app.messej.ui.chat.delegates.GroupChatActionsDelegate
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.publictab.huddles.huddleInfo.HuddleDeleteLeaveConfirmFragment
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils
import com.stfalcon.imageviewer.StfalconImageViewer


class GroupChatFragment : BaseChatFragment(), MenuProvider, GroupChatActionsDelegate by GroupChatActionsDelegate.GroupChatActionsDelegateImpl {

    private val args: GroupChatFragmentArgs by navArgs()

    override val viewModel: GroupChatViewModel by navGraphViewModels(R.id.nav_chat_group)

    private lateinit var actionBarBinding: ItemCustomActionBarHuddleChatBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        val root = super.onCreateView(inflater, container, savedInstanceState)
        inflateActionBar()
        return root
    }

    private fun inflateActionBar() {
        binding.actionBarStub.apply {
            viewStub?.apply {
                setOnInflateListener { _, inflated ->
                    actionBarBinding = ItemCustomActionBarHuddleChatBinding.bind(inflated)
                }
                layoutResource = R.layout.item_custom_action_bar_huddle_chat
                inflate()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(actionBarBinding.toolbar)
        }
    }

    private fun setup() {
        viewModel.setHuddleId(args.huddleId,HuddleType.PRIVATE)
        actionBarBinding.toolbarLayout.setOnClickListener {
            if (viewModel.huddle.value?.huddleStatus!=GroupChatStatus.BLOCKED && !viewModel.entryBlocked()){
                findNavController().navigateSafe(GroupChatFragmentDirections.actionGlobalNavHuddleInfo(args.huddleId))
            }
        }
        ActiveChatTracker.registerActiveScreen(ActiveChatTracker.ActiveChatScreen.GroupChat(args.huddleId),viewLifecycleOwner)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun observe() {
        viewModel.enableChatInteraction.observe(viewLifecycleOwner) {
            mAdapter?.allowReplySwipe = it?:false
            mAdapter?.notifyDataSetChanged()
        }

        viewModel.huddle.observe(viewLifecycleOwner) {
            actionBarBinding.huddle = it
        }

        viewModel.huddleStatus.observe(viewLifecycleOwner) {
            Log.d("HCVM", "observe: huddle status changed: $it")
            val type = viewModel.huddle.value?.huddleType?: return@observe
            if (viewModel.isUserBanned) {
                binding.actionDecorHolder.apply {
                    removeAllViews()
                    binding.footer.visibility = View.GONE
                    val binding = DataBindingUtil.inflate<LayoutMessageBannedUserBinding>(layoutInflater, R.layout.layout_message_banned_user, this, false)
                    binding.text = getString(R.string.chat_banned_user_group_message)
                    addView(binding.root)
                }
                return@observe
            }
            initJoinLayout(
                binding.actionDecorHolder, layoutInflater,viewLifecycleOwner,viewModel,it,type,
                onJoinClick = {
                    ensureInteractionAllowed {
                        viewModel.instantJoinHuddle()
                    }
                }, onAcceptInviteClick = {
                    ensureInteractionAllowed {
                        viewModel.acceptInvite()
                    }
                }
            )
        }

        viewModel.adminStatus.observe(viewLifecycleOwner) {
            it?: return@observe
            Log.d("HCVM", "observe: huddle admin status changed: $it")
            initAdminInvite(binding.actionDecorHolderTop,layoutInflater,viewLifecycleOwner,viewModel,it)
        }

        viewModel.userHasElevatedRole.observe(viewLifecycleOwner) {
            (activity as MenuHost).invalidateMenu()
        }
        viewModel.canShareHuddleLink.observe(viewLifecycleOwner) {
            (activity as MenuHost).invalidateMenu()
        }
        viewModel.canSendInvites.observe(viewLifecycleOwner) {
            (activity as MenuHost).invalidateMenu()
        }
        viewModel.userHasElevatedRole.observe(viewLifecycleOwner) {
            (activity as MenuHost).invalidateMenu()
        }
        viewModel.onlineCount.observe(viewLifecycleOwner) {
            actionBarBinding.onlineCount = it
        }

        viewModel.typingInfo.observe(viewLifecycleOwner) {
            actionBarBinding.typingInfo = it
        }

        viewModel.onExitChat.observe(viewLifecycleOwner){ reason ->
            when (reason) {
                GroupChatViewModel.ChatExitReason.HuddleDeleted -> Toast.makeText(context, getString(R.string.huddle_does_not_exist), Toast.LENGTH_SHORT).show()
                GroupChatViewModel.ChatExitReason.LoadFailed -> Toast.makeText(context, getString(R.string.huddle_load_failed), Toast.LENGTH_SHORT).show()
                is GroupChatViewModel.ChatExitReason.WrongHuddleType -> Toast.makeText(context, getString(R.string.huddle_wrong_type), Toast.LENGTH_SHORT).show()
                is GroupChatViewModel.ChatExitReason.UserAction -> when(reason.action){
                    AbstractHuddle.HuddleUserStatus.INVITE_DECLINED -> {
//                        Toast.makeText(requireContext(), "Invite Declined", Toast.LENGTH_SHORT).show()
                    }
                    AbstractHuddle.HuddleUserStatus.JOIN_REQUEST_BLOCKED -> {
                        Toast.makeText(requireContext(), resources.getString(R.string.huddle_group_invite_blocked), Toast.LENGTH_SHORT).show()
                    }
                    AbstractHuddle.HuddleUserStatus.CANCELLED -> {
                        Toast.makeText(requireContext(), resources.getString(R.string.huddle_group_request_cancelled), Toast.LENGTH_SHORT).show()
                    }
                    else -> {}
                }

                is GroupChatViewModel.ChatExitReason.HuddleAction -> {}
            }
            findNavController().popBackStack()
        }

        viewModel.onReportMessage.observe(viewLifecycleOwner) {
            val msg= it as HuddleChatMessage
            findNavController().navigateSafe(GroupChatFragmentDirections.actionGlobalReportToManagerFragment(msg.huddleIdInt, msg.messageId, null, ReportToManagerType.MESSAGE))
        }

        viewModel.onFailedToAcceptAdminInvite.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(activity, resources.getString(R.string.group_admin_request_limit_exceed_toast_receiver), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onHuddleLeft.observe(viewLifecycleOwner){
            if (it) findNavController().popBackStack()
        }

        viewModel.onJoinActionError.observe(viewLifecycleOwner){ message ->
            MaterialDialog(requireContext()).show {
                val view = DataBindingUtil.inflate<LayoutJoinErrorBinding>(layoutInflater, R.layout.layout_join_error, null, false)
                customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                cancelable(true)
                view.txtContent.text = message
                view.closeButton.setOnClickListener {
                    dismiss()
                    findNavController().popBackStack()
                }
            }
        }

        viewModel.onJoinLimitExceeded.observe(viewLifecycleOwner){ params ->
            MaterialDialog(requireContext()).show {
                val view = DataBindingUtil.inflate<LayoutJoinErrorBinding>(layoutInflater, R.layout.layout_join_error, null, false)
                customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                cancelable(true)
                view.txtContent.text = context.getString(if (params.premium) R.string.title_group_max_count_premium_user else R.string.title_group_max_count_free_user,params.limit)
                view.closeButton.setOnClickListener {
                    dismiss()
                    findNavController().popBackStack()
                }
            }
        }

        setFragmentResultListener(HuddleDeleteLeaveConfirmFragment.HUDDLE_DELETE_LEAVE_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(HuddleDeleteLeaveConfirmFragment.HUDDLE_DELETE_LEAVE_RESULT_KEY)
            if (result) viewModel.leaveHuddle()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        if ((viewModel.userHasElevatedRole.value == true ||
            viewModel.canShareHuddleLink.value == true ||
            viewModel.canSendInvites.value==true) && !viewModel.entryBlocked()) {
            menuInflater.inflate(R.menu.menu_chat_group_actions, menu)
        }
    }

    private var notificationBadge: BadgeDrawable? = null

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        if(viewModel.userHasElevatedRole.value == true) {
            notificationBadge = BadgeDrawable.create(requireContext()).apply {
                isVisible = (viewModel.huddle.value?.requestsAndInvites ?: 0) > 0
                adjustForNotifications(requireContext())
                BadgeUtils.attachBadgeDrawable(this, actionBarBinding.toolbar, R.id.action_more)
            }
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    private fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_chat_group_action_more, popup.menu)
        popup.setForceShowIcon(true)
        popup.menu.apply {
            findItem(R.id.action_requests).isVisible = viewModel.userHasElevatedRole.value==true
            findItem(R.id.action_invite).isVisible = viewModel.canSendInvites.value==true
            findItem(R.id.action_leave).isVisible = viewModel.userHasElevatedRole.value == false
        }

        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_requests -> findNavController().navigateSafe(GroupChatFragmentDirections.actionGlobalManageRequestInviteFragment(args.huddleId))
                R.id.action_invite -> findNavController().navigateSafe(GroupChatFragmentDirections.actionGlobalAddParticipantsFragment(args.huddleId, false))
                R.id.action_leave -> findNavController().navigateSafe(GroupChatFragmentDirections.actionGlobalHuddleDeleteLeaveConfirmFragment(false, HuddleType.PRIVATE))
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    override fun showAttachDialog() {
        var src = AttachmentSource.entries.except(AttachmentSource.LOCATION)
        val action = GroupChatFragmentDirections.actionGlobalImageAttachSourceFragment(src)
        findNavController().navigateSafe(action)
    }

    override fun showImagePreview() {
        findNavController().navigateSafe(GroupChatFragmentDirections.actionPublicGroupChatFragmentToPublicGroupChatAttachImageFragment(viewModel.huddle.value?.name))
    }

    override fun showVideoPreview() {
        findNavController().navigateSafe(GroupChatFragmentDirections.actionPrivateGroupChatFragmentToPublicGroupChatAttachVideoFragment(viewModel.huddle.value?.name))
    }

    override fun navigateToLocationSelect() {
        findNavController().navigateSafe(GroupChatFragmentDirections.actionPrivateGroupChatFragmentToGroupChatAttachLocationFragment())
    }

    override fun showDocumentPreview() {
        findNavController().navigateSafe(GroupChatFragmentDirections.actionPrivateGroupChatFragmentToPublicGroupChatAttachDocumentFragment(viewModel.huddle.value?.name))
    }

    override fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_chat_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_reply -> viewModel.replyToSelection()
                        R.id.action_forward->findNavController().navigate(GroupChatFragmentDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.GROUPS, messageId = viewModel.selectedChats.value?.get(0)))
                        R.id.action_copy -> viewModel.copySelection()
                        R.id.action_report -> viewModel.reportSelection()
                        R.id.action_cancel_report -> viewModel.reportSelection()
                        R.id.action_delete -> confirmDelete(R.string.chat_delete_confirm_title, R.string.chat_delete_confirm_message,
                                                            viewModel.huddle.value?.huddleType==HuddleType.PRIVATE,
                                                            viewModel.canDeleteSelectionForEveryone.value == true) {
                            viewModel.deleteSelection(it)
                        }
                        else -> return false
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }

    override fun onForwardClick(item: AbstractChatMessage, position: Int) {
        findNavController().navigate(GroupChatFragmentDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.GROUPS, messageId =  item.messageId))
    }
    override fun customizeImageOverlay(viewer: StfalconImageViewer<OfflineMedia>, headerBinding: LayoutImageViewerHeaderBinding, msg: AbstractChatMessageWithMedia) {
        headerBinding.toolbar.addMenuProvider(object: MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_chat_image_fullscreen_with_actions,menu)
                menu.findItem(R.id.action_delete).isVisible = viewModel.canDeleteMessage(msg)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                when (menuItem.itemId) {
                    R.id.action_save_to_gallery -> viewModel.saveToGallery(msg.offlineMedia)
                    R.id.action_delete -> confirmDelete(R.string.chat_delete_confirm_title, R.string.chat_delete_confirm_message,
                                                        viewModel.huddle.value?.huddleType==HuddleType.PRIVATE,
                                                        viewModel.canDeleteMessageForEveryOne(msg)) {
                        viewModel.deleteMessage(msg.message,it)
                        viewer.close()
                    }
                    else -> return false
                }
                return true
            }

        },viewLifecycleOwner)
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.sendChatExit()
    }
}