package com.app.messej.ui.home.publictab.flash.player

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import androidx.paging.PagingData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.FlashVideoStats
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.worker.FlashVideoDownloadWorker
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.flash.myflash.FlashVideoUIModel
import com.app.messej.ui.home.publictab.flash.player.cache.FlashVideoPreloadManager
import com.app.messej.ui.utils.CountryListUtil
import com.google.gson.Gson
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlin.math.max

abstract class BaseFlashPlayerViewModel(application: Application) : AndroidViewModel(application) {

    protected val accountRepo = AccountRepository(application)
    protected val profileRepo = ProfileRepository(application)
    protected val flashRepo = FlashRepository(application)

    protected val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    val user: CurrentUser get() = accountRepo.user

    val isMinister : Boolean
        get() = user.citizenship == UserCitizenship.MINISTER
    val isPresident : Boolean
        get() = user.citizenship == UserCitizenship.PRESIDENT

    val isCitizenUser: Boolean
        get() = user.citizenship.isVisitor

    private val viewState = MutableStateFlow<MultiStateView.ViewState?>(MultiStateView.ViewState.LOADING)

    val debouncedViewState = viewState.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).distinctUntilChanged()

    fun setViewState(state: MultiStateView.ViewState) {
        viewState.value = state
    }

    protected abstract val _flashFeedList: LiveData<PagingData<FlashVideo>>

    private val flashStats: MutableList<FlashVideoStats> = mutableListOf()

    private fun MutableList<FlashVideoStats>.takeLast(count: Int): MutableList<FlashVideoStats> {
        return subList(max(size-count, 0), max(size-1,0))
    }

    val flashFeedList: MediatorLiveData<PagingData<FlashVideoUIModel>> by lazy {
        val med = MediatorLiveData<PagingData<FlashVideoUIModel>>()
        fun combine() {
            val list = _flashFeedList.value
            val nickNames = _nickNames.value
            val newList = list?.map { item ->
                val nickNameOrName = nickNames?.find { nn -> nn.userId == item.userId }?.nickName?:item.senderDetails?.name
                item.senderDetails?.name = nickNameOrName.orEmpty()
                FlashVideoUIModel(flashVideo = item).apply {
                    val selectedFlash = _selectedChatsList.find { return@find it.flashVideo.id==item.id }
                    selectedFlash?.let {
                        selected = true
                        _selectedChatsList.remove(selectedFlash)
                        _selectedChatsList.add(this)
                    }
                }
            }
            med.postValue(newList)
        }
        med.addSource(_flashFeedList) { combine() }
        med.addSource(_nickNames) { combine() }
        med
    }

    val flashPlayerFeedList: LiveData<PagingData<FlashPlayerAdapter.FlashPlayerUIModel>> by lazy {
        flashFeedList.map { pg ->
            val flags = _countryList.value
            pg.map { flash ->
                val countryFlag = flash.flashVideo.senderDetails?.countryCode?.let { cc ->
                    flags?.get(cc)
                }
                Log.d("BVSM1","flashStats ${flashStats.map{
                    System.identityHashCode(it)
                }}")
                val stat: FlashVideoStats = flashStats.find { it.flashId == flash.flashVideo.id }?:run{
                    val new = FlashVideoStats.from(flash.flashVideo)
                    flashStats.add(new)
                    new
                }
                Log.d("BVSM1","${System.identityHashCode(this)} Inside flashPlayerFeedList: ${stat.userLivePodium} ${flash.flashVideo.caption} ${System.identityHashCode(stat)}")
                FlashPlayerAdapter.FlashPlayerUIModel(flash.flashVideo, stat, countryFlag)
            }
        }
    }

    var activeItem: Int? = null

    private var scrollingDown: Boolean? = null

    fun setActiveItem(pos: Int,scroll: Boolean = false) {
        val cur = activeItem
        scrollingDown = if (scroll && cur!=null) {
            if(cur==pos) scrollingDown else cur<pos
        }
        else null
        Log.w("FLASHP", "setActiveItem: $pos,$scroll | current: $cur scrollingDown: $scrollingDown")
        activeItem = pos
    }

    fun skipToNext(): Int? {
        scrollingDown?.let {
            activeItem?.let { cur ->
                val next = if (it) cur+1 else (cur-1).coerceAtLeast(0)
                Log.w("FLASHP", "skipToNext: skip $cur -> $next")
                return next
            }
        }
        return null
    }

    fun clearActiveItem() {
        activeItem = null
    }

    fun getDetailForItem(flashId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = flashRepo.getFlashDetail(flashId)) {
                is ResultOf.Success -> {
                    result.value.let { flash ->
                        flashStats.find { it.flashId == flash.id }?.apply {
                            updateWith(flash)
                            Log.w("BFPVM", "getDetailForItem: ${Gson().toJson(flash)}")
                            Log.w("BFPVM", "getDetailForItem: ${Gson().toJson(this)}")
                        }
                    }
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    private val _cookies = flashRepo.playbackCookieFlow()
    @UnstableApi
    val cookies = _cookies.map {
        if (it==null || it.isExpired){
            refreshCookies()
            null
        } else {
            FlashVideoPreloadManager.init(it)
            it
        }
    }.stateIn(viewModelScope, SharingStarted.Eagerly,null)

    val onCookiesRefreshed = LiveEvent<VideoPlaybackCookie>()

    protected val _countryList = MutableLiveData<Map<String,Int>>()

    init {
        refreshCookies()
        _countryList.postValue(CountryListUtil.getCustomCountryMap())
    }

    fun refreshCookies() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = flashRepo.refreshPlaybackCookie()) {
                is ResultOf.Success -> {
                    result.value.let {
                        onCookiesRefreshed.postValue(it)
                    }
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    @UnstableApi
    fun preloadMedia(flash: FlashVideo) {
        FlashVideoPreloadManager.preloadMedia(flash)
    }

    val onFlashDeleted = LiveEvent<Boolean>()

    fun deleteFlash(flash: FlashVideo) {
        viewModelScope.launch(Dispatchers.IO){
            when(flashRepo.deleteFlash(flash)) {
                is ResultOf.Success -> {
                    onFlashDeleted.postValue(true)
                }
                else -> {}
            }
        }
    }

    val onFlashBlocked = LiveEvent<Boolean>()
    fun blockFlashUser(userId: Int) {
        viewModelScope.launch(Dispatchers.IO){
            when (flashRepo.flashBlock(userId)) {
                is ResultOf.Success -> {
                    onFlashBlocked.postValue(true)
                }
                else -> {}
            }
        }
    }

    val onFlashSaved = LiveEvent<Boolean>()

    fun saveFlash(flash: FlashVideo) {
        viewModelScope.launch(Dispatchers.IO) {
            if(flashRepo.saveFlash(flash)) {
                onFlashSaved.postValue(true)
            }
        }
    }

    fun saveToGallery(flash: FlashVideo) {
        viewModelScope.launch(Dispatchers.IO) {
            FlashVideoDownloadWorker.startDownload(flash)
        }
    }

    val onCommentDisabled = LiveEvent<Boolean>()

    fun commentToggle(flash: FlashVideo) {
        viewModelScope.launch(Dispatchers.IO){
            when(flashRepo.commentToggle(flash.id,enable = flash.commentDisabled)) {
                is ResultOf.Success -> {
                    flash.commentDisabled = !flash.commentDisabled
                    Log.w("BFPF", "commentToggle: $flash")
                    onCommentDisabled.postValue(flash.commentDisabled)
                }
                else -> {}
            }
        }
    }

    val onItemLiked = LiveEvent<Int>()
    fun toggleLike(item: FlashVideo, position: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            Log.i("BVSM1", "toggleLike: ${item.caption} ")
            val flash = flashStats.find { it.flashId == item.id }?: FlashVideoStats.from(item)
            when (if(flash.liked) flashRepo.unlikeFlash(item.id) else flashRepo.likeFlash(item.id)) {
                is ResultOf.Success -> {
                    val count = if (flash.liked) flash.likeCount.dec() else flash.likeCount.inc()
                    item.apply {
                        likeCount = count
                        isLiked = !flash.liked
                    }
                    flash.apply {
                        likeCount = count
                        liked = !flash.liked
                    }
                    onItemLiked.postValue(position)
                }
                else -> {}
            }
        }
    }

    private val _chatSelectionMode = MutableLiveData(false)
    val chatSelectionMode: LiveData<Boolean> = _chatSelectionMode

    private var _selectedChatsList: MutableList<FlashVideoUIModel> = mutableListOf()
    protected val _selectedChats = MutableLiveData<List<FlashVideo>>(listOf())
    val selectedChats: LiveData<List<String>> = _selectedChats.map { chat ->
        return@map chat.map { it.id }
    }

    val selectedCount = selectedChats.map {
        it.size
    }

    fun enterSelectionMode() {
        if (_chatSelectionMode.value == false) {
            _chatSelectionMode.value = true
            _selectedChatsList = mutableListOf()
        }
    }

    fun selectMessage(msg: FlashVideoUIModel, pos: Int): Boolean {
        if (_chatSelectionMode.value == false) return false
        _selectedChatsList.apply {
            if (removeIf { msg.flashVideo.id == it.flashVideo.id }) {
                msg.selected = false
            } else {
                msg.selected = true
                add(msg)
            }
            _selectedChats.value = map { it.flashVideo }.toList()
        }
        if(_selectedChatsList.isEmpty()) {
            exitSelectionMode()
        }
        return true
    }

    fun exitSelectionMode() {
        _chatSelectionMode.value = false
        _chatSelectionMode.value = false
        _selectedChatsList.forEach {
            it.selected = false
        }
        _selectedChatsList = mutableListOf()
        _selectedChats.value = listOf()
    }

    fun deleteMultipleFlash() {
        selectedChats.value?.let {list->
            if(list.isEmpty()) return
            viewModelScope.launch(Dispatchers.IO){
                when(flashRepo.deleteFlashVideos(list)) {
                    is ResultOf.Success -> {
                        onFlashDeleted.postValue(true)
                    }
                    else -> {}
                }
            }
            exitSelectionMode()
        }
    }

    fun isSelf(flashUserID: Int) = user.id == flashUserID

    val hasEmpowermentToBlockUserOnFlash: Boolean
        get() = user.userEmpowerment?.allowBlockUserPostingFlash == true


    val canDeleteFlashVideo: Boolean
        get() {
            // Check if the empowerment is not null
            user.userEmpowerment?.canDeleteAnyFlashVideo?.let {
                // If it's true, return true immediately
                if (it) return true
            }
            // If it's false or null, check the user's citizenship
            return user.citizenship == UserCitizenship.AMBASSADOR ||
                    user.citizenship == UserCitizenship.MINISTER
        }

    val onPaidLikeInsufficientBalance = LiveEvent<Boolean>()
    val onPaidLikeError = LiveEvent<String>()

    val isGiftReceiverBanned=LiveEvent<Boolean>()

    fun sendPaidLike(flashId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = flashRepo.sendPaidLike(flashId)) {
                is ResultOf.Success -> {
                    Log.i("BFPVM", "sendPaidLike: Success for $flashId")
                }
                is ResultOf.APIError -> {
                    //423
                    if (result.code == 423) {
                        isGiftReceiverBanned.postValue(true)
                    }
                    else if (result.code == 400) {
                        onPaidLikeInsufficientBalance.postValue(true)
                    } else {
                        onPaidLikeError.postValue(result.error.message)
                    }
                }
                is ResultOf.Error -> {
                    Log.e("BFPVM", "sendPaidLike: Error for $flashId")
                }
            }
        }
    }
    
}