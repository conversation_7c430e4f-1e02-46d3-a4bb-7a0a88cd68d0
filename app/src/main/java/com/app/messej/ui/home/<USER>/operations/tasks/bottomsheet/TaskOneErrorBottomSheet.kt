package com.app.messej.ui.home.businesstab.operations.tasks.bottomsheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.databinding.LayoutTaskOneBottomSheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class TaskOneErrorBottomSheet : BottomSheetDialogFragment() {
    var binding: LayoutTaskOneBottomSheetBinding?=null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View?{
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_task_one_bottom_sheet, container, false)
        binding?.lifecycleOwner = viewLifecycleOwner
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        binding?.btnTaskDialogueActionClose?.setOnClickListener {
            dismissNow()
        }
    }

}