package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavChatGroupDirections
import com.app.messej.R
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.databinding.FragmentHuddleParticipantsBinding
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView

class HuddleParticipantsFragment : Fragment() {

    private lateinit var binding: FragmentHuddleParticipantsBinding
    private val viewModel : HuddleParticipantsViewModel by navGraphViewModels(R.id.navigation_huddle_participants)

    private val args: HuddleParticipantsFragmentArgs by navArgs()

    private var mAdapter : HuddleParticipantsListAdapter? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_participants, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        val actionBar = binding.customActionBar.toolbar
        (activity as MainActivity).setupActionBar(actionBar)
        actionBar.title = requireContext().getString(R.string.huddle_info_participants_text)
    }

    private fun setup() {
        viewModel.setHuddleId(args.huddleId,args.huddleType)
        initAdapter()
        binding.apply {
            keyword = viewModel!!.searchKeyword
        }

        binding.addParticipantsFab.setOnClickListener {
            findNavController().navigateSafe(NavChatGroupDirections.actionGlobalAddParticipantsFragment(args.huddleId, false))
        }

        binding.participantsFilterButton.setOnClickListener {
            findNavController().navigateSafe(HuddleParticipantsFragmentDirections.actionPublicHuddleParticipantsFragmentToHuddleParticipantsFilterMenuFragment())
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).apply { visibility = View.GONE }
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = resources.getString(R.string.huddle_participants_no_result_found_text)
            findViewById<MaterialButton>(R.id.eds_empty_action).apply {
                visibility = View.GONE
            }
        }
    }

    private fun observe() {
        viewModel.huddle.observe(viewLifecycleOwner) {
            it?: return@observe
            binding.customActionBar.toolbar.title = if (it.totalMembers==null) requireContext().getString(R.string.huddle_info_participants_text) else requireContext().getString(R.string.huddle_info_total_participants_text,it.totalMembers)
            mAdapter?.apply {
                isCurrentUserElevated = (it.isAdmin || it.isManager)
                currentUserId = viewModel.user.id
                isTribe= it.isTribe == true
                isCurrentUserPremium=viewModel.user.premium  /**checks user is premium */
                isCurrentUserManager=it.isManager==true
                isCurrentUserEmpoweredToBlock = viewModel.user.userEmpowerment?.canBlockAnyUserInHuddle == true && it.huddleType == HuddleType.PUBLIC
            }
        }
        viewModel.participantsList.observe(viewLifecycleOwner) { pagingData ->
            if (pagingData!=null)
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
        }

        viewModel.onFailedToSendAdminInvite.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(activity, resources.getString(R.string.huddle_admin_request_limit_exceed_toast_sender), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.participantsAPIRequestActionError.observe(viewLifecycleOwner) {
            it?.let {
                if (it.isEmpty()) {
                    Toast.makeText(activity, resources.getString(R.string.default_unknown_error_message), Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(activity, it, Toast.LENGTH_SHORT).show()
                }
            }
        }
        viewModel.onChatIDComplete.observe(viewLifecycleOwner){
            val action = HuddleParticipantsFragmentDirections.actionGlobalNavigationChatPrivate(it.first,it.second)
            (activity as MainActivity).navController.navigateSafe(action)
        }

        viewModel.onRestrictedFromHuddle.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                showSnackbar(it)
            }
        }
    }

    private fun initAdapter() {
        mAdapter = HuddleParticipantsListAdapter(args.huddleType,object : HuddleParticipantsListAdapter.ItemListener {

            override fun onItemClick(item: Participant, position: Int, currentUserId: Int?) {
//                if (item.id == currentUserId) findNavController().navigateSafe(HuddleParticipantsFragmentDirections.actionGlobalProfileFragment())
//                else {
//                    val userType = PublicUserProfileLoaderFragment.getUserType(item.userCitizenship)
//                    findNavController().navigateSafe(HuddleParticipantsFragmentDirections.actionGlobalPublicUserProfileFragment(item.id, userType = userType))
//                }
//                val userType = PublicUserProfileLoaderFragment.getUserType(item.userCitizenship)

                    findNavController().navigateSafe(HuddleParticipantsFragmentDirections.actionGlobalPublicUserProfileFragment(item.id))

            }

            override fun onMenuItemClick(item: Participant, position: Int, menuItem: MenuItem) {
                when (menuItem.itemId) {
                    R.id.appoint_as_admin -> viewModel.canSendAdminRequest(item, cancel = false)
                    R.id.user_info->{
//                        val userType = PublicUserProfileLoaderFragment.getUserType(item.userCitizenship)
                        findNavController().navigateSafe(HuddleParticipantsFragmentDirections.actionGlobalPublicUserProfileFragment(item.id,  false, UserProfileContext.GENERAL))
                    }
                    R.id.send_private_message-> viewModel.generateRoomID(item.id)
                    R.id.remove_participant-> viewModel.removeUser(item)

                    R.id.cancel_invitation -> viewModel.appointAsAdmin(item, cancel = true)
                    R.id.dismiss_admin -> viewModel.dismissAdmin(item)
                    
                    R.id.block_participant -> confirmAction(
                        title = if(args.huddleType==HuddleType.PUBLIC) R.string.huddle_participants_menu_block_text else R.string.group_participants_menu_block_text,
                        message = resources.getString(if(args.huddleType==HuddleType.PUBLIC) R.string.other_user_huddle_block_confirm else R.string.other_user_group_block_confirm, item.name),
                        positiveTitle = R.string.common_yes,
                        negativeTitle = R.string.common_no
                    ) {
                        viewModel.blockUnblockUser(item.id, Participant.ParticipantsActionTypes.BLOCK_HUDDLE_PARTICIPANT)
                    }
                    R.id.unblock_participant -> viewModel.blockUnblockUser(item.id, Participant.ParticipantsActionTypes.UNBLOCK_HUDDLE_PARTICIPANT)

                    R.id.ban_replying-> viewModel.banUser(Participant.ParticipantStatus.REPLY_BAN, true, item.id)
                    R.id.unban_replying-> viewModel.banUser(Participant.ParticipantStatus.REPLY_BAN, false, item.id)

                    R.id.ban_commenting-> viewModel.banUser(Participant.ParticipantStatus.COMMENT_BAN, true, item.id)
                    R.id.unban_commenting-> viewModel.banUser(Participant.ParticipantStatus.COMMENT_BAN, false, item.id)

                    R.id.ban_posting-> viewModel.banUser(Participant.ParticipantStatus.POST_BAN, true, item.id)
                    R.id.unban_posting-> viewModel.banUser(Participant.ParticipantStatus.POST_BAN, false, item.id)
                    else -> {}
                }
            }
        })

        val layoutMan = LinearLayoutManager(context)
        binding.participantsList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }

}