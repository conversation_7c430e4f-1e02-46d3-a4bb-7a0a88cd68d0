package com.app.messej.ui.home.publictab.podiums.friends

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.PodiumFriend
import com.app.messej.databinding.ItemPodiumListFriendsBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.displayText

class PodiumLiveFriendsAdapter(val listener: UserActionListener):
    PagingDataAdapter<PodiumFriend, PodiumLiveFriendsAdapter.PodiumLiveFriendsViewHolder>(TransactionsDiff) {

    interface UserActionListener {
        fun onUserClick(item: PodiumFriend,view: View)
        fun onItemClicked(item: PodiumFriend)
    }

    override fun onBindViewHolder(holder: PodiumLiveFriendsViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = PodiumLiveFriendsViewHolder(
        ItemPodiumListFriendsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    inner class PodiumLiveFriendsViewHolder(private val binding: ItemPodiumListFriendsBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumFriend) = with(binding) {
            binding.apply {
                liveFriends = item

                item.podiumKind?.let { kind ->
                    kindChip.text = kind.displayText(this.root.context)
                }

                actionUser.setOnClickListener {
                    listener.onUserClick(item,it)
                }

                binding.root.setOnClickListener {
                    listener.onItemClicked(item)
                }
            }

        }
    }

    object TransactionsDiff : DiffUtil.ItemCallback<PodiumFriend>() {
        override fun areItemsTheSame(oldItem: PodiumFriend, newItem: PodiumFriend) = oldItem.userId == newItem.userId

        override fun areContentsTheSame(oldItem: PodiumFriend, newItem: PodiumFriend) = oldItem == newItem
    }
}