package com.app.messej.ui.enforcements

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.api.profile.UserEnforcements
import com.app.messej.data.model.enums.PayFineType
import com.app.messej.databinding.FragmentBlackListedBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BlackListedFragment : Fragment() {

    private val args: BlackListedFragmentArgs by navArgs()
    private lateinit var binding: FragmentBlackListedBinding
    private val viewModel: EnforcementsViewModel by activityViewModels()
    private val blackListViewModel: BlackListViewModel by viewModels()
    private val commonHomeViewModel: CommonHomeViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_black_listed, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = blackListViewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        setView()
        handleOnBackPressed()
    }

    private fun setView() {
        binding.textTitle.text = args.payFineType.setTitle()
        binding.textViewDescription.text = args.payFineType.setDescription()
        setButtonTextAndAction()
    }

    private fun handleOnBackPressed() {
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Back action blocked here
            }
        })
    }

    private fun observe() {

        if (args.payFineType == PayFineType.LEGAL_PAY_FINE) {
            viewModel.enforcementsLiveData.observe(viewLifecycleOwner) {
                if (it?.enforcementsStatus?.blacklisted != true) findNavController().navigateUp()
            }
        }

        if (args.payFineType == PayFineType.SOCIAL_PAY_FINE) {
            blackListViewModel.isSocialFinePaid.observe(viewLifecycleOwner) {
                if (it) {
                    commonHomeViewModel.isSocialFineDetected(isFined = false)
                    findNavController().navigateUp()
                }
            }
        }
    }

    private fun PayFineType.setTitle(): String = when(this) {
        PayFineType.SOCIAL_PAY_FINE -> getString(R.string.common_you_are_suspended)
        PayFineType.LEGAL_PAY_FINE -> getString(R.string.legal_affairs_user_black_listed_title)
    }

    private fun PayFineType.setDescription(): String = when(this) {
        PayFineType.SOCIAL_PAY_FINE -> getString(R.string.social_upgrade_support_violation_message, viewModel.user.activePoints.formatDecimalWithRemoveTrailingZeros())
        PayFineType.LEGAL_PAY_FINE -> getString(R.string.legal_affairs_user_black_listed_description)
    }

    private fun setButtonTextAndAction() {
        val isLowFlixBalance = (viewModel.user.activePoints ?: 0.0) < 36.00
        binding.btnAction.text = when (args.payFineType) {
            PayFineType.SOCIAL_PAY_FINE -> getString(if (isLowFlixBalance) R.string.recharge else R.string.common_pay_fine)
            PayFineType.LEGAL_PAY_FINE -> getString(R.string.common_pay_fine)
        }
        binding.btnAction.setOnClickListener {
            when (args.payFineType) {
                PayFineType.SOCIAL_PAY_FINE -> {
                    if (isLowFlixBalance) {
                        findNavController().navigateSafe(direction = BlackListedFragmentDirections.actionGlobalBuyflaxFragment())
                    } else {
                        blackListViewModel.paySocialFine()
                    }
                }
                PayFineType.LEGAL_PAY_FINE -> {
                    findNavController().navigateSafe(
                        BlackListedFragmentDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_BLACKLISTED)
                    )
                }
            }
        }
    }

}