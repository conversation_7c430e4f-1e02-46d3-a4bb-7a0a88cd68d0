package com.app.messej.ui.home.privatetab.messages

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository

class PrivateMessagePendingListViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val huddleRepo = HuddlesRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val user: CurrentUser get() = accountRepo.user

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val _chatList = huddleRepo.getPrivateChatsPendingPager().liveData.cachedIn(viewModelScope)

    val chatList:MediatorLiveData<PagingData<PrivateMessagesAdapter.PrivateChatUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<PrivateMessagesAdapter.PrivateChatUIModel>?>(null)
        fun update() {
            val list: PagingData<PrivateMessagesAdapter.PrivateChatUIModel>? = _chatList.value?.map { chat ->
                _nickNames.value?.findNickName(chat.receiver)?.let {
                    chat.receiverDetails.name = it
                }
                PrivateMessagesAdapter.PrivateChatUIModel.ChatUIModel(chat)
            }
            med.postValue(list)
        }
        med.addSource(_nickNames) { update() }
        med.addSource(_chatList) { update() }
        med
    }
}