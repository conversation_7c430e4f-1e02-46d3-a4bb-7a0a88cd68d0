package com.app.messej.ui.customviews

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView
import com.app.messej.R
import com.app.messej.ui.utils.MentionTokenizer

class MentionAutoCompleteTextView: AppCompatMultiAutoCompleteTextView {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int = R.style.Widget_Flashat_GreyTextInput_AutoComplete) : super(context, attrs, defStyleAttr)

    override fun enoughToFilter(): <PERSON><PERSON><PERSON> {
        var isAtStart = selectionEnd>0 && text[selectionEnd-1] == MentionTokenizer.TOKEN_START_CHAR
        if(selectionEnd>1 && text[selectionEnd-2] != ' ') {
            isAtStart = false
        }
        return super.enoughToFilter() || isAtStart
    }
}