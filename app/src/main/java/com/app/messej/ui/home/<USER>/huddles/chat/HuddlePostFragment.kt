package com.app.messej.ui.home.publictab.huddles.chat

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.text.method.LinkMovementMethod
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.os.BundleCompat
import androidx.core.view.isVisible
import androidx.core.widget.doOnTextChanged
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.media3.exoplayer.ExoPlayer
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.api.huddles.HuddleMentionableUsersResponse
import com.app.messej.data.model.entity.Sticker
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.FragmentHuddleChatPostSheetBinding
import com.app.messej.ui.chat.adapter.ChatMessageViewHolder
import com.app.messej.ui.home.publictab.huddles.chat.PublishInFlashConfirmationFragment.Companion.POST_TO_FLASH_REQUEST_KEY
import com.app.messej.ui.home.publictab.huddles.chat.PublishInFlashConfirmationFragment.Companion.POST_TO_FLASH_RESULT_KEY
import com.app.messej.ui.home.sticker.StickerFragment
import com.app.messej.ui.utils.BottomSheetExtensions.setMatchParent
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.FragmentShareExtensions.getExternalShareResultInHuddlePost
import com.app.messej.ui.utils.MentionTokenizer
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkAudioPermission
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.app.messej.ui.utils.TextFormatUtils
import com.app.messej.ui.utils.TextFormatUtils.enableTextFormatting
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.yalantis.ucrop.UCrop
import com.yalantis.ucrop.UCropActivity
import kotlinx.coroutines.launch


class HuddlePostFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentHuddleChatPostSheetBinding

    val viewModel: HuddleChatViewModel by navGraphViewModels(R.id.nav_chat_huddle)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL,R.style.Widget_Flashat_HuddlePostBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_chat_post_sheet, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        setMatchParent()
    }

    override fun onResume() {
        super.onResume()
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                viewModel.cancelPostSheet()
            }
        })
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if(viewModel.isImageEditMode.value != true){
           viewModel.cancelPostSheet()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setupAdapter(participants: List<HuddleMentionableUsersResponse.MentionableUser>) {
        val mentionableUsersAdapter = MentionableUsersAdapter(requireContext(), participants)
        if(viewModel.chatTextInput.value?.isNotEmpty() == true) {
            binding.inputEditText.apply {
                setText(TextFormatUtils.highlightMentionsForEdit(requireContext(), text.toString(), participants))
            }
        }

        binding.inputEditText.apply {
            setAdapter(mentionableUsersAdapter)
            threshold = 0
            viewModel.postToEdit.value?.let {
                setText(TextFormatUtils.highlightMentionsForEdit(requireContext(),it.message.displayMessage.orEmpty(),it.message.mentionedUsers.orEmpty()))
            }
            //Create a new Tokenizer which will get text after '@' and terminate on ' '
            setTokenizer(MentionTokenizer())

            // Setting drop down below "@"
            doOnTextChanged { text, start, before, count ->
                val layout = layout
                val pos: Int = selectionStart
                val line: Int = layout.getLineForOffset(pos)
                val baseline: Int = layout.getLineBaseline(line)
                val bottom: Int = height

                dropDownVerticalOffset = baseline - bottom
            }
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isFitToContents = false
                state = BottomSheetBehavior.STATE_EXPANDED
                isDraggable = false
                isCancelable=false
            }
        }
        return dialog
    }

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    private fun observe() {
        viewModel.onTriggerCrop.observe(viewLifecycleOwner) {
            val options = UCrop.Options().apply {
                val color = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                setToolbarColor(color)
                setStatusBarColor(color)
                setToolbarTitle(resources.getString(R.string.common_crop))
                setFreeStyleCropEnabled(true)
                setAllowedGestures(UCropActivity.SCALE, UCropActivity.ROTATE, UCropActivity.ALL)
            }
            val crop = UCrop.of(it.first, it.second).withOptions(options)
            imageCropResult.launch(crop.getIntent(requireContext()))
        }

        viewModel.onTriggerImageEdit.observe(viewLifecycleOwner) {
            findNavController().navigateSafe(HuddlePostFragmentDirections.actionGlobalPostImageEdit(it.first, it.second))
        }

        viewModel.chatMediaImage.observe(viewLifecycleOwner) { med ->
            med ?: return@observe
            binding.chatImage.apply {
                med.resolution?.let { res ->
                    val ratio = res.width.toFloat() / res.height.toFloat()
                    (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = ratio.coerceIn(ChatMessageViewHolder.IMAGE_ASPECT_MIN, ChatMessageViewHolder.IMAGE_ASPECT_MAX).toString()
                }
            }
        }

        viewModel.chatMediaVideo.observe(viewLifecycleOwner) { med ->
            med ?: return@observe
            med.resolution?.let { res ->
                val ratio = res.width.toFloat() / res.height.toFloat()
                binding.videoHolder.apply {
                    (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = ratio.coerceIn(ChatMessageViewHolder.IMAGE_ASPECT_MIN, ChatMessageViewHolder.IMAGE_ASPECT_MAX).toString()
                }
            }
        }

        viewModel.chatMediaVideo.observe(viewLifecycleOwner) { med ->
            if ((med is TempMedia.SavedMedia && med.hasOfflineMedia) || med is TempMedia) {
                setupPlayer(med)
            } else {
                releasePlayer()
            }
        }

        viewModel.nowPlaying.observe(viewLifecycleOwner) {
            binding.info = if (it?.listPosition == MediaPlayerInfo.LIST_POS_RECORDING) it else null
            Log.d("HPF", "observe: setting preview: $it")
        }

        viewModel.onMessageCreated.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }

        viewModel.chatReplyTo.observe(viewLifecycleOwner) { reply ->
            reply ?: return@observe
            val isSelfMessage = (reply.senderId == viewModel.user.id)
            binding.replyLayout.apply {
                senderName = if (isSelfMessage) getString(R.string.common_you) else (reply.senderName)
                cancellable = false
                relation = when (reply.senderRelation) {
                    FollowerType.DEAR -> getString(R.string.profile_dear)
                    FollowerType.FAN -> getString(R.string.profile_fan)
                    FollowerType.LIKER -> getString(R.string.profile_liker)
                    else -> null
                }
                if (reply.hasSticker) {
                    (replyLayout.layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = "1"
                }

                chatMessage.apply {
                    if(reply.mediaType == MediaType.AUDIO) {
                        text = chatMessage.context.getString(R.string.message_list_preview_audio, reply.mediaDuration)
                    }
                    else {
                        setText(HuddleMessageViewHolder.formatAndHighlight(root.context,reply,viewModel.user.id)?:"", TextView.BufferType.SPANNABLE)
                        movementMethod = LinkMovementMethod.getInstance()
                    }
                }

                Log.d("HMVH", "setChatBubbleColor: calling")
                HuddleMessageViewHolder.setChatBubbleColor(chatBubble, reply.premium, isSelfMessage)
            }
        }

        viewModel.audioRecording.observe(viewLifecycleOwner) {
            if (it) {
                activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            } else {
                activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
        }

        viewModel.onAudioRecordingLimitReached.observe(viewLifecycleOwner) {
            if (it) binding.postMotionLayout.apply {
                setTransition(R.id.record_to_playback)
                transitionToEnd()
            }
        }

        viewModel.messageSending.observe(viewLifecycleOwner) {
            if (it) {
                Log.d("ENCODE", "observe: FLAG_KEEP_SCREEN_ON enabled")
                activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                binding.playerView.apply {
                    hideController()
                    useController = false
                }
                player?.pause()
            } else {
                activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                binding.playerView.apply {
                    useController = true
                }
                Log.d("ENCODE", "observe: FLAG_KEEP_SCREEN_ON cleared")
            }
        }
        viewModel.chatMediaVideoProgress.observe(viewLifecycleOwner) { progress ->
            binding.encodeProgress.progress = progress
            if (progress == null || progress < 0) {
                binding.encodeProgress.text = resources.getString(R.string.chat_video_encoding)
            } else {
                binding.encodeProgress.text = resources.getString(R.string.chat_video_encoding_progress, progress)
            }
        }

        viewModel.mediaTransfer.observe(viewLifecycleOwner) { transfer ->
            val progress = transfer?.progress
            binding.uploadProgress.progress = progress
            if (progress == null || progress < 0) {
                binding.uploadProgress.text = resources.getString(R.string.chat_video_uploading)
            } else {
                binding.uploadProgress.text = resources.getString(R.string.chat_video_uploading_progress, progress)
            }
        }

        viewModel.chatMediaAudio.observe(viewLifecycleOwner) {
            if (it is TempMedia.SavedMedia) {
                binding.postMotionLayout.apply {
                    setTransition(R.id.text_and_media_to_playback)
                    transitionToEnd()
                }
            }
        }

        viewModel.mentionableUsers.observe(viewLifecycleOwner) {
            setupAdapter(it)
        }

        viewModel.onFailedToPostMessage.observe(viewLifecycleOwner) {
            if (it == HuddleChatViewModel.MessagePostErrorType.NETWORK) {
                Toast.makeText(requireContext(), R.string.default_no_networks_error_message, Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), R.string.default_unknown_error_message, Toast.LENGTH_SHORT).show()
            }
        }

        setFragmentResultListener(StickerFragment.EMOJI_SELECT_RESULT_KEY) { _, bundle ->
            val sticker = BundleCompat.getParcelable(bundle, StickerFragment.EMOJI_RESPONSE, Sticker::class.java)?: return@setFragmentResultListener
            viewModel.addSticker(sticker)
            findNavController().popBackStack()
        }

        viewModel.showPostToFlashConfirmation.observe(viewLifecycleOwner) {
            if (it){
                if (viewModel.user.isFlashBlocked == true){
                    viewModel.prepareAndSendMessage()
                }
                else{
                    findNavController().navigateSafe(HuddlePostFragmentDirections.actionHuddlePostFragmentToHuddleToFlashConfirmFragment())
                }
            }
        }

        setFragmentResultListener(POST_TO_FLASH_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(POST_TO_FLASH_RESULT_KEY)
            viewModel.postVideoToFlash(result)
            viewModel.prepareAndSendMessage()
        }
    }

    private fun setup() {
        binding.closeButton.setOnClickListener {
            viewModel.cancelPostSheet()
            hideKeyboard()
            findNavController().popBackStack()
        }

        if (viewModel.enableTextFormatting) {
            binding.chatInput.enableTextFormatting(requireActivity(),viewModel, true)
            try {
                val endIconView = binding.chatInput.findViewById<View>(com.google.android.material.R.id.text_input_end_icon)
                endIconView.layoutParams = (endIconView.layoutParams as FrameLayout.LayoutParams).apply {
                    gravity = Gravity.BOTTOM
                }
            } catch(_: Exception) {}
        }

        binding.attachCamera.setOnClickListener {
            takeImage()
        }
        binding.attachCameraVideo.setOnClickListener {
            takeVideo()
        }
        binding.attachGallery.setOnClickListener {
            selectMediaFromGallery()
        }
        binding.cropButton.setOnClickListener {
            viewModel.cropAttachedMedia()
        }

        binding.imageEditButton.setOnClickListener {
            hideKeyboard()
            viewModel.huddleEditImage()
        }

        binding.mediaRemoveButton.setOnClickListener {
            viewModel.clearChatMedia()
        }
        binding.attachVoice.setOnClickListener {
            checkAudioPermission(binding.root) {
                binding.postMotionLayout.apply {
                    setTransition(R.id.text_and_media,R.id.recording)
                    transitionToEnd()
                }
                viewModel.clearChatTextAndMedia()
                viewModel.startAudioRecording()
            }
        }
        binding.attachVoiceControl.setOnClickListener {
            binding.postMotionLayout.apply {
                setTransition(R.id.record_to_playback)
                transitionToEnd()
            }
            viewModel.finishAudioRecording()
        }

        binding.audioPreviewPlayButton.setOnClickListener {
            viewModel.playPostAudio()
        }
        binding.audioPreviewDeleteButton.setOnClickListener {
            binding.postMotionLayout.apply {
                setTransition(R.id.playback_to_text)
                transitionToEnd()
            }
            viewModel.clearChatMedia()

        }

        binding.postButton.setOnClickListener {
            releasePlayer()
            hideKeyboard()
            viewModel.checkAndSendMessage()
//            viewModel.prepareAndSendMessage()
        }

        binding.attachEmoji.setOnClickListener{
            findNavController().navigateSafe(HuddlePostFragmentDirections.actionHuddlePostFragmentToStickerFragment())
        }

        binding.replyLayout.replyClearButton.setOnClickListener {
            viewModel.clearReplyTo()
        }

        getExternalShareResultInHuddlePost { message, mediaType, fileUri ->
            viewModel.handleExternalShare(message = message, mediaType = mediaType, fileUri = fileUri)
        }
    }

    override fun onPause() {
        super.onPause()
        if(activity?.isChangingConfigurations != true) {
            viewModel.releaseMediaAssets()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    private var player: ExoPlayer? = null

    private fun releasePlayer() {
        player?.apply {
            stop()
            release()
            player = null
        }
    }

    private fun setupPlayer(med: TempMedia) {
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build().apply {
                binding.playerView.player = this
                binding.playerView.findViewById<View>(androidx.media3.ui.R.id.exo_settings).isVisible = false
                // Prepare the player.
            }
        }

        player?.apply {
            clearMediaItems()
            setMediaItem(med.mediaItem)
            prepare()
//            play()
            playWhenReady = true
        }
    }

    private val imageCropResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let {
                val resultUri = UCrop.getOutput(it)?: return@let
                viewModel.addImage(resultUri)
            }
        }
    }

    private val takeImageResult =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { isSuccess ->
            if (isSuccess) {
                viewModel.addCapturedImage()
            }
        }

    private fun takeImage() {
        checkCameraPermission(binding.root) {
            lifecycleScope.launch {
                val uri = viewModel.getImageUriForCapture()
                takeImageResult.launch(uri)
            }
        }
    }

//    private val videoFromGalleryTrimResultLauncher = registerForActivityResult(StartActivityForResult()) { result: ActivityResult ->
//        if (result.resultCode == Activity.RESULT_OK) {
//            result.data?.let {
//                val uri = it.getStringExtra(TrimVideo.TRIM_VIDEO_URI)
//                Log.i("eeeeeeee","eeeeeeee"+Uri.parse(uri))
////                viewModel.videoEditInfo = VideoEditInfo(it.getLongExtra(TrimVideo.TRIMMED_VIDEO_START_POS, 0),
////                                                        it.getLongExtra(TrimVideo.TRIMMED_VIDEO_END_POS,0))
////                setupPlayer(MediaItem.fromUri(uri!!), uri, TimeUnit.SECONDS.toMillis(startpos), TimeUnit.SECONDS.toMillis(endPos))
//                viewModel.addMedia(Uri.parse(uri))
//            }
//        }
//    }

    private val selectImageOrVideoFromGalleryResult =
        registerForActivityResult(ActivityResultContracts.OpenDocument()) { uri: Uri? ->
//        registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri: Uri? ->
            uri?.let {
//                TrimVideo.activity(it.toString(), viewModel.videoDurationLimit)?.start(activity, videoFromGalleryTrimResultLauncher)
                viewModel.addMedia(uri)
            }
        }

    private fun selectMediaFromGallery() {
//        val request = if(viewModel.user.premium) ActivityResultContracts.PickVisualMedia.ImageAndVideo else ActivityResultContracts.PickVisualMedia.ImageOnly
//        selectImageOrVideoFromGalleryResult.launch(PickVisualMediaRequest(request))
        val request = if(viewModel.user.premium) arrayOf("image/*", "video/*") else arrayOf("image/*")
        selectImageOrVideoFromGalleryResult.launch(request)
    }


//    private val capturedVideoTrimResultLauncher = registerForActivityResult(StartActivityForResult()) { result: ActivityResult ->
//        if (result.resultCode == Activity.RESULT_OK) {
//            result.data?.let {
////                viewModel.videoEditInfo = VideoEditInfo(it.getLongExtra(TrimVideo.TRIMMED_VIDEO_START_POS, 0),
////                                                        it.getLongExtra(TrimVideo.TRIMMED_VIDEO_END_POS,0))
//                viewModel.addCapturedVideo()
//            }
//        }
//    }

    private val takeVideoResult =
        registerForActivityResult(object: ActivityResultContracts.CaptureVideo() {
            override fun createIntent(context: Context, input: Uri): Intent {
                val intent = super.createIntent(context, input)
                Log.d("CVIDEO", "createIntent: ${viewModel.videoDurationLimit}")
                viewModel.videoDurationLimit.let {
                    intent.putExtra(MediaStore.EXTRA_DURATION_LIMIT, it) // Duration in Seconds
                }
//                intent.putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 0); // Quality Low
//                intent.putExtra(MediaStore.EXTRA_SIZE_LIMIT, 5491520L); // 5MB
                return intent
            }
        }) { isSuccess ->
            if (isSuccess)  viewModel.addCapturedVideo()
        }
    private fun takeVideo() {
        checkCameraPermission(binding.root) {
            lifecycleScope.launch {
                val uri = viewModel.getVideoUriForCapture()
                takeVideoResult.launch(uri)
            }
        }
    }
}