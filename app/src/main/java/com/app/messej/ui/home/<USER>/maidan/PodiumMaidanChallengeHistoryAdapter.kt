package com.app.messej.ui.home.publictab.maidan

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import com.app.messej.databinding.ItemMaidanChallengeHistoryBinding

class PodiumMaidanChallengeHistoryAdapter : PagingDataAdapter<PodiumMaidanSupporter, PodiumMaidanChallengeHistoryAdapter.PodiumMaidanChallengeHistoryViewHolder>(PodiumMaidanChallengeHistoryDiff) {

    inner class PodiumMaidanChallengeHistoryViewHolder(private val binding: ItemMaidanChallengeHistoryBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumMaidanSupporter) = with(binding) {
            speaker = item
        }
    }

    object PodiumMaidanChallengeHistoryDiff : DiffUtil.ItemCallback<PodiumMaidanSupporter>() {

        override fun areItemsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: PodiumMaidanChallengeHistoryViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumMaidanChallengeHistoryViewHolder {
        val binding = ItemMaidanChallengeHistoryBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumMaidanChallengeHistoryViewHolder(binding)
    }
}
