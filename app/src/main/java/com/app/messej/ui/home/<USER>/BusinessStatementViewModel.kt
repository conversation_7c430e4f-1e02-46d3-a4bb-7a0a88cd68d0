package com.app.messej.ui.home.businesstab

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BusinessStatementViewModel(application: Application) : AndroidViewModel(application) {

    private var businessRepo: BusinessRepository = BusinessRepository(application)
    private val accountRepo = AccountRepository(application)

    private val _isPayoutStatus = MutableLiveData<Boolean?>(false)
    val isPayoutStatus: LiveData<Boolean?> = _isPayoutStatus

    private val _payOutClosed = MutableLiveData(false)
    val payOutStatusPopupClosed: LiveData<Boolean> = _payOutClosed

    val user = accountRepo.user


    val isVisitor: Boolean
    get() = accountRepo.user.citizenship == UserCitizenship.VISITOR

    val businessStatement: LiveData<BusinessStatement?> = businessRepo.getStatements()
    private val _statementLoading = MutableLiveData<Boolean>(false)
    val statementLoading: LiveData<Boolean> = _statementLoading

    init {
        loadBusinessStatement()
    }

    private fun loadBusinessStatement() {
        _statementLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<BusinessStatement> = businessRepo.getBusinessStatements()) {
                is ResultOf.Success -> {
                    _statementLoading.postValue(false)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {

                }
            }
            _statementLoading.postValue(false)
        }
    }


    fun closePayoutStatus() {
        _payOutClosed.postValue(true)
    }



}