package com.app.messej.ui.home.publictab.socialAffairs.activeCases

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.messej.R
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.api.socialAffairs.SocialCasesListResponse.Companion.dummySocialCase
import com.app.messej.data.model.enums.SocialActiveCaseMainTab
import com.app.messej.data.model.enums.SocialCaseFilter
import com.app.messej.data.model.enums.SocialCaseStatus
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.setText
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.setTextColor
import com.app.messej.ui.home.publictab.socialAffairs.components.SocialSmallRoundedActionButton
import com.app.messej.ui.home.publictab.socialAffairs.components.SocialUserAndDonationView

@Composable
fun ActiveCaseTabView(
    viewModel: SocialActiveCasesViewModel
) {
    val selectedTab by viewModel.selectedMainTab.observeAsState(initial = SocialActiveCaseMainTab.Donate)
    val tabList = viewModel.activeTabList
    val casesCount by viewModel.casesCount.observeAsState()

    TabRow(
        selectedTabIndex = selectedTab.ordinal, divider = {},
        containerColor = colorResource(id = R.color.colorSocialSurfaceSecondary),
        indicator = { tabPositionList ->
            //Used for animating the item on tab click
            //It will slide on top of the tabs.
            // It will only contain the current selected tab
            val tabPosition = tabPositionList[selectedTab.ordinal]
            ActiveTabItem(
                modifier = Modifier
                    .tabIndicatorOffset(currentTabPosition = tabPosition)
                    .background(color = colorResource(id = R.color.colorSocialLightGreen)),
                tab = selectedTab,
                isSelected = true,
                count = selectedTab.setCaseCount(count = casesCount)
            )
        },
    ) {
        // The actual tab will be shown here.
        tabList.forEach { tab ->
            val isSelected = selectedTab == tab
            Tab(
                selected = isSelected,
                onClick = { viewModel.setActiveCaseTab(tab = tab) }
            ) {
                ActiveTabItem(
                    tab = tab,
                    isSelected = isSelected,
                    count = tab.setCaseCount(count = casesCount)
                )
            }
        }
    }
}

private fun SocialActiveCaseMainTab.setCaseCount(count: Pair<Int?, Int?>?) : Int {
    return (if (this == SocialActiveCaseMainTab.Donate) count?.first
    else count?.second) ?: 0
}

@Composable
private fun ActiveTabItem(
    modifier: Modifier = Modifier,
    tab: SocialActiveCaseMainTab,
    isSelected: Boolean, count: Int?
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = dimensionResource(id = R.dimen.activity_margin)),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Icon(
            modifier = Modifier.size(size = dimensionResource(id = R.dimen.extra_margin)),
            painter = painterResource(id = when(tab) {
                SocialActiveCaseMainTab.Donate -> if (isSelected) R.drawable.ic_donate_selected else R.drawable.ic_donate_unselected
                SocialActiveCaseMainTab.NewCases -> if (isSelected) R.drawable.ic_new_case_selected else R.drawable.ic_new_case_unselected
            }),
            tint = Color.Unspecified,
            contentDescription = null
        )
        CustomHorizontalSpacer(
            space = dimensionResource(id = R.dimen.element_spacing)
        )
        Text(
            text = stringResource(id = tab.setText()) + " (${count ?: 0})",
            style = FlashatComposeTypography.captionBold,
            color = colorResource(id = if (isSelected) R.color.textColorAlwaysLightPrimary else R.color.textColorAlwaysDarkSecondaryLight)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ActiveTabItemPreview() {
    ActiveTabItem(
        tab = SocialActiveCaseMainTab.Donate,
        isSelected = true,
        count = 10
    )
}

@Composable
fun NewCasesFilterAlertView(
    caseFilterList: List<SocialCaseFilter>?,
    isExpanded: Boolean,
    onClick: (SocialCaseFilter) -> Unit,
    onDismiss : () -> Unit
) {
    DropdownMenu(
        containerColor = colorResource(id = R.color.colorSurfaceSecondaryDark),
        expanded = isExpanded,
        onDismissRequest = onDismiss
    ) {
        caseFilterList?.forEach { item ->
            DropdownMenuItem(
                text = {
                    Text(
                        text = stringResource(id = item.setText()),
                        style = FlashatComposeTypography.defaultType.body2,
                        color = colorResource(id = R.color.textColorPrimary)
                    )
                },
                onClick = {
                    onDismiss()
                    onClick(item)
                }
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun NewCasesFilterAlertViewPreview() {
    NewCasesFilterAlertView(
        isExpanded = true,
        onClick = {},
        onDismiss = {},
        caseFilterList = SocialCaseFilter.entries.toList()
    )
}

@Composable
fun SocialSingleListItemView(
    case: SocialCaseInfo,
    isStatusAndActionButtonsHidden: Boolean = false,
    onVote: (SocialVoteAction, Int?) -> Unit,
    onDonate: () -> Unit,
    onUserDpClick: (Int) -> Unit,
    onClick: () -> Unit
) {
    val isDonateView = case.isDonateView

    Row(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
            .clickable { onClick() }
            .background(color = colorResource(id = R.color.colorSocialSurfaceSecondary))
            .padding(horizontal = dimensionResource(id = R.dimen.element_spacing), vertical = 12.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        SocialUserAndDonationView(
            modifier = Modifier.fillMaxWidth(fraction = 0.5F),
            case = case,
            onUserDpClick = onUserDpClick
        )

        //Removing status, donate and voting buttons in below case
        if (isStatusAndActionButtonsHidden) return@Row
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.End,
            verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.line_spacing))
        ) {
            //when Case status is CLOSED, PENDING_APPROVAL. Show that status
            //Other wise show the title of the case
            if (case.needToBeReplaceCaseTitleWithStatus) {
                SocialCaseStatusText(status = case.status, isBorderVisible = true)
            } else {
                SocialItemStatusText(text = case.caseTitle)
            }

            //Removing the Donate and voting buttons in below case
            if (!case.isCaseActionButtonVisibleInActiveCaseList) return@Column

            //Donate Button
            if (isDonateView) {
                SocialSmallRoundedActionButton(
                    modifier = Modifier,
                    isSupport = true,
                    buttonType = case.donateButtonType,
                    text = stringResource(id = R.string.social_affair_donate),
                    onClick = onDonate
                )
                return@Column
            }

            Row(modifier = Modifier
                .padding(start = dimensionResource(id = R.dimen.line_spacing))
                .fillMaxWidth()
            ) {
                SocialSmallRoundedActionButton(
                    modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                    isSupport = false,
                    buttonType = case.voteButtonType,
                    text = "${case.opposedVotes ?: 0}",
                    icon = R.drawable.ic_dislike,
                    onClick = { onVote(SocialVoteAction.Oppose, case.id) }
                )
                CustomHorizontalSpacer(
                    space = dimensionResource(id = R.dimen.line_spacing)
                )
                SocialSmallRoundedActionButton(
                    modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                    isSupport = true,
                    buttonType = case.voteButtonType,
                    text = "${case.supportVotes ?: 0}",
                    icon = R.drawable.ic_chat_liked,
                    onClick = { onVote(SocialVoteAction.Support, case.id) }
                )
            }
        }
    }
}

@Preview
@Composable
private fun SocialSingleItemPreview() {
    SocialSingleListItemView(
        case = dummySocialCase,
        onVote = { _, _ ->},
        onDonate = {},
        onUserDpClick = {},
        onClick = {}
    )
}

@Composable
fun SocialItemStatusText(
    modifier: Modifier = Modifier,
    text: String?,
    textColor: Color = colorResource(id = R.color.textColorPrimary),
    isBorderVisible: Boolean = false
) {
    val shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing))
    val backgroundColor = colorResource(id = R.color.colorSocialSurfaceSecondaryLight)
    Text(
        text = text ?: "",
        modifier = modifier
            .clip(shape = shape)
            .background(color = backgroundColor)
            .border(width = 1.dp, color = if (isBorderVisible) textColor else backgroundColor, shape = shape)
            .padding(all = dimensionResource(id = R.dimen.line_spacing)),
        style = FlashatComposeTypography.overLineSmallerItalic.copy(fontSize = 11.sp),
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        color = textColor
    )
}

@Composable
fun SocialCaseStatusText(
    modifier: Modifier = Modifier,
    status: SocialCaseStatus?,
    isBorderVisible: Boolean = false
) {
    val context = LocalContext.current
    status?.setText(context = context)?.let {
        SocialItemStatusText(
            modifier = modifier,
            text = it,
            isBorderVisible = isBorderVisible,
            textColor = colorResource(id = status.setTextColor())
        )
    }
}
