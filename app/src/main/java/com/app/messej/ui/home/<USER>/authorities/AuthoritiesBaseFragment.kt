package com.app.messej.ui.home.publictab.authorities

import android.content.DialogInterface
import android.os.Bundle
import android.view.View
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.databinding.FragmentBaseAuthoritiesBinding
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.setupPayFineIcon
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder

abstract class AuthoritiesBaseFragment : Fragment() {

    protected abstract var binding: FragmentBaseAuthoritiesBinding
    private val viewModel: AuthoritiesViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        setupPromoBoard(binding.promoBar)
        setupPayFineIcon(composeView = binding.payFine)

        binding.presidentialAffairs.setOnClickListener {
//            onPresidentialAffairsClick()
            showFeatureUnderDevelopmentAlertDialog(message = R.string.authorities_presidential_affairs_under_development_message)
        }
        binding.legalAffairs.setOnClickListener {
            onLegalAffairsClick()
        }
        binding.socialWelfare.setOnClickListener {
            if (viewModel.user.citizenship.isFreeType) {
                showSocialWelfareFreeUserAlert()
                return@setOnClickListener
            }
            onSocialWelfareClick()
        }
        binding.stateAffairs.setOnClickListener {
            onStateAffairsClick()
//            showFeatureUnderDevelopmentAlertDialog(message = R.string.authorities_state_affairs_under_development_message)
        }
        binding.gradientArrayId = R.array.authorities_gradient_colors
    }

    abstract fun onPresidentialAffairsClick()
    abstract fun onLegalAffairsClick()
    abstract fun onSocialWelfareClick()
    abstract fun onStateAffairsClick()

    // Remove this after all other authority boards completes.
    abstract fun onNavigateToLegalAffairsClick()
    private fun showFeatureUnderDevelopmentAlertDialog(@StringRes message: Int) {
        val alertDialog = MaterialAlertDialogBuilder(requireContext())
            .setMessage(getString(message))
            .setPositiveButton(getString(R.string.authorities_continue_to_legal_affairs)) { _, _ ->
                onNavigateToLegalAffairsClick()
            }
            .setNegativeButton(getString(R.string.common_close)) { dialog, _ -> dialog.dismiss() }
            .create()
        alertDialog.show()

        val negativeButton = alertDialog.getButton(DialogInterface.BUTTON_NEGATIVE)
        negativeButton.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorError))
    }

    private fun showSocialWelfareFreeUserAlert() {
        showFlashatDialog {
            setMessage(message = R.string.social_free_user_permission_alert)
            setCloseButtonText(text = R.string.common_cancel)
            setConfirmButton(title = R.string.common_upgrade, icon = R.drawable.ic_promo_upgrade, tint = true) {
                findNavController().navigateSafe(direction = NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
                true
            }
        }
    }
}