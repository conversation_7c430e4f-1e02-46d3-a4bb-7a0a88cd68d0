package com.app.messej.ui.home.publictab.postat.create

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.OptIn
import androidx.core.view.isVisible
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.ItemFeedPostatMediaBinding
import com.google.android.material.button.MaterialButton

class FeedPostatViewerViewPagerAdapter(private val listener: PlayerActionListener, private var mediaList: MutableList<PostatMedia>) :
    RecyclerView.Adapter<FeedPostatViewerViewPagerAdapter.PostatMediaPagerViewHolder>() {

    interface PlayerActionListener {
        fun registerForFuturePlayback(obj: FuturePlaybackObject)

        fun detachFuturePlayback(pos: Int)
    }

    data class FuturePlaybackObject(
        val pos: Int,
        val postat: PostatMedia,
        val onPlay: (Player) -> Unit,
        val onStop: () -> Unit
    )

    inner class PostatMediaPagerViewHolder(val binding: ItemFeedPostatMediaBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun setData(media: PostatMedia) {
            binding.media = media
        }

        @OptIn(UnstableApi::class)
        fun bindPlayer(): FuturePlaybackObject? = with(binding) {
            media?.let {
                if (it.mediaType == MediaType.IMAGE) return@with null
                Log.w("PostatPlayer", "preparePlayerForSetup: ${it.media}")
                val fpo = FuturePlaybackObject(bindingAdapterPosition, it, { player ->
                    Log.w("PostatPlayer", "binding player: ${it.s3Key}")
                    binding.imageView.isVisible = false
                    player.apply {
                        binding.playerView.player = this
                        val playButton = binding.playerView.findViewById<MaterialButton>(R.id.exo_play_pause_custom)
                        playButton.setOnClickListener {
                            if (isPlaying) {
                                pause()
                            } else {
                                seekTo(0)
                                play()
                            }
                        }
                        addListener(object : Player.Listener {
                            override fun onIsPlayingChanged(isPlaying: Boolean) {
                                Log.w("FREF", "onIsPlayingChanged: $isPlaying")
                                if (isPlaying) {
                                    playButton.setIconResource(R.drawable.ic_media_stop_large)
                                } else {
                                    playButton.setIconResource(R.drawable.ic_media_play_large)
                                }
                            }
                        })
                    }
                }, {
                    binding.imageView.isVisible = true
                    cleanup()
                })
                listener.registerForFuturePlayback(fpo)
                return@with fpo
            }
        }

        fun cleanup() = with(binding) {
            playerView.player = null
            listener.detachFuturePlayback(bindingAdapterPosition)
        }
    }
    override fun getItemCount(): Int = mediaList.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PostatMediaPagerViewHolder {

        val binding = ItemFeedPostatMediaBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )

        return PostatMediaPagerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: PostatMediaPagerViewHolder, position: Int) {
        holder.setData(mediaList[position])
    }

    override fun onViewDetachedFromWindow(holder: PostatMediaPagerViewHolder) {
        super.onViewDetachedFromWindow(holder)
        Log.w("PostatPlayer", "onViewDetachedFromWindow: $holder")
        holder.cleanup()
    }

    override fun onViewAttachedToWindow(holder: PostatMediaPagerViewHolder) {
        super.onViewAttachedToWindow(holder)
        Log.w("PostatPlayer", "onViewAttachedToWindow: $holder")
        holder.bindPlayer()
    }

    fun updateData(mediaList: List<PostatMedia>) {
        this.mediaList.clear()
        this.mediaList.addAll(mediaList)
        notifyDataSetChanged()
    }

}