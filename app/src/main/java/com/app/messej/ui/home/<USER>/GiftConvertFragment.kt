package com.app.messej.ui.home.gift

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.core.graphics.drawable.toDrawable
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.GiftConversion
import com.app.messej.databinding.FragmentGiftConvertBinding
import com.app.messej.databinding.ItemGiftLoaderBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.doubleClickPrevent
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.progressindicator.LinearProgressIndicator
import com.google.android.material.textview.MaterialTextView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class GiftConvertFragment : Fragment() {

    private lateinit var binding: FragmentGiftConvertBinding
    private val giftCommonViewModel: GiftCommonViewModel by activityViewModels()
    private val viewModel: GiftConvertViewModel by viewModels()
    private val args: GiftConvertFragmentArgs by navArgs()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_convert, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = if(args.convertType == GiftConversion.COIN_TO_FLAX){
            getString(R.string.title_coins_converter)
        } else{
            getString(R.string.title_flax_converter)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setConversionMode(args.convertType)
        setUp()
        observe()
    }

    private fun setUp() {
        giftCommonViewModel.getGiftList()
        binding.textEnterValue.editText?.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                viewModel.didEnterValue.postValue(true)
            }
        }
        binding.btnConvert.setOnClickListener {
            if (doubleClickPrevent()) {
                actionConvert()
            }
        }
    }

    private fun actionConvert() {
        if (args.convertType == GiftConversion.COIN_TO_FLAX) {
            viewModel.giftConversionAction(true)
        } else {
            viewModel.giftConversionAction(false)
        }
        hideKeyboard()
        showLoader()
    }

    private fun observe() {

            giftCommonViewModel.giftListData.observe(viewLifecycleOwner) {
                viewModel.setGiftResponse(it)
            }

        viewModel.isButtonEnabled.observe(viewLifecycleOwner){
            Log.d("IS_ENABLED","${it}")
        }

        val textInputEditText = binding.textEnterValue.editText as AutoCompleteTextView
        textInputEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val value = s.toString()
                if (value.isNotEmpty()) {
                    viewModel.setValue(value)
                } else {
                    viewModel.clearText()
                }
            }

            override fun afterTextChanged(s: Editable?) {

            }
        })
        viewModel.coinFlaxError.observe(viewLifecycleOwner) {
            binding.textEnterValue.error = when (it) {
                GiftConvertViewModel.Companion.ValueError.NONE -> {
                    binding.textEnterValue.isErrorEnabled = false
                    null
                }

                GiftConvertViewModel.Companion.ValueError.COIN_MAX -> {
                    binding.textEnterValue.isErrorEnabled = true
                    getString(R.string.max_coin_convert_limit_reached)
                }

                GiftConvertViewModel.Companion.ValueError.FLAX_MAX -> {
                    binding.textEnterValue.isErrorEnabled = true
                    getString(R.string.max_flax_convert_limit_reached)
                }

                else -> {
                    binding.textEnterValue.isErrorEnabled = false
                    null
                }
            }
        }

        viewModel.successMessage.observe(viewLifecycleOwner) {
            showSuccessMessage(it)
            textInputEditText.text.clear()
        }
        viewModel.errorMessage.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }
        binding.textConversionHistory.setOnClickListener {
            findNavController().navigateSafe(GiftConvertFragmentDirections.actionCoinConvertToGiftConversionHistory())
        }

        viewModel.conversionRate.observe(viewLifecycleOwner){
        }
        viewModel.accountDetails.observe(viewLifecycleOwner){
        }

        viewModel.coinFlaxConversionValue.observe(viewLifecycleOwner) {
            if (it != null) {
                viewModel.setValue(data = if (args.convertType == GiftConversion.COIN_TO_FLAX) args.availableCoins else args.availableFlix)
                binding.PointToFlaxautoCompleteTextView.setText(if (args.convertType == GiftConversion.COIN_TO_FLAX) args.availableCoins else args.availableFlix)
            }
        }
    }

    private fun showLoader() {
        val binding = ItemGiftLoaderBinding.inflate(LayoutInflater.from(requireContext()))
        val customView = binding.root
        val progressBar: LinearProgressIndicator = binding.giftPointConvertIndicator

        val materialAlertDialog = MaterialAlertDialogBuilder(requireContext(), R.style.TransparentMaterialAlertDialog).setView(customView).setCancelable(true).create()
        materialAlertDialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        lifecycleScope.launch {
            var progress = 0
            while (true) {
                // Update the UI on the main thread
                withContext(Dispatchers.Main) {
                    progressBar.progress = progress
                }
                delay(100) // Delay for 100 milliseconds
                progress += 5
                if (progress >= 100) {
                    progress = 0
                }
            }
        }
        materialAlertDialog.show()

        viewModel.dialogDismiss.observe(viewLifecycleOwner) {
            if (it) materialAlertDialog.dismiss()
        }
    }

    private fun showSuccessMessage(convertedFlax: String) {
        val customView = LayoutInflater.from(requireContext()).inflate(R.layout.item_point_to_flax_success, null)
        val textFlax = customView.findViewById<MaterialTextView>(R.id.text_flax_amount_converted)

        textFlax.text =if(args.convertType == GiftConversion.COIN_TO_FLAX) {
            getString(R.string.gift_flax_1, convertedFlax)
        }else{
            getString(R.string.common_coins, convertedFlax)
        }
        val materialAlertDialog = MaterialAlertDialogBuilder(requireContext(), R.style.TransparentMaterialAlertDialog).setView(customView).setCancelable(true).create()
        materialAlertDialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        materialAlertDialog.show()
    }


}