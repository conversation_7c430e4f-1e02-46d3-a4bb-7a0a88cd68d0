package com.app.messej.ui.home.publictab.huddles

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage.MessageType
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.SuggestedHuddle
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.enums.GroupChatStatus
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.ItemHuddleListBinding
import com.app.messej.databinding.ItemHuddleListMyPostsBinding
import com.app.messej.databinding.ItemHuddleListMyPostsEmptyBinding
import com.app.messej.databinding.ItemHuddleListTribeBinding
import com.app.messej.databinding.LayoutHuddleLastMessagePreviewBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.publictab.huddles.PublicHuddlesAdapter.HuddleUIModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.DateFormatHelper
import com.app.messej.ui.utils.MentionTokenizer
import com.app.messej.ui.utils.TextFormatUtils.applyMarkdownFormatting


class PublicHuddlesAdapter(private val inflater: LayoutInflater, private val userId: Int, private val mListener: ItemListener) :
    PagingDataAdapter<HuddleUIModel, PublicHuddlesAdapter.HuddleViewHolder>(
        HuddleDiff
    ) {

    sealed class HuddleUIModel {
        abstract val huddle: AbstractHuddle?

        data class LocalHuddleUIModel(override val huddle: PublicHuddle, var selected: Boolean = false) : HuddleUIModel()
        data class MyPostsUIModel(override val huddle: PublicHuddle) : HuddleUIModel()
        data object MyPostsEmptyUIModel : HuddleUIModel() {
            override val huddle: AbstractHuddle?  = null
        }

        data object MyHuddlesEmptyModel: HuddleUIModel() {
            override val huddle: AbstractHuddle?  = null
        }

        data class SuggestedHuddleUIModel(override val huddle: SuggestedHuddle) : HuddleUIModel()
    }


    interface ItemListener {
        fun onItemClick(item: AbstractHuddle, position: Int)
        fun onMyPostClick() {}
        fun onItemLongClick(item: PublicHuddle, position: Int)

        fun onEdsActionCLick() {}

        fun isVisitor():Boolean
    }

    companion object {
        const val ITEM_LOCAL_HUDDLE = 1
        const val ITEM_SUGGESTED_HUDDLE = 2
        const val ITEM_TRIBE = 3
        const val ITEM_MY_POSTS = 4
        const val ITEM_MY_POSTS_EMPTY = 5
        const val ITEM_MY_HUDDLES_EMPTY = 6
    }


    override fun onBindViewHolder(holder: HuddleViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }


    override fun getItemViewType(position: Int): Int {
        return when (val item = peek(position)) {
            is HuddleUIModel.LocalHuddleUIModel -> if (item.huddle.isTribe) ITEM_TRIBE else ITEM_LOCAL_HUDDLE
            is HuddleUIModel.SuggestedHuddleUIModel -> ITEM_SUGGESTED_HUDDLE
            is HuddleUIModel.MyPostsUIModel -> ITEM_MY_POSTS
            is HuddleUIModel.MyPostsEmptyUIModel -> ITEM_MY_POSTS_EMPTY
            is HuddleUIModel.MyHuddlesEmptyModel -> ITEM_MY_HUDDLES_EMPTY
            null -> throw IllegalStateException("Unknown view")
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HuddleViewHolder = when (viewType) {
        ITEM_LOCAL_HUDDLE -> LocalHuddleViewHolder(ItemHuddleListBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        ITEM_SUGGESTED_HUDDLE -> SuggestedHuddleViewHolder(ItemHuddleListBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        ITEM_TRIBE -> TribeViewHolder(ItemHuddleListTribeBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        ITEM_MY_POSTS -> MyPostsViewHolder(ItemHuddleListMyPostsBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        ITEM_MY_POSTS_EMPTY -> MyPostsEmptyViewHolder(ItemHuddleListMyPostsEmptyBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        ITEM_MY_HUDDLES_EMPTY -> MyHuddlesEmptyViewHolder(LayoutListStateEmptyBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        else -> throw IllegalStateException("Unknown view type")
    }

    abstract inner class HuddleViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        abstract fun bind(item: HuddleUIModel, pos: Int)
    }

    open inner class MyPostsViewHolder(private val binding: ItemHuddleListMyPostsBinding) : HuddleViewHolder(binding.root) {
        override fun bind(item: HuddleUIModel, pos: Int) = with(binding) {
            item as HuddleUIModel.MyPostsUIModel

            val lastMessage = item.huddle.lastMessage
//            mainText.text = when (lastMessage?.postType) {
//                HuddlePostType.POST -> root.context.getString(R.string.my_posts_new_post)
//                HuddlePostType.REPLY -> root.context.getString(R.string.my_posts_reply)
//                else -> ""
//            }
            mainText.text=lastMessage?.huddleName
            showLastMessagePreview(this.lastMessagePreview, item.huddle, true)
            lastMessagePreview.huddleLastMessageSender.isVisible = false
            huddleTime.text = DateFormatHelper.humanizeMessageTime(lastMessage?.parsedCreatedTime, root.context)
            layoutHolder.setOnClickListener {
                mListener.onMyPostClick()
            }
            return@with
        }
    }

    open inner class MyPostsEmptyViewHolder(private val binding: ItemHuddleListMyPostsEmptyBinding) : HuddleViewHolder(binding.root) {
        override fun bind(item: HuddleUIModel, pos: Int) = with(binding) {
            item as HuddleUIModel.MyPostsEmptyUIModel

            layoutHolder.setOnClickListener {
                mListener.onMyPostClick()
            }
            return@with
        }
    }

    open inner class MyHuddlesEmptyViewHolder(private val binding: LayoutListStateEmptyBinding) : HuddleViewHolder(binding.root) {
        override fun bind(item: HuddleUIModel, pos: Int) = with(binding) {
            root.layoutParams = root.layoutParams.apply {
                height = ViewGroup.LayoutParams.WRAP_CONTENT
            }
            edsEmptyAction.isAllCaps = false
            prepare(
                image = R.drawable.im_eds_huddles,
                message = if(!mListener.isVisitor()) R.string.public_huddle_eds else R.string.public_huddle_eds_visitor,
                action =if(!mListener.isVisitor()) R.string.public_huddle_action_create_new else null
            ) {
                mListener.onEdsActionCLick()
            }
        }
    }

    open inner class LocalHuddleViewHolder(private val binding: ItemHuddleListBinding) : HuddleViewHolder(binding.root) {
        override fun bind(item: HuddleUIModel, pos: Int) = with(binding) {
            item as HuddleUIModel.LocalHuddleUIModel
            bindLocalHuddle(binding, item, pos)
            huddleCount.text = item.huddle.totalMembers.toString()
        }
    }

    inner class TribeViewHolder(private val binding: ItemHuddleListTribeBinding) : HuddleViewHolder(binding.root) {
        override fun bind(item: HuddleUIModel, pos: Int) = with(binding) {
            item as HuddleUIModel.LocalHuddleUIModel
            bindLocalHuddle(huddleLayout, item, pos)
            binding.adminTag.text = root.context.getString(if (item.huddle.managerId == userId) R.string.title_your_tribe else R.string.title_joined_tribe)
            huddleLayout.lastMessagePreview.huddleLastMessageSender.setTextColor(ContextCompat.getColor(root.context,R.color.colorPrimaryDark))
            if (item.huddle.involvement == HuddleInvolvement.MANAGER) {
                val count = item.huddle.totalMembers - 1
                huddleLayout.huddleCount.text = root.resources.getQuantityString(R.plurals.common_n_dears, count, count)
            }
        }
    }

    inner class SuggestedHuddleViewHolder(private val binding: ItemHuddleListBinding) : HuddleViewHolder(binding.root) {
        override fun bind(item: HuddleUIModel, pos: Int) = with(binding) {
            item as HuddleUIModel.SuggestedHuddleUIModel
            layoutHolder.setOnClickListener {
                mListener.onItemClick(item.huddle, layoutPosition)
            }
            huddle = item.huddle
            lastMessagePreview.huddleLastMessage.text = item.huddle.about
            huddleCount.text = item.huddle.totalMembers.toString()
        }
    }

    private fun bindLocalHuddle(binding: ItemHuddleListBinding, item: HuddleUIModel.LocalHuddleUIModel, pos: Int) = with(binding) {
        showHuddleStatus(this, item.huddle)
        showLastMessagePreview(lastMessagePreview, item.huddle)
        Log.i("PHA", "showLastMessagePreview:${item.huddle.name} - ${item.huddle.senderDetails?.name}")
        huddle = item.huddle
        localHuddle = item.huddle
        selected = item.selected
        root.setOnClickListener { mListener.onItemClick(item.huddle, pos) }
        root.setOnLongClickListener {
            mListener.onItemLongClick(item.huddle, pos)
            true
        }
        binding.lastMessagePreview.huddleLastMessageSender.setTextAppearance(if (item.huddle.hasUnread) R.style.TextAppearance_Flashat_LastMessage_Sender_Unread else R.style.TextAppearance_Flashat_LastMessage_Sender)
    }

    private fun showHuddleStatus(binding: ItemHuddleListBinding, item: PublicHuddle) = with(binding) {
        when (item.huddleStatus) {
            GroupChatStatus.INVITED -> {
                huddleTime.visibility = View.GONE
                huddleJoinStatus.isVisible = true
                huddleJoinStatus.setText(R.string.message_list_join_status_invited)
            }

            GroupChatStatus.JOIN_REQUESTED -> {
                huddleTime.visibility = View.GONE
                huddleJoinStatus.isVisible = true
                huddleJoinStatus.setText(R.string.message_list_join_status_requested)
            }

            else -> {
                huddleTime.isVisible = true
                huddleJoinStatus.visibility = View.GONE
                huddleTime.text = DateFormatHelper.humanizeMessageTime(item.parsedActivity, root.context)
            }
        }
    }

    private fun showLastMessagePreview(binding: LayoutHuddleLastMessagePreviewBinding, item: PublicHuddle, forceLight: Boolean = false) = with(binding) {
        huddleLastMessageMediaIcon.isVisible = false
        val defaultTextColor = if(forceLight) R.color.textColorAlwaysLightSecondary else if(item.hasUnread) R.color.textColorSecondary else R.color.textColorSecondaryLight
        huddleLastMessage.apply {
            text = item.about
            setTextColor(ContextCompat.getColor(root.context,defaultTextColor))
        }
        huddleLastMessageSender.isVisible = item.hasLastMessage


        item.lastMessage?.let { lm ->
            if (item.canReadChats) {
                val name = if (lm.belongsToUser(userId)) this.root.context.getString(R.string.common_you) else if (lm.senderDetails != null) lm.senderDetails?.name else item.senderDetails?.name
                huddleLastMessageSender.text = this.root.context.getString(R.string.message_list_last_message_sender, name)
                val convertedMessage = UserInfoUtil.decodeMentions(lm.displayMessage.orEmpty(), lm.mentionedUsers.orEmpty()) { user, token ->
                    "${MentionTokenizer.TOKEN_START_CHAR}${user.userNickNameOrName}"
                }.applyMarkdownFormatting(root.context)
                when(lm.messageType) {

                    MessageType.POLL -> {
                        huddleLastMessage.text = convertedMessage
                        huddleLastMessageMediaIcon.apply {
                            isVisible = true
                            setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_poll))
                        }
                    }

                    MessageType.ACTIVITY -> {

                        huddleLastMessageSender.text = ""
                        huddleLastMessage.text = lm.displayMessage.orEmpty()

                    }

                    MessageType.LOCATION -> {
                        huddleLastMessageMediaIcon.apply {
                            isVisible = true
                            setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_location_14dp))
                        }
                        huddleLastMessage.setText(R.string.message_list_preview_location)
                    }

                    MessageType.STICKER -> {
                        huddleLastMessageMediaIcon.apply {
                            isVisible = true
                            setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_sticker_comment))
                        }
                        huddleLastMessage.setText(R.string.chat_sticker)
                    }

                    MessageType.MEDIA -> {
                        when (lm.mediaMeta?.mediaType) {
                            MediaType.IMAGE -> {
                                huddleLastMessageMediaIcon.apply {
                                    isVisible = true
                                    setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_image_14dp))
                                }
                                if (!lm.displayMessage.isNullOrBlank()) huddleLastMessage.text = convertedMessage else huddleLastMessage.setText(R.string.message_list_preview_photo)
                            }

                            MediaType.AUDIO -> {
                                huddleLastMessageMediaIcon.apply {
                                    isVisible = true
                                    setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_audio_14dp))
                                }
                                if (!lm.displayMessage.isNullOrBlank()) huddleLastMessage.text = convertedMessage else huddleLastMessage.text = lm.mediaMeta?.mediaDuration
                            }

                            MediaType.VIDEO -> {
                                huddleLastMessageMediaIcon.apply {
                                    isVisible = true
                                    setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.ic_chat_video_14dp))
                                }
                                if (!lm.displayMessage.isNullOrBlank()) huddleLastMessage.text = convertedMessage else huddleLastMessage.text = lm.mediaMeta?.mediaDuration
                            }

                            MediaType.DOCUMENT -> {
                                huddleLastMessageMediaIcon.apply {
                                    isVisible = true
                                    lm.mediaMeta?.let {
                                        val res = MediaUtils.getDocumentResFromType(it.mediaDocumentType)
                                        setImageDrawable(ContextCompat.getDrawable(binding.root.context, res))
                                    }
                                }
                                huddleLastMessage.text = lm.mediaMeta?.documentDisplayName.orEmpty()
                            }

                            else -> {}
                        }
                    }
                    MessageType.TEXT, MessageType.GIFT -> {
                        huddleLastMessage.text = convertedMessage
                    }
                }
            }
        }
    }

    object HuddleDiff : DiffUtil.ItemCallback<HuddleUIModel>() {
        override fun areItemsTheSame(oldItem: HuddleUIModel, newItem: HuddleUIModel) = oldItem.huddle?.id == newItem.huddle?.id

        override fun areContentsTheSame(oldItem: HuddleUIModel, newItem: HuddleUIModel): Boolean {
            return oldItem == newItem
        }
    }
}