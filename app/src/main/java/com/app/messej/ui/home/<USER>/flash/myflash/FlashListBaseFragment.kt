package com.app.messej.ui.home.publictab.flash.myflash

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.appcompat.view.ActionMode
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.isTabletScreen
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

abstract class FlashListBaseFragment : Fragment(), FlashListAdapter.FlashClickListener, MenuProvider {

    protected var mAdapter: FlashListAdapter? = null

    protected abstract val viewModel: FlashListBaseViewModel

    abstract val multiStateView: MultiStateView

    abstract val flashList: RecyclerView

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onResume() {
        super.onResume()
        if ((mAdapter?.itemCount ?: 0) > 0) {
            mAdapter?.refresh()
        }
    }

    protected open fun setup() {
        initAdapter()
        val emptyView: View = multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.setEmptyView()
    }

    protected open fun LayoutListStateEmptyBinding.setEmptyView() {
        this.prepare(
            image = R.drawable.im_eds_my_flash,
            message = null
        )
    }

    protected open fun observe() {
        viewModel.flashFeedList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }
        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            it?.let { vs ->
                multiStateView.viewState = vs
            }
        }
        viewModel.selectedCount.observe(viewLifecycleOwner) {
            actionMode?.title = resources.getQuantityString(R.plurals.chat_selection_mode_count,it,it)
        }
        viewModel.chatSelectionMode.observe(viewLifecycleOwner) {
            if(it) {
                showSelectionMode(true)
            } else {
                showSelectionMode(false)
            }
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = FlashListAdapter(this)

        val layoutMan = GridLayoutManager(requireContext(), if(isTabletScreen) 5 else 3)

        flashList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                viewModel.setViewState(state)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }

    // Selection Mode
    var actionMode: ActionMode? = null
    private fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_flash_selection_cancel, menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_cancel -> {
                            viewModel.exitSelectionMode()
                            showSelectionMode(false)
                        }
                        else -> return false
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }

    protected open val supportsSelectionMode: Boolean = false

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        if (supportsSelectionMode) {
            return menuInflater.inflate(R.menu.menu_flash_selection, menu)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        return when (menuItem.itemId) {
            R.id.action_select->{
                viewModel.enterSelectionMode()
                return true
            }
            else -> false
        }
    }

    override fun onFlashClicked(pos: Int, flash: FlashVideoUIModel) {
        if (supportsSelectionMode && viewModel.selectMessage(flash,pos)) {
            // Selection has worked
        } else {
            viewModel.setActiveItem(pos)
            onFlashClicked(flash.flashVideo, pos)
        }
    }

    abstract fun onFlashClicked(flash: FlashVideo, pos: Int)

    override fun onFlashLongPressed(pos: Int, flash: FlashVideoUIModel, view: View) {
        showLongPressMenu(flash.flashVideo,view)
    }

    protected open fun showLongPressMenu(flash: FlashVideo, view: View) {
        // Do Nothing
    }
}