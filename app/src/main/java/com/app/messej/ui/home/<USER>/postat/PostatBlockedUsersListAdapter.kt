package com.app.messej.ui.home.publictab.postat

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.PostatMentionedUser
import com.app.messej.databinding.ItemPostatBlockedUserListBinding

class PostatBlockedUsersListAdapter(
    private val listener: PostatActionListener,
    val actionRes: String
) : PagingDataAdapter<PostatMentionedUser, PostatBlockedUsersListAdapter.PostatBlockedUsersVH>(PostatDiff) {

    interface PostatActionListener {
        fun onActionButtonClicked(view: View, user: PostatMentionedUser)
    }

    inner class PostatBlockedUsersVH(private val binding: ItemPostatBlockedUserListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PostatMentionedUser) = with(binding) {
            user = item
            action = actionRes
            unblockActionButton.setOnClickListener {
                listener.onActionButtonClicked(it, item)
            }
        }
    }

    object PostatDiff : DiffUtil.ItemCallback<PostatMentionedUser>() {
        override fun areItemsTheSame(oldItem: PostatMentionedUser, newItem: PostatMentionedUser) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PostatMentionedUser, newItem: PostatMentionedUser) = oldItem == newItem
    }

    override fun onBindViewHolder(holder: PostatBlockedUsersVH, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PostatBlockedUsersVH {
        val binding = ItemPostatBlockedUserListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PostatBlockedUsersVH(binding)
    }
}