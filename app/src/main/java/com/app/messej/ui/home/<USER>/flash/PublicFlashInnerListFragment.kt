package com.app.messej.ui.home.publictab.flash

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.FragmentNavigator
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.FlashTab
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutListStateErrorBinding
import com.app.messej.databinding.LayoutPublicFlashListBinding
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerFragment
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.isTabletScreen
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class PublicFlashInnerListFragment : Fragment() {

    private lateinit var binding: LayoutPublicFlashListBinding

    private var mAdapter: PublicFlashAdapter? = null

    private lateinit var viewModel: PublicFlashListViewModel

    protected lateinit var tab: FlashTab

    companion object {
        const val ARG_TAB = "tab"

        fun getTabBundle(tab: FlashTab) = Bundle().apply {
            putInt(ARG_TAB, tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): FlashTab {
            val tabInt = bundle?.getInt(ARG_TAB) ?: 0
            return FlashTab.entries[tabInt]
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        tab = parseTabBundle(arguments)
        viewModel = ViewModelProvider(requireActivity())[tab.name, PublicFlashListViewModel::class]
        viewModel.setTab(tab)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_public_flash_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        setEmptyView()
        initAdapter()

        binding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
            }
        }
    }

    private fun observe() {
        viewModel.flashFeedList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }
        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            it?.let { vs ->
                binding.multiStateView.viewState = vs
            }
        }
    }

    fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            image = R.drawable.im_eds_my_flash, message = R.string.flash_home_feed_eds
        )
        val errorView: View = binding.multiStateView.getView(MultiStateView.ViewState.ERROR) ?: return
        val errorViewBinding = LayoutListStateErrorBinding.bind(errorView)
        errorViewBinding.prepare(
            image = R.drawable.im_eds_my_flash, message = R.string.default_eds_error_message,
            action = R.string.common_retry
        ) {
            mAdapter?.refresh()
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = PublicFlashAdapter(object : PublicFlashAdapter.FlashClickListener {
            override fun onFlashClicked(pos: Int, view: View, flash: FlashVideo) {
                if (flash.isAvailable) {
                    viewModel.setActiveItem(pos)
                    toPlayer(0, BaseFlashPlayerFragment.getTransitionExtras(view, flash.id))
                }
            }
        })

        val layoutMan = GridLayoutManager(requireContext(), if(isTabletScreen) 3 else 2)

        binding.flashList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                if (loadState.refresh !is LoadState.Loading) {
                    binding.swipeRefresh.isRefreshing = false
                }
                val state = ViewUtils.getViewState(loadState, itemCount, true)
                viewModel.setViewState(state)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }

    fun toPlayer(pos: Int, extras: FragmentNavigator.Extras) {
        (parentFragment as? PublicFlashBaseFragment)?.toPlayer(pos, extras)
    }
}

