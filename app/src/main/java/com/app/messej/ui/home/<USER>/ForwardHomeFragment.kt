package com.app.messej.ui.home.forward

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.ExternalMessage
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.enums.ForwardMode
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.ForwardType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.databinding.FragmentHomeForwardBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.FragmentShareExtensions.setExternalShareRequest
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.tabs.TabLayoutMediator

class ForwardHomeFragment : Fragment(), MenuProvider {

    private val homeViewModel: CommonHomeViewModel by activityViewModels()
    private val viewModel: ForwardViewModel by viewModels()
    private val forwardCommonViewModel: ForwardCommonViewModel by navGraphViewModels(R.id.navigation_forward)
    private lateinit var mPrivatePagerAdapter: FragmentStateAdapter
    private lateinit var binding: FragmentHomeForwardBinding
    private val args: ForwardHomeFragmentArgs by navArgs()

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar, customBackButton = true)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_home_forward, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    private fun setup() {
        forwardCommonViewModel.setMessageId(args.messageId, args.srcType, args.textMessage, args.mediaType)
        viewModel.setTab(ForwardType.MESSAGES.ordinal)
    }

    fun observe() {
        forwardCommonViewModel.forwardMode.observe(viewLifecycleOwner) { mode ->
            val titleId = if (mode == ForwardMode.FORWARD) {
                R.string.title_forwarded_to
            } else {
                R.string.title_share_to
            }
            binding.customActionBar.toolBarTitle.text = getString(titleId)
        }
        forwardCommonViewModel.sourceMessage.observe(viewLifecycleOwner) {
            it?.let {
                var allowedTypes = ForwardType.entries.toTypedArray()
                if (it is ExternalMessage && args.srcType == ForwardSource.PODIUMS) {
                    allowedTypes = arrayOf(ForwardType.MESSAGES)
                } else if (it is HuddleChatMessage && it.hasMention) {
                    allowedTypes = arrayOf(ForwardType.HUDDLES)
                } else if (it.messageType == AbstractChatMessage.MessageType.LOCATION) {
                    allowedTypes = arrayOf(ForwardType.MESSAGES)
                } else if (it.messageType == AbstractChatMessage.MessageType.STICKER) {
                    allowedTypes = arrayOf(ForwardType.HUDDLES)
                } else if (it.mediaType == MediaType.DOCUMENT || (it is ExternalMessage && it.externalMediaType == MediaType.DOCUMENT)) {
                    allowedTypes = allowedTypes.except(ForwardType.HUDDLES)
                }
                mPrivatePagerAdapter = object : FragmentStateAdapter(childFragmentManager, viewLifecycleOwner.lifecycle) {

                    override fun getItemCount(): Int {
                        return allowedTypes.size
                    }

                    override fun createFragment(position: Int): Fragment {
                        return when (position) {
                            ForwardType.MESSAGES.ordinal -> ForwardViewPagerFragment().apply {
                                arguments = ForwardViewPagerFragment.setParams(ForwardType.MESSAGES.ordinal)
                            }

                            ForwardType.GROUPS.ordinal -> ForwardViewPagerFragment().apply {
                                arguments = ForwardViewPagerFragment.setParams(ForwardType.GROUPS.ordinal)
                            }

                            ForwardType.HUDDLES.ordinal -> ForwardViewPagerFragment().apply {
                                arguments = ForwardViewPagerFragment.setParams(ForwardType.HUDDLES.ordinal)
                            }

                            else -> throw java.lang.IllegalArgumentException("There should only be 3 tabs")
                        }
                    }
                }
                binding.forwardViewpager.apply {
                    isUserInputEnabled = false
                    adapter = mPrivatePagerAdapter
                    registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                        override fun onPageSelected(position: Int) {
                            viewModel.setTab(position)
                        }
                    })
                }

                TabLayoutMediator(binding.customActionBar.homeTab, binding.forwardViewpager) { tab, position ->
                    when (position) {
                        ForwardType.MESSAGES.ordinal -> {
                            if (it.messageType == AbstractChatMessage.MessageType.LOCATION || it.messageType == AbstractChatMessage.MessageType.STICKER || (it is HuddleChatMessage && it.hasMention)) {
                                binding.customActionBar.homeTab.visibility = View.GONE
                            } else {
                                tab.text = resources.getString(R.string.home_private_tab_buddies)
                                tab.setIcon(R.drawable.ic_home_private_tab_buddies)
                                binding.customActionBar.homeTab.visibility = View.VISIBLE
                            }
                        }

                        ForwardType.GROUPS.ordinal -> {
                            tab.text = resources.getString(R.string.home_private_tab_lounges)
                            tab.setIcon(R.drawable.ic_home_private_tab_lounges)
                        }

                        ForwardType.HUDDLES.ordinal -> {
                            tab.text = resources.getString(R.string.home_public_tab_huddles)
                            tab.setIcon(R.drawable.ic_home_public_tab_huddles)
                        }
                    }
                }.attach()
            }
        }

        homeViewModel.showProfileCompletionDialog.observe(viewLifecycleOwner) {
            Log.d("HOMEFRG", "observe: showProfileCompletionDialog is $it")
            if (it == true) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalIncompleteProfileDialogFragment())
            }
        }

        forwardCommonViewModel.onMessageForwarded.observe(viewLifecycleOwner) {
            val message = if (forwardCommonViewModel.forwardMode.value == ForwardMode.FORWARD) R.string.title_success_fully_forwarded else R.string.title_success_fully_shared
            showToast(message)
            findNavController().popBackStack()
        }

        forwardCommonViewModel.onError.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }

        forwardCommonViewModel.onLimitExceeds.observe(viewLifecycleOwner) {
            it?.let {
                if (it) {
                    val messageText = if (forwardCommonViewModel.messageLimit == 1) R.string.title_share_limit_exceeds else R.string.title_forward_limit_exceeds
                    Toast.makeText(requireContext(), getString(messageText), Toast.LENGTH_SHORT).show()
                }
            }
        }

        forwardCommonViewModel.navigateToNextFragment.observe(viewLifecycleOwner) { shareDetails ->
            val action = when (shareDetails) {
                is ForwardCommonViewModel.ShareRequest.PrivateChatRequest -> {
                    ForwardHomeFragmentDirections.actionGlobalNavigationChatPrivate(
                        roomId = shareDetails.roomId,
                        receiver = shareDetails.receiverId,
                    )
                }

                is ForwardCommonViewModel.ShareRequest.GroupChatRequest -> {
                    when (shareDetails.forwardType) {
                        ForwardType.GROUPS -> {
                            ForwardHomeFragmentDirections.actionGlobalNavigationChatGroup(
                                huddleId = shareDetails.roomId.toInt(),
                            )
                        }

                        ForwardType.HUDDLES -> {
                            ForwardHomeFragmentDirections.actionGlobalNavChatHuddle(
                                huddleId = shareDetails.roomId.toInt(),
                            )
                        }
                        else -> throw IllegalArgumentException("Invalid forward type for GroupChatRequest")
                    }
                }
            }

            val mediaTypeArg = args.mediaType
            if (mediaTypeArg != null) {
                setExternalShareRequest(message = args.textMessage, mediaType = args.mediaType, fileUri = args.mediaUri)
            }
            findNavController().popBackStack(R.id.navigation_forward, true)
            (activity as MainActivity).navController.navigateSafe(action)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_forward, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> {
                forwardCommonViewModel.clearList()
                val tab = viewModel.tab.value?: return false
                val action = if (args.messageId.isNullOrEmpty()) {
                    ForwardHomeFragmentDirections.actionForwardMessageHomeToForwardSearchFragment(
                        tab = tab.ordinal,
                        textMessage = args.textMessage,
                        mediaType = args.mediaType,
                        fileUri = args.mediaUri
                    )
                } else {
                    ForwardHomeFragmentDirections.actionForwardMessageHomeToForwardSearchFragment(messageId = args.messageId, tab = viewModel.tab.value?.ordinal!!)
                }
                findNavController().navigateSafe(action)
            }

            android.R.id.home -> {
                forwardCommonViewModel.clearList()
                findNavController().popBackStack()
            }
        }
        return true
    }
}
