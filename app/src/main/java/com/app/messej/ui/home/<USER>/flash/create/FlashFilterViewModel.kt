package com.app.messej.ui.home.publictab.flash.create

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.Category
import com.app.messej.data.model.enums.FlashFilterAge
import com.app.messej.data.model.enums.FlashFilterGender
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class FlashFilterViewModel(application: Application) : AndroidViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())

    val selectedGender = MutableLiveData<FlashFilterGender?>(null)
    val selectedAge = MutableLiveData<FlashFilterAge?>(null)
    private val selectedCountry = MutableLiveData<String?>(null)
    val selectedCategory = MutableLiveData<Int?>()

    init {
        getHuddleCategoriesList()
    }

    fun setFilterValues(args: FlashFilterFragmentArgs) {
//        args.age?.let {
//            if (it.trim().isNotEmpty()) selectedAge.postValue(FlashFilterAge.toAgeValue(it))
//        }

//        args.gender?.let {
//            if (it.trim().isNotEmpty()) selectedGender.postValue(FlashFilterGender.toGenderValue(it))
//        }

        args.category.let {
            selectedCategory.postValue(it)
        }
    }

    fun setGender(value: FlashFilterGender) {
        selectedGender.postValue(value)
    }

    fun setAge(value: FlashFilterAge) {
        selectedAge.postValue(value)
    }

    fun setCategoryId(value: Int?) {
        selectedCategory.value = value
    }

    fun setCountry(value: String) {
        selectedCountry.value = value
    }

    private val _huddleCategoryList: MutableLiveData<MutableList<Category>> = MutableLiveData(null)
    val huddleCategoryList: LiveData<MutableList<Category>> = _huddleCategoryList

    private fun getHuddleCategoriesList() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.getHuddleCategories(HuddleType.PUBLIC)) {
                is ResultOf.APIError -> {

                }

                is ResultOf.Error -> {

                }

                is ResultOf.Success -> {
                    _huddleCategoryList.postValue(result.value.categories)
                }
            }
        }
    }

    fun resetValues() {
        selectedGender.postValue(null)
        selectedAge.postValue(null)
        selectedCountry.postValue(null)
        selectedCategory.postValue(null)
    }

}