package com.app.messej.ui.home.publictab.flash

import android.graphics.drawable.Drawable
import android.os.Bundle
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.BindingAdapter
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.data.model.FlashVideoStats
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.FlashTab
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerFragment
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoinFromGreenDot
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PublicFlashPlayerFragment : BaseFlashPlayerFragment() {

    override lateinit var viewModel: PublicFlashListViewModel

    private val baseViewModel: PublicFlashViewModel by activityViewModels()

    private val args: PublicFlashPlayerFragmentArgs by navArgs()

    private lateinit var tab: FlashTab

    companion object {

        @JvmStatic
        @BindingAdapter("app:tabBackground", "app:tabBackgroundIf")
        fun tabBackground(view: AppCompatTextView, res: Drawable?, show: Boolean?) { // This methods should not have any return type, = declaration would make it return that object declaration.
            view.setBackgroundDrawable(if(show==true)res else null)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        tab = args.tab
        viewModel = ViewModelProvider(requireActivity())[tab.name, PublicFlashListViewModel::class]
        viewModel.setTab(tab)
    }

    override fun setup() {
        super.setup()

        binding.tabAll.setOnClickListener {
            switchTab(FlashTab.ALL)
        }
        binding.tabStars.setOnClickListener {
            switchTab(FlashTab.STARS)
        }
        binding.tabMe.setOnClickListener {
            switchTab(FlashTab.ME)
        }

        viewModel.tab.observe(viewLifecycleOwner) {
            binding.flashTab = it
        }
    }

    private fun switchTab(tab: FlashTab) {
        baseViewModel.setCurrentTab(tab)
        findNavController().navigateSafe(PublicFlashPlayerFragmentDirections.actionFlashPlayerFragmentSelf(tab))
    }

    override fun canPlayFlashVideo(flash: FlashVideo): Boolean {
        return flash.isAvailable
    }

    override fun onLivePodiumIndicatorClicked(flashVideo: FlashVideoStats, item: FlashVideo) {
        val podiumId = flashVideo.userLivePodiumId ?: return
        validateAndConfirmJoinFromGreenDot(podiumId, item.senderDetails?.name.orEmpty(), viewModel.user)
    }
}