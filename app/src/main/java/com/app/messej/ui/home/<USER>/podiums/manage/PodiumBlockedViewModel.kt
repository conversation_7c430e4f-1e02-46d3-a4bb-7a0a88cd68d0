package com.app.messej.ui.home.publictab.podiums.manage

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged

class PodiumBlockedViewModel(application: Application): AndroidViewModel(application) {

    enum class PodiumBlockedTab {
        TAB_BLOCKED,
        TAB_FROZEN
    }

    private val _currentTab = MutableLiveData<PodiumBlockedTab?>(null)
    val currentTab: LiveData<PodiumBlockedTab?> = _currentTab.distinctUntilChanged()

    fun setCurrentTab(tab: PodiumBlockedTab, skipIfSet: Boolean = false) {
        Log.w("FSVM", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
    }
}