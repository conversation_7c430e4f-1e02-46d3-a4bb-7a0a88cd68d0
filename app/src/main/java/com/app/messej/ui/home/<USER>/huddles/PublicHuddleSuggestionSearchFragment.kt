package com.app.messej.ui.home.publictab.huddles

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.SuggestedHuddle
import com.app.messej.databinding.FragmentPublicHuddleSuggestionBinding
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PublicHuddleSuggestionSearchFragment : Fragment() {

    private lateinit var binding: FragmentPublicHuddleSuggestionBinding

    private val viewModel: PublicHuddlesSuggestionViewModel by viewModels()

    private var mAdapter: PublicHuddleSuggestionAdapter? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_huddle_suggestion, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                setNavigationIcon(R.drawable.ic_close)
                setNavigationIconTint(resources.getColor(R.color.white))
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // for action menu
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
        binding.customActionBar.apply {
            keyword = viewModel.searchKeyword
            showKeyboard(searchBox)
        }
    }

    private fun observe() {
        viewModel.huddlesSearchSuggestionList.observe(viewLifecycleOwner) {
            mAdapter?.submitData(lifecycle = viewLifecycleOwner.lifecycle, it)
        }

//        lifecycleScope.launch {
//            repeatOnLifecycle(Lifecycle.State.STARTED) {
//                mAdapter?.loadStateFlow?.collect {
////                    binding.huddlesSuggestionProgress.isVisible = it.source.append is LoadState.Loading
//                }
//            }
//        }
    }

    private fun initAdapter() {
        mAdapter = PublicHuddleSuggestionAdapter(object : PublicHuddleSuggestionAdapter.HuddleClickListener {
            override fun onHuddleClick(view: View, huddle: SuggestedHuddle, position: Int) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChatHuddle(huddle.id))
            }
        })
        val layoutMan = LinearLayoutManager(context)

        binding.huddleList.apply {
            adapter = mAdapter
            layoutManager = layoutMan
            setHasFixedSize(false)
            addItemDecoration(DividerItemDecoration(this.context, DividerItemDecoration.VERTICAL))
        }
    }
}