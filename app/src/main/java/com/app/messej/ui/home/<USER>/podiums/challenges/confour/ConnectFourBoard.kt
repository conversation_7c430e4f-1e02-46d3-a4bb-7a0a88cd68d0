package com.app.messej.ui.home.publictab.podiums.challenges.confour

import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.SlotState
import com.app.messej.data.model.enums.ConFourGameStatus

class ConnectFourBoard {
    companion object {
        const val ROWS = 6
        const val COLS = 7
        const val CONNECT_TARGET = 4

        fun List<List<Int>>.parseBoard(): List<List<SlotState>> {
            return map { row ->
                row.map {
                    when (it) {
                        1 -> SlotState.PLAYER1
                        2 -> SlotState.PLAYER2
                        else -> SlotState.EMPTY
                    }
                }
            }
        }

        fun List<List<SlotState>>.unParseBoard(): List<List<Int>> {
            return map { row ->
                row.map {
                    when (it) {
                        SlotState.EMPTY -> 0
                        SlotState.PLAYER1 -> 1
                        SlotState.PLAYER2 -> 2
                    }
                }
            }
        }

        fun List<List<SlotState>>.printableString(): String {
            var str = ""
            this.forEach { row ->
                row.forEach {
                    str += when (it) {
                        SlotState.EMPTY -> "⚫"
                        SlotState.PLAYER1 -> "🟣"
                        SlotState.PLAYER2 -> "🟡"
                    }
                }
                str += "\n"
            }
            return str
        }
    }

    private var boardArray = Array(ROWS) { Array(COLS) {
        SlotState.EMPTY
    } }

    var board: List<List<SlotState>>
        get() = boardArray.map { it.asList() }
        set(value) {
            boardArray = value.map {
                it.toTypedArray()
            }.toTypedArray()
            checkConnect4()
        }

    val gameStatus: ConFourGameStatus
        get() {
            return if (connectFourLine != null) ConFourGameStatus.WON
            else if (isFull) ConFourGameStatus.DRAW
            else ConFourGameStatus.NONE
        }

    enum class Connect4Direction(val right: Int, val down: Int) {
        HORIZONTAL(MatchDirection.RIGHT.right, MatchDirection.RIGHT.down),
        VERTICAL(MatchDirection.DOWN.right, MatchDirection.DOWN.down),
        DIAGONAL(MatchDirection.DOWN_RIGHT.right, MatchDirection.DOWN_RIGHT.down),
        ANTI_DIAGONAL(MatchDirection.UP_RIGHT.right, MatchDirection.UP_RIGHT.down);

        override fun toString() = "$name [$right, $down]"
    }

    data class DropPoint(
        val row: Int,
        val column: Int
    )

    val printableString: String
        get() = board.printableString()

    data class ConnectFourLine(
        val player: ChallengePlayer,
        val start: DropPoint,
        val end: DropPoint
    ) {
        val direction: Connect4Direction
            get() = if (start.row == end.row) Connect4Direction.HORIZONTAL
            else if (start.column == end.column) Connect4Direction.VERTICAL
            else if (start.row < end.row) Connect4Direction.DIAGONAL
            else Connect4Direction.ANTI_DIAGONAL

        val connectionSlots: List<DropPoint>
            get() {
                val list = mutableListOf<DropPoint>()
                var point = DropPoint(start.row, start.column)
                while (point.row != end.row || point.column != end.column) {
                    list.add(point)
                    point = DropPoint(point.row+direction.down, point.column+direction.right)
                }
                list.add(point)
                return list
            }
    }
    var connectFourLine : ConnectFourLine? = null
        private set

    val hasConnect4: Boolean
        get() = connectFourLine != null


    init {
        resetBoard()
    }

    /**
     * Resets the board to empty
     */
    fun resetBoard() {
        boardArray.forEach { row ->
            row.forEachIndexed { col, _ ->
                row[col] = SlotState.EMPTY
            }
        }
        connectFourLine = null
    }

    /**
     * Drops a piece at [col] for the player [player]
     * @throws Exception if [hasConnect4] is true
     */
    fun dropPiece(col: Int, player: ChallengePlayer): Pair<DropPoint, ConFourGameStatus>? {
        if (hasConnect4) throw Exception("Trying to drop a piece when already connected")
        if (col >= COLS || col < 0) throw Exception("Invalid column")
        var droppedInRow = -1
        val tempBoard = boardArray.map { it.clone() }.toTypedArray()
        for (row in ROWS - 1 downTo 0) {
            if (boardArray[row][col] == SlotState.EMPTY) {
                tempBoard[row][col] = player.slotState
                droppedInRow = row
                break
            }
        }
        val tempBoardObj = ConnectFourBoard()

        if (droppedInRow >= 0) {
            tempBoardObj.board = tempBoard.map { it.asList() }

        }
        return if (droppedInRow == -1) null else Pair(DropPoint(droppedInRow, col), tempBoardObj.gameStatus)
    }

    fun registerDrop(point: DropPoint, player: ChallengePlayer) {
        if (boardArray[point.row][point.column] != SlotState.EMPTY) {
            throw Exception("There is already a coin in that spot")
        }
        if (point.row>0) {
            for (row in point.row - 1 downTo 0) {
                if (boardArray[row][point.column] != SlotState.EMPTY) {
                    throw Exception("Slots above are not empty")
                }
            }
        }
        if (point.row<ROWS-1) {
            for (row in point.row + 1 until ROWS) {
                if (boardArray[row][point.column] == SlotState.EMPTY) {
                    throw Exception("There are empty slots below")
                }
            }
        }
        boardArray[point.row][point.column] = player.slotState
        val line = checkConnect4(point.row, point.column)
        connectFourLine = line
    }

    /**
     * Enum class for the direction of a match with increment integers for row and column
     * e.g. MatchDirection.RIGHT - down = 0 and right = 1 means that loops need to increment the row by 0 and column by 1 to traverse in said direction
     */
    enum class MatchDirection(val right: Int, val down: Int) {
        RIGHT(1, 0),
        LEFT(-1, 0),
        DOWN(0, 1),
        UP(0, -1),
        UP_RIGHT(1, -1),
        UP_LEFT(-1, -1),
        DOWN_RIGHT(1, 1),
        DOWN_LEFT(-1, 1)
    }

    /**
     * looks for and counts matches in [direction] from [row][col]
     */
    private fun matchesTo(row: Int, col: Int, direction: MatchDirection): Int {
        var matches = 0
        val player = boardArray[row][col]
        if (player == SlotState.EMPTY) {
            return 0
        }
        var r = row + direction.down
        var c = col + direction.right
        while (r in 0 until ROWS && c in 0 until COLS) {
            if (boardArray[r][c] != player) {
                break
            }
            matches++
            r += direction.down
            c += direction.right
        }
        return matches
    }

    /**
     * Checks if the drop at [row][col] has a connect4 match
     */
    private fun checkConnect4(row: Int, col: Int) : ConnectFourLine? {
        val state = boardArray[row][col]
        val player = state.player?: return null

        val matchesToTheLeft = matchesTo(row, col, MatchDirection.LEFT)
        val matchesToTheRight = matchesTo(row,col,MatchDirection.RIGHT)

        if (matchesToTheLeft + matchesToTheRight + 1 >= CONNECT_TARGET) {
            return ConnectFourLine(
                player = player,
                start = DropPoint(row, col - matchesToTheLeft),
                end = DropPoint(row, col + matchesToTheRight)
            )
        }

        val matchesAbove = matchesTo(row, col, MatchDirection.UP)
        val matchesBelow = matchesTo(row, col, MatchDirection.DOWN)

        if (matchesAbove + matchesBelow + 1 >= CONNECT_TARGET) {
            return ConnectFourLine(
                player = player,
                start = DropPoint(row - matchesAbove, col),
                end = DropPoint(row + matchesBelow, col)
            )
        }

        val matchesAboveLeft = matchesTo(row, col, MatchDirection.UP_LEFT)
        val matchesBelowRight = matchesTo(row, col, MatchDirection.DOWN_RIGHT)

        if (matchesAboveLeft + matchesBelowRight + 1 >= CONNECT_TARGET) {
            return ConnectFourLine(
                player = player,
                start = DropPoint(row - matchesAboveLeft, col - matchesAboveLeft),
                end = DropPoint(row + matchesBelowRight, col + matchesBelowRight)
            )
        }

        val matchesAboveRight = matchesTo(row, col, MatchDirection.UP_RIGHT) // 0
        val matchesBelowLeft = matchesTo(row, col, MatchDirection.DOWN_LEFT) // 3

        if (matchesAboveRight + matchesBelowLeft + 1 >= CONNECT_TARGET) {
            return ConnectFourLine(
                player = player,
                start = DropPoint(row + matchesBelowLeft, col - matchesBelowLeft),
                end = DropPoint(row - matchesAboveRight, col + matchesAboveRight)
            )
        }

        return null // No winner
    }

    /**
     * check for existing connections by finding the topmost coin in each column
     */
    private fun checkConnect4() {
        for (col in 0 until COLS) {
            var topFilled = -1
            for (row in 0 until ROWS) {
                if (boardArray[row][col] != SlotState.EMPTY) {
                    topFilled = row
                    break
                }
            }
            if (topFilled >= 0) {
                checkConnect4(topFilled,col)?.let { line ->
                    connectFourLine = line
                    return
                }

            }
        }
    }

    /**
     * Returns true if the board is full
     */
    val isFull: Boolean
        get() = boardArray.none { row ->
            row.any {
                it == SlotState.EMPTY
            }
        }

    /**
     * Returns true if there are more coins dropped in the board than the current board, excluding the drop at [excludeDrop]
     */
    fun needsRepair(newBoard: List<List<SlotState>>, excludeDrop: DropPoint? = null): Boolean {
        for(row in 0 until ROWS) {
            for(col in 0 until COLS) {
                if (row != excludeDrop?.row || col != excludeDrop.column) {
                    if (newBoard[row][col] != boardArray[row][col]) {
                        return true
                    }
                }
            }
        }
        return false
    }

    data class BoardDiff(
        val additions: List<DropPoint>,
        val removals: List<DropPoint>,
        val changes: List<DropPoint>
    )

    fun calculateDiff(newBoard: List<List<SlotState>>): BoardDiff {
        val additions = mutableListOf<DropPoint>()
        val removals = mutableListOf<DropPoint>()
        val changes = mutableListOf<DropPoint>()
        for(row in 0 until ROWS) {
            for(col in 0 until COLS) {
                if (newBoard[row][col] != boardArray[row][col]) {
                    if (newBoard[row][col] == SlotState.EMPTY) {
                        removals.add(DropPoint(row, col))
                    } else if(boardArray[row][col] == SlotState.EMPTY){
                        additions.add(DropPoint(row,col))
                    } else {
                        changes.add(DropPoint(row,col))
                    }
                }
            }
        }
        return BoardDiff(
            additions = additions.toList(),
            removals = removals.toList(),
            changes = changes.toList()
        )
    }

}
