package com.app.messej.ui.auth.profile

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.SelectableUser
import com.app.messej.databinding.ItemRegisterSuperstarListBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class RegisterSuperstarListQuickAdapter(data: MutableList<SelectableUser>): BaseQuickAdapter<SelectableUser, BaseDataBindingHolder<ItemRegisterSuperstarListBinding>>(R.layout.item_register_superstar_list,data) {

    override fun convert(holder: BaseDataBindingHolder<ItemRegisterSuperstarListBinding>,
                         item: SelectableUser) {
        holder.dataBinding?.apply {
            user = item.user
            selected = item.selected
        }
    }

    class DiffCallback: DiffUtil.ItemCallback<SelectableUser>() {
        override fun areItemsTheSame(oldItem: SelectableUser,newItem: SelectableUser): Boolean {
            return oldItem.user.id == newItem.user.id
        }

        override fun areContentsTheSame(oldItem: SelectableUser,
                                        newItem: SelectableUser): Boolean {
            return oldItem.selected == newItem.selected
        }
    }
}