package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.databinding.FragmentChallengeFeeStatusBottomSheetBinding
import com.app.messej.databinding.ItemChallengeFeeStatusBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.ScaleInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class ChallengeFeeStatusBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private lateinit var binding: FragmentChallengeFeeStatusBottomSheetBinding
    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    private var mContributorsAdapter: BaseQuickAdapter<PodiumChallenge.ChallengeUser, BaseDataBindingHolder<ItemChallengeFeeStatusBinding>>? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_challenge_fee_status_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        observe()
    }

    fun setup() {
        initAdapter()
        binding.editButton.setOnClickListener {
            viewModel.podium.value?.let {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(it.id, viewModel.activeChallengeId.value?.first))
            }
        }

        binding.deleteButton.setOnClickListener {
            confirmAction(
                message = getString(R.string.podium_challenge_delete_alert),
                positiveTitle = R.string.common_delete,
                negativeTitle = R.string.common_cancel
            ) {
                viewModel.deleteChallenge()
            }
        }

        binding.actionChooseMore.setOnClickListener {
            findNavController().navigateSafe(ChallengeFeeStatusBottomSheetFragmentDirections.actionChallengeFeeStatusBottomSheetFragmentToChooseMoreContributorsFragment())
        }

        binding.actionStartChallenge.setOnClickListener {
            viewModel.startPodiumChallenge()
        }

    }

    fun observe() {
        viewModel.contributorsList.observe(viewLifecycleOwner){
            mContributorsAdapter?.apply {
                if (data.size == 0 || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        viewModel.challengeDeleted.observe(viewLifecycleOwner) {
            if (it) {
                showToast(R.string.podium_challenge_deleted_text)
                findNavController().popBackStack()
            }
        }

        viewModel.onChallengeStarted.observe(viewLifecycleOwner) {
                findNavController().popBackStack()
        }
    }

    private fun initAdapter() {

        mContributorsAdapter = object : BaseQuickAdapter<PodiumChallenge.ChallengeUser, BaseDataBindingHolder<ItemChallengeFeeStatusBinding>>(R.layout.item_challenge_fee_status, mutableListOf()) {
            override fun convert(
                holder: BaseDataBindingHolder<ItemChallengeFeeStatusBinding>,
                item: PodiumChallenge.ChallengeUser,
            ) {
                holder.dataBinding?.apply {
                    lifecycleOwner = viewLifecycleOwner
                    user = item
                    clickable = false
                }
            }
        }.apply {
            animationEnable = true
            adapterAnimation = ScaleInAnimation()
            isAnimationFirstOnly = true
            setDiffCallback(ContributorDiffCallback)
        }


        binding.contributorsList.apply {
            layoutManager = GridLayoutManager(context, 2)
            setHasFixedSize(true)
            adapter = mContributorsAdapter
        }


    }

    object ContributorDiffCallback : DiffUtil.ItemCallback<PodiumChallenge.ChallengeUser>(){
        override fun areItemsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser) = oldItem == newItem

    }
}