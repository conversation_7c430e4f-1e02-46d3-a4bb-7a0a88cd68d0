package com.app.messej.ui.home.publictab.postat

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import com.app.messej.data.model.enums.PostatTab
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository

class PublicPostatViewModel(application: Application) : AndroidViewModel(application) {

    val profileRepo = ProfileRepository(application)
    val accountRepo = AccountRepository(application)


    private val _currentTab = MutableLiveData(PostatTab.default)
    val currentTab: LiveData<PostatTab?> = _currentTab.distinctUntilChanged()

    fun setCurrentTab(tab: PostatTab, skipIfSet: Boolean = false) {
        if (skipIfSet && currentTab.value != null) return
        _currentTab.value = tab
    }
}