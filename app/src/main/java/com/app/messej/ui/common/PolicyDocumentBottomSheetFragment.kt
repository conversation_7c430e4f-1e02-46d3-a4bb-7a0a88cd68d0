package com.app.messej.ui.common

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentBottomSheetPolicyDocumentBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class PolicyDocumentBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentBottomSheetPolicyDocumentBinding
    private val viewModel: PolicyDocumentViewModel by viewModels()
    private val documentArgs: PolicyDocumentBottomSheetFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_bottom_sheet_policy_document, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getLegalDocument(documentArgs.documentType)
        setup()
        observe()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_CaseDetailsBottomSheet)
    }

    override fun getTheme(): Int {
        return R.style.Widget_Flashat_CaseDetailsBottomSheet
    }

    private fun setup() {
        binding.icClose.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.txtPolicyTitle.text = when(documentArgs.documentType) {
            DocumentType.TASK_TAB_ACCOUNT_STATUS ->  getString(R.string.business_task_title_account_status).uppercase()
            DocumentType.TASK_TAB_PROFILE_ABOUT -> getString(R.string.bottom_sheet_profile).uppercase()
            DocumentType.TASK_TAB_HUDDLE_ABOUT -> getString(R.string.business_task_title_account_huddle).uppercase()
            DocumentType.TASK_TAB_E_TRIBE -> getString(R.string.business_task_title_account_e_tribe)
            DocumentType.TASK_TAB_RATE_FLASHAT_ABOUT -> getString(R.string.business_task_title_account_rate_flashat).uppercase()
            DocumentType.TASK_TAB_PERFORMANCE_RATING_ABOUT -> getString(R.string.business_task_title_account_performance_rating).uppercase()
            DocumentType.TASK_TAB_MINIMUM_BALANCE_ABOUT -> getString(R.string.business_task_title_account_minimum_balance).uppercase()
            DocumentType.TASK_TAB_GENEROSITY_ABOUT -> getString(R.string.business_task_title_account_generosity).uppercase()
            DocumentType.TASK_TAB_ELIGIBILITY_ABOUT -> getString(R.string.title_business_eligibility).uppercase()
            else -> ""
        }
    }

    private fun observe() {
        viewModel.policyData.observe(viewLifecycleOwner) {
            binding.policyWebView.visibility = View.VISIBLE
            val html = it?.legalDocument?.description
            if (!html.isNullOrEmpty()) {
                binding.policyWebView.loadData(html, "text/html; charset=utf-8", "UTF-8")
            }
        }
        viewModel.tncError.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
                findNavController().popBackStack()
            }
            binding.policyWebView.visibility = View.GONE
        }
    }

}