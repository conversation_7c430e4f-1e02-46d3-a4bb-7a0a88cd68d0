package com.app.messej.ui.home.publictab.podiums.challenges

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.databinding.FragmentChallengeInfoBinding
import com.app.messej.databinding.ItemPodiumChallengeContributorsBinding
import com.app.messej.databinding.ItemPodiumChallengeParticipantBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChallengeParticipant
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class ChallengeInfoFragment : Fragment() {

    private lateinit var binding: FragmentChallengeInfoBinding

    private val viewModel : PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    private var mParticipantsAdapter: BaseQuickAdapter<ChallengeParticipant, BaseDataBindingHolder<ItemPodiumChallengeParticipantBinding>>? = null

    private var mContributorsAdapter: BaseQuickAdapter<PodiumChallenge.ChallengeUser, BaseDataBindingHolder<ItemPodiumChallengeContributorsBinding>>? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_challenge_info, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun setup() {
        initAdapter()

        binding.deleteButton.setOnClickListener {
            confirmAction(
                message = getString(R.string.podium_challenge_delete_alert),
                positiveTitle = R.string.common_delete,
                negativeTitle = R.string.common_cancel
            ) {
                viewModel.deleteChallenge()
            }
        }

        binding.continueButton.setOnClickListener {
            val challenge = viewModel.activeChallenge.value?: return@setOnClickListener
            val podiumId = challenge.podiumId
            val challengeId = challenge.challengeId
            val options = NavOptions.Builder()
                .setPopUpTo(R.id.podiumChallengeInfoFragment, inclusive = true)
                .build()
            if (challenge.contributorType == ChallengeContributionType.SPEAKERS && challenge.anyContributorAccepted)
                findNavController().navigateSafe(ChallengeInfoFragmentDirections.actionPodiumChallengeInfoFragmentToChallengeFeeStatusBottomSheetFragment(), options)
            else
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(podiumId, challengeId), options)
        }

        binding.chooseFacilitatorButton.setOnClickListener {
            val podiumId = viewModel.podiumId.value?: return@setOnClickListener
            val challengeId = viewModel.activeChallengeId.value?.first?: return@setOnClickListener
            val options = NavOptions.Builder()
                .setPopUpTo(R.id.podiumChallengeInfoFragment, inclusive = true)
                .build()
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChallengeSetup(podiumId, challengeId), options)
        }
    }


    private fun observer() {
        viewModel.challengeParticipants.observe(viewLifecycleOwner) {
            mParticipantsAdapter?.apply {
                if (data.size == 0 || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        viewModel.activeChallenge.observe(viewLifecycleOwner) {
            it?: return@observe
            it.contributors.filter {user-> user.requestAccepted == true }.let {
                mContributorsAdapter?.apply {
                    if (data.size == 0 || it.isEmpty()) {
                        setNewInstance(it.toMutableList())
                    } else {
                        setDiffNewData(it.toMutableList())
                    }
                }
            }

            binding.tvChallengeTitle.text = getString(it.challengeType.resId)
            binding.tvChallengeTime.text = it.formattedChallengeDuration(resources)
        }

        viewModel.challengeDeleted.observe(viewLifecycleOwner) {
            if (it) findNavController().popBackStack()
        }
    }

    private fun initAdapter() {

        val participantsDiff = object : DiffUtil.ItemCallback<ChallengeParticipant>() {
            override fun areItemsTheSame(oldItem: ChallengeParticipant, newItem: ChallengeParticipant): Boolean {
                return oldItem.score.id == newItem.score.id
            }

            override fun areContentsTheSame(oldItem: ChallengeParticipant, newItem: ChallengeParticipant): Boolean {
                return oldItem == newItem
            }
        }

        mParticipantsAdapter =
            object : BaseQuickAdapter<ChallengeParticipant, BaseDataBindingHolder<ItemPodiumChallengeParticipantBinding>>(R.layout.item_podium_challenge_participant, mutableListOf()) {
                override fun convert(holder: BaseDataBindingHolder<ItemPodiumChallengeParticipantBinding>, item: ChallengeParticipant) {
                    holder.dataBinding?.apply {
                        speaker = item.speaker
                    }
                }
            }

        binding.rvChallengeParticipants.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(false)
            adapter = mParticipantsAdapter
        }

        mParticipantsAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(participantsDiff)
        }

        mContributorsAdapter =
            object : BaseQuickAdapter<PodiumChallenge.ChallengeUser, BaseDataBindingHolder<ItemPodiumChallengeContributorsBinding>>(R.layout.item_podium_challenge_contributors, mutableListOf()) {
                override fun convert(holder: BaseDataBindingHolder<ItemPodiumChallengeContributorsBinding>, item: PodiumChallenge.ChallengeUser) {
                    holder.dataBinding?.apply {
                        contributor = item
                    }
                }
            }

        val contributorsDiff = object : DiffUtil.ItemCallback<PodiumChallenge.ChallengeUser>() {
            override fun areItemsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser): Boolean {
                return oldItem == newItem
            }
        }

        binding.rvChallengeContributors.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(false)
            adapter = mContributorsAdapter
        }

        mContributorsAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(contributorsDiff)
        }

    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.actionBar.toolbar)
        bindFlaxRateToolbarChip(binding.actionBar.flaxRateChip)
        binding.actionBar.toolBarTitle.text = getString(R.string.podium_challenge_info)
    }
}