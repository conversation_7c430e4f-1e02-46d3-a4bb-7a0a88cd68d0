package com.app.messej.ui.home.publictab.authorities.legalAffairs.violations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.app.messej.R
import com.app.messej.data.model.enums.LegalAffairsViolationSubTab
import com.app.messej.databinding.FragmentLegalAffairsViolationsBinding
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsCommonViewModel

class ViolationsFragment : Fragment() {

    private lateinit var binding: FragmentLegalAffairsViolationsBinding
    private val viewModel: LegalAffairsCommonViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_legal_affairs_violations, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        setupClickListeners()
    }

    private fun setupClickListeners() {
        binding.apply {
            btnOpen.cardView.setOnClickListener {
                viewModel.setViolationSubTab(tab = LegalAffairsViolationSubTab.Open)
            }

            btnClosed.cardView.setOnClickListener {
                viewModel.setViolationSubTab(tab = LegalAffairsViolationSubTab.Closed)
            }

            btnFines.cardView.setOnClickListener {
                viewModel.setViolationSubTab(tab = LegalAffairsViolationSubTab.Fines)
            }

            btnStatistics.cardView.setOnClickListener {
                viewModel.setViolationSubTab(tab = LegalAffairsViolationSubTab.Statistics)
            }
        }
    }

    private fun observe() {
        viewModel.currentSelectedLegalAffairsViolationSubTab.observe(viewLifecycleOwner) { tab ->
            binding.apply {
                btnOpen.isSelected = tab == LegalAffairsViolationSubTab.Open
                btnClosed.isSelected = tab == LegalAffairsViolationSubTab.Closed
                btnFines.isSelected = tab == LegalAffairsViolationSubTab.Fines
                btnStatistics.isSelected = tab == LegalAffairsViolationSubTab.Statistics
            }
            replaceFrameLayout(currentTab = tab)
        }
    }

    private fun replaceFrameLayout(currentTab : LegalAffairsViolationSubTab) {
        val fragment = if (currentTab == LegalAffairsViolationSubTab.Statistics) LegalAffairsViolationStatisticsFragment() else LegalAffairsViolationsListFragment()

        childFragmentManager
            .beginTransaction()
            .replace(R.id.frame_layout_violations, fragment)
            .commit()
    }
}