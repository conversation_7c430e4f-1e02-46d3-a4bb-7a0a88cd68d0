package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.View
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.isVisible
import androidx.lifecycle.MutableLiveData
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.entity.Podium
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.FragmentExtensions.confirmAction

class PodiumAdminsBottomSheetFragment : PodiumAdminsBaseFragment() {

    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
//        setup()
        observer()
    }

//    private fun setup(){
//        viewModel.getPodiumAdminsList()
//    }

    override val loadingStateLiveData: MutableLiveData<Boolean>
        get() = viewModel.adminsListLoading

    override val kebabVisibility: Boolean
        get() = false


    private fun observer(){

        viewModel.allAdmins.observe(viewLifecycleOwner){
            mAdminsAllListAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        
        loadingStateLiveData.observe(viewLifecycleOwner){
            binding.actionLoading.isVisible=it
        }

        viewModel.onAdminActionFinished.observe(viewLifecycleOwner){
            mAdminsAllListAdapter?.refresh()
        }
    }

    override fun setupMoreMenu(v: View, item: PodiumSpeaker): Boolean {
        var hasOptions = false
        v.setOnClickListener {
            val popup = PopupMenu(v.context,v)
            popup.menuInflater.inflate(R.menu.menu_podium_admins_list, popup.menu)
            popup.menu.apply {
                findItem(R.id.action_cancel_invitation).isVisible = item.invitedToBeAdmin == true
                hasOptions = hasOptions || item.invitedToBeAdmin == true
                val canDismiss = item.role == Podium.PodiumUserRole.ADMIN && item.id != viewModel.user.id
                findItem(R.id.action_dismiss_admin).isVisible = canDismiss
                hasOptions = hasOptions || canDismiss
            }
            popup.setOnMenuItemClickListener { menuItem ->
                when(menuItem.itemId) {
                    R.id.action_cancel_invitation -> {
                        confirmAction(
                            title = null, message = getString(R.string.podium_cancel_admin_invite_confirmation_message)
                        ) {
                            viewModel.cancelAdminInvite(item.id)
                        }
                    }
                    R.id.action_dismiss_admin -> {
                        confirmAction(
                            title = null, message = getString(R.string.podium_admin_dismiss_confirmation_message)
                        ) {
                            viewModel.dismissAsAdmin(item.id)
                        }
                    }
                    else -> return@setOnMenuItemClickListener false
                }
                return@setOnMenuItemClickListener true
            }
            popup.show()
        }
        return hasOptions
    }

    override fun onResume() {
        super.onResume()
        mAdminsAllListAdapter?.refresh()
    }
}