package com.app.messej.ui.home.publictab.authorities.legalAffairs.ban

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.data.repository.LegalAffairsRepository

class BanViewModel(application: Application) : AndroidViewModel(application) {

    private val legalAffairsRepository = LegalAffairsRepository(application)
    val myLegalRecordsCount = MutableLiveData<LegalRecordsResponse?>(null)
    val investigationBureauCount = MutableLiveData<LegalRecordsResponse?>(null)

    val homeBanList = legalAffairsRepository
        .getViolationList(
            recordType = LegalAffairTabs.Bans.serializedName(),
            countCallBack = {
                myLegalRecordsCount.postValue(
                    LegalRecordsResponse(
                        violationsCount = it?.violationsCount,
                        banCount = it?.banCount,
                        reportingCount = it?.reportingCount
                    )
                )
            }
        )
        .liveData
        .cachedIn(viewModelScope)

    val investigationBureauBanList = legalAffairsRepository
        .getLegalAffairsBoardDetails(
            recordType = LegalAffairTabs.Bans.serializedName(),
            tab = LegalAffairsMainTab.InvestigationBureau.serializedName(),
            countCallBack = { item ->
                investigationBureauCount.postValue(
                    LegalRecordsResponse(
                        banCount = item?.banCount,
                        reportsCount = item?.reportsCount,
                        hiddenCount = item?.hiddenCount
                    )
                )
            }
        )
        .liveData
        .cachedIn(viewModelScope)

}