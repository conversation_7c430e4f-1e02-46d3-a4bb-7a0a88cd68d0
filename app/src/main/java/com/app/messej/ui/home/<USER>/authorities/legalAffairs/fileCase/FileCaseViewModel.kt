package com.app.messej.ui.home.publictab.authorities.legalAffairs.fileCase

import android.app.Application
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.MainApplication
import com.app.messej.R
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.ReportProofMedia
import com.app.messej.data.model.api.DealsBeneficiary
import com.app.messej.data.model.api.DealsBeneficiaryListResponse
import com.app.messej.data.model.api.FileCaseRequest
import com.app.messej.data.model.api.ReportCategoryResponse.ReportCategory
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.LegalAffairsRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import com.app.messej.ui.legal.report.ReportViewModel
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class FileCaseViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepository = AccountRepository(application)
    private val repository = LegalAffairsRepository(application)
    private val businessRepository = BusinessRepository(application)
    private val profileRepository = ProfileRepository(application)

    private val _accountDetails = accountRepository.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    private val _proofFiles = MutableLiveData<List<ReportProofMedia>>()
    val proofFiles: LiveData<List<ReportProofMedia>> = _proofFiles

    private val _reportCategories : MutableLiveData<List<ReportCategory>?> = MutableLiveData(null)
    val reportCategories: LiveData<List<ReportCategory>?> = _reportCategories

    private val _selectedCategory = MutableLiveData<ReportCategory?>(null)

    private val _usersList = MutableLiveData<List<DealsBeneficiary>>()
    val usersList : LiveData<List<DealsBeneficiary>> = _usersList

    private val _selectedUser = MutableLiveData<DealsBeneficiary?>(null)

    private var userSearchingJob: Job? = null

    val userName = MutableLiveData<String>()
    val indicementText = MutableLiveData<String>()

    private val _isSubmitting = MutableLiveData(false)
    val isSubmitting: LiveData<Boolean> = _isSubmitting

    private var fileCaseOngoingJob: Job? = null
    private val _mediaEncode = MutableLiveData<MediaTransfer?>(null)

    val isCaseFileSuccessfully = LiveEvent<Boolean>()

    val errorMessage = LiveEvent<String>()
    val fileUploadErrorMessage = LiveEvent<Int>()

    init {
        getReportCategories()
    }

    val isMaximumFilesUploaded = _proofFiles.map { it.size == 5 }

    private fun getReportCategories() {
        viewModelScope.launch(Dispatchers.IO){
            when(val result = repository.getReportCategories()){
                is ResultOf.Success -> {
                    _reportCategories.postValue(result.value.categories)
                }
                is ResultOf.APIError -> { }
                is ResultOf.Error -> { }
            }
        }
    }

    fun getUsersList(keyword: String) {
        userSearchingJob?.cancel()
        userSearchingJob = viewModelScope.launch(Dispatchers.IO) {
            delay(timeMillis = 400)
            when (val result: ResultOf<DealsBeneficiaryListResponse> = businessRepository.getBeneficiaryList(keyword =  keyword, page = "1", membershipFilter = null)) {
                is ResultOf.Success -> {
                    _usersList.postValue(result.value.users)
                }

                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    fun setSelectedUser(user: DealsBeneficiary?) {
        _selectedUser.postValue(user)
    }

    val isSubmitButtonEnabled : MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun checkInputsValid() {
            med.postValue(
                _selectedUser.value?.id != null &&
                !indicementText.value.isNullOrEmpty() &&
                !_proofFiles.value.isNullOrEmpty() &&
                _selectedCategory.value != null
            )
        }
        med.addSource(_selectedUser) { checkInputsValid() }
        med.addSource(indicementText) { checkInputsValid() }
        med.addSource(_selectedCategory) { checkInputsValid() }
        med.addSource(_proofFiles) { checkInputsValid() }
        med
    }

    fun setCategory(category: ReportCategory?) {
        _selectedCategory.value = category
    }

    private fun MutableLiveData<List<ReportProofMedia>>.add(file: ReportProofMedia) {
        val tempList = value?.toMutableList() ?: mutableListOf()
        if (tempList.find { it.uriString == file.uriString } != null) return
        tempList.add(file)
        postValue(tempList)
    }

    private fun MutableLiveData<List<ReportProofMedia>>.remove(file: ReportProofMedia) {
        val tempList = value?.toMutableList() ?: mutableListOf()
        tempList.remove(file)
        postValue(tempList)
    }

    fun addProofFile(uri: Uri) {
        val contentResolver = getApplication<MainApplication>().contentResolver
        val mime = MediaUtils.getMimeTypeFromUri(uri,contentResolver).orEmpty()
        val meta = MediaUtils.getFileNameAndSizeFromUri(uri, contentResolver)
        /*
        By default, the system grants your app access to media files until the device is restarted or until your app stops.
        If your app performs long-running work, such as uploading a large file in the background,
        you might need this access to be persisted for a longer period of time
         */
        try {
            contentResolver.takePersistableUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        } catch (_: Exception) {
        }
        viewModelScope.launch(Dispatchers.IO) {
            _proofFiles.add(
                ReportProofMedia(
                    uri = uri,
                    name = meta?.first.orEmpty(),
                    mimeType = mime
                )
            )
        }
    }

    fun deleteFile(file: ReportProofMedia) {
        _proofFiles.remove(file)
    }

    fun submitCase() {
        viewModelScope.launch(Dispatchers.IO) {
            _isSubmitting.postValue(true)

            val request = FileCaseRequest(
                categoryId = _selectedCategory.value?.categoryId,
                userId = _selectedUser.value?.id,
                reportedId = "${_selectedUser.value?.id ?: 0}",
                reason = indicementText.value
            )

            val proofs = _proofFiles.value.orEmpty()
            proofs.forEach {
                val uiModel = proofMedia.value?.find { pm -> pm.proof.uuid == it.uuid }
                uiModel?.processing = true
                if (!it.isProcessed) {
                    try {
                        Log.w("RPF", "process media: $it")
                        processMedia(it)
                    } catch(e: Exception) {
                        uiModel?.processing = false
                        return@launch
                    }
                }
                if (it.mediaUploaded) return@forEach

                Log.w("RPF", "upload media: $it")
                when(repository.uploadMedia(it)) {
                    is ResultOf.Success -> { }
                    else -> {
                        fileUploadErrorMessage.postValue(R.string.default_file_upload_failed)
                        uiModel?.processing = false
                        _isSubmitting.postValue(false)
                        return@launch
                    }
                }
                uiModel?.processing = false
            }
            if (!proofs.all { it.isProcessed } || !proofs.all { it.mediaUploaded }) {
                fileUploadErrorMessage.postValue(R.string.default_file_upload_failed)
                _isSubmitting.postValue(false)
                return@launch
            }
            request.proofFiles = proofs.map { it.s3UploadMedia.key }
            when(val result = repository.fileCase(req = request)) {
                is ResultOf.Success -> {
                    profileRepository.getAccountDetails()
                    isCaseFileSuccessfully.postValue(true)
                }
                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                }
                is ResultOf.Error -> {

                }
            }
            _isSubmitting.postValue(false)
        }
    }

    private suspend fun processMedia(med: ReportProofMedia) = withContext(Dispatchers.IO) {
        fileCaseOngoingJob?.let {
            fileCaseOngoingJob = null
            if (it.isActive) it.cancelAndJoin()
        }
        launch {
            try {
                val transfer = MediaTransfer(med.uuid)
                _mediaEncode.postValue(transfer)
                if (med.processedFile != null) return@launch
                med.processedFile = when (med.mediaType) {
                    MediaType.IMAGE -> processImage(med)
                    MediaType.VIDEO -> processVideo(med, object : VideoEncoderUtil.MediaProcessingListener {
                        override fun onProgress(progress: Int) {
                            Log.d("ENCODE", "Progress: $progress%")
                            transfer.progress = progress
                        }

                        override fun onProcessingFinished(success: Boolean) {
                            Log.d("ENCODE", "Encode Done ($success) as per VM")
                        }
                    })

                    else -> null
                }
                _mediaEncode.postValue(null)
            } catch (e: Throwable) {
                Log.w("ENCODE", "ProcessVideo cancelled", e)
                Firebase.crashlytics.recordException(e)
                fileCaseOngoingJob = null
                _mediaEncode.postValue(null)
                throw Exception("media processing cancelled")
            }
        }.apply {
            fileCaseOngoingJob = this
            join()
        }
    }

    private suspend fun processVideo(med: ReportProofMedia, listener: VideoEncoderUtil.MediaProcessingListener): File = withContext(Dispatchers.IO) {

        Firebase.crashlytics.log("Starting Video compress")
        Log.d("ENCODE", "Starting Video compress")
        try {
            val result = repository.processVideo(med, listener)
            Log.d("ENCODE", "processed ${med.uri} to ${result.path}")
            Firebase.crashlytics.log("processed ${med.uri} to ${result.path}")
            return@withContext result
        } catch (e: Throwable) {
            Log.w("ENCODE", "ProcessVideo cancelled", e)
            Firebase.crashlytics.recordException(e)
            throw Exception("Video processing cancelled")
        }
    }

    private suspend fun processImage(med: ReportProofMedia): File = withContext(Dispatchers.IO) {
        Firebase.crashlytics.log("Starting image compress")
        Log.d("ENCODE", "Starting image compress")
        try {
            val imageFile = repository.storeImageUriToTempFile(med.uri)
            val result = repository.compressImage(imageFile)
            Log.d("ENCODE", "processed ${med.uri} to ${result.path}")
            Firebase.crashlytics.log("processed ${med.uri} to ${result.path}")
            return@withContext result
        } catch (e: Throwable) {
            Log.w("ENCODE", "Process image cancelled", e)
            Firebase.crashlytics.recordException(e)
            throw Exception("image processing cancelled")
        }
    }

    private val _mediaUpload: LiveData<List<MediaTransfer>?> = MediaUploadListener.uploadProgressFlow.map { prog ->
        val proofs = _proofFiles.value.orEmpty().map { it.uuid }
        return@map prog.filter { it.key in proofs }.map {
            MediaTransfer(it.key).apply {
                progress = it.value
            }
        }
    }.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val proofMedia: LiveData<List<ReportViewModel.ProofMediaUIModel>> by lazy {
        val med = MediatorLiveData<List<ReportViewModel.ProofMediaUIModel>>()
        fun update() {
            val _encode = _mediaEncode.value
            val uploads = _mediaUpload.value
            val current = proofMedia.value

            med.postValue(_proofFiles.value?.map { proof ->
                val uiModel = current?.find { ui -> ui.proof.uuid == proof.uuid }?: ReportViewModel.ProofMediaUIModel(proof)
                val encode = if (_encode?.messageId==proof.uuid) _encode else null
                val upload = uploads?.find { it.messageId == proof.uuid }
                uiModel.encodeProgress = encode?.progress?:-1
                uiModel.uploadProgress = upload?.progress?:-1
                Log.d("RPF", "proofMedia: ${uiModel.proof.name} | processing: ${uiModel.processing} | isProcessed: ${uiModel.proof.isProcessed} | encode: ${uiModel.encodeProgress} | uploaded: ${uiModel.proof.mediaUploaded} | upload: ${uiModel.uploadProgress} | total: ${uiModel.totalProgress}")
                return@map uiModel
            })
        }
        med.addSource(_proofFiles) { update() }
        med.addSource(_mediaEncode) { update() }
        med.addSource(_mediaUpload) { update() }
        med.distinctUntilChanged()
    }
}