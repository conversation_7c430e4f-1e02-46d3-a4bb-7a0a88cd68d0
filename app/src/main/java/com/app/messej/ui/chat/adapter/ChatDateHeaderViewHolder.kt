package com.app.messej.ui.chat.adapter

import com.app.messej.databinding.ItemChatMessageDateHeaderBinding
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.utils.DateFormatHelper

class ChatDateHeaderViewHolder(val binding: ItemChatMessageDateHeaderBinding) : ChatAdapter.ChatViewHolder(binding.root) {
    override fun bind(item: ChatMessageUIModel) = with(binding) {
        val date = item as ChatMessageUIModel.DateSeparatorModel
        chatDate.text = DateFormatHelper.humanizeChatDate(date.date,this.root.context)
    }
}