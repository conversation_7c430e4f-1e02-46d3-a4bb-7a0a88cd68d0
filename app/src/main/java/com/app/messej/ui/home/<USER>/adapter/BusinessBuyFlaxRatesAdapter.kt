package com.app.messej.ui.home.businesstab.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.databinding.ItemBuyFlaxListsBinding
import com.app.messej.ui.home.businesstab.BuyCoinsViewModel
import com.app.messej.ui.home.businesstab.BuyCoinsViewModel.FlaxRatesAndProductDetails

class BusinessBuyFlaxRatesAdapter(
    private val buyFlaxRates: List<BuyCoinsViewModel.FlaxRatesAndProductDetails>,
    val listener: ActionListener,
    val isBuyCoinVisible: Boolean,
    var selectedItem: FlaxRatesAndProductDetails?
) : RecyclerView.Adapter<BusinessBuyFlaxRatesAdapter.BusinessBuyFlaxViewHolder>() {

    private var selectedPosition: Int = -1

    interface ActionListener {
        fun InAppClickListener(item: BuyCoinsViewModel.FlaxRatesAndProductDetails)
        fun itemClickListener(item: FlaxRatesAndProductDetails, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = BusinessBuyFlaxViewHolder(
        ItemBuyFlaxListsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    override fun onBindViewHolder(holder: BusinessBuyFlaxViewHolder, position: Int) {
        val item = buyFlaxRates[position]
        holder.bind(item)
    }

    inner class BusinessBuyFlaxViewHolder(private val binding: ItemBuyFlaxListsBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: BuyCoinsViewModel.FlaxRatesAndProductDetails) = with(binding) {
            binding.apply {
                buyFlaxRate = item
                isSelected = selectedItem == item
                isBuyCoin = isBuyCoinVisible
                materialCardView.setOnClickListener {
                    listener.itemClickListener(item, bindingAdapterPosition)
                }
            }
//            binding.btnBuyNow.setOnClickListener {
//                Log.d("PurchaseDATAAA","data adapter Selected ${item}")
//                    listener.InAppClickListener(item)
//            }
        }
    }

    fun updateSelectedItem(newSelectedItem: FlaxRatesAndProductDetails?, position: Int) {
        selectedItem = newSelectedItem
        setSelectedPosition(position = position)
    }

    private fun setSelectedPosition(position: Int) {
        if (selectedPosition != position) {
            val previousSelection = selectedPosition
            selectedPosition = position
            notifyItemChanged(previousSelection)
            notifyItemChanged(position)
        }
    }

    override fun getItemCount(): Int {
        return buyFlaxRates.size
    }

}
