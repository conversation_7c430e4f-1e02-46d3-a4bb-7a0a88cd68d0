package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.AbstractUser
import com.app.messej.databinding.ItemPodiumPrizeContributorBinding

class PodiumCreateSpeakersAdapter(
    private val inflater: LayoutInflater,
    private var speakers: MutableList<AbstractUser>,
) : RecyclerView.Adapter<PodiumCreateSpeakersAdapter.ActiveSpeakerViewHolder>() {

    override fun getItemCount(): Int {
        return speakers.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ActiveSpeakerViewHolder {
        return ActiveSpeakerViewHolder(ItemPodiumPrizeContributorBinding.inflate(inflater, parent, false))
    }

    override fun onBindViewHolder(holder: ActiveSpeakerViewHolder, position: Int) {
        holder.bind(speakers[position])
    }

    inner class ActiveSpeakerViewHolder(val binding: ItemPodiumPrizeContributorBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(activeSpeaker: AbstractUser) {
            binding.apply {
                contributor = activeSpeaker
            }
        }
    }

    fun updateData(newSpeakers: List<AbstractUser>) {
        val diffResult = DiffUtil.calculateDiff(SpeakersDiffCallback(speakers, newSpeakers))

        speakers.clear()
        speakers.addAll(newSpeakers)

        diffResult.dispatchUpdatesTo(this)
    }

    class SpeakersDiffCallback(
        private val oldList: List<AbstractUser>,
        private val newList: List<AbstractUser>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].id == newList[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition]
        }
    }
}