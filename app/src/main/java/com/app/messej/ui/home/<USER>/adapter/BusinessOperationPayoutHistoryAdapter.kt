package com.app.messej.ui.home.businesstab.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.BusinessPayoutHistory
import com.app.messej.data.model.enums.PointsHistoryStatus
import com.app.messej.databinding.ItemOperationPayoutHistoryBinding

class BusinessOperationPayoutHistoryAdapter :
    PagingDataAdapter<BusinessPayoutHistory, BusinessOperationPayoutHistoryAdapter.BusinessPayoutHistoryViewHolder>(DearsDiff) {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        BusinessPayoutHistoryViewHolder(
            ItemOperationPayoutHistoryBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: BusinessPayoutHistoryViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class BusinessPayoutHistoryViewHolder(private val binding: ItemOperationPayoutHistoryBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: BusinessPayoutHistory) = with(binding) {
            model = item
            binding.color = statusColor(item,binding.root.context)
            binding.status = payoutStatus(item,binding.root.context)
//            binding.fieldRequestedPp.text= setPpValue(item)
        }
    }

    object DearsDiff : DiffUtil.ItemCallback<BusinessPayoutHistory>() {
        override fun areItemsTheSame(oldItem: BusinessPayoutHistory, newItem: BusinessPayoutHistory) =
            oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: BusinessPayoutHistory, newItem: BusinessPayoutHistory) =
            oldItem == newItem
    }

    fun statusColor(item: BusinessPayoutHistory, context: Context): Int {
        return when (item.status) {
            PointsHistoryStatus.PENDING.type -> {
                ResourcesCompat.getColor(context.resources, android.R.color.holo_orange_dark, null)
            }
            PointsHistoryStatus.REJECTED.type -> {
                ResourcesCompat.getColor(context.resources, android.R.color.holo_red_dark, null)
            }
            else -> {
                ResourcesCompat.getColor(context.resources, android.R.color.holo_green_dark, null)
            }
        }
    }

    fun payoutStatus(item: BusinessPayoutHistory, context: Context): String {
        return when (item.status) {
            PointsHistoryStatus.PENDING.type -> {
                context.resources.getString(R.string.business_payout_history_status_pending)
            }
            PointsHistoryStatus.REJECTED.type -> {
                context.resources.getString(R.string.business_payout_history_status_rejected)
            }
            else -> {
                context.resources.getString(R.string.business_payout_history_status_approved)
            }
        }
    }

    fun setPpValue(item: BusinessPayoutHistory): String {
        return "${item.requestedPointsForReview} PP"
    }
}