package com.app.messej.ui.home.publictab.huddles.poll

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.api.poll.Answer
import com.app.messej.data.model.api.poll.PollVisibilityModel
import com.app.messej.data.model.enums.PollType
import com.app.messej.databinding.ItemPollProgressBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class ItemAnswerPollAdapter(data: MutableList<Answer>, pollsType:PollType): BaseQuickAdapter<Answer, BaseDataBindingHolder<ItemPollProgressBinding>>(R.layout.item_poll_progress, data) {
    private lateinit var _pollVisibility:PollVisibilityModel

    init {
        _pollVisibility = when (pollsType){
            PollType.SCHEDULE_POLL -> {
                PollVisibilityModel(showPercentage = true, showCount = true, showRadioButton = false, isAnswerSelected = false,isEdit = true)
            }

            PollType.PAST_POLL -> {
                PollVisibilityModel(showPercentage = true, showCount = true, showRadioButton = false, isAnswerSelected = false,isEdit = true)
            }

            else -> { PollVisibilityModel(showPercentage = true, showCount = true, showRadioButton = false, isAnswerSelected = false,isEdit = false)}
        }
    }


    object PollDiff : DiffUtil.ItemCallback<Answer>() {
        override fun areItemsTheSame(oldItem: Answer, newItem: Answer): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: Answer, newItem: Answer): Boolean {
            return oldItem.id == newItem.id
        }

    }


    override fun convert(holder: BaseDataBindingHolder<ItemPollProgressBinding>, item: Answer) {
        holder.dataBinding?.apply {
            answer = item
            //pollVisibility = _pollVisibility
        }

    }

}
