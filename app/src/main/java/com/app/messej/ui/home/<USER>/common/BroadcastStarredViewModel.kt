package com.app.messej.ui.home.publictab.common

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AttachLocation
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.entity.BroadcastChatMessageWithMedia
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.enums.BroadcastAction
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.repository.BroadcastRepository
import com.app.messej.data.socket.repository.BroadcastEventRepository
import com.app.messej.ui.chat.BaseChatViewModel
import com.app.messej.ui.chat.ChatMessageUIModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BroadcastStarredViewModel(application: Application): BaseChatViewModel(application)  {

    private val broadcastRepo = BroadcastRepository(getApplication())
    private val eventRepo = BroadcastEventRepository

    private val socketRepo = BroadcastEventRepository

    private val _broadcasterId = MutableLiveData<Int?>(null)

    private val _broadcasterImage = MutableLiveData<String?>(null)
    val broadcasterImage: LiveData<String?> = _broadcasterImage

    private val _broadcastMode = MutableLiveData<BroadcastMode?>()
    val broadcastMode: LiveData<BroadcastMode?> = _broadcastMode


    fun setBroadcastMode(mode: BroadcastMode) {
        if (_broadcastMode.value==mode) return
        accountRepo.user.apply {
            _broadcasterId.value = id
            _broadcasterImage.value = profile.thumbnail
        }
        _broadcastMode.value = mode
    }

    fun setIncomingBroadcastMode(broadcaster: Int) {
        viewModelScope.launch(Dispatchers.IO){
            val star = profileRepo.getUserStar(broadcaster)
            withContext(Dispatchers.Main) {
                _broadcasterImage.postValue(star?.thumbnail)
                _broadcasterId.value = broadcaster
                _broadcastMode.value = null
            }
        }
    }

    override val showChats = MutableLiveData<Boolean>(true)

    override val _chatList: LiveData<PagingData<ChatMessageUIModel>> = _broadcastMode.switchMap {
        val broadcaster = _broadcasterId.value?: return@switchMap null
        val pager = if (it == null) broadcastRepo.getIncomingStarredBroadcastPager(broadcaster) else broadcastRepo.getOutgoingStarredBroadcastPager(it)

        return@switchMap pager.liveData.map { pagingData: PagingData<BroadcastChatMessageWithMedia> ->
            pagingData.map { msg ->
                ChatMessageUIModel.ChatMessageModel(msg, false)
            }
                .setShowName().insertDateSeparators()
        }.cachedIn(viewModelScope)
    }


    override fun getSenderForReply(msg: AbstractChatMessage): SenderDetails? {
        return null
    }

    private suspend fun performMessageUpload(msg: BroadcastChatMessageWithMedia) = chatRepo.sendBroadcastMessage(msg)

    val canForwardSelection = _selectedChats.map { it.isNotEmpty() && it.all { ch -> ch.messageType == AbstractChatMessage.MessageType.TEXT } }
    val actionIsStar = _selectedChats.map {
        it.all { chat -> !(chat as BroadcastMessage).starred }
    }

    val onStarAction = LiveEvent<BroadcastAction>()

    fun toggleStar() {
        val star = actionIsStar.value?:return
        val list = _selectedChatsList.map{ it as BroadcastMessage }.filter {
            it.starred != star
        }
        if (list.isEmpty()) return
        viewModelScope.launch {
            eventRepo.onStarAction.take(1).collect {
                onStarAction.postValue(if (it.starred) BroadcastAction.STAR else BroadcastAction.UNSTAR)
            }
        }
        eventRepo.starBroadcastMessage(list,star)
        exitSelectionMode()
    }

    fun unstarAll() {
        viewModelScope.launch {
            eventRepo.onStarAction.take(1).collect {
                onStarAction.postValue(if (it.starred) BroadcastAction.STAR else BroadcastAction.UNSTAR)
            }
        }
        eventRepo.unstarAllBroadcastMessages(_broadcasterId.value!!,_broadcastMode.value)
    }

    val onMessageForwarded = LiveEvent<List<BroadcastMode>>()
    val onCannotForward = LiveEvent<Boolean>()

    fun confirmForward(modes: List<BroadcastMode>) {
        _selectedChatsList.forEach {
            val message = it as BroadcastMessage
            viewModelScope.launch(Dispatchers.IO) {
                if(!chatRepo.checkIfCanForwardBroadcast(message)) {
                    onCannotForward.postValue(true)
                    return@launch
                }
                modes.forEach { mode ->
                    val msg =  chatRepo.cloneBroadcastMessage(mode, message)
                    performMessageUpload(msg)
                }
                withContext(Dispatchers.Main) {
                    exitSelectionMode()
                }
                onMessageForwarded.postValue(modes)
            }
        }
    }

    override val typingListener = object: TypingListener {
        override fun onTyping(typing: Boolean) {
            // No need to send event
        }
    }

    override suspend fun sendMessage(message: String, media: TempMedia?, replyTo: ReplyTo?, location: AttachLocation?, color: ChatTextColor?): AbstractChatMessage? {
        return null
    }

    override fun onTriggerUpload(msg: AbstractChatMessageWithMedia) {
    }

    override val deleteTimeout = accountRepo.getAccountDetailsFlow().map {
        it?.broadcastDeleteTimeoutInSeconds
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = null
    )

    override fun likeItem(item: AbstractChatMessage) {
    }

    fun deleteSelection(forEveryone: Boolean) {
        val list = _selectedChatsList.map{ it as BroadcastMessage }
        if (list.isEmpty()) {
            exitSelectionMode()
            return
        }
        eventRepo.deleteBroadcastMessage(list,forEveryone)
        exitSelectionMode()
    }


}