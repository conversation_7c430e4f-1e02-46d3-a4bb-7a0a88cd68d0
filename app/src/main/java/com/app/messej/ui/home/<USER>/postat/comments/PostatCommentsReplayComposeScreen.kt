package com.app.messej.ui.home.publictab.postat.comments

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import com.app.messej.R
import com.app.messej.data.model.api.postat.PostComment
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.flash.comments.CommentsListener
import com.app.messej.ui.home.publictab.flash.comments.CommentsReplySingleItem
import com.app.messej.ui.home.publictab.flash.comments.CommentsSingleItem
import com.app.messej.ui.utils.FragmentExtensions.showToast


@Composable
fun PostatCommentsRepliesScreen(viewModel: PostatCommentsViewModel, listener: CommentsListener) {
    val postatComments = viewModel.commentList.collectAsLazyPagingItems()

    val context= LocalContext.current
    val scroll = rememberNestedScrollInteropConnection()
    val state = rememberLazyListState()

    var selectedComment by remember { mutableStateOf<String?>(null) }

    LaunchedEffect(Unit) {
        viewModel.conNewCommendAdded.collect {
            selectedComment=null
            postatComments.refresh()
            state.animateScrollToItem(index = 0)
        }
    }
    LaunchedEffect(Unit) {
        viewModel.replyDeleted.collect {
            selectedComment=null
            context.showToast(context.getString(R.string.comment_deleted))
            postatComments.refresh()
        }
    }
    LazyColumn(
        state = state,
        modifier = Modifier.fillMaxWidth().nestedScroll(scroll),
        verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
        contentPadding = PaddingValues(vertical = dimensionResource(id = R.dimen.activity_margin))
    ) {
        items(postatComments.itemCount, key = {
            val item = postatComments[it]
            val dataModel = (item as ChatMessageUIModel.PostCommentWithRepliesModel)
            val data = dataModel.comment as PostComment
            data.commentId
        }
        ) {
            var showReplies by remember { mutableStateOf(false) }

            var visibleRepliesCount by remember { mutableStateOf(3) }

            val item = postatComments[it]
            val dataModel = (item as ChatMessageUIModel.PostCommentWithRepliesModel)
            val data = dataModel.comment as PostComment
            val replies = dataModel.reply


            val alpha  = if (selectedComment==data.commentId||selectedComment==null) 1f else 0.3f
            Column(modifier = Modifier.fillMaxWidth()) {
                CommentsSingleItem(
                    onSelectedItemClick = {
                        selectedComment = if (selectedComment == data.commentId) null else data.commentId
                    },

                    comment = data, flag = dataModel.countryFlag, currentUser = viewModel.user.id, selectedItem = selectedComment, listener = listener

                )
                if (showReplies) {
                    val visibleReplies = replies.take(visibleRepliesCount)

                    visibleReplies.forEach { reply ->
                        val flag = reply.senderDetails?.countryCode?.let { viewModel.getFlag(it) }

                        CommentsReplySingleItem(
                            replyComment = reply,
                            comment = data,
                            onSelectedItemClick = {
                                selectedComment = if (selectedComment == reply.commentId) null else reply.commentId
                            },
                            flag = flag,
                            isFlash = false,
                            currentUser = viewModel.user.id,
                            listener = listener,
                            selectedItem = selectedComment,
                            onDeleted = {
                                showReplies = false
                            }
                        )
                    }
                }

                if (data.totalReplies != 0 && data.senderDetails?.deletedAccount == false) {
                    Text(
                        modifier = Modifier
                            .alpha(alpha)
                            .padding(start = 38.dp, top = 10.dp)
                            .clickable {
                                if (selectedComment?.isNotEmpty() == true) return@clickable

                                if (!showReplies) {
                                    showReplies = true
                                    viewModel.callReplies(data.commentId)
                                } else {
                                    // Increase visible count if not fully loaded
                                    if (visibleRepliesCount < replies.size) {
                                        visibleRepliesCount += 3
                                    } else {
                                        showReplies = false
                                        visibleRepliesCount = 3 // reset if needed
                                    }
                                }
                            },
                        text = when {
                            !showReplies -> stringResource(R.string.reply_view_more, data.totalReplies.toString())
                            visibleRepliesCount < (data.totalReplies?:0) -> stringResource(R.string.reply_view_more, "${data.totalReplies?.minus(visibleRepliesCount)}")
                            else -> stringResource(R.string.reply_hide_replies)
                        },
                        style = FlashatComposeTypography.overLineSmaller,
                        color = colorResource(id = R.color.textColorPrimary)
                    )
                }

            }
        }
    }

}
