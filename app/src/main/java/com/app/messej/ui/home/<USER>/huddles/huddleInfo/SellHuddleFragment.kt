package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.graphics.Paint
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentSellHuddleBinding
import com.app.messej.databinding.LayoutSellHuddleConfirmCancelBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SellHuddleFragment : Fragment() {
    private lateinit var binding: FragmentSellHuddleBinding
    private val viewModel: SellHuddleViewModel by viewModels()
    private val args: SellHuddleFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_sell_huddle, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = if (args.isForSale) getString(R.string.title_edit_sell_huddle) else getString(R.string.title_sell_huddle)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
        setUp()
    }

    companion object {
        const val SELL_HUDDLE_SUCCESS = "sell_huddle_success"
        const val SELL_HUDDLE_CREATE_MODE = "sell_huddle_create_mode"
    }

    private fun setUp() {
        viewModel.setHuddleId(args.huddleId, args.isForSale, args.huddlePrice)
        Log.d("HUDDLEPRICE",""+args.huddlePrice)
        binding.textLearnMore.paintFlags = binding.textLearnMore.paintFlags or Paint.UNDERLINE_TEXT_FLAG


        binding.textAckHuddleSell.apply {
            val clickablePart = getString(R.string.huddle_selling_procedure)
            val fullText = getString(R.string.text_huddle_sell_ack)
            text = fullText.highlightOccurrences(clickablePart) {
                object : ClickableSpan() {
                    override fun onClick(widget: View) {
                        val documentType = DocumentType.HUDDLE_SELLING_PROCEDURE
                        findNavController().navigateSafe(
                            SellHuddleFragmentDirections.actionGlobalPolicyFragment(documentType, false)
                        )
                    }
                }
            }
            movementMethod = LinkMovementMethod.getInstance()

            binding.actionSellHuddle.setOnClickListener {
                if (args.isForSale) viewModel.sellHuddle(true) else viewModel.sellHuddle(false)
                hideKeyboard()
            }

            val messageSpan = resources.getString(R.string.label_sell_huddle_flax_amount).highlightOccurrences("*") {
                ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.colorBusinessRed))
            }
            binding.labelFlaxAmount.text = messageSpan

            binding.textLearnMore.setOnClickListener {
                findNavController().navigateSafe(SellHuddleFragmentDirections.actionSellHuddleFragmentToOfferYourHuddleFragment())
            }
            binding.actionCancelSaleOffer.setOnClickListener {
                MaterialDialog(requireContext()).show {
                    val view = DataBindingUtil.inflate<LayoutSellHuddleConfirmCancelBinding>(layoutInflater, R.layout.layout_sell_huddle_confirm_cancel, null, false)
                    view.viewModel = viewModel
                    customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                    cancelable(false)
                    view.nickNameTitle.text = resources.getString(R.string.cancel_sell_huddle)
                    view.actionConfirm.setOnClickListener {
                        viewModel.cancelSaleOffer()
                        dismiss()
                    }
                    view.actionCancel.setOnClickListener {
                        dismiss()
                    }
                }
            }

        }
    }

    private fun observe() {

        viewModel.sellAmountError.observe(viewLifecycleOwner) {
            binding.textEnterFlaxAmount.error = when (it) {


                SellHuddleViewModel.Companion.SellAmountError.NONE -> {
                    binding.textEnterFlaxAmount.isErrorEnabled = false
                    null
                }

                SellHuddleViewModel.Companion.SellAmountError.LIMIT_EXCEEDS -> {
                    binding.textEnterFlaxAmount.isErrorEnabled = true
                    getString(R.string.maximum_limit_reached)
                }

                null -> {
                    binding.textEnterFlaxAmount.isErrorEnabled = false
                    null
                }
            }
        }




        viewModel.showSuccess.observe(viewLifecycleOwner) {
            lifecycleScope.launch {

                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
                delay(1000)
                findNavController().popBackStack()
                setFragmentResult(
                    SELL_HUDDLE_CREATE_MODE, bundleOf(
                        SELL_HUDDLE_SUCCESS to args.huddleId
                    )
                )
            }
        }
        viewModel.errorMessage.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }
    }
}