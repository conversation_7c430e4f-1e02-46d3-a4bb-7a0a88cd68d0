package com.app.messej.ui.auth.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterUpgradeSuperstarBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class RegisterUpgradeSuperstarFragment : Fragment() {

    private lateinit var binding: FragmentRegisterUpgradeSuperstarBinding

    private val viewModel: RegisterUpgradeSuperstarViewModel by navGraphViewModels(R.id.navigation_register_superstar)

    private val args: RegisterUpgradeSuperstarFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding  =  DataBindingUtil.inflate(inflater, R.layout.fragment_register_upgrade_superstar, container, false)
        binding.apply {
            viewModel = viewModel
            lifecycleOwner = viewLifecycleOwner
            name = args.name
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        binding.laterButton.setOnClickListener {
//            requireActivity().recreate()
            findNavController().navigateSafe(RegisterUpgradeSuperstarFragmentDirections.actionGlobalHomeFragment())
        }

        binding.upgradeButton.setOnClickListener {
            // TODO need to verify how the completion for this works
            findNavController().navigateSafe(RegisterUpgradeSuperstarFragmentDirections.actionGlobalUpgradePremiumFragment())

        }
    }

    private fun observe() {

    }

}