package com.app.messej.ui.home.publictab.authorities

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentAuthoritiesStandAloneBinding
import com.app.messej.databinding.FragmentBaseAuthoritiesBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class AuthoritiesStandAloneFragment : AuthoritiesBaseFragment() {

    override lateinit var binding: FragmentBaseAuthoritiesBinding
    private lateinit var outerBinding: FragmentAuthoritiesStandAloneBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        outerBinding = DataBindingUtil.inflate(
            inflater, R.layout.fragment_authorities_stand_alone, container, false
        )
        outerBinding.lifecycleOwner = viewLifecycleOwner
        binding = outerBinding.authoritiesCommonLayout
        return outerBinding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(outerBinding.customActionBar.toolbar)
        outerBinding.customActionBar.toolbar.title = getString(R.string.home_private_tab_authorities)
    }

    override fun onPresidentialAffairsClick() {
    }

    override fun onLegalAffairsClick() {
        findNavController().navigateSafe(
            AuthoritiesStandAloneFragmentDirections.actionGlobalLegalAffairsFragment()
        )
    }

    override fun onSocialWelfareClick() {

    }

    override fun onStateAffairsClick() {
        findNavController().navigateSafe(
        AuthoritiesStandAloneFragmentDirections.actionAuthoritiesFragmentToStateAffairsFragment())
    }

    override fun onNavigateToLegalAffairsClick() {
        findNavController().navigateSafe(
            AuthoritiesStandAloneFragmentDirections.actionGlobalLegalAffairsFragment()
        )
    }
}