package com.app.messej.ui.home.publictab.flash.myflash

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.FlashReportedComment
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class ReportedCommentsViewModel(application: Application): AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val flashRepo = FlashRepository(getApplication())
    protected val profileRepo = ProfileRepository(application)

    val user: CurrentUser get() = accountRepo.user

    private val viewState = MutableStateFlow<MultiStateView.ViewState?>(MultiStateView.ViewState.LOADING)

    val debouncedViewState = viewState.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).distinctUntilChanged()

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    val reportedCommentsList = flashRepo.getReportedCommentsPager().liveData.cachedIn(viewModelScope)

    fun setViewState(state: MultiStateView.ViewState) {
        viewState.value = state
    }

    val onDeleteComment = LiveEvent<Boolean>()

    fun deleteReportedComment(comment: FlashReportedComment) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = flashRepo.deleteComment(comment.commentId)) {
                is ResultOf.Success -> {
                    delay(500)
                    onDeleteComment.postValue(true)
                }
                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }
            }
        }
    }
}