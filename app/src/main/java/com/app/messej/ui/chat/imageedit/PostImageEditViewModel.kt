package com.app.messej.ui.chat.imageedit

import android.app.Application
import android.net.Uri
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.repository.ChatRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class PostImageEditViewModel(application: Application) :AndroidViewModel(application){

    var destinationFile: File? = null
    private val chatRepo = ChatRepository(getApplication())

    enum class StrokeWidth {
        ONE, TWO, THREE
    }

    private val _strokeWidth = MutableLiveData(StrokeWidth.ONE)
    val strokeType: LiveData<StrokeWidth> = _strokeWidth

    fun setStrokeType(strokeWidth: StrokeWidth) {
        _strokeWidth.postValue(strokeWidth)
    }

    fun getDestinationFile(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            destinationFile = file
        }

    }



}