package com.app.messej.ui.home.gift.bottomSheet

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.app.messej.data.model.socket.FlaxTransferPayload

class ReceivedFlaxBottomSheetViewModel(application: Application) : AndroidViewModel(application) {


    private val _actionDownArrow = MutableLiveData<Boolean>(false)
    val actionDownArrow: LiveData<Boolean> = _actionDownArrow

    private val _flaxTransfer = MutableLiveData<FlaxTransferPayload?>()
    val flaxTransfer: MutableLiveData<FlaxTransferPayload?> = _flaxTransfer
    init {
        // Initialize the action state to closed
        _actionDownArrow.postValue(false)

    }
    fun setVisibility() {
        _actionDownArrow.value = !(_actionDownArrow.value ?: false)
    }

    fun setArgs(value: FlaxTransferPayload?) {
        _flaxTransfer.postValue(value)
    }

}