package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.HuddleRequestsAdminActionRequest
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ManageRequestInviteViewModel(application: Application): AndroidViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())

    val showCompactLoading = MutableLiveData(false)

    private val huddleID = MutableLiveData<Int?>(null)

    fun setHuddleId(id: Int) {
        huddleID.postValue(id)
        getCounts(id)
    }

    val huddleRequestList = huddleID.switchMap {
        it ?: return@switchMap null
        huddleRepo.getHuddleRequestsList(huddleId = huddleID.value!!) { count ->
            _requestCount.postValue(count)
        }.liveData.cachedIn(viewModelScope)
    }

    val huddleInvitationsList = huddleID.switchMap {
        it ?: return@switchMap null
        huddleRepo.getHuddleInvitesList(huddleId = huddleID.value!!) { count ->
            _inviteCount.postValue(count)
        }.liveData.cachedIn(viewModelScope)
    }

    private val _requestCount = MutableLiveData<Int>(0)
    val requestCount: LiveData<Int> = _requestCount

    private val _inviteCount = MutableLiveData<Int>(0)
    val inviteCount: LiveData<Int> = _inviteCount

    private val _requestsAdminActionError = MutableLiveData<String?>(null)
    val requestsAdminActionError: LiveData<String?> = _requestsAdminActionError

    private fun getCounts(huddleId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            val inviteRes = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleInvites(huddleId, page = 1)
            _inviteCount.postValue(inviteRes.body()?.result?.total?:0)
//            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getHuddleInvites(huddleId, page = 1)
//            _inviteCount.postValue(response.body()?.result?.total?:0)
        }
    }

    fun blockUnblockUser(memberId: Int, action: Participant.ParticipantsActionTypes) {
        viewModelScope.launch(Dispatchers.IO) {
            val list: ArrayList<Int> = arrayListOf()
            list.add(memberId)
            when (val result = huddleRepo.blockOrUnblockHuddleUser(huddleId = huddleID.value!!, action = action, userList = list)) {
                is ResultOf.Success -> {
//                    searchTerm.postValue("")
                    setHuddleId(huddleID.value!!)
                }
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
            }
        }
    }

    fun blockJoinRequest(memberId: Int){
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.blockHuddleJoinRequest(huddleId = huddleID.value!!, memberId)) {
                is ResultOf.Success -> {
                    setHuddleId(huddleID.value!!)
                }
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
            }
        }
    }

    fun huddleRequestsAdminAction(memberId: Int, action: HuddleRequestsAdminActionRequest.RequestsAdminAction) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.huddleRequestsAdminAction(huddleId = huddleID.value!!, action, memberId)){
                is ResultOf.Success -> {
                    setHuddleId(huddleID.value!!)
                }
                is ResultOf.APIError -> {
                    _requestsAdminActionError.postValue(result.error.message)
                }
                is ResultOf.Error -> {

                }
            }
        }
    }

    fun cancelInvitation(memberId: Int){
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.cancelHuddleInvitation(huddleId = huddleID.value!!, memberIds = arrayListOf(memberId))){
                is ResultOf.Success -> {
                    setHuddleId(huddleID.value!!)
                }
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
            }
        }
    }
}