package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentRestoreRatingHistoryBinding
import com.app.messej.ui.home.businesstab.adapter.DealsRestoreRatingHistoryPagerAdapter
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.kennyc.view.MultiStateView

class RestoreRatingHistoryFragment : Fragment() {
    private lateinit var binding: FragmentRestoreRatingHistoryBinding
    private val viewModel: BusinessDealsListViewModel by viewModels()
    private var restoreAdapter: DealsRestoreRatingHistoryPagerAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_restore_rating_history, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()

    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.history_restore_rating)
    }

    fun setUp() {
        initAdapter()
    }

    fun observe() {
        viewModel.restoreRatingHistoryList.observe(viewLifecycleOwner) {
            restoreAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun initAdapter() {
        restoreAdapter = DealsRestoreRatingHistoryPagerAdapter(viewModel.user.id)
        val layoutManParticipant = LinearLayoutManager(context)

        binding.rvRestoreRatingHistory.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = restoreAdapter
        }

        restoreAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

//        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
//            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_no_flix_purchase_history)
//            findViewById<AppCompatTextView>(R.id.eds_empty_message).text= "No Restore History found"
//
//        }
    }

}