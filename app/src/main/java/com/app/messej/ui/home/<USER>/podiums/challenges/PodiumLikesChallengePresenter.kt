package com.app.messej.ui.home.publictab.podiums.challenges

import android.content.res.ColorStateList
import android.graphics.Color
import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.ItemPodiumSpeakerChallengeDecorLikesBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardLikesBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardLikesScoreBinding
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.transitionToText
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import kotlinx.coroutines.launch
import kotlin.math.max
import kotlin.math.roundToInt

class PodiumLikesChallengePresenter(holder: ViewGroup, challenge: PodiumChallenge, challengeListener: ChallengeEventListener): PodiumChallengePresenter(holder, challenge, challengeListener) {

    override lateinit var liveBinding: LayoutPodiumChallengeBoardLikesBinding

    override val challengeTitle: Int
        get() = R.string.podium_challenge_likes

    override fun setupView() {
        liveBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_likes, holder, false)
    }

    override fun decorateSpeakerTile(item: ActiveSpeakerUIModel, holder: ViewGroup, mainScreen: Boolean): Boolean {
        if (mainScreen) return false
        if (!challenge.running) return false
        Log.w("PCP", "decorateSpeakerTile: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}",)
        val sc = scores.find { it.id == item.speaker.id } ?: return false
        val overlay = DataBindingUtil.inflate<ItemPodiumSpeakerChallengeDecorLikesBinding>(layoutInflater, R.layout.item_podium_speaker_challenge_decor_likes, holder, false)
        try {
            val color = Color.parseColor(sc.color)
            overlay.likeIcon.imageTintList = ColorStateList.valueOf(color)
            overlay.likeIconClone.imageTintList = ColorStateList.valueOf(color)
        } catch (_: Exception) {}
        holder.removeAllViews()
        holder.addView(overlay.root)
        return true
    }

    override fun onSpeakerClick(item: ActiveSpeakerUIModel, view: View, mainScreen: Boolean): Boolean {
        Log.w("PCP", "onSpeakerClick: ${item.speaker.name} | mainscreen: $mainScreen")
        if (mainScreen) return false
        if(!challenge.running) return false
        if(challengeListener.getLiveViewModel().updateChallengeScore(item.speaker.id)) {
            try {
                Log.w("PCP", "onSpeakerClick: ${item.speaker.name} | animating heart")
                val icon = view.findViewById<AppCompatImageView>(R.id.like_icon_clone)
                pulseAnimation(icon)
            } catch (e: Exception) {}
        }
        return true
    }

    override fun cleanup() {
        super.cleanup()
        liveScoreBindings = mutableListOf()
    }

    override fun onNewScoresAvailable(forceRefresh: Boolean) {
        Log.w("PCP", "onNewScoresAvailable: $forceRefresh | ${scores.map { it.score }}", )
        if (forceRefresh) refreshLiveUI()
        else refreshScores()
    }

    private var liveScoreBindings: MutableList<Pair<Int,LayoutPodiumChallengeBoardLikesScoreBinding>> = mutableListOf()

    override fun refreshLiveUI() {
        Log.w("PCP", "refreshLiveUI: $challenge", )
        tickerJob?.cancel()
        Log.d("PCP", "tickerJob canceled in: refreshLiveUI")
        scope.launch {
            liveBinding.apply {
                timer.text = DateTimeUtils.formatSeconds(challenge.duration ?: 0)
                title.text = resources.getString(R.string.podium_challenge_likes)

                content.isVisible = false
                scoreHolder.isVisible = false
            }

            Log.w("PCP", "refreshLiveUI: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}", )

            if (challenge.hasStarted) {
                suspend fun finalize() {
                    liveBinding.scoreHolder.isVisible = false
                    liveBinding.content.transitionToText(resources.getString(R.string.podium_challenge_finalizing))
                }
                if (!challenge.gameOver) {
                    liveBinding.scoreHolder.apply {
                        removeAllViews()
                        liveScoreBindings = mutableListOf()
                        scores.forEach { sc ->
                            Log.w("PCP", "refreshLiveUI: adding score $sc")
                            val b = DataBindingUtil.inflate<LayoutPodiumChallengeBoardLikesScoreBinding>(layoutInflater, R.layout.layout_podium_challenge_board_likes_score, this, false)
                            b.progress.max = 100
                            b.progress.min = 0
                            try {
                                b.progress.setIndicatorColor(Color.parseColor(sc.color))
                            } catch (_: Exception) {}
                            addView(b.root)
                            liveScoreBindings.add(Pair(sc.id, b))
                        }
                        refreshScores()
                        challengeListener.onRequireSpeakerTileDecor()
                        isVisible = true
                    }
                    liveBinding.timer.showChallengeTimer {
                        finalize()
                        challengeListener.onRequireSpeakerTileDecor()
                    }
                } else {
                    finalize()
                }
            } else {
                liveBinding.content.isVisible = true
                liveBinding.content.showStartCountdown {
                    refreshLiveUI()
                }
            }
        }
    }

    private fun refreshScores() {
        val maxScore = max(5.0,scores.maxOfOrNull { it.score }?:0.0)
        scores.forEach { sc ->
            val b =liveScoreBindings.find { it.first == sc.id }?.second?: return@forEach
            b.score.text = sc.score.toInt().toString()
            val progress = sc.score.toFloat()/ maxScore*100
            b.progress.setProgress(progress.roundToInt(),true)
        }
    }

    override fun getExtraTextForWinnerCard(winner: PodiumChallengeScore): String {
        return resources.getString(R.string.podium_challenge_winner_likes, winner.score.toInt().toString())
    }

    override fun getExtraTextForWinnerTicker(winners: List<PodiumChallengeScore>): String {
        return resources.getString(R.string.podium_challenge_winner_ticker_likes, winners[0].score.toInt().toString())
    }

    override fun convertScore(sc: PodiumChallengeScore): String {
        return sc.score.toInt().toString()
    }
}