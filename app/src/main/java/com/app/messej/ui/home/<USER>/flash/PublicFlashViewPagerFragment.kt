package com.app.messej.ui.home.publictab.flash

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.FragmentNavigator
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.databinding.LayoutPublicFlashBinding
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PublicFlashViewPagerFragment: PublicFlashBaseFragment() {

    override lateinit var binding: LayoutPublicFlashBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_public_flash, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun toPlayer(pos: Int, extras: FragmentNavigator.Extras) {
        findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToFlashPlayerFragment(viewModel.currentTab.value?: return),extras)
    }

    override fun toSearch() {
        findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToFlashSearchFragment())
    }

    override fun toMyFlash() {
        findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToMyFlashFragment())
    }

}