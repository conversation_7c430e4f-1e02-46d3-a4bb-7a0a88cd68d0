package com.app.messej.ui.auth.profile

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.SelectableCountry
import com.app.messej.data.model.SelectableUser
import com.app.messej.data.model.User
import com.app.messej.data.model.api.profile.SetSuperstarRequest
import com.app.messej.data.model.api.profile.SuperstarSuggestionResponse
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.auth.common.GenderUIPackage
import com.app.messej.ui.utils.CountryListUtil
import com.hadilq.liveevent.LiveEvent
import com.hbb20.CCPCountry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import me.xdrop.fuzzywuzzy.FuzzySearch

@OptIn(FlowPreview::class)
class RegisterSuperstarViewModel(application: Application) : AndroidViewModel(application) {


    private var profileRepo: ProfileRepository = ProfileRepository(application)

    private val _dataLoading = MutableLiveData(false)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _suggestionResponse = MutableLiveData<SuperstarSuggestionResponse?>(null)

    private val _suggestions: MutableLiveData<MutableList<User>?> = MutableLiveData(null)

    private val _selectedSuperstar: MutableLiveData<User?> = MutableLiveData(null)
    val selectedSuperstar: LiveData<User?> = _selectedSuperstar

    val onSuperstarPreselected = LiveEvent<Boolean>()

    init {
        viewModelScope.launch {
            _suggestions.asFlow().collect {
                it?.find { user -> user.isReferrer }?.let { referrer ->
                    selectSuperstar(referrer)
                    onSuperstarPreselected.postValue(true)
                }
            }
        }
    }

    private val _selectableSuggestions: MediatorLiveData<List<SelectableUser>> by lazy {
        val med = MediatorLiveData<List<SelectableUser>>(listOf())
        fun combineListWithSelection(bringSelectionToTop: Boolean = false) {
            val sel = _selectedSuperstar.value
            var selFound = false
            val list = _suggestions.value.orEmpty().map {
                val select = it.id == sel?.id
                if (select) selFound = true
                return@map SelectableUser(it, select)
            }.toMutableList()
            if (sel != null && !selFound && list.isNotEmpty()) {
                // selected ID not found in current list. clearing selection
                Log.d("SUP", "combineListWithSelection: selection cleared")
                _selectedSuperstar.postValue(null)
                // no need to post this value as mediator will be triggered once more
            } else {
                if (bringSelectionToTop) list.sortByDescending { it.selected }
                _selectableSuggestions.postValue(list)
            }
        }
        med.addSource(_suggestions) { combineListWithSelection(true) }
        med.addSource(_selectedSuperstar) { combineListWithSelection() }
        med
    }
    val selectableSuggestions: LiveData<List<SelectableUser>> = _selectableSuggestions

    private val _suggestionError = MutableLiveData<String?>(null)
    val suggestionError: LiveData<String?> = _suggestionError

    fun triggerInitialSuggestions() {
        if (_suggestionResponse.value != null || _dataLoading.value == true || _moreDataLoading.value == true) return
        getSuggestions(false)
    }

    fun triggerResearch() {
        resetSuggestions()
        getSuggestions(false)
    }

    fun loadMoreSuggestions() {
        val resp = _suggestionResponse.value ?: return
        if (!resp.nextPage) {
            return
        }
        getSuggestions(true)
    }

    private fun resetSuggestions() {
        _suggestions.value = null
        _suggestionResponse.value = null
        onResetSuggestions.value = true
    }

    private fun getSuggestions(loadMore: Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {
            var page = 1
            val search = if (searchTerm.value.isNullOrEmpty()) null else searchTerm.value
            val countries = _selectedCountries.value?.map { it.englishName }
            val genders = _selectedGenders.value?.map { it.code }
            if (loadMore) {
                val resp = _suggestionResponse.value ?: return@launch
                page = resp.currentPage + 1
                _moreDataLoading.postValue(true)
            } else {
                // reset suggestions
                _dataLoading.postValue(true)
            }
            when (val result: ResultOf<SuperstarSuggestionResponse> = profileRepo.getSuperstarSuggestions(
                page = page, keyword = search, countries = countries, genders = genders
            )) {
                is ResultOf.Success -> {
                    val list = _suggestions.value ?: mutableListOf()
                    list.addAll(result.value.users)
                    withContext(Dispatchers.Main) {
                        _suggestionResponse.value = result.value
                        _suggestions.value = list
                        onLoadPage.value = result.value
                    }
                }
                is ResultOf.APIError -> {
                    if (loadMore) onLoadMoreError.postValue(result.error.message) else _suggestionError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            if (loadMore) _moreDataLoading.postValue(false) else _dataLoading.postValue(false)
        }
    }

    fun selectSuperstar(user: User) {
        _selectedSuperstar.postValue(user)
    }

    private val _moreDataLoading = MutableLiveData<Boolean>(false)
    val moreDataLoading: LiveData<Boolean> = _moreDataLoading

    val onResetSuggestions = LiveEvent<Boolean>()
    val onLoadPage = LiveEvent<SuperstarSuggestionResponse?>()
    val onLoadMoreError = LiveEvent<String>()

    val searchTerm = MutableLiveData<String?>(null)

    private val _selectedCountries = MutableLiveData<List<CCPCountry>>(listOf())
    val selectedCountries: LiveData<List<CCPCountry>> = _selectedCountries

    private val _selectedGenders = MutableLiveData<List<GenderUIPackage>>(listOf())
    val selectedGenders: LiveData<List<GenderUIPackage>> = _selectedGenders

    private val _showAllFilters = MutableLiveData(false)
    val showAllFilters: LiveData<Boolean> = _showAllFilters

    fun removeCountryFilter(c: CCPCountry) {
        val new = _selectedCountries.value.orEmpty().toMutableList()
        new.removeAll { it.nameCode == c.nameCode }
        _selectedCountries.value = new
        triggerResearch()
    }

    fun removeGenderFilter(g: GenderUIPackage) {
        val new = _selectedGenders.value.orEmpty().toMutableList()
        new.removeAll { it.code == g.code }
        _selectedGenders.value = new
        triggerResearch()
    }

    fun toggleFilterShowAll(show: Boolean) {
        _showAllFilters.postValue(show)
    }

    // Filters

    private val _countryList = MutableLiveData<List<CCPCountry>>(listOf())

    init {
        viewModelScope.launch {
            searchTerm.asFlow().debounce(1000L).collect {
                it ?: return@collect
                triggerResearch()
            }
        }

        val data = CountryListUtil.getCustomCountryList()
        _countryList.postValue(data)
    }

    private val _temporaryCountrySelection = MutableLiveData<List<CCPCountry>>(listOf())
    val temporaryCountrySelection: LiveData<List<CCPCountry>> = _temporaryCountrySelection

    private val _temporaryGenderSelection = MutableLiveData<List<GenderUIPackage>>(listOf())
    val temporaryGenderSelection: LiveData<List<GenderUIPackage>> = _temporaryGenderSelection

    private val _countryListWithSelection: MediatorLiveData<List<SelectableCountry>> by lazy {
        val med = MediatorLiveData<List<SelectableCountry>>(listOf())
        med.addSource(_countryList) { updateCountriesWithSelection() }
        med.addSource(_temporaryCountrySelection) { updateCountriesWithSelection() }
        med
    }

    private fun updateCountriesWithSelection() {
        val sel = _temporaryCountrySelection.value.orEmpty()
        val sc = _countryList.value.orEmpty().map { country ->
            return@map SelectableCountry(
                obj = country, selected = sel.find { it.nameCode == country.nameCode } != null
            )
        }.toMutableList()
        _countryListWithSelection.postValue(sc)
    }

    val countryFilterSearchTerm = MutableLiveData("")

    private val _filteredCountryList: MediatorLiveData<List<SelectableCountry>> by lazy {
        val med = MediatorLiveData<List<SelectableCountry>>(listOf())
        med.addSource(_countryListWithSelection) { filterCountryList() }
        med.addSource(countryFilterSearchTerm) { filterCountryList(true) }
        med
    }
    val filteredCountryList: LiveData<List<SelectableCountry>> = _filteredCountryList

    private fun filterCountryList(sort: Boolean = false) {
        val search = countryFilterSearchTerm.value.orEmpty().lowercase()
        var fl = _countryListWithSelection.value.orEmpty()
        if (search.isNotBlank()) {
            fl = fl.filter {
                it.obj.name.lowercase().contains(search) || FuzzySearch.ratio(it.obj.name, search) > 60
            }.sortedByDescending {
                if (it.obj.name.lowercase().contains(search)) 101 else FuzzySearch.ratio(it.obj.name, search)
            }
        } else if (sort) {
            fl = fl.sortedByDescending { it.selected }
        }
        _filteredCountryList.postValue(fl)
    }

    fun setupTempFilters() {
        _temporaryCountrySelection.value = _selectedCountries.value
        _temporaryGenderSelection.value = _selectedGenders.value
    }

    fun clearAllFilters() {
        _temporaryCountrySelection.value = listOf()
        _temporaryGenderSelection.value = listOf()
        _selectedCountries.value = _temporaryCountrySelection.value
        _selectedGenders.value = _temporaryGenderSelection.value
        isAllSelected.postValue(false)
    }

    fun applyFilters() {
        countryFilterSearchTerm.value = ""
        _selectedCountries.value = _temporaryCountrySelection.value
        _selectedGenders.value = _temporaryGenderSelection.value
        viewModelScope.launch {
            delay(500)
            triggerResearch()
        }
    }

    val isAllSelected = LiveEvent<Boolean>()

    fun toggleCountrySelection(pos: Int) {
        val list = _filteredCountryList.value.orEmpty().toMutableList()
        val country = list.getOrNull(pos) ?: return
        val sel = _temporaryCountrySelection.value.orEmpty().toMutableList()

        val exists = sel.find { it.nameCode == country.obj.nameCode }
        if (exists != null) {
            sel.remove(exists)
            isAllSelected.postValue(false)
        } else {
            sel.add(country.obj)
            if(sel.size == _countryList.value?.size) {
                isAllSelected.postValue(true)
            }
        }
        _temporaryCountrySelection.postValue(sel)
    }

    fun selectAllCountries(select: Boolean) {
        val sel = mutableListOf<CCPCountry>()
        if(select) {
            sel.addAll(_countryList.value.orEmpty().toMutableList())
        }
        _temporaryCountrySelection.postValue(sel)
    }

    fun checkAllCountriesSelected() {
        if(_temporaryCountrySelection.value?.size == _countryList.value?.size) {
            isAllSelected.postValue(true)
        }
    }

    fun toggleGenderSelection(gender: GenderUIPackage) {
        val sel = _temporaryGenderSelection.value.orEmpty().toMutableList()
        val exists = sel.find { it.code == gender.code }
        if (exists != null) {
            sel.remove(exists)
        } else {
            sel.add(gender)
        }
        _temporaryGenderSelection.postValue(sel)
    }

    // Validation and Next

    val superstarStageValid = _selectedSuperstar.map {
        return@map it != null
    }

    private val _setSuperstarLoading = MutableLiveData(false)
    val setSuperstarLoading: LiveData<Boolean> = _setSuperstarLoading

    private val _setSuperstarError = MutableLiveData<String?>(null)
    val setSuperstarError: LiveData<String?> = _setSuperstarError

    val onSetSuperstarComplete = LiveEvent<Boolean>()

    fun setSuperstar() {
        _setSuperstarLoading.postValue(true)
        _setSuperstarError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            if (superstarStageValid.value != true) return@launch
            val req = SetSuperstarRequest(_selectedSuperstar.value!!.id)
            when (val result: ResultOf<CurrentUser> = profileRepo.setSuperstar(req)) {
                is ResultOf.Success -> {
                    onSetSuperstarComplete.postValue(true)
                }
                is ResultOf.APIError -> {
                    _setSuperstarError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _setSuperstarLoading.postValue(false)
        }
    }

}