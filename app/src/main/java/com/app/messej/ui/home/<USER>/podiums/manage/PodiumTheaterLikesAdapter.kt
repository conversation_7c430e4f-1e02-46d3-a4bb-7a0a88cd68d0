package com.app.messej.ui.home.publictab.podiums.manage

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import com.app.messej.databinding.ItemPodiumLikesCountBinding

class PodiumTheaterLikesAdapter : PagingDataAdapter<PodiumMaidanSupporter, PodiumTheaterLikesAdapter.PodiumTheaterLikesViewHolder>(DiffCallback) {

    inner class PodiumTheaterLikesViewHolder(private val binding: ItemPodiumLikesCountBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumMaidanSupporter) = with(binding) {
            speaker = item
        }
    }

    object DiffCallback : DiffUtil.ItemCallback<PodiumMaidanSupporter>() {
        override fun areItemsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: PodiumMaidanSupporter,
            newItem: PodiumMaidanSupporter,
        ): Boolean {
            return oldItem.likes == newItem.likes
        }
    }

    override fun onBindViewHolder(holder: PodiumTheaterLikesAdapter.PodiumTheaterLikesViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumTheaterLikesViewHolder {
        val binding = ItemPodiumLikesCountBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumTheaterLikesViewHolder(binding)
    }
}