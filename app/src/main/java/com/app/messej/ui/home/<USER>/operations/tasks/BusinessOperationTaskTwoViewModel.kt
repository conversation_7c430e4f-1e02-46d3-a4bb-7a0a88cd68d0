package com.app.messej.ui.home.businesstab.operations.tasks

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import kotlinx.coroutines.launch

class BusinessOperationTaskTwoViewModel(application: Application) :AndroidViewModel(application){

    private val accountRepo = AccountRepository(application)
    private var businessRepo: BusinessRepository = BusinessRepository(application)
    val businessOperation: LiveData<BusinessOperation?> = businessRepo.getOperations()

    private val _user = MutableLiveData<CurrentUser>()
    val user: LiveData<CurrentUser> = _user
    init {
        getUserAccount()
        viewModelScope.launch {
            businessRepo.getFlashAtActivityDetails()
        }
    }

    private fun getUserAccount() {
        viewModelScope.launch {
            _user.postValue(accountRepo.user)
        }
    }
}