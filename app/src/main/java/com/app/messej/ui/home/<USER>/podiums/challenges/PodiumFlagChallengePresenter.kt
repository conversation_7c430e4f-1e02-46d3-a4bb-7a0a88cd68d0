package com.app.messej.ui.home.publictab.podiums.challenges

import android.annotation.SuppressLint
import android.content.res.AssetFileDescriptor
import android.media.MediaPlayer
import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.data.model.api.podium.FlagChallengeQuestion
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.LayoutPodiumChallengeBoardFlagsBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardFlagsOptionBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardFlagsQuestionBinding
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeOutIn
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.transitionToText
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

class PodiumFlagChallengePresenter(holder: ViewGroup, challenge: PodiumChallenge, challengeListener: ChallengeEventListener) : PodiumChallengePresenter(holder, challenge, challengeListener) {

    override lateinit var liveBinding: LayoutPodiumChallengeBoardFlagsBinding
    private var questions = listOf<FlagChallengeQuestion>()
    private var selectedAnswerID: Int? = null
    private lateinit var player: MediaPlayer

    companion object {
        const val QUESTION_DURATION_MILLIS = 20000L
        const val SHOW_QUESTION_FOR_MILLIS = 10000L
        const val NUMBER_OF_QUESTIONS = 15

        private fun Long.seconds() = this/1000

        fun getChallengeDurationSeconds(): Int = ((QUESTION_DURATION_MILLIS.seconds()*NUMBER_OF_QUESTIONS)-SHOW_QUESTION_FOR_MILLIS.seconds()).toInt()
    }

    override val challengeTitle: Int
        get() = R.string.podium_challenge_flags

    override fun setupView() {
        setupPlayer()
        liveBinding = LayoutPodiumChallengeBoardFlagsBinding.inflate(layoutInflater, holder, false)
    }

    private fun setupPlayer() {
        val mp = MediaPlayer()
        val afd: AssetFileDescriptor = resources.openRawResourceFd(R.raw.flag_challenege_audio) ?: return
        mp.setDataSource(afd)
        afd.close()
        mp.prepare()
        mp.start()
        mp.isLooping=true
        player = mp
    }

    fun onFlagChallengeQuestions(q: List<FlagChallengeQuestion>) {
        questions = q
        if (uiState==UIState.LIVE) {
            refreshChallengeUI(challenge.status)
        }
    }

    override fun cleanup() {
        super.cleanup()
        stopMusic()
    }

    private fun startMusic() {
        liveBinding.micVisible = true
        if (!musicMuted) {
            player.let {
                if (!it.isPlaying) {
                    it.seekTo(0)
                    it.start()
                }
            }
        }
    }

    private var musicMuted = false

    private fun toggleMute() {
        if (musicMuted) {
            player.start()
        } else player.pause()
        liveBinding.muted = !musicMuted
        musicMuted = !musicMuted
    }

    private fun stopMusic() {
        liveBinding.micVisible = false
        try {
            if (player.isPlaying) {
                player.stop()
            }
            player.release()
        } catch (e: IllegalStateException) {
            e.printStackTrace()
        }
    }

    override fun onNewScoresAvailable(forceRefresh: Boolean) {
    }

    override fun refreshLiveUI() {
        scope.launch {
            liveBinding.apply {
                timerHolder.visibility = View.INVISIBLE
                title.text = resources.getString(R.string.podium_challenge_flags)
                content.isVisible = false
                questionsHolder.isVisible = false
                questionCount.text = ""
                muted = musicMuted
                muteButton.setOnClickListener {
                    toggleMute()
                }
            }

            challengeListener.getLiveViewModel().apply {
                if (iAmMuted.value==false) {
                    muteToggleSelf()
                }
            }

            Log.w("PCP", "${this@PodiumFlagChallengePresenter} refreshLiveUI: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}", )

            if (challenge.hasStarted) {
                suspend fun finalize() {
                    selectedAnswerID = -1
                    liveBinding.questionsHolder.isVisible = false
                    liveBinding.content.transitionToText(resources.getString(R.string.podium_challenge_finalizing))
                }
                if (!challenge.gameOver) {
                    startMusic()
                    val index = showQuestion()
                    Log.w("PCP", "${this@PodiumFlagChallengePresenter} refreshLiveUI: question timer done $index" )
                    // This should ideally run after the flow
                    if (index==null) {}
                    else if (index>=(questions.size-1)) {
                        stopMusic()
                        finalize()
                    } else {
                        refreshLiveUI()
                    }
                } else {
                    stopMusic()
                    finalize()
                }
            } else {
                liveBinding.questionsHolder.isVisible = false
                liveBinding.content.isVisible = true
                liveBinding.timer.text = DateTimeUtils.formatSeconds(challenge.duration ?: 0)
                tickerJob = liveBinding.content.showStartCountdown {
                    refreshLiveUI()
                }
                Log.d("PCP", "tickerJob set in RefreshLiveUI: PFCP")
            }
        }
    }

    private suspend fun showQuestion(): Int? {
        liveBinding.questionsHolder.removeAllViews()
        liveBinding.questionsHolder.isVisible = true
        var elapsedMs = 0L
        var timeElapsedForQuestionMs = 0L

        fun calculateElapsed(): Long? {
            elapsedMs = challenge.liveTimeElapsed?.toMillis()?: return null
            timeElapsedForQuestionMs = elapsedMs % QUESTION_DURATION_MILLIS
            return elapsedMs
        }

        Log.w("PCP", "showQuestion: liveTimeElapsed - ${challenge.liveTimeElapsed?.seconds}")

        calculateElapsed()?: return null

        Log.w("PCP", "showQuestion: elapsed - $elapsedMs, for question: $timeElapsedForQuestionMs")

        val questionIndex = (elapsedMs/ QUESTION_DURATION_MILLIS).toInt()
        val question = questions.getOrNull(questionIndex)?: return null
        Log.w("PCP", "${this@PodiumFlagChallengePresenter} showQuestion: total elapsed - $elapsedMs, question - $questionIndex, elapsed: $timeElapsedForQuestionMs", )

        @SuppressLint("SetTextI18n")
        liveBinding.questionCount.text = "${questionIndex+1}/$NUMBER_OF_QUESTIONS"
        liveBinding.timerHolder.visibility = View.VISIBLE
        if (timeElapsedForQuestionMs< SHOW_QUESTION_FOR_MILLIS) {

            coroutineScope {
                launch {
                    liveBinding.content.fadeOutIn(liveBinding.questionsHolder)
                }
            }
            val b = DataBindingUtil.inflate<LayoutPodiumChallengeBoardFlagsQuestionBinding>(layoutInflater, R.layout.layout_podium_challenge_board_flags_question, liveBinding.questionsHolder, false)
            b.question = question
            challengeListener.getLiveViewModel().getCountryFlag(question.countryCode)?.let {

                b.countryFlag.setImageResource(it)
            }
            b.selectedId = null
            b.isParticipant = iAmParticipant

            fun LayoutPodiumChallengeBoardFlagsOptionBinding.bindOption(opt: FlagChallengeQuestion.FlagChallengeOption) {
                button.setOnClickListener {
                    selectedAnswerID = opt.answerId
                    selectedId = opt.answerId
                    b.selectedId=opt.answerId
                }
            }
            b.optionOne.bindOption(question.options[0])
            b.optionTwo.bindOption(question.options[1])
            b.optionThree.bindOption(question.options[2])
            b.optionFour.bindOption(question.options[3])
            liveBinding.questionsHolder.addView(b.root)

            calculateElapsed()
            val timeLeftForQuestionMs = SHOW_QUESTION_FOR_MILLIS - timeElapsedForQuestionMs
            Log.w("PCP", "${this@PodiumFlagChallengePresenter} showQuestion after q tr: total elapsed - $elapsedMs, question - $questionIndex, elapsed: $timeElapsedForQuestionMs, left: $timeLeftForQuestionMs", )

            liveBinding.timer.showChallengeTimer(remainingMs = timeLeftForQuestionMs, 5) {
                selectedAnswerID?.let {
                    challengeListener.getLiveViewModel().setAnswer(question.countryCode, it)
                }
                selectedAnswerID = null
            }.join()
        }
        if (questionIndex == (NUMBER_OF_QUESTIONS-1)) {
            return questionIndex
        }

        liveBinding.questionsHolder.isVisible = false
        liveBinding.content.isVisible = true
        coroutineScope {
            launch {
                liveBinding.questionsHolder.fadeOutIn(liveBinding.content) { outV, inV ->
                    outV.isVisible = false
                    inV.isVisible = true
                    liveBinding.content.text = resources.getString(R.string.podium_challenge_flags_preparing)
                }
            }
        }
        liveBinding.content.text = resources.getString(R.string.podium_challenge_flags_preparing)
        calculateElapsed()
        liveBinding.timer.showChallengeTimer(remainingMs = QUESTION_DURATION_MILLIS - timeElapsedForQuestionMs, -1) {

        }.join()
//        delay((QUESTION_DURATION_IN_SECONDS - timeElapsedForQuestion)*1000)

        return questionIndex
    }

    override suspend fun showResultUI() {
        stopMusic()
        super.showResultUI()
    }

    override fun getExtraTextForWinnerCard(winner: PodiumChallengeScore): String {
        return "${winner.score.toInt()}/$NUMBER_OF_QUESTIONS"
    }

    override fun getExtraTextForWinnerTicker(winners: List<PodiumChallengeScore>): String {
        return "${winners[0].score.toInt()}/$NUMBER_OF_QUESTIONS"
    }

}