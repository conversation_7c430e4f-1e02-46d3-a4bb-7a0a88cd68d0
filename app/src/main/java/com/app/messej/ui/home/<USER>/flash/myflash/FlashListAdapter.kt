package com.app.messej.ui.home.publictab.flash.myflash

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.databinding.ItemFlashListMiniBinding

class FlashListAdapter(private val listener: <PERSON><PERSON>lickListener,private val showSenderInsteadOfViews: Boolean = false) : PagingDataAdapter<FlashVideoUIModel, FlashListAdapter.FlashListViewHolder>(FeedsDiff) {

    interface FlashClickListener {
        fun onFlashClicked(pos: Int, flash: FlashVideoUIModel)
        fun onFlashLongPressed(pos: Int, flash: FlashVideoUIModel, view: View)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = FlashListViewHolder(
        ItemFlashListMiniBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun onBindViewHolder(holder: <PERSON>ListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class FlashListViewHolder(private val binding: ItemFlashListMiniBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: FlashVideoUIModel) = with(binding) {
            flash = item
            showSender = showSenderInsteadOfViews
            flashCard.setOnLongClickListener{
                Log.d("FlashListAdapter", "bind: Long press on my flash")
                listener.onFlashLongPressed(bindingAdapterPosition, item, it)
                true
            }
            flashCard.setOnClickListener {
                listener.onFlashClicked(bindingAdapterPosition,item)
            }
        }
    }


    object FeedsDiff : DiffUtil.ItemCallback<FlashVideoUIModel>() {
        override fun areItemsTheSame(oldItem: FlashVideoUIModel, newItem: FlashVideoUIModel): Boolean {
            return oldItem.flashVideo.id == newItem.flashVideo.id
        }

        override fun areContentsTheSame(oldItem: FlashVideoUIModel, newItem: FlashVideoUIModel): Boolean {
            return oldItem == newItem
        }
    }
}