package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.ReportedTab
import com.app.messej.databinding.FragmentBaseReportedBinding
import com.google.android.material.button.MaterialButton

class HuddleReportsFragment : Fragment() {

    private lateinit var binding: FragmentBaseReportedBinding
    private lateinit var mPagerAdapter: FragmentStateAdapter

    val viewModel : ReportedMessagesViewModel by viewModels()

    private val args: HuddleReportsFragmentArgs by navArgs()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_base_reported, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
        val actionBar = (activity as MainActivity?)?.supportActionBar
        if(args.huddleType == HuddleType.PRIVATE) {
            actionBar?.title = getString(R.string.huddle_info_reported_messages_title_text)
        }
    }

    private fun setup() {

        binding.layoutTabs.isVisible = args.huddleType == HuddleType.PUBLIC
        viewModel.setHuddleId(args.huddleId,args.ReportedTab)

        mPagerAdapter = object : FragmentStateAdapter(childFragmentManager,viewLifecycleOwner.lifecycle) {
            override fun getItemCount(): Int = if (args.huddleType == HuddleType.PUBLIC) ReportedTab.values().size else 1

            override fun createFragment(position: Int): Fragment {
                return when(position) {
                    0 -> ReportedMessagesFragment().apply { arguments = ReportedMessagesFragment.getBundle(args.huddleId) }
                    1 -> ReportedCommentsFragment().apply { arguments = ReportedCommentsFragment.getBundle(args.huddleId) }
                    else -> throw java.lang.IllegalArgumentException("There should only be 2 tabs")
                }
            }
        }

        binding.reportedPager.apply {
            isUserInputEnabled = false
            adapter = mPagerAdapter
        }

        binding.btnMessages.setOnClickListener {
            viewModel.setCurrentTab(ReportedTab.TAB_MESSAGES)
            (it as MaterialButton).isChecked = true
        }

        binding.btnComments.setOnClickListener {
            viewModel.setCurrentTab(ReportedTab.TAB_COMMENTS)
            (it as MaterialButton).isChecked = true
        }
    }

    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            binding.reportedPager.setCurrentItem(it.ordinal,false)
        }
    }

}