package com.app.messej.ui.home.publictab.flash.myflash

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.databinding.FragmentFlashInnerBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureFlashPostingAllowed
import com.app.messej.ui.home.publictab.flash.myflash.MyFlashFragment.Companion.showFlashPostLimitingAlert
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView

class MyFlashDraftFragment : FlashListBaseFragment() {

    override val viewModel: MyFlashDraftViewModel by viewModels()

    private lateinit var binding: FragmentFlashInnerBinding
    private var apiLoader : MaterialDialog? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_inner, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override val multiStateView: MultiStateView
        get() = binding.multiStateView

    override val flashList: RecyclerView
        get() = binding.flashList

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
    }

    override fun LayoutListStateEmptyBinding.setEmptyView() {
        this.prepare(
            image = R.drawable.im_eds_my_flash,
            message = R.string.flash_drafts_eds
        )
    }

    override fun observe() {
        super.observe()

        viewModel.onFlashDeleted.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_action_draft_delete_toast)
        }
        viewModel.selectedCount.observe(viewLifecycleOwner) {
            binding.unsaveButton.text = getString(R.string.flash_discard_count_label,it.toString())
        }

        viewModel.isFlashEligibilityLoading.observe(viewLifecycleOwner) {
            if (it) showAPILoader() else hideAPILoader()
        }

        viewModel.flashEligibility.observe(viewLifecycleOwner) {
            Log.d("MFDRF", "FlashEligibility -> $it")
        }

        viewModel.flashDraftId.observe(viewLifecycleOwner) { flashId ->
            ensureFlashPostingAllowed {
                if (viewModel.flashEligibility.value?.flashEligibility == false) {
                    showFlashPostLimitingAlert (
                        citizenship = viewModel.user.citizenship,
                        enabledFlashCount = viewModel.flashEligibility.value?.enabledFlashCount,
                        onConfirmButtonClick = {
                            findNavController().navigateSafe(
                                NavGraphHomeDirections.actionGlobalUpgradePremiumFragment()
                            )
                        }
                    )
                    return@ensureFlashPostingAllowed
                }
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalFlashRecordFragment(flashId))
            }
        }
    }

    override fun onFlashClicked(flash: FlashVideo, pos: Int) {
        viewModel.getFlashEligibilityDetails(flash.id)
    }

    override val supportsSelectionMode: Boolean
        get() = true

    override fun showLongPressMenu(flash: FlashVideo, view: View) {
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_flash_draft_select, popup.menu)
        popup.setForceShowIcon(true)

        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_delete -> showRecordingDeleteAlert(1) {
                    viewModel.deleteFlash(flash)
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun showAPILoader() {
        apiLoader = showLoader()
        apiLoader?.show()
    }

    private fun hideAPILoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

    private fun showRecordingDeleteAlert(total: Int, onConfirm: () -> Unit) {
        MaterialAlertDialogBuilder(requireContext()).setTitle(resources.getQuantityString(R.plurals.flash_my_flash_draft_discard_confirm_title, total, total))
            .setMessage(resources.getQuantityString(R.plurals.flash_my_flash_draft_discard_confirm_message, total)).setPositiveButton(getText(R.string.common_delete)) { dialog, _ ->
                dialog.dismiss()
                onConfirm.invoke()
            }.setNegativeButton(getText(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }

    override fun setup() {
        super.setup()
        binding.unsaveButton.setOnClickListener {
            showRecordingDeleteAlert(viewModel.selectedCount.value?:0) {
                viewModel.deleteMultipleFlash()
            }
        }
    }
}