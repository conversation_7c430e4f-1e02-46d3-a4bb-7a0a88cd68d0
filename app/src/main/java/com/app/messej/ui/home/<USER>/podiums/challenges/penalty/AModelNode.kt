package com.app.messej.ui.home.publictab.podiums.challenges.penalty

import io.github.sceneview.math.Position
import io.github.sceneview.model.ModelInstance
import io.github.sceneview.node.ModelNode

internal class AModelNode(
    modelInstance: ModelInstance,
    autoAnimate: Boolean = true,
    scaleToUnits: Float? = null,
    centerOrigin: Position? = null,
    private val onAnimationEnd: AModelNode.(name:String) -> Unit = {}
) : ModelNode(modelInstance, autoAnimate, scaleToUnits, centerOrigin) {

    override fun onFrame(frameTimeNanos: Long) {
        val animationIndex = playingAnimations.entries.firstOrNull()?.key

        super.onFrame(frameTimeNanos)

        if (animationIndex!=null && playingAnimations.isEmpty()) {
            val animator = animator
            val duration = animator.getAnimationDuration(animationIndex)
            val preFinalAnimationTime = duration - 0.01f // or whatever is better for your animation
            animator.applyAnimation(animationIndex, preFinalAnimationTime)
            animator.updateBoneMatrices()
            onAnimationEnd.invoke(this,animator.getAnimationName(animationIndex))
        }
    }
}