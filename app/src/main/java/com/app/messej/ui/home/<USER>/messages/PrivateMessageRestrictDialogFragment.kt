package com.app.messej.ui.home.privatetab.messages

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.databinding.FragmentPrivateMessageRestrictDialogBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class PrivateMessageRestrictDialogFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentPrivateMessageRestrictDialogBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_private_message_restrict_dialog, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    companion object {
        const val CONFIRM_RESTRICT_KEY = "profile_chat_confirm_restrict"
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        clickListeners()
    }

    private fun clickListeners() {
        binding.actionCancel.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.actionRestrict.setOnClickListener {
            findNavController().popBackStack()
            setFragmentResult(
                CONFIRM_RESTRICT_KEY, bundleOf(
                    CONFIRM_RESTRICT_KEY to true
                )
            )
        }

    }
}