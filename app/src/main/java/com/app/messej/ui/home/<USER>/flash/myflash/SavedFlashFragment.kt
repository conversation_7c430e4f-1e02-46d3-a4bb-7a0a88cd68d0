package com.app.messej.ui.home.publictab.flash.myflash

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.databinding.FragmentFlashInnerBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class SavedFlashFragment : FlashListBaseFragment() {

    override val viewModel: SavedFlashViewModel by navGraphViewModels(R.id.nav_flash_saved)

    private lateinit var binding: FragmentFlashInnerBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_inner, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override val multiStateView: MultiStateView
        get() = binding.multiStateView

    override val flashList: RecyclerView
        get() = binding.flashList

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
    }

    override fun LayoutListStateEmptyBinding.setEmptyView() {
        this.prepare(
            image = R.drawable.im_eds_my_flash,
            message = R.string.flash_saved_eds
        )
    }

    override fun observe() {
        super.observe()

        viewModel.onFlashDeleted.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_unsaved)
        }
        viewModel.selectedCount.observe(viewLifecycleOwner) {
            binding.unsaveButton.text = getString(R.string.flash_unsave_count_label,it.toString())
        }
    }

    override fun onFlashClicked(flash: FlashVideo, pos: Int) {
        findNavController().navigateSafe(SavedFlashFragmentDirections.actionSavedFlashFragmentToSavedFlashPlayerFragment())
    }

    override val supportsSelectionMode: Boolean
        get() = true

    override fun showLongPressMenu(flash: FlashVideo, view: View) {
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_flash_draft_select, popup.menu)
        popup.setForceShowIcon(true)

        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_delete -> viewModel.deleteFlash(flash)
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    override fun setup() {
        super.setup()
        binding.unsaveButton.setOnClickListener {
            viewModel.deleteMultipleFlash()
        }
    }
}