package com.app.messej.ui.composeComponents

import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.data.model.enums.UserBadge

/**
Used for showing Premium, Verified Badge
 */
@Composable
fun UserBadge(modifier: Modifier, userType: UserBadge?, size: Dp = 11.dp) {
    val res = when (userType) {
        UserBadge.PREMIUM -> R.drawable.ic_user_badge_premium
        UserBadge.VERIFIED -> R.drawable.ic_user_badge_verified
        else -> null
    }
    res?.let {
        Icon(
            painter = painterResource(id = it),
            modifier = modifier.size(size = size),
            tint = Color.Unspecified,
            contentDescription = null
        )
    }
}