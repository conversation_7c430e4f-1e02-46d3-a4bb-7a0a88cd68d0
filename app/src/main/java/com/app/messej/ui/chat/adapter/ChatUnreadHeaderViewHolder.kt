package com.app.messej.ui.chat.adapter

import com.app.messej.databinding.ItemChatMessageUnreadHeaderBinding
import com.app.messej.ui.chat.ChatMessageUIModel

class ChatUnreadHeaderViewHolder(val binding: ItemChatMessageUnreadHeaderBinding) : ChatAdapter.ChatViewHolder(binding.root) {
    override fun bind(item: ChatMessageUIModel) = with(binding) {
        unread = item as ChatMessageUIModel.UnreadSeparatorModel
    }
}