package com.app.messej.ui.home.privatetab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.databinding.LayoutPrivateGroupsBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PrivateGroupsViewPagerFragment : PrivateGroupsBaseFragment() {

    override lateinit var binding: LayoutPrivateGroupsBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_private_groups, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun navigateToSearch() {
        findNavController().navigateSafe(HomePrivateFragmentDirections.actionHomePrivateFragmentToHomePrivateGroupSearchFragment())
    }

    override fun navigateToCreate() {
        findNavController().navigateSafe(HomePrivateFragmentDirections.actionGlobalCreateHuddleFragment(HuddleType.PRIVATE))
    }

    override fun navigateToChat(huddleId: Int) {
        findNavController().navigateSafe(HomePrivateFragmentDirections.actionGlobalNavigationChatGroup(huddleId))
    }

}