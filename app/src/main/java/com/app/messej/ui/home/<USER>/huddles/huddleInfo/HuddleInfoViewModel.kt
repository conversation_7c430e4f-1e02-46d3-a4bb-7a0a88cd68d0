package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.R
import com.app.messej.data.model.api.huddles.HuddleInfo
import com.app.messej.data.model.enums.HuddleAction
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class HuddleInfoViewModel(application: Application) : AndroidViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())
    private val accountRepo: AccountRepository = AccountRepository(application)
    private val huddleID = MutableLiveData<Int?>(null)
    private val profileRepo = ProfileRepository(application)

    val user = accountRepo.userFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    companion object {
        enum class NameError {
            NONE, LT_MIN, GT_MAX
        }
        private const val NAME_MIN_LENGTH = 3
        private const val NAME_MAX_LENGTH = 50
        private const val DEFAULT_LANGUAGE="english"
        const val HUDDLE_UPDATED = "huddle_updated"
    }

    fun setHuddleId(id: Int) {
        if (huddleID.value == id) return
        getHuddleInfo(id)
        huddleID.postValue(id)
    }

    val showCompactLoading = MutableLiveData(false)

    private val _huddleInfo = MutableLiveData<HuddleInfo?>(null)
    val huddle: LiveData<HuddleInfo?> = _huddleInfo

    val iAmMinister = user.map { it?.citizenship == UserCitizenship.MINISTER }
    val iAmPresident = user.map { it?.citizenship == UserCitizenship.PRESIDENT }


    private val ministerDeletableCitizenships = setOf(UserCitizenship.VISITOR, UserCitizenship.CITIZEN, UserCitizenship.RESIDENT)

    val isHuddleDeletableByMinister = huddle.map { it?.isManager == false && it?.managerDetails?.citizenship in ministerDeletableCitizenships }

    val showEnableRequestToJoin = huddle.map {
        it ?: return@map false
        !it.isPrivate && (it.isAdmin || it.isManager) && it.managerPremium
    }

    val showLinkSharing = huddle.map {
        it ?: return@map false
        !(it.isPrivate) && requestToJoin.value == false && (it.isAdmin || it.isManager || it.participantShare)
    }

    val showAllowLinkSharing = huddle.map {
        it ?: return@map false
        !(it.isPrivate) && requestToJoin.value == false && (it.isAdmin || it.isManager)
    }

    val requestToJoin = _huddleInfo.map {
        it?.requestToJoin
    }
    val onHuddleInfoError = LiveEvent<Boolean>()


    private val _profileInfoLoading = MutableLiveData(true)
    val profileInfoLoading: LiveData<Boolean> = _profileInfoLoading

    private val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = listOf()
    )

    fun getHuddleInfo(huddleId: Int) {
        _profileInfoLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<HuddleInfo> = huddleRepo.getFullHuddleInfo(huddleId)) {
                is ResultOf.APIError -> {
                    onHuddleInfoError.postValue(true)
                }
                is ResultOf.Error -> {
                    onHuddleInfoError.postValue(true)
                }
                is ResultOf.Success -> {
                    val huddle  = result.value
                    val nickNameOrName = nickNames.value.find { nn -> nn.userId == huddle.managerId }?.nickName?:huddle.createdBy
                    huddle.createdBy = nickNameOrName
                    _huddleInfo.postValue(huddle)
                }
            }
            _profileInfoLoading.postValue(false)
        }
    }

    private val _huddleLeaving = MutableLiveData(false)
    val huddleLeaving: LiveData<Boolean> = _huddleLeaving

    val onHuddleLeft = LiveEvent<HuddleType>()

    fun leaveOrDeleteHuddle() {
        val huddle = huddle.value ?: return
        _huddleLeaving.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (
                if (huddle.isManager || (isHuddleDeletableByMinister.value == true && iAmMinister.value == true )|| iAmPresident.value==true) {
                    huddleRepo.deleteHuddle(huddle.id)
                } else huddleRepo.leaveHuddle(huddle.id)) {
                is ResultOf.APIError -> { }
                is ResultOf.Error -> { }
                is ResultOf.Success -> {
                    onHuddleLeft.postValue(if (huddle.isPrivate) HuddleType.PRIVATE else HuddleType.PUBLIC)
                }
            }
            _huddleLeaving.postValue(false)
        }
    }

    val onHuddleMuted = LiveEvent<Boolean>()

    fun muteUnmuteHuddle(mute: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            if (mute != huddle.value?.isMuted) {
                val list: MutableList<Int> = mutableListOf()
                list.add(huddle.value?.id!!)
                val action = if (mute) HuddleAction.MUTE else HuddleAction.UNMUTE
                val type = if (huddle.value?.isPrivate == true) HuddleType.PRIVATE else HuddleType.PUBLIC
                when (huddleRepo.performHuddleAction(list, action, type)) {
                    is ResultOf.Success -> {
                        _huddleInfo.value?.let {
                            _huddleInfo.postValue(it.copy(
                                isMuted = mute
                            ))
                        }
                        onHuddleMuted.postValue(mute)
                    }
                    else -> {
                        _huddleInfo.value?.let {
                            _huddleInfo.postValue(it.copy(
                                isMuted = !mute
                            ))
                        }
                    }
                }
            }
        }
    }

    fun onOffLinkSharing(checked: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            if (checked != huddle.value?.participantShare) {
                when (huddleRepo.updateHuddle(huddleId = huddle.value?.id!!, participantShare = checked)) {
                    is ResultOf.Success -> {
                        _huddleInfo.value?.let {
                            _huddleInfo.postValue(it.copy(
                                participantShare = checked
                            ))
                        }
                    }
                    is ResultOf.APIError -> {

                    }
                    is ResultOf.Error -> {

                    }
                }
            }
        }
    }

    fun onOffRequestToJoin(checked: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            if (checked != requestToJoin.value) {
                when (huddleRepo.updateHuddle(huddleId = huddle.value?.id!!, requestToJoin = checked)) {
                    is ResultOf.Success -> {
                        _huddleInfo.value?.let {
                            _huddleInfo.postValue(it.copy(
                                requestToJoin = checked
                            ))
                        }
                    }
                    is ResultOf.APIError -> {

                    }
                    is ResultOf.Error -> {

                    }
                }
            }
        }
    }

    fun getHuddleLeaveDeleteButtonText(isManager: Boolean, isPrivate: Boolean): Int {
        return when {
            isManager && isPrivate -> R.string.huddle_info_manager_delete_huddle_text_huddle_type_private
            isManager && !isPrivate -> R.string.huddle_info_manager_delete_huddle_text_huddle_type_public
            !isManager && isPrivate -> R.string.huddle_info_user_leave_huddle_text_huddle_type_private
            else -> R.string.huddle_info_user_leave_huddle_text_huddle_type_public
        }
    }

    fun reFreshHuddleInfo(huddleID: Int) {
        getHuddleInfo(huddleID)
    }

}