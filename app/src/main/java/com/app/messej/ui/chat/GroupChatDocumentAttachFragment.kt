package com.app.messej.ui.chat

import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R

class GroupChatDocumentAttachFragment: BaseDocumentAttachFragment() {

    override val viewModel: GroupChatViewModel by navGraphViewModels(R.id.nav_chat_group)

    private val args: GroupChatDocumentAttachFragmentArgs by navArgs()

    override val destinationName: String?
        get() = args.destination
}