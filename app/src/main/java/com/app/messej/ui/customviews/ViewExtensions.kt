package com.app.messej.ui.customviews

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.View
import android.widget.ImageView
import androidx.core.content.res.ResourcesCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import jp.wasabeef.transformers.glide.BlurTransformation


object ViewExtensions {

    fun View.setBlurredBackground(background: Int, context: Context) {
        Glide.with(context).asDrawable().load(ResourcesCompat.getDrawable(resources, background, null)).apply(RequestOptions.bitmapTransform(BlurTransformation(context, 25, 10, true)))
            .into(object : CustomTarget<Drawable>() {
                override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                    <EMAIL> = resource
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                }
            })
    }

    fun ImageView.setBlurredImage(background: Int, radius: Int, context: Context) {
        Glide.with(context).asDrawable().load(ResourcesCompat.getDrawable(resources, background, null)).transform(jp.wasabeef.glide.transformations.BlurTransformation(radius)).into(this)
    }

}