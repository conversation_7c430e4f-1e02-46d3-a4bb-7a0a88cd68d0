package com.app.messej.ui.home.publictab.authorities.legalAffairs

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.legal.CaseDetails
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.ItemDefenseMediaPreviewBinding

class DefenseProofViewPagerAdapter(
    private val defenseProofFiles: List<CaseDetails.ProofFile>, val onClick: (CaseDetails.ProofFile?) -> Unit
) : RecyclerView.Adapter<DefenseProofViewPagerAdapter.DefenseProofViewHolder>() {

    inner class DefenseProofViewHolder(val binding: ItemDefenseMediaPreviewBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(proofFile: CaseDetails.ProofFile?) {
            binding.apply {
                imageUrl = proofFile?.url
                isVideo = proofFile?.mediaType == MediaType.VIDEO
                previewImage.setOnClickListener { onClick(proofFile) }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DefenseProofViewHolder {
        val binding = ItemDefenseMediaPreviewBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DefenseProofViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return defenseProofFiles.size
    }

    override fun onBindViewHolder(holder: DefenseProofViewHolder, position: Int) {
        val item = defenseProofFiles[position]
        holder.bind(proofFile = item)
    }
}