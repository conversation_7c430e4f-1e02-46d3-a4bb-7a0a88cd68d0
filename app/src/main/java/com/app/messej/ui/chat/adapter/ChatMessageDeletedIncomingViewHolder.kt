package com.app.messej.ui.chat.adapter

import com.app.messej.R
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.databinding.ItemChatMessageIncomingDeletedBinding
import com.app.messej.ui.chat.ChatMessageUIModel

class ChatMessageDeletedIncomingViewHolder(val binding: ItemChatMessageIncomingDeletedBinding, userId: Int,val mListener: ChatAdapter.ChatClickListener)
    : ChatMessageViewHolder(binding.root,userId,false,mListener) {

    override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
        super.bind(item)
        val cm = (item as ChatMessageUIModel.ChatMessageModel)
        message = cm.message
        selected = item.selected
        deletedText = when (val msg = cm.message) {
                is BroadcastMessage -> root.context.getString(R.string.broadcast_deleted)
                is HuddleChatMessage -> {
                    if (cm.message.reported) root.context.getString(R.string.chat_message_reported)
                    else if (msg.senderDetails?.blockedByEitherAdmin==true) root.context.getString(R.string.chat_message_admin_kicked)
                    else if (msg.remover?.idInt == null) msg.displayMessage?:""
                    else if (msg.remover.idInt == userId) root.context.getString(R.string.chat_message_deleted_manager)
                    else root.context.getString(R.string.chat_message_deleted_user)
                }
                else -> root.context.getString(if (cm.message.reported) R.string.chat_message_reported else R.string.chat_message_deleted)
            }

        showName = if (cm.message is HuddleChatMessage) cm.showName else false

        senderName = when (val msg = cm.chat) {
            is HuddleChatMessageWithMedia -> msg.senderNickNameOrName
            else -> ""
        }
        chatHolder.setOnLongClickListener {
            if(cm.message.reported) {
                mListener.onItemLongClick(cm.message, layoutPosition)
            }
            true
        }

        setChatBubbleColor(chatBubble, cm)
    }

    override fun getHighlightView() = null
}