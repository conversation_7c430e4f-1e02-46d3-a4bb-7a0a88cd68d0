package com.app.messej.ui.home.publictab.flash

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.databinding.ItemFlashListBinding
import com.app.messej.ui.home.publictab.flash.myflash.FlashVideoUIModel
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerFragment

class  PublicFlashAdapter(private val listener: FlashClickListener) : PagingDataAdapter<FlashVideoUIModel, PublicFlashAdapter.FeedsListViewHolder>(FeedsDiff) {

    interface FlashClickListener {
        fun onFlashClicked(pos: Int, view: View, flash: FlashVideo)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = FeedsListViewHolder(
        ItemFlashListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun onBindViewHolder(holder: FeedsListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class FeedsListViewHolder(private val binding: ItemFlashListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: FlashVideoUIModel) = with(binding) {
            flash = item.flashVideo
            ViewCompat.setTransitionName(flashThumb, BaseFlashPlayerFragment.transitionNameForId(item.flashVideo.id))
            flashCard.setOnClickListener {
                listener.onFlashClicked(bindingAdapterPosition,flashThumb,item.flashVideo)
            }
//            val imageByteArray: ByteArray = Base64.decode("iVBORw0KGgoAAAANSUhEUgAAAAMAAAAGCAYAAAAG5SQMAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAWUlEQVQImQFOALH/AZiWhv8AAgYAAvv2AAQECA0A7OvvAO/6/gABJyEb/zQnGQDS2uMABEkuEAD6DhMANvruAAT8FywAz9XjAPT2DwAAExYa/zc1NP8HChH/XvIcaXfZxUYAAAAASUVORK5CYII=", Base64.DEFAULT)
//            Glide.with(root.context)
//                .load(imageByteArray)
//                .into(flashThumb)
        }
    }


    object FeedsDiff : DiffUtil.ItemCallback<FlashVideoUIModel>() {
        override fun areItemsTheSame(oldItem: FlashVideoUIModel, newItem: FlashVideoUIModel): Boolean {
            return oldItem.flashVideo.id == newItem.flashVideo.id
        }

        override fun areContentsTheSame(oldItem: FlashVideoUIModel, newItem: FlashVideoUIModel): Boolean {
            return oldItem == newItem
        }
    }
}