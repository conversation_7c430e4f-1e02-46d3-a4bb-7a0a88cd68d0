package com.app.messej.ui.home.publictab.authorities.legalAffairs.hidden

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.data.repository.LegalAffairsRepository

class HiddenViewModel(application: Application) : AndroidViewModel(application) {

    private val legalAffairsRepository = LegalAffairsRepository(application)
    val investigationBureauCount = MutableLiveData<LegalRecordsResponse?>(null)

    val hiddenList = legalAffairsRepository
        .getLegalAffairsBoardDetails(
            recordType = LegalAffairTabs.Hidden.serializedName(),
            tab = LegalAffairsMainTab.InvestigationBureau.serializedName(),
            countCallBack = { item ->
                investigationBureauCount.postValue(
                    LegalRecordsResponse(
                        banCount = item?.banCount,
                        reportsCount = item?.reportsCount,
                        hiddenCount = item?.hiddenCount
                    )
                )
            }
        )
        .liveData
        .cachedIn(viewModelScope)

}