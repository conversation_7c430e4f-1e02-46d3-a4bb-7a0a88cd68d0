package com.app.messej.ui.home.publictab.podiums

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.databinding.ItemPodiumListBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.displayText

class PodiumAdapter( private val tab:PodiumTab?, private val listener: PodiumActionListener): PagingDataAdapter<Podium,PodiumAdapter.PodiumListViewHolder>(PodiumDiff) {

    interface PodiumActionListener {
        fun onPodiumClicked(pod: Podium)
        fun onEdit(pod: Podium)

        fun onAboutClicked(pod: Podium)
        fun onPodiumMenuClicked(pod:Podium,view: View)
        fun onPodiumDpClicked(pod:Podium)

    }

    inner class PodiumListViewHolder(private val binding:ItemPodiumListBinding):RecyclerView.ViewHolder(binding.root){
        fun bind(item: Podium) = with(binding) {
            podium = item
            isMypodiumTab = tab == PodiumTab.MY_PODIUM
            
            when (item.role) {
                Podium.PodiumUserRole.INVITED -> {
                    thumbTag.isVisible = true
                    thumbTag.setText(R.string.common_invited)
                }
                Podium.PodiumUserRole.MANAGER -> {
                    thumbTag.isVisible = true
                    thumbTag.setText(R.string.common_manager)
                }
                Podium.PodiumUserRole.ADMIN -> {
                    thumbTag.isVisible = true
                    thumbTag.setText(R.string.common_admin)
                }
                Podium.PodiumUserRole.INVITEE -> {
                    thumbTag.isVisible = true
                    /*thumbTag.setText(R.string.common_invitee)*/ /**commenting as per new requirement*/
                    thumbTag.setText(R.string.common_invited)
                }
                else -> {
                    thumbTag.isVisible = false
                }
            }
            item.gameType?.let {
                gameType.setText(it.resId)
            }

            item.kind?.let { kind ->
                kindChip.text = kind.displayText(this.root.context)
            }

            card.setOnClickListener {
                if (tab == PodiumTab.LIVE_PODIUM || tab == null) {
                    listener.onPodiumClicked(item)
                }
            }

            actionUser.setOnClickListener {
                if (tab == PodiumTab.LIVE_PODIUM || tab == null) {
                    listener.onPodiumClicked(item)
                }
            }

            btnLiveJoin.setOnClickListener {
                listener.onPodiumClicked(item)
            }

            btnEdit.setOnClickListener {
                listener.onEdit(item)
            }
            btnRecords.setOnClickListener {
                listener.onAboutClicked(item)
            }
            btnAbout.setOnClickListener {
                listener.onAboutClicked(item)
            }
            btnPodiumAction.setOnClickListener {
                listener.onPodiumMenuClicked(item,it)
            }
            podiumDp.setOnClickListener {
                listener.onPodiumDpClicked(item)
            }
        }
    }
    object PodiumDiff : DiffUtil.ItemCallback<Podium>(){
        override fun areItemsTheSame(oldItem: Podium, newItem: Podium) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: Podium, newItem: Podium) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: PodiumListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumListViewHolder {
        val binding = ItemPodiumListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumListViewHolder(binding)
    }
}