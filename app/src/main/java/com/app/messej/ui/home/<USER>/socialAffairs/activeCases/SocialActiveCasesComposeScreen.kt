package com.app.messej.ui.home.publictab.socialAffairs.activeCases

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import com.app.messej.R
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.enums.SocialActiveCaseMainTab
import com.app.messej.data.model.enums.SocialActiveTab
import com.app.messej.data.model.enums.SocialCaseFilter
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.ui.composeComponents.ComposeShimmerListLayout
import com.app.messej.ui.composeComponents.CustomPaginationView
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.setText
import com.app.messej.ui.home.publictab.socialAffairs.committee.SocialCommitteeMembersShimmerItem
import com.app.messej.ui.home.publictab.socialAffairs.components.SocialSmallRoundedButton

interface SocialActiveCaseClickListeners {
    fun onVote(action: SocialVoteAction, caseId: Int?)
    fun onDonate(case: SocialCaseInfo)
    fun onSocialEngageTabClick()
    fun onUserDPClick(userId: Int)
    fun onClick(case: SocialCaseInfo?)
}

@Composable
fun SocialActiveCaseComposeScreen(
    viewModel: SocialActiveCasesViewModel,
    listeners: SocialActiveCaseClickListeners
) {
    val casesPagingItem = viewModel.caseList.collectAsLazyPagingItems()

    val currentSubTabList by viewModel.currentSubTabList.observeAsState()
    val currentFilterTabList by viewModel.currentFilterTabList.observeAsState()
    val countryFlagList by viewModel.countryFlagList.observeAsState()

    val selectedMainTab by viewModel.selectedMainTab.observeAsState(initial = SocialActiveCaseMainTab.Donate)
    val selectedSubTab by viewModel.selectedSubTab.observeAsState(initial = SocialActiveTab.All)
    val selectedFilterTab by viewModel.selectedFilterTab.observeAsState(initial = SocialCaseFilter.All)

    val isFilterIconVisible by viewModel.isFilterIconVisible.observeAsState(initial = false)
    var isNewCaseDropDownVisible by remember { mutableStateOf(value = false) }

    LaunchedEffect(key1 = Unit) {
        viewModel.isReadyToFreshList.collect {
            if (it) {
                casesPagingItem.refresh()
                viewModel.resetReplayCache()
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        //Donate, New Cases Tab View
        ActiveCaseTabView(viewModel = viewModel)

        //Sub Tab View
        LazyRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
            contentPadding = PaddingValues(all = dimensionResource(id = R.dimen.element_spacing))
        ) {
            items(items = currentSubTabList ?: emptyList(), key = { it.ordinal }) { subTab ->
                val isSelected = subTab == selectedSubTab
                SocialSmallRoundedButton(
                    text = stringResource(id = subTab.setText()),
                    contentColor = colorResource(id = if (isSelected) R.color.white else R.color.textColorPrimary),
                    backgroundColor = colorResource(id = if (isSelected) R.color.colorSocialGreen else R.color.colorSocialSurfaceSecondary),
                    onClick = {
                        if (subTab == SocialActiveTab.Social) {
                            listeners.onSocialEngageTabClick()
                            return@SocialSmallRoundedButton
                        }
                        viewModel.setSubTab(tab = subTab)
                    }
                )
            }
        }

        //Cases Filter Text and Filter Icon View
        Row(modifier = Modifier
            .padding(horizontal = dimensionResource(id = R.dimen.element_spacing))
            .animateContentSize()
            .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = buildAnnotatedString {
                    append(text = stringResource(id = if (selectedFilterTab == SocialCaseFilter.All) selectedSubTab.setText() else selectedFilterTab.setText()))
                    append(text = when(selectedFilterTab) {
                        SocialCaseFilter.Declined, SocialCaseFilter.UnmetVoting -> ""
                        else -> " " + stringResource(id = R.string.legal_affairs_cases)
                    })
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(weight = 1F),
                style = FlashatComposeTypography.defaultType.body2,
                color = colorResource(id = R.color.textColorPrimary),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            AnimatedVisibility(
                visible = isFilterIconVisible,
                enter = slideInHorizontally { it },
                exit = slideOutHorizontally { it }
            ) {
                Box {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_filter_horizontal),
                        modifier = Modifier
                            .size(size = 20.dp)
                            .clip(shape = CircleShape)
                            .clickable { isNewCaseDropDownVisible = !isNewCaseDropDownVisible },
                        tint = colorResource(id = R.color.textColorPrimary),
                        contentDescription = null
                    )
                    NewCasesFilterAlertView(
                        caseFilterList = currentFilterTabList,
                        isExpanded = isNewCaseDropDownVisible,
                        onDismiss = { isNewCaseDropDownVisible = false },
                        onClick = viewModel::setFilterTab
                    )
                }
            }
        }

        //Paginated Items View
        CustomPaginationView(
            lazyPagingItem = casesPagingItem,
            emptyItemIcon = if (selectedMainTab == SocialActiveCaseMainTab.Donate) R.drawable.bg_empty_social_donate_case else R.drawable.bg_empty_social_new_case,
            loadingAnimation = {
                ComposeShimmerListLayout(
                    modifier = Modifier.padding(top = dimensionResource(id = R.dimen.element_spacing)),
                    verticalSpace = dimensionResource(id = R.dimen.line_spacing),
                    itemCount = 10
                ) { brush ->
                    SocialCommitteeMembersShimmerItem(brush = brush)
                }
            },
            verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
            contentPadding = PaddingValues(all = dimensionResource(id = R.dimen.element_spacing)),
            emptyViewTitle = if (selectedMainTab == SocialActiveCaseMainTab.Donate) {
                when (selectedFilterTab) {
                    SocialCaseFilter.Pending -> R.string.social_donate_no_pending_case_title
                    SocialCaseFilter.Closed -> R.string.social_donate_no_closed_case_title
                    else -> R.string.social_donate_empty_title
                }
            } else {
                R.string.social_new_cases_empty_title
            },
            lazyColumnContent = {
                items(count = casesPagingItem.itemCount) { position ->
                    val item = casesPagingItem[position] ?: return@items
                    val user = item.userDetail
                    val countryFlag = countryFlagList?.get(user?.countryCode)
                    val updatedItem = item.copy(userDetail = user?.copy(countryFlag = countryFlag))
                    SocialSingleListItemView(
                        case = updatedItem,
                        isStatusAndActionButtonsHidden = selectedFilterTab == SocialCaseFilter.Pending || selectedFilterTab == SocialCaseFilter.Closed,
                        onVote = listeners::onVote,
                        onDonate = { listeners.onDonate(case = updatedItem) },
                        onUserDpClick = listeners::onUserDPClick,
                        onClick = { listeners.onClick(case = item) }
                    )
                }
            }
        )
    }
}