package com.app.messej.ui.home.businesstab.operations.tasks

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.entity.BusinessTaskOne
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BusinessOperationsTaskViewModel(application: Application) : AndroidViewModel(application) {

    var otpVerificationComplete = LiveEvent<Boolean>()
    var dialogDismiss = LiveEvent<Boolean>()
    private val businessRepo = BusinessRepository(application)
    val taskThreeLearnMoreClicked = MutableLiveData<Boolean>(false)
    private var pagePosition = LiveEvent<Int?>()
    val showCompactLoading = MutableLiveData(false)
    val taskOneData=LiveEvent<BusinessTaskOne?>()
    fun setVerificationCompleted(verified: Boolean) {
        if(verified){
            if(pagePosition.value==0) {
                otpVerificationComplete.postValue(true)
            }
        }else{
            otpVerificationComplete.postValue(false)
        }
    }

    fun getBusinessOperations(){
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<BusinessTaskOne> = businessRepo.getBusinessTaskOne()) {
                is ResultOf.Success -> {
                    taskOneData.postValue(result.value)
                }
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }

        }
    }
    fun setSubmitDialogDismiss() {
      dialogDismiss.postValue(true)
    }

    fun setTabPosition(position: Int?) {
       pagePosition.postValue(position!!)
    }


    private var profileRepo = ProfileRepository(application)
    val customersDearsList = profileRepo.getDearsListPager().liveData.cachedIn(viewModelScope)

}