package com.app.messej.ui.home.publictab.podiums

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.databinding.FragmentPublicPodiumBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.SubscriptionStatusViewModel
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindGiftRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindLegalAffairsPayFineButton
import com.app.messej.ui.utils.FragmentExtensions.bindMaidanToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.setBadgeNumber
import com.app.messej.ui.utils.FragmentExtensions.setupPayFineIcon
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils

class PublicPodiumStandaloneFragment: PublicPodiumBaseFragment() {

    private lateinit var outerBinding: FragmentPublicPodiumBinding

    private val homeViewModel: CommonHomeViewModel by activityViewModels()
    private val subscriptionStatusViewModel: SubscriptionStatusViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_podium, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        binding = outerBinding.layout
        binding.viewModel = viewModel
        return outerBinding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(outerBinding.appbar.toolbar, customBackButton = false)
        bindGiftRateToolbarChip(outerBinding.appbar.giftChip)
        bindFlaxRateToolbarChip(outerBinding.appbar.flaxRateChip)
        bindMaidanToolbarChip(outerBinding.appbar.maidanChip)
        bindLegalAffairsPayFineButton(outerBinding.appbar.payFineChip)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        outerBinding.interactionBanner.upgradeTitle.setOnClickListener{
            upgradeToPremium()
        }
    }
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_home_notifications,menu)
        super.onCreateMenu(menu, menuInflater)
    }

    private var notificationBadge: BadgeDrawable? = null

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        notificationBadge = BadgeDrawable.create(requireContext())
        setBadgeNumber(notificationBadge,homeViewModel.unreadNotifications.value)
        notificationBadge?.apply {
            adjustForNotifications(requireContext())
            BadgeUtils.attachBadgeDrawable(this, outerBinding.appbar.toolbar, R.id.action_notifications)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_notifications -> findNavController().navigateSafe(PublicPodiumStandaloneFragmentDirections.actionGlobalNotificationFragment())
            R.id.action_search -> {
                navigateToSearch()
            }
            else -> return false
        }
        return true
    }

    override fun setup(){
        mPodiumPagerAdapter = object: FragmentStateAdapter(this) {
            override fun getItemCount(): Int = 1

            override fun createFragment(position: Int): Fragment {
                val tab = PodiumTab.LIVE_PODIUM
                return PodiumInnerListFragment().apply {
                    arguments = PodiumInnerListFragment.getTabBundle(tab)
                }
            }
        }
        binding.layoutTabs.isVisible = false

        super.setup()

        outerBinding.upgradeBanner.upgradeBannerLayout.setOnClickListener {
            upgradeToPremium()
        }

        outerBinding.upgradeBanner.dismissUpgradeBannerBtn.setOnClickListener {
            homeViewModel.onDismissUpgradeBanner()
        }

        setupPromoBoard(outerBinding.promoBar)
        setupPayFineIcon(composeView = outerBinding.payFine)
    }

    private fun upgradeToPremium() {

        subscriptionStatusViewModel.isActive.value?.let { isActive ->
            when (isActive) {
                UserSubscriptionStatus.ACTIVE, UserSubscriptionStatus.EXPIRED -> {
                    findNavController().navigateSafe(
                        PublicPodiumStandaloneFragmentDirections.actionGlobalAlreadySubscribedFragment(false)
                    )
                }
                else -> {
                    findNavController().navigateSafe(PublicPodiumStandaloneFragmentDirections.actionGlobalUpgradePremiumFragment())
                }
            }
        }
    }

    fun observe() {
        homeViewModel.accountDetails.observe(viewLifecycleOwner){
            outerBinding.citizenship = it?.citizenship
            outerBinding.isPremium = it?.isPremium
            outerBinding.daysLeft = it?.remainingDaysForResident
        }
        homeViewModel.didDismissUpgradeBannerToday.observe(viewLifecycleOwner) {
            outerBinding.showUpgradeBanner = !it
        }

        subscriptionStatusViewModel.subscriptionLoaded.observe(viewLifecycleOwner) {
            it?.let { clickable ->
                outerBinding.upgradeBanner.clickable = clickable
                outerBinding.interactionBanner.clickable = clickable
            }
        }
    }

    override fun navigateToSearch() {
        findNavController().navigateSafe(PublicPodiumStandaloneFragmentDirections.actionPublicPodiumStandaloneFragmentToPodiumSearchFragment(viewModel.currentTab.value?:PodiumTab.MY_PODIUM))
    }

}