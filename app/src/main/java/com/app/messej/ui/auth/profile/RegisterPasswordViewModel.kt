package com.app.messej.ui.auth.profile

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.Constants.InputValidationResult
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class RegisterPasswordViewModel(application: Application) : AndroidViewModel(application) {

    private var profileRepo: ProfileRepository = ProfileRepository(application)
    private var accountRepo: AccountRepository = AccountRepository(application)

    //  Prerequisite Data
    private val _countryCode = MutableLiveData<String?>(null)
    val countryCode: LiveData<String?> = _countryCode

    private val _phoneNumber = MutableLiveData<String?>(null)
    val phoneNumber: LiveData<String?> = _phoneNumber

    private val _email = MutableLiveData<String?>(null)
    val email: LiveData<String?> = _email

    private val _otpRequestMode = MutableLiveData<OTPRequestMode?>(null)
    val otpRequestMode: LiveData<OTPRequestMode?> = _otpRequestMode

    private val _phoneNumberWithCountryCode = MutableLiveData<String?>(null)
    val phoneNumberWithCountryCode: LiveData<String?> = _phoneNumberWithCountryCode

    fun setArgs(code: String, number: String, email: String, otpRequestMode: OTPRequestMode) {
        _countryCode.value = code
        _phoneNumber.value = UserInfoUtil.removeLeadingZeroes(number)
        _email.value = email
        _otpRequestMode.value = otpRequestMode
        val combined = UserInfoUtil.combineCountryCodeAndMobileNumber(this.countryCode.value.orEmpty(), phoneNumber.value.orEmpty())
        _phoneNumberWithCountryCode.postValue(combined)
    }

    // CREATE PASSWORD

    val password = MutableLiveData<String>()
    val confirmPassword = MutableLiveData<String>()

    val ruleCharacterCount = MutableLiveData(InputValidationResult.NONE)
    val ruleCharacterCase = MutableLiveData(InputValidationResult.NONE)
    val ruleNumberAndSpecialChar = MutableLiveData(InputValidationResult.NONE)
    val rulePasswordMatch = MutableLiveData(InputValidationResult.NONE)
    val didEnterConfirmPassword = MutableLiveData<Boolean>(false)


    private val _passwordValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        med.addSource(password) { checkPassword() }
        med.addSource(confirmPassword) { checkPassword() }
        med
    }
    val passwordValid: LiveData<Boolean> = _passwordValid

    private val _showPasswordMatchError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(didEnterConfirmPassword) {shouldShowPasswordError() }
        med.addSource(_passwordValid) {shouldShowPasswordError() }
        med
    }

    val showPasswordMatchError: LiveData<Boolean> = _showPasswordMatchError

    private fun shouldShowPasswordError() {
        if (didEnterConfirmPassword.value==false) {
            _showPasswordMatchError.postValue(false)
            return
        }
        _showPasswordMatchError.postValue(_passwordValid.value==false)
    }

    private val _passwordLoading = MutableLiveData(false)
    val passwordLoading: LiveData<Boolean> = _passwordLoading

    val onSetPasswordComplete = LiveEvent<Boolean>()
    val onPasswordResetComplete = LiveEvent<Boolean>()

    private val _setPasswordError = MutableLiveData<String?>(null)
    val setPasswordError: LiveData<String?> = _setPasswordError

    private fun checkPassword() {
        val mixPattern = Regex("^(?=.*[a-z])(?=.*[A-Z])")
        val numberAndSpecialPattern = Regex("^(?=.*[0-9].*)|(?=.*[^\\w\\s].*)")
        var flagChar = false
        var flagMix = false
        var flagNumberAndSpecial = false
        var flagPasswordMatch = false
        if (!password.value.isNullOrEmpty()) {
            ruleCharacterCount.postValue(
                if (password.value!!.length >= 6) {
                    flagChar = true
                    InputValidationResult.PASS
                } else InputValidationResult.FAIL
            )
            ruleCharacterCase.postValue(
                if (mixPattern.containsMatchIn(password.value!!)) {
                    flagMix = true
                    InputValidationResult.PASS
                } else InputValidationResult.FAIL
            )
            ruleNumberAndSpecialChar.postValue(
                if (numberAndSpecialPattern.containsMatchIn(password.value!!)) {
                    flagNumberAndSpecial = true
                    InputValidationResult.PASS
                } else InputValidationResult.FAIL
            )
            rulePasswordMatch.postValue(
                if (confirmPassword.value.equals(password.value)) {
                    flagPasswordMatch = true
                    InputValidationResult.PASS
                } else InputValidationResult.FAIL
            )
            if (flagMix && flagChar && flagNumberAndSpecial && flagPasswordMatch) _passwordValid.postValue(true)
            else _passwordValid.postValue(false)
        } else {
            ruleCharacterCount.postValue(InputValidationResult.NONE)
            ruleCharacterCase.postValue(InputValidationResult.NONE)
            ruleNumberAndSpecialChar.postValue(InputValidationResult.NONE)
        }
    }

    fun setPassword() {
        _passwordLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<CurrentUser> = profileRepo.setPassword(
                password = password.value!!, confirmPassword = confirmPassword.value!!, countryCode = _countryCode.value!!, phoneNumber = _phoneNumber.value!!
            )) {
                is ResultOf.Success -> {
                    onSetPasswordComplete.postValue(true)
                }

                is ResultOf.APIError -> {
                    _setPasswordError.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _passwordLoading.postValue(false)
        }
    }

    fun setEmailPassword(){
        _passwordLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<CurrentUser> = profileRepo.setEmailPassword(
                password = password.value!!, confirmPassword = confirmPassword.value!!,email=_email.value!!
            )) {
                is ResultOf.Success -> {
                    onSetPasswordComplete.postValue(true)
                }
                is ResultOf.APIError -> {
                    _setPasswordError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _passwordLoading.postValue(false)
        }
    }

    fun resetPassword() {
        viewModelScope.launch(Dispatchers.IO) {
            _passwordLoading.postValue(true)
            when (val result: ResultOf<CurrentUser> = profileRepo.resetPassword(
                password = password.value!!, confirmPassword = confirmPassword.value!!, countryCode = _countryCode.value!!, phoneNumber = _phoneNumber.value!!
            , email = email.value!!)) {
                is ResultOf.Success -> {
                    onPasswordResetComplete.postValue(true)
                }
                is ResultOf.APIError -> {
                    _setPasswordError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _passwordLoading.postValue(false)
        }
    }
    fun exitPasswordStage() {
        viewModelScope.launch {
            accountRepo.clearAccount()
            onExitPasswordStage.postValue(true)
        }
    }

    val onExitPasswordStage = LiveEvent<Boolean>()

}