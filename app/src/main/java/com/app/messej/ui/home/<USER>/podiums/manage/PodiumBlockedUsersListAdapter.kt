package com.app.messej.ui.home.publictab.podiums.manage

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.databinding.ItemPodiumBlockedUserBinding

class PodiumBlockedUsersListAdapter(private val listener: PodiumActionListener, val actionRes: String): PagingDataAdapter<PodiumSpeaker, PodiumBlockedUsersListAdapter.PodiumBlockedUsersVH>(PodiumDiff) {

    interface PodiumActionListener {
        fun onActionButtonClicked(view: View, speaker: PodiumSpeaker)
        fun AppCompatTextView.setupStatChip(item: PodiumSpeaker) {}
    }

    inner class PodiumBlockedUsersVH(private val binding: ItemPodiumBlockedUserBinding): RecyclerView.ViewHolder(binding.root){
        fun bind(item: PodiumSpeaker) = with(binding) {
            speaker = item
            action = actionRes
            unblockActionButton.setOnClickListener {
                listener.onActionButtonClicked(it, item)
            }
            with(listener) {
                theaterStatChip.setupStatChip(item)
            }
        }
    }
    object PodiumDiff : DiffUtil.ItemCallback<PodiumSpeaker>(){
        override fun areItemsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: PodiumBlockedUsersVH, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumBlockedUsersVH {
        val binding = ItemPodiumBlockedUserBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumBlockedUsersVH(binding)
    }

}