package com.app.messej.ui.home.publictab.flash.player.cache

import android.util.Log
import androidx.core.net.toUri
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.offline.Download
import androidx.media3.exoplayer.offline.DownloadManager
import androidx.media3.exoplayer.offline.DownloadRequest
import com.app.messej.MainApplication
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.utils.MediaUtils
import java.util.concurrent.Executor


object FlashVideoPreloadManager {

    private const val cacheSize: Long = 100 * 1024 * 1024

    // Note: This should be a singleton in your app.
    @UnstableApi
    private val databaseProvider = StandaloneDatabaseProvider(MainApplication.applicationContext())

    // A download cache should not evict media, so should use a NoopCacheEvictor.
    @UnstableApi
    val downloadCache = SimpleCache(
        MediaUtils.getFlashCacheDirectory(MainApplication.applicationContext()),
        LeastRecentlyUsedCacheEvictor(cacheSize),
        databaseProvider
    )

    // Create the download manager.
    @UnstableApi
    private var downloadManager: DownloadManager? = null

    @UnstableApi
    private fun getCookieMonsterFactory(cookie: VideoPlaybackCookie): DefaultDataSource.Factory {
        val context = MainApplication.applicationContext()

        // Create a factory for reading the data from the network.

        val factory = DefaultHttpDataSource.Factory()
            .setUserAgent(Util.getUserAgent(context, "Flashat"))
            .setDefaultRequestProperties(mapOf("Cookie" to cookie.cookieValue))
            .setAllowCrossProtocolRedirects(true)

        return DefaultDataSource.Factory(context, factory)
    }

    @UnstableApi
    fun init(cookie: VideoPlaybackCookie) {
        try {

            Log.d("FVPM", "init: ")

            val dataSourceFactory = getCookieMonsterFactory(cookie)
            // Choose an executor for downloading data. Using Runnable::run will cause each download task to
            // download data on its own thread. Passing an executor that uses multiple threads will speed up
            // download tasks that can be split into smaller parts for parallel execution. Applications that
            // already have an executor for background downloads may wish to reuse their existing executor.
            val downloadExecutor = Executor(Runnable::run)

            downloadManager = DownloadManager(MainApplication.applicationContext(), databaseProvider, downloadCache, dataSourceFactory, downloadExecutor).apply {

                // Optionally, properties can be assigned to configure the download manager.
//            requirements = requirements
                maxParallelDownloads = 2

                addListener(object : DownloadManager.Listener {
                    override fun onDownloadChanged(downloadManager: DownloadManager, download: Download, finalException: Exception?) {
                        Log.d(
                            "FVPM",
                            "onDownloadChanged: ${download.state} | ${downloadManager.notMetRequirements} | ${MediaUtils.humanizeBytes(download.bytesDownloaded)}: ${download.percentDownloaded}% | ${download.request.uri}"
                        )
                    }
                })
            }
        } catch (_: Exception) {}
    }

    @UnstableApi
    fun getCacheDataSource(cookie: VideoPlaybackCookie): DataSource.Factory {
        val upstream = getCookieMonsterFactory(cookie)
        // Create a read-only cache data source factory using the download cache.
        val cacheDataSourceFactory: DataSource.Factory =
            CacheDataSource.Factory()
                .setCache(downloadCache)
                .setUpstreamDataSourceFactory(upstream)
                .setCacheWriteDataSinkFactory(null) // Disable writing.

        return cacheDataSourceFactory
    }

    @UnstableApi
    fun preloadMedia(flash: FlashVideo) {
        val request = DownloadRequest.Builder(flash.id, flash.videoUrl.toUri()).build()
        downloadManager?.apply {
            addDownload(request)
            resumeDownloads()
        }
    }


}