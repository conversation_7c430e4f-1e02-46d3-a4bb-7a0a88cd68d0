package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumBuyCameraBottomSheetBinding
import com.app.messej.databinding.LayoutBuyCameraConfirmationBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PodiumBuyCameraBottomSheetFragment : BasePodiumActionsBottomSheetFragment() {


    private lateinit var binding: FragmentPodiumBuyCameraBottomSheetBinding
    private val args: PodiumBuyCameraBottomSheetFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_buy_camera_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        binding.showBuy = args.buyAction
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun setup() {
        viewModel.getPodiumBuyCameraDetails()
        binding.btnClose.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.actionBuyCamera.setOnClickListener {
            MaterialDialog(requireContext()).show {
                val view = DataBindingUtil.inflate<LayoutBuyCameraConfirmationBinding>(layoutInflater, R.layout.layout_buy_camera_confirmation, null, false)
                view.viewModel = viewModel
                customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                cancelable(false)
                view.nickNameTitle.text = getString(R.string.podium_buy_camera_confirmation,viewModel.cameraPurchaseInfo.value?.requiredFlix.toString())
                view.nickNameTitle.textSize = 13F
                view.actionProceed.setOnClickListener {
                    viewModel.buyCameraTime(true)
                    dismiss()
                }
                view.actionCancel.setOnClickListener {
                    dismiss()
                }
            }
        }
    }

    private fun observer() {
        viewModel.onCameraPurchaseSuccess.observe(viewLifecycleOwner) {
            lifecycleScope.launch {
                Toast.makeText(requireContext(), getString(R.string.podium_camera_time_purchased_success), Toast.LENGTH_SHORT).show()
                delay(1000)
                findNavController().popBackStack()
            }
        }
    }

}