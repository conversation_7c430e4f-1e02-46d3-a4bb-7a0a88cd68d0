package com.app.messej.ui.home.publictab.huddles

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.repository.HuddlesRepository

class OtherUserHuddleListViewModel(application: Application) : AndroidViewModel(application) {

    val huddleRepo = HuddlesRepository(application)

    private val userId = MutableLiveData<Int?>(null)

    fun setUserId(id:Int?){
        userId.value = id
    }

    val otherHuddlesList = userId.switchMap { userId ->
        userId?.let {
            huddleRepo.getOtherUsersHuddlePager(it).liveData.cachedIn(viewModelScope)
        }
    }
}