package com.app.messej.ui.home.publictab.podiums.challenges.create

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.databinding.ItemFacilitatorListBinding
import com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumCreateChallengeViewModel.SelectableSpeakerUIModel
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class ChallengeFacilitatorListAdapter(data: MutableList<SelectableSpeakerUIModel>): BaseQuickAdapter<SelectableSpeakerUIModel, BaseDataBindingHolder<ItemFacilitatorListBinding>>(R.layout.item_facilitator_list, data) {

    var currentUserId: Int? = null
    override fun convert(holder: BaseDataBindingHolder<ItemFacilitatorListBinding>,
                         item: SelectableSpeakerUIModel
    ) {
        holder.dataBinding?.apply {
            speaker = item
            currentUserID = currentUserId
        }
    }

    class DiffCallback: DiffUtil.ItemCallback<SelectableSpeakerUIModel>() {
        override fun areItemsTheSame(oldItem: SelectableSpeakerUIModel, newItem: SelectableSpeakerUIModel): Boolean {
            return oldItem.speaker.id == newItem.speaker.id
        }

        override fun areContentsTheSame(oldItem: SelectableSpeakerUIModel,
                                        newItem: SelectableSpeakerUIModel): Boolean {
            return oldItem.selected == newItem.selected
        }
    }
}