package com.app.messej.ui.home.publictab.broadcast

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.databinding.FragmentPublicBroadcastSearchBinding
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils

class PublicBroadcastSearchFragment : Fragment() {

    private val args: PublicBroadcastSearchFragmentArgs by navArgs()

    private lateinit var binding : FragmentPublicBroadcastSearchBinding
    private val viewModel: PublicBroadcastSearchViewModel by viewModels()

    private var mAdapter: PublicBroadcastListAdapter? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater,R.layout.fragment_public_broadcast_search, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                setNavigationIcon(R.drawable.ic_close)
                setNavigationIconTint(resources.getColor(R.color.white))
            }
        }
    }

    private fun observe() {
        viewModel.searchList.observe(viewLifecycleOwner){
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun setup() {
        viewModel.setUserType(args.tab)
        initAdapter()
        binding.customActionBar.apply {
            keyword = viewModel.searchKeyword
            showKeyboard(searchBox)
        }
    }

    private fun initAdapter() {
        mAdapter = PublicBroadcastListAdapter(object :PublicBroadcastListAdapter.ItemListener{
            override fun onItemClick(user: UserRelative) {
//                val userType = PublicUserProfileLoaderFragment.getUserType(user.citizenship)
                Log.w("PUPS", "onItemClick: navigation to profile")
                findNavController().navigateSafe(PublicBroadcastSearchFragmentDirections.actionGlobalPublicUserProfileFragment(user.id))
            }
        })

        val layoutMan = LinearLayoutManager(context)
        binding.broadcastList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }
}