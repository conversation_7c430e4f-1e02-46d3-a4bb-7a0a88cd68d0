package com.app.messej.ui.home.publictab.authorities.stateAffairs

import com.google.gson.annotations.SerializedName

class StateAffairsTotalUsers(
    @SerializedName("citizen") val citizen: TotalData?=null,
    @SerializedName("resident") val resident: TotalData?=null,
    @SerializedName("visitor") val visitor: TotalData?=null,
    @SerializedName("golden") val golden:TotalData?=null,

) {}

class TotalData(
    @SerializedName("total") val total: Int? = null,
    @SerializedName("month") val month: Int? = null,
    @SerializedName("year") val year: Int? = null,
)

