package com.app.messej.ui.home.publictab.podiums.yalla

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.podium.challenges.YallaGuysJoinResponse
import com.app.messej.data.model.entity.YallaGuysChallenge
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.APIUtil.canShowAPIErrorMessage
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch


class YallaGuysListViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)
    private val accountRepo = AccountRepository(application)

    val user: CurrentUser get() = accountRepo.user

    private val _podiumId = MutableLiveData<String>()
    val podiumId: LiveData<String> = _podiumId

    fun setParams(podiumId: String) {
        _podiumId.value = podiumId
    }

    private val viewState = MutableStateFlow<MultiStateView.ViewState?>(MultiStateView.ViewState.LOADING)

    fun setViewState(state: MultiStateView.ViewState) {
        viewState.value = state
    }

    val debouncedViewState = viewState.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).distinctUntilChanged()

    val yallaGuysList = podiumId.switchMap {
        podiumRepository.getYallaGuysListPager(podiumId.value.orEmpty()).liveData
            .cachedIn(viewModelScope)
    }

    val onChallengeJoined = LiveEvent<YallaGuysJoinResponse.YallaJoinStatus>()

    val onChallengeJoinFull = LiveEvent<Boolean>()
    val onChallengeJoinError = LiveEvent<String>()

    fun joinChallenge(challenge: YallaGuysChallenge) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.joinYallaChallenge(_podiumId.value?: return@launch, challenge.challengeId)) {
                    is ResultOf.Success -> {
                        onChallengeJoined.postValue(result.value.status)
                    }

                    is ResultOf.APIError -> {
                        if (result.code==403) {
                            onChallengeJoinFull.postValue(true)
                        }
                        else if (canShowAPIErrorMessage(result.code)) {
                            onChallengeJoinError.postValue(result.errorMessage())
                        }
                    }

                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "sendPodiumInvitation: error: ${e.message}")
            }
        }
    }

}