package com.app.messej.ui.home.gift

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.repository.GiftRepository

class GiftConversionHistoryViewModel(application: Application) : AndroidViewModel(application) {
    private val giftRepository = GiftRepository(application)
    val giftConversionHistory = giftRepository.getGiftConversionHistoryPager("conversions").liveData.cachedIn(viewModelScope)
}