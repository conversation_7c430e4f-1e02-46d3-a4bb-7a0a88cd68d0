package com.app.messej.ui.home.publictab.postat

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.enums.PostatTab

class PostatFullFeedViewModel(application: Application) : PostatFeedBaseViewModel(application) {

    override val _postatList: LiveData<PagingData<Postat>> = postatRepo.getPostatFeedPager(PostatTab.ALL).liveData.cachedIn(viewModelScope).map {
        it.map { it.postat }
    }

}