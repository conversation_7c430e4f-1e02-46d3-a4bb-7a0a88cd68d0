package com.app.messej.ui.home.publictab.postat.create

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.annotation.OptIn
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.get
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.data.model.AbstractUriMedia
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.entity.Postat
import com.app.messej.databinding.FragmentCreatePostatBinding
import com.app.messej.databinding.LayoutPodiumCameraDisabledBinding
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import java.lang.ref.WeakReference

class CreatePostatFragment : Fragment(), (PostatViewerViewPagerAdapter.PlayerActionListener) {

    private lateinit var binding: FragmentCreatePostatBinding
    val viewModel: CreatePostatViewModel by navGraphViewModels(R.id.nav_create_postat)

    private lateinit var mPostatViewerViewPagerAdapter: PostatViewerViewPagerAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_create_postat, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    override fun onResume() {
        super.onResume()
        viewModel.setOnMediaPickerPage(false)
        playMediaIfReady()
    }

    override fun onPause() {
        super.onPause()
        stopMediaPlayback()
    }

    override fun onStart() {
        super.onStart()
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("CPVM", "handleOnBackPressed:")
                if (viewModel.isDraftMode.value == true) {
                    findNavController().popBackStack(R.id.nav_create_postat, true)
                } else {
                    findNavController().popBackStack()
                }
            }
        })
    }

    private fun observer() {
        viewModel.postatMedia.observe(viewLifecycleOwner) {
            it?: return@observe
            mPostatViewerViewPagerAdapter.updateData(it)
        }

        viewModel.mentionedUsers.observe(viewLifecycleOwner) { user ->
            binding.mentionSubText.text = user.joinToString(separator = ", ") { it.name }
        }

        viewModel.postatVideoProgress.observe(viewLifecycleOwner) { progress ->
            binding.encodeProgress.progress = progress
            if (progress == null || progress < 0) {
                binding.encodeProgress.text = resources.getString(R.string.postat_processing_media)
            } else {
                binding.encodeProgress.text = resources.getString(R.string.postat_processing_media_progress, progress)
            }
        }

        viewModel.postatVideoProgress.observe(viewLifecycleOwner) {
            stopMediaPlayback()
        }

        viewModel.mediaTransfer.observe(viewLifecycleOwner) { transfer ->
            val progress = transfer?.progress
            binding.uploadProgress.progress = progress ?: 0
            progress ?: return@observe
            if (progress < 0) {
                binding.uploadingText.text = resources.getString(R.string.postat_media_uploading)
            } else {
                binding.uploadingText.text = resources.getString(R.string.postat_media_uploading_progress, progress)
            }
        }

        viewModel.uploadCompleted.observe(viewLifecycleOwner) { type ->
            val messageResId = if (type == Postat.PostatType.DRAFT) {
                R.string.post_saved_to_draft_toast
            } else {
                R.string.post_successfully_shared_toast
            }
            Toast.makeText(requireContext(), getString(messageResId), Toast.LENGTH_SHORT).show()
            findNavController().popBackStack(R.id.nav_create_postat, true)
        }

        viewModel.onPostatSavedToDraft.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), getString(R.string.post_saved_to_draft_toast), Toast.LENGTH_SHORT).show()
            findNavController().popBackStack(R.id.nav_create_postat, true)
        }

        viewModel.onPostatDeleted.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), getString(R.string.postat_deleted), Toast.LENGTH_SHORT).show()
            findNavController().popBackStack(R.id.nav_create_postat, true)
        }

        viewModel.onPostatPosted.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(requireContext(), getString(R.string.post_successfully_shared_toast), Toast.LENGTH_SHORT).show()
                findNavController().popBackStack(R.id.nav_create_postat, true)
            } else Toast.makeText(requireContext(), getString(R.string.default_unknown_error_message), Toast.LENGTH_SHORT).show()
        }
    }

    private fun setup() {
        setupPlayer()
        mPostatViewerViewPagerAdapter = PostatViewerViewPagerAdapter(this, mutableListOf())
        binding.selectedMediaViewPager.apply {
            adapter = mPostatViewerViewPagerAdapter
            orientation = ViewPager2.ORIENTATION_HORIZONTAL
            currentItem = 1
        }
        binding.dotsIndicator.attachTo(binding.selectedMediaViewPager)

        binding.musicLayout.setOnClickListener {
            findNavController().navigateSafe(CreatePostatFragmentDirections.actionCreatePostatFragmentToPostatAudioListFragment())
        }

        binding.mentionLayout.setOnClickListener {
            findNavController().navigateSafe(CreatePostatFragmentDirections.actionCreatePostatFragmentToPostatMentionListFragment())
        }

        binding.previousButton.setOnClickListener {
            if (viewModel.isDraftMode.value == true) {
                findNavController().popBackStack(R.id.nav_create_postat, true)
            } else findNavController().popBackStack()
        }

        binding.postButton.setOnClickListener {
            viewModel.postPostat()
        }
        binding.draftButton.text = if (viewModel.isPremium) resources.getString(R.string.postat_draft) else resources.getString(R.string.common_discard)
        binding.draftButton.setOnClickListener {
            if (viewModel.isPremium){
                viewModel.postPostat(true)
            }else{
                actionDiscardPostat()
            }


        }

        binding.deleteButton.setOnClickListener {
            confirmPostatDelete()
        }
    }

    private fun actionDiscardPostat() {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumCameraDisabledBinding>(layoutInflater, R.layout.layout_podium_camera_disabled, null, false)
            view.textHeader = getString(R.string.postat_discard_free_user)
            view.canUpgrade = false
            view.canBuy = true
            view.actionClose.text = resources.getString(R.string.common_cancel)
            view.actionBuy.text = resources.getString(R.string.common_confirm)

            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
            view.actionBuy.setOnClickListener {
                findNavController().popBackStack(R.id.nav_create_postat, true)
                dismiss()
            }
            view.actionUpgrade.setOnClickListener {
            }
        }
    }

    private var futurePlaybackObject: WeakReference<PostatViewerViewPagerAdapter.FuturePlaybackObject>? = null

    @OptIn(UnstableApi::class)
    fun getMediaSource(media: MediaItem): MediaSource? {
        val cookie = viewModel.cookie.value?:return null
        val factory = DefaultHttpDataSource.Factory()
            .setUserAgent(Util.getUserAgent(requireContext(), "Flashat"))
            .setDefaultRequestProperties(mapOf("Cookie" to cookie.cookieValue))
            .setAllowCrossProtocolRedirects(true)

        val dataSourceFactory = DefaultDataSource.Factory(requireContext(), factory)

        return ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(media)
    }

    override fun registerForFuturePlayback(obj: PostatViewerViewPagerAdapter.FuturePlaybackObject) {
        futurePlaybackObject = WeakReference(obj)
        Log.w("POSTATF", "registerForFuturePlayback: ${obj.pos}")
        playMediaIfReady()
    }

    override fun detachFuturePlayback(pos: Int) {
        if (futurePlaybackObject?.get()?.pos == pos) {
            Log.w("POSTATF", "detachFuturePlayback: $pos")
            futurePlaybackObject = null
        }
    }

    private fun playMediaIfReady() {
        val videoPlayer = vPlayer?: return
        fun play(fpo: PostatViewerViewPagerAdapter.FuturePlaybackObject) {
            videoPlayer.setupPlayerWithMedia(fpo.media, viewModel.useOriginalAudio.value != true)
            fpo.onPlay.invoke(videoPlayer)
        }
        futurePlaybackObject?.get()?.also { fpo ->
            Log.w("POSTATF", "playMediaIfReady: found FPO: ${fpo.pos}")
            play(fpo)
        }?: run {
//                Log.w("POSTATF", "playMediaIfReady: trying to find current FPO")
            val vh = (binding.selectedMediaViewPager[0] as RecyclerView).findViewHolderForAdapterPosition(binding.selectedMediaViewPager.currentItem)
//                Log.w("POSTATF", "playMediaIfReady: found ViewHolder for pos ${binding.mediaViewPager.currentItem}")
            val fpo = (vh as? PostatViewerViewPagerAdapter.ViewPagerViewHolder)?.bindPlayer()?: return@run
            Log.w("POSTATF", "playMediaIfReady: found current FPO | ${fpo.pos}")
            play(fpo)
        }
    }

    private fun stopMediaPlayback() {
        vPlayer?.stop()
        futurePlaybackObject?.get()?.let { fpo ->
            Log.w("POSTATF", "stopMediaPlayback: found FPO: ${fpo.pos}")
            fpo.onStop.invoke()
        }
    }

    private var vPlayer: ExoPlayer? = null

    private fun releasePlayer() {
        stopMediaPlayback()
        futurePlaybackObject = null
        vPlayer?.apply {
            stop()
            release()
            vPlayer = null
        }
    }

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    private fun setupPlayer() {
        if (vPlayer == null) {
            vPlayer = ExoPlayer.Builder(requireContext()).build()
        }
        vPlayer?.apply {
            repeatMode = Player.REPEAT_MODE_ONE
            playWhenReady = false
            prepare()
        }
    }

    @OptIn(UnstableApi::class)
    private fun ExoPlayer.setupPlayerWithMedia(post: AbstractUriMedia?, mute: Boolean = false) {
        clearMediaItems()

        if (post is PostatDeviceMedia) {
            setMediaItem(post.mediaItem)
        } else if (post is PostatMedia){
            post.mediaUrl?: return
            val mediaSource = getMediaSource(MediaItem.fromUri(post.mediaUrl))?: return
            setMediaSource(mediaSource)
        }

        prepare()
        volume = if(mute) 0f else 1f
        play()
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    private fun confirmPostatDelete() {
        confirmAction(message = getString(R.string.postat_delete_post)){
            viewModel.deletePost()
        }
    }

}