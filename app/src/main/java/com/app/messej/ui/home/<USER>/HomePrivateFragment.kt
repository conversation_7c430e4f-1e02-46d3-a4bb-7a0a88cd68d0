package com.app.messej.ui.home.privatetab

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.databinding.FragmentHomePrivateBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.SubscriptionStatusViewModel
import com.app.messej.ui.home.privatetab.messages.PrivateMessagesBaseFragment
import com.app.messej.ui.home.privatetab.messages.PrivateMessagesViewPagerFragment
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.FragmentExtensions.adjustForSubNavDot
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindGiftRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindLegalAffairsPayFineButton
import com.app.messej.ui.utils.FragmentExtensions.bindMaidanToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.setBadgeNumber
import com.app.messej.ui.utils.FragmentExtensions.setupPayFineIcon
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils
import com.google.android.material.tabs.TabLayoutMediator

class HomePrivateFragment : Fragment(), MenuProvider {
    private lateinit var binding: FragmentHomePrivateBinding

    private val homeViewModel: CommonHomeViewModel by activityViewModels()
    private val viewModel: HomePrivateViewModel by activityViewModels()
    private val subscriptionStatusViewModel: SubscriptionStatusViewModel by activityViewModels()

    private val args: HomePrivateFragmentArgs by navArgs()

    companion object {
        enum class PrivateTab {
            MESSAGES,
            GROUPS,
            INTRUDERS
        }
    }

    private lateinit var mPrivatePagerAdapter: FragmentStateAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_home_private, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.appbar.toolbar, customBackButton = false)
        bindFlaxRateToolbarChip(binding.appbar.flaxRateChip)
        bindGiftRateToolbarChip(binding.appbar.giftChip)
        bindMaidanToolbarChip(binding.appbar.maidanChip)
        bindLegalAffairsPayFineButton(binding.appbar.payFineChip)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
        PrivateTab.values().getOrNull(args.tab)?.let {
            viewModel.setCurrentTab(it,true)
        }
        binding.appbar.interactionBanner.upgradeTitle.setOnClickListener{
            upgradeToPremium()
        }
        binding.appbar.upgradeBanner.upgradeBannerLayout.setOnClickListener{
            upgradeToPremium()
        }
        binding.appbar.upgradeBanner.dismissUpgradeBannerBtn.setOnClickListener {
            homeViewModel.onDismissUpgradeBanner()
        }
    }

    private fun upgradeToPremium() {

        subscriptionStatusViewModel.isActive.value?.let { isActive ->
            when (isActive) {
                UserSubscriptionStatus.ACTIVE, UserSubscriptionStatus.EXPIRED -> {
                    findNavController().navigateSafe(
                        NavGraphHomeDirections.actionGlobalAlreadySubscribedFragment(false)
                    )
                }

                else -> {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
                }
            }
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_home_notifications,menu)
    }

    private var notificationBadge: BadgeDrawable? = null

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        notificationBadge = BadgeDrawable.create(requireContext())
        setBadgeNumber(notificationBadge,homeViewModel.unreadNotifications.value)
        notificationBadge?.apply {
            adjustForNotifications(requireContext())
            BadgeUtils.attachBadgeDrawable(this, binding.appbar.toolbar, R.id.action_notifications)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_notifications -> findNavController().navigateSafe(HomePrivateFragmentDirections.actionGlobalNotificationFragment())
            else -> return false
        }
        return true
    }

    private fun setup() {
        mPrivatePagerAdapter = object : FragmentStateAdapter(childFragmentManager,viewLifecycleOwner.lifecycle) {

            override fun getItemCount(): Int {
                val list = if (homeViewModel.isPremiumUser.value == true) PrivateTab.entries.toTypedArray() else PrivateTab.entries.except(PrivateTab.INTRUDERS)
                return list.size
            }

            override fun createFragment(position: Int): Fragment {
                return when (position) {
                    PrivateTab.MESSAGES.ordinal -> PrivateMessagesViewPagerFragment().apply {
                        arguments = PrivateMessagesBaseFragment.getTabBundle(PrivateChat.PrivateMessageTabType.BUDDIES)
                    }
                    PrivateTab.GROUPS.ordinal -> PrivateGroupsViewPagerFragment()
                    PrivateTab.INTRUDERS.ordinal -> PrivateMessagesViewPagerFragment().apply {
                        arguments = PrivateMessagesBaseFragment.getTabBundle(PrivateChat.PrivateMessageTabType.INTRUDER)
                    }
                    else -> throw java.lang.IllegalArgumentException("There should only be 3 tabs")
                }
            }
        }

        binding.privatePager.apply {
            isUserInputEnabled = false
            adapter = mPrivatePagerAdapter
            registerOnPageChangeCallback (object: ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    viewModel.setCurrentTab(PrivateTab.values()[position])
                }
            })
        }

        TabLayoutMediator(binding.appbar.homeTab, binding.privatePager) { tab, position ->
            when(position) {
                PrivateTab.MESSAGES.ordinal -> {
                    tab.text = resources.getString(R.string.home_private_tab_buddies)
                    tab.setIcon(R.drawable.ic_home_private_tab_buddies)
                }
                PrivateTab.GROUPS.ordinal -> {
                    tab.text = resources.getString(R.string.home_private_tab_lounges)
                    tab.setIcon(R.drawable.ic_home_private_tab_lounges)
                }
                PrivateTab.INTRUDERS.ordinal -> {
                    tab.text = resources.getString(R.string.home_private_tab_intruders)
                    tab.setIcon(R.drawable.ic_home_private_tab_intruders)
                }
            }
        }.attach()

        setupPromoBoard(binding.appbar.promoBar)
        setupPayFineIcon(composeView = binding.appbar.payFine)
    }

    fun observe() {
        homeViewModel.accountDetails.observe(viewLifecycleOwner){
            binding.appbar.citizenship = it?.citizenship
            binding.appbar.isPremium = it?.isPremium
            binding.appbar.daysLeft = it?.remainingDaysForResident
        }
        homeViewModel.unreadBuddiesChats.observe(viewLifecycleOwner) {
            binding.appbar.homeTab.getTabAt(PrivateTab.MESSAGES.ordinal)?.orCreateBadge?.apply {
                isVisible = it > 0
                adjustForSubNavDot(requireContext())
            }
        }
        homeViewModel.didDismissUpgradeBannerToday.observe(viewLifecycleOwner) {
            binding.appbar.showUpgradeBanner = !it && homeViewModel.isPremiumUser.value==false
        }

        homeViewModel.unreadIntruderChats.observe(viewLifecycleOwner) {
            binding.appbar.homeTab.getTabAt(PrivateTab.INTRUDERS.ordinal)?.orCreateBadge?.apply {
                isVisible = it > 0
                adjustForSubNavDot(requireContext())

            }
        }

        homeViewModel.unreadPrivateHuddles.observe(viewLifecycleOwner) {
            binding.appbar.homeTab.getTabAt(PrivateTab.GROUPS.ordinal)?.orCreateBadge?.apply {
                isVisible = it > 0
                adjustForSubNavDot(requireContext())

            }
        }
        homeViewModel.unreadNotifications.observe(viewLifecycleOwner) {
            setBadgeNumber(notificationBadge,it)
        }

        homeViewModel.showProfileCompletionDialog.observe(viewLifecycleOwner) {
            Log.d("HOMEFRG", "observe: showProfileCompletionDialog is $it")
            if (it==true) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalIncompleteProfileDialogFragment())
            }
        }
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            Log.w("HPVM", "observe currentTab: $it ${it.ordinal}, pager: ${binding.privatePager.currentItem}")
            if (binding.privatePager.currentItem==it.ordinal) return@observe
            binding.privatePager.setCurrentItem(it.ordinal,false)
        }

        subscriptionStatusViewModel.subscriptionLoaded.observe(viewLifecycleOwner) {
            it?.let { clickable ->
                binding.appbar.upgradeBanner.clickable = clickable
                binding.appbar.interactionBanner.clickable = clickable
            }
        }
    }
}
