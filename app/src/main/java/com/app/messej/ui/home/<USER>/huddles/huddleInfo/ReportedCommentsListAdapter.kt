package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.view.LayoutInflater
import android.view.ViewGroup
import com.app.messej.data.model.entity.HuddleReportedComment
import com.app.messej.databinding.ItemHuddleReportedCommentBinding
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.chat.adapter.ChatAdapter

class ReportedCommentsListAdapter(
    private val inflater: LayoutInflater,
    private val userId: Int,
    private var mListener: ChatClickListener,
    private val reportListener: ReportActionListener
): ChatAdapter(inflater, userId, false, mListener) {

    interface ReportActionListener {
        fun onDeleteAction(item: HuddleReportedComment, position: Int)
        fun onViewComment(item: HuddleReportedComment, position: Int)
        fun onViewReporters(item: HuddleReportedComment, position: Int)
    }

    companion object {
        const val ITEM_REPORTED_COMMENT = 41
        const val ITEM_REPORTED_MESSAGE = 42
    }

        override fun getItemViewType(position: Int): Int {
        return when (peek(position)) {
            is ChatMessageUIModel.PostCommentModel -> ITEM_REPORTED_COMMENT
            is ChatMessageUIModel.ChatMessageModel -> ITEM_REPORTED_MESSAGE
            else -> throw IllegalStateException("Unknown view")
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        return if(viewType == ITEM_REPORTED_COMMENT) {
            ReportedCommentViewHolder(ItemHuddleReportedCommentBinding.inflate(inflater, parent, false), userId, mListener, reportListener)
        }
        else super.onCreateViewHolder(parent, viewType)
    }
}