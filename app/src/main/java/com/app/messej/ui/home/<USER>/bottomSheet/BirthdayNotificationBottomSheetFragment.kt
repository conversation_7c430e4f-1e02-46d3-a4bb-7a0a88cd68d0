package com.app.messej.ui.home.gift.bottomSheet

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BirthdayNotificationBottomSheetFragment : BirthdayBaseBottomSheetFragment() {
    private val args: BirthdayNotificationBottomSheetFragmentArgs by navArgs()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()

    }
    fun setUp() {
        viewModel.setBirthday(args.userId)
        Log.d("NOTi_USERID",""+viewModel._profile.value.toString())
        binding.currentBirthday = args.currentBirthday

        binding.birthdayUserDp.setOnClickListener {
            findNavController().navigateSafe(BirthdayAlertBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(viewModel.firstBirthday.value?.userId ?:return@setOnClickListener))
        }
    }
}