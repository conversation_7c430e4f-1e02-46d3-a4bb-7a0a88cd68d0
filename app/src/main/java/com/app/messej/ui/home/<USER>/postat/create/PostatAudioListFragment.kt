package com.app.messej.ui.home.publictab.postat.create

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.Util
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.postat.MusicFile
import com.app.messej.databinding.FragmentPostatAudioListBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutListStateErrorBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class PostatAudioListFragment : Fragment() {

    lateinit var binding: FragmentPostatAudioListBinding

    val viewModel: CreatePostatViewModel by navGraphViewModels(R.id.nav_create_postat)

    private var audioListAdapter: PostatAudioListAdapter? = null

    private var player: ExoPlayer? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_audio_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.removeKeywordFromSearchBox()
    }

    private fun setup() {
        setupPlayer()
        initAdapter()
        setEmptyView()

        binding.closeButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.doneButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.originalAudioSwitch.setOnCheckedChangeListener { buttonView, isChecked ->
            if(buttonView.isPressed) {
                viewModel.useOriginalAudio(isChecked)
            }
            if (isChecked) {
                player?.stop()
            }
        }
    }

    private fun observer() {
        viewModel.audioList.observe(viewLifecycleOwner) {
            it?.let {
                audioListAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
            }
        }
        viewModel.onDeselectAudio.observe(viewLifecycleOwner) {
            Log.w("PALF", "onDeselectAudio: $it", )
            audioListAdapter?.notifyItemChanged(it)
        }
    }

    private fun initAdapter() {
        audioListAdapter = PostatAudioListAdapter(object : PostatAudioListAdapter.AudioActionListener {
            override fun onAudioClick(music: CreatePostatViewModel.SelectableAudioUIModel, layoutPosition: Int) {
//                playAudio(musicFile.mediaMeta.mediaUrl, viewModel.selectedAudio.value?.mediaMeta?.musicId == musicFile.mediaMeta.musicId)
                viewModel.selectAudio(music, layoutPosition)
                setupPlayerWithMedia(music.audio)
            }

        }, viewLifecycleOwner).apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount, true)
                binding.multiStateView.viewState = state
            }
        }

        binding.musicList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = audioListAdapter
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    private fun setupPlayer() {
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build()
        }
        player?.apply {
            repeatMode = Player.REPEAT_MODE_OFF
            prepare()
        }
    }

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    fun setupPlayerWithMedia(audio: MusicFile) {
        val cookie = viewModel.cookie.value?:return
        val factory = DefaultHttpDataSource.Factory()
            .setUserAgent(Util.getUserAgent(requireContext(), "Flashat"))
            .setDefaultRequestProperties(mapOf("Cookie" to cookie.cookieValue))
            .setAllowCrossProtocolRedirects(true)

        val dataSourceFactory = DefaultDataSource.Factory(requireContext(), factory)

        val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
            .createMediaSource(MediaItem.fromUri(audio.mediaMeta.mediaUrl))
        Log.w("APLAY", "setupPlayerWithMedia: ${cookie.cookieValue}")
        player?.apply {
            Log.w("APLAY", "setupPlayerWithMedia: ${audio.mediaMeta.mediaUrl}")
            setMediaSource(mediaSource)
            prepare()
            play()
        }
    }

    private fun releasePlayer() {
        player?.apply {
            stop()
            release()
            player = null
        }
    }

    override fun onStop() {
        super.onStop()
        player?.apply {
            if (isPlaying) pause()
        }
    }

    fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            message = R.string.postat_audio_eds
        )
        val errorView: View = binding.multiStateView.getView(MultiStateView.ViewState.ERROR) ?: return
        val errorViewBinding = LayoutListStateErrorBinding.bind(errorView)
        errorViewBinding.prepare(
            message = R.string.default_eds_error_message,
            action = R.string.common_retry
        ) {
            audioListAdapter?.refresh()
        }
    }


}