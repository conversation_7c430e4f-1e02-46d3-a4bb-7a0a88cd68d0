package com.app.messej.ui.home.publictab.broadcast

import android.content.DialogInterface
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.R
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.BroadcastTab
import com.app.messej.databinding.FragmentPublicBroadcastBinding
import com.app.messej.ui.enforcements.EnforcementsViewModel
import com.app.messej.ui.home.publictab.stars.PublicStarsInnerListFragment
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.google.android.material.dialog.MaterialAlertDialogBuilder

abstract class PublicBroadcastBaseFragment : Fragment(), MenuProvider {

    protected lateinit var binding: FragmentPublicBroadcastBinding

    protected val viewModel: PublicBroadcastViewModel by activityViewModels()
    protected val enforcementVieModel: EnforcementsViewModel by activityViewModels()

    private lateinit var mPagerAdapter: FragmentStateAdapter

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.w("MNUF", "PBF: addAsMenuHost")
        addAsMenuHost()
        setup()
        observe()
    }
    
    abstract val tabList: List<BroadcastTab>

    private fun setup() {

        Log.w("PBBF", "setup: tabList = $tabList", )
        mPagerAdapter = object: FragmentStateAdapter(this) {

            override fun getItemCount(): Int = tabList.size

            override fun createFragment(position: Int): Fragment {
                val tab = tabList[position]
                Log.w("PBBF", "setup: tab for position $position = $tab", )
                return if (tab == BroadcastTab.TAB_STARS) {
                    PublicStarsInnerListFragment()
                } else {
                    PublicBroadcastListFragment().apply {
                        arguments = PublicBroadcastListFragment.getTabBundle(tab)
                    }
                }
            }
        }.apply {

        }

        binding.broadcastPager.apply {
            isUserInputEnabled = false
            adapter = mPagerAdapter

        }
        binding.btnFollowing.setOnClickListener {
            viewModel.setCurrentTab(BroadcastTab.TAB_STARS)
        }
        binding.btnDears.setOnClickListener {
            viewModel.setCurrentTab(BroadcastTab.TAB_DEARS)
        }
        binding.btnFans.setOnClickListener {
            viewModel.setCurrentTab(BroadcastTab.TAB_FANS)
        }
        binding.btnLikers.setOnClickListener {
            viewModel.setCurrentTab(BroadcastTab.TAB_LIKERS)
        }
        binding.broadcastFab.setOnClickListener{
            val mode = when(viewModel.currentTab.value) {
                BroadcastTab.TAB_STARS -> null
                BroadcastTab.TAB_DEARS -> BroadcastMode.ALL_DEARS
                BroadcastTab.TAB_FANS -> BroadcastMode.ALL_FANS
                BroadcastTab.TAB_LIKERS -> BroadcastMode.ALL_LIKERS
                null -> null
            }

            if(enforcementVieModel.userBanned || enforcementVieModel.userIsSuspectedToBan) {
                showBannedAlertDialog()
                return@setOnClickListener
            }
            mode?.let {
                toBroadcast(it,null)
            }
        }

    }

    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            if (binding.broadcastPager.currentItem==tabList.indexOf(it)) return@observe
            binding.broadcastPager.setCurrentItem(tabList.indexOf(it),false)
        }

        viewModel.canBroadcast.observe(viewLifecycleOwner) {
            if (it) binding.broadcastFab.show() else binding.broadcastFab.hide()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        Log.w("MNUF", "PBF: onCreateMenu")
        return menuInflater.inflate(R.menu.menu_home_broadcast,menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> {
                toSearch()
            }
            else -> return false
        }
        return true
    }

    private fun showBannedAlertDialog() {
        val alertDialog = MaterialAlertDialogBuilder(requireContext())
            .setMessage(getString(R.string.chat_banned_user_broadcast_message))
            .setNegativeButton(getString(R.string.common_close)) { dialog, _ -> dialog.dismiss() }
            .create()

        alertDialog.show()
        val negativeButton = alertDialog.getButton(DialogInterface.BUTTON_NEGATIVE)
        negativeButton.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorError))
    }

    abstract fun toSearch()
    abstract fun toBroadcast(broadcastMode: BroadcastMode, char: Char?)

}