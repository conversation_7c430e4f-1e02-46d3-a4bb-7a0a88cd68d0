package com.app.messej.ui.home.publictab.maidan

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.databinding.FragmentCreateMaidanBinding
import com.app.messej.ui.home.publictab.podiums.challenges.create.ContributorSearchAdapter
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlin.math.roundToInt

class CreateMaidanFragment : Fragment() {

    private lateinit var binding: FragmentCreateMaidanBinding
    private val viewModel: CreateMaidanViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_create_maidan, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        binding.cancelButton.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.createButton.setOnClickListener {
            viewModel.createMaidanChallenge()
        }
    }

    private fun observe() {

        val textInputEditText = binding.textInputChallengerSearch.editText as AutoCompleteTextView
        textInputEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val searchText = s.toString()
                if (searchText.isNotEmpty()) {
                    viewModel.searchContributor(searchText)
                } else {
                    textInputEditText.dismissDropDown()
                }
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })

        viewModel.contributorsSearchList.observe(viewLifecycleOwner) {
            if (it != null) {
                val mAdapter = ContributorSearchAdapter(requireContext(), it)
                (binding.textInputChallengerSearch.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(mAdapter)
                    mAdapter.notifyDataSetChanged()
                    setOnItemClickListener { _, _, position, _ ->
                        mAdapter.getItem(position).let { podiumSpeaker ->
                            text = null
                            viewModel.onContributorSelected(podiumSpeaker)
                        }
                    }
                }
            }
        }

        viewModel.onMaidanCreated.observe(viewLifecycleOwner) {
            if ((it.challenge?.challengeFee ?: 0.0) != 0.0) {
                showToast(getString(R.string.podium_maidan_fee_debit_toast, it.challenge?.challengeFee?.roundToInt().toString().orEmpty()))
            }
            val options = NavOptions.Builder()
            .setPopUpTo(R.id.publicMaidanFragment, inclusive = true)
            .build()
            findNavController().navigateSafe(PublicMaidanFragmentDirections.actionGlobalNavLivePodium(it.id, kind = PodiumKind.MAIDAN.ordinal),options)
        }
        viewModel.onMaidanCreateError.observe(viewLifecycleOwner) {
            showSnackbar(it)
        }

        viewModel.createMaidanLoading.observe(viewLifecycleOwner) {

        }

        viewModel.onLiveInAnotherPodium.observe(viewLifecycleOwner) {
            val builder = MaterialAlertDialogBuilder(requireContext()).apply {
                setCancelable(false)
                setMessage(it.message)
                if (it.canLeave) {
                    setPositiveButton(resources.getString(R.string.podium_maidan_exit_and_create)) { dialog, which ->
                        dialog.dismiss()
                        viewModel.leaveOtherPodium(it.otherPodiumId)
                    }
                    setNegativeButton(resources.getString(R.string.common_cancel)) { dialog, which ->
                        dialog.dismiss()
                        val action = NavGraphHomeDirections.actionGlobalNavLivePodium(it.otherPodiumId)
                        val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                        findNavController().navigateSafe(action, options)
                    }
                } else {
                    setPositiveButton(resources.getString(R.string.podium_join_back)) { dialog, which ->
                        dialog.dismiss()
                        val action = NavGraphHomeDirections.actionGlobalNavLivePodium(it.otherPodiumId)
                        val options = NavOptions.Builder().setPopUpTo(R.id.nav_live_podium, inclusive = true).build()
                        findNavController().navigateSafe(action, options)
                    }
                }
            }
            builder.show()
        }

        viewModel.onLeftOtherPodium.observe(viewLifecycleOwner) {
            viewModel.createMaidanChallenge()
        }
    }
}