package com.app.messej.ui.home.publictab.podiums.create

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumInviteBinding
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class PodiumInviteBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentPodiumInviteBinding

    private val viewModel: PodiumInviteViewModel by viewModels()

    val args: PodiumInviteBottomSheetFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_invite, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setPodiumId(args.podiumId)
        this.isCancelable = true
        setup()
        observe()
    }

    fun setup(){
        binding.cancelButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.sendButton.setOnClickListener {
            viewModel.sendPodiumInvitation()
        }
    }

    fun observe(){
        viewModel.invitationSent.observe(viewLifecycleOwner){
            viewModel.clearSelection()
            if (it){
                showToast(R.string.podium_invitation_sent)
                findNavController().popBackStack()
            }
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isFitToContents = true
                isDraggable = true
                skipCollapsed = true
                isCancelable=true
            }
        }
        return dialog
    }

}