package com.app.messej.ui.home.publictab.authorities.legalAffairs

import android.app.Application
import androidx.annotation.DrawableRes
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.ReportPreviewProvider
import com.app.messej.data.model.api.legal.AbstractCaseDetails
import com.app.messej.data.model.api.legal.CaseDetails
import com.app.messej.data.model.api.legal.LegalAffairsPayRequest
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.CaseVerdictAction
import com.app.messej.data.model.enums.LegalAffairsPaymentType
import com.app.messej.data.model.enums.ReportCaseStatus
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalAffairsRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.CountryListUtil
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class CaseDetailsViewModel(application: Application) : AndroidViewModel(application) {

    private val repo = LegalAffairsRepository(getApplication())
    protected val accountRepo = AccountRepository(application)
    private val profileRepo = ProfileRepository(application)

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    val user: CurrentUser get() = accountRepo.user

    private val caseId = MutableLiveData<Int>()

    fun setCaseID(id: Int) {
        caseId.value = id
        refreshCaseDetails(id)
    }

    init {
        initCountryList()
    }

    private var _countryList: Map<String, Int>? = null

    private fun initCountryList() {
        viewModelScope.launch(Dispatchers.IO) {
            _countryList = CountryListUtil.getCustomCountryMap()
        }
    }

    @DrawableRes
    fun getCountryFlag(code: String?): Int {
        code ?: return 0
        return _countryList?.get(code)?:0
    }

    private val _caseDetailsViewState = MutableLiveData<MultiStateView.ViewState?>(MultiStateView.ViewState.LOADING)
    val caseDetailsViewState: LiveData<MultiStateView.ViewState?> = _caseDetailsViewState

    private val _caseDetails = MutableLiveData<CaseDetails>()
    val caseDetails: LiveData<CaseDetails> = _caseDetails

    val userPrivate = _caseDetails.map {
        it?: return@map false
        return@map it.userPrivate(user.id)
    }

    val contentPreview: LiveData<ReportPreviewProvider?> = _caseDetails.map {
        it?: return@map null
        return@map object: ReportPreviewProvider {
            override val title: String? = if (it.reportedContentType == ReportContentType.PODIUM) it.caption else null
            override val message: String? = if (it.reportedContentType == ReportContentType.PODIUM) null else it.caption
            override val mediaMeta: MediaMeta? = it.firstMediaMeta?.asMediaMeta
            override val contentType: ReportContentType = it.reportedContentType
            override val reportType: ReportType = it.reportType
            override val user: AbstractUser? = it.userDetails
            override val countryFlag: Int? = getCountryFlag(it.userDetails?.countryCode)
            override val edited: Boolean = it.edited == true
            override val deleted: Boolean = it.deleted == true
        }
    }

    val canVoteInInvestigationBureau = _caseDetails.map {
        it?: return@map false
        return@map it.caseStatus== ReportCaseStatus.INVESTIGATION_BUREAU
                && it.eligibleToVote==true
                && it.userId!=user.id
                && it.reporterId!=user.id
                && (it.investigationBureau?.totalVotes?:0)<AbstractCaseDetails.MAX_VOTES_IB
    }

    val canDefendAsAdvocate = _caseDetails.map {
        it?: return@map false
        return@map it.caseStatus== ReportCaseStatus.ADVOCATES_UNION
                && it.eligibleToVote==true
                && it.userId!=user.id
                && it.reporterId!=user.id
    }

    val canVoteInJury = _caseDetails.map {
        it?: return@map false
        return@map it.caseStatus== ReportCaseStatus.JURY
                && it.eligibleToVote==true
                && it.userId!=user.id
                && it.reporterId!=user.id
                && (it.jury?.totalVotes?:0)<AbstractCaseDetails.MAX_VOTES_JURY
    }

    val showViolationCount = _caseDetails.map {
        it?: return@map false
        return@map it.caseStatus== ReportCaseStatus.JURY
                || it.caseStatus== ReportCaseStatus.ADVOCATES_UNION
    }

    val canHireAdvocate = _caseDetails.map {
        it?: return@map false
        return@map it.caseStatus == ReportCaseStatus.APPEAL
                && it.userId==user.id
    }

    val onGetCaseDetailsError = LiveEvent<Boolean>()

    fun refreshCaseDetails() {
        val id = caseId.value?: return
        refreshCaseDetails(id)
    }

    private fun refreshCaseDetails(id: Int) {
        viewModelScope.launch(Dispatchers.IO){
            _caseDetailsViewState.postValue(MultiStateView.ViewState.LOADING)
            when(val result = repo.getCaseDetails(id)){
                is ResultOf.Success -> {
                    _caseDetails.postValue(result.value)
                    _caseDetailsViewState.postValue(MultiStateView.ViewState.CONTENT)
                }
                is ResultOf.APIError -> {
                    onGetCaseDetailsError.postValue(true)
                    _caseDetailsViewState.postValue(MultiStateView.ViewState.ERROR)
                }
                is ResultOf.Error -> {
                    onGetCaseDetailsError.postValue(true)
                    _caseDetailsViewState.postValue(MultiStateView.ViewState.ERROR)
                }
            }
        }
    }

    val onActionDone = LiveEvent<Boolean>()
    val isJuryVoteCompleted = LiveEvent<Boolean>()
    val onActionError = LiveEvent<String>()
    val hireAdvocateSuccessfully = LiveEvent<Boolean>()

    fun voteIB(guilty: Boolean) {
        viewModelScope.launch(Dispatchers.IO){
            when(val result = repo.voteInInvestigation(caseId.value?: return@launch, guilty)){
                is ResultOf.Success -> {
                    onActionDone.postValue(true)
                }
                is ResultOf.APIError -> {
                    onActionError.postValue(result.errorMessage())
                }
                is ResultOf.Error -> {
                    onActionError.postValue(result.errorMessage())
                }
            }
        }
    }

    fun voteJury(guilty: Boolean, action: CaseVerdictAction, onSuccess: () -> Unit) {
        viewModelScope.launch(Dispatchers.IO){
            when(val result = repo.voteInJury(caseId.value?: return@launch, guilty, action)){
                is ResultOf.Success -> {
                    isJuryVoteCompleted.postValue(true)
                    onSuccess()
                }
                is ResultOf.APIError -> {
                    onActionError.postValue(result.errorMessage())
                }
                is ResultOf.Error -> {
                    onActionError.postValue(result.errorMessage())
                }
            }
        }
    }

    fun hireAdvocate(reportId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            val request = LegalAffairsPayRequest(paymentType = LegalAffairsPaymentType.REPORT_ADVOCATE_FEE, reportId = reportId, enforcement = null)
            when(val result = repo.legalAffairsPay(req = request)) {
                is ResultOf.Success -> {
                    profileRepo.getAccountDetails()
                    onActionDone.postValue(true)
                    hireAdvocateSuccessfully.postValue(true)
                }
                is ResultOf.APIError -> {
                    onActionError.postValue(result.errorMessage())
                }
                is ResultOf.Error -> {
                    onActionError.postValue(result.errorMessage())
                }
            }
        }
    }
}