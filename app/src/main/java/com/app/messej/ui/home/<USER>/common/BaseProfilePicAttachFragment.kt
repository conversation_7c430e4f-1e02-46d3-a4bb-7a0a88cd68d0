package com.app.messej.ui.home.publictab.common

import android.app.Activity
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.app.messej.R
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.launch


abstract class BaseProfilePicAttachFragment : Fragment() {

    protected abstract val viewModel : BaseProfilePicAttachViewModel

    protected abstract val bindingRoot: View

    protected open val shouldCropImage: Boolean = true


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
    }

    private fun observe() {
        viewModel.onTriggerCrop.observe(viewLifecycleOwner) {
            if (shouldCropImage) {
            val options = UCrop.Options().apply {
                setCircleDimmedLayer(true)
                val color = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                setToolbarColor(color)
                setStatusBarColor(color)
                setToolbarTitle(resources.getString(R.string.common_crop))
            }
            val crop = UCrop.of(it.first, it.second)
                .withAspectRatio(1f,1f)
                .withOptions(options)
            imageCropResult.launch(crop.getIntent(requireContext()))
        }
            else {
                viewModel.addCroppedImage(it.first)
            }

        }
    }

    private val imageCropResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let {
                val resultUri = UCrop.getOutput(it)?: return@let
                viewModel.addCroppedImage(resultUri)
            }
        } else {
            viewModel.onCropCancelled()
        }
    }

    protected fun selectImageFromGallery() = selectImageFromGalleryResult.launch("image/*")

    private val takeImageResult =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { isSuccess ->
            if (isSuccess) {
                viewModel.addCapturedImage()
            }
        }

    private val selectImageFromGalleryResult =
        registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
            uri?.let {
                viewModel.addImage(uri)
            }
        }

    protected fun takeImage() {
        checkCameraPermission(bindingRoot) {
            lifecycleScope.launch {
                val uri = viewModel.getImageUriForCapture()
                takeImageResult.launch(uri)
            }
        }
    }
}