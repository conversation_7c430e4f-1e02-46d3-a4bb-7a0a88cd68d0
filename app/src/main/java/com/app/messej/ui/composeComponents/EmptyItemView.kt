package com.app.messej.ui.composeComponents

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.app.messej.R
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer

@Preview(showBackground = true)
@Composable
fun ListEmptyItemView(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int = R.drawable.im_eds_huddles,
    @StringRes textId: Int = R.string.default_eds_error_message,
    textColor: Color = colorResource(id = R.color.textColorPrimary),
    bottomView : @Composable (() -> Unit)? = null
) {
    Column(
        modifier = modifier.padding(horizontal = dimensionResource(id = R.dimen.extra_margin)).fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Image(
            painterResource(id = icon),
            contentDescription = null
        )
        Text(
            text = stringResource(id = textId),
            color = textColor,
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.extra_margin)).fillMaxWidth(),
            style = FlashatComposeTypography.defaultType.body2,
            textAlign = TextAlign.Center
        )
        bottomView?.let {
            CustomVerticalSpacer(space = dimensionResource(id = R.dimen.extra_margin))
            it()
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ListErrorItemView(
    modifier: Modifier = Modifier,
    @DrawableRes icon: Int = R.drawable.im_eds_huddles,
    @StringRes textId: Int = R.string.default_eds_error_message,
    textColor: Color = colorResource(id = R.color.textColorPrimary),
    onRetry: () -> Unit = {}
) {
    Column(
        modifier = modifier.padding(horizontal = dimensionResource(id = R.dimen.extra_margin)).fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Image(
            painterResource(id = icon),
            contentDescription = null
        )
        Text(
            text = stringResource(id = textId),
            color = textColor,
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.extra_margin)).fillMaxWidth(),
            style = FlashatComposeTypography.defaultType.body2,
            textAlign = TextAlign.Center
        )
        Button(onClick = onRetry,
               modifier = Modifier.padding(top = dimensionResource(id = R.dimen.element_spacing)),
               colors = ButtonDefaults.buttonColors(
                   backgroundColor = colorResource(id = R.color.textColorSecondaryLight)
               )
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_refresh_24),
                    contentDescription = stringResource(id = R.string.common_retry),
                    tint = Color.White
                )
                Text(
                    text = stringResource(id = R.string.common_retry),
                    color = Color.White,
                    modifier = Modifier.padding(start = dimensionResource(id = R.dimen.line_spacing)),
                    style = FlashatComposeTypography.defaultType.button,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}