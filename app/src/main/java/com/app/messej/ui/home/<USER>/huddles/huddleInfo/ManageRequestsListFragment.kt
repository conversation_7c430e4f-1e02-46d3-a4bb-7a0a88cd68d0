package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.huddles.HuddleRequestsAdminActionRequest
import com.app.messej.data.model.api.huddles.HuddleRequestsResponse
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.databinding.FragmentManageRequestListBinding
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView

class ManageRequestsListFragment : Fragment() {

    private lateinit var binding: FragmentManageRequestListBinding
    private var mAdapter : ManageRequestsListAdapter? = null
    private val viewModel : ManageRequestInviteViewModel by viewModels(ownerProducer = { requireParentFragment() })


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_manage_request_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
    }

    private fun initAdapter() {
        mAdapter = ManageRequestsListAdapter(object : ManageRequestsListAdapter.ItemListener {

            override fun onItemClick(item: HuddleRequestsResponse.HuddleRequest, position: Int, menuItem: MenuItem) {
                when (menuItem.itemId) {
                    R.id.unblock_requested_user -> {
                        viewModel.blockUnblockUser(item.memberId, Participant.ParticipantsActionTypes.UNBLOCK_HUDDLE_PARTICIPANT)
                    }
                    else -> {}
                }
            }

            override fun onHuddleAction(item: HuddleRequestsResponse.HuddleRequest, action: HuddleRequestsAdminActionRequest.RequestsAdminAction) {
                if (action == HuddleRequestsAdminActionRequest.RequestsAdminAction.BLOCK){
                    viewModel.blockJoinRequest(item.memberId)
                }else{
                    viewModel.huddleRequestsAdminAction(item.memberId,action)
                }
            }
        })

        val layoutMan = LinearLayoutManager(context)
        binding.requestsList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).apply { visibility = View.GONE }
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = resources.getString(R.string.huddle_participants_no_result_found_text)
            findViewById<MaterialButton>(R.id.eds_empty_action).apply {
                visibility = View.GONE
            }
        }
    }

    private fun observe() {
        viewModel.huddleRequestList.observe(viewLifecycleOwner){
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.requestsAdminActionError.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }
    }

}