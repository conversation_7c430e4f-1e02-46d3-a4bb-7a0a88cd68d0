package com.app.messej.ui.home.gift

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.FlixPurchaseTypesTab
import com.app.messej.data.model.enums.PurchaseItem
import com.app.messej.databinding.FragmentFlaxPurchaseHistoryBinding
import com.app.messej.ui.home.businesstab.adapter.DealsFlixPurchaseHistoryPagerAdapter
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView

class FlixPurchaseHistoryFragment : Fragment() {

    private lateinit var binding: FragmentFlaxPurchaseHistoryBinding
    private val viewModel: FlaxPurchaseHistoryViewModel by viewModels()
    private var purchaseAdapter: DealsFlixPurchaseHistoryPagerAdapter? = null
    private val args: CoinPurchaseHistoryFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flax_purchase_history, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text =if(args.purchasetype==PurchaseItem.BUY_COIN) getString(R.string.title_coin_purchase_history) else getString(R.string.title_flax_purchase_history)
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        initAdapter()
        viewModel.purchaseItem.postValue(args.purchasetype)
       viewModel.setCurrentTab(FlixPurchaseTypesTab.TAB_OWN)

        binding.btnOwn.setOnClickListener {
            viewModel.setCurrentTab(FlixPurchaseTypesTab.TAB_OWN)
            (it as MaterialButton).isChecked = true
        }
        binding.btnOthers.setOnClickListener {
            viewModel.setCurrentTab(FlixPurchaseTypesTab.TAB_OTHERS)
            (it as MaterialButton).isChecked = true
        }
    }

    private fun observe() {
        viewModel.buyFlaxHistory?.observe(viewLifecycleOwner) {
            purchaseAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
        viewModel.purchaseItem.observe(viewLifecycleOwner) {
           Log.w("PurchaseItem",it.toString())
        }
        viewModel.currentTab.observe(viewLifecycleOwner){ currenttab->
            currenttab?.let { it ->
                if (it!=null) {
                    viewModel.setData(it)
                }
            }
        }

    }

    private fun initAdapter() {
        purchaseAdapter = DealsFlixPurchaseHistoryPagerAdapter()
        val layoutManParticipant = LinearLayoutManager(context)

        binding.flaxPurchaseHistoryList.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = purchaseAdapter
        }

        purchaseAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_no_flix_purchase_history)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text= context.getString(R.string.no_flix_purchases_found)

        }
    }


}