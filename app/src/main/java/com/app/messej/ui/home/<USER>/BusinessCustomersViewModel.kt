package com.app.messej.ui.home.businesstab

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

class BusinessCustomersViewModel(application: Application) : AndroidViewModel(application) {

    private var businessRepo = BusinessRepository(application)
    private var profileRepo = ProfileRepository(application)
    private val datastoreRepo = FlashatDatastore()
    private val accountRepo=AccountRepository(application)


    val businessOperation: LiveData<BusinessOperation?> = businessRepo.getOperations()

    private val _customerDetailsLoading = MutableLiveData<Boolean>(false)
    val customerDetailsLoading: LiveData<Boolean> = _customerDetailsLoading

    private val _customerDetailsError = MutableLiveData<String?>(null)
    val customerDetailsError: LiveData<String?> = _customerDetailsError

    private val _dearsCount = MutableLiveData<Int?>(null)
    val dearsCount: LiveData<Int?> = _dearsCount

//    private val _user = MutableLiveData<CurrentUser?>(null)
    val user = LiveEvent<CurrentUser?>()

    private val isStartPageClicked=LiveEvent<Boolean> ()
    private val mediatorLiveData = MediatorLiveData<Pair<BusinessOperation?, Boolean>>()

    val appLanguage = MutableLiveData<String?>()

    val combinedLiveData: LiveData<Pair<BusinessOperation?, Boolean>> = mediatorLiveData
    init {
        getUserAccount()
            getStartClicked()
        viewModelScope.launch {
            businessRepo.getFlashAtActivityDetails()
        }
        mediatorLiveData.addSource(businessOperation) { operation ->
            val isStartPageClickedValue = isStartPageClicked.value
            if (isStartPageClickedValue != null) {
                mediatorLiveData.value = Pair(operation, isStartPageClickedValue)
            }
        }

        mediatorLiveData.addSource(isStartPageClicked) { isClicked ->
            val businessOperationValue = businessOperation.value
            if (businessOperationValue != null) {
                mediatorLiveData.value = Pair(businessOperationValue, isClicked)
            }
        }
        getAppLanguage()
    }

    fun getAppLanguage(){
        viewModelScope.launch {
            val data = datastoreRepo.settingsFlow().first()
            appLanguage.postValue(data.appLanguage)
        }
    }

    private fun getStartClicked() {
       viewModelScope.launch {
           isStartPageClicked.postValue(datastoreRepo.isBusinessStartShown())
       }

    }

    private fun getUserAccount() {
        viewModelScope.launch {
            profileRepo.refreshAccountDetails()
        }
    }

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val _customersDearsList = profileRepo.getDearsListPager().liveData.cachedIn(viewModelScope)

    val customersDearsList: MediatorLiveData<PagingData<UserRelative>?> by lazy {
        val med = MediatorLiveData<PagingData<UserRelative>?>(null)
        fun updateMembersList() {
            val list = _customersDearsList.value?.map { item ->
                _nickNames.value?.findNickName(item.id)?.let { item.name = it }
                item
            }
            customersDearsList.postValue(list)

        }
        med.addSource(_customersDearsList) { updateMembersList() }
        med.addSource(_nickNames) { updateMembersList() }
        med
    }

    private val _customersFansList = profileRepo.getFansListPager().liveData.cachedIn(viewModelScope)

    val customersFansList: MediatorLiveData<PagingData<UserRelative>?> by lazy {
        val med = MediatorLiveData<PagingData<UserRelative>?>(null)
        fun updateMembersList() {
            val list = _customersFansList.value?.map { item ->
                _nickNames.value?.findNickName(item.id)?.let { item.name = it }
                item
            }
            customersFansList.postValue(list)
        }
        med.addSource(_customersFansList) { updateMembersList() }
        med.addSource(_nickNames) { updateMembersList() }
        med
    }


//    val customersDearsList = profileRepo.getDearsListPager().liveData.cachedIn(viewModelScope)
//
//    val customersFansList = profileRepo.getFansListPager().liveData.cachedIn(viewModelScope)

    val onNavigateToProfile = LiveEvent<Pair<Int,UserCitizenship>>()
    val onNavigateToPrivateMessage = LiveEvent<Pair<String,Int>>()

    fun navigateToProfile(user: UserRelative) {
        onNavigateToProfile.postValue(Pair(user.id,user.citizenship))
    }

    fun navigateToPrivateMessage(user: UserRelative) {
        viewModelScope.launch {
            val roomId = HuddlesRepository(getApplication()).getPrivateChatRoomId(user.id)
            onNavigateToPrivateMessage.postValue(Pair(roomId,user.id))
        }
    }

    private val _isListExpanded = MutableLiveData(false)
    val isListExpanded: LiveData<Boolean> = _isListExpanded

    fun setListExpanded(args: Boolean) {
        _isListExpanded.postValue(args)
    }

    val onCustomerListViewAll = LiveEvent<Boolean>()

    fun customerListViewAll() {
        onCustomerListViewAll.postValue(true)
    }
    fun setDearsCount(itemCount: Int?) {
        _dearsCount.postValue(itemCount)
    }
    fun shareReferralLink() {
        viewModelScope.launch {
            accountRepo.userFlow.collect {
                user.postValue(it)
            }
        }
    }

    fun countAppShare() {
        viewModelScope.launch {
            profileRepo.countAppShare()
        }
    }

}
