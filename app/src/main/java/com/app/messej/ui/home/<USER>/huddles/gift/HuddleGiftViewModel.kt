package com.app.messej.ui.home.publictab.huddles.gift

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.socket.GiftHuddlePayload
import com.app.messej.data.socket.repository.GiftEventRepository

class HuddleGiftViewModel(application: Application):AndroidViewModel(application) {
    private val giftEventRepo = GiftEventRepository
    fun sendHuddleGift(huddleId: Int, messageId: String) {
        giftEventRepo.sendHuddleGift(payload = GiftHuddlePayload(huddleId = HuddleChatMessage.prefixHuddleId(huddleId), messageId = messageId))
    }
}