package com.app.messej.ui.auth.profile

import android.app.Application
import androidx.lifecycle.viewModelScope
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.profile.BaseUsernameViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class RegisterUsernameViewModel(application: Application): BaseUsernameViewModel(application) {

    override fun checkUsernameAvailability(){
        _usernameAvailabilityLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<String> =
                profileRepo.checkOrSetUsername(username.value!!, true)) {
                is ResultOf.Success -> {
                    onCheckUsernameComplete.postValue(result.value)
                    _usernameAvailable.postValue(true)
                    _usernameAvailabilityLoading.postValue(false)
                }
                is ResultOf.APIError -> {
                    _checkUsernameError.postValue(result.error.message)
                    _usernameAvailable.postValue(false)
                    _usernameAvailabilityLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                    _usernameAvailabilityLoading.postValue(false)
                }
            }
        }
    }

    fun setUsername(){
        _usernameLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<String> =
                profileRepo.checkOrSetUsername(username.value!!)) {
                is ResultOf.Success -> {
                    onSetUsernameComplete.postValue(true)
                    _usernameLoading.postValue(false)
                }
                is ResultOf.APIError -> {
                    _setUsernameError.postValue(result.error.message)
                    _usernameLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                    _usernameLoading.postValue(false)
                }
            }
        }
    }
}