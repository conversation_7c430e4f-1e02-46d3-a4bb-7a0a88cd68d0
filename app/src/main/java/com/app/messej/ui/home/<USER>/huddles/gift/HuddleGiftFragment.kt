package com.app.messej.ui.home.publictab.huddles.gift

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.ui.home.gift.GiftListBottomSheetBaseFragment
import com.app.messej.ui.home.gift.GiftListingViewModel
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowGift
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class HuddleGiftFragment: GiftListBottomSheetBaseFragment() {

    private val args : HuddleGiftFragmentArgs by navArgs()
    private val huddleGiftViewModel: HuddleGiftViewModel by viewModels()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }
    private fun setup(){
        viewModel.setParams(GiftListingViewModel.GiftParams(
            receiver = args.receiverId,
            giftContext = GiftContext.GIFT_HUDDLE,
            contextId = args.huddleId.toString(),
            managerId = args.managerId
        ))
//        viewModel.loadGift(args.singleTabItem)
        setUpTabData()
    }

    private fun observe(){
        viewModel.onGiftSent.observe(viewLifecycleOwner){
            it.let {payload->
                if (payload.hasVideo) {
                    findNavController().popBackStack()
                    downloadAndShowGift(payload)
                }else{
                    findNavController().popBackStack()
                    val action = NavGraphHomeDirections.actionGlobalNotificationLottieBottomSheetFragment(it.id, args.receiverId, false)
                    val options = NavOptions.Builder().setPopUpTo(R.id.giftListFragment, inclusive = true).build()
                    findNavController().navigateSafe(action, options)

                }
            }
                giftCommonViewModel.getGiftList()
                huddleGiftViewModel.sendHuddleGift(args.huddleId,args.messageId?:"")
//                Toast.makeText(requireContext(), getString(R.string.gift_sent_successfully, viewModel.nameOrNickname.value), Toast.LENGTH_SHORT).show()
        }
    }
    override fun onGiftItemClick(item: GiftItem, preview:Boolean) {
        if(viewModel.isGiftLoading.value==true && preview) return
        viewModel.setGiftLoading(true)
        viewModel.sendGift(item,preview)
    }


}