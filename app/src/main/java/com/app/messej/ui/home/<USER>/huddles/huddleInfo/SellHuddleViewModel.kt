package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.huddles.SellHuddleResponse
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SellHuddleViewModel(application: Application) : AndroidViewModel(application) {
    val huddleRepo = HuddlesRepository(application)

    companion object {
        enum class SellAmountError {
            NONE, LIMIT_EXCEEDS
        }

        private const val MAX_FLAX_AMOUNT_LIMIT = 9999999.00
    }

    val isChecked = MutableLiveData(false)
    val flaxAmount = MutableLiveData<String>()
    private val huddleID = MutableLiveData<Int>()
    val isForSaleValid = MutableLiveData<Boolean>()
    val showSuccess = LiveEvent<String>()
    val errorMessage = LiveEvent<String>()

    private val flaxAmountNotEmpty = flaxAmount.map { it.isNotEmpty() }
    val editMode = MutableLiveData<Boolean>(false)


    private val _flaxAmountValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun checkFlaxAmount() {
            _flaxAmountValid.postValue(
                flaxAmount.value.orEmpty().isNotBlank() && validateFlaxAmount() == true && flaxAmountNotEmpty.value == true
            )
        }
        med.addSource(flaxAmountNotEmpty) { checkFlaxAmount() }

        med
    }
    val flaxAmountValid: LiveData<Boolean> = _flaxAmountValid

    val sellButtonEnable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            med.postValue(isChecked.value == true && flaxAmount.value != null && _flaxAmountValid.value == true)
        }
        med.addSource(isChecked) { check() }
        med.addSource(flaxAmount) { check() }
        med.addSource(_flaxAmountValid) { check() }
        med
    }
    private val _sellAmountError = MediatorLiveData(SellAmountError.NONE)
    val sellAmountError: LiveData<SellAmountError> = _sellAmountError
    fun validateFlaxAmount(): Boolean {
        if (flaxAmount.value!!.toDouble() >= MAX_FLAX_AMOUNT_LIMIT) {
            _sellAmountError.postValue(SellAmountError.LIMIT_EXCEEDS)
            return false
        } else {
            _sellAmountError.postValue(SellAmountError.NONE)
            return true
        }
    }


    fun setHuddleId(huddleId: Int, isForSale: Boolean, huddlePrice: String) {
        huddleID.postValue(huddleId)
        isForSaleValid.postValue(isForSale)
        if (huddlePrice != "0.0") {
//            flaxAmount.postValue("")
            flaxAmount.postValue(huddlePrice)
        }
        if(isForSale) {
            isChecked.postValue(true)
        }
    }


    fun sellHuddle(isForSale: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            flaxAmount.value?.toDouble().let {
                when (val result: ResultOf<APIResponse<SellHuddleResponse>> = huddleRepo.sellHuddle(huddleID.value!!, it!!, isForSale)) {
                    is ResultOf.Success -> {
                        showSuccess.postValue(result.value.message)
                    }

                    is ResultOf.APIError -> {
                        errorMessage.postValue(result.error.message)
                    }

                    is ResultOf.Error -> {
                        errorMessage.postValue(result.toString())
                    }
                }
            }

        }
    }

    fun cancelSaleOffer() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<APIResponse<SellHuddleResponse>> = huddleRepo.cancelSellHuddle(huddleID.value!!, flaxAmount.value?.toDouble() ?: 0.0)) {
                is ResultOf.Success -> {
                    showSuccess.postValue(result.value.message)
                }

                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                }

                is ResultOf.Error -> {

                }
            }
        }
    }
}