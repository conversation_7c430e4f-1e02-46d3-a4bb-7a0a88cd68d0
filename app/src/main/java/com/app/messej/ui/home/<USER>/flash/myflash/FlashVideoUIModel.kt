package com.app.messej.ui.home.publictab.flash.myflash

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import com.app.messej.data.model.entity.FlashVideo

data class FlashVideoUIModel(val flashVideo: FlashVideo): BaseObservable() {
    @get:Bindable
    var selected: Boolean = false
        set(value) {
            field = value
            notifyPropertyChanged(BR.selected)
        }
}