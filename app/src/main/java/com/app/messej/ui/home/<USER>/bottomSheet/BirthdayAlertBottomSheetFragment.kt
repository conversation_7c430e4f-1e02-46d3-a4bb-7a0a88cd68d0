package com.app.messej.ui.home.gift.bottomSheet

import android.os.Bundle
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BirthdayAlertBottomSheetFragment : BirthdayBaseBottomSheetFragment() {
    private val commonViewModel: CommonHomeViewModel by activityViewModels()
    private val args: BirthdayAlertBottomSheetFragmentArgs by navArgs()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }
    fun setUp() {
        binding.currentBirthday = args.currentBirthday
        binding.btnBirthdayList.setOnClickListener {
            findNavController().navigateSafe(BirthdayAlertBottomSheetFragmentDirections.actionBirthdayAlertBottomSheetFragmentToTodaysBirthdayFragment())
        }

        binding.birthdayUserDp.setOnClickListener {
            findNavController().navigateSafe(BirthdayAlertBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(viewModel.firstBirthday.value?.userId ?:return@setOnClickListener))
        }
    }

   private fun observe(){
        commonViewModel.birthdaysList.observe(viewLifecycleOwner){ list->
            list?.let {
                viewModel.setBirthdays(it)
            }
        }
    }

}