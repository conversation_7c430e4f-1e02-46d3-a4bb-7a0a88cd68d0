package com.app.messej.ui.home.publictab.flash.myflash

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.FlashReportedComment
import com.app.messej.databinding.ItemFlashReportedCommentBinding

class ReportedCommentsListAdapter(
    private val reportListener: ReportActionListener
) : PagingDataAdapter<FlashReportedComment, ReportedCommentsListAdapter.FlashReportedCommentViewHolder>(ReportsDiff) {

    interface ReportActionListener {
        fun onDeleteAction(item: FlashReportedComment, position: Int)
        fun onViewComment(item: FlashReportedComment, position: Int)
        fun onViewReporters(item: FlashReportedComment, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = FlashReportedCommentViewHolder(
        ItemFlashReportedCommentBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun onBindViewHolder(holder: FlashReportedCommentViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class FlashReportedCommentViewHolder(private val binding: ItemFlashReportedCommentBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: FlashReportedComment) = with(binding) {
            comment = item
            deleteMessageButton.setOnClickListener {
                reportListener.onDeleteAction(item,bindingAdapterPosition)
            }
            chatMessage.setOnClickListener {
                reportListener.onViewComment(item,bindingAdapterPosition)
            }
            reportedParticipantsButton.setOnClickListener {
                reportListener.onViewReporters(item,bindingAdapterPosition)
            }
        }
    }

    object ReportsDiff : DiffUtil.ItemCallback<FlashReportedComment>() {
        override fun areItemsTheSame(oldItem: FlashReportedComment, newItem: FlashReportedComment): Boolean {
            return oldItem.commentId == newItem.commentId
        }

        override fun areContentsTheSame(oldItem: FlashReportedComment, newItem: FlashReportedComment): Boolean {
            return oldItem == newItem
        }
    }
}