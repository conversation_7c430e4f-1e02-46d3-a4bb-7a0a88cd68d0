package com.app.messej.ui.home.publictab.authorities.legalAffairs.violations

import android.content.res.ColorStateList
import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.legal.LegalRecordsResponse.ReportCase
import com.app.messej.data.model.enums.ReportCaseStatus
import com.app.messej.databinding.ItemLegalAffairsViolationsBinding
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils.getStatusText
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils.getStatusTextColor

class LegalAffairsCaseListAdapter(private val listener: ItemClickListener) : PagingDataAdapter<ReportCase, LegalAffairsCaseListAdapter.ListViewHolder>(ViolationDiff) {

    enum class DisplayMode {
        CASE,
        USER,
        MIXED
    }
    interface ItemClickListener {
        fun getUserId(): Int
        fun onClick(case: ReportCase)
        fun showStatus(): Boolean = true
        @ColorRes fun cardBackgroundColor() :  Int? = null
        fun displayMode(): DisplayMode = DisplayMode.CASE
    }

    override fun onBindViewHolder(holder: ListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ListViewHolder {
        val binding = ItemLegalAffairsViolationsBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ListViewHolder(binding = binding)
    }

    inner class ListViewHolder(private val binding: ItemLegalAffairsViolationsBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(case: ReportCase) {
            binding.apply {
                item = case
                userMode = when(listener.displayMode()) {
                    DisplayMode.CASE -> false
                    DisplayMode.USER -> true
                    DisplayMode.MIXED -> case.userReport
                }
                showStatus = listener.showStatus()
                currentUserId = listener.getUserId()

                val showAppeal = case.caseStatus == ReportCaseStatus.APPEAL && listener.getUserId()==case.userId
                val showFine = case.caseStatus == ReportCaseStatus.VERDICT && case.hasFineFor(listener.getUserId())
                Log.w("LACLA", "bind: showAppeal $showAppeal showFine $showFine | file: ${!(showAppeal || showFine)}")
                btnFile.isVisible = !(showAppeal || showFine)
                btnAppeal.isVisible = showAppeal
                btnFine.isVisible = showFine
                violationStatus = case.getStatusText(root.resources,listener.getUserId())
                violationStatusTextColor = case.getStatusTextColor(root.context)
                listener.cardBackgroundColor()?.let {
                    cardViewViolationList.setCardBackgroundColor(ColorStateList.valueOf(ContextCompat.getColor(root.context, it)))
                }
                btnAppeal.setOnClickListener { listener.onClick(case = case) }
                btnFile.setOnClickListener { listener.onClick(case = case) }
                btnFine.setOnClickListener { listener.onClick(case = case) }
            }
        }
    }

    object ViolationDiff : DiffUtil.ItemCallback<ReportCase>() {
        override fun areItemsTheSame(oldItem: ReportCase, newItem: ReportCase): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: ReportCase, newItem: ReportCase): Boolean {
            return oldItem == newItem
        }
    }
}