package com.app.messej.ui.home.publictab.flash

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.appcompat.widget.PopupMenu
import androidx.core.os.bundleOf
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.FragmentNavigator
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.afollestad.materialdialogs.MaterialDialog
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.FlashTab
import com.app.messej.databinding.LayoutPublicFlashBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureFlashPostingAllowed
import com.app.messej.ui.home.publictab.flash.myflash.MyFlashFragment.Companion.showFlashPostLimitingAlert
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerFragment
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder

abstract class PublicFlashBaseFragment : Fragment(), MenuProvider {

    protected abstract var binding: LayoutPublicFlashBinding

    protected lateinit var mFlashPagerAdapter: FragmentStateAdapter

    protected val viewModel: PublicFlashViewModel by activityViewModels()

    private var apiLoader : MaterialDialog? = null

    companion object {
        private const val TRIGGER_PLAYER_REQUEST_KEY = "flash_trigger_player"

        fun parseResult(bundle: Bundle): Boolean {
            return bundle.getBoolean(TRIGGER_PLAYER_REQUEST_KEY)
        }

        fun triggerPlayer(fm: FragmentManager) {
            fm.setFragmentResult(TRIGGER_PLAYER_REQUEST_KEY, bundleOf(TRIGGER_PLAYER_REQUEST_KEY to true))
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    private fun setup() {

        mFlashPagerAdapter = object: FragmentStateAdapter(this) {
            override fun getItemCount(): Int = FlashTab.entries.size

            override fun createFragment(position: Int): Fragment {
                val tab = FlashTab.entries[position]
                return PublicFlashInnerListFragment().apply {
                    arguments = PublicFlashInnerListFragment.getTabBundle(tab)
                }
            }
        }
        binding.flashPager.apply {
            isUserInputEnabled = false
            adapter = mFlashPagerAdapter
        }

        binding.flashCreateLayout.addFlashButton.setOnClickListener {
            viewModel.getFlashEligibilityDetails()
        }

        binding.btnAll.setOnClickListener {
            viewModel.setCurrentTab(FlashTab.ALL)
        }

        binding.btnStars.setOnClickListener {
            viewModel.setCurrentTab(FlashTab.STARS)
        }

        binding.btnMe.setOnClickListener {
            viewModel.setCurrentTab(FlashTab.ME)
        }

        viewModel.currentTab.value?.let {
            binding.flashPager.setCurrentItem(it.ordinal, false)
        }

        setFragmentResultListenerOnActivity(TRIGGER_PLAYER_REQUEST_KEY) { _, bundle ->
            val result = parseResult(bundle)
            Log.d("PUBLICT", "setFragmentResultListenerOnActivity: going to player: $result")
            if(result) {
//                viewModel.setActiveItem(viewModel.activeItem?:0)
                toPlayer(0, BaseFlashPlayerFragment.getBlankTransitionExtras())
            }
        }
    }

    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            if (binding.flashPager.currentItem==it.ordinal) return@observe
            binding.flashPager.setCurrentItem(it.ordinal,false)
        }

        viewModel.flashEligibility.observe(viewLifecycleOwner) {
            Log.d("PUBLICT", "FlashEligibility -> $it")
        }

        viewModel.actionLoading.observe(viewLifecycleOwner) {
            if (it) showAPILoader() else hideAPILoader()
        }

        viewModel.isEligibilityLoaded.observe(viewLifecycleOwner) {
            if (viewModel.user.isFlashBlocked == true) {
                MaterialAlertDialogBuilder(requireContext())
                    .setMessage(R.string.flash_blocked_dialog)
                    .setPositiveButton(R.string.common_close) { dialog, which ->
                        dialog.dismiss()
                    }
                    .show()
                return@observe
            }

            ensureFlashPostingAllowed {
                if (viewModel.flashEligibility.value?.flashEligibility == false) {
                    showFlashPostLimitingAlert (
                        citizenship = viewModel.user.citizenship,
                        enabledFlashCount = viewModel.flashEligibility.value?.enabledFlashCount,
                        onConfirmButtonClick = {
                            findNavController().navigateSafe(
                                NavGraphHomeDirections.actionGlobalUpgradePremiumFragment()
                            )
                        }
                    )
                    return@ensureFlashPostingAllowed
                }
                viewModel.flashEligibility.value?.enabledFlashDuration?.let { duration ->
                    findNavController().navigateSafe(
                        NavGraphHomeDirections.actionGlobalFlashRecordFragment(
                            drafId = null, flashDuration = duration
                        )
                    )
                }
            }
        }
    }

    private fun showAPILoader() {
        apiLoader = showLoader()
        apiLoader?.show()
    }

    private fun hideAPILoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_home_flash,menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> toSearch()
            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    protected fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_flash_action_more, popup.menu)
        popup.setForceShowIcon(true)

        popup.menu.apply {
            findItem(R.id.action_filter).isVisible = false
        }

        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_my_flash-> toMyFlash()
                R.id.action_filter-> {}
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    abstract fun toPlayer(pos: Int, extras: FragmentNavigator.Extras)
    abstract fun toSearch()
    abstract fun toMyFlash()
}