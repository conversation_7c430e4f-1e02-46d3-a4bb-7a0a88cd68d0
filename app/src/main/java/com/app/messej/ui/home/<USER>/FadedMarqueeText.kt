package com.app.messej.ui.home.promobar

import android.util.Log
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.key
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.CompositingStrategy
import androidx.compose.ui.graphics.drawscope.ContentDrawScope
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp

@Composable
fun FadedMarqueeText(modifier: Modifier = Modifier, text: AnnotatedString, style: TextStyle, spacerWidthDp: Dp, forceDirection: LayoutDirection? = null, onDelayCalculated: ((Long) -> Unit)? = null) {
    fun ContentDrawScope.drawFadedEdge(leftEdge: Boolean) {
        val edgeWidthPx = 24.dp.toPx()
        drawRect(
            topLeft = Offset(if (leftEdge) 0f else size.width - edgeWidthPx, 0f),
            size = Size(edgeWidthPx, size.height),
            brush = Brush.horizontalGradient(
                colors = listOf(Color.Transparent, Color.Black), startX = if (leftEdge) 0f else size.width, endX = if (leftEdge) edgeWidthPx else size.width - edgeWidthPx
            ), blendMode = BlendMode.DstIn
        )
    }
    val direction = forceDirection?:LocalLayoutDirection.current
    val textMeasurer = rememberTextMeasurer()
    val density = LocalDensity.current

    val textWidthDp = remember {
        with(density) {
            textMeasurer.measure(text, style).size.width.toDp()
        }
    }
    Log.d("TEST","textWidth : $textWidthDp")

    val textVelocity = 70.dp

    key(text) {
        CompositionLocalProvider(LocalLayoutDirection provides direction) {
            Row(modifier
                    .graphicsLayer { compositingStrategy = CompositingStrategy.Offscreen }
                    .drawWithContent {
                        drawContent()
                        drawFadedEdge(leftEdge = true)
                        drawFadedEdge(leftEdge = false)
                    }
                    .customBasicMarquee(iterations = Int.MAX_VALUE, velocity = textVelocity, repeatDelayMillis = 200, initialDelayMillis = 200)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically) {
                Spacer(modifier = Modifier.width(spacerWidthDp))
                Text(
                    text = text, style = style, maxLines = 1
                )
                Spacer(modifier = Modifier.width(spacerWidthDp))

            }

            val iterationDurationMs = with(density) {
                (((textWidthDp + spacerWidthDp).toPx() / textVelocity.toPx()) * 1000).toLong()
            }

            LaunchedEffect(iterationDurationMs) {
                onDelayCalculated?.invoke(iterationDurationMs + 400)
//            delay(iterationDurationMs)
            }
        }
    }
}