package com.app.messej.ui.enforcements

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.repository.SocialAffairsRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BlackListViewModel(application: Application) : AndroidViewModel(application = application) {

    private val socialRepository = SocialAffairsRepository(context = application)

    private val _isSubmitting = MutableLiveData(false)
    val isSubmitting: LiveData<Boolean> = _isSubmitting

    val isSocialFinePaid = LiveEvent<Boolean>()
    val onError = LiveEvent<String>()
    fun paySocialFine() {
        viewModelScope.launch(context = Dispatchers.IO) {
            _isSubmitting.postValue(true)
            val response = socialRepository.payFine()
            when(response) {
                is ResultOf.Success -> {
                    isSocialFinePaid.postValue(true)
                }
                is ResultOf.APIError -> {
                    onError.postValue(response.error.message)
                }
                is ResultOf.Error -> {
                    onError.postValue(response.errorMessage())
                }
            }
            _isSubmitting.postValue(false)
        }
    }
}