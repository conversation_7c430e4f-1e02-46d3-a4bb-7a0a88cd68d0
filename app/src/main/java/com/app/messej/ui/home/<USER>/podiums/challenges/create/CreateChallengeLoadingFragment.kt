package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumFlagChallengePresenter
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class CreateChallengeLoadingFragment : Fragment() {

    private val args : CreateChallengeLoadingFragmentArgs by navArgs()

    private val viewModel: PodiumCreateChallengeViewModel by navGraphViewModels(R.id.nav_challenge_setup)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_create_challenge_loading, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun observer() {
        viewModel.activeChallenge.observe(viewLifecycleOwner) {

            if(it?.challengeType==ChallengeType.FLAGS){
                viewModel.setChallengeTimer(PodiumFlagChallengePresenter.getChallengeDurationSeconds())
            }

            it?.let {
                val options = NavOptions.Builder().setPopUpTo(R.id.createChallengeLoadingFragment, inclusive = true).build()
                when (it.screen) {
                    PodiumChallenge.ChallengeScreen.SELECT_FACILITATOR -> {
                        findNavController().navigateSafe(
                            CreateChallengeLoadingFragmentDirections.actionCreateChallengeLoadingFragmentToChallengeFacilitatorListFragment(), options
                        )
                    }

                    PodiumChallenge.ChallengeScreen.SET_TIMER -> {
                        if (it.challengeType == ChallengeType.FLAGS) {
                            findNavController().navigateSafe(
                                CreateChallengeLoadingFragmentDirections.actionCreateChallengeLoadingFragmentToPodiumChallengePrizeContributorFragment(), options
                            )
                        } else {
                            findNavController().navigateSafe(
                                CreateChallengeLoadingFragmentDirections.actionCreateChallengeLoadingFragmentToChallengeTimerFragment(), options
                            )
                        }
                    }

                    PodiumChallenge.ChallengeScreen.SELECT_CONTRIBUTION -> {
                        findNavController().navigateSafe(
                            CreateChallengeLoadingFragmentDirections.actionCreateChallengeLoadingFragmentToPodiumChallengePrizeContributorFragment(), options
                        )
                    }

                    PodiumChallenge.ChallengeScreen.INVITE_PARTICIPANTS -> {
                        findNavController().navigateSafe(
                            CreateChallengeLoadingFragmentDirections.actionCreateChallengeLoadingFragmentToPodiumConFourParticipants(), options
                        )
                    }
                    null -> {}
                }
            }
        }
    }

    private fun setup() {
        viewModel.setChallenge(args.podiumId, args.challengeId, args.invitedParticipants)
        if (args.challengeId == null) {
            val options = NavOptions.Builder()
                .setPopUpTo(R.id.createChallengeLoadingFragment, inclusive = true)
                .build()
            findNavController().navigateSafe(
                CreateChallengeLoadingFragmentDirections
                    .actionCreateChallengeLoadingFragmentToPodiumChallengeListFragment(), options
            )
        }
    }

}