package com.app.messej.ui.home.businesstab.customers

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.databinding.FragmentBusinessCustomersFansBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.businesstab.BusinessCustomersViewModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.kennyc.view.MultiStateView

class BusinessCustomersFansFragment : Fragment() {

    private lateinit var binding: FragmentBusinessCustomersFansBinding
    private val viewModel: BusinessCustomersViewModel by activityViewModels()

    private var mAdapter: BusinessCustomerListAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_customers_fans, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setEmptyView()
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            image = R.drawable.im_eds_fans_list,
            message = R.string.broadcast_list_fans_empty_text,
            action = null
        )
    }

    private fun observe() {
        viewModel.customersFansList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }
    }

    private fun initAdapter() {
        mAdapter = BusinessCustomerListAdapter(object: BusinessCustomerListAdapter.ActionListener {
            override fun onProfileClick(item: UserRelative) {
                viewModel.navigateToProfile(item)
            }

            override fun onPrivateMessageClick(item: UserRelative) {
                viewModel.navigateToPrivateMessage(item)
            }

        })

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 &&  loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT

            }
        }

        val layoutMan = LinearLayoutManager(context)
        binding.fansList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

    }

}
