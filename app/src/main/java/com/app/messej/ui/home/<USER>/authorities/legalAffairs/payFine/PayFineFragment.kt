package com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine

import android.content.DialogInterface
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentPayFineBinding
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils
import com.app.messej.ui.utils.FragmentExtensions.showLoader
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class PayFineFragment : Fragment() {

    private val viewModel: PayFineViewModel by viewModels()
    private lateinit var binding: FragmentPayFineBinding
    private lateinit var adapter: PayFineAdapter
    private var apiLoader : MaterialDialog? = null
    private val args: PayFineFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_pay_fine, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    companion object {
        const val PAY_FINE_REQUEST_KEY = "pay_fine_request"
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.common_pay_fine)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    fun setup() {
        setupParams()
        setupClickListeners()
        initAdapter()
        observe()
    }

    private fun showAPILoader() {
        apiLoader = showLoader()
        apiLoader?.show()
    }

    private fun hideAPILoader() {
        apiLoader?.dismiss()
        apiLoader = null
    }

    private fun setupParams() {
        viewModel.setParams(
            reportId = args.reportId,
            reportCategory = args.fineCategory
        )
    }

    private fun setupClickListeners() {
        binding.apply {
            btnPayFine.setOnClickListener {
                val haveSufficientCoin = (viewModel?.accountDetails?.value?.currentCoin ?: 0.0) >= (viewModel?.totalFineAmount?.value ?: 0.0)
                showAlertDialog(
                    message = getString(R.string.defend_case_pay_fine_alert_description, "${viewModel?.totalFineAmount?.value}"),
                    onPositiveButtonClick = {
                        if (!haveSufficientCoin) {
                            showAlertDialog(
                                message = getString(R.string.defend_case_pay_fine_insufficient_alert),
                                negativeButtonText = R.string.common_close,
                                positiveButtonText = R.string.title_buy_coins,
                                onPositiveButtonClick = {
                                    findNavController().navigateSafe(
                                        PayFineFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = true)
                                    )
                                }
                            )
                            return@showAlertDialog
                        }
                        viewModel?.payTotalFines()
                    }
                )
            }
            btnCancel.setOnClickListener {
                findNavController().navigateUp()
            }
        }
    }

    private fun initAdapter() {
        adapter = PayFineAdapter()
        binding.recyclerView.adapter = this.adapter

        adapter.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
            }
        }

        AuthoritiesUtils.setupListEmptyView(
            multiStateView = binding.multiStateView,
            message = R.string.legal_affairs_no_pending_fines
        )
    }

    private fun observe() {
        viewModel.pendingFineList.observe(viewLifecycleOwner) {
            adapter.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.accountDetails.observe(viewLifecycleOwner) {
            Log.d("PFF", "account details -> $it")
        }

        viewModel.onActionDone.observe(viewLifecycleOwner) {
            requireActivity().supportFragmentManager.setFragmentResult(PAY_FINE_REQUEST_KEY, bundleOf())
            findNavController().navigateUp()
        }

        viewModel.onActionError.observe(viewLifecycleOwner) { errorMessage ->
            showToast(message = errorMessage)
        }

        viewModel.actionLoading.observe(viewLifecycleOwner) {
            if (it) showAPILoader() else hideAPILoader()
        }
    }

    private fun showAlertDialog(
        message: String,
        onPositiveButtonClick: () -> Unit,
        @StringRes positiveButtonText: Int = R.string.common_confirm,
        @StringRes negativeButtonText: Int = R.string.common_cancel,
    ) {
        val alertDialog = MaterialAlertDialogBuilder(requireContext())
            .setMessage(message)
            .setPositiveButton(getString(positiveButtonText)) { _, _ ->
                onPositiveButtonClick()
            }
            .setNegativeButton(getString(negativeButtonText)) { dialog, _ -> dialog.dismiss() }
            .create()
        alertDialog.show()

        val negativeButton = alertDialog.getButton(DialogInterface.BUTTON_NEGATIVE)
        negativeButton.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorError))
    }
}