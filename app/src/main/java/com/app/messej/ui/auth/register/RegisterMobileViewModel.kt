package com.app.messej.ui.auth.register

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class RegisterMobileViewModel(application: Application) : AndroidViewModel(application) {

    private var authRepo: AuthenticationRepository = AuthenticationRepository(application)
    private val accountRepo: AccountRepository = AccountRepository(getApplication())

    private val _emailValid = MutableLiveData(false)

    val loggedInUser: CurrentUser?
        get() {
            return if (accountRepo.loggedIn) accountRepo.user else null
        }

//    MOBILE NUMBER STAGE

    val countryCode = MutableLiveData<String?>()
    val countryCodeWithPlus: LiveData<String?> = countryCode.map {
        return@map UserInfoUtil.addPlusToCountryCode(it.orEmpty())
    }

    val phoneNumber = MutableLiveData<String>()
    val didEnterPhoneNumber = MutableLiveData<Boolean>(false)

    val tncAccepted = MutableLiveData<Boolean>(false)

    private val _didShowEmailError= MutableLiveData(false)
    private val didShowEmailError: LiveData<Boolean?> = _didShowEmailError

    val email =MutableLiveData<String>()


    private val phoneNumberWithCountryCode: MediatorLiveData<String?> by lazy {
        val med = MediatorLiveData<String?>(null)
        med.addSource(countryCode) { combineMobileNumber() }
        med.addSource(phoneNumber) { combineMobileNumber() }
        med
    }

    private fun combineMobileNumber() {
        phoneNumberWithCountryCode.postValue(UserInfoUtil.combineCountryCodeAndMobileNumber(countryCode.value.orEmpty(), phoneNumber.value.orEmpty()))
    }

    // will be false even if input is empty
    private val _phoneNumberValid = MutableLiveData(false)

    fun setPhoneNumberValid(valid: Boolean) {
        _phoneNumberValid.postValue(valid)
    }

    fun checkEmailValidity() {
        clearEmailAvailabilityError()
        if (UserInfoUtil.isEmailValid(email.value.toString()) && email.value.toString().isNotEmpty()) {
            _emailValid.postValue(true)
        } else {
            _emailValid.postValue(false)
        }
    }

    var didTryPhoneNumberHint: Boolean = false

    // show error if phone number is invalid after entering 3 characters
    private val _showPhoneInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(_phoneNumberValid) { shouldShowPhoneNumberError() }
        med.addSource(didEnterPhoneNumber) { shouldShowPhoneNumberError() }
        med
    }

    private val _showEmailInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(_didShowEmailError) { shouldShowEmailError() }
        med.addSource(_emailValid) { shouldShowEmailError() }
        med
    }

    val showPhoneInvalidError: LiveData<Boolean> = _showPhoneInvalidError
    val showEmailInvalidError: LiveData<Boolean> = _showEmailInvalidError


    private fun shouldShowEmailError() {
        if (didShowEmailError.value == false|| email.value.isNullOrEmpty()) {
            _showEmailInvalidError.postValue(false)
            return
        }
        _showEmailInvalidError.postValue(_emailValid.value == false)
    }
    private fun shouldShowPhoneNumberError() {
        if (didEnterPhoneNumber.value==false || phoneNumber.value.isNullOrEmpty()) {
            _showPhoneInvalidError.postValue(false)
            return
        }
        _showPhoneInvalidError.postValue(_phoneNumberValid.value==false)
    }

    private val _mobileNumberStageValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(_phoneNumberValid) { validateMobileNumberStage() }
        med.addSource(tncAccepted) { validateMobileNumberStage() }
        med
    }

    private val _emailStageValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(_emailValid) { validateEmailStage() }
        med.addSource(tncAccepted) { validateEmailStage() }
        med
    }
    val mobileNumberStageValid: LiveData<Boolean> = _mobileNumberStageValid
    val emailStageValid: LiveData<Boolean> = _emailStageValid

    private fun validateMobileNumberStage() {
        _mobileNumberStageValid.postValue((tncAccepted.value == true) && _phoneNumberValid.value == true)
    }
    private fun validateEmailStage() {
        _emailStageValid.postValue((tncAccepted.value == true) && _emailValid.value == true)
    }
    private val _mobileAvailabilityLoading = MutableLiveData(false)
    val mobileAvailabilityLoading: LiveData<Boolean> = _mobileAvailabilityLoading

    private val _mobileAvailabilityError = MutableLiveData<String?>(null)
    val mobileAvailabilityError: LiveData<String?> = _mobileAvailabilityError

    private val _emailAvailabilityError = MutableLiveData<String?>(null)
    val emailAvailabilityError: LiveData<String?> = _emailAvailabilityError

    fun clearAvailabilityError() {
        _mobileAvailabilityError.postValue(null)
    }

     private fun clearEmailAvailabilityError() {
        _emailAvailabilityError.postValue(null)
    }
    fun didShowEmailError(isShow: Boolean) {
        _didShowEmailError.postValue(isShow)
    }

    val onCheckMobileAvailabilityComplete = LiveEvent<Boolean>()

    val onCheckEmailComplete = LiveEvent<Boolean>()

    fun checkMobileNumberAvailability() {
        _mobileAvailabilityLoading.postValue(true)
        _mobileAvailabilityError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            val result: ResultOf<String>?
            result = authRepo.checkPhoneAvailability(phoneNumber.value!!, countryCode.value!!)
            when (result) {
                is ResultOf.Success -> {
                    onCheckMobileAvailabilityComplete.postValue(true)
                    _mobileAvailabilityLoading.postValue(false)
                }
                is ResultOf.APIError -> {
                    _mobileAvailabilityError.postValue(result.error.message)
                    _mobileAvailabilityLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                    _mobileAvailabilityLoading.postValue(false)
                }
            }
        }
    }

    fun checkEmailAvailability() {
        _mobileAvailabilityLoading.postValue(true)
        _mobileAvailabilityError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<String>? = authRepo.checkEmailAvailability(email.value)) {
                is ResultOf.Success -> {
                     onCheckEmailComplete.postValue(true)
                    _mobileAvailabilityLoading.postValue(false)
                }
                is ResultOf.APIError -> {
                    _emailAvailabilityError.postValue(result.error.message)
                    _mobileAvailabilityLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                    _mobileAvailabilityLoading.postValue(false)
                }
                else->{

                }
            }
        }
    }
}