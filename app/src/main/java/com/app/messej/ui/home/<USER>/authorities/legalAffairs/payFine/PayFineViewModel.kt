package com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.legal.LegalAffairsPayRequest
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.LegalAffairsPaymentType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalAffairsRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PayFineViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepository = AccountRepository(application)
    private val legalAffairsRepository = LegalAffairsRepository(application)
    val profRepo = ProfileRepository(application)
    private val _accountDetails = accountRepository.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    private val _actionLoading = MutableLiveData(false)
    val actionLoading: LiveData<Boolean> = _actionLoading

    data class FineParams(
        val reportId: Int? = null,
        val reportCategory: String? = null
    )
    val fineParams = MutableLiveData<FineParams>()

    fun setParams(reportId: Int? = null, reportCategory: String? = null) {
        fineParams.postValue(
            FineParams(
                reportId = reportId,
                reportCategory = reportCategory
            )
        )
    }

    private val _totalFineAmount = MutableLiveData<Double?>()
    val totalFineAmount : LiveData<Double?> = _totalFineAmount

    val onActionDone = LiveEvent<Boolean>()
    val onActionError = LiveEvent<String>()

    val pendingFineList = fineParams.switchMap {
        legalAffairsRepository
            .getPendingFines(
                params = it.takeIf { it.reportId != 0 && it.reportId != null  },
                totalFineAmountCallBack = { fine ->
                    setTotalFineAmount(fine = fine)
                }
            )
            .liveData
            .cachedIn(viewModelScope)
    }

    private fun setTotalFineAmount(fine: Double) {
        _totalFineAmount.postValue(fine)
    }

    private fun setLoading(isLoading: Boolean) {
        _actionLoading.postValue(isLoading)
    }

    fun payTotalFines() {
        viewModelScope.launch(Dispatchers.IO) {
            setLoading(isLoading = true)
            val request = LegalAffairsPayRequest(paymentType = LegalAffairsPaymentType.REPORT_FINE, reportId = fineParams.value?.reportId?.takeIf { it != 0 }, enforcement = fineParams.value?.reportCategory)
            when(val result = legalAffairsRepository.legalAffairsPay(req = request)) {
                is ResultOf.Success -> {
                    profRepo.getAccountDetails()
                    onActionDone.postValue(true)
                }
                is ResultOf.APIError -> {
                    onActionError.postValue(result.errorMessage())
                }
                is ResultOf.Error -> {
                    onActionError.postValue(result.errorMessage())
                }
            }
            setLoading(isLoading = false)
        }
    }

}