package com.app.messej.ui.home.businesstab

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Paint
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.DealsBeneficiary
import com.app.messej.data.model.api.DealsPurpose
import com.app.messej.databinding.FragmentSendFlaxBinding
import com.app.messej.databinding.LayoutConfirmSaveBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.businesstab.adapter.DealsBeneficiaryAdapter
import com.app.messej.ui.home.businesstab.adapter.DealsPurposeDropdownAdapter
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.showAlertWithSingleButton
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import java.util.Locale

class SendFlaxFragment : Fragment(), MenuProvider {

    private var dialogue: MaterialDialog?=null
    private lateinit var binding: FragmentSendFlaxBinding
    private val viewModel: SentFlaxViewModel by viewModels()
    private val args: SendFlaxFragmentArgs by navArgs()
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_send_flax, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.title_send_flax)
    }

    override fun onResume() {
        super.onResume()
        if(viewModel.otherUser.value==null){
            clearSentFlax()
        }
        viewModel.purpose.postValue(null)
    }
    
    private fun setUp() {
        viewModel.setDataFromPodium(args.receiverId)
        viewModel.getDealsPurposes()
        binding.textSentFlaxHistory.paintFlags =binding.textSentFlaxHistory.paintFlags or Paint.UNDERLINE_TEXT_FLAG
        binding.textSearchBeneficiary.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    viewModel.validateBeneficiary(false)
                } else {
                    hideKeyboard()
                }
            }
        }
        binding.textEnterAmount.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    viewModel.validateFlaxAmount(false)
                } else {
                    hideKeyboard()
                }
            }
        }
        binding.textSelectPurpose.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    viewModel.validatePurpose(false)
                } else {
                    hideKeyboard()
                }
            }
        }

        binding.actionSentFlaxSave.setOnClickListener {
            ensureInteractionAllowed {
                showBackPressAlert()
            }
        }

        binding.textSentFlaxHistory.setOnClickListener {
            val action = HomeBusinessFragmentDirections.actionGlobalSentFlaxHistory()
            findNavController().navigateSafe(action)
        }

        binding.actionCancel.setOnClickListener {
            clearSentFlax()
            findNavController().popBackStack()
        }
    }

    @SuppressLint("DefaultLocale")
    fun observe() {

        val textInputEditText = binding.textSearchBeneficiary.editText as AutoCompleteTextView
        textInputEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val searchText = s.toString()
                if (searchText.isNotEmpty()) {
                    viewModel.getBeneficiaryList(keyword = searchText)
                }
                textInputEditText.setSelection(searchText.length)
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })

        viewModel.dealsBeneficiaryList.observe(viewLifecycleOwner) {
            if (it != null) {
                val adapter = DealsBeneficiaryAdapter(requireContext(), it)
                (binding.textSearchBeneficiary.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    adapter.notifyDataSetChanged()
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        onBeneficiarySelected(item)
                    }
                }
            }
        }


        viewModel.dealsTransferPurpose.observe(viewLifecycleOwner) { purposeList ->

            if (purposeList != null) {
                val adapter = DealsPurposeDropdownAdapter(requireContext(), purposeList)
                (binding.textSelectPurpose.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    adapter.notifyDataSetChanged()
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        onPurposeSelected(item)
                    }
                }
            }
        }

        viewModel.beneficiaryError.observe(viewLifecycleOwner) {
            binding.textSearchBeneficiary.error = when (it) {
                true -> {
                    binding.textSearchBeneficiary.isErrorEnabled = true
                    resources.getString(R.string.please_select_a_beneficiary_name_to_proceed)
                }

                false -> {
                    binding.textSearchBeneficiary.isErrorEnabled = false
                    null
                }

                null -> {
                    binding.textSearchBeneficiary.isErrorEnabled = false
                    null
                }
            }
        }


        viewModel.flaxAmountError.observe(viewLifecycleOwner) {
            binding.textEnterAmount.error = when (it) {
                SentFlaxViewModel.Companion.FlaxError.EMPTY -> {
                    binding.textEnterAmount.isErrorEnabled = true
                    resources.getString(R.string.enter_a_valid_amount)
                }

                SentFlaxViewModel.Companion.FlaxError.NONE -> {
                    binding.textEnterAmount.isErrorEnabled = false
                    null
                }

                SentFlaxViewModel.Companion.FlaxError.LIMIT_EXCEEDS -> {
                    binding.textEnterAmount.isErrorEnabled = true
                    resources.getString(R.string.oops_it_seems_that_the_amount_you_ve_entered_n_exceeds_your_available_flax_balance)
                }

                SentFlaxViewModel.Companion.FlaxError.NO_FLAX_BALANCE -> {
                    binding.textEnterAmount.isErrorEnabled = true
                    getString(R.string.you_don_t_have_sufficient_flax_balance_to_complete_the_transfer)
                }
                SentFlaxViewModel.Companion.FlaxError.MIN_FLAX -> {
                    binding.textEnterAmount.isErrorEnabled = true
                    getString(R.string.send_flax_min_flax_required)
                }
                SentFlaxViewModel.Companion.FlaxError.MAX_FLAX -> {
                    binding.textEnterAmount.isErrorEnabled = true
                    getString(R.string.send_flax_max_flax_required)
                }

                null -> {
                    binding.textEnterAmount.isErrorEnabled = false
                    null
                }
            }
        }


        viewModel.purposeError.observe(viewLifecycleOwner) {
            binding.textSelectPurpose.error = when (it) {
                true -> {
                    binding.textSelectPurpose.isErrorEnabled = true
                    resources.getString(R.string.please_choose_a_purpose_before_continuing)
                }

                false -> {
                    binding.textSelectPurpose.isErrorEnabled = false
                    null
                }

                null -> {
                    binding.textSelectPurpose.isErrorEnabled = false
                    null
                }
            }
        }

     viewModel.sendFlax.observe(viewLifecycleOwner){
         it?.let { response ->
             if (viewModel.beneficiary.value != null && viewModel.flaxAmount.value != null && viewModel.purpose.value != null) {
                 dialogue = MaterialDialog(requireContext()).show {
                     val view = DataBindingUtil.inflate<LayoutConfirmSaveBinding>(layoutInflater, R.layout.layout_confirm_save, null, false)
                     view.viewModel = viewModel
                     customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                     cancelable(false)
                     val textInput = binding.textEnterAmount.editText?.text?.toString()
                     val inputValue = textInput?.toDoubleOrNull() ?: 0.0
                     val percentAmount = calculateFlaxValue(inputValue, it.sendingFees ?: 0.0, it.userRating ?: 0.0)

                     view.nickNameTitle.text = getString(
                         R.string.your_flax_rate_is_sending_flax,
                         binding.textEnterAmount.editText?.text.toString(),
                         response.receiverName.toString())

                     view.nickNameSubTitle.text = getString(R.string.your_flax_rate_is_sending_flax_sub_title,
                                                            String.format(Locale.US,"%.2f", response.userRating) + "%",
                                                            String.format(Locale.US,"%.2f", response.sendingFees) + "%",
                                                            String.format(Locale.US,"%.2f", response.receivingFlax))

                     //                            resources.getString(R.string.are_u_sure_sent_flax, binding.textEnterAmount.editText?.text.toString(), binding.textSearchBeneficiary.editText?.text.toString())
                     view.actionConfirm.setOnClickListener {
                             viewModel.sentFlax()
                             dismiss()
                     }
                     view.actionCancel.setOnClickListener {
                         if (args.receiverId != -1) {
                             clearSentFlaxFromPodium()
                             dismiss()
                         } else {
                             clearSentFlax()
                             dismiss()
                         }
                     }
                 }

             }
         }
     }

        viewModel.flaxRate.observe(viewLifecycleOwner){
                viewModel.getSendFlaxDetails()
        }

        viewModel.showSuccess.observe(viewLifecycleOwner){
            if (it.isNotEmpty()){
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
                clearSentFlax()
                if(args.receiverId!=-1){
                    findNavController().popBackStack()
                }
            }
        }
        viewModel.errorMessage.observe(viewLifecycleOwner){
            if (it.isNotEmpty()){
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.noFlaxBalanceError.observe(viewLifecycleOwner){
            if (it != null){
                Toast.makeText(requireContext(), getString(R.string.you_don_t_have_sufficient_flax_balance_to_complete_the_transfer), Toast.LENGTH_SHORT).show()
            }
        }
        viewModel.otherUser.observe(viewLifecycleOwner) {
            val user = binding.textSearchBeneficiary
            user.isEnabled=false
            val editText=user.editText as AutoCompleteTextView
            editText.setText(it, false)
        }

        viewModel.isReceiverBanned.observe(viewLifecycleOwner) {
            if (it) {
                showAlertWithSingleButton(
                    message = R.string.banned_user_flix_receive_alert_message,
                    onClick = { }
                )
            }
        }

    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
    }


    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            android.R.id.home -> {
                findNavController().popBackStack()
            }
        }
        return true
    }

    private fun showBackPressAlert() {
        viewModel.validateSendFlax()

    }


    private fun onPurposeSelected(item: DealsPurpose) {
        binding.textSelectPurpose.editText?.setText(item.purpose)
        viewModel.purpose.postValue(item.id)
    }

    private fun onBeneficiarySelected(item: DealsBeneficiary) {
        binding.textSearchBeneficiary.editText?.setText(item.name)
        viewModel.beneficiary.postValue(item.id)
    }

    private fun hideKeyboard() {
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view?.windowToken, 0)
    }

    private fun calculateFlaxValue(input: Double, rate: Double, taxRate: Double): Double {
        return (input - (input * rate)) * (taxRate.div(100))
    }

    private fun clearSentFlax() {
        binding.textSearchBeneficiary.editText?.setText("")
        viewModel.beneficiary.postValue(null)
        clearSentFlaxFromPodium()
        dialogue?.dismiss()
    }

    private fun clearSentFlaxFromPodium() {
        binding.textEnterAmount.editText?.setText("")
        viewModel.flaxAmount.postValue(null)
        binding.textSelectPurpose.editText?.setText("")
        viewModel.purpose.postValue(null)
    }

}


