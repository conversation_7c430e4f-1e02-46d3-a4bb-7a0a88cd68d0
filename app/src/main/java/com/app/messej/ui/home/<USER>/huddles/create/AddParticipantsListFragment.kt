package com.app.messej.ui.home.publictab.huddles.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.huddles.AddParticipantsResponse
import com.app.messej.databinding.FragmentAddParticipantsListBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.checkbox.MaterialCheckBox
import com.google.android.material.checkbox.MaterialCheckBox.OnCheckedStateChangedListener
import com.kennyc.view.MultiStateView


class AddParticipantsListFragment : Fragment() {

    private lateinit var binding: FragmentAddParticipantsListBinding
    private val viewModel: AddParticipantsViewModel by viewModels(ownerProducer = { requireParentFragment() })

    private var mAdapter: AddParticipantsListAdapter? = null

    private lateinit var tab: AddParticipantsViewModel.ParticipantsTab

    companion object {
        const val ARG_TAB = "participantTab"

        fun getTabBundle(tab: AddParticipantsViewModel.ParticipantsTab) = Bundle().apply {
            putInt(ARG_TAB, tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): AddParticipantsViewModel.ParticipantsTab {
            val tabInt = bundle?.getInt(ARG_TAB) ?: 0
            return AddParticipantsViewModel.ParticipantsTab.values()[tabInt]
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        tab = parseTabBundle(arguments)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_add_participants_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private val checkBoxListener = OnCheckedStateChangedListener { checkBox, state ->
        if (state == MaterialCheckBox.STATE_CHECKED) {
            val items = mAdapter?.snapshot()?.items?.map { it.members } ?: return@OnCheckedStateChangedListener
            viewModel.selectMembers(items)
        } else {
            val items = mAdapter?.snapshot()?.items?.map { it.members } ?: return@OnCheckedStateChangedListener
            viewModel.unSelectMembers(items)
        }
    }

    private fun setup() {
        initAdapter()
        setEmptyView()
        binding.checkboxSelectAll.isEnabled = false
        binding.checkboxSelectAll.addOnCheckedStateChangedListener(checkBoxListener)

        binding.apply {
            keyword = when (tab) {
                AddParticipantsViewModel.ParticipantsTab.DEARS -> viewModel!!.searchKeywordDears
                AddParticipantsViewModel.ParticipantsTab.FANS -> viewModel!!.searchKeywordFans
                AddParticipantsViewModel.ParticipantsTab.LIKERS -> viewModel!!.searchKeywordLikers
            }
        }

        when (tab) {
            AddParticipantsViewModel.ParticipantsTab.DEARS -> binding.search.editText?.hint = getString(R.string.add_participants_search_dears)
            AddParticipantsViewModel.ParticipantsTab.FANS -> binding.search.editText?.hint = getString(R.string.add_participants_search_fans)
            AddParticipantsViewModel.ParticipantsTab.LIKERS -> binding.search.editText?.hint = getString(R.string.add_participants_search_likers)
        }

    }

    private fun setEmptyView() {
        val emptyView = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        when (tab) {
            AddParticipantsViewModel.ParticipantsTab.DEARS -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_dears_list,
                    message = R.string.create_huddle_dears_empty_text,
                )
            }

            AddParticipantsViewModel.ParticipantsTab.FANS -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_fans_list,
                    message = R.string.create_huddle_fans_empty_text,
                )
            }

            AddParticipantsViewModel.ParticipantsTab.LIKERS -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_likers_list,
                    message = R.string.broadcast_list_likers_empty_text,
                )
            }
        }
    }

    private fun observe() {
        when (tab) {
            AddParticipantsViewModel.ParticipantsTab.DEARS -> {
                viewModel.participantsDearsList.observe(viewLifecycleOwner) { pagingData ->
                    if (pagingData != null) {
                        binding.checkboxSelectAll.isEnabled = true
                        mAdapter?.let {
                            it.submitData(viewLifecycleOwner.lifecycle, pagingData)
                            if (it.itemCount > 0) binding.checkboxSelectAll.isChecked = it.itemCount == viewModel.selectedItemCount(tab)
                        }
                    }
                }
            }

            AddParticipantsViewModel.ParticipantsTab.FANS -> {
                viewModel.participantsFansList.observe(viewLifecycleOwner) { pagingData ->
                    if (pagingData != null) {
                        binding.checkboxSelectAll.isEnabled = true
                        mAdapter?.let {
                            it.submitData(viewLifecycleOwner.lifecycle, pagingData)
                            if (it.itemCount > 0) binding.checkboxSelectAll.isChecked = it.itemCount == viewModel.selectedItemCount(tab)
                        }
                    }
                }
            }

            AddParticipantsViewModel.ParticipantsTab.LIKERS -> {
                viewModel.participantsLikersList.observe(viewLifecycleOwner) { pagingData ->
                    if (pagingData != null) {
                        binding.checkboxSelectAll.isEnabled = true
                        mAdapter?.let {
                            it.submitData(viewLifecycleOwner.lifecycle, pagingData)
                            if (it.itemCount > 0) binding.checkboxSelectAll.isChecked = it.itemCount == viewModel.selectedItemCount(tab)
                        }
                    }
                }
            }
        }

        viewModel.onRemoveSelection.observe(viewLifecycleOwner) {
            binding.checkboxSelectAll.apply {
                removeOnCheckedStateChangedListener(checkBoxListener)
                checkedState = MaterialCheckBox.STATE_UNCHECKED
                addOnCheckedStateChangedListener(checkBoxListener)
            }
        }
    }

    private fun initAdapter() {
        mAdapter = AddParticipantsListAdapter(object : AddParticipantsListAdapter.ItemListener {
            override fun onItemClick(item: AddParticipantsResponse.Members, position: Int) {
                viewModel.selectMember(item, position)

                if (mAdapter?.itemCount == viewModel.selectedItemCount(tab)) {
                    binding.checkboxSelectAll.isChecked = true
                }
            }

            override fun onItemChecked(item: AddParticipantsResponse.Members, position: Int) {
            }

        })

        val layoutMan = LinearLayoutManager(context)
        binding.broadcastList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
                binding.checkboxSelectAll.isVisible = state == MultiStateView.ViewState.CONTENT
                binding.textSelectAll.isVisible = state == MultiStateView.ViewState.CONTENT
                binding.search.isVisible = state == MultiStateView.ViewState.CONTENT

                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }

}