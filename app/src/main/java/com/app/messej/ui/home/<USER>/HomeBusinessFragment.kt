package com.app.messej.ui.home.businesstab

import android.app.Activity
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.business.PayoutData
import com.app.messej.data.model.enums.AttachmentSource
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentHomeBusinessBinding
import com.app.messej.ui.chat.ChatAttachSourceFragment
import com.app.messej.ui.common.PolicyDocumentFragment
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.businesstab.operations.status.BusinessWorkStatusBaseFragment
import com.app.messej.ui.home.businesstab.operations.status.BusinessWorkStatusViewPagerFragment
import com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindGiftRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindLegalAffairsPayFineButton
import com.app.messej.ui.utils.FragmentExtensions.bindMaidanToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.setBadgeNumber
import com.app.messej.ui.utils.FragmentExtensions.setupPayFineIcon
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils
import com.google.android.material.tabs.TabLayoutMediator
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class HomeBusinessFragment : Fragment(), MenuProvider {
    private lateinit var binding: FragmentHomeBusinessBinding

    private lateinit var mBusinessPagerAdapter: FragmentStateAdapter
    private val mViewModel: HomeBusinessViewModel by activityViewModels()
    private val businessWithDrawViewModel: BusinessWithDrawViewModel by activityViewModels()
    private val businessStatementViewModel : BusinessStatementViewModel by activityViewModels()
    private val commonViewModel: CommonHomeViewModel by activityViewModels()
    private val args: HomeBusinessFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_home_business, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.appbar.toolbar, customBackButton = false)
        bindFlaxRateToolbarChip(binding.appbar.flaxRateChip)
        bindGiftRateToolbarChip(binding.appbar.giftChip)
        bindMaidanToolbarChip(binding.appbar.maidanChip)
        bindLegalAffairsPayFineButton(binding.appbar.payFineChip)
        selectDealsTab(args.destination)
    }
    

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        observe()
        setup()
        binding.homePager.isUserInputEnabled = false
    }

    companion object {
        const val TAB_OPERATIONS = 0
//        const val TAB_CUSTOMERS = 1
        const val TAB_DEALS = 1
        const val TAB_STATEMENT = 2

    }

    private fun setup() {
        mBusinessPagerAdapter = object : FragmentStateAdapter(this) {

            override fun getItemCount(): Int = 3

            override fun createFragment(position: Int): Fragment {
                return when (position) {

                 //   TAB_OPERATIONS -> BusinessOperationTaskHomeFragment()
                    TAB_OPERATIONS -> BusinessWorkStatusViewPagerFragment()
                    TAB_DEALS -> BusinessDealsViewPagerFragment()
                    TAB_STATEMENT ->{
                        BusinessStatementFragment()
                    }

                    else -> throw IllegalArgumentException("There should only be 2 tabs")
                }
            }
        }
        val initialTab = mViewModel.currentTab.value
        binding.homePager.apply {
            adapter = mBusinessPagerAdapter
            setCurrentItem(initialTab?: TAB_DEALS, false)
            registerOnPageChangeCallback (object: ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    mViewModel.setCurrentTab(position)
                }
            })
        }

        TabLayoutMediator(binding.appbar.homeTab, binding.homePager) { tab, position ->
            when (position) {
                TAB_OPERATIONS -> {
                    tab.text = resources.getString(R.string.title_business_work)
                    tab.setIcon(R.drawable.ic_home_business_tab_operations)
                }
                TAB_DEALS -> {
                    tab.text = resources.getString(R.string.title_home_business_tab_deals)
                    tab.setIcon(R.drawable.ic_home_business_tab_deals)
                }
                TAB_STATEMENT -> {
                    tab.text = resources.getString(R.string.title_home_business_tab_statements)
                    tab.setIcon(R.drawable.ic_home_business_tab_statement)
                }
            }
        }.attach()

        setupPromoBoard(binding.appbar.promoBar)
        setupPayFineIcon(composeView = binding.appbar.payFine)
    }

    fun observe() {
        businessWithDrawViewModel.onFlaxSellRequest.observe(viewLifecycleOwner) {
            if(it){
                businessWithDrawViewModel.eligibilityCheck()
            }
        }

        businessWithDrawViewModel.payoutEligibility.observe(viewLifecycleOwner) {
            Log.i("observe: ", "log to print entered BusinessDealsViewPagerFragment")
                if (it?.eligibility == true && it.payoutData != null) {
                when (it.payoutData.status) {
                    PayoutData.PayoutStatus.INCOMPLETE -> {
                        findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalBusinessPointsReviewDialogFragment())
                    }

                    else -> {
                        findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalFlaxSellingConfirmationFragment())
                    }
                }
            }
                else if (it?.eligibility == false && it.payoutData != null) {
                    findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalFlaxSellingConfirmationFragment())
                }
                else {
                findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalBusinessPointsReviewDialogFragment())
            }
        }

        commonViewModel.accountDetails.observe(viewLifecycleOwner){
            binding.appbar.citizenship = it?.citizenship
            binding.appbar.isPremium = it?.isPremium
            binding.appbar.daysLeft = it?.remainingDaysForResident
        }
        commonViewModel.ppAccountClicked.observe(viewLifecycleOwner){
            if (it){
                selectStatementTab()
            }
        }
        commonViewModel.learnMoreClicked.observe(viewLifecycleOwner){
            if (it){
                findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalPolicyFragment(DocumentType.HOME_APP_BUSINESS,false))
            }
        }
        commonViewModel.navigateToDearsList.observe(viewLifecycleOwner){
            if (it){
                findNavController().navigateSafe(HomeBusinessFragmentDirections.actionHomeBusinessFragmentToBusinessOperationsDearsList())
            }
        }
        mViewModel.profileDpClicked.observe(viewLifecycleOwner){
            if (it){
                val action = HomeBusinessFragmentDirections.actionGlobalImageAttachSourceFragment(arrayOf(AttachmentSource.GALLERY,AttachmentSource.CAMERA_PHOTO))
                findNavController().navigateSafe(action)
            }
        }

        commonViewModel.navigateDocumentPolicy.observe(viewLifecycleOwner){
            if (it.second){
                val action = HomeBusinessFragmentDirections.actionGlobalPolicyFragment(it.first,false)
                findNavController().navigateSafe(action)
            }
        }

        mViewModel.onTriggerCrop.observe(viewLifecycleOwner) {
            imageCropResult.launch(it.getIntent(requireContext()))
        }

        setFragmentResultListener(ChatAttachSourceFragment.ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when(ChatAttachSourceFragment.getSource(bundle)) {
                AttachmentSource.GALLERY -> selectImageFromGallery()
                AttachmentSource.CAMERA_PHOTO -> takeImage()
                else -> {
                    // do nothing
                }
            }
        }

        setFragmentResultListener(BusinessWorkStatusBaseFragment.STATUS_CHANGE_REQUEST_KEY) { _, bundle ->
            val isUpdate = bundle.getBoolean(BusinessWorkStatusBaseFragment.STATUS_CHANGE_RESULT_KEY)
            if (isUpdate) {
               mViewModel.onActivityUpdated(true)
            }
        }

        setFragmentResultListener(PolicyDocumentFragment.REGISTER_DOCUMENT_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(PolicyDocumentFragment.REGISTER_DOCUMENT_RESULT_KEY)
            if (result) {
                businessWithDrawViewModel.tncAccepted.postValue(result)
            }
            findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalBusinessPointsReviewDialogFragment())
        }
        mViewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            Log.w("HBVM", "observe currentTab: ${it}, pager: ${binding.homePager.currentItem}")
            if (binding.homePager.currentItem==it) return@observe
            binding.homePager.setCurrentItem(it,false)
        }

    }
    private fun selectStatementTab() {
        lifecycleScope.launch {
            delay(50)
            withContext(Dispatchers.Main){
                binding.homePager.setCurrentItem(2,true)
            }
        }
    }
    private fun selectDealsTab(destination: Int) {
        lifecycleScope.launch {
            delay(500)
            withContext(Dispatchers.Main){
                if(destination!=-1)
                binding.homePager.setCurrentItem(destination,true)
            }
        }
    }


private val imageCropResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
    if (result.resultCode == Activity.RESULT_OK) {
        result.data?.let {
            val resultUri = UCrop.getOutput(it)?: return@let
            mViewModel.addCroppedImage(resultUri)
        }
    } else {
        mViewModel.onCropCancelled()
    }
}
    private fun selectImageFromGallery() = selectImageFromGalleryResult.launch("image/*")

    private val takeImageResult =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { isSuccess ->
            if (isSuccess) {
                mViewModel.addCapturedImage()
            }
        }

    private val selectImageFromGalleryResult =
        registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
            uri?.let {
                mViewModel.addImage(uri)
            }
        }

    private fun takeImage() {
        checkCameraPermission(binding.root) {
            lifecycleScope.launch {
                val uri = mViewModel.getImageUriForCapture()
                takeImageResult.launch(uri)
            }
        }
    }


    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        Log.w("MNUF", "HPF: onCreateMenu")
        return menuInflater.inflate(R.menu.menu_home_notifications,menu)
    }

    private var notificationBadge: BadgeDrawable? = null

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        notificationBadge = BadgeDrawable.create(requireContext())
        setBadgeNumber(notificationBadge,commonViewModel.unreadNotifications.value)
        notificationBadge?.apply {
            adjustForNotifications(requireContext())
            BadgeUtils.attachBadgeDrawable(this, binding.appbar.toolbar, R.id.action_notifications)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_notifications -> findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalNotificationFragment())
            else -> return false
        }
        return true
    }
}