package com.app.messej.ui.home.gift

import android.app.Dialog
import android.graphics.Typeface
import android.os.Bundle
import android.text.style.StyleSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.runtime.SideEffect
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.databinding.FragmentGiftAlertVideoBottomSheetBinding
import com.app.messej.ui.home.CommonGiftViewModel
import com.app.messej.ui.home.publictab.podiums.videoGift.TransparentVideo
import com.app.messej.ui.home.publictab.podiums.videoGift.TransparentVideoViewModel
import com.app.messej.ui.utils.BottomSheetExtensions.setMatchParent
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.Synchronize
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import java.io.File

class GiftAlertVideoBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentGiftAlertVideoBottomSheetBinding
    private val viewModel: CommonGiftViewModel by activityViewModels()
//    private val navArgs: GiftAlertBottomSheetFragmentArgs by navArgs()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet_Transparent)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_alert_video_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        observe()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isFitToContents = false
                isDraggable = false
                skipCollapsed = true
                isCancelable=true
            }
        }
        return dialog
    }

    private fun observe() {
        viewModel.onGiftReplySent.observe(this) {
            exitAndPrepare()
            Toast.makeText(context, getString(R.string.title_reply_sent_successfully), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onStart() {
        super.onStart()
        setMatchParent()
    }

    private fun setup() {
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            state = BottomSheetBehavior.STATE_EXPANDED
        }
        setupGiftVideo()
        val video = viewModel.consumeGiftVideo() ?: return
        displayVideoGift(video.first, video.second)
    }

    private lateinit var transparentVideoViewModel: TransparentVideoViewModel

    fun setupGiftVideo() {
        transparentVideoViewModel = TransparentVideoViewModel(lifecycleScope, requireContext())
    }

    private var didPopBackstack: Boolean by Synchronize(false)

    private fun popSafely() {
        Log.w("PLF", "popSafely: didPopBackstack: $didPopBackstack")
        if (didPopBackstack) return
        didPopBackstack = true
        Log.w("PLF", "popSafely: popping...")
        findNavController().popBackStack(R.id.giftAlertVideoBottomSheetFragment, true)
    }

    private fun exitAndPrepare() {
        popSafely()
    }

    override fun onStop() {
        super.onStop()
        transparentVideoViewModel.stopPlayback()
        viewModel.readyToPlayGifts()
    }

    private fun displayVideoGift(gift: SentGiftPayload?, file: File) {
        Log.d("GFTV", "displayVideoGift ${gift?.translatedName} - ${file.absolutePath}")
        binding.videoGiftHolder.isVisible = true

        binding.giftSnackbar.root.isVisible = gift!=null
        if (gift!=null) {
            binding.giftSnackbar.apply {
                podiumGift = gift
                citizenship= viewModel.user.citizenship
                val senderNameOrNickName = viewModel.getNickName(gift.senderId!!, gift.senderName)
                val receiveNameOrNickName = viewModel.getNickName(gift.receiverId!!, gift.receiverName)

                val tabTitle = when (gift.giftType) {
                                    GiftType.VIP -> getString(R.string.gift_tab_title_vip)
                                    GiftType.PERSONAL -> getString(R.string.gift_title_personal)
                                    else -> "" // Provide a default value if necessary
                                }

                val msg = if (gift.isReceived) {
                    if (gift.giftType == GiftType.BANK && gift.categoryName == SentGiftPayload.COINS){
                        getString(R.string.gift_video_coins_txn_title, senderNameOrNickName, gift.coins.toString(), receiveNameOrNickName)
                    }
                    else if (gift.giftType == GiftType.BANK && gift.categoryName == SentGiftPayload.FLIX){
                        getString(R.string.gift_video_received_flix_title, senderNameOrNickName, gift.flix.toString(), receiveNameOrNickName)
                    }
                    else if (gift.specialOccasionDate!=null){
                        getString(R.string.gift_video_special_received, senderNameOrNickName, gift.giftName,receiveNameOrNickName)
                    }else if (gift.giftType == GiftType.VIP) {
                        getString(R.string.gift_video_received_updated, senderNameOrNickName)
                    }
                    else if (gift.giftType == GiftType.PERSONAL) {
                        getString(R.string.gift_video_received_updated, senderNameOrNickName)
                    }
                    else{
                        getString(R.string.gift_video_sent_personal, senderNameOrNickName,receiveNameOrNickName)
                    }
                }
                else if (gift.isSent) {
                    if (gift.giftType == GiftType.BANK && gift.categoryName == SentGiftPayload.COINS) {
                        getString(R.string.gift_video_coins_txn_title,senderNameOrNickName,gift.coins.toString(),receiveNameOrNickName)
                    } else if(gift.giftType == GiftType.BANK && gift.categoryName == SentGiftPayload.FLIX){
                        viewModel.onFlixDeductionAccountUpdate(gift.flix)
                        getString(R.string.gift_video_sent_bank_flix,senderNameOrNickName,gift.flix.toString(),receiveNameOrNickName)
                    }
                    else if (gift.giftType == GiftType.VIP) {
                        getString(R.string.gift_video_sent_vip, receiveNameOrNickName)
                    } else {
                        if (gift.specialOccasionDate!=null){
                            getString(R.string.gift_video_special_sent,gift.giftName, receiveNameOrNickName)
                        }else{
                            getString(R.string.gift_video_sent_personal, senderNameOrNickName, receiveNameOrNickName)
                        }
                    }
                } else {
                    if (gift.giftType == GiftType.BANK && gift.categoryName == SentGiftPayload.COINS) {
                        getString(R.string.gift_video_coins_txn_title,senderNameOrNickName,gift.coins.toString(),receiveNameOrNickName)
                    } else if(gift.giftType == GiftType.BANK && gift.categoryName == SentGiftPayload.FLIX){
                        viewModel.onFlixDeductionAccountUpdate(gift.flix)
                        getString(R.string.gift_video_sent_bank_flix,senderNameOrNickName,gift.flix.toString(),receiveNameOrNickName)
                    } else{
                        getString(R.string.gift_video_other_user, senderNameOrNickName, receiveNameOrNickName)
                    }
                }
                giftCoin.text = if (viewModel.user.id == gift.receiverId) gift.coinsReceived?.toString()
                else gift.coins?.toInt().toString()

                title.text = msg.highlightOccurrences(senderNameOrNickName.orEmpty()) {
                    StyleSpan(Typeface.BOLD)
                }.highlightOccurrences(receiveNameOrNickName.orEmpty()) {
                    StyleSpan(Typeface.BOLD)
                }.highlightOccurrences(gift.translatedName.orEmpty()) {
                    StyleSpan(Typeface.BOLD)
                }

                closeButton.setOnClickListener {
                    exitAndPrepare()
                }
                giftButton.setOnClickListener {
                    gift.senderId.let { senderId->
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalGiftFragment(senderId, giftContext = GiftContext.GIFT_BUDDIES))
                    }
                }
                thanksButton.setOnClickListener {
                     if(!viewModel.onSayThanksLoading){
                        viewModel.sayThanks(gift)}
                }
                replayButton.setOnClickListener {
                    transparentVideoViewModel.onMediaFinished = false
                    transparentVideoViewModel.replayVideo()
                }
            }
        }

        transparentVideoViewModel.initialiseMediaPlayer(videoFile = file)
        binding.giftVideoComposeView.setContent {
            Log.d("GFTV", "displayVideoGift composable")

            val onMediaFinished = transparentVideoViewModel.onMediaFinished
            Log.d("GFTVFINSHED", onMediaFinished.toString())

            binding.giftSnackbar.replayButton.isEnabled = onMediaFinished


           /* val sayThanksLoading = viewModel.onSayThanksLoading
            Log.d("DoubleClicked", sayThanksLoading.toString())
            binding.giftSnackbar.thanksButton.isEnabled = !sayThanksLoading*/ /**added loading condition in button click**/

            if (onMediaFinished) {
                if(gift==null || gift.isSent || !gift.isInvolved){
                    SideEffect {
                        exitAndPrepare()
                    }
                }
                // Do nothing, or you can put some specific logic here
                Log.d("GFTV", "VIDEO FINISHED OBSERVED IN FRAGMENT")

            }
            TransparentVideo(viewModel = transparentVideoViewModel)
        }
    }
}