package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.transition.AutoTransition
import androidx.transition.TransitionManager
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumRecord
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.databinding.FragmentPodiumAboutBottomSheetBinding
import com.app.messej.databinding.ItemPodiumAboutAdminListBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.ScaleInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.kennyc.view.MultiStateView

class PodiumAboutBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private lateinit var binding: FragmentPodiumAboutBottomSheetBinding

    private val viewModel: PodiumAboutViewModel by viewModels()

    val args: PodiumAboutBottomSheetFragmentArgs by navArgs()

    private var mRecordsAdapter: PodiumRecordsListAdapter? = null

    private var mAdminsListAdapter: BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumAboutAdminListBinding>>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment

        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_about_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setPodiumIdAndRole(args.podiumId, args.elevated)
        binding.compactedUser=args.compacted
        setup()
        observe()
    }

    fun setup(){
        setEmptyView()
        initAdapters()
        binding.expandArrow.setOnClickListener {
            viewModel.toggleAdminView()
        }

        binding.actionClose.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.managerInfoFixed.setOnClickListener {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(viewModel.podiumManager.value?.id ?:return@setOnClickListener))
        }
    }

    fun observe(){
        viewModel.admins.observe(viewLifecycleOwner){
            it?: return@observe
            mAdminsListAdapter?.apply {
                if (data.size == 0 || it.size == 0) {
                    setNewInstance(it.toMutableList())
                } else {
                    setDiffNewData(it.toMutableList())
                }
            }
            binding.multiStateView.viewState = if(it.size == 0) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
        }
        viewModel.adminListExpanded.observe(viewLifecycleOwner){
//            TransitionManager.beginDelayedTransition(binding.parentLayout, AutoTransition())
            binding.adminListExpanded = it
        }

        viewModel.recordsList.observe(viewLifecycleOwner){pagingData ->
            pagingData?.let {
                mRecordsAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
                TransitionManager.beginDelayedTransition(binding.historyLayout, AutoTransition())
            }
        }

        viewModel.podiumDetailsLoading.observe(viewLifecycleOwner){
            binding.multiStateViewShimmer.viewState = if (it) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }
    }

    private fun initAdapters(){

        val adminsListDiffer = object : DiffUtil.ItemCallback<PodiumSpeaker>() {
            override fun areItemsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker): Boolean {
                return oldItem.id == newItem.id
            }
            override fun areContentsTheSame(oldItem: PodiumSpeaker,newItem: PodiumSpeaker): Boolean {
                return oldItem == newItem
            }
        }

        mAdminsListAdapter = object : BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumAboutAdminListBinding>>(R.layout.item_podium_about_admin_list, mutableListOf()) {

            init {
                addChildClickViewIds(R.id.user_dp)
            }
            override fun convert(holder: BaseDataBindingHolder<ItemPodiumAboutAdminListBinding>, item: PodiumSpeaker) {
                holder.dataBinding?.apply {
                    speaker = item
                }
            }

        }.apply {
            animationEnable = true
            adapterAnimation = ScaleInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(adminsListDiffer)
            setOnItemClickListener { adapter, _, position ->

            }
            setOnItemChildClickListener { adapter, view, position ->
                Log.i( "initAdapters: ", "setOnItemChildClickListener")
                when (view.id) {
                    R.id.user_dp -> {
                        var itemId: Int = getItem(position).id
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(itemId))
                    }
                }
            }
        }

        binding.adminsList.apply {
            layoutManager = GridLayoutManager(context, 2)
            setHasFixedSize(true)
            adapter = mAdminsListAdapter
        }


        mRecordsAdapter = PodiumRecordsListAdapter(object : PodiumRecordsListAdapter.PodiumActionListener {
            override fun onRecordsClicked(pod: PodiumRecord) {
                //Handle record click here (if required). Remove listener method if not required.
            }
        })

        val layoutMan = LinearLayoutManager(context)

        binding.recordList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mRecordsAdapter
        }

        mRecordsAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateViewHistory.viewState = ViewUtils.getViewState(loadState, itemCount)
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateViewHistory.getView(MultiStateView.ViewState.EMPTY)?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.edsEmptyAction.isAllCaps = false
        emptyViewBinding.prepare(
            message = R.string.podium_records_eds
        )
    }


}