package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.GiftConversion
import com.app.messej.data.model.enums.TransactionTab
import com.app.messej.databinding.FragmentBaseBusinessDealsBinding
import com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BusinessDealsViewPagerFragment:BusinessDealsBaseFragment() {
    override lateinit var binding: FragmentBaseBusinessDealsBinding
    private val viewModel: BusinessWithDrawViewModel by activityViewModels ()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_base_business_deals, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModels
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }
    fun observe() {
        viewModel.isEligibilityLoading.observe(viewLifecycleOwner){
            binding.buttonSellFlax.isClickable=!it
        }
    }

    fun setup() {
        binding.textColor = resources.getColor(R.color.textColorOnPrimary)
        binding.mainTextColor = resources.getColor(R.color.colorSecondary)
        binding.cardBackground = resources.getColor(R.color.colorPrimary)
        binding.viewAllTextColor = resources.getColor(R.color.colorPrimary)

    }


    override fun gotoSendFlax() {
        val action = HomeBusinessFragmentDirections.actionGlobalFlaxTransfer()
        findNavController().navigateSafe(action)
    }

    override fun gotoBuyCoin() {
        val action = HomeBusinessFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = true, hideActionBar = true)
        findNavController().navigateSafe(action)
    }

    override fun gotoCoinToFlax() {
        val action = HomeBusinessFragmentDirections.actionGlobalConvertCoinToFlaxFragment(GiftConversion.COIN_TO_FLAX)
        findNavController().navigateSafe(action)
    }

    override fun gotoFlaxToCoin() {
        val action = HomeBusinessFragmentDirections.actionGlobalConvertCoinToFlaxFragment(GiftConversion.FLAX_TO_COIN)
        findNavController().navigateSafe(action)
    }

    override fun gotoViewAll() {
        val action = HomeBusinessFragmentDirections.actionGlobalTransactionList(TransactionTab.TAB_FLAX)
        findNavController().navigateSafe(action)
    }

    override fun gotoSellFlax() {
        viewModel.setOnFlaxSellRequest(true)
    }

    override fun gotoRestoreRatings() {
        val action = HomeBusinessFragmentDirections.actionHomeBusinessFragmentToRestoreRatingFragment(false)
        findNavController().navigateSafe(action)
    }

    override fun gotoBuyFlix() {
        val action = HomeBusinessFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = false, hideActionBar = true)
        findNavController().navigateSafe(action)
    }

}