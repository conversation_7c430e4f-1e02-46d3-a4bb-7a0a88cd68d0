package com.app.messej.ui.chat

import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.view.ActionMode
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.view.MenuProvider
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.ui.PlayerView
import androidx.navigation.fragment.findNavController
import androidx.paging.CombinedLoadStates
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.BuildConfig
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.LayoutImageViewerHeaderBinding
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.utils.FragmentExtensions.copy
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowGift
import com.app.messej.ui.utils.FullScreenVideoPlayer
import com.app.messej.ui.utils.FullScreenVideoPlayer.prepareFullscreen
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.bumptech.glide.Glide
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView
import com.makeramen.roundedimageview.RoundedImageView
import com.stfalcon.imageviewer.StfalconImageViewer
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import render.animations.Attention
import render.animations.Render
import java.io.File


abstract class BaseChatDisplayFragment: Fragment(), ChatAdapter.ChatClickListener {

    protected var mAdapter: ChatAdapter? = null

    abstract val viewModel: BaseChatDisplayViewModel

    protected var isAdminMessage: Boolean = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d("VEXO", "onViewCreated: onCreated")
        if (!viewModel.loggedIn) findNavController().popBackStack()
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
    }

    private fun observe() {
        viewModel.chatList.observe(viewLifecycleOwner) {
            it?.let {
                mAdapter?.apply {
                    submitData(viewLifecycleOwner.lifecycle, it)
                }
            }
        }

        viewModel.pendingScrollToMessage.observe(viewLifecycleOwner) {
            Log.d("BCF", "initAdapter: scroll set for $it ${mAdapter?.itemCount}")
            //do not remove this observer. this is required for video fullscreen to work
        }

        viewModel.scrollToMessageNow.observe(viewLifecycleOwner) {
            findItemPosition(it)?.let { pos ->
                smartSmoothScrollToPosition(pos)
            }
        }

        viewModel.highlightMessage.observe(viewLifecycleOwner) {
            findItemPosition(it)?.let { pos ->
                chatRecyclerView.findViewHolderForAdapterPosition(pos)?.let { vh ->
                    if(vh is ChatAdapter.ChatHighlightViewProvider) {
                        vh.getHighlightView()?.let { view ->
                            view.visibility = View.VISIBLE
                            val render = Render(requireContext())
                            render.setAnimation(Attention().Flash(view))
                            render.setDuration(2000)
                            render.start()

                            lifecycleScope.launch {
                                delay(2000)
                                view.visibility = View.GONE
                            }
                        }
                    }
                }
            }
        }

        viewModel.searchResultMessageIds.observe(viewLifecycleOwner) { res ->
            res?.forEach {
                mAdapter?.notifyItemChanged(it.position)
            }
//            mAdapter?.notifyDataSetChanged()
        }
        viewModel.removedHighlights.observe(viewLifecycleOwner) { res ->
            res.forEach {
                mAdapter?.notifyItemChanged(it)
            }
        }

        viewModel.onItemChange.observe(viewLifecycleOwner) {
            Log.w("VPLAY", "onItemChange: $it" )
            if (it==-1) {
                mAdapter?.notifyDataSetChanged()
                return@observe
            }
            mAdapter?.notifyItemChanged(it)
        }

        viewModel.onPlayerStateChange.observe(viewLifecycleOwner) {
            mAdapter?.notifyItemChanged(it)
        }
        viewModel.onAudioPlaybackFinished.observe(viewLifecycleOwner) {
            val pos = it.messageId?.let { id -> findItemPosition(id) }
            pos?: return@observe
            playNextMedia(pos)
        }

        viewModel.chatSelectionMode.observe(viewLifecycleOwner) {
            showSelectionMode(it)
        }
        viewModel.selectedChats.observe(viewLifecycleOwner) {
            actionMode?.title = resources.getQuantityString(R.plurals.chat_selection_mode_count,it.size,it.size)
        }

        viewModel.canCopySelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_copy)?.isVisible = it
        }
        viewModel.canDeleteSelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_delete)?.isVisible = it
        }
        viewModel.canForwardPrivateSelection.observe(viewLifecycleOwner){
            actionMode?.menu?.findItem(R.id.action_forward)?.isVisible = it && !isAdminMessage
        }

        viewModel.onCopyText.observe(viewLifecycleOwner) {
            copyToClipBoard(it)
        }

        viewModel.onSavedToGallery.observe(viewLifecycleOwner) {
            Toast.makeText(context, getString(R.string.chat_saved_to_gallery), Toast.LENGTH_SHORT).show()
        }

        viewModel.onStopVideoPlayback.observe(viewLifecycleOwner) {
            Log.d("VEXO", "observe: onStopVideoPlayback")
            releasePlayer()
        }

        viewModel.nowPlaying.observe(viewLifecycleOwner) {
            Log.d("VEXO", "observe: $it ${mAdapter?.itemCount}")
            //do not remove this observer. this is required for video fullscreen to work
        }

        multiStateView?.viewState = MultiStateView.ViewState.LOADING

        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            Log.w("BCDFLSL", "debouncedViewState: viewstate = $it")
            multiStateView?.viewState = it

        }
    }

    private fun copyToClipBoard(msg: AbstractChatMessage) {
        val message = if(msg is HuddleChatMessage) {
            msg.convertedMentionMessage
        } else {
            msg.displayMessage
        }
        copy(message)
    }

    protected abstract val chatRecyclerView: RecyclerView
    protected abstract val multiStateView: MultiStateView?

    protected abstract val bindingRoot: View

    protected open fun allowReplySwipe() = false

    override fun onMediaDownload(view: View, msg: AbstractChatMessageWithMedia, position: Int) {
        if (viewModel.selectMessage(msg.message,position)) return
        viewModel.downloadMedia(msg,position) /*{
            onMediaImageTap(view, msg, position)
        }*/
    }

    open fun customizeImageOverlay(viewer: StfalconImageViewer<OfflineMedia>,headerBinding: LayoutImageViewerHeaderBinding, msg: AbstractChatMessageWithMedia) {
        headerBinding.toolbar.addMenuProvider(object: MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_chat_image_fullscreen,menu)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                when (menuItem.itemId) {
                    R.id.action_save_to_gallery -> viewModel.saveToGallery(msg.offlineMedia)
                    else -> return false
                }
                return true
            }

        },viewLifecycleOwner)
    }

    override fun onMediaImageTap(view: View, msg: AbstractChatMessageWithMedia, position: Int) {
        if (viewModel.selectMessage(msg.message,position)) return
        msg.offlineMedia?.let { media ->
            val headerBinding = LayoutImageViewerHeaderBinding.inflate(layoutInflater, null, false)
            val viewer = StfalconImageViewer
                .Builder(context, listOf(media)) { imageView, image ->
                    Glide.with(requireContext())
                        .load(image.path)
                        .into(imageView)
                }
                .withTransitionFrom(view as RoundedImageView)
                .withHiddenStatusBar(false)
                .withOverlayView(headerBinding.root)
                .build()

            viewer.show()

            headerBinding.toolbar.apply {
                setNavigationIcon(R.drawable.ic_back_button)
                setNavigationIconTint(ContextCompat.getColor(context,R.color.textColorOnPrimary))
                setNavigationOnClickListener {
                    viewer.close()
                }
            }

            val hcm = msg.message
            val converted = if (hcm is HuddleChatMessage && hcm.hasMention) {
                UserInfoUtil.decodeMentions(hcm.displayMessage.orEmpty(), hcm.mentionedUsers.orEmpty())
            } else msg.message.displayMessage.orEmpty()
            headerBinding.caption = converted
            headerBinding.ctc = hcm.chatTextColor

            customizeImageOverlay(viewer,headerBinding,msg)
        }
    }

    override fun onMediaDocumentTap(view: View, msg: AbstractChatMessageWithMedia, position: Int) {
       openDocument(msg)
    }

    override fun onMediaLocationTap(view: View, msg: AbstractChatMessageWithMedia, position: Int) {
        msg.message.attachedLocation?.let { loc ->
            val gmmIntentUri = Uri.parse("geo:?q=${loc.latitude},${loc.longitude}")
            Log.w("GADDR", "onMediaLocationTap: $gmmIntentUri")
            val mapIntent = Intent(Intent.ACTION_VIEW, gmmIntentUri)
            mapIntent.setPackage("com.google.android.apps.maps")
            startActivity(mapIntent)
        }
    }

    override fun onMediaAudioPlay(view: View, msg: AbstractChatMessageWithMedia, position: Int) {
        if (viewModel.selectMessage(msg.message,position)) return
        viewModel.playMedia(msg,position)
    }

    protected open val shouldAutoplayNextAudio = false

    private fun playNextMedia(pos: Int) {
        if (!shouldAutoplayNextAudio) return
        mAdapter?.apply {
            val nextPos = pos-1
            val items = snapshot().items
            if (nextPos<0) return
            val nextItem = items.getOrNull(nextPos)?: return
            when(nextItem) {
                is ChatMessageUIModel.DateSeparatorModel,is ChatMessageUIModel.UnreadSeparatorModel -> playNextMedia(nextPos)
                is ChatMessageUIModel.ChatMessageModel -> {
                    if (nextItem.message.hasMedia && nextItem.message.mediaType == MediaType.AUDIO) {
                        if (!isPositionVisible(nextPos)) smartSmoothScrollToPosition(nextPos)
                        if (nextItem.chat.hasOfflineMedia) viewModel.playMedia(nextItem.chat, nextPos)
                    }
                }
                else -> return
            }

        }
    }

    override fun onMediaAudioToggleSpeed(view: View, msg: AbstractChatMessageWithMedia, position: Int) {
        viewModel.cycleMediaSpeed(msg)
    }

    override fun onMediaAudioSeek(view: View, msg: AbstractChatMessageWithMedia, position: Int, seekPosition: Float) {
        if (viewModel.selectMessage(msg.message,position)) return
        viewModel.seekMedia(msg,position,seekPosition)
    }

    private fun openDocument(msg: AbstractChatMessageWithMedia){
        msg.offlineMedia?.let {
            val fileUri: Uri = try {
                FileProvider.getUriForFile(
                    requireContext(), "${BuildConfig.APPLICATION_ID}.provider", File(it.path)
                )
            } catch (e: IllegalArgumentException) {
                Log.e(
                    "File Selector", "The selected file can't be shared: ${it.path}"
                )
                return
            }
            try {
                val mime: String = requireContext().contentResolver.getType(fileUri) ?: "*/*"
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(fileUri, mime)
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                startActivity(intent)
            }catch (e: Exception){
                if (e is ActivityNotFoundException){
                    Toast.makeText(requireContext(), R.string.chat_document_app_not_found, Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun releasePlayer() {
        Log.d("VEXO", "releasePlayer: ")
        FullScreenVideoPlayer.release()
        fullscreenPlayer = null
    }

    private var fullscreenPlayer: FullScreenVideoPlayer.FullScreenActions? = null

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    protected fun playVideo(view: PlayerView, med: MediaItem? = null) {
        Log.d("VEXO", "playVideo: ")

        view.findViewById<View>(androidx.media3.ui.R.id.exo_settings).isVisible = false

        FullScreenVideoPlayer.getPlayer(requireContext()).apply {
            fullscreenPlayer = prepareFullscreen(view)
            clearMediaItems()
            med?.let {
                setMediaItem(it)
            }
            prepare()
            play()
        }
    }

    override fun onMediaVideoPlay(view: PlayerView, msg: AbstractChatMessageWithMedia, position: Int): Boolean {
        if (viewModel.selectMessage(msg.message,position)) return false
        val media = msg.offlineMedia?.let { MediaItem.fromUri(it.path) }?: return false
        viewModel.onPlayingVideo(msg,position)
        playVideo(view, media)
        return true
    }

    override fun onViewHolderCleanup(messageId: String) {
        if(viewModel.releaseMediaAssetsIfPlaying(messageId)) {
            Log.d("VEXO", "onMediaVideoCleanup: ")
            releasePlayer()
        }
    }

    override fun onItemClick(item: AbstractChatMessage, position: Int) = viewModel.selectMessage(item,position)

    override fun onItemLongClick(item: AbstractChatMessage, position: Int) {
        viewModel.enterSelectionMode(item,position)
    }

    override fun onItemReplyClick(item: AbstractChatMessage, position: Int): Boolean {
        item.replyTo?.let {
            viewModel.onReplyClick(item)
            return true
        }
        return false
    }

    override fun onItemLike(item: AbstractChatMessage, position: Int) {}

    override fun onGiftClick(item: SentGiftPayload, position: Int) {

        if(item.giftAnimationUrlAndroid!=null) {
            downloadAndShowGift(item)
        }else{
            item.senderId?.let { senderId ->
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNotificationLottieBottomSheetFragment(item.id, senderId, true))
            }
        }
    }

    protected fun confirmDelete(@StringRes title: Int, @StringRes message: Int, canDeleteForMe: Boolean, onConfirm: (forEveryone: Boolean) -> Unit) {
        confirmDelete(title, message, canDeleteForMe,true, onConfirm)
    }

    protected fun confirmDelete(@StringRes title: Int, @StringRes message: Int, canDeleteForMe: Boolean, canDeleteForEveryone: Boolean, onConfirm: (forEveryone: Boolean) -> Unit) {
        val builder = MaterialAlertDialogBuilder(requireContext()).setTitle(resources.getString(title)).setMessage(resources.getString(message))
        if (canDeleteForEveryone) {
            builder.setPositiveButton(resources.getString(R.string.chat_delete_for_everyone)) { dialog, which ->
                onConfirm.invoke(true)
            }
        }
        builder.setNeutralButton(resources.getString(R.string.common_cancel)) { dialog, which ->

        }
        if(canDeleteForMe) {
            builder.setNegativeButton(resources.getString(R.string.chat_delete_for_me)) { dialog, which ->
                onConfirm.invoke(false)
            }
        }
        builder.show()
    }

    protected abstract val provideAdapter: ChatAdapter

    protected open val reversedLayout: Boolean = true

    protected open fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = provideAdapter

        mAdapter?.apply {
            stateRestorationPolicy = RecyclerView.Adapter.StateRestorationPolicy.PREVENT_WHEN_EMPTY

            addOnPagesUpdatedListener {
                viewModel.pendingScrollToMessage.value?.let {
                    findItemPosition(it)?.let { pos ->
                        refresh()
                        chatRecyclerView.scrollToPosition(pos)
                        viewModel.clearPendingScroll()
                    }

                }
                viewModel.searchResultMessageIds.value?.let {
                    val man = chatRecyclerView.layoutManager as LinearLayoutManager
                    viewModel.findNearest(man.findFirstCompletelyVisibleItemPosition(),man.findLastCompletelyVisibleItemPosition())
                }
            }
            addLoadStateListener { loadState ->
                processLoadStates(loadState)
            }
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeInserted(positionStart: Int, itemCount: Int) {
                    super.onItemRangeInserted(positionStart, itemCount)
                    if((positionStart == 0) &&(chatRecyclerView.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition() == 0) {
                        chatRecyclerView.scrollToPosition(0)
                    }
                }
            })
        }

        val layoutMan = LinearLayoutManager(context)
        if (reversedLayout) {
            layoutMan.reverseLayout = true
            layoutMan.stackFromEnd = false
        }

        chatRecyclerView.apply {
            itemAnimator = null
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }
    }

    protected open fun processLoadStates(loadState: CombinedLoadStates) {
        multiStateView?.let { msv ->
            val state = if(viewModel.showChats.value==false) MultiStateView.ViewState.CONTENT
            else ViewUtils.getViewState(loadState,mAdapter?.itemCount?:0)

            Log.d("CHAT","$state $loadState ${mAdapter?.itemCount}")
            viewModel.setViewState(state)
        }
    }

    protected fun findItemPosition(id: String): Int? {
        mAdapter?.apply {
            val items = snapshot().items
            if (items.isEmpty()) return null
            val position = items.indexOfFirst { item ->
                return@indexOfFirst item is ChatMessageUIModel.ChatMessageModel && item.message.messageId == id
            }
            if (position>=0) return position
        }
        return null
    }

    protected fun isPositionVisible(pos: Int): Boolean {
        val layout = (chatRecyclerView.layoutManager as LinearLayoutManager)
        val top = layout.findFirstCompletelyVisibleItemPosition()
        val bottom = layout.findLastCompletelyVisibleItemPosition()
        return pos in top..bottom
    }

    protected fun smartSmoothScrollToPosition(pos: Int, maxDistance: Int = 20) {
        val layout = (chatRecyclerView.layoutManager as LinearLayoutManager)
        val top = layout.findFirstCompletelyVisibleItemPosition()
        val bottom = layout.findLastCompletelyVisibleItemPosition()
        val distance = if (pos<top) top-pos else if(pos>bottom) pos-bottom else 0

        if (distance>maxDistance) chatRecyclerView.scrollToPosition(pos) else chatRecyclerView.smoothScrollToPosition(pos)
    }

    // Selection Mode

    var actionMode: ActionMode? = null

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

//        val rotation = if (SDK_INT >= Build.VERSION_CODES.R) {
//            activity?.display?.rotation?:1
//        } else {
//            @Suppress("DEPRECATION")
//            activity?.windowManager?.defaultDisplay?.rotation?:1
//        }
//        Log.w("VEXO", "onConfigurationChanged: ${newConfig.orientation}: $rotation")

        Log.w("VEXO", "onResume: nowPlaying ${viewModel.nowPlaying.value}")
        viewModel.nowPlaying.value?.let {
            if (it.listPosition>=0) {
                Log.w("VEXO", "onResume: scrolling to ${it.listPosition}")
                chatRecyclerView.scrollToPosition(it.listPosition)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (fullscreenPlayer?.exitFullScreen()!=true) findNavController().popBackStack()
            }
        })
    }

    override fun onPause() {
        super.onPause()
        if(activity?.isChangingConfigurations != true) {
            Log.d("VEXO", "onPause")
            viewModel.releaseMediaAssets()
            releasePlayer()
            showSelectionMode(false)
        }
    }

    open fun showSelectionMode(show: Boolean) {
        // override to support selection
    }
}