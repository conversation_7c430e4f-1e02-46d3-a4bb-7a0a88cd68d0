package com.app.messej.ui.home.publictab.flash.myflash

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.FlashEligibility
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DDMMYYYY_DASHED
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.ZoneId
import java.time.ZonedDateTime

class MyFlashDraftViewModel(application: Application) : FlashListBaseViewModel(application) {

    override val _flashFeedList: LiveData<PagingData<FlashVideo>> = flashRepo.getDraftFlashPager().liveData.cachedIn(viewModelScope)

    private var currentDateZonedTime = ZonedDateTime.now(ZoneId.systemDefault())
    private val currentDate = DateTimeUtils.format(currentDateZonedTime, FORMAT_DDMMYYYY_DASHED)

    private val _flashEligibility = MutableLiveData<FlashEligibility?>(null)
    val flashEligibility : LiveData<FlashEligibility?> = _flashEligibility

    private val _isFlashEligibilityLoading = MutableLiveData(false)
    val isFlashEligibilityLoading : LiveData<Boolean> = _isFlashEligibilityLoading

    val flashDraftId = LiveEvent<String>()

    fun getFlashEligibilityDetails(flashId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            _isFlashEligibilityLoading.postValue(true)
            val result = flashRepo.getFlashEligibilityDetails(currentDate = currentDate)
            when(result) {
                is ResultOf.Success -> {
                    _flashEligibility.postValue(result.value)
                    flashDraftId.postValue(flashId)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
            _isFlashEligibilityLoading.postValue(false)
        }
    }

}