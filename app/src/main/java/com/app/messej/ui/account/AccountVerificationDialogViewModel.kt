package com.app.messej.ui.account

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.profile.VerifyAccountResponse
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AccountVerificationDialogViewModel(application: Application) : AndroidViewModel(application) {
    private val profileRepo = ProfileRepository(getApplication())

    private val _accountDetails = MutableLiveData<VerifyAccountResponse?>()
    val accountDetails: LiveData<VerifyAccountResponse?> = _accountDetails

    val verifyAccountResponse = LiveEvent<Boolean>()

    private val _dataLoading = MutableLiveData<Boolean?>(false)
    val dataLoading: LiveData<Boolean?> = _dataLoading

    init {
        getVerifyAccountDetails()
    }

    fun verifyAccount() {
        _dataLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<String> = profileRepo.verifyAccount()) {
                is ResultOf.Success -> {
                    _dataLoading.postValue(false)
                    verifyAccountResponse.postValue(true)
                }
                is ResultOf.APIError -> {
                    _dataLoading.postValue(false)
                }
                is ResultOf.Error -> {
                }
            }
            _dataLoading.postValue(false)
        }
    }

    private fun getVerifyAccountDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = profileRepo.getVerifyAccountDetails()) {
                is ResultOf.Success -> {
                    _accountDetails.postValue(result.value)
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }
}