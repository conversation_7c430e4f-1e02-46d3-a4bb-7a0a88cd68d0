package com.app.messej.ui.home.forward.search

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.forward.Forward
import com.app.messej.data.model.enums.ForwardType
import com.app.messej.data.repository.ForwardRepository
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class ForwardSearchViewModel(application: Application):AndroidViewModel(application) {

    private val repo= ForwardRepository(application)
    private val _tab = MutableLiveData<ForwardType>(null)
    val tab: MutableLiveData<ForwardType> = _tab

    private val _forward = MutableLiveData<Forward>(null)
    private val forward: MutableLiveData<Forward> = _forward

    private val _pageState= MutableLiveData<String>(null)
    private val pageState: MutableLiveData<String> = _pageState

     val isAnyOptionSelected = MutableLiveData(false)
     val actionLoading = MutableLiveData(false)
    val selectedPosition = LiveEvent<Int>()

    val dataLoading = MutableLiveData(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(actionLoading.value==true||dataLoading.value==true)
        }
        med.addSource(actionLoading) { update() }
        med.addSource(dataLoading) { update() }
        med
    }

    var searchKeyword = MutableLiveData("")
    private val searchTerm = MutableLiveData("")

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                searchTerm.postValue(it.orEmpty())
            }
        }
    }



    val forwardSearchList = searchTerm.switchMap { search ->
        _tab.value?.let { tab ->
            if (search.isNullOrEmpty()) {
                repo.getForwardListing(tab).liveData.cachedIn(viewModelScope)
            } else {
                repo.getForwardSearchList(tab, search).liveData.cachedIn(viewModelScope)
            }
        } ?: MutableLiveData() 
    }

    fun setTab(tab: Int?) {
        when (tab) {
            0 -> _tab.value=ForwardType.MESSAGES
            1 -> _tab.value=ForwardType.GROUPS
            2 ->_tab.value=ForwardType.HUDDLES
            else->{
                _tab.value=ForwardType.MESSAGES
            }
        }
    }

    fun setLoading(isLoading: Boolean) {
        actionLoading.postValue(isLoading)
    }

    fun setSelectedItem(position: Int) {
         selectedPosition.postValue(position)
    }

    fun setDataLoading(isLoading: Boolean){
        dataLoading.postValue(isLoading)
    }

    fun setOptionSelected(selected: Boolean) {
       isAnyOptionSelected.postValue(selected)
    }
}