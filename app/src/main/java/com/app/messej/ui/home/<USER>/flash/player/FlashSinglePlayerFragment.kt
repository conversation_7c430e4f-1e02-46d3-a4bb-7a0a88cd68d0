package com.app.messej.ui.home.publictab.flash.player

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs

class FlashSinglePlayerFragment : BaseFlashPlayerFragment() {
    override val viewModel: FlashSinglePlayerViewModel by viewModels()

    private val args: FlashSinglePlayerFragmentArgs by navArgs()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        viewModel.setFlashId(args.flashId)
        viewModel.setActiveItem(0)
        super.onViewCreated(view, savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        if(!args.commentId.isNullOrBlank()) {
            gotoFlashComments(args.flashId)
        }
    }
}