package com.app.messej.ui.home.gift.bottomSheet;


import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.BirthdayUser
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BirthdayBottomSheetViewModel(application:Application) : AndroidViewModel(application){
    private  val profileRepo = ProfileRepository(application)
    private val accountRepo: AccountRepository = AccountRepository(application)
    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading


    private val userId = MutableLiveData<Int?>(null)
     val _profile = userId.switchMap {
        it ?: return@switchMap MutableLiveData<OtherUser?>(null)
        profileRepo.getLocalOtherUserProfile(it)
    }
    val user: CurrentUser?
        get() = if (accountRepo.loggedIn) accountRepo.user else null

    private val _birthdaysList = MutableLiveData<List<BirthdayUser>>(null)

    val birthdaysList: MediatorLiveData<List<BirthdayUser>> by lazy {
        val med= MediatorLiveData<List<BirthdayUser>>()
        fun combine(){
            val profile = _profile.value
          if(profile != null){
             val birthday = BirthdayUser(
                 name = profile.name,
                 userId=profile.id,
                 profilePhoto = profile.thumbnail
             )
              med.postValue(listOf(birthday))
          }else{
              med.postValue(_birthdaysList.value)
          }
        }
        med.addSource(_birthdaysList){ combine() }
        med.addSource(_profile){ combine() }
        med
    }


    val otherBirthdayCount =_birthdaysList.map {
        it?.size
    }

     val firstBirthday =birthdaysList.map {
         it?.firstOrNull()
     }



    fun setBirthdays(birthdays:List<BirthdayUser>){
        _birthdaysList.postValue(birthdays)
    }

    fun setBirthday(id:Int){
            _dataLoading.postValue(true)
            userId.postValue(id)
        Log.d("NOTi_USERID","NOTI "+id.toString())
            viewModelScope.launch(Dispatchers.IO) {
                when (val result = profileRepo.getPublicUserDetails(id)) {
                    is ResultOf.Success -> {
                        _dataLoading.postValue(false)
                        Log.d("NOTi_USERID","NOTI_Response "+result.value.toString())
                    }

                    is ResultOf.APIError -> {
                        _dataLoading.postValue(false)
                    }

                    is ResultOf.Error -> {
                        _dataLoading.postValue(false)
                    }
                }
            }

    }
}
