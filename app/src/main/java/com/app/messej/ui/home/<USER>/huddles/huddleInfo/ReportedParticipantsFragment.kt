package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.huddles.ReportedByResponse
import com.app.messej.databinding.FragmentReportedParticipantsBinding
import com.app.messej.databinding.LayoutReportedMessageDialogBinding
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class ReportedParticipantsFragment : Fragment() {

    private lateinit var binding: FragmentReportedParticipantsBinding
    private val viewModel : ReportedParticipantsViewModel by viewModels()

    private var mAdapter : ReportedParticipantsListAdapter? = null
    private val args : ReportedParticipantsFragmentArgs by navArgs()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_reported_participants, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.reportedParticipantsList.observe(viewLifecycleOwner){
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun setup() {
        viewModel.setReportId(args.huddleId,args.reportId,args.count,args.reportType)
        initAdapter()
    }

    override fun onStart() {
        super.onStart()
        val actionBar = binding.customActionBar.toolbar
        (activity as MainActivity).setupActionBar(actionBar)
        viewModel.reportedParticipantsCount.observe(viewLifecycleOwner){
            actionBar.title = resources.getString(R.string.huddle_reported_participants_title, it)
        }
    }

    private fun initAdapter() {
        mAdapter = ReportedParticipantsListAdapter(object : ReportedParticipantsListAdapter.ItemListener{
            override fun onItemClick(item: ReportedByResponse.ReportedParticipant, position: Int) {
                showDetailsDialog(item)
            }
        })

        val layoutMan = LinearLayoutManager(context)
        binding.messagesList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }

    private fun showDetailsDialog(details: ReportedByResponse.ReportedParticipant) {
        val binding = LayoutReportedMessageDialogBinding.inflate(layoutInflater)
        binding.category.text = details.category
        binding.userComment.text = details.comment

        MaterialAlertDialogBuilder(requireContext())
            .setView(binding.root)
            .create()
            .show()
    }
}