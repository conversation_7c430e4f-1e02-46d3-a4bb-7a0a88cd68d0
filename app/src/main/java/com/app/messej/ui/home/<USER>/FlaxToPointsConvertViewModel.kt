package com.app.messej.ui.home.gift

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.app.messej.data.repository.GiftRepository
import com.hadilq.liveevent.LiveEvent

class FlaxToPointsConvertViewModel(application: Application) : AndroidViewModel(application) {
    private val giftRepository = GiftRepository(application)
    val successMessage = LiveEvent<String>()
    val errorMessage = LiveEvent<String>()
    val dialogDismiss = LiveEvent<Boolean>()

    private val _pointsVar = MutableLiveData<String>()
    val pointsVar: LiveData<String> = _pointsVar

    private val _flaxVar = MutableLiveData<String>()
    val flaxVar: LiveData<String> = _flaxVar

    fun seArgs(points: String,flax: String) {
        _pointsVar.value = points
        _flaxVar.value = flax
    }

//    fun giftFlaxToPointConvert() {
//        viewModelScope.launch(Dispatchers.IO) {
//            val giftPointToFlaxConvertRequest = GiftPointToFlaxConvertRequest(action = "f2p", coins = pointsVar.value?.toDouble(), flax = flaxVar.value?.toDouble())
//
//            when (val result: ResultOf<APIResponse<GiftPointToFlaxConvertResponse>> = giftRepository.giftConversion(giftPointToFlaxConvertRequest)) {
//                is ResultOf.Success -> {
//                    successMessage.postValue(result.value.result.flax.toString())
//                    dialogDismiss.postValue(true)
//                }
//
//                is ResultOf.APIError -> {
//                    errorMessage.postValue(result.error.message)
//                    dialogDismiss.postValue(true)
//                }
//
//                is ResultOf.Error -> {
//                    // Handle other errors
//                }
//            }
//        }
//
//    }

}