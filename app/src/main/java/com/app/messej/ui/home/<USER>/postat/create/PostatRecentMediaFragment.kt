package com.app.messej.ui.home.publictab.postat.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.databinding.FragmentPostatRecentMediaBinding

class PostatRecentMediaFragment : Fragment() {

    private lateinit var binding: FragmentPostatRecentMediaBinding

    private var mMediaPickerAdapter: PostatMediaPickerAdapter? = null
    val viewModel: CreatePostatViewModel by navGraphViewModels(R.id.nav_create_postat)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_recent_media, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun setup() {
        initAdapter()
    }

    private fun observer(){
        viewModel.mediaList.observe(viewLifecycleOwner) {
            it?: return@observe
            mMediaPickerAdapter?.submitData(lifecycle, it)
        }
    }

    private fun initAdapter() {
        mMediaPickerAdapter = PostatMediaPickerAdapter(viewLifecycleOwner,object : PostatMediaPickerAdapter.PostatMediaActionListener {
            override fun onMediaSelected(view: View, media: PostatDeviceMedia) {
                viewModel.toggleSelectMedia(media)
            }
        })

        binding.mediaList.apply {
            layoutManager = GridLayoutManager(requireContext(), 4)
            setHasFixedSize(true)
            adapter = mMediaPickerAdapter
        }

//        mMediaPickerAdapter?.apply {
//            addLoadStateListener { loadState ->
//                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
//                else if (loadState.append.endOfPaginationReached) {
//                    if (itemCount < 1){
//                        MultiStateView.ViewState.EMPTY
//                    }else {
//                        MultiStateView.ViewState.CONTENT
//                    }
//                } else {
//                    MultiStateView.ViewState.CONTENT
//                }
//            }
//        }
    }

    override fun onResume() {
        super.onResume()
        mMediaPickerAdapter?.refresh()
    }

}