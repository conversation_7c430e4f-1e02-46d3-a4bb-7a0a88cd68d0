package com.app.messej.ui.auth.forgotPassword

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.auth.VerifyResponse
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ForgotPasswordViewModel(application: Application) : AndroidViewModel(application) {

    private var authRepo: AuthenticationRepository = AuthenticationRepository(application)

    val countryCode = MutableLiveData<String?>(null)

    val didEnterPhoneNumber = MutableLiveData<Boolean>(false)
    val didEnterEmail = MutableLiveData<Boolean>(false)
    // will be false even if input is empty
    private val _phoneNumberValid = MutableLiveData(false)
    private val _emailValid = MutableLiveData(false)
    private val _isMobileNextButtonVisible = MutableLiveData(false)
    private val _isEmailNextButtonVisible = MutableLiveData(false)


    private val _phoneNumberWithCountryCode = MutableLiveData<String?>(null)
    val phoneNumberWithCountryCode: LiveData<String?> = _phoneNumberWithCountryCode

    //Do not navigate on livedata events
    private val _isMobileVerified= MutableLiveData(false)
    val isMobileVerified: LiveData<Boolean?> = _isMobileVerified

    private val _isEmailVerified= MutableLiveData(false)
    val isEmailVerified: LiveData<Boolean?> = _isEmailVerified

    private val _verifyAccountLoading= MutableLiveData(false)
    val verifyAccountLoading: LiveData<Boolean?> = _verifyAccountLoading

    private val _verifyMobileError= MutableLiveData<String?>(null)
    val verifyMobileError: LiveData<String?> = _verifyMobileError

    private val _verifyEmailError= MutableLiveData<String?>(null)
    val verifyEmailError: LiveData<String?> = _verifyEmailError

    private val _unSelectedPage= MutableLiveData(1)
    val unSelectedPage: LiveData<Int?> = _unSelectedPage


    fun setPhoneNumberValid(valid: Boolean) {
        _phoneNumberValid.postValue(valid)
    }


    fun setEmailValid(valid: Boolean) {
        _emailValid.postValue(valid)
    }

    // show error if phone number is invalid after entering 3 characters
    private val _showPhoneInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(didEnterPhoneNumber) { shouldShowPhoneNumberError() }
        med.addSource(_phoneNumberValid) { shouldShowPhoneNumberError() }
        med
    }

    private val _showEmailInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(didEnterEmail) { shouldShowEmailError() }
        med.addSource(_emailValid) { shouldShowEmailError() }
        med
    }

    val showPhoneInvalidError: LiveData<Boolean> = _showPhoneInvalidError
    val showEmailInvalidError: LiveData<Boolean> = _showEmailInvalidError
    val isMobileNextButtonVisible: LiveData<Boolean> = _isMobileNextButtonVisible
    val isEmailNextButtonVisible: LiveData<Boolean> = _isEmailNextButtonVisible

    private fun shouldShowPhoneNumberError() {
        _isMobileNextButtonVisible.postValue(_phoneNumberValid.value == false)
        if (didEnterPhoneNumber.value == false) {
            _showPhoneInvalidError.postValue(false)
            return
        }
        _showPhoneInvalidError.postValue(_phoneNumberValid.value == false)
    }

    fun  setUnSelectedPage(pageNo: Int){
        _unSelectedPage.postValue(pageNo)
    }

    private fun shouldShowEmailError() {
        _isEmailNextButtonVisible.postValue(_emailValid.value == false)
        if (didEnterEmail.value == false) {
            _showEmailInvalidError.postValue(false)
            return
        }
        _showEmailInvalidError.postValue(_emailValid.value == false)
    }
    fun verifyMobileAccount(phoneNumber:String="", countryCode:String="", OTPRequestMode: OTPRequestMode) {
        _isMobileVerified.postValue(false)
        _isMobileVerified.postValue(false)
        _verifyAccountLoading.postValue(false)
        viewModelScope.launch(Dispatchers.IO) {
            when(val result: ResultOf<VerifyResponse> =authRepo.getMobileOTP(OTPRequestMode, phoneNumber, countryCode, resend = false)){
                is ResultOf.Success -> {
                  when(OTPRequestMode){
                      com.app.messej.data.model.enums.OTPRequestMode.RESET_MOBILE -> _isMobileVerified.postValue(true)
                     else->{
                          _isMobileVerified.postValue(true)
                      }
                   }
                }
               is ResultOf.APIError -> {
                   when(OTPRequestMode){
                       com.app.messej.data.model.enums.OTPRequestMode.RESET_MOBILE -> _verifyMobileError.postValue(result.error.message)
                     else->{
                      }
                  }
              }
             is ResultOf.Error -> {
                 when(OTPRequestMode){
                     com.app.messej.data.model.enums.OTPRequestMode.RESET_MOBILE -> _verifyMobileError.postValue(result.exception.message)
                     else->{
                     }
                 }
             }
              }
        }
        _verifyAccountLoading.postValue(false)
    }

    fun verifyEmailAccount(email:String="", OTPRequestMode: OTPRequestMode) {
        _verifyAccountLoading.postValue(false)
        _verifyEmailError.postValue(null)
        _verifyMobileError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            when(val result: ResultOf<VerifyResponse> =authRepo.getEmailOTP(OTPRequestMode, email,false)){
                is ResultOf.Success -> {
                    when(OTPRequestMode){
                        com.app.messej.data.model.enums.OTPRequestMode.RESET_EMAIL -> _isEmailVerified.postValue(true)
                        else->{
                            _isEmailVerified.postValue(true)
                        }
                    }
                }
                is ResultOf.APIError -> {
                    when(OTPRequestMode){
                        com.app.messej.data.model.enums.OTPRequestMode.RESET_EMAIL -> _verifyEmailError.postValue(result.error.message)
                        else->{
                        }
                    }
                }
                is ResultOf.Error -> {
                    when(OTPRequestMode){
                        com.app.messej.data.model.enums.OTPRequestMode.RESET_EMAIL -> _verifyEmailError.postValue(result.exception.message)
                        else->{
                        }
                    }
                }
            }
        }
        _verifyAccountLoading.postValue(false)
    }
}
