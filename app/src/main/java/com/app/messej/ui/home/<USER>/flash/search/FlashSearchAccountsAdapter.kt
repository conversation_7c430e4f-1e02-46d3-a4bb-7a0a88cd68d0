package com.app.messej.ui.home.publictab.flash.search

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.huddles.FlashSearchResponse.FlashUser
import com.app.messej.databinding.ItemUserWithActionStatsBinding

class FlashSearchAccountsAdapter (val listener: ActionListener): PagingDataAdapter<FlashUser, FlashSearchAccountsAdapter.FlashSearchAccountsViewHolder>(UserDiff) {
    interface ActionListener {
        fun onItemClick(item: FlashUser)
    }

    inner class FlashSearchAccountsViewHolder(private val binding: ItemUserWithActionStatsBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(item: FlashUser) = with(binding) {
            user = item
            Log.d("FSAA", "bind: ${item.name}")
            clickable = true
            showUsername = true
            root.setOnClickListener {
                listener.onItemClick(item)
            }
        }
    }

    object UserDiff : DiffUtil.ItemCallback<FlashUser>() {
        override fun areItemsTheSame(oldItem: FlashUser, newItem: FlashUser) =
            oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: FlashUser, newItem: FlashUser) =
            oldItem == newItem
    }

    override fun onBindViewHolder(holder: FlashSearchAccountsViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FlashSearchAccountsViewHolder {
        val binding = ItemUserWithActionStatsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return FlashSearchAccountsViewHolder(binding)
    }
}