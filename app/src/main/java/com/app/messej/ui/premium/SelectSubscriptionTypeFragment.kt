package com.app.messej.ui.premium

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.databinding.FragmentSelectSubscriptionTypeBinding
import com.app.messej.databinding.LayoutPodiumLiveFriendsActionBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class SelectSubscriptionTypeFragment : BottomSheetDialogFragment()  {

    private val viewModel: UpgradePremiumViewModel by activityViewModels()
    private lateinit var binding: FragmentSelectSubscriptionTypeBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_select_subscription_type, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        viewModel.flixSubscriptionDetails()
    }

    private fun setup() {
        viewModel.selectDates()
        viewModel.handleVisibility(true)

        binding.purchaseFlixButton.setOnClickListener {
            viewModel.flixSubscriptionDetails.value?.let { it ->
                if (it.subscriptionMethod == "FLiX" || it.subscriptionMethod.isNullOrEmpty()|| it.subscription_acive==false|| !viewModel.user.premium) {
                    viewModel.handleVisibility(true)
                } else {
                    showAlreadyPurchasedAlert()
                }
            }

        }

        binding.purchaseConfirmButton.setOnClickListener{
            if(viewModel.disableFlixButton.value==false && viewModel.user.premium) return@setOnClickListener
            viewModel.subscribeByFlix()
        }
    }

    private fun showAlreadyPurchasedAlert() {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumLiveFriendsActionBinding>(layoutInflater, R.layout.layout_podium_live_friends_action, null, false)
            view.textHeader = getString(R.string.Already_sub_flix_note)
            view.actionFriendUnfollow.visibility = View.GONE
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
        }
    }

    private fun observe() {

        viewModel.subscriptionError.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.isSubscribedByFlix.observe(viewLifecycleOwner){
            if(it==true){
                Toast.makeText(requireContext(), getString(R.string.title_subscription_sucess), Toast.LENGTH_SHORT).show()
                findNavController().navigateSafe(SelectSubscriptionTypeFragmentDirections.actionSelectSubscribedTypeFragmentToAlreadySubscribedFragment(true))

            }
        }
        viewModel.disableFlixButton.observe(viewLifecycleOwner){
            Log.d("Disable",""+it)
        }
    }





    override fun getTheme(): Int {
        return R.style.Widget_Flashat_Tribe_BottomSheet
    }
}