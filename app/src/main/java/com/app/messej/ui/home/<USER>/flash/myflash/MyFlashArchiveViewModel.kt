package com.app.messej.ui.home.publictab.flash.myflash

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class MyFlashArchiveViewModel(application: Application) : FlashListBaseViewModel(application) {
    override val _flashFeedList: LiveData<PagingData<FlashVideo>> = flashRepo.getArchivedFlashPager().liveData.cachedIn(viewModelScope)

    val onFlashUnarchived = LiveEvent<Boolean>()

    fun unarchiveFlash(flash: FlashVideo) {
        viewModelScope.launch(Dispatchers.IO){
            when(flashRepo.unarchiveFlash(flash.id)) {
                is ResultOf.Success -> {
                    onFlashUnarchived.postValue(true)
                }
                else -> {}
            }
        }
    }
}