package com.app.messej.ui.home.publictab.podiums.challenges

import android.content.res.AssetFileDescriptor
import android.content.res.ColorStateList
import android.media.MediaPlayer
import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.platform.ComposeView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.challenges.KnowledgeRaceData
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge.ChallengeStatus
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.LayoutPodiumChallengeBoardKnowledgeBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardKnowledgeOptionBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardKnowledgeQuestionBinding
import com.app.messej.databinding.LayoutPodiumUserEliminatedBinding
import com.app.messej.databinding.LayoutPodiumUserJoinedBinding
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeInOut
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeOutIn
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.transitionToText
import com.app.messej.ui.home.publictab.podiums.challenges.knowledgerace.KnowledgeRaceBoard
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel
import com.app.messej.ui.utils.LocaleUtil
import com.app.messej.ui.utils.ViewUtils.fillAddView
import com.google.android.material.button.MaterialButton
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.time.ZonedDateTime

class PodiumKnowledgeRaceChallengePresenter(holder: ViewGroup, challenge: PodiumChallenge, challengeListener: ChallengeEventListener, private val knowledgeRaceListener: KnowledgeRaceEventListener): PodiumChallengePresenter(holder, challenge, challengeListener)  {

    override lateinit var liveBinding: LayoutPodiumChallengeBoardKnowledgeBinding
    private lateinit var musicPlayer: MediaPlayer
    private lateinit var chimePlayer: MediaPlayer

    private var scoreState = mutableStateOf(scores)

    private var chatInputOverlay: ConstraintLayout? = null
    data class KnowledgeRaceSettings(
        var language: AppLocale,
        var music: Boolean,
        var soundEffects: Boolean,
    )

    interface KnowledgeRaceEventListener {
        fun showHowToPlayOverlay(millis: Long)
        fun submitAnswer(question: KnowledgeRaceData.Question, answer: KnowledgeRaceData.Option)
        fun showSettingsBottomSheet(knowledgeRaceSettings: KnowledgeRaceSettings)
        fun showEliminationWarning()
    }

    override fun setupView() {
        setupPlayer()
        liveBinding = LayoutPodiumChallengeBoardKnowledgeBinding.inflate(layoutInflater, holder, false)
        liveBinding.questionsHolder.isInvisible = true
    }

    override val challengeTitle: Int
        get() = R.string.podium_challenge_knowledge

    private var prevScore: PodiumChallengeScore? = null

    override fun onUpdateChallengeAndScores(cs: PodiumChallenge) {
        super.onUpdateChallengeAndScores(cs)
        scoreState.value = scores
        if (uiState== UIState.LIVE) {
            myScore?.let { ms ->
                prevScore?.let { ps ->
                    if (ps.score < ms.score) {
                        playChime()
                    }
                    if (!didWarnElimination && ps.consecutiveSkips == 0 && (ms.consecutiveSkips ?: 0) > 0) {
                        didWarnElimination = true
                        knowledgeRaceListener.showEliminationWarning()
                    }
                }
                prevScore = ms
            }
        }
        Log.w("KRB", "onNewScoresAvailable: ${scoreState.value}")
    }

    override fun onNewScoresAvailable(forceRefresh: Boolean) {
        scope.launch {
            if (challenge.hasStarted && !challenge.gameOver) {
                showQuestion()
            }
        }
    }

    override fun cleanup() {
        stopMusic()
        super.cleanup()
    }

    private fun setupPlayer() {
        musicPlayer = MediaPlayer().apply {
            val afd: AssetFileDescriptor = resources.openRawResourceFd(R.raw.knowledge_race_audio) ?: return
            setDataSource(afd)
            afd.close()
            prepare()
            isLooping = true
//            start()
        }

        chimePlayer = MediaPlayer().apply {
            val afd: AssetFileDescriptor = resources.openRawResourceFd(R.raw.knowledge_race_chime) ?: return
            setDataSource(afd)
            afd.close()
            prepare()
        }
    }

    private fun startMusic() {
        liveBinding.micVisible = true
        if (knowledgeRaceSettings.music) {
            musicPlayer.let {
                if (!it.isPlaying) {
                    it.seekTo(0)
                    it.start()
                }
            }
        }
    }

    private var knowledgeRaceSettings = KnowledgeRaceSettings(
        language = LocaleUtil.getAppLocale(),
        music = true,
        soundEffects = true
    )

    fun updateKnowledgeRaceSettings(settings: KnowledgeRaceSettings) {
        if (settings.music != knowledgeRaceSettings.music) {
            toggleMute(settings.music)
        }
        knowledgeRaceSettings = settings
    }

    private fun toggleMute(playMusic : Boolean) {
        if (playMusic) {
            musicPlayer.start()
        } else musicPlayer.pause()
    }

    private fun stopMusic() {
        liveBinding.micVisible = false
        try {
            if (musicPlayer.isPlaying) {
                musicPlayer.stop()
            }
            musicPlayer.release()
            if (chimePlayer.isPlaying) {
                chimePlayer.stop()
            }
            chimePlayer.release()

        } catch (e: Exception) { }
    }

    private fun playChime() {
        if (knowledgeRaceSettings.soundEffects) {
            chimePlayer.let {
                it.seekTo(0)
                it.start()
            }
        }
    }

    override fun onSpeakerClick(item: PodiumSpeakerUIModel.ActiveSpeakerUIModel, view: View, mainScreen: Boolean): Boolean {
        return true
    }

    override fun refreshChallengeUI(status: ChallengeStatus) {
        try {
            scope.launch {
                if (status in listOf(ChallengeStatus.GAME_OVER,ChallengeStatus.ENDED) && uiState==UIState.LIVE) {
                    Log.w("PCPKR", "refreshChallengeUI: waiting for winner animation to finish")
                    if (status == ChallengeStatus.GAME_OVER) {
                        holdWinnerUI()
                    }
                    winnerAnimationJob?.join()
                    Log.w("PCPKR", "refreshChallengeUI: winner animation finished")
                    hideOverlays()
                }
            }
        } catch (_: Exception) {}
        super.refreshChallengeUI(status)
    }

    private var didWarnElimination = false

    override fun refreshLiveUI() {
        Log.w("PCPKR", "refreshLiveUI: $challenge")
        scope.launch {
            Log.w("PCPKR", "refreshLiveUI: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}")

            liveBinding.apply {
                timerHolder.visibility = View.INVISIBLE
                title.text = resources.getString(challengeTitle)
                content.isVisible = false
                questionCount.text = ""
                settingsButton.setOnClickListener {
                    knowledgeRaceListener.showSettingsBottomSheet(knowledgeRaceSettings)
                }
            }

            challengeListener.getLiveViewModel().apply {
                if (iAmMuted.value==false) {
                    muteToggleSelf()
                }
            }

            challengeListener.showSpeakerOverlay(true,0f)
            challengeListener.showChatOverlay(true).apply {
                ComposeView(this.context).apply {
                    id = View.generateViewId()
                    setContent {
                        KnowledgeRaceBoard(scoreState)
                    }
                    fillAddView(this)
                }
            }
            challengeListener.showChatInputOverlay(true).apply {
                this.alpha = 0f
                chatInputOverlay = this
            }
            startMusic()
            Log.w("PCPKR", "refreshLiveUI ${challenge.hasStarted} ${challenge.gameOver}" )
            if (challenge.hasStarted) {
                if (!challenge.gameOver) {
                    showQuestion()
                } else {
                    stopMusic()
                    finalize()
                }
            } else {
                liveBinding.content.isVisible = true
                liveBinding.content.showStartCountdown(
                    {
                        if (challengeListener.getLiveViewModel().iAmPartOfRunningChallenge.value == true) {
                            knowledgeRaceListener.showHowToPlayOverlay(challenge.countDownRemaining?.toMillis() ?: 0)
                        }
                    },
                    {
                        refreshLiveUI()
                    }
                )
            }
        }
    }

    private suspend fun showEmpty() {
        liveBinding.questionsHolder.isInvisible = true
        liveBinding.content.transitionToText(resources.getString(R.string.podium_challenge_knowledge_preparing))
    }

    private suspend fun finalize() {
        liveBinding.questionsHolder.isInvisible = true
        liveBinding.content.transitionToText(resources.getString(R.string.podium_challenge_finalizing))
    }

    override fun showFinalScores(): Boolean  = false

    private var lastQuestion: KnowledgeRaceData.Question? = null
    private var lastQuestionBinding: LayoutPodiumChallengeBoardKnowledgeQuestionBinding? = null
    private var lastEndTime: ZonedDateTime? = null

    private suspend fun showQuestion() {
        val data = challenge.knowledgeRaceData ?: return
        val question = data.currentQuestion?:lastQuestion
        val endTime = data.parsedTurnEndTime
        if (question == lastQuestion && endTime == lastEndTime) {
            Log.d("PCPKR","showQuestion: trying to show same question. Abort")
            return
        }
        val questionHasExpired = endTime != null && ZonedDateTime.now().isAfter(endTime)
        Log.w("PCPKR","showQuestion: $question, endTime: ${endTime?.let { DateTimeUtils.getLocalDateTime(it)}}, questionHasExpired: $questionHasExpired")
        if (question == null) {
            showEmpty()
            return
        }

        Log.w("PCPKR", "showQuestion: end time - $endTime, question - $question")

        liveBinding.timerHolder.visibility = View.VISIBLE

        fun LayoutPodiumChallengeBoardKnowledgeQuestionBinding.lockQuestion() {
            Log.w("PCPKR", "showQuestion: question locked")
            canSelect = false
            showResult = question.correctAnswerOptionId
        }

        if (lastQuestion != question) {

            val b = LayoutPodiumChallengeBoardKnowledgeQuestionBinding.inflate(layoutInflater, liveBinding.questionsHolder, false)
            lastQuestionBinding = b

            fun LayoutPodiumChallengeBoardKnowledgeOptionBinding.bindOption(opt: KnowledgeRaceData.Option?) {
                if (opt==null) {
                    button.isVisible = false
                    return
                }
                button.text = opt.localeTitle(knowledgeRaceSettings.language)
                button.setOnClickListener {
                    b.selectedId = opt.id
                    knowledgeRaceListener.submitAnswer(question, opt)
                }
            }

            b.questionTitle.text = question.localeTitle(knowledgeRaceSettings.language)

            b.optionOne.bindOption(question.options?.getOrNull(0))
            b.optionTwo.bindOption(question.options?.getOrNull(1))
            b.optionThree.bindOption(question.options?.getOrNull(2))
            b.optionFour.bindOption(question.options?.getOrNull(3))
            scope.launch {
                Log.w("PCPKR", "showQuestion: $lastQuestion | $question")
                val currentView: View = if(lastQuestion==null) liveBinding.content else liveBinding.questionsHolder
                currentView.fadeOutIn(liveBinding.questionsHolder) { outV, inV ->
                    Log.w("PCPKR", "showQuestion: fadeOutIn: updating views")
                    liveBinding.questionsHolder.removeAllViews()
                    liveBinding.questionsHolder.addView(b.root)
                    outV.isVisible = false
                    inV.isVisible = true
                }
            }
            liveBinding.questionCount.text = resources.getString(R.string.podium_knowledge_race_question_count,question.questionNumber.toString())
        }
        if (endTime!=null) {
            tickerJob?.cancel()
            Log.d("PCP", "tickerJob canceled in: KR showQuestion")
            DateTimeUtils.durationFromNowToFuture(endTime)?.toMillis()?.let { timeLeft ->
                liveBinding.timer.showChallengeTimer(remainingMs = timeLeft, 5) {
                    lastQuestionBinding?.lockQuestion()
                }
            }
        } else {
            liveBinding.timer.apply {
                setTextColor(ContextCompat.getColor(context, R.color.textColorAlwaysDarkHint))
                text = DateTimeUtils.formatSeconds(0)
            }
        }

        lastQuestionBinding?.let { b ->
            b.question = question
            b.selectedId = myScore?.knowledgeAnswerId
            b.canSelect = iAmParticipant && !questionHasExpired
        }

        lastQuestion = question
        lastEndTime = endTime
    }

    override suspend fun showResultUI() {
        stopMusic()
        super.showResultUI()
    }

    override fun onUserJoined(user: PodiumParticipant) {
        Log.w("PCPKR","onUserJoined: $user | $chatInputOverlay")
        val holder = chatInputOverlay ?: return
        val layoutBinding = LayoutPodiumUserJoinedBinding.inflate(layoutInflater, holder, false)
        layoutBinding.user = user
        layoutBinding.root.id = View.generateViewId()
        holder.fillAddView(layoutBinding.root)
        holder.alpha = 0f
        scope.launch {
            holder.fadeInOut {
                Log.w("PCPKR","onUserJoined: fadeIn")
                delay(5000)
            }
        }
    }

    fun onUserEliminated(users: List<PodiumChallengeScore>) {
        Log.w("PCPKR","onUserEliminated: $users | $chatInputOverlay")
        val holder = chatInputOverlay ?: return

        scope.launch {
            users.forEach { user ->
                val layoutBinding = LayoutPodiumUserEliminatedBinding.inflate(layoutInflater, holder, false)
                layoutBinding.user = user
                layoutBinding.root.id = View.generateViewId()
                holder.fillAddView(layoutBinding.root)
                holder.alpha = 0f
                holder.fadeInOut {
                    Log.w("PCPKR", "onUserJoined: fadeIn")
                    delay(5000)
                }
            }
        }
    }

    var winnerAnimationJob: Job? = null

    private fun holdWinnerUI() {
        winnerAnimationJob?.cancel()
        winnerAnimationJob = scope.launch {
            delay(5000)
        }.apply {
            invokeOnCompletion {
                winnerAnimationJob = null
            }
        }
    }

    override fun getExtraTextForWinnerCard(winner: PodiumChallengeScore): String {
        return "${winner.score.toInt()}/${lastQuestion?.questionNumber?:10}"
    }

    override fun getExtraTextForWinnerTicker(winners: List<PodiumChallengeScore>): String {
        return "${winners[0].score.toInt()}/${lastQuestion?.questionNumber?:10}"
    }

    override fun convertScore(sc: PodiumChallengeScore): String {
        return "${sc.score.toInt()}/${lastQuestion?.questionNumber?:10}"
    }

    companion object {
        @JvmStatic
        @BindingAdapter("thisAnswer","selectedAnswer", "correctAnswer")
        fun setOptionTint(view: MaterialButton, option: Int?, selected: Int?, correctAnswer: Int?) {
            Log.w("PCPKR", "setOptionTint: text: ${view.text} selected: $selected, correct: $correctAnswer")
            val tint = if (option!=null && option==selected) {
                when (correctAnswer) {
                    option -> R.color.colorPass
                    null -> R.color.colorPrimary
                    else -> R.color.colorError
                }
            } else R.color.transparent
            view.backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(view.context,tint))
        }
    }
}