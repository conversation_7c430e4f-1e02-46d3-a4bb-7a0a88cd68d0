package com.app.messej.ui.home.businesstab.operations.tasks.bottomsheet

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.LayoutConfirmCancelBinding
import com.app.messej.databinding.LayoutHomeaddressBottomsheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class HomeAddressBottomSheet : BottomSheetDialogFragment() {
    private val viewModel: HomeAddressViewModel by viewModels()
    private val addressArgs: HomeAddressBottomSheetArgs by navArgs()
    private lateinit var binding: LayoutHomeaddressBottomsheetBinding


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_homeaddress_bottomsheet, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = false
        setUp()
        observe()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState)
        this.dialog!!.setCancelable(false)
    }


    @SuppressLint("SetTextI18n")
    private fun setUp() {

        viewModel.setMode(addressArgs.isEdit, binding.phoneCountryPicker.selectedCountryCode)

        binding.textInputName.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    viewModel.clearNameError()
                }
            }
        }
        binding.textInputZipcodePo.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    viewModel.clearZipCodeError()
                }
            }
        }
            binding.textInputAddress1.editText?.apply {
                setOnFocusChangeListener { _, hasFocus ->
                    if (hasFocus) {
                        viewModel.clearAddressOneError()
                    }
                }
            }

        binding.textInputContactNumber.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    viewModel.clearPhoneNumberValidError()
                }
            }
        }
        binding.textInputCity.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    viewModel.clearCityValidError()
                }
            }
        }

/**Country picker*/
        binding.addressCountryPicker.apply {
            setOnCountryChangeListener {
                viewModel.setCountryName(selectedCountryNameCode)
            }
            viewModel.setCountryName(selectedCountryNameCode)
        }
/**phone country code*/
        binding.phoneCountryPicker.apply {
            setOnCountryChangeListener {
                viewModel.setCountryCode(selectedCountryCode)
            }
            viewModel.setCountryCode(selectedCountryCode)
        }
        binding.taskProfSave.setOnClickListener {
            viewModel.saveHomeAddress()
        }
        binding.actionCancel.setOnClickListener {
          if (viewModel.isDataAvailable()){
              MaterialDialog(requireContext()).show {
                  val view = DataBindingUtil.inflate<LayoutConfirmCancelBinding>(layoutInflater, R.layout.layout_confirm_cancel, null, false)
                  view.viewModel = viewModel
                  customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                  cancelable(false)
                  view.nickNameTitle.text=resources.getString(R.string.sure_you_want_to_cancel)
                  view.actionConfirm.setOnClickListener {
                      dismiss()
                      <EMAIL>()
                  }
                  view.actionCancel.setOnClickListener {
                      dismiss()
                  }
              }
          }else{
              dismiss()
              <EMAIL>()
          }



        }


    }

    private fun observe() {

        viewModel.nameValid.observe(viewLifecycleOwner) {
            it?.let {
                if (!it) {
                    binding.textInputName.isErrorEnabled = true
                    binding.textInputName.error = resources.getString(R.string.this_field_is_required)
                } else {
                    binding.textInputName.isErrorEnabled = false
                }
            }
        }

        viewModel.addressValid.observe(viewLifecycleOwner) {
            it?.let {
                if (!it) {
                    binding.textInputAddress1.isErrorEnabled = true
                    binding.textInputAddress1.error = resources.getString(R.string.this_field_is_required)
                } else {
                    binding.textInputAddress1.isErrorEnabled = false
                }
            }
        }

        viewModel.zipCodeValid.observe(viewLifecycleOwner) {
            it?.let {
                if (!it) {
                    binding.textInputZipcodePo.isErrorEnabled = true
                    binding.textInputZipcodePo.error = resources.getString(R.string.this_field_is_required)
                } else {
                    binding.textInputZipcodePo.isErrorEnabled = false
                }
            }
        }

        viewModel.phoneNumberValid.observe(viewLifecycleOwner) {
            it?.let {
                if (!it) {
                    binding.textInputContactNumber.isErrorEnabled = true
                    binding.textInputContactNumber.error = resources.getString(R.string.this_field_is_required)
                } else {
                    binding.textInputContactNumber.isErrorEnabled = false
                }
            }
        }

        viewModel.cityValid.observe(viewLifecycleOwner) {
            it?.let {
                if (!it) {
                    binding.textInputCity.isErrorEnabled = true
                    binding.textInputCity.error = resources.getString(R.string.this_field_is_required)
                } else {
                    binding.textInputCity.isErrorEnabled = false
                }
            }
        }


        viewModel.countryName.observe(viewLifecycleOwner) {
            it?.let{
                binding.addressCountryPicker.setCountryForNameCode(it)
            }
        }

        viewModel.countryCode.observe(viewLifecycleOwner) {
            if (it!=null) {
                binding.phoneCountryPicker.setCountryForPhoneCode(UserInfoUtil.removePlusFromCountryCode(it)!!.toInt())

            }
        }
        viewModel.homeAddressStageValid.observe(viewLifecycleOwner){

        }
        viewModel.addressCompleted.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), getString(R.string.home_address_success_toast), Toast.LENGTH_SHORT).show()
            findNavController().popBackStack()
        }
    }
}



