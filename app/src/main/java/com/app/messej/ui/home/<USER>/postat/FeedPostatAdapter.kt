package com.app.messej.ui.home.publictab.postat

import android.text.format.DateUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.annotation.OptIn
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.get
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.data.model.entity.Postat
import com.app.messej.databinding.ItemPostatPostBinding
import com.app.messej.ui.home.publictab.postat.create.FeedPostatViewerViewPagerAdapter
import java.lang.ref.WeakReference

class FeedPostatAdapter(private val listener: FeedPostatActionListener): PagingDataAdapter<FeedPostatAdapter.PostatUIModel, FeedPostatAdapter.FeedPostatViewHolder>(FeedPostatDiff) {

    interface FeedPostatActionListener {
        fun onCommentsClicked(postatId: String, userId: Int)
        fun onPostClicked(postat: Postat, view: View,position: Int)
        fun onGiftClicked(postat: Postat)
        fun onClickUser(pos: Int, item: Postat)
        @OptIn(UnstableApi::class)
        fun getMediaSource(media: MediaItem): MediaSource?

        fun onItemPageChanged(pos: Int)

        fun onLivePodiumIndicatorClicked(postat: Postat)

        fun isSelf(item: Postat):Boolean
    }

    data class PostatUIModel(val postat : Postat) {
        @DrawableRes
        var countryFlag: Int? = null
    }

    companion object {
        const val POSTAT_ASPECT_DEFAULT = 1.0f
        const val POSTAT_ASPECT_MIN = 4/5f
        const val POSTAT_ASPECT_MAX = 21/9f
    }

    inner class FeedPostatViewHolder(private val binding: ItemPostatPostBinding): RecyclerView.ViewHolder(binding.root) {

        private var futurePlaybackObject: WeakReference<FeedPostatViewerViewPagerAdapter.FuturePlaybackObject>? = null

        private var currentPlaybackObject: WeakReference<FeedPostatViewerViewPagerAdapter.FuturePlaybackObject>? = null

        private var boundItem: Postat? = null

        fun bind(item: PostatUIModel) = with(binding) {
            boundItem = item.postat
            postat = item.postat
            isSelf = listener.isSelf(item.postat)
            commentFlag.setImageResource(item.countryFlag ?: 0)
            btnMore.setOnClickListener {
                listener.onPostClicked(postat!!, it,absoluteAdapterPosition)
            }

            val firstAspect = item.postat.media.firstOrNull()?.resolution?.aspectRatio?:POSTAT_ASPECT_DEFAULT

            val coercedAspect = firstAspect.coerceIn(POSTAT_ASPECT_MIN, POSTAT_ASPECT_MAX)
            (mediaHolder.layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = (coercedAspect).toString()

            val adapter = FeedPostatViewerViewPagerAdapter(object : FeedPostatViewerViewPagerAdapter.PlayerActionListener {
                override fun registerForFuturePlayback(obj: FeedPostatViewerViewPagerAdapter.FuturePlaybackObject) {
                    futurePlaybackObject = WeakReference(obj)
                    Log.w("POSTATF", "registerForFuturePlayback: $bindingAdapterPosition | ${obj.pos}")
                }

                override fun detachFuturePlayback(pos: Int) {
                    if (futurePlaybackObject?.get()?.pos == pos) {
                        Log.w("POSTATF", "detachFuturePlayback: $bindingAdapterPosition | $pos")
                        futurePlaybackObject = null
                    }
                }
            }, item.postat.media.toMutableList())

            mediaViewPager.registerOnPageChangeCallback(object: ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    listener.onItemPageChanged(bindingAdapterPosition)
                }
            })
            mediaViewPager.adapter = adapter
            dotsIndicator.attachTo(mediaViewPager)

            commentCountHolder.setOnClickListener {
                listener.onCommentsClicked(item.postat.messageId,item.postat.userId)
            }
            item.postat.parsedCreatedTime?.let { ago ->
                postTime.text = DateUtils.getRelativeTimeSpanString(ago.toInstant().toEpochMilli())
            }
            giftHolder.setOnClickListener {
                listener.onGiftClicked(item.postat)
            }
            ivUserDp.setOnClickListener {
                if(item.postat.senderDetails.userLivePodium && item.postat.senderDetails.userLivePodiumId!=null && !listener.isSelf(item.postat)){
                    listener.onLivePodiumIndicatorClicked(item.postat)
                }
                else listener.onClickUser(bindingAdapterPosition,item.postat)
            }
            commentorName.setOnClickListener {
               listener.onClickUser(bindingAdapterPosition,item.postat)
            }
        }

        fun playMediaIfReady(videoPlayer: ExoPlayer, audioPlayer: ExoPlayer, resetAudio: Boolean) {
            fun play(fpo: FeedPostatViewerViewPagerAdapter.FuturePlaybackObject) {
                currentPlaybackObject = WeakReference(fpo)
                videoPlayer.setupWithMedia(fpo.postat.mediaUrl, boundItem?.isOriginalAudio==true)
                fpo.onPlay.invoke(videoPlayer)
            }
            currentPlaybackObject?.get()?.onStop?.invoke()
            currentPlaybackObject = null
            if (videoPlayer.isPlaying) {
                videoPlayer.stop()
            }
            futurePlaybackObject?.get()?.also { fpo ->
                Log.w("POSTATF", "playMediaIfReady: found FPO: ${fpo.pos}")
                play(fpo)
            }?: run {
//                Log.w("POSTATF", "playMediaIfReady: trying to find current FPO")
                val vh = (binding.mediaViewPager[0] as RecyclerView).findViewHolderForAdapterPosition(binding.mediaViewPager.currentItem)
//                Log.w("POSTATF", "playMediaIfReady: found ViewHolder for pos ${binding.mediaViewPager.currentItem}")
                val fpo = (vh as? FeedPostatViewerViewPagerAdapter.PostatMediaPagerViewHolder)?.bindPlayer()?: return@run
                Log.w("POSTATF", "playMediaIfReady: found current FPO | ${fpo.pos}")
                play(fpo)
            }
            if (resetAudio) {
                Log.d("POSTATF", "playMediaIfReady: bound item: $boundItem")
                boundItem?.let { bi ->
                    if (bi.hasMusic) {
                        Log.d("POSTATF", "playMediaIfReady: playing music: ${bi.musicData}")
                        audioPlayer.setupWithMedia(bi.musicData?.mediaUrl)
                    }
                }
            }
        }

        fun stopMediaPlayback() {
            futurePlaybackObject?.get()?.let { fpo ->
                Log.w("POSTATF", "stopMediaPlayback: found FPO: ${fpo.pos}")
                fpo.onStop.invoke()
            }
        }

        @OptIn(UnstableApi::class)
        fun ExoPlayer.setupWithMedia(url: String?, playSound: Boolean = true) {
            Log.w("POSTATF", "setupPlayerWithMedia: $url")
            clearMediaItems()
            url?: return
            val mediaSource = listener.getMediaSource(MediaItem.fromUri(url))?: return
            setMediaSource(mediaSource)
            volume = if (playSound) 1f else 0f
            prepare()
            play()
        }
    }

    object FeedPostatDiff : DiffUtil.ItemCallback<PostatUIModel>(){
        override fun areItemsTheSame(oldItem: PostatUIModel, newItem: PostatUIModel): Boolean {
            return oldItem.postat.messageId == newItem.postat.messageId
        }
        override fun areContentsTheSame(oldItem: PostatUIModel, newItem: PostatUIModel): Boolean {
            return oldItem == newItem
        }
    }

    override fun onBindViewHolder(holder: FeedPostatAdapter.FeedPostatViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FeedPostatAdapter.FeedPostatViewHolder {
        val binding = ItemPostatPostBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return FeedPostatViewHolder(binding)
    }
}