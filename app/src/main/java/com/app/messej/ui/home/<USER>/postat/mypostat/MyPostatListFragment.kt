package com.app.messej.ui.home.publictab.postat.mypostat

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import com.afollestad.materialdialogs.MaterialDialog
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.postat.PostatRuleLimitResponse
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentPostatMineBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePostatPostingAllowed
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.isTabletScreen
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder


class MyPostatListFragment : Fragment() {

    private var mAdapter: MyPostatListAdapter? = null

    private val viewModel: MyPostatViewModel by activityViewModels()

    private lateinit var binding: FragmentPostatMineBinding
    private var dialogue: MaterialDialog?=null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_mine, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onResume() {
        super.onResume()
        mAdapter?.refresh()
    }

    private fun setup() {
        initAdapter()
    }

    private fun observe() {
        viewModel.myPostatList.observe(viewLifecycleOwner) { pagingData ->
            Log.d("MyPostat", "observe pagingData: $pagingData") // Log before mapping
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
            }
        }

        viewModel.postatLimitData.observe(viewLifecycleOwner){
            if(it.eligible){
                if (viewModel.postatMessageId == null) addPostat()
                else findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePostat(viewModel.postatMessageId))
            }else {
                showLimitAlert(it)
            }
        }
    }

    private fun showLimitAlert(response: PostatRuleLimitResponse) {
        showFlashatDialog {
            setMessage(
                if (response.citizenship.isFreeType) {
                    getString(R.string.postat_rule_limit_free_users, response.roleLimit.toString())
                } else if (response.citizenship == UserCitizenship.GOLDEN) {
                    getString(R.string.postat_rule_limit_golden_users,response.roleLimit.toString())
                } else {
                    getString(R.string.postat_rule_limit_premium_users, response.roleLimit.toString())
                }
            )
            setConfirmButton(R.string.common_upgrade, R.drawable.ic_promo_upgrade, true) {
                findNavController().navigateSafe(HomePublicFragmentDirections.actionGlobalUpgradePremiumFragment())
                true
            }
            setConfirmButtonVisible(visible = response.showUpgradeButton)
        }
    }

    private fun initAdapter() {
        Log.d("PostatFragment", "initAdapter: create new adapter")
        mAdapter = MyPostatListAdapter(object : MyPostatListAdapter.PostatClickListener {
            override fun onMyPostatClicked(pos: Int, postat: Postat) {
                if (postat.type == Postat.PostatType.DRAFT) {
                    ensurePostatPostingAllowed {
                        viewModel.checkPostatCreationLimit(postatId = postat.messageId)
                    }
                } else {
                    findNavController().navigateSafe(HomePublicFragmentDirections.actionGlobalMyPostatFeedFragment())
                }
            }

            override fun onAddPostatClicked() {
                viewModel.checkPostatCreationLimit()
            }

            override fun onUploadPostatClicked() {
                viewModel.startPostatSync()
            }
        })

        binding.postatList.apply {
            layoutManager = GridLayoutManager(requireContext(), if(isTabletScreen) 5 else 3)
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
        }
    }

    private fun addPostat() {
        ensurePostatPostingAllowed {
            if (viewModel.isUserBlockedFromPostat) {
                MaterialAlertDialogBuilder(requireContext())
                    .setMessage(R.string.postat_post_violation_message)
                    .setPositiveButton(R.string.common_close) { dialog, _ ->
                        dialog.dismiss()
                    }.show()
                return@ensurePostatPostingAllowed
            }
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePostat(null))
        }
    }
}
