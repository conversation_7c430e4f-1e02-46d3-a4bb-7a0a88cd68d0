package com.app.messej.ui.home.publictab.huddles.comments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.app.messej.R
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.ItemChatMessageHuddleBinding
import com.app.messej.databinding.ItemHuddlePostCommentBinding
import com.app.messej.databinding.ItemHuddlePostCommentEmptyBinding
import com.app.messej.databinding.ItemHuddlePostCommentHeaderBinding
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.home.publictab.huddles.chat.HuddleChatAdapter
import com.app.messej.ui.home.publictab.huddles.chat.HuddleMessageViewHolder

class HuddlePostCommentsAdapter(
    private val inflater: LayoutInflater,
    private val userId: Int,
    private var mListener: ChatCommentListener,
    private var userCitizenship : UserCitizenship?
    ): HuddleChatAdapter(inflater, userId, mListener,userCitizenship) {

    companion object {
        const val ITEM_POST_COMMENT_HEADER = 30
        const val ITEM_POST_COMMENT = 31
        const val ITEM_POST_COMMENT_EDS = 32
    }

    interface ChatCommentListener: HuddleChatClickListener {
        fun onCommentOptionsClick(msg: PostCommentItem, position: Int, view: View)
        fun onClickUser(msg: PostCommentItem)
    }

    override fun getItemViewType(position: Int): Int {
        return when(peek(position)) {
            is ChatMessageUIModel.PostCommentHeaderModel -> ITEM_POST_COMMENT_HEADER
            is ChatMessageUIModel.PostCommentModel -> ITEM_POST_COMMENT
            is ChatMessageUIModel.PostCommentEmptyModel -> ITEM_POST_COMMENT_EDS
            else -> super.getItemViewType(position)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChatViewHolder {
        return if(viewType == ITEM_POST_COMMENT_HEADER) {
            HuddlePostCommentHeaderViewHolder(ItemHuddlePostCommentHeaderBinding.inflate(inflater, parent, false))
        } else if(viewType == ITEM_POST_COMMENT) {
            HuddlePostCommentViewHolder(ItemHuddlePostCommentBinding.inflate(inflater, parent, false), mListener)
        } else if(viewType == ITEM_POST_COMMENT_EDS) {
            HuddlePostCommentEmptyViewHolder(ItemHuddlePostCommentEmptyBinding.inflate(inflater, parent, false))
        } else if(isOfMessageTypeButNotDeleted(viewType)) {
            HuddleMessageInCommentsViewHolder(ItemChatMessageHuddleBinding.inflate(inflater, parent, false), userId, mListener,userCitizenship)
        }
        else super.onCreateViewHolder(parent, viewType)
    }

    class HuddleMessageInCommentsViewHolder(binding: ItemChatMessageHuddleBinding, userId: Int, private var mListener: ChatCommentListener,private var userCitizenship : UserCitizenship?): HuddleMessageViewHolder(binding,userId, mListener,userCitizenship) {}

    class HuddlePostCommentViewHolder(val binding: ItemHuddlePostCommentBinding, private var mListener: ChatCommentListener) : ChatViewHolder(binding.root) {

        override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
            val dataModel = (item as ChatMessageUIModel.PostCommentModel)
            comment = dataModel.comment as PostCommentItem
            chatFlag.setImageResource(dataModel.countryFlag?:0)
            commentActions.setOnClickListener {
                mListener.onCommentOptionsClick(dataModel.comment, layoutPosition, commentActions)
            }
            userDp.setOnClickListener {
                mListener.onClickUser(dataModel.comment)
            }
        }
    }

    class HuddlePostCommentHeaderViewHolder(val binding: ItemHuddlePostCommentHeaderBinding) : ChatViewHolder(binding.root) {
        override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
            val dataModel = (item as ChatMessageUIModel.PostCommentHeaderModel)
            commentHeader.text = root.resources.getString(R.string.chat_huddle_message_comment_count_label, dataModel.count)

        }
    }

    class HuddlePostCommentEmptyViewHolder(val binding: ItemHuddlePostCommentEmptyBinding) : ChatViewHolder(binding.root) {
        override fun bind(item: ChatMessageUIModel): Unit = with(binding) { }
    }
}