package com.app.messej.ui.home.gift

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.enums.FlixPurchaseTypesTab
import com.app.messej.data.model.enums.PurchaseItem
import com.app.messej.data.repository.BusinessRepository

class FlaxPurchaseHistoryViewModel(application: Application) : AndroidViewModel(application) {
    val businessRepo = BusinessRepository(application)

    // Purchase Item LiveData with initial value
    val purchaseItem = MutableLiveData<PurchaseItem>(PurchaseItem.BUY_COIN)

    // Current tab LiveData with initial value
    private val _currentTab = MutableLiveData<FlixPurchaseTypesTab?>(FlixPurchaseTypesTab.TAB_OWN)
    val currentTab: LiveData<FlixPurchaseTypesTab?> = _currentTab

    // Combined LiveData for observing purchaseItem and currentTab
    private val combinedLiveData = MediatorLiveData<Pair<PurchaseItem, FlixPurchaseTypesTab?>>().apply {
        addSource(purchaseItem) { type ->
            value = type to currentTab.value
        }
        addSource(currentTab) { tab ->
                value = purchaseItem.value!! to tab
        }
    }

    /** Get Purchase History For COIN and FLIX */
    val buyFlaxHistory = combinedLiveData.switchMap { (type, tab) ->
        Log.d("PurchaseITEM", "list : $type with tab: $tab")
        val flixPurchaseMode = if (tab == FlixPurchaseTypesTab.TAB_OWN) FlixPurchaseTypesTab.TAB_OWN else FlixPurchaseTypesTab.TAB_OTHERS
        updateApiCall(type, flixPurchaseMode)
    }

    /** API Call Logic */
    private fun updateApiCall(
        type: @JvmSuppressWildcards PurchaseItem,
        flixPurchaseMode: FlixPurchaseTypesTab?,
    ) = businessRepo.getDealsBuyFlaxPurchaseHistoryPager(type, flixPurchaseMode)
        .liveData
        .cachedIn(viewModelScope)

    /** Set Current Tab */
    fun setCurrentTab(tab: FlixPurchaseTypesTab?, skipIfSet: Boolean = false) {
        Log.w("TABTRANSACTION", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value != null) return
        _currentTab.value = tab
    }

    /** Set Data Method */
    fun setData(flixPurchaseTypesTab: FlixPurchaseTypesTab) {
        // Update purchaseItem explicitly
        purchaseItem.value?.let { type ->
                updateApiCall(type, flixPurchaseTypesTab)
        }
    }
}