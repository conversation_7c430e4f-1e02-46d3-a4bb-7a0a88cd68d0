package com.app.messej.ui.auth.biometric

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentBiometricBinding
import java.util.concurrent.Executor

class BiometricFragment : Fragment() {
    private var callback: BioMetricCallBack?=null
    private var binding: FragmentBiometricBinding? = null
    val viewModel: BiometricViewModel by viewModels()
    private var executor: Executor? = null
    private var biometricPrompt: BiometricPrompt? = null
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_biometric, container, false)
        binding?.lifecycleOwner = viewLifecycleOwner
        binding?.viewModel = viewModel
        return binding?.root
    }

    override fun onStart() {
        super.onStart()
        setUp()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        // This makes sure that the container activity has implemented
        // the callback interface. If not, it throws an exception
        try {
            callback = context as BioMetricCallBack
        } catch (e: ClassCastException) {
            throw ClassCastException(
                "$context must implement BioMetricCallBack"
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.bioMetricButton?.setOnClickListener {
            viewModel.showBiometricPrompt()
        }
        observe()
    }

    private fun observe() {
        viewModel.biometricEnabled.observe(viewLifecycleOwner) { isEnabled ->
            isEnabled?.let {
                if (it) {
                    val promptInfo = BiometricPrompt.PromptInfo.Builder().setTitle(getString(R.string.title_biometric_title)).setSubtitle(getString(R.string.sub_title_biometric))
                        .setNegativeButtonText(getString(R.string.common_cancel)).setConfirmationRequired(true).build()
                    try {
                        biometricPrompt?.authenticate(promptInfo)
                    } catch (e: IllegalStateException) {
                        Handler(Looper.getMainLooper()).postDelayed({ biometricPrompt?.authenticate(promptInfo) }, 100)
                    }
                } else {
                   callback?.noBioMetric()
                }
            }
        }
        viewModel.biometricSuccess.observe(viewLifecycleOwner) { isSuccess ->
            isSuccess?.let {
                if (it) {
                   callback?.onBiometricSuccess()
                } else {
                    callback?.onBiometricFailure()
                    binding?.bioMetricSubTitle?.text = "Error While Authenticating"
                }
            }

        }
    }

    private fun setUp() {
        executor = ContextCompat.getMainExecutor(requireContext())
        biometricPrompt = BiometricPrompt(requireActivity(), executor!!, object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                viewModel.failedMassage(errString.toString())
            }

            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                viewModel.setAuthenticationSuccess(true)
            }

            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                biometricPrompt?.cancelAuthentication()
                viewModel.setAuthenticationFailed(false)
            }
        })

    }

    interface BioMetricCallBack {
        fun onBiometricSuccess()
        fun onBiometricFailure()
        fun noBioMetric()
    }

}