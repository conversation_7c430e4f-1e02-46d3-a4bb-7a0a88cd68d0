package com.app.messej.ui.auth.onboarding

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.repository.datastore.FlashatDatastore
import kotlinx.coroutines.launch

class OnboardViewModel(application: Application) : AndroidViewModel(application) {

    private val datastoreRepo: FlashatDatastore = FlashatDatastore()

    private val _onboardingCompleted = MutableLiveData<Boolean>(false)
    val onboardingCompleted: LiveData<Boolean> = _onboardingCompleted

    init {
        viewModelScope.launch {
            _onboardingCompleted.postValue(datastoreRepo.onBoardingShown())
        }
    }

    fun finishOnboarding() {
        viewModelScope.launch {
            datastoreRepo.setOnboardingShown(true)
            _onboardingCompleted.postValue(true)
        }
    }
}