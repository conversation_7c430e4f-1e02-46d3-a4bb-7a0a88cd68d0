
package com.app.messej.ui.home.publictab.authorities.legalAffairs.violations

import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowColumn
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.messej.R
import com.app.messej.data.model.enums.LegalAffairStatisticsAppealedEnumItems

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun CustomChartView(
    items: List<AppealedItem>?
) {

    if (items.isNullOrEmpty()) {
        Box(modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_empty_graph_view),
                  contentDescription = null
            )
        }
        return
    }

    val maxItemInTheList = items.maxOf { it.count ?: 0 }

    Column(modifier = Modifier.fillMaxWidth()) {
        Row(modifier = Modifier
            .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
            .padding(bottom = dimensionResource(id = R.dimen.activity_margin))
            .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Bottom
        ) {
            repeat(times = items.size) {
                val chartData = items[it]
                BarChartSingleItemWithText(
                    count = chartData.count,
                    percentage = ((chartData.count ?: 0).toFloat() / maxItemInTheList.toFloat()),
                    barColor = getGraphColor(type = chartData.type)
                )
            }
        }

        FlowColumn(
            modifier = Modifier.fillMaxWidth().padding(horizontal = dimensionResource(id = R.dimen.activity_margin)),
            maxItemsInEachColumn = 3,
            verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.activity_margin))
        ) {
            repeat(items.size) {
                val chartData = items[it]
                ChartLabelSingleItemView(
                    modifier = Modifier.fillMaxWidth(fraction = 0.5F),
                    labelColor = getGraphColor(type = chartData.type),
                    labelText = getGraphLabelName(type = chartData.type)
                )
            }
        }
    }
}

private fun getGraphColor(type:LegalAffairStatisticsAppealedEnumItems) : Color = when(type) {
    LegalAffairStatisticsAppealedEnumItems.PayableFines -> ChartColorPayableFine
    LegalAffairStatisticsAppealedEnumItems.AppointingAdvocates -> ChartColorAppointingAdvocates
    LegalAffairStatisticsAppealedEnumItems.InJury -> ChartColorInJury
    LegalAffairStatisticsAppealedEnumItems.ConfirmedGuilty -> ChartColorConfirmedGuilty
    LegalAffairStatisticsAppealedEnumItems.FoundNotGuilty -> ChartColorFoundNotGuilty
}

private fun getGraphLabelName(type: LegalAffairStatisticsAppealedEnumItems) : Int = when(type) {
    LegalAffairStatisticsAppealedEnumItems.PayableFines -> R.string.legal_affairs_payable_fines
    LegalAffairStatisticsAppealedEnumItems.ConfirmedGuilty -> R.string.legal_affairs_confirmed_guilty
    LegalAffairStatisticsAppealedEnumItems.AppointingAdvocates -> R.string.legal_affairs_appointing_advocates
    LegalAffairStatisticsAppealedEnumItems.FoundNotGuilty -> R.string.legal_affairs_found_not_guilty
    LegalAffairStatisticsAppealedEnumItems.InJury -> R.string.legal_affairs_in_jury
}

@Composable
fun BarChartSingleItemWithText(
    count: Int?,
    percentage: Float,
    barColor: Color
) {
    val textColorPrimary = colorResource(id = R.color.colorChatTextPrimary)
    Column(
        verticalArrangement = Arrangement.Bottom,
        modifier = Modifier.height(height = CHART_HEIGHT.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "${count ?: 0}",
            fontSize = 11.sp,
            color = textColorPrimary
        )
        Spacer(modifier = Modifier.height(height = dimensionResource(id = R.dimen.line_spacing)))
        Box(
            modifier = Modifier
                .width(width = 26.dp)
                .background(
                    color = barColor, shape = RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp)
                )
                .fillMaxHeight(fraction = percentage)
        )
    }
}

@Composable
fun ChartLabelSingleItemView(
    modifier: Modifier = Modifier,
    labelColor: Color,
    @StringRes labelText: Int
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.Top
    ) {
        Box(
            modifier = Modifier
                .background(color = labelColor, shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
                .size(size = 14.dp)
        )
        Text(
            text = stringResource(id = labelText),
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.element_spacing)),
            color = colorResource(id = R.color.colorChatTextPrimary),
            fontSize = 11.sp
        )
    }
}

private val ChartColorPayableFine = Color(color = 0xFFEF1D2C)
private val ChartColorAppointingAdvocates = Color(color = 0xFF00B6C3)
private val ChartColorInJury = Color(color = 0xFFFA00CC)
private val ChartColorConfirmedGuilty = Color(color = 0xFF37CE00)
private val ChartColorFoundNotGuilty = Color(color = 0xFF9C9C9C)
private const val CHART_HEIGHT = 100