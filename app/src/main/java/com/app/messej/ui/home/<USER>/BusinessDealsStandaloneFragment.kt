package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.business.PayoutData
import com.app.messej.data.model.enums.GiftConversion
import com.app.messej.data.model.enums.TransactionTab
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.databinding.FragmentBaseBusinessDealsBinding
import com.app.messej.databinding.FragmentBusinessDealsStandaloneBinding
import com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BusinessDealsStandaloneFragment : BusinessDealsBaseFragment() {

    override lateinit var binding: FragmentBaseBusinessDealsBinding
    private lateinit var outerBinding: FragmentBusinessDealsStandaloneBinding
    private val args: BusinessDealsStandaloneFragmentArgs by navArgs()
    private val viewModel: BusinessWithDrawViewModel by activityViewModels ()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_deals_standalone, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        binding = outerBinding.baseLayout
        binding.viewModel = viewModels
        return outerBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()

    }

    private fun setup() {
        Log.d("ISResident",""+args.isResident)
        outerBinding.isResident = args.isResident
        outerBinding.isVisitor = args.isVisitor
        binding.upgradeButton.setOnClickListener {
            upgradeToPremium()
        }
    }
    private fun observe() {
        viewModel.onFlaxSellRequest.observe(viewLifecycleOwner) {
            if (it) {
                viewModel.eligibilityCheck()
            }
        }

        viewModel.payoutEligibility.observe(viewLifecycleOwner) {
            Log.i("observe: ", "log to print entered BusinessDealsViewPagerFragment")
            if (it?.eligibility == true && it.payoutData != null) {
                when (it.payoutData.status) {
                    PayoutData.PayoutStatus.INCOMPLETE -> {
                        findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalBusinessPointsReviewDialogFragment())
                    }

                    else -> {
                        findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalFlaxSellingConfirmationFragment())
                    }
                }
            } else if (it?.eligibility == false && it.payoutData != null) {
                findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalFlaxSellingConfirmationFragment())
            } else {
                findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalBusinessPointsReviewDialogFragment())
            }
        }
        viewModel.isEligibilityLoading.observe(viewLifecycleOwner) {
            binding.buttonSellFlax.isClickable = !it
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(outerBinding.customActionBar.toolbar)
        outerBinding.customActionBar.toolbar.title = getString(R.string.title_home_business_tab_deals)
    }

    override fun gotoSendFlax() {
        val action = HomeBusinessFragmentDirections.actionGlobalFlaxTransfer()
        findNavController().navigateSafe(action)
    }

    override fun gotoBuyCoin() {
        val action = HomeBusinessFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = true, hideActionBar = true)
        findNavController().navigateSafe(action)
    }

    override fun gotoCoinToFlax() {
        val action = HomeBusinessFragmentDirections.actionGlobalConvertCoinToFlaxFragment(GiftConversion.COIN_TO_FLAX)
        findNavController().navigateSafe(action)
    }

    override fun gotoFlaxToCoin() {
        val action = HomeBusinessFragmentDirections.actionGlobalConvertCoinToFlaxFragment(GiftConversion.FLAX_TO_COIN)
        findNavController().navigateSafe(action)
    }

    override fun gotoViewAll() {
        val action = HomeBusinessFragmentDirections.actionGlobalTransactionList(TransactionTab.TAB_FLAX)
        findNavController().navigateSafe(action)
    }

    override fun gotoSellFlax() {
        viewModel.setOnFlaxSellRequest(true)
    }

    override fun gotoRestoreRatings() {
        val action = HomeBusinessFragmentDirections.actionHomeBusinessFragmentToRestoreRatingFragment(true)
        findNavController().navigateSafe(action)
    }

    override fun gotoBuyFlix() {
        val action = HomeBusinessFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = false, hideActionBar = true)
        findNavController().navigateSafe(action)
    }

    private fun upgradeToPremium() {
        when (viewModel.isActive.value) {
            UserSubscriptionStatus.ACTIVE, UserSubscriptionStatus.EXPIRED -> {
                findNavController().navigateSafe(
                    NavGraphHomeDirections.actionGlobalAlreadySubscribedFragment(false)
                )
            }

            else -> {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalUpgradePremiumFragment())
            }
        }
    }

}