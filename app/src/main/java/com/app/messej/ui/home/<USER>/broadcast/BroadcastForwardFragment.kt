package com.app.messej.ui.home.publictab.broadcast

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.databinding.FragmentBroadcastForwardBottomSheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class BroadcastForwardFragment : BottomSheetDialogFragment() {

    companion object {
        const val FORWARD_MODES_KEY = "forward_modes"

        fun createBundle(modes: List<BroadcastMode>): Bundle {
            var modeInt = 0
            modes.forEach {
                modeInt = modeInt or (1 shl it.ordinal)
            }
            return bundleOf(FORWARD_MODES_KEY to modeInt)
        }

        fun parseBundle(bundle: Bundle): List<BroadcastMode> {
            val modeInt = bundle.getInt(FORWARD_MODES_KEY)
            val modes: MutableList<BroadcastMode> = mutableListOf()
            BroadcastMode.values().forEach {
                if (((1 shl it.ordinal) and modeInt)>0) {
                    modes.add(it)
                }
            }
            return modes
        }
    }

    private lateinit var binding: FragmentBroadcastForwardBottomSheetBinding

    val viewModel: BroadcastForwardViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_broadcast_forward_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewmodel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.forwardModes.observe(viewLifecycleOwner) {
            binding.apply {
                dearsCheck.visibility = if(it[BroadcastMode.ALL_DEARS]==true) View.VISIBLE else View.GONE
                fansCheck.visibility = if(it[BroadcastMode.ALL_FANS]==true) View.VISIBLE else View.GONE
                likersCheck.visibility = if(it[BroadcastMode.ALL_LIKERS]==true) View.VISIBLE else View.GONE
                premiumLikersCheck.visibility = if(it[BroadcastMode.ALL_PREMIUM_LIKERS]==true) View.VISIBLE else View.GONE
            }
        }
    }

    private fun setup() {

        binding.dearsHolder.setOnClickListener { viewModel.toggleForwardMode(BroadcastMode.ALL_DEARS) }
        binding.fansHolder.setOnClickListener { viewModel.toggleForwardMode(BroadcastMode.ALL_FANS) }
        binding.likersHolder.setOnClickListener { viewModel.toggleForwardMode(BroadcastMode.ALL_LIKERS) }
        binding.premiumLikersHolder.setOnClickListener { viewModel.toggleForwardMode(BroadcastMode.ALL_PREMIUM_LIKERS) }

        binding.confirmButton.setOnClickListener {
            findNavController().popBackStack()
            setFragmentResult(FORWARD_MODES_KEY, createBundle(viewModel.getForwardModes()))
        }

        binding.cancelButton.setOnClickListener {
            findNavController().popBackStack()
        }
    }


}