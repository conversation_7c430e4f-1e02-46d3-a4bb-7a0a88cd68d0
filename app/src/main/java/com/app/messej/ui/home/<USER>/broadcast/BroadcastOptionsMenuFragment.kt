package com.app.messej.ui.home.publictab.broadcast

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentBroadcastOptionsBottomSheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class BroadcastOptionsMenuFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentBroadcastOptionsBottomSheetBinding

    private val args: BroadcastOptionsMenuFragmentArgs by navArgs()

    companion object {
        const val SELECTED_OPTION_KEY = "broadcast_option"
        const val SELECTED_OPTION_STARRED = "broadcast_starred"
        const val SELECTED_OPTION_PRIVACY = "broadcast_privacy"
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_broadcast_options_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {

    }

    private fun setup() {
        binding.starredLayout.setOnClickListener {
            findNavController().popBackStack()
            setFragmentResult(
                SELECTED_OPTION_KEY, bundleOf(
                    SELECTED_OPTION_KEY to SELECTED_OPTION_STARRED
                )
            )
        }

        binding.privacyLayout.apply {
            visibility = if (args.showPrivacyOption) {
                setOnClickListener {
                    findNavController().popBackStack()
                    setFragmentResult(
                        SELECTED_OPTION_KEY, bundleOf(
                            SELECTED_OPTION_KEY to SELECTED_OPTION_PRIVACY
                        )
                    )
                }
                View.VISIBLE
            } else View.GONE
        }
    }
}