package com.app.messej.ui.chat

import android.app.Application
import android.media.AudioAttributes
import android.media.MediaPlayer
import android.media.PlaybackParams
import android.net.Uri
import android.os.CountDownTimer
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.TerminalSeparatorType
import androidx.paging.insertSeparators
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.ChatMessageSearchResult
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.api.profile.UserEnforcements
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BaseMediaUploadRepository.VideoDownloadResult
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.worker.ChatMessageWorker
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.socket.repository.PrivateChatEventRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.EnumUtil.next
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.MediaUtils.getFile
import com.app.messej.data.utils.ResultOf
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.io.File
import java.time.temporal.ChronoUnit
import kotlin.math.roundToLong

abstract class BaseChatDisplayViewModel(application: Application) : AndroidViewModel(application) {

    protected val accountRepo = AccountRepository(application)
    protected val profileRepo = ProfileRepository(application)

    protected val chatRepo = ChatRepository(application)

    private val eventRepo = PrivateChatEventRepository

    companion object {
        private const val TIMER_INTERVAL: Long = 250L
    }

    val enforcementFLow = accountRepo.getEnforcementsFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = null
    )

    val enforcements: UserEnforcements
        get() = enforcementFLow.value?: UserEnforcements(
            userId = accountRepo.user.id,
            enforcementsStatus = UserEnforcements.EnforcementStatus(),
            enforcementsMeta = UserEnforcements.EnforcementMeta()
        )

    val isUserBanned : Boolean
        get() = enforcements.enforcementsStatus.banned ||
                enforcements.enforcementsStatus.blacklisted ||
                enforcements.enforcementsStatus.suspectedBan ||
                enforcements.enforcementsStatus.pendingBlacklist

    protected val _dataLoading = MutableLiveData<Boolean>(false)
    val dataLoading: LiveData<Boolean> = _dataLoading

    protected val _actionLoading = MutableLiveData<Boolean>(false)
    val actionLoading: LiveData<Boolean> = _actionLoading

    val loggedIn: Boolean get() = accountRepo.loggedIn
    val user: CurrentUser get() = accountRepo.user
    val isVisitor: Boolean
        get() = user.citizenship.isVisitor

    // Chat Functions

    private fun shouldSeparateMessages(before: ChatMessageUIModel.ChatMessageModel, after: ChatMessageUIModel.ChatMessageModel?): Boolean {
        after ?: return true
        val bTime = before.chat.message.parsedCreatedTime ?: return false
        val aTime = after.chat.message.parsedCreatedTime ?: return false
        return !DateTimeUtils.isSame(bTime, aTime, ChronoUnit.DAYS)
    }

    protected abstract val _chatList: LiveData<PagingData<ChatMessageUIModel>>


    protected fun PagingData<ChatMessageUIModel.ChatMessageModel>.setShowName(): PagingData<ChatMessageUIModel.ChatMessageModel> {
        return this.insertSeparators(TerminalSeparatorType.SOURCE_COMPLETE) { before, after ->
            //hide repeating name for same sender
            if (before?.chat?.message?.sender == after?.chat?.message?.sender && after?.chat?.message?.messageType?.isNotActivity()==true) {
                before?.showName = false
            }
            null
        }
    }

    protected fun PagingData<ChatMessageUIModel.ChatMessageModel>.insertDateSeparators(): PagingData<ChatMessageUIModel> {
        return this.insertSeparators(TerminalSeparatorType.SOURCE_COMPLETE) { before, after ->
            if (before != null && shouldSeparateMessages(before, after)) {
                val ct = before.chat.message.parsedCreatedTime?: return@insertSeparators null
                ChatMessageUIModel.DateSeparatorModel(ct)
            } else {
                null
            }

        }
    }

    protected val _mediaTransfers: MutableList<MediaTransfer> = mutableListOf()
    protected val onMediaTransfersChanged = MutableLiveData<Boolean>(true)

    protected fun MutableList<MediaTransfer>.contains(id: String): Boolean {
        return this.find { it.messageId == id } != null
    }

    protected fun MutableList<MediaTransfer>.add(id: String, silent: Boolean = false) {
        this.add(MediaTransfer(id))
        if (silent) return
        onMediaTransfersChanged.postValue(true)
    }

    protected fun MutableList<MediaTransfer>.remove(id: String) {
        this.removeIf { it.messageId == id }
        onMediaTransfersChanged.postValue(true)
    }

    protected fun MutableList<MediaTransfer>.setProgress(id: String, progress: Int) {
//            Log.d("VDOWNLOAD", "setProgress: $progress to ${this.find { it.messageId == id }}")
        this.find { it.messageId == id }?.progress = progress
    }

    init {
        viewModelScope.launch {
            try {
                MediaUploadListener.uploadProgressFlow.collect {
                    Log.d("ENCODE", "MUL collected $it")
                    Log.d("ENCODE", "existing transfers $it")
                    it.forEach { (id, progress) ->
                        _mediaTransfers.setProgress(id, progress)
                    }
                }
            } finally { }
        }
    }

    abstract val showChats: LiveData<Boolean>

    protected fun checkIfNeedsSending(data: PagingData<ChatMessageUIModel.ChatMessageModel>) {
        var hasUnsent = false
        data.map { item ->
            if (item.chat.offlineMedia?.uploadState is MediaUploadState.Pending || item.message.sendStatus == AbstractChatMessage.SendStatus.PENDING) {
                hasUnsent = true
            }
        }
        if(hasUnsent) {
            viewModelScope.launch {
                ChatMessageWorker.startIfNotRunning()
            }
        }
    }

    protected open fun shouldHighlightMessageId(id: String): Boolean {
        val searchList = searchResultMessageIds.value?: return false
        return searchList.find { it.messageId == id }!=null
    }

    val chatList: MediatorLiveData<PagingData<ChatMessageUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<ChatMessageUIModel>?>(null)
        fun updateChatList() {
            var list = if (showChats.value == true) _chatList.value else PagingData.empty()
            val searchTerm = debouncedSearchText.value


            list = list?.map { item ->
                if (item is ChatMessageUIModel.ChatMessageModel) {
                    if (item.chat.offlineMedia?.uploadState is MediaUploadState.Uploading) {
                        if (!_mediaTransfers.contains(item.message.messageId)) {
                            _mediaTransfers.add(item.message.messageId)
                        }
                    }

                    item.ongoingTransfer = _mediaTransfers.find { it.messageId == item.message.messageId }
                    item.playerInfo = if (_nowPlayingMedia?.messageId == item.message.messageId && (_nowPlayingMedia?.listPosition ?: -1) >= 0) _nowPlayingMedia else null
                    item.selected = _selectedChatsList.find { return@find it.messageId==item.message.messageId }!=null
                    item.highlightMatches = if(!searchTerm.isNullOrBlank() && shouldHighlightMessageId(item.message.messageId)) searchTerm else null
                }
                item
            }
            med.postValue(list)
            pendingItemChange?.let {
                onItemChange.postValue(it)
                pendingItemChange = null
            }
        }
        med.addSource(_chatList) { updateChatList() }
        med.addSource(showChats) { updateChatList() }
        med.addSource(onMediaTransfersChanged) { updateChatList() }
        med.addSource(nowPlayingMedia) {
            Log.w("VPLAY", "nowPlayingMedia changed: ${nowPlayingMedia.value}" )
            updateChatList() }
        med.addSource(_selectedChats) { updateChatList() }
        med.addSource(searchResultMessageIds) { updateChatList() }
        med
    }

    protected var pendingItemChange: Int? = null

    val onItemChange = LiveEvent<Int>()

    protected val _scrollToMessage = MutableLiveData<String?>(null)
    val pendingScrollToMessage: LiveData<String?> = _scrollToMessage

    val scrollToMessageNow = LiveEvent<String>()

    val highlightMessage = LiveEvent<String>()

    fun clearPendingScroll() {
        _scrollToMessage.postValue(null)
    }

    fun postPendingScrollToMessage(id: String) {
        _scrollToMessage.postValue(id)
    }

   open fun onReplyClick(item: AbstractChatMessage) {
        val replyMsgId = item.replyTo?.messageId?: return
        scrollToMessageNow.postValue(replyMsgId)
        viewModelScope.launch {
            delay(1000)
            highlightMessage.postValue(replyMsgId)
        }
    }

    private val viewState = MutableStateFlow<MultiStateView.ViewState?>(MultiStateView.ViewState.LOADING)

    private val combinedViewState = _dataLoading.asFlow().combine(viewState) { dl, vs ->
        Log.d("CHAT","combinedViewState: _dataLoading $dl | viewState $vs")
        if (dl==true || vs==null) MultiStateView.ViewState.LOADING else vs
    }.stateIn(viewModelScope,SharingStarted.Eagerly,MultiStateView.ViewState.LOADING)

    val debouncedViewState = combinedViewState.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).distinctUntilChanged()


    fun setViewState(state: MultiStateView.ViewState) {
        viewState.value = state
    }


    // Chat Search

    protected val debouncedSearchText = MutableLiveData<String?>(null)

    val removedHighlights = LiveEvent<List<Int>>()

    val currentSearchResultIndex = MutableLiveData<Int>(0)

    val searchResultMessageIds: MediatorLiveData<List<ChatMessageSearchResult>?> by lazy {
        val med = MediatorLiveData<List<ChatMessageSearchResult>?>(null)
        med.addSource(debouncedSearchText) { term ->
            if(term.isNullOrBlank()) {
                med.value?.let { old ->
                    med.postValue(null)
                    removedHighlights.postValue(old.map { it.position })
                }
                return@addSource
            }
            Log.d("CMVH", "searchResultMessageIds: updating")
            viewModelScope.launch(Dispatchers.IO) {
                val new = provideSearchResults(term)
                Log.d("CMVH", "searchResultMessageIds: ${new.size}")
                val old = med.value?: listOf()
                val removed = old.map{ it.position }.minus(new.map { it.position }.toSet())
                med.postValue(new)


                currentSearchResultIndex.postValue(0)
//                Log.d("CMVH", "currentSearchResultIndex: 0")
//                new.getOrNull(0)?.let {
//                    Log.d("CMVH", "scrollTo: ${it.messageId}")
//                    _scrollToMessage.postValue(it.messageId)
//                }
                removedHighlights.postValue(removed)
            }
        }
        med
    }

    val canFindUp = currentSearchResultIndex.map {
        return@map it<(searchResultMessageIds.value.orEmpty().size-1)
    }

    val canFindDown = currentSearchResultIndex.map {
        return@map it>0
    }

    fun findUp() {
        val results = searchResultMessageIds.value?: return
        val currentPos = (currentSearchResultIndex.value?:0)+1
        if(currentPos>=results.size) return

        Log.d("CMVH", "currentSearchResultIndex: $currentPos")
        Log.d("CMVH", "scrollTo: ${results[currentPos].messageId}")
        currentSearchResultIndex.postValue(currentPos)
        scrollToMessageNow.postValue(results[currentPos].messageId)
    }

    fun findDown() {
        val results = searchResultMessageIds.value?: return
        val currentPos = (currentSearchResultIndex.value?:0)-1
        if(currentPos<0) return

        Log.d("CMVH", "currentSearchResultIndex: $currentPos")
        Log.d("CMVH", "scrollTo: ${results[currentPos].messageId}")
        currentSearchResultIndex.postValue(currentPos)
        scrollToMessageNow.postValue(results[currentPos].messageId)
    }

    fun findNearest(top: Int, bottom: Int) {
        Log.d("FNRST", "findNearest: scroll is bw $top and $bottom")
        if (top<0 || bottom<0) return
        val list = searchResultMessageIds.value?: return
        if (list.isEmpty()) return
        var bestIndex: Int = 0
        Log.d("FNRST", "findNearest: best set to $bestIndex of ${list.size}")
        // find nearest outside index
        val nearTopIndex = list.sortedBy { it.position }.indexOfLast { it.position < top }
        val topDistance = if(nearTopIndex>=0) top - list[nearTopIndex].position else null
        topDistance?.let {
            Log.d("FNRST", "findNearest: nearest to top is $nearTopIndex at pos ${list[nearTopIndex].position} with distance $topDistance")
            bestIndex = nearTopIndex
            Log.d("FNRST", "findNearest: best set to $bestIndex of ${list.size}")
        }
        val nearBottomIndex = list.sortedBy { it.position }.indexOfFirst { it.position > bottom }
        val bottomDistance = if(nearBottomIndex>=0) list[nearBottomIndex].position - bottom else null
        bottomDistance?.let {
            Log.d("FNRST", "findNearest: nearest to bottom is $nearBottomIndex at pos ${list[nearBottomIndex].position} with distance $bottomDistance")
            if (topDistance==null || bottomDistance<topDistance) {
                bestIndex = nearBottomIndex
                Log.d("FNRST", "findNearest: best set to $bestIndex of ${list.size}")
            }
        }
        // try to find in visible range
        val insideIndex = list.indexOfFirst { it.position in top..bottom }.let {
            if (it>=0) it else null
        }
        insideIndex?.let {
            Log.d("FNRST", "findNearest: nearest inside is $it at pos ${list[it].position}")
            bestIndex = it
            Log.d("FNRST", "findNearest: best set to $bestIndex of ${list.size}")
        }
        currentSearchResultIndex.postValue(bestIndex)
        scrollToMessageNow.postValue(list[bestIndex].messageId)
    }

    open suspend fun provideSearchResults(term: String): List<ChatMessageSearchResult> = listOf()

    // Media OPS
    @OptIn(FlowPreview::class)
    fun downloadMedia(message: AbstractChatMessageWithMedia, pos: Int, onFinish: (() -> Unit)? = null) {
        viewModelScope.launch {
            if (!_mediaTransfers.contains(message.message.messageId)) {
                pendingItemChange = pos
                _mediaTransfers.add(message.message.messageId)
                val messageId = message.message.messageId
                message.message.mediaMeta?.let { meta ->
                    if (meta.mediaType == MediaType.VIDEO || meta.mediaType == MediaType.DOCUMENT) {
                        chatRepo.downloadMultipartMedia(meta).collect {
                            when (val res = it) {
                                is VideoDownloadResult.Progress -> {
                                    Log.d("VDOWNLOAD", "progress VM: ${res.percent}")
                                    _mediaTransfers.setProgress(messageId, res.percent)
                                }
                                is VideoDownloadResult.Complete -> {
                                    // Nothing to do. Channel will close
                                    chatRepo.saveOfflineMedia(messageId,meta,res.file)
                                }

                                is VideoDownloadResult.Error -> throw res.error
                            }
                        }
                        Log.d("VDOWNLOAD", "collect ended")
                        _mediaTransfers.remove(message.message.messageId)
                        // DB invalidation will refresh the UI anyway if its a success
//                        onItemChange.postValue(pos)
                    } else {
                        val result = chatRepo.downloadChatMedia(message.message)
                        // DB invalidation will refresh the UI anyway if its a success
                        if (result !is ResultOf.Success) pendingItemChange = pos
                        _mediaTransfers.remove(message.message.messageId)
                    }
                }

            }
        }
    }

    protected var _nowPlayingMedia: MediaPlayerInfo? = null
    protected val nowPlayingMedia: MutableLiveData<MediaPlayerInfo?> = MutableLiveData()
    val nowPlaying: LiveData<MediaPlayerInfo?> = nowPlayingMedia
    val onAudioPlaybackFinished =  LiveEvent<MediaPlayerInfo>()

    private var mediaPlayer: MediaPlayer? = null
    private var mediaTimer: CountDownTimer? = null

    @Volatile
    protected var mediaPLayerLocked = false

    private fun seekPlayer(pos: Float) {
        try {
            mediaPlayer?.seekTo((pos * 1000).toLong(), MediaPlayer.SEEK_CLOSEST)
        } catch (_: Exception) {
        }
    }

    fun cycleMediaSpeed(msg: AbstractChatMessageWithMedia) {
        mediaPlayer?.let { player ->
            val med = _nowPlayingMedia ?: return
            //Check current progress of audio and hold pending duration
            val pendingDuration = med.duration - med.progress
            // cycle through the values. wrap around if overflowed
            //check the audio is going to complete or not, using pendingDuration with .1 seconds
            if(med.messageId!=msg.message.messageId || med.state != MediaPlayerInfo.MediaState.PLAYING || pendingDuration <= 0.1) return
            med.audioSpeed = med.audioSpeed.next()
            val playbackParams = PlaybackParams()
            playbackParams.speed = med.audioSpeed.speed
            try {
                player.playbackParams = playbackParams
                _nowPlayingMedia = med
                nowPlayingMedia.postValue(med)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun setupPlayer(resetPlayer: Boolean) {
        mediaPLayerLocked = true
        stopTimer()
        viewModelScope.launch(Dispatchers.IO) {
            try {
                _nowPlayingMedia?.also {
                    if (mediaPlayer == null || resetPlayer) {
                        mediaPlayer?.let { player ->
                            try {
                                if (player.isPlaying) player.stop()
                                player.release()
                                mediaPlayer = null
                            } catch (_: Exception) {
                            }
                        }
                        mediaPlayer = MediaPlayer().apply {
                            setAudioAttributes(
                                AudioAttributes.Builder().setContentType(AudioAttributes.CONTENT_TYPE_SPEECH).setUsage(AudioAttributes.USAGE_MEDIA).build()
                            )
                            setDataSource(getApplication(), Uri.fromFile(File(it.mediaPath)))
                            prepare()
                        }
                    }
                    when (it.state) {
                        MediaPlayerInfo.MediaState.PLAYING -> {
                            if (mediaPlayer?.isPlaying == false) mediaPlayer?.start()
                            seekPlayer(it.progress)
                            startTimer(it.duration - it.progress.roundToLong() + 2) { fin ->
                                try {
                                    if (mediaPlayer?.isPlaying == false) {
                                        it.state = MediaPlayerInfo.MediaState.NONE
                                        onAudioPlaybackFinished.postValue(it)
                                        stopTimer()
                                    } else it.progress = mediaPlayer?.currentPosition?.div(1000F) ?: 0f
                                } catch (e: Exception) {
                                    stopTimer()
                                }
                            }
                        }

                        MediaPlayerInfo.MediaState.PAUSED -> if (mediaPlayer?.isPlaying == true) mediaPlayer?.pause()
                        MediaPlayerInfo.MediaState.NONE -> if (mediaPlayer?.isPlaying == true) mediaPlayer?.stop()
                    }

                }
            }
            catch (e: Exception) {
                Firebase.crashlytics.log("trying to play media: ${_nowPlayingMedia?.mediaPath}")
                Firebase.crashlytics.recordException(e)
            } finally {

            }
            mediaPLayerLocked = false
        }
    }

    /**
     * Triggered twice when a different audio needs to start playing. Once for the old position and then for the new position
     * Also triggered every second while playing
     */
    val onPlayerStateChange = LiveEvent<Int>()
    val onStopVideoPlayback = LiveEvent<Unit>()

    private fun clearNowPlaying() {
        val med = _nowPlayingMedia?: return
        if(med.meta.mediaType==MediaType.VIDEO) {
            onStopVideoPlayback.value = Unit
        }
        med.apply {
            state = MediaPlayerInfo.MediaState.NONE
            progress = 0F
        }
        _nowPlayingMedia = null
        nowPlayingMedia.value = null
        onPlayerStateChange.postValue(med.listPosition)
    }

    protected fun setupMedia(info: MediaPlayerInfo, toggleState: Boolean = true, seekPos: Float? = null) {
        var med = _nowPlayingMedia
        Log.w("BCDVM", "setupMedia: $med", )
        val sameMedia = med!=null && med.messageId == info.messageId && med.listPosition == info.listPosition
        if (!sameMedia) {
            //reset old media info
            clearNowPlaying()
            med = info
        } else {
            med!!.listPosition = info.listPosition
        }

        //set seek position
        seekPos?.let {
            med.progress = it
            med.state = MediaPlayerInfo.MediaState.PAUSED
        }
        if (med.progress >= med.duration) med.progress = 0F

        if (toggleState) {
            med.state = if (med.state == MediaPlayerInfo.MediaState.PLAYING) MediaPlayerInfo.MediaState.PAUSED else MediaPlayerInfo.MediaState.PLAYING
        }

        if (!sameMedia) {
            // different media file
            _nowPlayingMedia = med

            nowPlayingMedia.value = med
            onPlayerStateChange.postValue(info.listPosition)
        }
        setupPlayer(!sameMedia)
    }

    open fun playMedia(message: AbstractChatMessageWithMedia, pos: Int) {
        if (mediaPLayerLocked) return
        if (!message.hasOfflineMedia) return
        setupMedia(MediaPlayerInfo.from(message, pos))

    }

    open fun onPlayingVideo(message: AbstractChatMessageWithMedia, pos: Int) {
        stopMediaPlayback()
        if (!message.hasOfflineMedia) return
        val info = MediaPlayerInfo.from(message,pos).apply {
            state = MediaPlayerInfo.MediaState.PLAYING
        }
        _nowPlayingMedia = info
        nowPlayingMedia.postValue(info)
    }

    /**
     * Call this method when you need to stop all media playback and clear resources,
     * for e.g. when you are going to start recording audio
     */
    protected fun stopMediaPlayback(force: Boolean = true) {
        _nowPlayingMedia?.let {
            try {
                mediaPlayer?.apply {
                    stop()
                    release()
                }
                mediaPlayer = null
                if (force || it.listPosition!=MediaPlayerInfo.LIST_POS_RECORDING) {
                    clearNowPlaying()
                }
                mediaPLayerLocked = false
            } catch (_: Exception) {
            }
        }
    }

    fun releaseMediaAssetsIfPlaying(messageId: String): Boolean {
        return if (_nowPlayingMedia?.messageId==messageId) {
            releaseMediaAssets()
            true
        } else false
    }

    fun releaseMediaAssets() {
        stopMediaPlayback(false)
    }

    fun seekMedia(message: AbstractChatMessageWithMedia, pos: Int, seekPos: Float) {
        Log.d("PHCVM", "seekMedia: ${message.message.messageId}")
        if (mediaPLayerLocked) return
        if (!message.hasOfflineMedia) return
        setupMedia(MediaPlayerInfo.from(message, pos), false, seekPos)
    }

    private fun stopTimer() {
        mediaTimer?.cancel()
        mediaTimer = null
    }

    private fun startTimer(seconds: Long = 0, tick: ((Boolean) -> Unit)? = null) {
        Log.d("TIMER", "startTimer: $seconds")
        viewModelScope.launch(Dispatchers.Main) {
            stopTimer()
            mediaTimer = object : CountDownTimer(seconds * 1000, TIMER_INTERVAL) {
                override fun onTick(millisUntilFinished: Long) {
                    tick?.invoke(false)
                }

                override fun onFinish() {
                    tick?.invoke(true)
                }
            }.start()

        }
    }

    override fun onCleared() {
        super.onCleared()
        stopTimer()
    }

    // Selection mode and actions
    private val _chatSelectionMode = MutableLiveData(false)
    val chatSelectionMode: LiveData<Boolean> = _chatSelectionMode

    protected var _selectedChatsList: MutableList<AbstractChatMessage> = mutableListOf()
    protected val _selectedChats = MutableLiveData<List<AbstractChatMessage>>(listOf())
    val selectedChats: LiveData<List<String>> = _selectedChats.map { chat ->
        return@map chat.map { it.messageId } }

    fun enterSelectionMode(msg: AbstractChatMessage, pos: Int) {
        if (_chatSelectionMode.value == false) {
            _chatSelectionMode.value = true
            _selectedChatsList = mutableListOf()
        }
        selectMessage(msg, pos)
    }

    fun exitSelectionMode() {
        _chatSelectionMode.value = false
        pendingItemChange = -1
        _selectedChatsList = mutableListOf()
        _selectedChats.value = listOf()
    }

    val canCopySelection = _selectedChats.map { it.size==1 && it[0].hasText }

    val canForwardPrivateSelection = _selectedChats.map { it.size==1}

    open val canDeleteSelection = _selectedChats.map { it.isNotEmpty() && it.all { ch -> (ch.sender == user.id) && !ch.deleted } }

    open fun canDeleteMessage(msg: AbstractChatMessageWithMedia) = msg.message.sender == user.id && !msg.message.deleted

    fun selectMessage(msg: AbstractChatMessage, pos: Int): Boolean {
        Log.d("CHAT", "selectMessage: selecting $pos | ${msg.displayMessage}")
        if (_chatSelectionMode.value == false) return false
        if(msg.deleted) return false
        pendingItemChange = pos
        _selectedChatsList.apply {
            if (!removeIf { msg.messageId == it.messageId }) {
                add(msg)
            }
            _selectedChats.value = toList()
        }
        if (_selectedChatsList.isEmpty()) exitSelectionMode()
        return true
    }

    abstract fun getSenderForReply(msg: AbstractChatMessage): SenderDetails?

    val onCopyText = LiveEvent<AbstractChatMessage>()

    fun copySelection() {
        if (_selectedChatsList.size>0) {
            val msg = _selectedChatsList[0]
            onCopyText.postValue(msg)
        }
        exitSelectionMode()
    }

    val onSavedToGallery = LiveEvent<Boolean>()

    fun saveToGallery(media: OfflineMedia?){
        media?: return
        viewModelScope.launch(Dispatchers.IO) {
            MediaUtils.saveImageToGallery(getApplication(), media.getFile())
            onSavedToGallery.postValue(true)
        }
    }
}