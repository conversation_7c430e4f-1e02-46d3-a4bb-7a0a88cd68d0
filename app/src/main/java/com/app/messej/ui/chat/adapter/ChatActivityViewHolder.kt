package com.app.messej.ui.chat.adapter

import com.app.messej.databinding.ItemChatMessageActivityBinding
import com.app.messej.ui.chat.ChatMessageUIModel

class ChatActivityViewHolder(val binding: ItemChatMessageActivityBinding) : ChatAdapter.ChatViewHolder(binding.root) {
    override fun bind(item: ChatMessageUIModel) = with(binding) {
        message = (item as ChatMessageUIModel.ChatMessageModel).message
    }
}