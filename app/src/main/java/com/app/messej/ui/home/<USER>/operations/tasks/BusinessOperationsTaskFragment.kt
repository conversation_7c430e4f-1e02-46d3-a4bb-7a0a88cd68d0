package com.app.messej.ui.home.businesstab.operations.tasks

import BusinessOperationTaskTwoFragment
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.R
import com.app.messej.databinding.FragmentBusinessTaskBinding
import com.app.messej.databinding.LayoutBusinessTabCustomBinding
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class BusinessOperationsTaskFragment : Fragment() {
    private var mTaskAdapter: FragmentStateAdapter? = null
    private lateinit var binding: FragmentBusinessTaskBinding
    var customBinding: LayoutBusinessTabCustomBinding? =null
    private val commonViewModel: BusinessOperationsTaskViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_task, container, false)
        binding.lifecycleOwner = viewLifecycleOwner

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun observe() {
        commonViewModel.otpVerificationComplete.observe(viewLifecycleOwner){
            if(it) {
                    binding.taskTab.run {
                        getTabAt(1)?.view?.isClickable = true
                        getTabAt(2)?.view?.isClickable = true
                        selectTaskTwoTab()
                    }
            }else{
                binding.taskTab.run {
                    getTabAt(1)?.view?.isClickable = false
                    getTabAt(2)?.view?.isClickable = false
                    selectTaskOneTab()
                }
            }
        }
 /*      commonViewModel.taskOneData.observe(viewLifecycleOwner){
            it?.let {
                if (it.paypalVerified == true && it.profileCompletePercentage == 100 && commonViewModel.taskThreeLearnMoreClicked.value == false) {
                    binding.taskTab.run {
                        getTabAt(1)?.view?.isClickable = true
                        getTabAt(2)?.view?.isClickable = true
                        selectTaskTwoTab()
                    }
                } else if (it.paypalVerified == true && it.profileCompletePercentage == 100) {
                    binding.taskTab.run {
                        getTabAt(1)?.view?.isClickable = true
                        getTabAt(2)?.view?.isClickable = true
                    }
                }
            }
        }
*/
    }

    private fun selectTaskTwoTab() {
        lifecycleScope.launch {
            delay(100)
            withContext(Dispatchers.Main){
                binding.taskPager.setCurrentItem(1,true)
            }
        }
    }

    private fun selectTaskOneTab() {
        lifecycleScope.launch {
            delay(100)
            withContext(Dispatchers.Main){
                binding.taskPager.setCurrentItem(0,true)
            }
        }
    }

    private fun selectTaskThreeTab() {
        lifecycleScope.launch {
            delay(100)
            withContext(Dispatchers.Main){
                binding.taskPager.setCurrentItem(2,true)
            }
        }
    }

    private fun setUp() {
        mTaskAdapter = object : FragmentStateAdapter(this) {

            override fun getItemCount(): Int = 3

            override fun createFragment(position: Int): Fragment {
                // Return a NEW fragment instance in createFragment(int)
                val fragment = when (position) {
                    0 -> BusinessOperationTaskOneFragment()
                    1 -> BusinessOperationTaskTwoFragment()
                    2 -> BusinessOperationTaskThreeFragment()
                    else -> throw java.lang.IllegalArgumentException("There should only be 3 tabs")
                }
                return fragment
            }
        }

        binding.taskPager.adapter = mTaskAdapter
        binding.taskPager.isUserInputEnabled = false
        commonViewModel.setTabPosition(0)


        TabLayoutMediator(binding.taskTab, binding.taskPager) { tab, position ->
            customBinding=DataBindingUtil.bind(LayoutInflater.from(context).inflate(R.layout.layout_business_tab_custom, null))
            tab.customView = customBinding?.root
            when (position) {
                0 ->  {
                    customBinding?.txtTabTitle?.apply {
                        text = context.getString(R.string.title_task_one)
                        setTextAppearance(android.R.style.TextAppearance_Medium)
                        setTextColor(resources.getColor(R.color.colorBusinessOperationTabTextSelectionColor, null))
                    }
                }
                1 -> {
                    tab.view.isClickable = false
                    customBinding?.txtTabTitle?.text = getString(R.string.title_task_two)
                }
                2 -> {
                    tab.view.isClickable = false
                    customBinding?.txtTabTitle?.text = getString(R.string.title_task_three)
                }
            }
        }.attach()

        binding.taskTab.addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                commonViewModel.setTabPosition(tab?.position)
                tab?.customView?.findViewById<TextView>(customBinding?.txtTabTitle?.id!!)?.apply {
                    setTextAppearance(android.R.style.TextAppearance_Medium)
                    setTextColor(resources.getColor(R.color.colorBusinessOperationTabTextSelectionColor, null))
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.customView?.findViewById<TextView>(customBinding?.txtTabTitle?.id!!)?.apply {
                    setTextAppearance(android.R.style.TextAppearance_Small)
                    setTextColor(resources.getColor(R.color.textColorOnPrimary,null))
                }
            }
            override fun onTabReselected(tab: TabLayout.Tab?) {

            }
        })
    }
}