package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.databinding.FragmentPodiumPrizeContributionBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.FragmentExtensions.showToast

class ChallengePrizeContributionFragment : Fragment() {

    private lateinit var binding: FragmentPodiumPrizeContributionBinding
    private val viewModel: PodiumCreateChallengeViewModel by navGraphViewModels(R.id.nav_challenge_setup)

    private var mSpeakerAdapter: PodiumCreateSpeakersAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_prize_contribution, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.podium_challenge_contributor_title)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun setup() {
        initAdapter()
        binding.startOrRequestButton.setOnClickListener {
            viewModel.sendContribution()
        }

        binding.contributorSearchButton.setOnClickListener {
            viewModel.searchContributor(binding.contributorAutocomplete.text.toString())
            hideKeyboard()
        }
    }

    private fun observer() {
        val textInputEditText = binding.textInputContributorSearch.editText as AutoCompleteTextView
        textInputEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val searchText = s.toString()
                if (searchText.isNotEmpty()) {
                    viewModel.searchContributor(searchText)
                } else {
                    textInputEditText.dismissDropDown()
                }
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })
        viewModel.contributorsSearchList.observe(viewLifecycleOwner) {
            if (it != null) {
                val mAdapter = ContributorSearchAdapter(requireContext(), it)
                (binding.textInputContributorSearch.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(mAdapter)
                    mAdapter.notifyDataSetChanged()
                    setOnItemClickListener { _, _, position, _ ->
                        mAdapter.getItem(position).let {podiumSpeaker->
                            text = null
                            viewModel.onContributorSelected(podiumSpeaker)
                        }
                    }
                }
            }
        }

        viewModel.contributionSent.observe(viewLifecycleOwner) {
            if (it) {
                findNavController().popBackStack(R.id.nav_challenge_setup, true)
            }
        }

        viewModel.startChallengeAPIFinished.observe(viewLifecycleOwner) {
            if (it) {
                findNavController().popBackStack(R.id.nav_challenge_setup, true)
            }
        }

        viewModel.participantsCountNotMet.observe(viewLifecycleOwner) {
            showToast(R.string.podium_challenge_minimum_participant_not_met)
        }

        viewModel.onLowCoinBalance.observe(viewLifecycleOwner) {
            if (it) showToast(R.string.podium_challenge_no_sufficient_coin)
        }

        viewModel.speakerContributionError.observe(viewLifecycleOwner) {
            val error = if (it==null) null else when(it.first) {
                PodiumCreateChallengeViewModel.ContributionErrorType.MIN_COINS -> getString(R.string.podium_challenge_min_coins_note, it.second.toString())
                PodiumCreateChallengeViewModel.ContributionErrorType.COIN_STEP -> getString(R.string.podium_challenge_min_coins_step, it.second.toString())
            }

            when (viewModel.contributionType.value) {
                ChallengeContributionType.SELF -> binding.textInputYouPrizeAmount.error = error
                ChallengeContributionType.SPEAKERS -> binding.textInputSpeakersPrizeAmount.error = error
                ChallengeContributionType.CONTRIBUTOR -> binding.textInputContributorPrizeAmount.error = error
                else -> {}
            }
        }

        viewModel.activeChallenge.observe(viewLifecycleOwner) { sp ->
            Log.w("PLVM", "observe: speakersFullList ${sp?.participantScores}")
            if (sp?.challengeType in listOf(ChallengeType.KNOWLEDGE,ChallengeType.CONFOUR,ChallengeType.PENALTY,ChallengeType.BOXES,ChallengeType.MAIDAN)) {
                mSpeakerAdapter?.updateData(sp?.participantScores.orEmpty())
            }
        }
    }

    private fun initAdapter() {
        Log.d("PLF", "initAdapter: create new adapter")

        mSpeakerAdapter = PodiumCreateSpeakersAdapter(layoutInflater, mutableListOf())

        binding.speakerList.apply {
            layoutManager = GridLayoutManager(context, 2).apply {}
            setHasFixedSize(false)
            adapter = mSpeakerAdapter
        }
    }
}