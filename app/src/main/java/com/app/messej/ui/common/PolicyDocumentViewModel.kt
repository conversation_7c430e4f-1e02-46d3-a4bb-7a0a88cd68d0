package com.app.messej.ui.common

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.policy.PolicyResponse
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.repository.LegalDocumentRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PolicyDocumentViewModel(application: Application) : AndroidViewModel(application) {

    private var docRepo: LegalDocumentRepository = LegalDocumentRepository()

//  TNC

    private val _tncLoading = MutableLiveData<Boolean>(false)
    val tncLoading: LiveData<Boolean> = _tncLoading

    private val _tncError = MutableLiveData<String?>(null)
    val tncError: LiveData<String?> = _tncError

    private val _policyData = MutableLiveData<PolicyResponse?>()
    val policyData: LiveData<PolicyResponse?> = _policyData

    private val _isAccept = MutableLiveData<Boolean>(false)
    val isAccept: LiveData<Boolean> = _isAccept

    fun getLegalDocument(documentType: DocumentType) {
        _tncLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<PolicyResponse> = docRepo.getLegalDocument(documentType.type)) {
                is ResultOf.Success -> {
                    _policyData.postValue(result.value)
                }
                is ResultOf.APIError -> {
                    _tncError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _tncLoading.postValue(false)
        }
    }

    fun setButtonVisibility(boolean: Boolean){
        _isAccept.postValue(boolean)
    }
}