package com.app.messej.ui.home.publictab.podiums.manage

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.databinding.ItemPodiumWaitingSpeakerAllBinding

class PodiumWaitingListAllAdapter(private val listener: PodiumActionListener): PagingDataAdapter<PodiumSpeaker, PodiumWaitingListAllAdapter.PodiumWaitingListViewHolder>(PodiumDiff) {

    interface PodiumActionListener {
        fun onWaitingMenuClicked(view: View, speaker: PodiumSpeaker)

    }

    inner class PodiumWaitingListViewHolder(private val binding: ItemPodiumWaitingSpeakerAllBinding): RecyclerView.ViewHolder(binding.root){
        fun bind(item: PodiumSpeaker) = with(binding) {
            speaker = item
            participantsActionButton.setOnClickListener {
                listener.onWaitingMenuClicked(it, item)
            }
        }
    }
    object PodiumDiff : DiffUtil.ItemCallback<PodiumSpeaker>(){
        override fun areItemsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: PodiumWaitingListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumWaitingListViewHolder {
        val binding = ItemPodiumWaitingSpeakerAllBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumWaitingListViewHolder(binding)
    }

}