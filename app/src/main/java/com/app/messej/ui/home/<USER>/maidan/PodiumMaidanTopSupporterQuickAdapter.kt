package com.app.messej.ui.home.publictab.maidan

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.databinding.ItemPodiumMaidanSupportersBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class PodiumMaidanTopSupporterQuickAdapter(data: MutableList<PodiumChallenge.ChallengeUser>):
    BaseQuickAdapter<PodiumChallenge.ChallengeUser, BaseDataBindingHolder<ItemPodiumMaidanSupportersBinding>>(R.layout.item_podium_maidan_supporters, data) {

    override fun convert(holder: BaseDataBindingHolder<ItemPodiumMaidanSupportersBinding>,
                         item: PodiumChallenge.ChallengeUser
    ) {
        holder.dataBinding?.apply {
            speaker = item
        }
    }

    class DiffCallback: DiffUtil.ItemCallback<PodiumChallenge.ChallengeUser>() {
        override fun areItemsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: PodiumChallenge.ChallengeUser,
                                        newItem: PodiumChallenge.ChallengeUser
        ): Boolean {
            return oldItem.score == newItem.score
        }
    }
}