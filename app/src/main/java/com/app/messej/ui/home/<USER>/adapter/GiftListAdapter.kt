import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.enums.GiftType
import com.app.messej.databinding.ItemGiftBinding
import com.app.messej.databinding.ItemGiftCategoriesBinding
class GiftListAdapter(
    private val giftType: GiftType,
    private val listener: ActionListener
) : PagingDataAdapter<GiftListUIModel, RecyclerView.ViewHolder>(GiftDiff) {

    interface ActionListener {
        fun onItemClick(item: GiftItem)
        fun isChallenge(): Boolean = false
        fun isVisitor(): Boolean = false
//        fun isFlix(): Boolean = false
        fun availableFLixBalance():Double
        fun availableCoinBalance():Double
    }

    companion object{
        private const val VIEW_TYPE_CATEGORY_HEADER = 0
        private const val VIEW_TYPE_GIFT_ITEM= 1
    }

    override fun getItemViewType(position: Int): Int {
        return when (getItem(position)) {
            is GiftListUIModel.CategoryHeader -> VIEW_TYPE_CATEGORY_HEADER
            is GiftListUIModel.Gift -> VIEW_TYPE_GIFT_ITEM
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_CATEGORY_HEADER -> {
                val binding = ItemGiftCategoriesBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                CategoryViewHolder(binding)
            }
            VIEW_TYPE_GIFT_ITEM -> {
                val binding = ItemGiftBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                GiftViewHolder(binding, listener)
            }
            else -> throw IllegalArgumentException("Invalid View Type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is GiftListUIModel.CategoryHeader -> (holder as CategoryViewHolder).bind(item.categoryName,giftType,listener)
            is GiftListUIModel.Gift -> (holder as GiftViewHolder).bind(item.gift,giftType)
            else -> {}
        }
    }

    // ViewHolder for Categories
    class CategoryViewHolder(private val binding: ItemGiftCategoriesBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(categoryName: String, giftType: GiftType, listener: ActionListener) {
            binding.giftMode = giftType
            binding.textCategoryName.text = categoryName
            Log.d("BALNCE","flix blnce"+listener.availableFLixBalance().toString())
            Log.d("BALNCE","coin blnce"+listener.availableCoinBalance().toString())

            binding.textFlixNameBank.text = if(categoryName == "FLiX")
                binding.root.context.getString(R.string.title_business_balance,listener.availableFLixBalance().toString())
            else
                binding.root.context.getString(R.string.title_business_balance,listener.availableCoinBalance().toString())
            if ((listener.isChallenge() && giftType == GiftType.BANK && categoryName == binding.root.context.getString(R.string.common_flix))||(listener.isVisitor()&& categoryName == binding.root.context.getString(R.string.common_flix))){
                binding.giftParentLayout.isVisible=false
            }
        }
    }

    // ViewHolder for Gift Items
    class GiftViewHolder(private val binding: ItemGiftBinding, private val listener: ActionListener) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(gift: GiftItem, giftType: GiftType) {
            binding.gift = gift
//            binding.isFlix = listener.isFlix()
            if ((listener.isChallenge() && giftType == GiftType.BANK && gift.categoryName == binding.root.context.getString(R.string.common_flix))||(listener.isVisitor()&& gift.categoryName == binding.root.context.getString(R.string.common_flix))){
                binding.layoutGiftItem.isVisible=false
            }
            binding.isFlix = gift.categoryName == binding.root.context.getString(R.string.common_flix)
            binding.root.setOnClickListener {
                listener.onItemClick(gift)
            }
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        val manager = recyclerView.layoutManager
        if (manager is GridLayoutManager) {
            manager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (getItemViewType(position)) {
                        VIEW_TYPE_CATEGORY_HEADER -> 3
                        VIEW_TYPE_GIFT_ITEM  -> 1
                        else -> -1
                    }
                }
            }
        }
    }



       object GiftDiff : DiffUtil.ItemCallback<GiftListUIModel>() {
            override fun areItemsTheSame(oldItem: GiftListUIModel, newItem: GiftListUIModel): Boolean {
                return when {
                    oldItem is GiftListUIModel.CategoryHeader && newItem is GiftListUIModel.CategoryHeader ->
                        oldItem.categoryName == newItem.categoryName
                    oldItem is GiftListUIModel.Gift && newItem is GiftListUIModel.Gift ->
                        oldItem.gift.id == newItem.gift.id
                    else -> false
                }
            }

            override fun areContentsTheSame(oldItem: GiftListUIModel, newItem: GiftListUIModel): Boolean {
                return oldItem == newItem
            }
        }

}
