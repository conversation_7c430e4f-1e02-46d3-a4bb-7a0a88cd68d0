package com.app.messej.ui.home.publictab.huddles.chat

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.ui.PlayerView
import androidx.navigation.fragment.findNavController
import androidx.paging.CombinedLoadStates
import androidx.paging.LoadState
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.databinding.FragmentHuddleMyPostsBinding
import com.app.messej.databinding.ItemCustomActionBarHuddleMyPostsBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.chat.BaseChatDisplayFragment
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch


open class HuddleMyPostsFragment : BaseChatDisplayFragment(), HuddleMyPostAdapter.HuddleMyPostClickListener {

    private lateinit var binding: FragmentHuddleMyPostsBinding
    override val viewModel: HuddleMyPostsViewModel by viewModels()
    private lateinit var actionBarBinding: ItemCustomActionBarHuddleMyPostsBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_my_posts, container, false)
        inflateActionBar()
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    private fun inflateActionBar() {
        binding.actionBarStub.apply {
            viewStub?.apply {
                setOnInflateListener { _, inflated ->
                    actionBarBinding = ItemCustomActionBarHuddleMyPostsBinding.bind(inflated)
                }
                layoutResource = R.layout.item_custom_action_bar_huddle_my_posts
                inflate()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        setEmptyView()
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(actionBarBinding.toolbar)
        }
    }

    override fun onResume() {
        super.onResume()
        if ((mAdapter?.itemCount ?: 0) > 0) {
            mAdapter?.refresh()
        }
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.messagesList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView?
        get() = binding.multiStateView

    override val reversedLayout: Boolean
        get() = false

    private fun setup() {
        binding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
            }
        }
    }

    override fun processLoadStates(loadState: CombinedLoadStates) {
        super.processLoadStates(loadState)
        if (loadState.refresh !is LoadState.Loading) {
            binding.swipeRefresh.isRefreshing = false
        }
        if (loadState.refresh is LoadState.Loading) {
            binding.swipeRefresh.isRefreshing = true
        }
    }

    override val provideAdapter: ChatAdapter
        get() = HuddleMyPostAdapter(layoutInflater, viewModel.user.id, this,viewModel.user.citizenship)

    @SuppressLint("NotifyDataSetChanged")
    private fun observe() {
        viewModel.postCount.observe(viewLifecycleOwner) {
            actionBarBinding.postCount = it.toString()
        }

        viewModel.replyCount.observe(viewLifecycleOwner) {
            actionBarBinding.replyCount = it.toString()
        }
    }

    private var emptyViewBinding: LayoutListStateEmptyBinding? = null

    private fun setEmptyView() {
        try {
            val empty = DataBindingUtil.inflate<LayoutListStateEmptyBinding>(layoutInflater, R.layout.layout_list_state_empty, binding.multiStateView, false)
            binding.multiStateView.setViewForState(empty.root, MultiStateView.ViewState.EMPTY, false)
            emptyViewBinding = empty
            emptyViewBinding?.apply {
                edsEmptyImage.visibility = View.GONE
                edsEmptyMessage.text = resources.getString(R.string.chat_huddle_eds)
            }
            Log.w("BCDFLSL", "setEmptyView: empty view binding has been set!!")
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            Log.e("BCDFLSL", "setEmptyView: ")
        }
    }

    override fun onPostClick(msg: HuddleChatMessage) {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChatHuddle(msg.huddleIdInt,if(msg.senderDetails?.premium==true) msg.messageId else null))
    }

    override fun onUpgradeClick() { }

    override fun onFollowClick(msg: HuddleChatMessage) { }

    override fun onMessageOptionsClick(msg: HuddleChatMessageWithMedia, position: Int, view: View) { }

    override fun onClickOnMention(user: MentionedUser) { }
    override fun onGiftClick(msg: HuddleChatMessage) {

    }

    override fun goToGiftFile(isSelf: Boolean) {
    }

    override fun goToIdCard(msg: HuddleChatMessage) {
    }

    override fun onItemLongClick(item: AbstractChatMessage, position: Int) { }

    private var streamPrepareJob: Job? = null

    override fun onViewHolderCleanup(messageId: String) {
        super.onViewHolderCleanup(messageId)
        streamPrepareJob?.cancel()
        streamPrepareJob = null
    }

    override fun onStreamMedia(view: PlayerView, msg: AbstractChatMessageWithMedia, position: Int): Boolean {
        streamPrepareJob?.let {
            it.cancel()
            streamPrepareJob = null
        }
        streamPrepareJob = viewLifecycleOwner.lifecycleScope.launch {
            try {
                val url = viewModel.getVideoStreamingURL(msg, position) ?: return@launch
                if (!isActive) return@launch
                val media = MediaItem.fromUri(url)
                viewModel.onStreamingVideo(msg, url, position)
                playVideo(view, media)
            } finally {
                streamPrepareJob = null
            }
        }
        return true
    }
}