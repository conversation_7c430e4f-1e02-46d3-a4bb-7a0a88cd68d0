package com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColor
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.updateTransition
import androidx.compose.animation.fadeIn
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.SlotState

@Preview
@Composable
fun BoxChallengeBoardPreview() {
    var lastTouch by remember { mutableStateOf("Last Touch") }
    var board = remember { mutableStateOf(BoxChallengeBoardModel.dummy) }
    Column {
        Text(text = lastTouch, modifier = Modifier.fillMaxWidth().wrapContentHeight().background(colorResource(R.color.colorSurfaceSecondaryDarker)).padding(8.dp))
        BoxChallengeBoard(board) {
            lastTouch = it.toString()
            board.value.drawLine(it, ChallengePlayer.PLAYER1)
            Log.d("TAG", "BoxChallengeBoardPreview: $it")
        }
    }
}

@Preview(uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES)
@Composable
fun BoxChallengeBoardPreviewDark() {
    var board = remember { mutableStateOf(BoxChallengeBoardModel.dummy) }
    BoxChallengeBoard(board)
}

private val SlotState.boxColor: List<Int>?
    get() = when(this) {
        SlotState.EMPTY -> null
        SlotState.PLAYER1 -> listOf(R.color.colorPrimary, R.color.colorPrimaryLight)
        SlotState.PLAYER2 -> listOf(R.color.colorSecondaryDark, R.color.colorSecondary)
    }

@Composable
fun BoxChallengeBoard(modelState: State<BoxChallengeBoardModel>, modifier: Modifier = Modifier, onClick: (BoxChallengeBoardModel.Line) -> Unit = {}) {

    val model = modelState.value
    val rows = BoxChallengeBoardModel.ROWS
    val cols = BoxChallengeBoardModel.COLS

    CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
        BoxWithConstraints(
            modifier = modifier.padding(8.dp).fillMaxWidth().background(colorResource(R.color.colorSurfaceSecondary), RoundedCornerShape(16.dp))
                .border(3.dp, colorResource(R.color.colorPodiumConnect4), RoundedCornerShape(16.dp)).shadow(3.dp, RoundedCornerShape(16.dp))
        ) {
            val padding = 24.dp
            val boxSize = (this.maxWidth - padding - padding) / cols

            val dotSize = max(boxSize * 0.35f, 16.dp)
            val dotOffset = dotSize / 2

            val lineWidth = dotSize / 2
            val lineOffset = lineWidth / 2

            val cornerInset = boxSize * 0.15f
            val centerInset = boxSize * 0.35f

            Column {
                Row(modifier = Modifier.padding(horizontal = padding)) {
                    repeat(cols) { col ->
                        Box(
                            modifier = Modifier.padding(horizontal = cornerInset).size(boxSize - cornerInset - cornerInset, padding)
//                            .background(colorResource(R.color.colorPass))
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() }, indication = null
                                ) {
                                    onClick(BoxChallengeBoardModel.Line.Horizontal(0, col))
                                })
                    }
                }
                repeat(rows) { row ->
                    Row {
                        Box(
                            modifier = Modifier.padding(vertical = cornerInset).size(padding, boxSize - cornerInset - cornerInset)
//                            .background(colorResource(R.color.colorPass))
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() }, indication = null
                                ) {
                                    onClick(BoxChallengeBoardModel.Line.Vertical(row, 0))
                                })
                        repeat(cols) { col ->
                            val boxColor = model.board.boxes[row][col].boxColor?.map { colorResource(it) }
                            val transition = updateTransition(targetState = boxColor, label = "Gradient Transition")

                            val startColor by transition.animateColor(label = "Start Color") {
                                it?.first() ?: Color.Transparent
                            }
                            val endColor by transition.animateColor(label = "End Color") {
                                it?.last() ?: Color.Transparent
                            }

                            val backgroundColor = listOf(startColor, endColor)
                            Box(modifier = Modifier.size(boxSize).pointerInput(Unit) {
                                    detectTapGestures { offset ->
//                                        val clickInset = (boxSize*0.25f).toPx()
//
//                                        if (offset.x<clickInset && offset.y in clickInset..size.height-clickInset) onClick(BoxChallengeBoardModel.Line.Vertical(row,col))
//                                        else if (offset.x>size.width-clickInset && offset.y in clickInset..size.height-clickInset) onClick(BoxChallengeBoardModel.Line.Vertical(row,col+1))
//                                        else if (offset.y<clickInset && offset.x in clickInset..size.width-clickInset) onClick(BoxChallengeBoardModel.Line.Horizontal(row,col))
//                                        else if (offset.y>size.height-clickInset && offset.x in clickInset..size.width-clickInset) onClick(BoxChallengeBoardModel.Line.Horizontal(row+1,col))

                                        val cornerInsetPx = cornerInset.toPx()
                                        val centerInsetPx = centerInset.toPx()

                                        val isTopOrRight = offset.x > offset.y
                                        val isBottomOrRight = size.width - offset.x < offset.y


                                        when {
                                            isTopOrRight && isBottomOrRight -> {
                                                //Right
                                                if (offset.x > size.width - centerInsetPx && offset.y in cornerInsetPx..size.height - cornerInsetPx) {
                                                    onClick(BoxChallengeBoardModel.Line.Vertical(row, col + 1))
                                                }
                                            }

                                            isTopOrRight && !isBottomOrRight -> {
                                                //Top
                                                if (offset.y < centerInsetPx && offset.x in cornerInsetPx..size.width - cornerInsetPx) {
                                                    onClick(BoxChallengeBoardModel.Line.Horizontal(row, col))
                                                }
                                            }

                                            !isTopOrRight && isBottomOrRight -> {
                                                //Bottom
                                                if (offset.y > size.height - centerInsetPx && offset.x in cornerInsetPx..size.width - cornerInsetPx) {
                                                    onClick(BoxChallengeBoardModel.Line.Horizontal(row + 1, col))
                                                }
                                            }

                                            else -> {
                                                //Left
                                                if (offset.x < centerInsetPx && offset.y in cornerInsetPx..size.height - cornerInsetPx) {
                                                    onClick(BoxChallengeBoardModel.Line.Vertical(row, col))
                                                }
                                            }
                                        }
                                    }
                                }.background(
                                    brush = Brush.linearGradient(
                                        colors = backgroundColor, start = Offset(0f, 0f), end = Offset(100f, 100f)
                                    ),
                                )) {
                                <EMAIL>(
                                    visible = model.board.verticalLines[row][col].notEmpty, enter = fadeIn()
                                ) {
                                    VerticalLine(lineWidth, Modifier.offset(-lineOffset, 0.dp))
                                }
                                <EMAIL>(
                                    visible = col == cols - 1 && model.board.verticalLines[row][cols].notEmpty, enter = fadeIn()
                                ) {
                                    VerticalLine(lineWidth, Modifier.offset(boxSize - lineOffset, 0.dp))
                                }

                                <EMAIL>(
                                    visible = model.board.horizontalLines[row][col].notEmpty, enter = fadeIn()
                                ) {
                                    HorizontalLine(lineWidth, Modifier.offset(0.dp, -lineOffset))
                                }
                                <EMAIL>(
                                    visible = row == rows - 1 && model.board.horizontalLines[rows][col].notEmpty, enter = fadeIn()
                                ) {
                                    HorizontalLine(lineWidth, Modifier.offset(0.dp, boxSize - lineOffset))
                                }

                                // Top-left dot (overlapping previous box)
                                Dot(dotSize, Modifier.offset(-dotOffset, -dotOffset))
                                if (row == rows - 1) {
                                    // Bottom-left dot
                                    Dot(dotSize, Modifier.offset(-dotOffset, boxSize - dotOffset))
                                    if (col == cols - 1) {
                                        // Bottom-right dot (overlapping next box)
                                        Dot(dotSize, Modifier.offset(boxSize - dotOffset, boxSize - dotOffset))
                                    }
                                }
                                if (col == cols - 1) {
                                    // Top-right dot
                                    Dot(dotSize, Modifier.offset(boxSize - dotOffset, -dotOffset))
                                }


                            }
                        }

                        Box(
                            modifier = Modifier.padding(vertical = cornerInset).size(padding, boxSize - cornerInset - cornerInset)
//                            .background(colorResource(R.color.colorPass))
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() }, indication = null
                                ) {
                                    onClick(BoxChallengeBoardModel.Line.Vertical(row, cols))
                                })
                    }
                }
                Row(modifier = Modifier.padding(horizontal = padding)) {
                    repeat(cols) { col ->
                        Box(
                            modifier = Modifier.padding(horizontal = cornerInset).size(boxSize - cornerInset - cornerInset, padding)
//                            .background(colorResource(R.color.colorPass))
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() }, indication = null
                                ) {
                                    onClick(BoxChallengeBoardModel.Line.Horizontal(rows, col))
                                })
                    }
                }
            }

        }
    }
}

@Composable
fun Dot(size: Dp, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .size(size)
            .background(colorResource(R.color.colorSurfaceSecondaryDarker), shape = CircleShape)
            .border(size * 0.1f, colorResource(R.color.textColorSecondaryLight), shape = CircleShape)
    )
}

@Composable
fun VerticalLine(width: Dp, modifier: Modifier = Modifier) {
    var startAnimation by remember { mutableStateOf(false) }
    val density = LocalDensity.current
    val targetWidthPx = with(density) { width.toPx() }

    val animatedWidthPx by animateFloatAsState(
        targetValue = if (startAnimation) targetWidthPx else 0f,
        animationSpec = spring(
            dampingRatio = 0.8f,
            stiffness = Spring.StiffnessVeryLow
        ),
        label = "VerticalLineWidth"
    )

    val animatedWidthDp = with(density) { animatedWidthPx.toDp() }

    LaunchedEffect(Unit) {
        startAnimation = true
    }

    Box(
        modifier = modifier
            .fillMaxHeight()
            .width(animatedWidthDp)
            .background(colorResource(R.color.textColorSecondaryLight))
            .border(1.dp, colorResource(R.color.textColorAlwaysLightSecondary))
    )
}

@Composable
fun HorizontalLine(width: Dp, modifier: Modifier = Modifier) {
    var startAnimation by remember { mutableStateOf(false) }
    val density = LocalDensity.current
    val targetHeightPx = with(density) { width.toPx() }

    val animatedHeightPx by animateFloatAsState(
        targetValue = if (startAnimation) targetHeightPx else 0f,
        animationSpec = spring(
            dampingRatio = 0.8f,
            stiffness = Spring.StiffnessVeryLow
        ),
        label = "HorizontalLineHeight"
    )

    val animatedHeightDp = with(density) { animatedHeightPx.toDp() }

    LaunchedEffect(Unit) {
        startAnimation = true
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(animatedHeightDp)
            .background(colorResource(R.color.textColorSecondaryLight))
            .border(1.dp, colorResource(R.color.textColorAlwaysLightSecondary))
    )
}