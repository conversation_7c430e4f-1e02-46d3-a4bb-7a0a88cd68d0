package com.app.messej.ui.composeComponents

import androidx.annotation.DrawableRes
import androidx.annotation.FloatRange
import androidx.annotation.StringRes
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import com.app.messej.R
import kotlinx.coroutines.delay

@Composable
fun <T : Any> CustomPaginationView(
    modifier: Modifier = Modifier,
    lazyListState: LazyListState = rememberLazyListState(),
    lazyPagingItem: LazyPagingItems<T>,
    contentPadding: PaddingValues = PaddingValues(vertical = dimensionResource(id = R.dimen.activity_margin)),
    verticalArrangement: Arrangement.Vertical = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.activity_margin)),
    @DrawableRes emptyItemIcon: Int,
    @StringRes emptyViewTitle: Int,
    userScrollEnabled: Boolean = true,
    listItemEmptyBottomContent : @Composable (() -> Unit)? = null,
    lazyColumnContent: LazyListScope.() -> Unit,
    loadingAnimation: @Composable () -> Unit = { CustomLinearProgressIndicator() },
) {
    when (lazyPagingItem.loadState.refresh) {
        is LoadState.NotLoading -> {
            if (lazyPagingItem.itemCount == 0) {
                ListEmptyItemView(
                    modifier = Modifier.fillMaxSize(),
                    textId = emptyViewTitle,
                    icon = emptyItemIcon,
                    bottomView = listItemEmptyBottomContent
                )
            } else {
                LazyColumn(
                    modifier = modifier,
                    state = lazyListState,
                    contentPadding = contentPadding,
                    userScrollEnabled = userScrollEnabled,
                    verticalArrangement = verticalArrangement
                ) {
                    lazyColumnContent(this)
                    customPaginationLazyListAppendView(
                        lazyPagingItem = lazyPagingItem,
                        listScope = this
                    )
                }
            }
        }

        is LoadState.Loading -> {
            Box(modifier = Modifier.fillMaxSize()) {
                loadingAnimation()
            }
        }

        is LoadState.Error -> {
            ListErrorItemView(
                icon = emptyItemIcon,
                onRetry = { lazyPagingItem.retry() }
            )
        }
    }
}

private fun <T : Any> customPaginationLazyListAppendView(
    lazyPagingItem: LazyPagingItems<T>,
    listScope: LazyListScope
) {
    listScope.apply {
        when (lazyPagingItem.loadState.append) {
            is LoadState.NotLoading -> {}

            is LoadState.Loading -> {
                item {
                    //If need to show loading at the bottom of pagination when requesting new page
                    Box(modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        CustomLinearProgressIndicator(
                            modifier = Modifier.fillMaxWidth(0.5F)
                        )
                    }
                }
            }

            is LoadState.Error -> {
                item {
                    // Can use this if the next page is in error state.
                    // Visible at the bottom of the list.
                    PagerErrorItem(
                        onRetryClick = { lazyPagingItem.retry() }
                    )
                }
            }
        }
    }
}

@Composable
fun CustomLinearProgressIndicator(
    modifier: Modifier = Modifier,
    color : Color = colorResource(id = R.color.colorPrimary),
    strokeCap: StrokeCap = StrokeCap.Round
) {
    LinearProgressIndicator(
        modifier = modifier.fillMaxWidth(),
        color = color,
        strokeCap = strokeCap
    )
}

@Composable
fun CustomLinearProgressIndicatorWithProgress(
    modifier: Modifier = Modifier,
    color : Color = colorResource(id = R.color.colorPrimary),
    strokeCap: StrokeCap = StrokeCap.Round,
    @FloatRange(from = 0.0, to = 1.0) progress: Float
) {
    var initialProgress by rememberSaveable { mutableFloatStateOf(0F) }

    LaunchedEffect(Unit) {
        delay(timeMillis = 300)
        initialProgress = progress
    }

    val progressAnimated by animateFloatAsState(
        targetValue = initialProgress,
        animationSpec = tween(easing = LinearEasing)
    )

    LinearProgressIndicator(
        progress = progressAnimated,
        modifier = modifier.fillMaxWidth(),
        color = color,
        strokeCap = strokeCap
    )
}

@Composable
private fun PagerErrorItem(
    modifier: Modifier = Modifier,
    message: String = stringResource(id = R.string.default_unknown_error_message),
    onRetryClick: () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = dimensionResource(id = R.dimen.element_spacing))
            .border(width = 1.dp, color = colorResource(id = R.color.textColorPrimary),
                shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))
            ).padding(horizontal = dimensionResource(id = R.dimen.element_spacing)),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            color = colorResource(id = R.color.textColorPrimary),
            text = message,
            style = FlashatComposeTypography.defaultType.overline,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .weight(weight = 1f)
                .padding(end = dimensionResource(id = R.dimen.element_spacing))
        )
        CustomSmallButton(
            text = stringResource(id = R.string.common_retry),
            onClick = onRetryClick
        )
    }
}