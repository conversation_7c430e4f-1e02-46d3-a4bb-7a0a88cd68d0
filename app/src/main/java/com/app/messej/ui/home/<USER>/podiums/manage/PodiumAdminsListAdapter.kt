package com.app.messej.ui.home.publictab.podiums.manage

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.databinding.ItemPodiumAdminsListBinding

class PodiumAdminsListAdapter(val kebabVisibility: Boolean, val setupMoreMenu: (View, PodiumSpeaker) -> Boolean): PagingDataAdapter<PodiumSpeaker, PodiumAdminsListAdapter.PodiumAdminsListVH>(
    PodiumDiff
) {

    inner class PodiumAdminsListVH(private val binding: ItemPodiumAdminsListBinding): RecyclerView.ViewHolder(binding.root){
        fun bind(item: PodiumSpeaker) = with(binding) {
            speaker = item
            actionKebab = kebabVisibility
            adminsActionButton.isVisible = setupMoreMenu(adminsActionButton, item)
        }
    }
    object PodiumDiff : DiffUtil.ItemCallback<PodiumSpeaker>(){
        override fun areItemsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: PodiumAdminsListVH, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumAdminsListVH {
        val binding = ItemPodiumAdminsListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumAdminsListVH(binding)
    }

}