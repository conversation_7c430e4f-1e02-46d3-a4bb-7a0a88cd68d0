package com.app.messej.ui.chat

import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R

class PrivateChatImageAttachFragment : BaseImageAttachFragment() {
    override val viewModel: PrivateChatViewModel by navGraphViewModels(R.id.nav_chat_private)

    private val args: PrivateChatImageAttachFragmentArgs by navArgs()

    override val destinationName: String?
        get() = args.destination
}