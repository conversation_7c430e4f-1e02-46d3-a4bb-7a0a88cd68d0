package com.app.messej.ui.home.publictab.postat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.entity.Postat
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PostatFullFeedFragment : PostatFeedBaseFragment() {

    override val viewModel: PostatFullFeedViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        innerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_inner_list, container, false)
        innerBinding.lifecycleOwner = viewLifecycleOwner
        innerBinding.viewModel = viewModel
        return innerBinding.root
    }

    override fun onResume() {
        super.onResume()
        mAdapter?.refresh()
    }

    override fun onUserClick(item: Postat) {
        val isPremium = viewModel.user.premium
        if (isPremium) {
            findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToUserPostatFeedFragment(item.userId,item.senderDetails.userLivePodiumId))
        } else {
            findNavController().navigateSafe(PublicPostatStandaloneFragmentDirections.actionPublicPostatStandaloneFragmentToUserPostatFeedFragment(item.userId,item.senderDetails.userLivePodiumId))
        }
    }
}