package com.app.messej.ui.home.publictab.podiums.live

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.databinding.ItemPodiumAloneScrollBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class NextPodiumsAdapter(data: MutableList<Podium>): BaseQuickAdapter<Podium, BaseDataBindingHolder<ItemPodiumAloneScrollBinding>>(R.layout.item_podium_alone_scroll, data) {

    init {
        addChildClickViewIds(R.id.action_enter)
    }

    override fun convert(holder: BaseDataBindingHolder<ItemPodiumAloneScrollBinding>,
                         item: Podium
    ) {
        holder.dataBinding?.apply {
            podium = item
        }
    }

    class DiffCallback: DiffUtil.ItemCallback<Podium>() {
        override fun areItemsTheSame(oldItem: Podium, newItem: Podium): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Podium,
                                        newItem: Podium): Boolean {
            return oldItem == newItem
        }
    }
}