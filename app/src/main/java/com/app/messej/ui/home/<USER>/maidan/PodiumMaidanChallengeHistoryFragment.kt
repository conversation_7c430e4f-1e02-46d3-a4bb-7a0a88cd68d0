package com.app.messej.ui.home.publictab.maidan

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumMaidanChallengeHistoryBinding
import com.app.messej.ui.utils.ViewUtils

class PodiumMaidanChallengeHistoryFragment : Fragment() {

    private lateinit var binding: FragmentPodiumMaidanChallengeHistoryBinding
    private var mMaidanChallengeHistoryAdapter: PodiumMaidanChallengeHistoryAdapter? = null
    private val viewModel: PodiumMaidanStatsViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_maidan_challenge_history, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {

        (activity as MainActivity?)?.setupActionBar(binding.appbar.toolbar)

        mMaidanChallengeHistoryAdapter = PodiumMaidanChallengeHistoryAdapter()

        binding.rvChallengeHistory.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mMaidanChallengeHistoryAdapter
        }

        mMaidanChallengeHistoryAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
            }
        }
    }

    private fun observe() {

        viewModel.challengeHistoryPager.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mMaidanChallengeHistoryAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }
    }
}