package com.app.messej.ui.home.publictab.flash.player

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData

class FlashSinglePlayerViewModel(application: Application) : BaseFlashPlayerViewModel(application) {

    val dataLoading = MutableLiveData<Boolean>(false)

    private val _flashId = MutableLiveData<String?>(null)
    val flashId: LiveData<String?> = _flashId

    fun setFlashId(id: String) {
        _flashId.postValue(id)
    }

    override val _flashFeedList = _flashId.switchMap {
        if (it != null) {
            flashRepo.getSingleFlashPager(it).liveData.cachedIn(viewModelScope)
        } else null
    }

}