package com.app.messej.ui.home.publictab.huddles.comments

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.core.os.BundleCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.entity.Sticker
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.GroupChatStatus
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.model.enums.UserRole
import com.app.messej.databinding.FragmentHuddlePostCommentsBinding
import com.app.messej.ui.chat.ActiveChatTracker
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportBanAllowed
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureReportContentAllowed
import com.app.messej.ui.home.publictab.huddles.chat.BaseHuddleChatFragment
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoinFromGreenDot
import com.app.messej.ui.home.sticker.StickerFragment
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.setupMenuItemTextColor
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.snackbar.Snackbar
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class HuddlePostCommentsFragment : BaseHuddleChatFragment(), HuddlePostCommentsAdapter.ChatCommentListener {

    private lateinit var binding: FragmentHuddlePostCommentsBinding

    private val args: HuddlePostCommentsFragmentArgs by navArgs()

    override val viewModel: HuddlePostCommentsViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_post_comments, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observers()
    }

    private var privacySnackBar: Snackbar? = null

    private fun observers() {
        viewModel.onAddComment.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
        }

        viewModel.onDeleteComment.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
        }

        viewModel.onCommentReportCancel.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
        }

        viewModel.huddleStatus.observe(viewLifecycleOwner){
            mAdapter?.refresh()
        }

        viewModel.hasEditTextFocused.observe(viewLifecycleOwner) {
            if(it) {
                mAdapter?.let { adapter ->
                    lifecycleScope.launch {
                        try {
                            if ((binding.messagesList.layoutManager as LinearLayoutManager).findFirstVisibleItemPosition() == 0) {
                                delay(1000)
                                binding.messagesList.smoothScrollToPosition(1)
                            }
                        } finally {}
                    }
                }
            }
        }

        viewModel.huddlePrivacy.observe(viewLifecycleOwner) {
            if (it?.canComment == false) {
                if (privacySnackBar==null) {
                    privacySnackBar = showSnackbar(R.string.huddle_comment_restriction_message)
                }
            } else {
                privacySnackBar?.let { sb ->
                    if (sb.isShown) sb.dismiss()
                    privacySnackBar = null
                }
            }
        }
        setFragmentResultListener(StickerFragment.EMOJI_SELECT_RESULT_KEY) { _, bundle ->
            val sticker = BundleCompat.getParcelable(bundle, StickerFragment.EMOJI_RESPONSE, Sticker::class.java)?: return@setFragmentResultListener
            viewModel.postSticker(sticker)
        }
    }


    private fun setup() {
        viewModel.setPost(args.huddleId, args.postId)
        ActiveChatTracker.registerActiveScreen(ActiveChatTracker.ActiveChatScreen.HuddlePostComment(args.huddleId,args.postId), viewLifecycleOwner)
        binding.chatSendButton.setOnClickListener {
            viewModel.writeComment()
        }

        binding.chatInput.setEndIconOnClickListener {
            findNavController().navigate(HuddlePostCommentsFragmentDirections.actionHuddlePostCommentsFragmentToStickerFragment())
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.messagesList
    override val bindingRoot: View
        get() = binding.root
    override val provideAdapter: ChatAdapter
        get() = HuddlePostCommentsAdapter(layoutInflater, viewModel.user.id, this,viewModel.user.citizenship)

    override val multiStateView: MultiStateView?
        get() = null

    override fun customizeMessageOptions(popup: PopupMenu) {
        popup.menu.findItem(R.id.action_reply_to_post).isVisible = false
        popup.menu.findItem(R.id.action_edit_post).isVisible = false
    }

    override fun onCommentOptionsClick(msg: PostCommentItem, position: Int, view: View) {
        //To block actions on case : admin blocked
        if (viewModel.entryBlocked() || viewModel.huddleStatus.value != GroupChatStatus.ACTIVE) return

        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(R.menu.menu_huddle_post_comment_actions, popup.menu)
        popup.setForceShowIcon(true)
        val intervention = msg.senderId.let { viewModel.getInterventionForUser(it) }

        val isOwnComment = msg.senderId == viewModel.user.id

        val iamElevated = listOf(UserRole.ADMIN, UserRole.MANAGER).contains(viewModel.huddle.value?.role)
        val iAmAdmin = viewModel.huddle.value?.role==UserRole.ADMIN
        val iamPremiumUser = viewModel.user.premium
        val iAmPremiumAdmin = iAmAdmin && iamPremiumUser

        val managerIsPremium = viewModel.huddle.value?.managerPremium == true

        val isSenderPremium = msg.senderDetails?.premium==true
        val isSenderElevated = msg.senderDetails?.role?.isElevated==true
        val isSenderManager = msg.senderDetails?.role == UserRole.MANAGER
        val isSenderDeleted = msg.senderDetails?.deletedAccount==true
        val isTribe=viewModel.huddle.value?.isTribe==true

        val isCommentBanned=intervention?.commentBanned==true
        val isPostBanned=intervention?.postBanned==true
        val isReplyBanned=intervention?.replayBanned==true

        popup.menu.apply {
            findItem(R.id.action_cancel_report).isVisible = msg.isReported==true
            findItem(R.id.action_report).isVisible = msg.isReported!=true && !isOwnComment
            findItem(R.id.action_delete).isVisible = isOwnComment || (iamElevated && !isSenderManager) || (!isSenderPremium && iamPremiumUser)
            findItem(R.id.action_view_info).isEnabled = !isSenderDeleted
            findItem(R.id.action_view_info).isVisible = !isOwnComment && (/*iamPremiumUser ||*/ iamElevated) || isSenderPremium && !isSenderManager
            findItem(R.id.action_private_message).isVisible = false //!isOwnComment && (/*iamPremiumUser ||*/ iamElevated) || isSenderPremium && !isSenderManager
            findItem(R.id.action_private_message).isEnabled = !isSenderDeleted

            findItem(R.id.action_admin).isVisible = !isOwnComment && !isSenderManager && iamElevated && isSenderPremium && !isTribe && (msg.senderDetails?.role != null)
            findItem(R.id.action_admin).setTitle(
                if (isSenderElevated) R.string.chat_huddle_message_action_remove_admin
                else if(intervention?.invitedToBeAdmin==true) R.string.chat_huddle_message_action_admin_invite_cancel
                else R.string.chat_huddle_message_action_make_admin
            )

            findItem(R.id.ban_posting).let {
                it.isVisible = (managerIsPremium || iAmPremiumAdmin) && !isOwnComment && iamElevated && !isSenderManager && (msg.senderDetails?.role != null)
                it.setTitle(
                    if (isPostBanned==false) {
                        R.string.huddle_ban_from_posting
                    } else {
                        R.string.huddle_unban_from_posting

                    }
                )
            }
            findItem(R.id.ban_replying).let {
                it.isVisible = (managerIsPremium || iAmPremiumAdmin) && !isOwnComment && iamElevated && !isSenderManager && (msg.senderDetails?.role != null)
              it.setTitle(
                  if (!isReplyBanned){
                      R.string.huddle_ban_from_replying
                  }else{
                      R.string.huddle_unban_from_replying
                  }
              )
            }
            findItem(R.id.ban_commenting).let {
                it.isVisible = (managerIsPremium || iAmPremiumAdmin) && !isOwnComment && iamElevated && !isSenderManager && (msg.senderDetails?.role != null)
                it.setTitle(
                    if (!isCommentBanned) R.string.huddle_ban_from_commenting else R.string.huddle_unban_from_commenting
                )
            }

            findItem(R.id.remove_participant).isVisible= (managerIsPremium || iAmPremiumAdmin) && !isOwnComment && iamElevated && !isSenderManager && (msg.senderDetails?.role != null)
            findItem(R.id.action_block_from_huddle).isVisible = !isSenderManager && !isOwnComment && iamElevated && (msg.senderDetails?.role != null)
            findItem(R.id.action_block_from_huddle).isEnabled = !isSenderDeleted

            Log.d("RPF", "comment: ${msg.senderDetails}")
            findItem(R.id.action_report_legal).apply {
                isVisible = false // !isOwnComment && viewModel.user.canReport(msg.senderDetails?.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
            findItem(R.id.action_report_and_hide).apply {
                isVisible = false // !isOwnComment && viewModel.user.canReportAndHide(msg.senderDetails?.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
            findItem(R.id.action_ban).apply {
                isVisible = false // !isOwnComment && viewModel.user.canBan(msg.senderDetails?.citizenship)
                setupMenuItemTextColor(
                    color = R.color.colorError,
                    context = requireContext(),
                    isTextBold = true
                )
            }
        }
        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_delete -> viewModel.deleteComment(msg.commentId)
                R.id.action_report -> findNavController().navigateSafe(HuddlePostCommentsFragmentDirections.actionGlobalReportToManagerFragment(args.huddleId, args.postId, msg.commentId,
                                                                                                                                                ReportToManagerType.COMMENT))
                R.id.action_cancel_report -> viewModel.cancelReportComment(msg)
                R.id.action_view_info->{
//                    val userType = PublicUserProfileLoaderFragment.getUserType(msg.senderDetails?.citizenship)
                    Log.d("SID",""+msg.senderId)
                   findNavController().navigateSafe(HuddlePostCommentsFragmentDirections.actionHuddlePostCommentsFragmentToPublicUserProfileFragment(msg.senderId))
                }
                R.id.action_admin->{
                    if (msg.senderDetails?.role?.isElevated==true) viewModel.dismissAdmin(msg.senderId)
                    else if(intervention?.invitedToBeAdmin==true) viewModel.cancelAdminInvite(msg.senderId)
                    else viewModel.appointAsAdmin(msg.senderId)
                }
                R.id.action_forward->{
                    findNavController().navigateSafe(HuddlePostCommentsFragmentDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.HUDDLES, messageId = args.postId))
                }
                R.id.action_private_message->{
                    msg.senderId.let {
                        viewModel.navigateToPrivateMessage(it)
                    }
                }
                R.id.remove_participant->{
                    msg.senderId.let {
                        viewModel.removeUser(it)
                    }
                }
                R.id.action_block_from_huddle->{
                    if (intervention?.blockedFromHuddle==true) viewModel.blockUserFromHuddle(msg.senderId, Participant.ParticipantsActionTypes.UNBLOCK_HUDDLE_PARTICIPANT)
                    else confirmAction(
                        title = R.string.chat_huddle_message_action_block_from_huddle,
                        message = resources.getString(R.string.other_user_huddle_block_confirm, msg.senderDetails?.name?:""),
                        positiveTitle = R.string.common_yes,
                        negativeTitle = R.string.common_no
                    ) {
                        viewModel.blockUserFromHuddle(msg.senderId, Participant.ParticipantsActionTypes.BLOCK_HUDDLE_PARTICIPANT)
                    }
                }
                R.id.ban_posting->  viewModel.banUser(Participant.ParticipantStatus.POST_BAN, isPostBanned, msg.senderId)
                R.id.ban_replying->  viewModel.banUser(Participant.ParticipantStatus.REPLY_BAN, isReplyBanned, msg.senderId)
                R.id.ban_commenting->  viewModel.banUser(Participant.ParticipantStatus.COMMENT_BAN, isCommentBanned, msg.senderId)
                R.id.action_report_legal-> {
                    ensureReportContentAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.HuddlePostComment(msg, reportType = ReportType.REPORT).serialize()))
                    }
                }
                R.id.action_report_and_hide-> {
                    ensureReportContentAllowed {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.HuddlePostComment(msg, reportType = ReportType.REPORT_AND_HIDE).serialize()))
                    }
                }
                R.id.action_ban-> {
                    ensureReportBanAllowed {
                        msg.senderDetails?.asBasicUser()?.let {
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.User(it, reportType = ReportType.BAN).serialize()))
                        }
                    }
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    override fun onClickUser(msg: PostCommentItem) {
        findNavController().navigateSafe(HuddlePostCommentsFragmentDirections.actionGlobalPublicUserProfileFragment(msg.senderId))
    }

    override fun onClickOnMention(user: MentionedUser) {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(user.id))
    }

    override fun onGiftClick(msg: HuddleChatMessage) {

    }

    override fun goToGiftFile(isSelf: Boolean) {
    }

    override fun goToIdCard(msg: HuddleChatMessage) {
    }

    override fun onLivePodiumIndicatorClicked(msg: HuddleChatMessage) {
        val podiumId = msg.senderDetails?.userLivePodiumId ?: return
        validateAndConfirmJoinFromGreenDot(podiumId, msg.senderDetails?.name ?: return, viewModel.user)
    }

    override fun onItemLongClick(item: AbstractChatMessage, position: Int) {

    }

}