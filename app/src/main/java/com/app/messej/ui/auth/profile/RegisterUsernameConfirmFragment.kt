package com.app.messej.ui.auth.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterUsernameConfirmBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class RegisterUsernameConfirmFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentRegisterUsernameConfirmBinding

    private val args: RegisterUsernameConfirmFragmentArgs by navArgs()

    companion object {
        const val USERNAME_CONFIRM_REQUEST_KEY = "confirmUsername"
        const val USERNAME_CONFIRM_RESULT_KEY = "confirmUsernamePair"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_username_confirm, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        val confirmUsernameSubText: String = java.lang.String.format(resources.getString(R.string.register_create_username_confirm_text), args.username)
        binding.confirmUsernameText.text = confirmUsernameSubText

        binding.confirmButton.setOnClickListener {
            setFragmentResult(USERNAME_CONFIRM_REQUEST_KEY, bundleOf(USERNAME_CONFIRM_RESULT_KEY to true))
            findNavController().popBackStack()
        }

        binding.changeButton.setOnClickListener {
            findNavController().popBackStack()
        }
    }


}