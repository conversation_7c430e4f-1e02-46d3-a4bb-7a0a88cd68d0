package com.app.messej.ui.home.publictab.podiums.challenges.gift

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.challenges.PodiumGiftSupportersResponse.ChallengeSupporter
import com.app.messej.databinding.ItemGiftChallengeSupportersBinding

class PodiumGiftChallengeSupportersAdapter(): PagingDataAdapter<ChallengeSupporter, PodiumGiftChallengeSupportersAdapter.PodiumGiftChallengeSupportersListViewHolder>(PodiumUserDiff)  {

    inner class PodiumGiftChallengeSupportersListViewHolder(private val binding: ItemGiftChallengeSupportersBinding): RecyclerView.ViewHolder(binding.root){
        fun bind(item: ChallengeSupporter) = with(binding) {
            user = item
            showUsername = false
        }
    }
    object PodiumUserDiff : DiffUtil.ItemCallback<ChallengeSupporter>(){
        override fun areItemsTheSame(oldItem: ChallengeSupporter, newItem: ChallengeSupporter) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: ChallengeSupporter, newItem: ChallengeSupporter) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: PodiumGiftChallengeSupportersListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumGiftChallengeSupportersListViewHolder {
        val binding = ItemGiftChallengeSupportersBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumGiftChallengeSupportersListViewHolder(binding)
    }
}