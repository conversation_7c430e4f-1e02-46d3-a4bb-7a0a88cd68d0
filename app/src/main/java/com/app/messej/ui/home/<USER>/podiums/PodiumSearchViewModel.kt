package com.app.messej.ui.home.publictab.podiums

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch


class PodiumSearchViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)
    val podiumInvitationDeclined = LiveEvent<Boolean>()
    val podiumRemoval = LiveEvent<Boolean>()

    val iAmMinister:Boolean
        get() = user.citizenship == UserCitizenship.MINISTER

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    var searchKeyword = MutableLiveData<String>(null)

    var podiumTab:PodiumTab? = null

    private data class SearchParams(
        val tab: PodiumTab,
        val keyword: String
    )

    private val searchParams = MutableLiveData<SearchParams?>(null)

    fun setTab(tab: PodiumTab) {
        podiumTab = tab
        searchParams.postValue(SearchParams(tab, ""))
    }

    init {
        @OptIn(FlowPreview::class)
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                searchParams.value?.let { p ->
                    searchParams.postValue(p.copy(keyword = it.orEmpty()))
                }
            }
        }
    }

    fun resetSearch() {
        searchKeyword.postValue("")
    }

    private val _podiumSearchList = searchParams.switchMap {
        it?: return@switchMap null
        podiumRepository.getPodiumSearchListingPager(it.tab,it.keyword).liveData.cachedIn(viewModelScope)
    }

    val podiumSearchList: LiveData<PagingData<Podium>>  = _podiumSearchList

    fun declinePodiumInvitation(podiumId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (podiumRepository.declinePodiumInvitation(podiumId)) {
                    is ResultOf.Success -> {
                        podiumInvitationDeclined.postValue(true)
                    }
                    is ResultOf.APIError -> {}
                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PublicPodiumLVM", "declinePodiumInvitation: ${e.message}")
            }
        }
    }
    fun withDrawAsAdmin(id: Int,podiumId: String) {

        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.dismissAnAdmin(podiumId, id)) {
                    is ResultOf.Success -> {
                        podiumRemoval.postValue(true)
                    }

                    is ResultOf.APIError -> {
//                            podiumRemoval.postValue(true)
                    }
                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "dismissAsAdmin: erPodiumActionTyperor: ${e.message}")
            }

        }
    }
    val isAmbassadorOrMinister: Boolean
        get() = user.citizenship == UserCitizenship.AMBASSADOR || user.citizenship == UserCitizenship.MINISTER

}