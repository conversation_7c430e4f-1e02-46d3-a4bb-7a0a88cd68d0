package com.app.messej.ui.home.businesstab.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.BusinesDeals
import com.app.messej.databinding.ItemBusinesDealsListBinding

class BusinesDealsAdapter(private val businesDeals: List<BusinesDeals>,private val isSentFlax:Boolean) : RecyclerView.Adapter<BusinesDealsAdapter.BusinesDealsViewHolder>() {
    private var filteredData: List<BusinesDeals> = businesDeals.toMutableList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = BusinesDealsViewHolder(
        ItemBusinesDealsListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    override fun onBindViewHolder(holder: BusinesDealsViewHolder, position: Int) {
        val item = filteredData[position]
        holder.bind(item)
    }

    inner class BusinesDealsViewHolder(private val binding: ItemBusinesDealsListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: BusinesDeals) = with(binding) {

           binding.imagePersonPhoto.setImageResource(com.hbb20.R.drawable.flag_india)
            binding.textViewPersonName.text = item.name
            binding.textViewDate.text = item.date

            if(isSentFlax){
                binding.textViewGift.visibility=View.VISIBLE
                binding.flaxSendReceive.visibility=View.GONE

            }else{
                binding.textViewGift.visibility=View.GONE
                binding.flaxSendReceive.visibility=View.VISIBLE
                binding.flaxSendReceive.text = item.type

                when (item.type) {
                    "Sent Flax"-> binding.flaxSendReceive.setTextColor(Color.RED)
                   "Received Flax" -> binding.flaxSendReceive.setTextColor(Color.GREEN)
                    else -> binding.flaxSendReceive.setTextColor(Color.BLACK)
                }
            }


        }
    }

    override fun getItemCount(): Int {
        return filteredData.size
    }
    fun filter(query: String) {
        filteredData = businesDeals.filter { item ->
            item.name.contains(query, ignoreCase = true) // Customize this based on your data model
        }
        notifyDataSetChanged()
    }



}
