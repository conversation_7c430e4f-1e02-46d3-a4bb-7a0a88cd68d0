package com.app.messej.ui.auth

import android.app.Application
import android.os.CountDownTimer
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.auth.VerifyOTPResponse
import com.app.messej.data.model.api.auth.VerifyResponse
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.net.HttpURLConnection

class AuthOTPViewModel(application: Application) : AndroidViewModel(application) {

    private var authRepo: AuthenticationRepository = AuthenticationRepository(application)

    companion object {
        const val OTP_DIGITS = 6
    }

    //  Prerequisite Data
    private val _countryCode = MutableLiveData<String?>(null)
    val countryCode: LiveData<String?> = _countryCode

    private val _otpRequestMode = MutableLiveData<OTPRequestMode?>(null)
    val otpRequestMode: LiveData<OTPRequestMode?> = _otpRequestMode

    private val _phoneNumber = MutableLiveData<String?>(null)
    val phoneNumber: LiveData<String?> = _phoneNumber

    private val _phoneNumberWithCountryCode = MutableLiveData<String?>(null)
    val phoneNumberWithCountryCode: LiveData<String?> = _phoneNumberWithCountryCode

    private val _email = MutableLiveData<String?>(null)
    val email: LiveData<String?> = _email

    fun setMode(requestMode:OTPRequestMode, code: String?, number: String?, email:String?) {
        //TODO do validation
        _otpRequestMode.value=requestMode
        if (requestMode.isEmailType) {
            if (email==null) {
                throw Exception("Email should be provided for requestMode: $requestMode")
            }
        }
        else {
            if (code==null || number==null) {
                throw Exception("Phone number should be provided for requestMode: $requestMode")
            }
        }
        _countryCode.value = code
        _phoneNumber.value = UserInfoUtil.removeLeadingZeroes(number)
        _email.value = email
        val combined = UserInfoUtil.combineCountryCodeAndMobileNumber(countryCode.value.orEmpty(),phoneNumber.value.orEmpty())
        _phoneNumberWithCountryCode.postValue(combined)
    }

    val activeOTPDestination = _otpRequestMode.switchMap {
        it?: return@switchMap null
        if(it.isEmailType) _email else _phoneNumberWithCountryCode
    }

    private var referralCode: String? = null

    fun setReferralCode(code: String) {
        referralCode = code
    }

    //  OTP Stage

    private val _requestOTPLoading = MutableLiveData(false)
    val requestOTPLoading: LiveData<Boolean> = _requestOTPLoading

    private val _requestOTPError = MutableLiveData<String?>(null)
    val requestOTPError: LiveData<String?> = _requestOTPError

    private val _didRequestOTP = MutableLiveData(false)
    val didRequestOTP: LiveData<Boolean> = _didRequestOTP

    private val _canRequestOTPResend = MutableLiveData(true)
    val canRequestOTPResend: LiveData<Boolean> = _canRequestOTPResend

    private val _otpTimeout = MutableLiveData<Long>(0)
    val otpTimeout: LiveData<Long> = _otpTimeout

    val optTimeoutString = _otpTimeout.map {
        return@map DateTimeUtils.formatSeconds(it)
    }

    fun requestOTP(resend: Boolean = false) {
        if (!resend && _didRequestOTP.value == true) {
            // request OTP only if not already done
            return
        }

        if (resend && _canRequestOTPResend.value != true) {
            return
        }

        _requestOTPLoading.postValue(true)
        _requestOTPError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            val mode = _otpRequestMode.value?:return@launch
            val result: ResultOf<VerifyResponse> = if (mode.isEmailType) {
                authRepo.getEmailOTP(mode, _email.value!!, resend,referralCode)
            }else{
                authRepo.getMobileOTP(mode, _phoneNumber.value!!, _countryCode.value!!, resend, referralCode)
            }
            when (result) {
                is ResultOf.Success -> {
                    _didRequestOTP.postValue(true)
                    _canRequestOTPResend.postValue(false)
                    countDownWaitTime(result.value.waitTime.toLong())
                }
                is ResultOf.APIError -> {
                    if (result.code== HttpURLConnection.HTTP_BAD_REQUEST)
                        _requestOTPError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _requestOTPLoading.postValue(false)
        }
    }


    private var timer: CountDownTimer? = null

    private fun countDownWaitTime(wait: Long) {
        if(wait<=0) {
            _otpTimeout.postValue(0)
            _canRequestOTPResend.postValue(true)
        }
        _otpTimeout.postValue(wait)
        _canRequestOTPResend.postValue(false)
        viewModelScope.launch(Dispatchers.Main) {
            timer = object : CountDownTimer(wait * 1000, 1000) {

                override fun onTick(millisUntilFinished: Long) {
                    _otpTimeout.postValue(millisUntilFinished / 1000)
                }

                override fun onFinish() {
                    _otpTimeout.postValue(0)
                    _canRequestOTPResend.postValue(true)
                }
            }.start()
        }
    }

    override fun onCleared() {
        super.onCleared()
        timer?.cancel()
    }

    private val _verifyOTPLoading = MutableLiveData(false)
    val verifyOTPLoading: LiveData<Boolean> = _verifyOTPLoading

    private val _verifyOTPError = MutableLiveData<String?>(null)
    val verifyOTPError: LiveData<String?> = _verifyOTPError

    val otp = MutableLiveData<String>("")

    val otpSubmitValid: LiveData<Boolean> = otp.map  {
        return@map it.orEmpty().trim().length== OTP_DIGITS
    }

    val onOTPVerified = LiveEvent<Boolean>()

    private val _otpLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(_requestOTPLoading) { med.postValue(_requestOTPLoading.value==true && _verifyOTPLoading.value==true) }
        med.addSource(_verifyOTPLoading) { med.postValue(_requestOTPLoading.value==true && _verifyOTPLoading.value==true) }
        med
    }
    val otpLoading: LiveData<Boolean> = _otpLoading

    fun setOTP(_otp: String) {
        if(otp.value==_otp) return
        otp.postValue(_otp)
        _verifyOTPError.postValue(null)
    }

    fun verifyOTP() {
        _verifyOTPLoading.postValue(true)
        _verifyOTPError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            val mode = _otpRequestMode.value!!
            val result: ResultOf<VerifyOTPResponse> = if (mode.isEmailType) {
                authRepo.verifyEmailOTP(mode, _email.value!!, otp.value!!)
            } else {
                authRepo.verifyMobileOTP(mode, _phoneNumber.value!!, _countryCode.value!!, otp.value!!)
            }
            when (result) {
                is ResultOf.Success -> {
                    onOTPVerified.postValue(true)
                }
                is ResultOf.APIError -> {
                    if (result.error.message.isNullOrEmpty()){
                        _verifyOTPError.postValue("Internal Server Error")
                    }else{
                        _verifyOTPError.postValue(result.error.message)
                    }
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
            _verifyOTPLoading.postValue(false)
        }
    }
}
