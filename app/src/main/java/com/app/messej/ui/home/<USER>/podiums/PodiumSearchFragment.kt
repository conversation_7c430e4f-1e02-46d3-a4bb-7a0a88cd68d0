package com.app.messej.ui.home.publictab.podiums

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentPodiumSearchBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoin
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.setAsDestructiveMenuItem
import com.app.messej.ui.utils.FragmentExtensions.setAsDestructiveMenuItemParams
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class PodiumSearchFragment : Fragment() {

    private lateinit var binding: FragmentPodiumSearchBinding
    private val viewModel : PodiumSearchViewModel by viewModels()

    protected var mAdapter: PodiumAdapter? = null

    private val args: PodiumSearchFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(layoutInflater, R.layout.fragment_podium_search, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        viewModel.setTab(args.tab)
        initAdapter()
        binding.customActionBar.apply {
            keyword = viewModel.searchKeyword
            showKeyboard(searchBox)
        }
    }

    private fun observe() {
        viewModel.podiumSearchList.observe(viewLifecycleOwner){
            it?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }
    }

    private fun initAdapter() {

        val isFromMyPodium = viewModel.podiumTab == PodiumTab.MY_PODIUM

        mAdapter = PodiumAdapter(viewModel.podiumTab,object: PodiumAdapter.PodiumActionListener {
            override fun onPodiumClicked(pod: Podium) {
//                if (!pod.isManager && !pod.isLive) {
//                    showToast(R.string.podium_error_not_live)
//                }
//                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(pod.id,pod.kind?.ordinal?:-1))
                joinPodium(pod)
            }

            override fun onEdit(pod: Podium) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePodiumFragment(pod.id))
            }

            override fun onAboutClicked(pod: Podium) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavAboutPodium(pod.id, pod.role?.isElevated!!))
            }

            override fun onPodiumMenuClicked(pod: Podium, view: View) {
                /*info*/
                val isElevatedRole = pod.role?.isElevated == true
                val isPremiumParticipant = viewModel.user.premium && (pod.isAudience || pod.isInvited)
                val isUserCitizenAndParticipant = viewModel.user.citizenship == UserCitizenship.CITIZEN && pod.isAudience
                val isAdmin = pod.role == Podium.PodiumUserRole.ADMIN
                val isAdminAmbassadorOrMinister = (isAdmin && viewModel.isAmbassadorOrMinister)
                val isUserCitizenAndAdmin = (viewModel.user.citizenship == UserCitizenship.CITIZEN && pod.isAdmin)


                val popup = PopupMenu(requireContext(), view)
                popup.inflate(R.menu.menu_podium_actions)
                popup.menu.apply {
/*                    findItem(R.id.action_info).isVisible = isElevatedRole || pod.isInvited || isPremiumParticipant
                    findItem(R.id.action_live_list).isVisible = pod.isLive && (isElevatedRole || viewModel.isAmbassadorOrMinister || isPremiumParticipant||pod.isPrivate)
                    findItem(R.id.action_admin).isVisible = pod.isManager || viewModel.isAmbassadorOrMinister
                    findItem(R.id.action_remove).isVisible = ((pod.isLive&& viewModel.user.premiumUser)&&(isAdmin||pod.isInvited) || pod.isInvited||isAdmin||isUserCitizenAndParticipant)*/
//                    findItem(R.id.action_withdraw_from_admin).isVisible=pod.isAdmin
                    findItem(R.id.action_end_podium)?.let { endPodiumItem ->
                        endPodiumItem.setAsDestructiveMenuItem(requireContext())
                    }
                    findItem(R.id.action_info).isVisible = false
                    findItem(R.id.action_join).isVisible = !isFromMyPodium && !pod.isInvited
                    findItem(R.id.action_live_list).isVisible = pod.isLive && (isElevatedRole || viewModel.isAmbassadorOrMinister || isPremiumParticipant || pod.isPrivate) && pod.hideLiveUsers==false
                    findItem(R.id.action_admin).isVisible = pod.isManager || isAdminAmbassadorOrMinister
//                    findItem(R.id.action_remove).isVisible = ((pod.isLive && viewModel.user.premiumUser) && (isAdmin || pod.isInvited) || pod.isInvited || isUserCitizenAndAdmin||isAdminAmbassadorOrMinister ||  (pod.isInvited && viewModel.user.premium == false) )
                    findItem(R.id.action_end_podium).isVisible = (viewModel.user.userEmpowerment?.allowEndPodium == true  || viewModel.iAmMinister == true) && !isFromMyPodium
                    findItem(R.id.you_are_member)?.setAsDestructiveMenuItemParams(requireContext(),getString(R.string.podium_you_are_member),false)
                    findItem(R.id.join_as_member)?.setAsDestructiveMenuItemParams(requireContext(),getString(R.string.podium_join_as_member),true)
                    findItem(R.id.you_are_member).isVisible =((pod.isLive && viewModel.user.premiumUser) && (isAdmin || pod.isInvited) || pod.isInvited || isUserCitizenAndAdmin||isAdminAmbassadorOrMinister || (pod.isInvited && !viewModel.user.premium)||(pod.isPrivate && pod.isInvited))
                    findItem(R.id.join_as_member).isVisible = (isElevatedRole  || isPremiumParticipant || !viewModel.user.premium || pod.isInvitee) && pod.isLive && pod.isInvited
                    findItem(R.id.action_yalla_guys).isVisible = pod.kind == PodiumKind.LECTURE && pod.isLive && viewModel.podiumTab == PodiumTab.LIVE_PODIUM
                }
                popup.setOnMenuItemClickListener { item ->
                    when (item?.itemId) {
                        R.id.action_podium_info -> findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavAboutPodium(pod.id, pod.role?.isElevated!!,true))
                        R.id.action_join -> joinPodium(pod)
                        R.id.action_live_list -> findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicLiveUsersListBottomSheet(podiumId = pod.id))
                        R.id.action_admin -> {
                            Log.d("CITIZENCURRENT", "CURENT user citizen: " + viewModel.user.citizenship)
                            val actionKebab= pod.isAdmin|| viewModel.isAmbassadorOrMinister || isAdminAmbassadorOrMinister
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicAdminBottomSheet(podiumId = pod.id, actionKebab))
                        }

//                        R.id.action_remove -> confirmDeclineInvitation(pod)
                        R.id.join_as_member -> joinPodium(pod)
                        R.id.you_are_member -> confirmDeclineInvitation(pod)
//                        R.id.action_withdraw_from_admin ->viewModel.withDrawAsAdmin(viewModel.user.id, pod.id)
                        R.id.action_yalla_guys -> {
                            ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.CreateYallaGuys) {
                                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalYallaGuysCreateFragment(podiumId = pod.id,false))
                            }
                        }
                    }
                    return@setOnMenuItemClickListener true
                }
                popup.show()
            }
            override fun onPodiumDpClicked(pod: Podium) {
                if (pod.isInvited) {
                    confirmInvitation(pod)
                }
            }
        }).apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
        }
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            image = R.drawable.im_eds_podium,
            message = R.string.podium_empty_search,
        )
        val layoutMan = LinearLayoutManager(context)
        binding.podiumSearchList.apply {
            layoutManager = layoutMan
            setHasFixedSize(false)
            adapter = mAdapter
        }
    }

    private fun joinPodium(pod: Podium) {
        if (pod.isLive) {
            validateAndConfirmJoin(pod, viewModel.user) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(pod.id,pod.kind?.ordinal?:-1))
            }
        } else if (pod.shouldGoLive) {
            confirmGoLive { findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(pod.id,pod.kind?.ordinal?:-1)) }
        }
    }

    private fun confirmGoLive(confirm: () -> Unit) {
        confirmAction(
            title = R.string.podium_action_go_live_confirm_title,
            message = R.string.podium_action_go_live_confirm_message,
            positiveTitle = R.string.common_confirm,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    private fun confirmDeclineInvitation(pod: Podium) {
        if (pod.isInvited) {
            confirmAction(
                title = null, message = getString(R.string.podium_decline_invitation), positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
            ) {
                viewModel.declinePodiumInvitation(podiumId = pod.id)
            }
        } else if (pod.shouldGoLive && pod.role == Podium.PodiumUserRole.ADMIN) {
            confirmAction(
                title = null, message = getString(R.string.podium_dismiss_admin), positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
            ) {
                viewModel.withDrawAsAdmin(viewModel.user.id, pod.id)
            }
        }
    }

    private fun confirmInvitation(pod: Podium) {
        confirmAction(
            title = null, message = getString(R.string.podium_invitation_accept), positiveTitle = R.string.common_accept, negativeTitle = R.string.common_decline
        ){
            joinPodium(pod)
        }
    }
}