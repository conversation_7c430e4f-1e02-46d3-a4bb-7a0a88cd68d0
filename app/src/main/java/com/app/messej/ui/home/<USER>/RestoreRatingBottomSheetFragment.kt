package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.DealsBeneficiary
import com.app.messej.data.model.enums.RestoreType
import com.app.messej.databinding.FragmentRestoreRatingBottomSheetBaseBinding
import com.app.messej.databinding.LayoutPodiumCameraDisabledBinding
import com.app.messej.ui.home.businesstab.adapter.DealsBeneficiaryAdapter
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import java.util.Locale

class RestoreRatingBottomSheetFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentRestoreRatingBottomSheetBaseBinding
    private val viewModels: BusinessDealsListViewModel by viewModels()
     private val args: RestoreRatingBottomSheetFragmentArgs by navArgs()


    companion object {
        const val RESTORE_RATING_SUCCESS_REQUEST = "restore_rating_success_request"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_restore_rating_bottom_sheet_base, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModels
        binding.isResident =args.isResident
        return binding.root
    }

     override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
         super.onViewCreated(view, savedInstanceState)
         addAsMenuHost()
         setup()
         observe()
     }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.restore_rating_header)
    }

     private fun setup() {
         binding.icClose.setOnClickListener {
             clearBeneficiary()
         }

         binding.restoreRatingButton.setOnClickListener {
             if(viewModels.restoreRatingDetails.value?.sufficientBalance == true){
                 proceedToRestore(
                     title = if (viewModels.selectedRestoreRating.value == RestoreType.FOR_ME) {
                         getString(R.string.restore_rating_purchase_confirmation, String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.restoratingFlix))
                     } else {
                         getString(
                             R.string.restore_rating_purchase_confirmation_other_user,
                             String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.restoratingFlix),
                             viewModels.beneficiary.value?.name
                         )
                     }
                 )
             } else{
                 if(viewModels.restoreRatingDetails.value?.sufficientEffectiveBalance == true){
                     proceedToRestore(title =
                                          if (viewModels.selectedRestoreRating.value == RestoreType.FOR_ME) {
                                              getString(R.string.restore_effective_flix_confirmation,String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.flixBalance),String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.coinAmount))

                                          }else{
                                              getString(R.string.restore_effective_flix_confirmation_other_user,String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.flixBalance),String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.coinAmount), viewModels.beneficiary.value?.name)

                                          })


                 }else{
                     showRequiredFlixPopUp()
                 }
             }
         }
         binding.restoreRatingHistory.setOnClickListener {
             findNavController().navigateSafe(RestoreRatingBottomSheetFragmentDirections.actionGlobalRestoreRatingHistory())
         }
     }

    private fun observe(){
        viewModels.beneficiary.observe(viewLifecycleOwner){
            it?.id.let { userId->
                viewModels.getRestoreRating(userId)
            }

        }

        val textInputEditText = binding.textSearchBeneficiary.editText as AutoCompleteTextView
        textInputEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val searchText = s.toString()
                if (searchText.isNotEmpty()) {
                    viewModels.getBeneficiaryList(keyword = searchText)
                }

            }

            override fun afterTextChanged(s: Editable?) {
            }
        })

        viewModels.dealsBeneficiaryList.observe(viewLifecycleOwner) {
            if (it != null) {
                val adapter = DealsBeneficiaryAdapter(requireContext(), it)
                (binding.textSearchBeneficiary.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    adapter.notifyDataSetChanged()
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText("")
                        onBeneficiarySelected(item)
                    }
                }
            }
        }



       viewModels.onRestoreRatingSuccess.observe(viewLifecycleOwner){
           if(viewModels.selectedRestoreRating.value== RestoreType.FOR_ME){
               Toast.makeText(requireContext(), getString(R.string.restore_rating_your_rating_is_restored), Toast.LENGTH_SHORT).show()
           }else{
               Toast.makeText(requireContext(), getString(R.string.restore_rating_friend_rating,viewModels.beneficiary.value?.name), Toast.LENGTH_SHORT).show()
           }

           requireActivity().supportFragmentManager.setFragmentResult(RESTORE_RATING_SUCCESS_REQUEST, bundleOf())
           findNavController().popBackStack()
       }
   }

    private fun onBeneficiarySelected(item: DealsBeneficiary) {
        viewModels.beneficiary.postValue(item)
    }

    private fun proceedToRestore(title:String) {
        showFlashatDialog {
            setMessage(title)
            setConfirmButton(R.string.common_proceed) {
                if(viewModels.isDoubleClickPrevent){
                    viewModels.isDoubleClickPrevent =false
                    viewModels.purchaseRate()
                }
                true
            }
            setCloseButton(R.string.common_cancel){
                viewModels.isDoubleClickPrevent = true
                true
            }
        }



    }


    private fun showRequiredFlixPopUp() {
        showFlashatDialog {
            setMessage(getString(R.string.restore_rating_error,String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.flixBalance),String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.restoratingFlix),String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.requiredFlix)))
            setConfirmButton(R.string.common_confirm, icon = R.drawable.ic_flax_coin) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = false))
                true
            }
        }
    }


    fun restoreRatingInfo(header: String) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumCameraDisabledBinding>(layoutInflater, R.layout.layout_podium_camera_disabled, null, false)
            view.textHeader = header
            val header = view.layoutHeader
            header.setPadding(header.paddingLeft, header.paddingTop, header.paddingRight,2)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
        }
    }

    private fun clearBeneficiary() {
        binding.textSearchBeneficiary.editText?.setText("")
        viewModels.beneficiary.postValue(null)

    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_restore_rating, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            android.R.id.home -> {
                findNavController().popBackStack()
            }

            R.id.menu_restore_rating_info->{
                restoreRatingInfo(resources.getString(R.string.restore_rating_note))
            }
        }
        return true

    }

}