package com.app.messej.ui.home.publictab.postat.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.Postat
import com.app.messej.databinding.FragmentPostatDraftMediaBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.publictab.postat.mypostat.MyPostatListAdapter
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.isTabletScreen
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class PostatDraftMediaFragment : Fragment() {

    private var mAdapter: MyPostatListAdapter? = null

    val viewModel: CreatePostatViewModel by navGraphViewModels(R.id.nav_create_postat)

    private lateinit var binding: FragmentPostatDraftMediaBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_draft_media, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun setup() {
        setEmptyView()
        initAdapter()
    }

    override fun onResume() {
        super.onResume()
        mAdapter?.refresh()
    }

    private fun observer() {
        viewModel.draftPostatList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }
    }

    fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            message = R.string.postat_draft_eds_message
        )
    }

    private fun initAdapter() {
        mAdapter = MyPostatListAdapter(object : MyPostatListAdapter.PostatClickListener {
            override fun onMyPostatClicked(pos: Int, postat: Postat) {
                if (postat.type == Postat.PostatType.DRAFT) {
                    val options = NavOptions.Builder().setPopUpTo(R.id.nav_create_postat, inclusive = true).build()
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePostat(postat.messageId), options)
                }
            }

            override fun onAddPostatClicked() {
            }

            override fun onUploadPostatClicked() {
            }
        })

        binding.draftList.apply {
            layoutManager = GridLayoutManager(requireContext(), if(isTabletScreen) 5 else 3)
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
//                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = if (itemCount < 1 &&  loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
//                viewModel.setViewState(state)
//                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }


}