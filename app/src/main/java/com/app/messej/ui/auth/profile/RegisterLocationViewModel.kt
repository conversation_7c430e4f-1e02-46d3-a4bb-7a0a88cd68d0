package com.app.messej.ui.auth.profile

import android.app.Application
import android.util.Log
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.GeoLocationResponse
import com.app.messej.data.model.api.profile.RegisterLocationRequest
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.auth.common.BaseLocationSetViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class RegisterLocationViewModel(application: Application) : BaseLocationSetViewModel(application) {

    override fun confirmLocation() {
        val loc = selectedLocation.value
        if (loc != null) {
            _confirmLocationLoading.postValue(true)
            viewModelScope.launch(Dispatchers.IO) {
                val req: RegisterLocationRequest = loc
                req.name = null
                when (val result: ResultOf<CurrentUser> =
                    profileRepo.setLocation(req)) {
                    is ResultOf.Success -> {
                        println(result.value.toString())
                        onLocationConfirmed.postValue(true)
                        _confirmLocationLoading.postValue(false)
                    }
                    is ResultOf.APIError -> {
                        _confirmLocationLoading.postValue(false)
                        _setLocationError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {
                        _confirmLocationLoading.postValue(false)
                    }
                }

            }
        } else {
            getFallBackLocation()
        }
    }

    private fun getFallBackLocation(){
        Log.d("BLSF", "getFallBackLocation")
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<GeoLocationResponse> =
                profileRepo.getGeoLocation()) {
                is ResultOf.Success -> {
                    setAddressFromGeoCoder(result.value.location.lat, result.value.location.lng) { loc ->
                        loc.locationSkipped = true
                        onSelectLocation(loc)
                        _selectedLocation.postValue(loc)
                        confirmLocation()
                    }
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }
}