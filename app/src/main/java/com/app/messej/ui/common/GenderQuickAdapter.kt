package com.app.messej.ui.common

import com.app.messej.R
import com.app.messej.databinding.ItemGenderListBinding
import com.app.messej.ui.auth.common.GenderUIPackage
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class GenderQuickAdapter: BaseQuickAdapter<GenderUIPackage, BaseDataBindingHolder<ItemGenderListBinding>>(R.layout.item_gender_list, GenderUIPackage.getGenderUIList().toMutableList()) {

    override fun convert(holder: BaseDataBindingHolder<ItemGenderListBinding>,
                         item: GenderUIPackage
    ) {
        holder.dataBinding?.apply {
            gender = item
        }
    }
}