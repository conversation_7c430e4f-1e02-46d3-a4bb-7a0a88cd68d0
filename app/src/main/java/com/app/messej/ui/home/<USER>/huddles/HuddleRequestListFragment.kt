package com.app.messej.ui.home.publictab.huddles

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.databinding.FragmentHuddleRequestListBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class HuddleRequestListFragment : Fragment() {

    private val viewModel: PublicHuddlesViewModel by activityViewModels()

    private var mAdapter: PublicHuddlesAdapter? = null

    private lateinit var binding: FragmentHuddleRequestListBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_request_list, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
        setup()
        initAdapter()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
    }

    override fun onResume() {
        super.onResume()
        mAdapter?.refresh()
        viewModel.getHuddlesRequestCount()
    }

    fun setup() {
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            LayoutListStateEmptyBinding.bind(this).prepare(
                image = R.drawable.im_eds_huddle_requests,
                message = R.string.public_huddle_requests_eds,
            )
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = PublicHuddlesAdapter(layoutInflater,viewModel.user.id, object: PublicHuddlesAdapter.ItemListener {
            override fun onItemClick(item: AbstractHuddle, position: Int) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChatHuddle(item.id))
            }

            override fun onItemLongClick(item: PublicHuddle, position: Int) {

            }

            override fun isVisitor(): Boolean {
               return true
            }

        })

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }

        val layoutMan = LinearLayoutManager(context)

        binding.huddleRequestList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

    }

    private fun observe() {
        viewModel.huddleRequests.observe(viewLifecycleOwner) {
            it ?: return@observe
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }
}