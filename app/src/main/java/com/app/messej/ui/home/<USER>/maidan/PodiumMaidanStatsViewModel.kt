package com.app.messej.ui.home.publictab.maidan

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.podium.challenges.MyStats
import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PodiumMaidanStatsViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)

    private val _myStats = MutableLiveData<MyStats>()
    val myStats: LiveData<MyStats> = _myStats

    private val _competitorStats = MutableLiveData<List<PodiumMaidanSupporter>>(mutableListOf())
    val competitorStats: LiveData<List<PodiumMaidanSupporter>> = _competitorStats

    private val _challengeHistory = MutableLiveData<List<PodiumMaidanSupporter>>(mutableListOf())
    val challengeHistory: LiveData<List<PodiumMaidanSupporter>> = _challengeHistory

    private val _maidanStatsLoading = MutableLiveData(true)
    val maidanStatsLoading: LiveData<Boolean> = _maidanStatsLoading

    val challengeHistoryPager = podiumRepository.getMaidanChallengeHistoryPager().liveData.cachedIn(viewModelScope)
    val competitorStatsPager = podiumRepository.getMaidanCompetitorStatsPager().liveData.cachedIn(viewModelScope)

    fun getMaidanStatsSummary() {
        _maidanStatsLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.getMaidanStatsSummary()) {
                    is ResultOf.Success -> {
                        _myStats.postValue(result.value.myStats)
                        _competitorStats.postValue(result.value.competitorStats.data)
                        _challengeHistory.postValue(result.value.challengeHistory.data)

                    }

                    else -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumMaidanStatsVM", "maidanStatsSummary: error: ${e.message}")
            }
            _maidanStatsLoading.postValue(false)
        }
    }
}