package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.data.model.api.DealsBeneficiary
import com.app.messej.databinding.ItemUserPlainBinding

class ContributorSearchAdapter(c: Context, list: List<DealsBeneficiary>) : ArrayAdapter<DealsBeneficiary>(c, R.layout.item_user_plain, list) {
    val mContext = c
    val mList = list
    override fun getItem(position: Int): DealsBeneficiary {
        return mList[position]
    }

    override fun getCount(): Int {
        return mList.size
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {

        var cView = convertView
        val binding = if (convertView == null) {
            val inflater = (mContext as Activity).layoutInflater
            DataBindingUtil.inflate<ItemUserPlainBinding>(inflater, R.layout.item_user_plain, parent, false).apply {
                cView = root
            }
        } else {
            DataBindingUtil.bind(convertView)
        }
        try {
            val item = getItem(position)
            binding?.apply {
                user = item
                showUsername = false
                clickable = false
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return cView!!
    }
}