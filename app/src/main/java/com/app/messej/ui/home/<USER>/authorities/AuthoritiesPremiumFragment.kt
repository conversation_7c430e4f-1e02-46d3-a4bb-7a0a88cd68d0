package com.app.messej.ui.home.publictab.authorities

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentAuthoritiesPremiumBinding
import com.app.messej.databinding.FragmentBaseAuthoritiesBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindGiftRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindLegalAffairsPayFineButton
import com.app.messej.ui.utils.FragmentExtensions.bindMaidanToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.setBadgeNumber
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils

class AuthoritiesPremiumFragment : AuthoritiesBaseFragment(), MenuProvider {

    override lateinit var binding: FragmentBaseAuthoritiesBinding
    private lateinit var outerBinding: FragmentAuthoritiesPremiumBinding
    private val homeViewModel: CommonHomeViewModel by activityViewModels()
    private var notificationBadge: BadgeDrawable? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_authorities_premium, container, false)
        binding = outerBinding.authoritiesCommonLayout
        outerBinding.lifecycleOwner = viewLifecycleOwner
        return outerBinding.root
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_home_notifications, menu)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(outerBinding.actionBarPremium.toolbar, customBackButton = false)
        bindFlaxRateToolbarChip(outerBinding.actionBarPremium.flaxRateChip)
        bindGiftRateToolbarChip(outerBinding.actionBarPremium.giftChip)
        bindMaidanToolbarChip(outerBinding.actionBarPremium.maidanChip)
        bindLegalAffairsPayFineButton(outerBinding.actionBarPremium.payFineChip)
    }

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        notificationBadge = BadgeDrawable.create(requireContext())
        setBadgeNumber(notificationBadge,homeViewModel.unreadNotifications.value)
        notificationBadge?.apply {
            adjustForNotifications(requireContext())
            BadgeUtils.attachBadgeDrawable(this, outerBinding.actionBarPremium.toolbar, R.id.action_notifications)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_notifications -> findNavController().navigateSafe(direction = AuthoritiesPremiumFragmentDirections.actionGlobalNotificationFragment())
            else -> return false
        }
        return true
    }

    override fun onPresidentialAffairsClick() {
    }

    override fun onLegalAffairsClick() {
        findNavController().navigateSafe(
            AuthoritiesPremiumFragmentDirections.actionGlobalLegalAffairsFragment()
        )
    }

    override fun onSocialWelfareClick() {
        findNavController().navigateSafe(
            direction = AuthoritiesPremiumFragmentDirections.actionAuthoritiesPremiumFragmentToSocialHomeFragment()
        )
    }

    override fun onStateAffairsClick() {
        findNavController().navigateSafe(
            AuthoritiesStandAloneFragmentDirections.actionAuthoritiesFragmentToStateAffairsFragment()
        )
    }

    override fun onNavigateToLegalAffairsClick() {
        findNavController().navigateSafe(
            AuthoritiesPremiumFragmentDirections.actionGlobalLegalAffairsFragment()
        )
    }
}