package com.app.messej.ui.home.publictab.myETribe.contactETribe

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.ContactETribeRequest
import com.app.messej.data.model.enums.ETribeContactOptions
import com.app.messej.data.model.enums.ETribeTabs
import com.app.messej.data.repository.ETribeRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.composeComponents.ComposeTextFieldState
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ContactETribeViewModel(application: Application) : AndroidViewModel(application) {

    private val repository = ETribeRepository(application)

    val messageTextFieldState = ComposeTextFieldState()
    val messageLiveData = MutableLiveData<String?>(null)

    private val _selectedDropdown = MutableLiveData<ETribeTabs?>(null)
    val selectedDropdown: LiveData<ETribeTabs?> = _selectedDropdown

    private val _contactETribeOptions = MutableLiveData<List<ETribeContactOptions>?>(null)
    val contactETribeOptions : LiveData <List<ETribeContactOptions>?> = _contactETribeOptions

    val dropdownList = ETribeTabs.entries.toList()
    val contactETribeList = ETribeContactOptions.entries.toList()

    private val _isSubmitting = MutableLiveData(false)
    val isSubmitting : LiveData<Boolean> = _isSubmitting

    val errorMessage = LiveEvent<String?>()
    val isAPISuccess = LiveEvent<Boolean>()

    val isSNextButtonEnabled : MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun checkInputsValid() {
            med.postValue(
                !messageLiveData.value.isNullOrEmpty() &&
                messageLiveData.value?.isNotBlank() == true &&
                _selectedDropdown.value != null &&
                !_contactETribeOptions.value.isNullOrEmpty() &&
                _isSubmitting.value == false
            )
        }
        med.addSource(_selectedDropdown) { checkInputsValid() }
        med.addSource(_contactETribeOptions) { checkInputsValid() }
        med.addSource(messageLiveData) { checkInputsValid() }
        med
    }

    fun setDropdown(item: ETribeTabs) {
        _selectedDropdown.postValue(item)
    }

    fun setApiLoading(isLoading: Boolean) {
        _isSubmitting.postValue(isLoading)
    }

    fun addContactETribeOptions(item: ETribeContactOptions) {
        if (_contactETribeOptions.value?.contains(item) == true) return
        val tempList = (_contactETribeOptions.value?: emptyList()).toMutableList()
        tempList.add(element = item)
        _contactETribeOptions.value = tempList
    }

    fun removeContactETribeOption(item: ETribeContactOptions) {
        if (_contactETribeOptions.value?.contains(item) == false) return
        val tempList = (_contactETribeOptions.value?: emptyList()).toMutableList()
        tempList.remove(item)
        _contactETribeOptions.value = tempList
    }

    fun setMessageText(text: String?) {
        messageLiveData.postValue(text)
    }

    fun contactETribe() {
        viewModelScope.launch(Dispatchers.IO) {
            setApiLoading(isLoading = true)
            val contactMethodFormatted = _contactETribeOptions.value?.joinToString(",") { it.serializedName() }
            val req = ContactETribeRequest(
                content = messageTextFieldState.text,
                memberType = _selectedDropdown.value,
                contactMethods = contactMethodFormatted
            )
            val response = repository.contactETribe(req)
            when(response) {
                is ResultOf.Success -> {
                    isAPISuccess.postValue(true)
                }
                else -> errorMessage.postValue(response.errorMessage())
            }
            setApiLoading(isLoading = false)
        }
    }
}