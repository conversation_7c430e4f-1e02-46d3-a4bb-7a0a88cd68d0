package com.app.messej.ui.home.gift

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.gift.PointsPurchase
import com.app.messej.databinding.FragmentGiftListingBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.kennyc.view.MultiStateView


class PurchaseCoinsListingFragment : Fragment() {

    private lateinit var binding: FragmentGiftListingBinding
    var mAdapter:PurchaseListAdapter? = null
    private val viewModel: GiftCommonViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_listing, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }
    private  fun observe() {
        viewModel.purchaseHistoryLoading.observe(viewLifecycleOwner){
            if (it){
                binding.multiStateView.viewState = MultiStateView.ViewState.LOADING
            }else{
                binding.multiStateView.viewState = MultiStateView.ViewState.CONTENT
            }
        }


        viewModel.giftListData.observe(viewLifecycleOwner){
            mAdapter?.apply {
                if(it?.packagesList?.isEmpty()== true){
                    binding.multiStateView.viewState = MultiStateView.ViewState.EMPTY
                }
                if (data.size == 0 || it?.packagesList?.isEmpty()  == false) {
                    setNewInstance(it?.packagesList?.toMutableList())
                } else {
                    setDiffNewData(it?.packagesList?.toMutableList())
                }
            }
        }
    }
    private fun setup() {
        initAdapter()
        binding.buttonPurchaseHistory.setOnClickListener {
            findNavController().navigateSafe(GiftFragmentDirections.actionGiftFragmentToPurchaseHistoryFragment())
        }
    }

    private fun initAdapter() {
        mAdapter = PurchaseListAdapter(mutableListOf(), object : PurchaseListAdapter.ItemClickListener{

            override fun onClick(packages: PointsPurchase, position: Int) {
                if (packages.flax!! < viewModel.giftListData.value?.flax!!) {
                    findNavController().navigateSafe(GiftFragmentDirections.actionGiftFragmentToFlaxToPointsConvertDialogFragment(packages.points.toString(), packages.flax.toString()))
                } else {
                    Toast.makeText(requireContext(), resources.getString(R.string.flax_to_point_conversion_minimum_point_unavailable), Toast.LENGTH_SHORT).show()
                }
            }
        })
        val layoutMan = LinearLayoutManager(context)
        binding.listGifts.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true
            setDiffCallback(PurchaseListAdapter.GiftDiff)
        }
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.no_transactions_made)
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_gift_empty)
        }


    }

    }

