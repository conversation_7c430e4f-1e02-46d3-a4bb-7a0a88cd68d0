package com.app.messej.ui.chat

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.IntentSender
import android.location.Location
import android.os.Bundle
import android.text.InputType
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.databinding.FragmentAttachLocationBinding
import com.app.messej.ui.auth.common.LocationSearchFragment
import com.app.messej.ui.auth.common.NearbyLocationAdapter
import com.app.messej.ui.utils.MapUtils
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkLocationPermission
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.Granularity
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.Priority
import com.google.android.gms.location.SettingsClient
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.CircularBounds
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.api.net.FetchPlaceRequest
import com.google.android.libraries.places.api.net.PlacesClient
import com.google.android.libraries.places.api.net.SearchNearbyRequest
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeUnit

abstract class BaseLocationAttachFragment : Fragment(), OnMapReadyCallback {

    abstract val viewModel: BaseChatViewModel
    protected val locationViewModel: LocationAttachViewModel by viewModels()

    protected var mAdapter: NearbyLocationAdapter? = null
    private var map: GoogleMap? = null
    private lateinit var placesClient: PlacesClient
    private lateinit var fusedLocationProviderClient: FusedLocationProviderClient

    protected lateinit var binding: FragmentAttachLocationBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_attach_location, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = locationViewModel
        return binding.root
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        MapUtils.initPlacesClient()
        fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(context)
        placesClient = Places.createClient(this.requireContext())
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()

        binding.textBox.editText?.inputType = InputType.TYPE_NULL
        binding.textBox.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    navigateToSearch()
                }
            }
        }

        binding.textBox.setStartIconOnClickListener {
            findNavController().popBackStack()
        }

        binding.shareCurrentLocation.setOnClickListener {
            checkLocationPermission(binding.root) {
                Log.d("CURLOC", "shareCurrentLocation: has permission")
                getCurrentLocation { loc ->
                    Log.d("CURLOC", "shareCurrentLocation: got location")
                    viewModel.attachLocation(loc.latitude, loc.longitude)
                    findNavController().popBackStack()
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        checkLocationPermission(binding.root) {
            loadMap()
        }
    }

    private fun loadMap() {
        if(map != null) return
        val mapFragment = childFragmentManager.findFragmentById(binding.map.id) as SupportMapFragment?
        mapFragment!!.getMapAsync(this)
    }

    private fun moveMapToLocation(loc: LatLng) {
        map?.apply {
            animateCamera(CameraUpdateFactory.newLatLngZoom(loc, 13f))
            clear()
            addMarker(
                MarkerOptions().position(loc).visible(true).draggable(true).flat(true).icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_map_marker))
            )
        }
    }

    private fun observe() {
        locationViewModel.nearbyPlaces.observe(viewLifecycleOwner) {
            mAdapter?.apply {
                if (data.isEmpty() || it.isEmpty()) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        setFragmentResultListener(LocationSearchFragment.LOCATION_SEARCH_REQUEST_KEY) { _, bundle ->
            bundle.getString(LocationSearchFragment.LOCATION_SEARCH_RESULT_KEY)?.let { placeId ->
                if(placeId.trim().isEmpty()) return@let
                if (placeId == LocationSearchFragment.LOCATION_SEARCH_RESULT_CURRENT) {
                    locationViewModel.searchLocationName.postValue("")
                    setToCurrentLocation()
                } else {
                    val placeFields = listOf(Place.Field.ID, Place.Field.DISPLAY_NAME, Place.Field.LOCATION, Place.Field.FORMATTED_ADDRESS)
                    val placeRequest = FetchPlaceRequest.builder(placeId, placeFields).build()
                    MapUtils.initPlacesClient()
                    placesClient.fetchPlace(placeRequest).addOnCompleteListener { place ->
                        try {
                            val res = place.result
                            if (res != null) {
                                locationViewModel.searchLocationName.postValue(res.place.displayName)
                                res.place.location?.let { loc ->
                                    moveMapToLocation(loc)
                                    searchNearby(loc)
                                }
                            }
                        }
                        catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }

            }
        }
    }

    private fun searchNearby(loc: LatLng) {
        val placeFields = listOf(
            Place.Field.ID,
            Place.Field.DISPLAY_NAME,
            Place.Field.ICON_MASK_URL,
            Place.Field.ICON_BACKGROUND_COLOR,
            Place.Field.LAT_LNG
        )
        val bounds = CircularBounds.newInstance(loc, 5000.0) // radius in meters

        val request = SearchNearbyRequest.builder(bounds, placeFields)
            .setMaxResultCount(20)
            .build()

        placesClient.searchNearby(request)
            .addOnSuccessListener { response ->
                locationViewModel.setNearbyPlaces(response.places)
                // Handle the list of Place objects
            }
            .addOnFailureListener { exception ->
                // Handle error
            }
    }

    private fun setToCurrentLocation() {
        try {
            checkLocationPermission(binding.root) {
                getCurrentLocation { loc ->
                    moveMapToLocation(LatLng(loc.latitude, loc.longitude))
                    searchNearby(LatLng(loc.latitude, loc.longitude))
                }
            }
        } catch (e: SecurityException) {
            Log.e("Exception: %s", e.message, e)
        }
    }

    private fun initAdapter() {
        mAdapter = NearbyLocationAdapter(mutableListOf())

        binding.nearByPlacesRv.apply {
            setHasFixedSize(true)
            adapter = mAdapter
        }
        mAdapter!!.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true
            setDiffCallback(NearbyLocationAdapter.DiffCallback())

            setOnItemClickListener { adapter, _, position ->
                val data = (adapter as NearbyLocationAdapter).data[position]
                val latitude = data.location?.latitude?:return@setOnItemClickListener
                val longitude = data.location?.longitude?:return@setOnItemClickListener
                val address = data.displayName
                viewModel.attachLocation(latitude, longitude, address)
                findNavController().popBackStack()
            }
        }
    }

    override fun onMapReady(googleMap: GoogleMap) {
        map = googleMap
        setToCurrentLocation()
    }

    fun navigateToSearch() {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalLocationSearchFragment())
    }

    @SuppressLint("MissingPermission")
    fun getCurrentLocation(callback: (Location) -> Unit) {
        confirmLocationIsEnabled {
            lifecycleScope.launch {
                try {
                    val loc = fusedLocationProviderClient.getCurrentLocation(Priority.PRIORITY_BALANCED_POWER_ACCURACY, null).await()
                    if (loc != null) {
                        Log.d("CURLOC", "getLastLocation: got current Location")
                        callback.invoke(loc)
                    } else {
                        Firebase.crashlytics.recordException(Exception("Location was null for some reason"))
                        Log.d("CURLOC", "confirmLocationIsEnabled: Location was null for some reason")
                    }
                } catch (e: ExecutionException) {
                    Firebase.crashlytics.recordException(e)
                    Log.e("CURLOC", e.message, e)
                } catch (e: InterruptedException) {
                    Firebase.crashlytics.recordException(e)
                    Log.e("CURLOC", e.message, e)
                }
            }
        }
    }

    private val locationEnableResult = registerForActivityResult(
        ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        Log.d("CURLOC", "locationEnableResult: got result. location enabled? ${activityResult.resultCode == Activity.RESULT_OK}")
        if (activityResult.resultCode == Activity.RESULT_OK) {
            lifecycleScope.launch {
                delay(1000)
                setToCurrentLocation()
            }
        }
    }

    private fun confirmLocationIsEnabled(callback: () -> Unit) {
        val locationRequest = LocationRequest.Builder(
            Priority.PRIORITY_HIGH_ACCURACY, TimeUnit.MINUTES.toMillis(5)
        ).apply {
            setGranularity(Granularity.GRANULARITY_PERMISSION_LEVEL)
            setDurationMillis(TimeUnit.MINUTES.toMillis(5))
            setWaitForAccurateLocation(true)
            setMaxUpdates(1)
        }.build()

        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)

        val client: SettingsClient = LocationServices.getSettingsClient(requireActivity())
        Firebase.crashlytics.log("Checking if location is enabled")
        Log.d("CURLOC", "confirmLocationIsEnabled: Checking if location is enabled")
        val task = client.checkLocationSettings(builder.build())
        task.addOnSuccessListener {
            // All location settings are satisfied. The client can initialize
            // location requests here.
            Firebase.crashlytics.log("Location is enabled!!")
            Log.d("CURLOC", "confirmLocationIsEnabled: Location is enabled!!")
            callback.invoke()
        }

        task.addOnFailureListener { exception ->
            Firebase.crashlytics.log("Location is not enabled")
            Log.d("CURLOC", "confirmLocationIsEnabled: Location is not enabled")
            if (exception is ResolvableApiException) {
                // Location settings are not satisfied, but this can be fixed
                // by showing the user a dialog.

                Firebase.crashlytics.log("Exception is resolvable")
                Log.d("CURLOC", "confirmLocationIsEnabled: Exception is resolvable")
                try {
                    // Show the dialog by calling startResolutionForResult(),
                    // and check the result in onActivityResult().
                    val intentSenderRequest = IntentSenderRequest.Builder(exception.resolution).build()
                    locationEnableResult.launch(intentSenderRequest)
                } catch (sendEx: IntentSender.SendIntentException) {
                    // Ignore the error.
                    Firebase.crashlytics.recordException(sendEx)
                }
            }
        }

    }

}