package com.app.messej.ui.home.publictab.podiums.challenges.knowledgerace

import android.graphics.drawable.VectorDrawable
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.ImageShader
import androidx.compose.ui.graphics.ShaderBrush
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.min
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import coil3.compose.AsyncImage
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.model.enums.UserCitizenship
import kotlinx.coroutines.delay
import kotlin.math.roundToInt

@Preview
@Composable
fun KnowledgeRaceBoardPreview() {
//    Box(Modifier.padding(top = 50.dp).fillMaxWidth().fillMaxHeight(0.6f)) {
    var state = remember { mutableStateOf(listOf(
        PodiumChallengeScore(
            id = 1,
            score = 0.0,
            name = "John",
            username = "johnd",
            participantTokenNumber = 1,
            citizenship = UserCitizenship.CITIZEN,
            isPremium = false,
            thumbnail = "https://i.pravatar.cc/150?img=3"
        ),
        PodiumChallengeScore(
            id = 2,
            score = 5.0,
            name = "Mary Jane",
            username = "janed",
            participantTokenNumber = 2,
            citizenship = UserCitizenship.CITIZEN,
            isPremium = true,
            thumbnail = "https://i.pravatar.cc/150?img=4"
        ),
        PodiumChallengeScore(
            id = 3,
            score = 10.0,
            name = "Shahim M",
            username = "janed",
            participantTokenNumber = 3,
            citizenship = UserCitizenship.CITIZEN,
            isPremium = true,
            thumbnail = "https://i.pravatar.cc/150?img=4"
        )
    )) }

    Box(Modifier
            .width(412.dp)
            .height(540.dp)) {
        KnowledgeRaceBoard(state)
    }
}

@Composable
fun KnowledgeRaceBoard(participantState: State<List<PodiumChallengeScore>>, modifier: Modifier = Modifier) {

    val grassImage = ImageBitmap.imageResource(
        R.drawable.bg_challenge_knowledge_grass
    )

    val grassBrush = remember(grassImage) {
        ShaderBrush(
            shader = ImageShader(
                image = grassImage,
                tileModeX = TileMode.Repeated,
                tileModeY = TileMode.Repeated,
            )
        )
    }

    BoxWithConstraints(
        modifier = modifier
            .padding(4.dp)
            .shadow(2.dp, RoundedCornerShape(12.dp))
            .background(brush = grassBrush, RoundedCornerShape(8.dp))
    ) {
        val tracks = 6
        val maxScore = 10
        val trackColor = Color(0xFFF48F58)
        val trackSectionColor = Color(0xFFEE5E11)
        val trackRadius = 6.dp
        val trackLineWidth = 2.dp
        val trackLineColor = Color(0xFFDCDCDC)
        val textColor = Color.White
        val outerPadding = min(this.maxWidth/(tracks+1)/2,30.dp) // half the track width as padding
        val trackWidth = (this.maxWidth-outerPadding-outerPadding)/tracks

        val trackLength = this.maxHeight-(outerPadding*2)
        val sectionLength = trackLength/(maxScore+1)

        val playerSize = min(trackWidth*0.7f,60.dp)

        if(this.maxHeight<100.dp) return@BoxWithConstraints

        Box(
            modifier = Modifier
                .padding(outerPadding)
                .fillMaxSize()
                .background(trackColor, RoundedCornerShape(trackRadius))
                .border(trackLineWidth, trackLineColor, RoundedCornerShape(trackRadius))
                .drawBehind {
                    repeat(maxScore) { section ->
                        if (section > 0) {
                            val lineWidth = 1.dp.toPx()
                            drawLine(
                                color = trackSectionColor, start = Offset(0f, sectionLength.toPx() * section), end = Offset(size.width, sectionLength.toPx() * section), strokeWidth = lineWidth
                            )
                        }
                    }
                    repeat(tracks) { track ->
                        val strokeWidth = trackLineWidth.toPx()
                        val trackWidthPx = trackWidth.toPx()
                        if (track > 0) {
                            drawLine(
                                color = trackLineColor, start = Offset((trackWidthPx * track), 0f), end = Offset((trackWidthPx * track), size.height), strokeWidth = strokeWidth
                            )
                        }
                    }
                }
        ) {

            val flagHeightPx = with(LocalDensity.current) { (sectionLength-trackLineWidth).toPx() }.roundToInt()
            val drawable = ContextCompat.getDrawable(LocalContext.current, R.drawable.bg_challenge_knowledge_checkered_flag) as VectorDrawable
            val flagImage = drawable.toBitmap(flagHeightPx,flagHeightPx).asImageBitmap()

            val flagBrush = remember(flagImage) {
                ShaderBrush(
                    shader = ImageShader(
                        image = flagImage,
                        tileModeX = TileMode.Repeated,
                        tileModeY = TileMode.Repeated,
                    )
                )
            }

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(sectionLength)
                    .background(flagBrush, RoundedCornerShape(bottomStart = trackRadius, bottomEnd = trackRadius))
                    .align(Alignment.BottomCenter)
            ) {

            }
        }

        val density = LocalDensity.current

        repeat(maxScore+1) { score ->

            var textOffset by remember { mutableStateOf(6.dp) }

            Text(
                modifier = Modifier
                    .offset(this.maxWidth-outerPadding, outerPadding + (sectionLength*score) - textOffset)
                    .width(outerPadding)
                    .onGloballyPositioned {
                        with(density) {
                            textOffset = it.size.height.toDp()/2
                        }
                    },
                text = score.toString(),
                style = TextStyle(
                    color = textColor,
                                shadow = Shadow(
                                    color = Color.Black, offset = Offset(0f, 0f), blurRadius = 5f
                                ),
                    fontSize = 16.sp,
                    letterSpacing = 1.sp,
                    textAlign = TextAlign.Center,
                    fontFamily = FontFamily(Font(R.font.nunito_bold))
                )
            )
        }

        val participants = participantState.value

        val emptyTracks = (tracks - participants.size).coerceAtLeast(0)
        val startTrack = emptyTracks/2

        participants.sortedBy { it.participantTokenNumber?:0 }.forEachIndexed { index, participant ->
            key(participant.id) {
                val score = participant.score.toInt().coerceAtMost(maxScore)
                val track = startTrack + index
                var textOffset by remember { mutableStateOf(16.dp) }
//            val yOffset = outerPadding + (sectionLength * score) - (playerSize/2) - textOffset
                val animatedYOffset by animateDpAsState(
                    targetValue = (outerPadding + (sectionLength * score) - (playerSize / 2) - textOffset).coerceAtLeast(2.dp),
                    animationSpec = tween(durationMillis = 500, easing = FastOutSlowInEasing) // Adjust duration as needed
                )

                val scale = remember { Animatable(1f) }

                LaunchedEffect(score) {
                    delay(500)
                    scale.animateTo(
                        targetValue = 1.2f, // Expand slightly
                        animationSpec = tween(durationMillis = 200)
                    )
                    scale.animateTo(
                        targetValue = 1f, // Return to normal
                        animationSpec = tween(durationMillis = 200)
                    )
                }
                Column(
                    modifier = Modifier.width(trackWidth).wrapContentHeight().offset(x = (trackWidth * (track + 0.5f)), y = animatedYOffset), horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    Text(
                        modifier = Modifier.padding(horizontal = trackLineWidth + 2.dp).padding(bottom = 2.dp).fillMaxWidth().onGloballyPositioned {
                                with(density) {
                                    textOffset = it.size.height.toDp().value.roundToInt().dp + 2.dp
                                }
                            }, text = participant.name, style = TextStyle(
                            color = textColor, shadow = Shadow(
                                color = Color.Black, offset = Offset(0f, 0f), blurRadius = 5f
                            ), fontSize = 10.sp, letterSpacing = 1.sp, textAlign = TextAlign.Center, fontFamily = FontFamily(Font(R.font.nunito_bold))
                        ), maxLines = 1, overflow = TextOverflow.Ellipsis
                    )

                    Box(
                        modifier = Modifier.wrapContentSize().scale(scale.value)
                    ) {
                        AsyncImage(
                            model = participant.thumbnail,
                            placeholder = painterResource(R.drawable.im_user_placeholder),
                            error = painterResource(R.drawable.im_user_placeholder),
                            contentDescription = null,
                            modifier = Modifier.size(playerSize).shadow(2.dp, CircleShape).background(colorResource(R.color.colorAlwaysDarkSurface)).clip(CircleShape)
                        )
                        if (participant.premiumUser) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_user_badge_premium), contentDescription = "Premium Badge", modifier = Modifier.size(playerSize * 0.3f)
                            )
                        }
                    }

                    val painter = painterResource(id = R.drawable.im_challenge_knowledge_winner)
                    val imageRatio = painter.intrinsicSize.width / painter.intrinsicSize.height

                    val isWinner = score == maxScore

                    // Animate scale from 0.5f to 1f when isWinner becomes true
                    val scale by animateFloatAsState(
                        targetValue = if (isWinner) 1f else 0.5f,
                        animationSpec = tween(durationMillis = 600, easing = FastOutSlowInEasing),
                        label = "ZoomInAnimation"
                    )

                    if (isWinner) {
                        Image(
                            painter = painter, contentDescription = "Winner Badge", modifier = Modifier.fillMaxWidth().aspectRatio(imageRatio).offset(y = -(playerSize * 0.2f)).graphicsLayer(
                                scaleX = scale,
                                scaleY = scale
                            )
                        )
                    }
                }
            }
        }
    }
}