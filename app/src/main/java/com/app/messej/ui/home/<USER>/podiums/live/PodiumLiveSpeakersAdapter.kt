package com.app.messej.ui.home.publictab.podiums.live

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.databinding.ItemPodiumEmptySpeakerBinding
import com.app.messej.databinding.ItemPodiumSpeakerBinding
import com.app.messej.databinding.ItemPodiumSpeakerHeaderBinding
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel

class PodiumLiveSpeakersAdapter(
    private val inflater: LayoutInflater,
    private var speakers: MutableList<PodiumSpeakerUIModel>,
    private var mListener: SpeakerListener
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val ITEM_SPEAKER = 40
        const val ITEM_EMPTY_SPEAKER = 41
    }

    interface SpeakerListener {
        fun onEmptySpeakZoneClick()
        fun decorateSpeakerTile(item: ActiveSpeakerUIModel, binding: ItemPodiumSpeakerBinding) {}

        fun onActiveSpeakerClick(activeSpeaker: ActiveSpeakerUIModel, view: View)

        fun setSpeakerTitle(speakerHeader: ItemPodiumSpeakerHeaderBinding, item: ActiveSpeakerUIModel)

        fun showAudioControls(speakerId: Int): Boolean

        fun toggleMic(speakerId: Int, isMuted: Boolean)
    }


    override fun getItemViewType(position: Int): Int {
        return when (speakers[position]) {
            is ActiveSpeakerUIModel -> ITEM_SPEAKER
            is PodiumSpeakerUIModel.EmptyStateSpeakerUIModel -> ITEM_EMPTY_SPEAKER
        }
    }

    override fun getItemCount(): Int {
        return speakers.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            ITEM_SPEAKER -> {
                ActiveSpeakerViewHolder(ItemPodiumSpeakerBinding.inflate(inflater, parent, false))
            }

            ITEM_EMPTY_SPEAKER -> {
                EmptySpeakerViewHolder(ItemPodiumEmptySpeakerBinding.inflate(inflater, parent, false))
            }

            else -> throw IllegalStateException("Unknown view type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is ActiveSpeakerViewHolder -> holder.bind(speakers[position] as ActiveSpeakerUIModel)
            is EmptySpeakerViewHolder -> holder.bind(speakers[position] as PodiumSpeakerUIModel.EmptyStateSpeakerUIModel)
        }
    }

    inner class ActiveSpeakerViewHolder(val binding: ItemPodiumSpeakerBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(activeSpeaker: ActiveSpeakerUIModel) {
            val needsControls = mListener.showAudioControls(activeSpeaker.speaker.id)
            binding.apply {
                speaker = activeSpeaker
                root.setOnClickListener {
                    if (!needsControls) {
                    mListener.onActiveSpeakerClick(activeSpeaker, it)
                    }
                }

                showControls = needsControls
                actionMore.setOnClickListener {
                    mListener.onActiveSpeakerClick(activeSpeaker, it)
                }

                micButton.setOnClickListener {
                    mListener.toggleMic(activeSpeaker.speaker.id, !activeSpeaker.muted)
                }

                mListener.setSpeakerTitle(speakerHeader, activeSpeaker)
                speakerHeader.speaker = activeSpeaker
                mListener.decorateSpeakerTile(activeSpeaker, binding)
            }
        }
    }

    inner class EmptySpeakerViewHolder(val binding: ItemPodiumEmptySpeakerBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumSpeakerUIModel.EmptyStateSpeakerUIModel) {
            binding.emptymic.setOnClickListener{
                mListener.onEmptySpeakZoneClick()
            }
        }
    }

    fun updateData(newSpeakers: List<PodiumSpeakerUIModel>) {
        val diffResult = DiffUtil.calculateDiff(SpeakersDiffCallback(speakers, newSpeakers))

        speakers.clear()
        speakers.addAll(newSpeakers)

        diffResult.dispatchUpdatesTo(this)
    }

    class SpeakersDiffCallback(
        private val oldList: List<PodiumSpeakerUIModel>,
        private val newList: List<PodiumSpeakerUIModel>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldSpeaker = oldList[oldItemPosition]
            val newSpeaker = newList[newItemPosition]
            val activeSpeakerContentSame = oldSpeaker is ActiveSpeakerUIModel && newSpeaker is ActiveSpeakerUIModel && oldSpeaker.speaker.id == newSpeaker.speaker.id
            val emptySpeakerContentSame = oldSpeaker is PodiumSpeakerUIModel.EmptyStateSpeakerUIModel && newSpeaker is PodiumSpeakerUIModel.EmptyStateSpeakerUIModel

            return activeSpeakerContentSame || emptySpeakerContentSame
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldSpeaker = oldList[oldItemPosition]
            val newSpeaker = newList[newItemPosition]
            return oldSpeaker == newSpeaker
        }
    }
}