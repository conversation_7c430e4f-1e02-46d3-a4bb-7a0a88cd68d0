package com.app.messej.ui.home.publictab.huddles

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.huddles.OtherHuddlesList
import com.app.messej.databinding.ItemOtherHuddleListBinding

class OtherUserHuddlePagerAdapter(val listener: ActionListener) : PagingDataAdapter<OtherHuddlesList, OtherUserHuddlePagerAdapter.OthersHuddleListViewHolder>(TransactionsDiff) {
    interface ActionListener {
        fun goToHuddleClick(item: OtherHuddlesList)
    }

    override fun onBindViewHolder(holder: OthersHuddleListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = OthersHuddleListViewHolder(
        ItemOtherHuddleListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    inner class OthersHuddleListViewHolder(private val binding: ItemOtherHuddleListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: OtherHuddlesList) = with(binding) {
            binding.apply {
                otherHuddle = item
                root.setOnClickListener {
                listener.goToHuddleClick(item)
                }
            }
        }
    }


    object TransactionsDiff : DiffUtil.ItemCallback<OtherHuddlesList>() {
        override fun areItemsTheSame(oldItem: OtherHuddlesList, newItem: OtherHuddlesList) = oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: OtherHuddlesList, newItem: OtherHuddlesList) = oldItem == newItem
    }

}