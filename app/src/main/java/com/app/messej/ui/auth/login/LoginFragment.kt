package com.app.messej.ui.auth.login

import android.content.Context
import android.graphics.Paint
import android.os.Bundle
import android.telephony.TelephonyManager
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.WindowCompat
import androidx.credentials.CredentialManager
import androidx.credentials.CustomCredential
import androidx.credentials.GetCredentialRequest
import androidx.credentials.exceptions.GetCredentialException
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback
import com.app.messej.R
import com.app.messej.data.Constants.FLASHAT_TAG
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.databinding.FragmentLoginBinding
import com.app.messej.ui.auth.login.LoginViewModel.Companion.LoginMode
import com.app.messej.ui.utils.LocaleUtil
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.libraries.identity.googleid.GetSignInWithGoogleOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.android.libraries.identity.googleid.GoogleIdTokenParsingException
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.launch
import java.security.SecureRandom
import java.util.Base64
import java.util.Locale


class LoginFragment : Fragment() {

    private lateinit var binding: FragmentLoginBinding

    private lateinit var mLoginPagerAdapter: FragmentStateAdapter

    private val viewModel: LoginViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater,R.layout.fragment_login,container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
       checkIfMENARegion()
        observe()
    }

    private fun checkIfMENARegion() {
        val telephonyManager = context?.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        val countryIso = telephonyManager.networkCountryIso
        if (countryIso.isNullOrEmpty()) {
            viewModel.checkInMENARegion()
        }else{
            viewModel.setMenaRegion(LocaleUtil.checkInMenaRegion(countryIso.uppercase()))
        }
    }

    private fun setup() {
        activity?.window?.let {
            WindowCompat.getInsetsController(it, it.decorView).isAppearanceLightStatusBars = false
        }
        mLoginPagerAdapter = object: FragmentStateAdapter(this) {

            override fun getItemCount(): Int = 2

            override fun createFragment(position: Int): Fragment {
                // Return a NEW fragment instance in createFragment(int)
                val fragment = when (position) {
                    0 ->  LoginUsernameFragment()
                    1 ->  LoginMobileFragment()

                    else -> throw java.lang.IllegalArgumentException("There should only be 2 tabs")
                }
//        fragment.arguments = Bundle().apply {
//            // Our object is just an integer :-P
//            putInt(ARG_OBJECT, position + 1)
//        }
                return fragment
            }
        }

        binding.loginPager.adapter = mLoginPagerAdapter
        binding.loginPager.isUserInputEnabled = true

        TabLayoutMediator(binding.loginTab, binding.loginPager) { tab, position ->
            when(position) {
                0 -> tab.text = resources.getString(R.string.login_mode_email)
                1 -> tab.text = resources.getString(R.string.login_mode_mobile)
            }
        }.attach()

        binding.loginPager.registerOnPageChangeCallback(object: OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                viewModel.setLoginMode(if(position==1) LoginMode.MOBILE_NUMBER else LoginMode.USERNAME)
            }
        })
        binding.actionChangeLanguage.paintFlags =  binding.actionChangeLanguage.paintFlags or Paint.UNDERLINE_TEXT_FLAG
        binding.actionChangeLanguage.setOnClickListener {
            if(Locale.getDefault().language.equals(AppLocale.ARABIC.isoCode)){
                LocaleUtil.setLanguage(requireActivity(),AppLocale.ENGLISH)
            }else{
                LocaleUtil.setLanguage(requireActivity(),AppLocale.ARABIC)
            }
        }

        binding.googleButton.setOnClickListener {
            lifecycleScope.launch {
                try {
                    val cred = getGoogleCredential()
                    viewModel.signInWithGoogle(cred)
                }
                catch (_: Exception) {

                }
            }
        }
    }

    private suspend fun getGoogleCredential(): GoogleIdTokenCredential {

        fun generateNonce(): String {
            val nonceBytes = ByteArray(16)
            SecureRandom().nextBytes(nonceBytes)
            return Base64.getUrlEncoder().withoutPadding().encodeToString(nonceBytes)
        }

        val credentialManager: CredentialManager = CredentialManager.create(requireActivity())
        val nonce = generateNonce()

        val signInWithGoogleOption: GetSignInWithGoogleOption = GetSignInWithGoogleOption
            // Need to give the client id of the Web App, not Android.
            .Builder("808145004953-3nqa1tkuh69umqs2j4tv6b3c79martci.apps.googleusercontent.com")
            .setNonce(nonce)
            .build()

        val request: GetCredentialRequest = GetCredentialRequest.Builder().addCredentialOption(signInWithGoogleOption).build()

        try {
            val result = credentialManager.getCredential(
                request = request, context = requireActivity()
            )
            Log.d("GLG","signInWithGoogle in auth repo")
            when (val credential = result.credential) {
                is CustomCredential -> {
                    if (credential.type == GoogleIdTokenCredential.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL) {
                        try {
                            // Use googleIdTokenCredential and extract the ID to validate and authenticate on your server.
                            val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
                            Log.d("GLG", "googleIdTokenCredential : $googleIdTokenCredential")
                            return googleIdTokenCredential
                        } catch (e: GoogleIdTokenParsingException) {
                            Log.e("GLG", "Received an invalid google id token response", e)
                            throw e
                        }
                    } else {
                        // Catch any unrecognized custom credential type here.
                        Log.e("GLG", "Unexpected type of credential")
                        throw Exception("Unexpected type of credential")
                    }
                }

                else -> {
                    // Catch any unrecognized credential type here.
                    Log.e("GLG", "Unexpected type of credential")
                    throw Exception("Unexpected type of credential")
                }
            }
        } catch (e: GetCredentialException) {
            Log.d("GLG","signInWithGoogle in auth repo failure ${e.message} ${e.errorMessage} ${e.type}")
            throw e
//            handleFailure(e)
        }
    }

    private fun observe() {
        viewModel.onLoginComplete.observe(viewLifecycleOwner) {
            when(it) {
                LoginViewModel.Companion.NextDestination.BLACKLISTED_USER -> {
                    val action = LoginFragmentDirections.actionGlobalBlackListFragment()
                    val options = NavOptions.Builder().setPopUpTo(R.id.LoginFragment, inclusive = true).build()
                    findNavController().navigateSafe(action, options)
                }
                LoginViewModel.Companion.NextDestination.HOME -> {
                    findNavController().navigateSafe(LoginFragmentDirections.actionGlobalHomeFragment())
                }
                LoginViewModel.Companion.NextDestination.PROFILE_COMPLETION -> {
                    findNavController().navigateSafe(LoginFragmentDirections.actionGlobalNavGraphRegister())
                }
            }
        }
        viewModel.userName.observe(viewLifecycleOwner) {
            Log.d(FLASHAT_TAG, "username: $it")
        }
    }
}