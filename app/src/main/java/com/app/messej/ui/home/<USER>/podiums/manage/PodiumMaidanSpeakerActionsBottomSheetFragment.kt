package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.databinding.FragmentPodiumMaidanSpeakerActionsBottomSheetBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.setCitizenshipWithFlixRate
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PodiumMaidanSpeakerActionsBottomSheetFragment : BasePodiumActionsBottomSheetFragment() {

    private lateinit var binding: FragmentPodiumMaidanSpeakerActionsBottomSheetBinding

    private val args: PodiumMaidanSpeakerActionsBottomSheetFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_maidan_speaker_actions_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun observer() {
        userStatsViewModel.userStats.observe(viewLifecycleOwner) {
            binding.userInfo.stats = it
            it?: return@observe
            setCitizenshipWithFlixRate(binding.userInfo.userFlixRate,it,viewModel.isManager(args.userId))
        }
        viewModel.onLeftPodium.observe(viewLifecycleOwner) {
            viewModel.podium.value?.competitorPodiumId?.let { cId ->
                val options = NavOptions.Builder()
                    .setPopUpTo(R.id.nav_live_podium, inclusive = true)
                    .build()
                val action = PodiumMaidanSpeakerActionsBottomSheetFragmentDirections.actionGlobalNavLivePodium(podiumId = cId, kind = PodiumKind.MAIDAN.ordinal)
                findNavController().navigateSafe(action,options)
            }
        }
    }

    private fun setup() {
        viewModel.podiumId.value?.let {
            userStatsViewModel.setUserAndPodium(args.userId, it)
        }
        viewModel.getSpeaker(args.userId)?.also { speaker ->
            binding.speaker = speaker
            binding.canSwitch = viewModel.secondMainScreenSpeakerId.value==args.userId && viewModel.iAmSpeaker.value!=true
            binding.actionSwitch.setOnClickListener{
                goToCompetitorPodium()
            }
        }?: run {
            findNavController().popBackStack()
        }
    }

    private fun goToCompetitorPodium() {
        viewModel.leave { left ->
            if(!left) return@leave
            viewModel.podium.value?.competitorPodiumId?.let { cId ->
                val options = NavOptions.Builder()
                    .setPopUpTo(R.id.nav_live_podium, inclusive = true)
                    .build()
                val action = PodiumMaidanSpeakerActionsBottomSheetFragmentDirections.actionGlobalNavLivePodium(podiumId = cId, kind = PodiumKind.MAIDAN.ordinal)
                findNavController().navigateSafe(action,options)
            }
        }
    }


}