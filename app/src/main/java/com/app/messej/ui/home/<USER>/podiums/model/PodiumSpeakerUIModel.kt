package com.app.messej.ui.home.publictab.podiums.model

import android.util.Log
import androidx.annotation.DrawableRes
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.enums.AssemblySpeakingStatus

sealed class PodiumSpeakerUIModel : BaseObservable() {

    data class ActiveSpeakerUIModel(
        var speaker: PodiumSpeaker,
    ) : PodiumSpeakerUIModel() {
        @get:Bindable
        var currentlySpeaking: Boolean = false
            set(value) {
                if (field == value) return
                field = value
                notifyPropertyChanged(BR.currentlySpeaking)
            }

        @get:Bindable
        var muted: Boolean = true
            set(value) {
                field = value
//                Log.d("PodiumLVM", " Bindable muted: $value")
                speaker.muted = value
                notifyPropertyChanged(BR.muted)
                Log.d("PSUIM", "muted: $value | calling updateTimerVisibility()")
                updateTimerVisibility()
            }

        @get:Bindable
        var online: Boolean = true
            set(value) {
                field = value
//                Log.d("PodiumLVM", " Bindable online: $value")
                speaker.online = value
                notifyPropertyChanged(BR.online)
            }

        @get:Bindable
        var admin: Boolean = false
            set(value) {
                field = value
//                Log.d("PodiumLVM", " Bindable admin: $value")
                notifyPropertyChanged(BR.admin)
            }

        @get:Bindable
        var isManager: Boolean = speaker.isManager
            set(value) {
                field = value
//                Log.d("PodiumLVM", " Bindable isManager: $value")
                notifyPropertyChanged(BR.isManager)
                Log.d("PSUIM", "isManager: $value | calling updateTimerVisibility()")
                updateTimerVisibility()
            }

        @get:Bindable
        @DrawableRes
        var countryFlag: Int? = null
            set(value) {
                field = value
                notifyPropertyChanged(BR.countryFlag)
            }

        @get:Bindable
        var selected: Boolean? = null
            set(value) {
                field = value
                notifyPropertyChanged(BR.selected)
            }

        @get:Bindable
        var coinsToDisplay: String = speaker.coinsReceivedFormatted
            set(value) {
                field = value
                notifyPropertyChanged(BR.coinsToDisplay)
            }

        @get:Bindable
        var assemblyTimeRemaining: String? = null
            set(value) {
                field = value
                notifyPropertyChanged(BR.assemblyTimeRemaining)
                Log.d("PSUIM", "assemblyTimeRemaining: $value | calling updateTimerVisibility()")
                updateTimerVisibility()
            }

        @get:Bindable
        var assemblySpeakingStatus: AssemblySpeakingStatus? = speaker.speakingStatus
            set(value) {
                field = value
                notifyPropertyChanged(BR.assemblySpeakingStatus)
                Log.d("PSUIM", "assemblySpeakingStatus: $value | calling updateTimerVisibility()")
                updateTimerVisibility()
            }

        @get:Bindable
        var showAssemblyTimer: Boolean = false
            set(value) {
                field = value
                notifyPropertyChanged(BR.showAssemblyTimer)
            }

        private fun updateTimerVisibility() {
            val show = !(assemblySpeakingStatus == AssemblySpeakingStatus.INACTIVE && (!isManager || (muted || assemblyTimeRemaining.isNullOrBlank()))) && assemblySpeakingStatus != null
//            Log.d("PSUIM", "updateTimerVisibility: assemblySpeakingStatus: $assemblySpeakingStatus | assemblyTimeRemaining: $assemblyTimeRemaining")
//            if(show) Log.w("PSUIM", "updateTimerVisibility: show: $show" )
//            else Log.d("PSUIM", "updateTimerVisibility: show: $show" )
            showAssemblyTimer = show
        }

        init {
            muted = speaker.muted
        }

    }

    data object EmptyStateSpeakerUIModel : PodiumSpeakerUIModel()

}