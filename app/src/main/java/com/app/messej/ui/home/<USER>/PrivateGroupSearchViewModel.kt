package com.app.messej.ui.home.privatetab

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.ui.home.publictab.huddles.PublicHuddlesAdapter
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class PrivateGroupSearchViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val huddleRepo = HuddlesRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())
    var searchKeyword = MutableLiveData<String>(null)
    private val searchTerm = MutableLiveData("")

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                it?: return@collect
                if (it.isBlank()){
                    searchTerm.postValue("")
                }else{
                    searchTerm.postValue(it)
                }
            }
        }
    }

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    val user: CurrentUser get() = accountRepo.user

    private val _huddleList = searchTerm.switchMap {
        huddleRepo.getHuddlesSearchPager(type = HuddleType.PRIVATE, it).liveData.cachedIn(viewModelScope)
    }

    val huddleList: MediatorLiveData<PagingData<PublicHuddlesAdapter.HuddleUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<PublicHuddlesAdapter.HuddleUIModel>?>(null)
        fun updateHuddleList() {
            _dataLoading.value = false
            val list: PagingData<PublicHuddlesAdapter.HuddleUIModel>? = _huddleList.value?.map { item ->
                PublicHuddlesAdapter.HuddleUIModel.LocalHuddleUIModel(item, false)
            }
            huddleList.postValue(list)
        }
        med.addSource(_huddleList) { updateHuddleList() }
        med
    }

    fun resetSearch() {
        searchKeyword.postValue("")
    }
}