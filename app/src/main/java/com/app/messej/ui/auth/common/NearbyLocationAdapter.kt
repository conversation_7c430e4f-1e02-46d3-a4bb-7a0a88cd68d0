package com.app.messej.ui.auth.common

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.databinding.ItemNearByPlacesBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.google.android.libraries.places.api.model.Place

class NearbyLocationAdapter(data: MutableList<Place>) :
    BaseQuickAdapter<Place, BaseDataBindingHolder<ItemNearByPlacesBinding>>(R.layout.item_near_by_places, data) {

    override fun convert(holder: BaseDataBindingHolder<ItemNearByPlacesBinding>, item: Place) {
        holder.dataBinding?.apply {
            locName = item.displayName
            locIcon = item.iconMaskUrl

            try {
                item.iconBackgroundColor?.let { bg ->
                    locationIconMcv.setCardBackgroundColor(bg)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<Place>() {
        override fun areItemsTheSame(oldItem: Place, newItem: Place): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(
            oldItem: Place,
            newItem: Place,
        ): Boolean {
            return oldItem.id == newItem.id
        }
    }
}