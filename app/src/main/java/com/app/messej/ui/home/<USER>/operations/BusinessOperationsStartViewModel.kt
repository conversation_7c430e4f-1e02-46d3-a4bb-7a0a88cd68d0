package com.app.messej.ui.home.businesstab.operations

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.entity.BusinessTaskOne
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.datastore.FlashatDatastore

import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.launch

class BusinessOperationsStartViewModel(application: Application) : AndroidViewModel(application) {

    private val _isStartPageClicked by lazy { MutableLiveData(false) }
    val isStartPageClicked: LiveData<Boolean>
        get() = _isStartPageClicked
    val repository = FlashatDatastore()
    val setStartPageClicked: LiveEvent<Boolean>?=null

    private val businessRepo = BusinessRepository(application)
    val taskOneData: LiveData<BusinessTaskOne?> = businessRepo.getTaskOneDetails()

    val isPremiumMember = LiveEvent<Boolean>()

    init {
        getBusinessTaskOne()
    }

    private fun getBusinessTaskOne() {
        viewModelScope.launch {
            businessRepo.getBusinessTaskOne()
        }
    }

    fun setBusinessStartShown(){
        viewModelScope.launch {
            _isStartPageClicked.postValue(repository.isBusinessStartShown())
        }
    }

    fun setStartClicked(isClicked: Boolean) {
        viewModelScope.launch {
            repository.setBusinessStartShown(isClicked)
            setStartPageClicked?.postValue(isClicked)
        }
    }
}