package com.app.messej.ui.home.publictab.broadcast

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.app.messej.data.model.enums.BroadcastMode

class BroadcastForwardViewModel(application: Application): AndroidViewModel(application)  {

    private val _forwardModes = MutableLiveData<Map<BroadcastMode,Boolean>>(mapOf())
    val forwardModes: LiveData<Map<BroadcastMode,Boolean>> = _forwardModes

    fun toggleForwardMode(mode: BroadcastMode) {
        val map = _forwardModes.value?.toMutableMap()?: mutableMapOf()
        map[mode] = !map.getOrDefault(mode,false)
        _forwardModes.value = map
    }

    val canConfirmForward = _forwardModes.map {
        it.count { mode-> mode.value }>0
    }

    fun getForwardModes(): List<BroadcastMode> {
        return _forwardModes.value?.filter { mode-> mode.value }?.map { e -> e.key }?: listOf()
    }
}