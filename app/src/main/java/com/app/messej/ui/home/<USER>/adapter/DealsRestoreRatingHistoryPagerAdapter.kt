package com.app.messej.ui.home.businesstab.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.BuyFlaxRates
import com.app.messej.databinding.ItemRestoreRatingHistoryLayoutBinding

class DealsRestoreRatingHistoryPagerAdapter(val currentUserId: Int) : PagingDataAdapter<BuyFlaxRates, DealsRestoreRatingHistoryPagerAdapter.DealsRestoreRatingHistoryViewHolder>(TransactionsDiff) {


    override fun onBindViewHolder(holder: DealsRestoreRatingHistoryViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = DealsRestoreRatingHistoryViewHolder(
        ItemRestoreRatingHistoryLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    inner class DealsRestoreRatingHistoryViewHolder(private val binding: ItemRestoreRatingHistoryLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: BuyFlaxRates) = with(binding) {
            binding.apply {
                buyFlaxHistory = item
                content = when {
                    item.purchasedBy?.toInt() == currentUserId && currentUserId != item.userId ->  this.root.context.getString(R.string.flix_restored_for, item.restoredFor)
                    item.purchasedBy?.toInt() != currentUserId && currentUserId == item.userId ->  this.root.context.getString(R.string.flix_restored_by, item.restoredBy)
                    item.purchasedBy?.toInt() == currentUserId && currentUserId == item.userId ->this.root.context.getString(R.string.flix_restored_self)
                    else -> null
                }
            }
        }
    }


    object TransactionsDiff : DiffUtil.ItemCallback<BuyFlaxRates>() {
        override fun areItemsTheSame(oldItem: BuyFlaxRates, newItem: BuyFlaxRates) = oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: BuyFlaxRates, newItem: BuyFlaxRates) = oldItem == newItem
    }

}