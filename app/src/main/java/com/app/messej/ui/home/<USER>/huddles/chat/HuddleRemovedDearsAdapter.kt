package com.app.messej.ui.home.publictab.huddles.chat

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.databinding.ItemUserWithActionBinding


class HuddleRemovedDearsAdapter(val listener: ActionListener) : PagingDataAdapter<Participant, HuddleRemovedDearsAdapter.HuddleRemovedDearsViewHolder>(Diff) {


    interface ActionListener {
        fun addAgainClick(item: Participant)
    }

    inner class HuddleRemovedDearsViewHolder(private val binding: ItemUserWithActionBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(participant: Participant) = with(binding) {
            binding.apply {
                user = participant
                showUsername = false
                action = binding.root.context.getString(R.string.remove_dears_add_again)
                binding.userAction.isAllCaps=false
                userAction.setOnClickListener {
                    listener.addAgainClick(participant)
                }
            }
        }
    }

    override fun onBindViewHolder(holder: HuddleRemovedDearsViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HuddleRemovedDearsViewHolder {
        val binding = ItemUserWithActionBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return HuddleRemovedDearsViewHolder(binding)
    }


    object Diff : DiffUtil.ItemCallback<Participant>() {
        override fun areItemsTheSame(oldItem: Participant, newItem: Participant) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: Participant, newItem: Participant) = oldItem == newItem
    }

}


