package com.app.messej.ui.home.forward

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.ExternalMessage
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.api.forward.ForwardHuddleRequest
import com.app.messej.data.model.api.forward.ForwardRequest
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.enums.ForwardMode
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.ForwardType
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.repository.ForwardRepository
import com.app.messej.data.repository.worker.ChatMessageWorker
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ForwardCommonViewModel(application: Application) : AndroidViewModel(application) {

    private val chatRepo = ChatRepository(application)
    private val repo = ForwardRepository(application)
    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val _sourceMessage = MutableLiveData<AbstractChatMessage>(null)
    val sourceMessage: MutableLiveData<AbstractChatMessage> = _sourceMessage

    private val _forwardMode = MutableLiveData<ForwardMode>()
    val forwardMode: LiveData<ForwardMode> = _forwardMode

    val onMessageForwarded = LiveEvent<Boolean>()

    val onError = LiveEvent<String>()

    val actionLoading = MutableLiveData(false)
    private val huddleIdList = mutableListOf<Int>()

    val onLimitExceeds = LiveEvent<Boolean?>()

    private val _enableSend = MutableLiveData(false)
    val enableSend: LiveData<Boolean> = _enableSend

    val forwardRequestList = mutableListOf<ForwardRequest>()

    private val mediaType = MutableLiveData<MediaType>()

    var messageLimit: Int = 0

    fun setMessageId(messageId: String?, srcType: ForwardSource, message: String?, mediaType: String?) {
        if (!messageId.isNullOrEmpty() && message.isNullOrEmpty()) {
            getMessage(messageId, srcType)
            _forwardMode.postValue(ForwardMode.FORWARD)
            messageLimit = 5
        } else {
            _forwardMode.postValue(ForwardMode.SHARE)
            val externalMessage = ExternalMessage(rawMessage = message)
            _sourceMessage.postValue(externalMessage)
            mediaType?.let {
                this.mediaType.postValue(MediaType from it.lowercase())
                _sourceMessage.postValue(externalMessage.copy(externalMediaType = MediaType from it.lowercase()))
            }
            messageLimit = 1
        }
    }

    private fun getMessage(messageId: String, srcType: ForwardSource) {
        viewModelScope.launch {
            when (srcType) {
                ForwardSource.MESSAGES -> {
                    val selectedPrivateMessage = chatRepo.getPrivateChatMessage(messageId)
                    _sourceMessage.postValue(selectedPrivateMessage as AbstractChatMessage)
                }
                ForwardSource.GROUPS -> {
                    val selectedGroupMessage = chatRepo.getPrivateGroupMessage(messageId)
                    _sourceMessage.postValue(selectedGroupMessage as AbstractChatMessage)
                }
                ForwardSource.HUDDLES -> {
                    val selectedHuddleMessage = chatRepo.getPrivateHuddleMessage(messageId)
                    _sourceMessage.postValue(selectedHuddleMessage as AbstractChatMessage)
                }
                else -> {}
            }
        }
    }

    fun addForwardList(request: ForwardRequest) {
        forwardRequestList.add(request)
        _enableSend.postValue(forwardRequestList.size in 1..messageLimit)
    }

    fun removeForwardList(request: ForwardRequest) {
        forwardRequestList.remove(request)
        _enableSend.postValue(forwardRequestList.size in 1..messageLimit)
    }

    sealed class ShareRequest {
        abstract val roomId: String
        abstract val message: String
        abstract val forwardType: ForwardType
        abstract val mediaType: MediaType?

        data class PrivateChatRequest(
            override val roomId: String,
            val receiverId: Int,
            override val message: String,
            override val forwardType: ForwardType,
            override val mediaType: MediaType?,
        ) : ShareRequest()

        data class GroupChatRequest(
            override val roomId: String,
            override val message: String,
            override val forwardType: ForwardType,
            override val mediaType: MediaType?,
        ) : ShareRequest()
    }

    val navigateToNextFragment = LiveEvent<ShareRequest>()
    fun shareMessage() {
        if (forwardRequestList.size <= messageLimit) {
            val message = _sourceMessage.value ?: return
            viewModelScope.launch(Dispatchers.IO) {
                forwardRequestList.forEach { forwardRequest ->
                    when (forwardRequest.forwardType) {
                        ForwardType.MESSAGES -> {
                            val chat = forwardRequest.forward.asPrivateChat(user.id) ?: return@forEach
                            if (_forwardMode.value == ForwardMode.FORWARD) {
                                chatRepo.createChatMessageForward(chat, message)
                            } else {
                                navigateToNextFragment.postValue(
                                    ShareRequest.PrivateChatRequest(
                                        roomId = forwardRequest.forward.roomId ?: return@forEach,
                                        receiverId = forwardRequest.forward.userId ?: return@forEach,
                                        message = message.rawMessage.orEmpty(),
                                        forwardType = ForwardType.MESSAGES,
                                        mediaType = mediaType.value
                                    )
                                )
                            }
                        }

                        ForwardType.GROUPS -> {
                            val huddleId = forwardRequest.forward.id.toIntOrNull() ?: return@forEach
                            if (_forwardMode.value == ForwardMode.FORWARD) {
                                chatRepo.createChatMessageForward(huddleId, HuddleType.PRIVATE, message)
                            } else {
                                navigateToNextFragment.postValue(
                                    ShareRequest.GroupChatRequest(
                                        roomId = forwardRequest.forward.id,
                                        message = message.rawMessage.orEmpty(),
                                        forwardType = ForwardType.GROUPS,
                                        mediaType = mediaType.value
                                    )
                                )
                            }
                        }

                        ForwardType.HUDDLES -> {
                            if (_forwardMode.value == ForwardMode.FORWARD) {
                                if (_forwardMode.value == ForwardMode.FORWARD) huddleIdList.add(forwardRequest.forward.id.toInt())
                            } else {
                                navigateToNextFragment.postValue(
                                    ShareRequest.GroupChatRequest(
                                        roomId = forwardRequest.forward.id,
                                        message = message.rawMessage.orEmpty(),
                                        forwardType = ForwardType.HUDDLES,
                                        mediaType = mediaType.value
                                    )
                                )
                            }
                        }

                        else -> {}
                    }
                }
                ChatMessageWorker.startIfNotRunning()
                val hasMention = when (message) {
                    is HuddleChatMessage -> message.hasMention
                    else -> false
                }

                val forwardId = if (_forwardMode.value == ForwardMode.FORWARD) message.messageId else null
                val request = ForwardHuddleRequest(forwardId, huddleIdList, hasMention, message.rawMessage, null)
                _sourceMessage.value?.hasMedia?.let { hasMedia ->
                    if (hasMedia) {
                        request.setMedia(
                            MediaMeta(
                                mediaType = message.mediaMeta?.mediaType!!,
                                mediaName = message.media,
                                mimeType = message.mediaMeta?.mimeType,
                                s3Key = message.mediaMeta?.s3Key!!,
                                thumbnail = message.mediaMeta?.thumbnail,
                                mediaWidth = message.mediaMeta?.mediaWidth,
                                mediaHeight = message.mediaMeta?.mediaHeight,
                                mediaDuration = message.mediaMeta?.mediaDuration,
                                documentDisplayName = message.mediaMeta?.documentDisplayName,
                                formattedSize = message.mediaMeta?.formattedSize,
                            )
                        )
                    }
                }
                when (val result = repo.sendHuddleForwardList(request)) {
                    is ResultOf.Success -> {
                        onMessageForwarded.postValue(true)
                        huddleIdList.clear()
                        forwardRequestList.clear()
                    }

                    is ResultOf.APIError -> {
                        huddleIdList.clear()
                        forwardRequestList.clear()
                        onError.postValue(result.error.message)
                    }

                    else -> {}
                }
            }

        } else {
            onLimitExceeds.postValue(true)
        }
    }

    fun clearList() {
        forwardRequestList.clear()
        onLimitExceeds.postValue(false)
    }

    fun setLimitExceeds() {
        onLimitExceeds.postValue(true)
    }
}