package com.app.messej.ui.home.publictab.flash.comments

import android.util.Log
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.app.messej.R
import com.app.messej.data.model.PostatCommentLikePayload
import com.app.messej.data.model.api.postat.PostComment
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.composeComponents.UserBadge
import kotlinx.coroutines.delay

@Composable
fun CommentsSingleItem(

    @DrawableRes flag: Int? = null,
    currentUser: Int,

    comment: PostComment,
    onSelectedItemClick: () -> Unit,
    listener: CommentsListener,
    selectedItem:String?=null,

)
{
    Log.d("Selected","${selectedItem}")
    val context = LocalContext.current
    val commentTime =
        DateTimeUtils.durationToNowFromPast(comment.parsedCreatedTime)?.let { dur ->
            DateTimeUtils.formatDurationToSingleUnit(dur, context)
        }

    val isUserDeleted = comment.senderDetails?.deletedAccount == true
    val alpha  = if ( selectedItem == comment.commentId || selectedItem == null ) 1f else 0.3f
    Log.d("Selected"," alpha ${alpha}")

    Box {
        if (selectedItem == comment.commentId) {
            Popup(
                alignment = Alignment.BottomEnd,
                properties = PopupProperties(dismissOnClickOutside = true), // Changed to true
                onDismissRequest = {
                    // Call a function to clear selection when popup is dismissed
                    onSelectedItemClick()

                }
            ) {
                DeleteCommentButton(
                    onDeleteClicked = {
                        if (selectedItem == null) return@DeleteCommentButton
                        if(comment.type==CommentType.FLASH)  listener.onDeleteFlashClicked(comment.commentId, false, comment.senderDetails?.id ?: 0)
                        else  listener.onDeletePostatClicked(comment.messageId, comment.commentId, false, comment.senderDetails?.id ?: 0)
                    },
                )
            }
        }
        Row(
            modifier = Modifier
                .alpha(alpha)
                .padding(horizontal = 8.dp)
                .fillMaxWidth()
                .pointerInput(Unit) {
                    detectTapGestures(
                        onLongPress = {
                            if (comment.senderDetails?.id != currentUser) return@detectTapGestures
                            onSelectedItemClick()
                        },
                    )
                }
            ,
        ) {
            Box {
                // User Image View
                AsyncImage(
                    model = ImageRequest.Builder(context).crossfade(enable = true)
                        .data(data = comment.senderDetails?.thumbnail).build(),
                    placeholder = painterResource(R.drawable.im_user_placeholder_square),
                    error = painterResource(R.drawable.im_user_placeholder_square),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .size(size = 30.dp)
                        .clip(CircleShape)
                        .clickable {
                            if (selectedItem != null) return@clickable
                            if (!isUserDeleted) {
                                listener.onProfileImageClicked(comment.senderDetails?.id ?: 0)
                            }
                        }
                )
                // User Badge View
                UserBadge(
                    modifier = Modifier.align(alignment = Alignment.TopStart),
                    userType = if (comment.senderDetails?.premium == true) com.app.messej.data.model.enums.UserBadge.PREMIUM else null
                )
            }
            Column(
                modifier = Modifier
                    .padding(start = 8.dp)
                    .fillMaxWidth()
                    .weight(weight = 1f)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()) {
                    Text(
                        text = comment.senderDetails?.name.toString(),
                        style = FlashatComposeTypography.defaultType.subtitle2,
                        color = colorResource(id = R.color.textColorPrimary),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f, fill = false)

                    )
                    //Used for flag
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .crossfade(enable = true)
                            .data(data = flag).build(),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .padding(start = dimensionResource(id = R.dimen.element_spacing))
                            .size(width = 20.dp, height = 13.dp)
                    )

                    commentTime?.let {
                        Text(
                            modifier = Modifier
                                .padding(start = dimensionResource(id=R.dimen.line_spacing)),
                            text = it,
                            style = FlashatComposeTypography.overLineSmaller,
                            color = colorResource(id = R.color.textColorPrimary)
                        )
                    }
                }
                Text(
                    text = comment.comment.toString(),
                    style = FlashatComposeTypography.defaultType.overline,
                    color = colorResource(id = R.color.textColorPrimary)
                )

            }

            // Only show action buttons if user is not deleted
            if (!isUserDeleted) {
                ButtonActions(
                    onClicked = {
                        if (selectedItem != null) return@ButtonActions
                        listener.onReplyClicked(comment, null)
                                },
                    icon = R.drawable.ic_reply,
                    R.color.colorSecondary,
                    totalCount = comment.totalReplies
                )
                ButtonActions(
                    onClicked ={
                        if (selectedItem != null|| (comment.senderDetails?.id == currentUser)) return@ButtonActions
                        if (comment.type == CommentType.FLASH) listener.onFlashLikeClicked(comment.commentId, null)
                        else listener.onPostatLikeClicked(PostatCommentLikePayload(postId = comment.messageId,replyOwnerId = comment.senderDetails?.id, id = comment.commentId, isLiked = true, postOwnerId = comment.contentOwnerId ))
                               },
                    icon = if(comment.senderDetails?.id == currentUser) R.drawable.ic_podium_like_disable  else R.drawable.ic_podium_like,
                    totalCount = comment.totalLikeCount
                )
            }
        }
    }

}

/** single item for imagesButtons */

@Composable
fun ButtonActions(
    onClicked: () -> Unit,
    @DrawableRes icon: Int,
    @ColorRes tintColor: Int? = null,
    totalCount: Int? = null,
) {
    var isDoubleClicked by remember { mutableStateOf(false) }
    LaunchedEffect(isDoubleClicked) {
        if (isDoubleClicked) {
            delay(1000)
            isDoubleClicked = false
        }
    }

        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            IconButton(onClick = {
                if (isDoubleClicked) return@IconButton
                onClicked()
                isDoubleClicked = true
            }) {
                Icon(
                    modifier = Modifier.size(size = dimensionResource(R.dimen.activity_margin)),
                    painter = painterResource(id = icon),
                    contentDescription = null,
                    tint = if (tintColor == null) Color.Unspecified else colorResource(tintColor)
                )
            }
            if (totalCount != null) {
                Text(
                    text = totalCount.toString(),
                    style = FlashatComposeTypography.overLineSmaller,
                    color = colorResource(id = R.color.textColorPrimary)
                )
            }

        }
}

//@Preview(showBackground = true, uiMode = 33)
//@Composable
//fun ViewCommentsSingleScreen() {
//    CommentsSingleItem(
//        onReplayButtonClick = {},
//        onLikeButtonClick = {},
//        onReportButtonClick = {},
//        onSelectedItemClick = {},
//        onDeleted = {},
//        onProfileImageClicked = {},
//        isDeleteButtonVisible = true ,
//        currentUser =123,
//        alpha = 1f,
//        flashComments = PostComment(
//            huddleId = 12,
//            messageId = "asas",
//            created = "qweq",
//            media = "asda",
//            commentId = "asdas",
//            senderId = 12
//        )
//    )
//}