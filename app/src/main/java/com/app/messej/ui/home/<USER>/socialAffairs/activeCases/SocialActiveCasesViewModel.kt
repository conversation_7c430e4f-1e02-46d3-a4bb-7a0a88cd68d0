package com.app.messej.ui.home.publictab.socialAffairs.activeCases

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.socialAffairs.SocialVoteErrorResponse.SocialError
import com.app.messej.data.model.api.socialAffairs.SocialVoteRequest
import com.app.messej.data.model.enums.SocialActiveCaseMainTab
import com.app.messej.data.model.enums.SocialActiveTab
import com.app.messej.data.model.enums.SocialCaseFilter
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.SocialAffairsRepository
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.ui.utils.CountryListUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch

class SocialActiveCasesViewModel(application: Application) : AndroidViewModel(application = application) {

    private val socialRepo = SocialAffairsRepository(context = application)
    private val accountRepo = AccountRepository(application)
    val countryFlagList = MutableLiveData<Map<String, Int>>()
    private var isMainTabAlreadySetFromNavArgs = false

    val user: CurrentUser
        get() = accountRepo.user

    val activeTabList = SocialActiveCaseMainTab.entries.toList()
    val donateSubTabList = SocialActiveTab.entries.toList()
    val newCaseSubTabList = SocialActiveTab.entries.except(SocialActiveTab.Upgrade).toList()

    val donateFilterTabList = SocialCaseFilter.entries.except(SocialCaseFilter.Declined, SocialCaseFilter.UnmetVoting).toList()
    val newCasesFilterTabList = SocialCaseFilter.entries.except(SocialCaseFilter.Pending, SocialCaseFilter.Closed).toList()

    private val _selectedMainTab = MutableLiveData(SocialActiveCaseMainTab.Donate)
    val selectedMainTab: LiveData<SocialActiveCaseMainTab> = _selectedMainTab

    private val _selectedSubTab = MutableLiveData(SocialActiveTab.All)
    val selectedSubTab: LiveData<SocialActiveTab> = _selectedSubTab

    private val _selectedFilterTab = MutableLiveData(SocialCaseFilter.All)
    val selectedFilterTab : LiveData<SocialCaseFilter> = _selectedFilterTab

    val casesCount = MutableLiveData<Pair<Int?, Int?>>()

    init {
        viewModelScope.launch {
            countryFlagList.postValue(CountryListUtil.getCustomCountryMap())
        }
    }

    val combinedFilters: MediatorLiveData<Triple<SocialActiveCaseMainTab?, SocialActiveTab?, SocialCaseFilter?>> by lazy {
        val med = MediatorLiveData<Triple<SocialActiveCaseMainTab?, SocialActiveTab?, SocialCaseFilter?>>()
        fun update() {
           med.postValue(Triple(
               _selectedMainTab.value, _selectedSubTab.value, _selectedFilterTab.value
           ))
        }
        med.addSource(_selectedMainTab) { update() }
        med.addSource(_selectedSubTab) { update() }
        med.addSource(_selectedFilterTab) { update() }
        med
    }

    val caseList = combinedFilters.switchMap { (mainTab, subTab, filterTab) ->
        socialRepo.getSocialCases(
            mainTab = mainTab, tab = subTab, subTab = filterTab,
            countCallBack = { caseResponse ->
                casesCount.postValue(Pair(caseResponse?.donateCasesCount,caseResponse?.newCasesCount))
            }
        ).liveData
    }.asFlow().cachedIn(viewModelScope)

    fun setActiveCaseTab(tab : SocialActiveCaseMainTab, isFromNavArgs: Boolean = false) {
        //isMainTabAlreadySetFromNavArgs-> Added for bypass checking of set tab from navArgs.
        //If tab value is set from navArgs, and navigate to another fragment from this fragment.
        //On return back to this fragment, the tab will reset from the navArgs value.
        //To avoid this case isMainTabAlreadySetFromNavArgs variable added.
        if (isFromNavArgs) {
            if (isMainTabAlreadySetFromNavArgs) return
            isMainTabAlreadySetFromNavArgs = true
        }

        if (tab == _selectedMainTab.value) return
        _selectedMainTab.postValue(tab)
        setSubTab(tab = SocialActiveTab.All)
        setFilterTab(tab = SocialCaseFilter.All)
    }

    fun setSubTab(tab: SocialActiveTab) {
        if (tab == _selectedSubTab.value) return
        _selectedSubTab.postValue(tab)
        setFilterTab(tab = SocialCaseFilter.All)
    }

    fun setFilterTab(tab : SocialCaseFilter) {
        if (tab == _selectedFilterTab.value) return
        _selectedFilterTab.postValue(tab)
    }

    val currentSubTabList = _selectedMainTab.map {
        if (it == SocialActiveCaseMainTab.Donate) donateSubTabList
        else newCaseSubTabList
    }

    val isFilterIconVisible: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val isVisibleForPresident = if (user.citizenship.isPresident) _selectedSubTab.value == SocialActiveTab.All || _selectedSubTab.value == SocialActiveTab.Personal else false
            val isVisible = isVisibleForPresident || _selectedMainTab.value == SocialActiveCaseMainTab.NewCases
            med.postValue(isVisible)
        }
        med.addSource(_selectedMainTab) { update() }
        med.addSource(_selectedSubTab) { update() }
        med
    }

    val currentFilterTabList = _selectedMainTab.map {
        if (it == SocialActiveCaseMainTab.Donate) donateFilterTabList
        else newCasesFilterTabList
    }

    val voteError = LiveEvent<SocialError?>()
    val onError = LiveEvent<String?>()
    //Used replay = 1, because, when donate or do any vote action from the case info screen
    //and then going back to active case fragment, the active case need to refresh in that case.
    //The isReadyToFreshList -> changed to true in the refresh scenarios.
    //But when navigate back and making isReadyToFreshList to true, the collector of isReadyToFreshList will miss the collection in that scenario (in compose screen).
    //So to avoid this case make replay ==1. and after collect the replay is resetting.
    val isReadyToFreshList = MutableSharedFlow<Boolean>(replay = 1)

    fun setReloadCaseList() {
        viewModelScope.launch {
            isReadyToFreshList.emit(true)
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    fun resetReplayCache() {
        //Clears the last emitted value, so that the last emitted value will not re trigger
        isReadyToFreshList.resetReplayCache()
    }

    fun vote(voteAction: SocialVoteAction, caseId: Int?) {
        viewModelScope.launch(context = Dispatchers.IO) {
            val request = SocialVoteRequest(socialRequestId = caseId, voteAction = voteAction)
            val response = socialRepo.vote(request = request)
            when(response) {
                is SocialAffairsRepository.SocialResultOf.Success -> {
                    setReloadCaseList()
                }
                is SocialAffairsRepository.SocialResultOf.APIError -> {
                    if (response.code == 403 && response.error.result?.reason != null) {
                        voteError.postValue(response.error.result)
                    } else {
                        onError.postValue(response.error.message)
                    }
                }
                is SocialAffairsRepository.SocialResultOf.Error -> {
                    onError.postValue(response.exception.message)
                }
            }
        }
    }

}