package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.databinding.FragmentBaseBusinessDealsBinding
import com.app.messej.databinding.LayoutIdCardToolTipInformationBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.businesstab.operations.status.BusinessWorkStatusViewModel

abstract class BusinessDealsBaseFragment : Fragment() {

    protected abstract var binding: FragmentBaseBusinessDealsBinding
    protected val viewModels: BusinessDealsListViewModel by activityViewModels()
    private val workStatusViewModel: BusinessWorkStatusViewModel by activityViewModels()
//    private lateinit var mTransactionPagerAdapter: FragmentStateAdapter

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }


    override fun onResume() {
        super.onResume()
        viewModels.loadBusinessStatement()
    }

    private fun observe() {

//        viewModels.currentTab.observe(viewLifecycleOwner) {
//            it ?: return@observe
//            Log.w("TABTRANSACTION", "observe currentTab: $it")
//            if (binding.transactionPager.currentItem == it.ordinal) return@observe
//            binding.transactionPager.setCurrentItem(it.ordinal, false)
//        }
        viewModels.count.observe(viewLifecycleOwner){
            Log.w("ListExists", "$it")
        }

        viewModels.isFlaxRateFull.observe(viewLifecycleOwner){
            Log.w("FLIXRate",it.toString())
        }
    }

    private fun setUp() {
//        initAdapter()
//        viewModels.setCurrentTab(TransactionTab.TAB_FLAX)
//        binding.transactionPager.apply {
//            isUserInputEnabled = false
//            adapter = mTransactionPagerAdapter
//        }
//
//        binding.textviewViewAll.visibility = View.GONE

        binding.buttonSendFlax.setOnClickListener {
            ensureInteractionAllowed {
                gotoSendFlax()
            }
        }
        binding.buttonSellFlax.setOnClickListener {
            ensureInteractionAllowed {
                    gotoSellFlax()
            }
        }
        binding.buttonRestoreRatings.setOnClickListener {
            if(viewModels.isFlaxRateFull.value == true){
                Toast.makeText(requireContext(),"Your Rating is 100%",Toast.LENGTH_SHORT).show()
            }else{
                gotoRestoreRatings()
            }
        }

        binding.buttonBuyCoin.setOnClickListener {
            gotoBuyCoin()
        }
        binding.buttonConCoinToFlax.setOnClickListener {
            gotoCoinToFlax()
        }
        binding.buttonConFlaxToCoin.setOnClickListener {
            gotoFlaxToCoin()
        }

//        binding.textviewViewAll.setOnClickListener {
//            gotoViewAll()
//        }
        binding.buttonBuyFlix.setOnClickListener {
            gotoBuyFlix()
        }
        binding.buttonTransactions.setOnClickListener {
            gotoViewAll()
        }

//        binding.btnFlax.setOnClickListener {
//            viewModels.setCurrentTab(TransactionTab.TAB_FLAX)
//            (it as MaterialButton).isChecked = true
//        }
//        binding.btnCoins.setOnClickListener {
//            viewModels.setCurrentTab(TransactionTab.TAB_COINS)
//            (it as MaterialButton).isChecked = true
//        }
//        viewModels.currentTab.value?.let {
//            binding.transactionPager.setCurrentItem(it.ordinal, false)
//        }


    }

  /*  private fun initAdapter() {
        mTransactionPagerAdapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = TransactionTab.values().size
            override fun createFragment(position: Int): Fragment {
                val tab = TransactionTab.values()[position]
                return when (tab) {
                    TransactionTab.TAB_FLAX -> FlaxTransactionAllFragment()
                    TransactionTab.TAB_COINS -> GiftConversionHistoryAllFragment()

                    else -> throw IllegalArgumentException("Invalid position: $position")
                }
            }

        }

    }*/
  fun checkEligibility(header: String?=null,body:String?=null,action:Boolean = false) {
      MaterialDialog(requireContext()).show {
          val view = DataBindingUtil.inflate<LayoutIdCardToolTipInformationBinding>(layoutInflater, R.layout.layout_id_card_tool_tip_information, null, false)
          view.textHeader = header
          view.textBodys = body
          view.actionVisibility = action
          customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
          cancelable(true)
          view.actionClose.setOnClickListener {
              dismiss()
          }
          view.actionCloseOthers.setOnClickListener {
              dismiss()
          }
      }
  }

    abstract fun gotoSendFlax()
    abstract fun gotoBuyCoin()
    abstract fun gotoCoinToFlax()
    abstract fun gotoFlaxToCoin()
    abstract fun gotoViewAll()
    abstract fun gotoSellFlax()
    abstract  fun gotoRestoreRatings()
    abstract fun gotoBuyFlix()
}