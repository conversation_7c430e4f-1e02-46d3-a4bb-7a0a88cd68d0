package com.app.messej.ui.home.publictab.socialAffairs.caseInfo

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.R
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfoResponse
import com.app.messej.data.model.api.socialAffairs.SocialVoteErrorResponse.SocialError
import com.app.messej.data.model.api.socialAffairs.SocialVoteRequest
import com.app.messej.data.model.enums.MySocialSupportActionMenuItem
import com.app.messej.data.model.enums.SocialCaseStatus
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.SocialAffairsRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SocialCaseInfoViewModel(application: Application) : AndroidViewModel(application = application) {

    private val socialAffairRepo = SocialAffairsRepository(context = application)

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser
        get() = accountRepo.user

    private val _viewState = MutableLiveData(MultiStateView.ViewState.LOADING)
    val viewState: LiveData<MultiStateView.ViewState> = _viewState

    val errorMessage = LiveEvent<String?>()

    private val _caseInfo = MutableLiveData<SocialCaseInfoResponse?>()
    val caseInfo: LiveData<SocialCaseInfoResponse?> = _caseInfo

    private var caseId : Int? = null
    private var isUpgradeSupport: Boolean = false

    fun setCaseId(id: Int?, isUpgradeSupport: Boolean) {
        if (caseId == null) {
            caseId = id
            this.isUpgradeSupport = isUpgradeSupport
            getCaseInfoDetail()
        }
    }

    fun getCaseInfoDetail() {
        viewModelScope.launch(context = Dispatchers.IO) {
            _viewState.postValue(MultiStateView.ViewState.LOADING)
            val response = socialAffairRepo.getCaseInfo(id = "$caseId", isIncludeResidentStatusOnly = isUpgradeSupport)
            when(response) {
                is ResultOf.Success -> {
                    _caseInfo.postValue(response.value)
                    _viewState.postValue(MultiStateView.ViewState.CONTENT)
                }
                is ResultOf.APIError -> {
                    errorMessage.postValue(response.error.message)
                    _viewState.postValue(MultiStateView.ViewState.ERROR)
                }
                is ResultOf.Error -> {
                    _viewState.postValue(MultiStateView.ViewState.ERROR)
                }
            }
        }
    }

    fun updateQuestionsCount(newQuestionsCount: Int) {
        val updatedCaseInfo = _caseInfo.value?.copy(questionsCount = newQuestionsCount)
        _caseInfo.postValue(updatedCaseInfo)
        Log.d("SAQ", "New Count updated in case info -> $updatedCaseInfo")
    }

    val onVoteError = LiveEvent<SocialError?>()
    val isVoteSubmitting = MutableLiveData<Boolean>()
    val onVoteSuccess = LiveEvent<Boolean>()
    fun vote(voteAction: SocialVoteAction, caseId: Int?) {
        viewModelScope.launch(context = Dispatchers.IO) {
            isVoteSubmitting.postValue(true)
            val request = SocialVoteRequest(socialRequestId = caseId, voteAction = voteAction)
            val response = socialAffairRepo.vote(request = request)
            when(response) {
                is SocialAffairsRepository.SocialResultOf.Success -> {
                    getCaseInfoDetail()
                    onVoteSuccess.postValue(true)
                }
                is SocialAffairsRepository.SocialResultOf.APIError -> {
                    if (response.code == 403 && response.error.result?.reason != null) {
                        onVoteError.postValue(response.error.result)
                    } else {
                        errorMessage.postValue(response.error.message)
                    }
                }
                is SocialAffairsRepository.SocialResultOf.Error -> {
                    errorMessage.postValue(response.exception.message)
                }
            }
            isVoteSubmitting.postValue(false)
        }
    }

    //Archive, UnArchive, Delete for personal cases from case info screen
    val isPersonalCaseActionLoading = MutableLiveData<Boolean>()
    val isCaseStatusChanged = LiveEvent<Boolean>()
    val onCaseDeletedMessage = LiveEvent<Int>()
    fun changePersonalCaseStatus(
        newStatus: MySocialSupportActionMenuItem
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val previousStatus = _caseInfo.value?.data?.status
            isPersonalCaseActionLoading.postValue(true)
            val response = socialAffairRepo.editPersonalSupportCase(id = "$caseId", status = newStatus)
            when(response) {
                is SocialAffairsRepository.SocialResultOf.Success -> {
                    getCaseInfoDetail()
                    when {
                        previousStatus == SocialCaseStatus.ARCHIVED && newStatus == MySocialSupportActionMenuItem.Delete -> {
                            onCaseDeletedMessage.postValue(R.string.social_archive_case_delete_message)
                        }
                        previousStatus == SocialCaseStatus.NEW && newStatus == MySocialSupportActionMenuItem.Delete -> {
                            onCaseDeletedMessage.postValue(R.string.social_active_case_delete_message)
                        }
                    }
                    isCaseStatusChanged.postValue(true)
                }
                is SocialAffairsRepository.SocialResultOf.APIError -> {
                    errorMessage.postValue(response.error.message)
                }
                is SocialAffairsRepository.SocialResultOf.Error -> {
                    errorMessage.postValue(response.exception.message)
                }
            }
            isPersonalCaseActionLoading.postValue(false)
        }
    }

    val onCaseApproved = LiveEvent<Boolean>()
    val isApprovingCase = MutableLiveData<Boolean>()
    fun approveCase() {
        viewModelScope.launch(Dispatchers.IO) {
            isApprovingCase.postValue(true)
            val response = socialAffairRepo.approveCase(caseId = caseId)
            when(response) {
                is ResultOf.Success -> {
                    getCaseInfoDetail()
                    onCaseApproved.postValue(true)
                }
                is ResultOf.APIError -> {
                    errorMessage.postValue(response.error.message)
                }
                is ResultOf.Error -> {
                    errorMessage.postValue(response.errorMessage())
                }
            }
            isApprovingCase.postValue(false)
        }
    }

}