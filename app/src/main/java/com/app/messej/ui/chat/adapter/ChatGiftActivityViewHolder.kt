package com.app.messej.ui.chat.adapter

import android.util.Log
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.databinding.ItemChatGiftActivityBinding
import com.app.messej.ui.chat.ChatMessageUIModel

class ChatGiftActivityViewHolder(val binding: ItemChatGiftActivityBinding,private var mListener: ChatAdapter.ChatClickListener): ChatAdapter.ChatViewHolder(binding.root) {
    override fun bind(item: ChatMessageUIModel) = with(binding) {
        val cm = (item as ChatMessageUIModel.ChatMessageModel)
        message = cm.message
        root.setOnClickListener {
            val giftItem = item.chat.message.activityMeta
            val senderId =item.chat.message.sender
            val receiverId =item.chat.message.receiver
            Log.d("COINBA","Coin baance"+giftItem)
            giftItem?.let {item->
                try {
                    if(item.giftAnimationUrlAndroid!=null||item.giftGifUrl!=null) {
                        val giftPayload= SentGiftPayload(
                            id = item.giftId!!,
                            giftName = item.giftName,
                            coinsReceived = item._giftCoinValue,
                            senderName = item.giftSenderName,
                            giftAnimationUrlAndroid = item.giftAnimationUrlAndroid,
                            giftType = item.giftType,
                            description = item.giftDescription,
                            giftIdentifier = item.giftName,
                            giftAnimationUrl = item.giftAnimationUrlAndroid,
                            animationUrl = item.giftGifUrl,
                            thumbnail = null,
                            senderId = senderId,
                            receiverId = receiverId,
                            flix = null,
                            coins = null,
                            categoryName = null,
                            categoryId = null)

                        mListener.onGiftClick(giftPayload, layoutPosition)
                    }else{
                        Log.w("gifts"," No video url is available")
                    }
                }catch (e:Exception){
                    Log.w("gifts","Exception ${e.message}")
                }

            }

        }
    }
}