package com.app.messej.ui.home.publictab.postat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.R
import com.app.messej.data.model.enums.PostatTab
import com.app.messej.ui.home.publictab.postat.mypostat.MyPostatListFragment

class PublicPostatViewPagerFragment : PublicPostatBaseFragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_postat_base, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun setup() {
        mPostatPagerAdapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = PostatTab.entries.size

            override fun createFragment(position: Int): Fragment {
                val tab = PostatTab.entries[position]

                return when (tab) {
                    PostatTab.ME -> MyPostatListFragment()
                    PostatTab.STARS -> PostatStarsFeedFragment()
                    PostatTab.ALL -> PostatFullFeedFragment()
                    else -> throw Exception("Tab is UNSET. This should not happen")
                }
            }
        }
        super.setup()
    }
}