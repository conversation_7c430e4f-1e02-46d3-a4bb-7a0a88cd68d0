package com.app.messej.ui.home.publictab.authorities.legalAffairs.violations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairsStatisticsDropdown
import com.app.messej.databinding.FragmentLegalAffairsViolationStatisticsBinding
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils
import com.app.messej.ui.home.publictab.authorities.legalAffairs.CaseDetailsBottomSheet.Companion.CASE_DETAIL_REQUEST_KEY
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsFragmentDirections
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils

class LegalAffairsViolationStatisticsFragment : Fragment() {
    
    private lateinit var binding: FragmentLegalAffairsViolationStatisticsBinding
    private val viewModel: LegalAffairsViolationStatisticsViewModel by viewModels()
    private lateinit var activeCasesAdapter: LegalAffairsCaseListAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_legal_affairs_violation_statistics, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        setDropdownMenu()
        setupGraphView()
        setupClickEvents()
        initAdapter()
    }

    private fun initAdapter() {
        activeCasesAdapter = LegalAffairsCaseListAdapter(object : LegalAffairsCaseListAdapter.ItemClickListener {
            override fun getUserId() = viewModel.user.id
            override fun cardBackgroundColor(): Int? = R.color.colorSurface
            override fun onClick(case: LegalRecordsResponse.ReportCase) {
                findNavController().navigateSafe(LegalAffairsFragmentDirections.actionLegalAffairsFragmentToCaseDetailBottomSheet(caseID = case.id))
            }
        })

        val linearLayoutManager = LinearLayoutManager(context)
        binding.activeCases.recyclerView.apply {
            isNestedScrollingEnabled = true
            layoutManager = linearLayoutManager
            adapter = activeCasesAdapter
        }

        activeCasesAdapter.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.activeCases.multiStateView.viewState = state
            }
        }
        AuthoritiesUtils.setupListEmptyView(
            multiStateView = binding.activeCases.multiStateView
        )
    }

    private fun setDropdownMenu() {
        val items = resources.getStringArray(R.array.legal_affairs_violation_statistics)
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, items)
        binding.autoCompleteTextView.apply {
            setAdapter(adapter)
            setText(items[0], false)
            setOnItemClickListener { _, _, position, _ ->
                val selectedItem = items[position]
                setText(selectedItem, false)
                viewModel.setDropdownItem(LegalAffairsStatisticsDropdown.entries[position])
            }
        }
    }

    private fun setUpStatisticsCount(selectedTab:LegalAffairsStatisticsDropdown) {
        binding.apply {
            layoutUnderInvestigation.title = getString(R.string.legal_affairs_under_investigation)
            when(selectedTab) {
                LegalAffairsStatisticsDropdown.YourContent -> {
                    layoutContentsReported.title = getString(R.string.legal_affairs_contents_reported)
                    layoutFoundViolating.title = getString(R.string.legal_affairs_found_violating)
                    layoutNotViolating.title = getString(R.string.legal_affairs_found_not_violating)
                }
                LegalAffairsStatisticsDropdown.YouPersonally -> {
                    layoutContentsReported.title = getString(R.string.legal_affairs_no_of_reports)
                    layoutFoundViolating.title = getString(R.string.legal_affairs_found_guilty)
                    layoutNotViolating.title = getString(R.string.legal_affairs_found_not_guilty_two_line)
                }
            }
        }
    }

    private fun setupClickEvents() {
        binding.appealedView.icArrowMarkRounded.setOnClickListener {
            viewModel.setGraphVisibility()
        }
    }

    private fun setupGraphView() {
        binding.appealedView.composeGraphView.setContent {
            val appealedGraphValues by viewModel.appealedGraphValues.observeAsState()
            CustomChartView(items = appealedGraphValues)
        }
    }

    private fun observe() {
        viewModel.isGraphVisible.observe(viewLifecycleOwner) { isVisible ->
            binding.appealedView.apply {
                composeGraphView.visibility = if (isVisible) View.VISIBLE else View.GONE
                icArrowMarkRounded.animate().rotation(
                    if (isVisible) 180F else 0F
                ).setDuration(300).start()
            }
        }

        viewModel.statisticsList.observe(viewLifecycleOwner) {
            activeCasesAdapter.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.statisticCount.observe(viewLifecycleOwner) {
            binding.apply {
                layoutContentsReported.count = "${it?.reportedCount ?: 0}"
                layoutFoundViolating.count = "${it?.guilty ?: 0}"
                layoutNotViolating.count = "${it?.notGuilty ?: 0}"
                layoutUnderInvestigation.count = "${it?.underInvestigation ?: 0}"
                appealedView.appealedCount = "${it?.appeal ?: 0}"
                appealedView.totalCount = "${it?.reportedCount ?: 0}"
            }
        }

        viewModel.selectedDropdownItem.observe(viewLifecycleOwner) { tab ->
            setUpStatisticsCount(selectedTab = tab)
        }

        setFragmentResultListenerOnActivity(CASE_DETAIL_REQUEST_KEY) { _, _ ->
            activeCasesAdapter.refresh()
        }
    }
}