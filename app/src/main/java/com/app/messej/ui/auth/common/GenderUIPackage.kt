package com.app.messej.ui.auth.common

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.app.messej.R
import com.app.messej.data.model.enums.Gender

data class GenderUIPackage(
    @DrawableRes val image: Int,
    @StringRes val name: Int,
    val code: Gender,
    var selected: Boolean = false
) {
    companion object {
        fun getGenderUIList(): List<GenderUIPackage> {
            return listOf(
                GenderUIPackage(R.drawable.ic_gender_male, R.string.register_create_profile_gender_male, Gender.MALE),
                GenderUIPackage(R.drawable.ic_gender_female, R.string.register_create_profile_gender_female, Gender.FEMALE),
                GenderUIPackage(R.drawable.ic_gender_na, R.string.register_create_profile_gender_na, Gender.NOT_SAID),
            )
        }
    }
}