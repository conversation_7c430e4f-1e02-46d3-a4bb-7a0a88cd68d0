package com.app.messej.ui.home.publictab.myETribe.contactETribe

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.RadioButton
import androidx.compose.material.RadioButtonDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import com.app.messej.R
import com.app.messej.ui.composeComponents.FlashatComposeTypography

@Composable
fun CustomRadioButtonItem(
    text: String,
    isSelected: Boolean,
    onClick:() -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                indication = null,
                       interactionSource = remember { MutableInteractionSource() }) { onClick() },
        verticalAlignment = Alignment.CenterVertically
    ) {
        RadioButton(
            selected = isSelected,
            onClick = onClick,
            colors = RadioButtonDefaults.colors(
                selectedColor = colorResource(id = R.color.colorPrimary),
                unselectedColor = colorResource(id = R.color.textColorAlwaysDarkHint)
            )
        )
        Text(
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.element_spacing)).fillMaxWidth(),
            text = text,
            color = colorResource(id = R.color.textColorPrimary),
            style = FlashatComposeTypography.defaultType.body2
        )
    }
}

@Composable
fun CustomCheckBoxButtonItem(
    text: String,
    isSelected: Boolean,
    onClick:(isChecked : Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Checkbox(
            checked = isSelected,
            onCheckedChange = { onClick(it) },
            colors = CheckboxDefaults.colors(
                checkedColor = colorResource(id = R.color.colorPrimary),
                uncheckedColor = colorResource(id = R.color.textColorAlwaysDarkHint)
            )
        )
        Text(
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.element_spacing)).fillMaxWidth(),
            text = text,
            color = colorResource(id = R.color.textColorPrimary),
            style = FlashatComposeTypography.defaultType.body2
        )
    }
}