package com.app.messej.ui.home.deleteaccount

import android.content.Context
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.widget.addTextChangedListener
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.app.messej.R
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.FragmentDeleteAccountEmailBinding

class DeleteAccountEmailFragment :Fragment(){


    private lateinit var binding:FragmentDeleteAccountEmailBinding
    val viewModel:DeleteBottomSheetViewModel by viewModels()
    private val deleteAccountViewModel:DeleteAccountViewModel by activityViewModels()
    private var accountDeletedListener: AccountDeletedListener? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_delete_account_email,container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    interface AccountDeletedListener {
        fun onAccountDeleted()
        fun onRequestBottomSheetDismiss()
    }

    private fun setup() {

        binding.deleteAccountEmailAddress.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && !this.text.isNullOrEmpty()) {
                    viewModel.didEnterEmail.postValue(true)
                }else{
                    viewModel.didEnterEmail.postValue(false)
                }
                addTextChangedListener {
                        text ->
                    viewModel.didEnterPhoneNumber.value
                    if(UserInfoUtil.isEmailValid(text.toString())||text.toString().isEmpty()){
                        viewModel.setEmailValid(true)
                    }else{
                        viewModel.setEmailValid(false)
                    }

                }
            }
        }

        binding.root.rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val r = Rect()
            binding.root.rootView.getWindowVisibleDisplayFrame(r)
            val screenHeight: Int = binding.root.rootView.height
            val keypadHeight: Int = screenHeight - r.bottom
            if (keypadHeight < screenHeight * 0.15) {
                binding.deleteAccountEmailAddress.editText!!.clearFocus()
            }
        }
        binding.deleteAccountEmailAddress.editText?.setOnEditorActionListener {
                view, actioId, event ->
            binding.deleteAccountEmailAddress.editText!!.clearFocus()
            return@setOnEditorActionListener false
        }
        binding.deleteAccountNextButton.apply {
            setOnClickListener {
                if(binding.deleteAccountEmailAddress.editText?.text.toString().isNotEmpty()) {
                   viewModel.deleteAccountEmail(binding.deleteAccountEmailAddress.editText?.text.toString())
                }
            }
        }
        binding.actionAccountCancel.setOnClickListener {
            deleteAccountViewModel.setCancelClick(true)
            accountDeletedListener?.onRequestBottomSheetDismiss()
        }
    }

    private fun observe() {
        viewModel.showEmailInvalidError.observe(viewLifecycleOwner) {
            if (!binding.deleteAccountEmailAddress.editText?.text.isNullOrEmpty()) {
                binding.deleteAccountEmailError.text = if (it) {
                    binding.deleteAccountNextButton.isEnabled = false
                    binding.deleteAccountEmailError.text
                    resources.getString(R.string.forgot_password_error_email)
                } else {
                    ""; null
                }
            }
        }
        viewModel.isEmailNextButtonVisible.observe(viewLifecycleOwner){
            if (!binding.deleteAccountEmailAddress.editText?.text.isNullOrEmpty()) {
                binding.deleteAccountNextButton.isEnabled = it == false
            }
        }

        viewModel.unSelectedPage.observe(viewLifecycleOwner){
            if(it==0){ binding.deleteAccountEmailAddress.editText?.setText("")
                binding.deleteAccountEmailError.text = ""
                binding.deleteAccountNextButton.isEnabled=false
            }
        }

        viewModel.accountDeleteError.observe(viewLifecycleOwner){
            it?.let {
                if (it.isNotEmpty()){
                    Toast.makeText(requireContext(),it,Toast.LENGTH_SHORT).show()
                }
            }
        }
        viewModel.isAccountDeleted.observe(viewLifecycleOwner){
            it?.let {
                if(it) {
                    deleteAccountViewModel.setAccountDeleted(true)
                    accountDeletedListener?.onRequestBottomSheetDismiss()
                    accountDeletedListener?.onAccountDeleted()
                }
            }
        }
        viewModel.onAccountDeleted.observe(viewLifecycleOwner){
            if (it){
                deleteAccountViewModel.setCancelClick(true)
                Toast.makeText(requireContext(), getString(R.string.common_account_deleted), Toast.LENGTH_SHORT).show()
            }
        }
    }

    companion object {
        fun newInstance() = DeleteAccountEmailFragment()
    }



    override fun onAttach(context: Context) {
        super.onAttach(context)
        accountDeletedListener = parentFragment as? AccountDeletedListener
    }
}