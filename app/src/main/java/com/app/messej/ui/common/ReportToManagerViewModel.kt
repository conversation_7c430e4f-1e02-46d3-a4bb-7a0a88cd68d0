package com.app.messej.ui.common

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.ReportCategoryResponse.ReportCategory
import com.app.messej.data.model.api.ReportToManagerRequest
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ReportToManagerViewModel(application: Application): AndroidViewModel(application) {

    private val chatRepo = ChatRepository(getApplication())

    companion object {
        const val COMMENT_MAX_LENGTH = 300
    }

    private var _request: ReportToManagerRequest? = null

    fun setReportRequest(req: ReportToManagerRequest) {
        _request = req
        getReportCategories()
    }

    private val _reportCategories : MutableLiveData<List<ReportCategory>?> = MutableLiveData(null)
    val reportCategories: LiveData<List<ReportCategory>?> = _reportCategories

    private fun getReportCategories(){
        viewModelScope.launch(Dispatchers.IO){
            val result = chatRepo.getMessageReportCategories()
            when(result){
                is ResultOf.Success -> {
                    _reportCategories.postValue(result.value.categories)
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
                else -> {}
            }
        }
    }

    val reason = MutableLiveData<ReportCategory?>(null)
    val comment = MutableLiveData<String>()

    val commentValid = comment.map {
        return@map it.trim().length <= COMMENT_MAX_LENGTH
    }

    private val commentIsNotEmpty = comment.map {
        return@map it.trim().isNotEmpty()
    }

    private val _reportValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)

        fun validate() {
            _reportValid.postValue(
                commentValid.value == true && reason.value!=null && commentIsNotEmpty.value == true
            )
        }
        med.addSource(commentValid) {validate()}
        med.addSource(reason) {validate()}
        med.addSource(commentIsNotEmpty) {validate()}
        med
    }

    val reportValid: LiveData<Boolean> = _reportValid

    private val _reportLoading = MutableLiveData(false)
    val reportLoading: LiveData<Boolean> = _reportLoading

    val onReportMessageComplete = LiveEvent<Boolean>()

    fun reportMessage() {
        viewModelScope.launch(Dispatchers.IO){
            val req = _request?: return@launch

            req.categoryId = reason.value!!.categoryId
            req.comment = comment.value.orEmpty()
            Log.d("reportMessage", "$req")
            _reportLoading.postValue(true)
            val result = when(req) {
                is ReportToManagerRequest.MessageReportRequest -> chatRepo.reportMessage(req)
                is ReportToManagerRequest.CommentReportRequest -> chatRepo.reportComment(req)
                else -> null
            }
            when(result){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    onReportMessageComplete.postValue(true)
                }
                else -> {}
            }
            _reportLoading.postValue(false)
        }
    }
}