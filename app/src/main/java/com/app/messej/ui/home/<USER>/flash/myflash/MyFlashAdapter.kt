package com.app.messej.ui.home.publictab.flash.myflash

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.entity.FlashVideoWithMedia
import com.app.messej.databinding.ItemFlashListMineBinding
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.RequestOptions

class MyFlashAdapter(private val listener: FlashClickListener) : PagingDataAdapter<MyFlashAdapter.MyFlashUIModel, MyFlashAdapter.FlashListViewHolder>(FeedsDiff) {

    data class MyFlashUIModel(
        val flash: FlashVideoWithMedia,
        val transfer: MediaTransfer?
    )

    interface FlashClickListener {
        fun onFlashClicked(pos: Int, flash: FlashVideoWithMedia)
        fun onFlashLongPressed(pos: Int, flash: FlashVideoWithMedia, view: View)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = FlashListViewHolder(
        ItemFlashListMineBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun onBindViewHolder(holder: FlashListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class FlashListViewHolder(private val binding: ItemFlashListMineBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: MyFlashUIModel) = with(binding) {
            flash = item.flash.flash
            isOffline = item.flash.media!=null
            item.flash.media?.let { med ->
                val options = RequestOptions().frame(100)
                Glide.with(root.context).load(med.file).apply(options)
                    .transition(DrawableTransitionOptions.withCrossFade(300))
                    .into(flashThumb)
            }
            transfer = item.transfer
            flashCard.setOnClickListener {
                listener.onFlashClicked(bindingAdapterPosition,item.flash)
            }
            if(item.flash.media==null) {
                flashCard.setOnLongClickListener {
                    Log.d("FlashListAdapter", "bind: Long press on my flash")
                    listener.onFlashLongPressed(bindingAdapterPosition, item.flash, it)
                    true
                }
            }
        }
    }


    object FeedsDiff : DiffUtil.ItemCallback<MyFlashUIModel>() {
        override fun areItemsTheSame(oldItem: MyFlashUIModel, newItem: MyFlashUIModel): Boolean {
            return oldItem.flash.flash.id == newItem.flash.flash.id
        }

        override fun areContentsTheSame(oldItem: MyFlashUIModel, newItem: MyFlashUIModel): Boolean {
            return oldItem.flash == newItem.flash && oldItem.flash.media == newItem.flash.media
        }
    }
}