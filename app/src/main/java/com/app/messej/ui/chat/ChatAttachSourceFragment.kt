package com.app.messej.ui.chat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.enums.AttachmentSource
import com.app.messej.data.utils.EnumUtil
import com.app.messej.databinding.FragmentAttachSourceBottomSheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class ChatAttachSourceFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentAttachSourceBottomSheetBinding
    private val args: ChatAttachSourceFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding =
            DataBindingUtil.inflate(inflater, R.layout.fragment_attach_source_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        binding.srcGalleryHolder.isVisible = args.allowedSources.contains(AttachmentSource.GALLERY)
        binding.attachGallery.setOnClickListener {
            confirmSource(AttachmentSource.GALLERY)
        }

        binding.srcCameraHolder.isVisible = args.allowedSources.contains(AttachmentSource.CAMERA_PHOTO)
        binding.attachCamera.setOnClickListener {
            confirmSource(AttachmentSource.CAMERA_PHOTO)
        }

        binding.srcVideoHolder.isVisible = args.allowedSources.contains(AttachmentSource.CAMERA_VIDEO)
        binding.attachVideo.setOnClickListener {
            confirmSource(AttachmentSource.CAMERA_VIDEO)
        }

        binding.srcLocationHolder.isVisible = args.allowedSources.contains(AttachmentSource.LOCATION)
        binding.attachLocation.setOnClickListener {
            confirmSource(AttachmentSource.LOCATION)
        }

        binding.srcDocumentHolder.isVisible = args.allowedSources.contains(AttachmentSource.DOCUMENT)
        binding.attachDocument.setOnClickListener {
            confirmSource(AttachmentSource.DOCUMENT)
        }
    }

    private fun confirmSource(src: AttachmentSource) {
        findNavController().popBackStack()
        setFragmentResult(ATTACH_SOURCE_RESULT_KEY, bundleOf(ATTACH_SOURCE_RESULT_KEY to src.name))
    }

    companion object {
        const val ATTACH_SOURCE_RESULT_KEY = "attach_src"
        fun getSource(bundle: Bundle): AttachmentSource? {
            val str = bundle.getString(ATTACH_SOURCE_RESULT_KEY)?: return null
            return EnumUtil.enumValueOrNull(str)
        }
    }

}