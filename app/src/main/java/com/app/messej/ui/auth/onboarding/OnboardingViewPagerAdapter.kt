package com.app.messej.ui.auth.onboarding

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.appcompat.content.res.AppCompatResources
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.databinding.LayoutOnboardingViewPagerBinding

class OnboardingViewPagerAdapter : RecyclerView.Adapter<OnboardingViewPagerAdapter.ViewPagerViewHolder>(){

    private var _binding:LayoutOnboardingViewPagerBinding? = null
    private val binding get() = _binding!!
    inner class ViewPagerViewHolder(private val itemBinding: LayoutOnboardingViewPagerBinding) : RecyclerView.ViewHolder(itemBinding.root){
        fun bind(position: Int){
            when (position) {
                0 -> {
                    itemBinding.ivViewPager.setImageDrawable(AppCompatResources.getDrawable(itemBinding.root.context, R.drawable.bg_onboarding_graphics_one))
                    itemBinding.onboardingHeading.text = itemBinding.root.context.getString(R.string.onboarding_heading_1)
                    itemBinding.onboardingSubHeading.text = itemBinding.root.context.getString(R.string.onboarding_sub_heading_1)
                }
                1 -> {
                    itemBinding.ivViewPager.setImageDrawable(AppCompatResources.getDrawable(itemBinding.root.context, R.drawable.bg_onboarding_graphics_two))
                    itemBinding.onboardingHeading.text = itemBinding.root.context.getString(R.string.onboarding_heading_2)
                    itemBinding.onboardingSubHeading.text = itemBinding.root.context.getString(R.string.onboarding_sub_heading_2)
                    itemBinding.root.parent
                }
                else -> {
                    itemBinding.ivViewPager.setImageDrawable(AppCompatResources.getDrawable(itemBinding.root.context, R.drawable.bg_onboarding_graphics_three))
                    itemBinding.onboardingHeading.text = itemBinding.root.context.getString(R.string.onboarding_heading_3)
                    itemBinding.onboardingSubHeading.text = itemBinding.root.context.getString(R.string.onboarding_sub_heading_3)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewPagerViewHolder {
        _binding = LayoutOnboardingViewPagerBinding.inflate(LayoutInflater.from(parent.context), parent, false)
//        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_view_pager, parent,  false)
        return ViewPagerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewPagerViewHolder, position: Int) {
        holder.bind(position)
    }

    override fun getItemCount(): Int {
        return 3
    }
}