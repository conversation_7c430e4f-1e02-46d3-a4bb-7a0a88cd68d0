package com.app.messej.ui.home.privatetab.messages

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.TerminalSeparatorType
import androidx.paging.cachedIn
import androidx.paging.insertSeparators
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

@OptIn(FlowPreview::class)
class PrivateMessageSearchViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val huddleRepo = HuddlesRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())

    var searchKeyword = MutableLiveData<String>(null)
    private val searchTerm = MutableLiveData("")

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                it?: return@collect
                if (it.isBlank()){
                    searchTerm.postValue("")
                }else{
                    searchTerm.postValue(it)
                }
            }
        }
    }

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    val user: CurrentUser get() = accountRepo.user

    private val _suggestionList = searchTerm.switchMap { searchTerm ->
        huddleRepo.getNewMessageSearchSuggestionsList(searchKeyword = searchTerm)
            .cachedIn(viewModelScope)
    }

    val messageSearchSuggestionList : MediatorLiveData<PagingData<PrivateMessageSearchListAdapter.PrivateMessagesSearchUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<PrivateMessageSearchListAdapter.PrivateMessagesSearchUIModel>?>(null)
        fun updateSearchList() {
            _dataLoading.value = false
            val list = _suggestionList.value?.map { data ->
                PrivateMessageSearchListAdapter.PrivateMessagesSearchUIModel.UserModel(data)
            }?.insertSeparators(TerminalSeparatorType.SOURCE_COMPLETE) { before, after ->
                return@insertSeparators if ((before?.user?.isSuggestion == false) && (after?.user?.isSuggestion == true)){
                    PrivateMessageSearchListAdapter.PrivateMessagesSearchUIModel.RecommendationModel
                } else null
            }
            messageSearchSuggestionList.postValue(list)
        }
        med.addSource(_suggestionList) { updateSearchList() }
        med
    }

    fun resetSearch() {
        searchKeyword.postValue("")
    }

    val onNavigateToPrivateMessage = LiveEvent<Pair<String,Int>>()

    fun navigateToPrivateMessage(receiver: Int) {
        viewModelScope.launch {
            val roomId = HuddlesRepository(getApplication()).getPrivateChatRoomId(receiver)
            onNavigateToPrivateMessage.postValue(Pair(roomId,receiver))
        }
    }
}