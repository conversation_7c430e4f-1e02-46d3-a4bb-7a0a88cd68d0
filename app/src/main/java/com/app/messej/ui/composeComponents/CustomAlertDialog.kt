package com.app.messej.ui.composeComponents

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.AlertDialog
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.DialogProperties
import com.app.messej.R

@Composable
fun CustomAlertDialog(
    title: String? = null,
    description: String,
    onNegativeButtonClick: (() -> Unit)? = null,
    onDismissRequest: (() -> Unit)? = null,
    onPositiveButtonClick: (() -> Unit)? = null,
    positiveButtonText: String = stringResource(id = R.string.common_ok),
    negativeButtonText: String = stringResource(id = R.string.common_cancel),
    dismissOnBackPress: Boolean = false,
    dismissOnClickOutside: Boolean = false
) {
    AlertDialog(
        backgroundColor = colorResource(id = R.color.colorSurfaceSecondaryDark),
        shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)),
        properties = DialogProperties(dismissOnBackPress = dismissOnBackPress, dismissOnClickOutside = dismissOnClickOutside),
        onDismissRequest = { onDismissRequest?.let { it() } },
        buttons = {
            Row(
                modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.element_spacing)).fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                onNegativeButtonClick?.let {
                    TextButton(onClick = onNegativeButtonClick) {
                        Text(text = negativeButtonText,
                             style = FlashatComposeTypography.defaultType.button,
                             color = colorResource(id = R.color.colorError)
                        )
                    }
                }
                onPositiveButtonClick?.let {
                    TextButton(onClick = onPositiveButtonClick) {
                        Text(text = positiveButtonText,
                             style = FlashatComposeTypography.defaultType.button,
                             color = colorResource(id = R.color.colorPrimary)
                        )
                    }
                }
            }
        },
        title = title?.let {
            { Text(text = title, 
                   modifier = Modifier.fillMaxWidth(),
                   style = FlashatComposeTypography.defaultType.h6,
                   color = colorResource(id = R.color.textColorPrimary)
            ) }
        },
        text = {
            Text(
                text = description,
                modifier = Modifier.fillMaxWidth(),
                style = FlashatComposeTypography.defaultType.body2,
                color = colorResource(id = R.color.textColorPrimary)
            )
        }
    )
}

@Preview()
@Composable
private fun CustomAlertPreview() {
    CustomAlertDialog(
        title = "Custom Alert Dialog",
        description = "Sample Description",
        onNegativeButtonClick = {},
        onPositiveButtonClick = {}
    )
}
