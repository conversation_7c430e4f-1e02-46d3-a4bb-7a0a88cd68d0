package com.app.messej.ui.home.businesstab.adapter

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.app.messej.R
import com.app.messej.data.model.api.DealsBeneficiary
import com.app.messej.data.model.enums.UserType
import com.bumptech.glide.Glide
import com.makeramen.roundedimageview.RoundedImageView

class DealsBeneficiaryAdapter(c: Context, beneficiary: List<DealsBeneficiary>) : ArrayAdapter<DealsBeneficiary>(c, R.layout.item_beneficiary_list, beneficiary) {


    private val mContext = c
    val mBeneficiary = beneficiary

    private var selectedPos: Int? = null
    fun setSelectedPos(pos: Int) {
        selectedPos = pos
    }

    override fun getItem(position: Int): DealsBeneficiary {
        return mBeneficiary[position]
    }

    override fun getCount(): Int {
        return mBeneficiary.size
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var cView = convertView
        if (cView == null) {
            val inflater = (mContext as Activity).layoutInflater
            cView = inflater.inflate(R.layout.item_beneficiary_list, parent, false)
        }
        try {

            getItem(position).let {
                cView?.apply {
                    val text: AppCompatTextView = findViewById(R.id.textView_person_name)
                    val subText: AppCompatTextView = findViewById(R.id.textView_person_userName)
                    val imageBadge: AppCompatImageView = findViewById(R.id.badge_premium)
                    if (it.membership == UserType.FREE) {
                        imageBadge.visibility = View.GONE
                    } else {
                        imageBadge.visibility = View.VISIBLE
                    }

                    text.text = it.name
                    subText.text = it.username
                    val image: RoundedImageView = findViewById(R.id.image_person_photo)
                    Glide.with(context).load(it.thumbnail) // Replace with the actual URL from your API
                        .placeholder(R.drawable.im_user_placeholder_opaque).into(image)
                }
            }


        } catch (e: Exception) {
            e.printStackTrace()
        }
        return cView!!
    }


}

