package com.app.messej.ui.chat

import android.app.Activity
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.NavChatGroupDirections
import com.app.messej.R
import com.app.messej.databinding.FragmentPublicHuddlesChatAttachImageBinding
import com.app.messej.ui.chat.imageedit.PostImageEditFragment
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.enableTextFormatting
import com.yalantis.ucrop.UCrop
import com.yalantis.ucrop.UCropActivity

abstract class BaseImageAttachFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentPublicHuddlesChatAttachImageBinding

    abstract val viewModel: BaseChatViewModel

    abstract val destinationName: String?

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_huddles_chat_attach_image, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        binding.destination = destinationName
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(binding.customActionBar.toolbar)
            setHomeIcon(R.drawable.ic_close)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_image_attach_preview,menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        return when (menuItem.itemId) {
            android.R.id.home -> {
                viewModel.clearChatMedia()
                return false
            }
            R.id.action_crop->{
                viewModel.cropAttachedMedia()
                return true
            }
            R.id.action_draw->{
                viewModel.editImage()
                return true
            }
            else -> false
        }
    }

    private val imageCropResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let {
                val resultUri = UCrop.getOutput(it)?: return@let
                viewModel.addImage(resultUri)
            }
        }
    }

    private fun setup() {
        binding.chatSendButton.setOnClickListener {
            viewModel.prepareAndSendMessage()
        }

        if (viewModel.enableTextFormatting) {
            binding.chatInput.enableTextFormatting(requireActivity(),viewModel)
        }



    }

    private fun observe() {
        viewModel.onMessageCreated.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }

        viewModel.onTriggerCrop.observe(viewLifecycleOwner) {
            val options = UCrop.Options().apply {
                val color = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                setToolbarColor(color)
                setStatusBarColor(color)
                setToolbarTitle(resources.getString(R.string.common_crop))
                setFreeStyleCropEnabled(true)
                setAllowedGestures(UCropActivity.SCALE, UCropActivity.ROTATE, UCropActivity.ALL)
            }
            val crop = UCrop.of(it.first, it.second)
                .withOptions(options)
            imageCropResult.launch(crop.getIntent(requireContext()))
        }

        viewModel.onTriggerImageEdit.observe(viewLifecycleOwner){
            findNavController().navigateSafe(NavChatGroupDirections.actionGlobalPostImageEdit(it.first,it.second))
        }

        setFragmentResultListener(PostImageEditFragment.IMAGE_SAVE_REQUEST_KEY) { _, bundle ->
            val resultUri = if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                bundle.getParcelable(PostImageEditFragment.IMAGE_SAVE_RESULT_KEY,Uri::class.java)
            } else{
                bundle.getParcelable(PostImageEditFragment.IMAGE_SAVE_RESULT_KEY) as Uri?
            }
            viewModel.addImage(resultUri!!)
        }
    }
}