package com.app.messej.ui.home.publictab.maidan

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import com.app.messej.databinding.ItemPodiumMaidanTopSupportersBottomsheetBinding

class PodiumMaidanTopSupportersBottomSheetAdapter : PagingDataAdapter<PodiumMaidanSupporter, PodiumMaidanTopSupportersBottomSheetAdapter.PodiumMaidanTopSupportersViewHolder>(DiffCallback) {

    inner class PodiumMaidanTopSupportersViewHolder(private val binding: ItemPodiumMaidanTopSupportersBottomsheetBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumMaidanSupporter) = with(binding) {
            speaker = item
        }
    }

    object DiffCallback : DiffUtil.ItemCallback<PodiumMaidanSupporter>() {
        override fun areItemsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: PodiumMaidanSupporter,
            newItem: PodiumMaidanSupporter,
        ): Boolean {
            return oldItem.score == newItem.score
        }
    }

    override fun onBindViewHolder(holder: PodiumMaidanTopSupportersViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumMaidanTopSupportersViewHolder {
        val binding = ItemPodiumMaidanTopSupportersBottomsheetBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumMaidanTopSupportersViewHolder(binding)
    }
}