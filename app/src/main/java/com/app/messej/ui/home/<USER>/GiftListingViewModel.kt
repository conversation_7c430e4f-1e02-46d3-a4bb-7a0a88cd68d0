package com.app.messej.ui.home.gift

import GiftListUIModel
import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.insertSeparators
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.api.gift.GiftSendResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.enums.CurrencyType
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.GiftRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class GiftListingViewModel(app: Application):AndroidViewModel(app) {

    private val giftRepository: GiftRepository = GiftRepository(app)
    private val profileRepo:ProfileRepository= ProfileRepository(app)
    private  val accountRepo:AccountRepository= AccountRepository(app)

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    val onGiftSent=LiveEvent<SentGiftPayload>()
    val errorMessage=LiveEvent<String>()
    val insufficientBalance=LiveEvent<CurrencyType>()
    val isGiftReceiverBanned=LiveEvent<Boolean>()

    private val _giftParams = MutableLiveData<GiftParams?>(null)
    val giftParams: LiveData<GiftParams?> = _giftParams

    val giftBirthday: Boolean
        get() = _giftParams.value?.birthday==true
    val userLevelCongrats: Boolean
        get() = _giftParams.value?.userCongrats == true
    val isForChallenge: Boolean
        get() = !_giftParams.value?.challengeId.isNullOrBlank()

    val giftType = MutableLiveData(GiftType.PERSONAL)

    private val _giftItems = giftType.switchMap { type ->
        Log.d("TYPEGIFTAPI", "setCurrentTab: $type")
        giftRepository.getGiftPagerList(type, giftBirthday, userLevelCongrats).liveData.cachedIn(viewModelScope)
    }

    val giftItems = _giftItems.map { pagingData ->
        pagingData.map {
            GiftListUIModel.Gift(it)
        }.insertSeparators { before: GiftListUIModel.Gift?, after: GiftListUIModel.Gift? ->
                if (before == null && after == null) {
                    // List is empty
                    null
                } else if (after == null) {
                    // End of list
                    null
                } else if (before == null) {
                    // Beginning of list, add the header
                    GiftListUIModel.CategoryHeader(after.gift.categoryName ?: "Unknown Category")
                } else if (before.gift.categoryName != after.gift.categoryName) {
                    // Different category, add the header
                    GiftListUIModel.CategoryHeader(after.gift.categoryName ?: "Unknown Category")
                } else {
                    // Same category, no separator
                    null
                }
            }
    }

    val giftItemDetails = LiveEvent<Pair<GiftSendResponse, GiftItem>>()

    val user = accountRepo.user

    private val _otherUser = MutableLiveData<OtherUser?>(null)
    val otherUser: LiveData<OtherUser?> = _otherUser

   val isVisitor: Boolean
       get() {
        return otherUser.value?.citizenship== UserCitizenship.VISITOR || user.citizenship== UserCitizenship.VISITOR
       }

    private val nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    data class GiftParams(
        val receiver: Int,
        val giftContext: GiftContext,
        val contextId: String?=null,

        // For Challenge
        val challengeId: String?=null,
        val challengeEndTimeStampUTC: Long? = null,

        // For huddles
        val managerId:Int?=null,

        // For ??
        val birthday: Boolean = false,
        val userCongrats: Boolean = false
    )

    fun setParams(params: GiftParams) {
        Log.d("GLVM","setParams: $params")
        fetchReceiverDetails(params.receiver)
        _giftParams.postValue(params.copy(
            contextId = params.contextId.takeIf { !it.isNullOrBlank() },
            challengeId = params.challengeId.takeIf { !it.isNullOrBlank() },
            challengeEndTimeStampUTC = params.challengeEndTimeStampUTC?.takeIf { it>0 },
            managerId = params.managerId.takeIf { it != -1 }
        ))
    }

    fun setReceiver(receiverId: Int, context: GiftContext, contextId: String? = null) {
        setParams(GiftParams(receiverId, context, contextId))
    }

    val nameOrNickname: MediatorLiveData<String?> by lazy {
        val med = MediatorLiveData<String?>()
        fun update() {
            _otherUser.value?.also {
                med.postValue(nickNames.value.nickNameOrName(it))
            } ?: {
                med.postValue(null)
            }
        }
        med.addSource(nickNames) { update() }
        med.addSource(_otherUser) { update() }
        med
    }
    private fun fetchReceiverDetails(receiverId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = profileRepo.getPublicUserDetails(receiverId)) {
                is ResultOf.Success -> {
                    _otherUser.postValue(result.value)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    private val _isGiftLoading = MutableLiveData<Boolean>(false)
    val isGiftLoading: LiveData<Boolean> = _isGiftLoading

    fun sendGift(item: GiftItem, preview: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            val params = _giftParams.value?: return@launch
            _isGiftLoading.postValue(true)
            when (val result = giftRepository.sendGift(item.id,params,preview)) {
                is ResultOf.Success -> {
                    if (preview) {
                        giftItemDetails.postValue(Pair(result.value,item))
                    } else {
                        val receiver = _otherUser.value ?: return@launch
                        val response = result.value
                        onGiftSent.postValue(
                            SentGiftPayload.from(
                                item,
                                user,
                                receiver,
                                coinsReceived = response.coinsReceived,
                                managerId = response.managerId,
                                managerReceivedCoins = response.managerReceivedCoins
                            )
                        )
                        profileRepo.refreshAccountDetails()
                    }
                }
                is ResultOf.APIError -> {
                    if (result.code == 423) {
                        isGiftReceiverBanned.postValue(true)
                    }
                    else if (result.code == 402) {
                        insufficientBalance.postValue(if (item.isCoin) CurrencyType.COINS else if (item.isFlix) CurrencyType.FLIX else null)
                    } else {
                        errorMessage.postValue(result.error.message)
                    }
                }
                is ResultOf.Error -> {
                    Log.d("qqqq",""+errorMessage)
                }
            }
            _isGiftLoading.postValue(false)
        }
    }
    fun setGiftLoading(loading:Boolean){
        _isGiftLoading.postValue(loading)
    }

}
