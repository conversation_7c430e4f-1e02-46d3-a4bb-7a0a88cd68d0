package com.app.messej.ui.home.publictab.podiums.live

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView

class LiveChatRecyclerView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : RecyclerView(context, attrs, defStyleAttr) {

    override fun getBottomFadingEdgeStrength(): Float {
        return 0f
    }
}