package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.launch

class BuySellHuddleViewModel(application: Application) : AndroidViewModel(application) {
    val huddleRepo = HuddlesRepository(application)
    val businessRepo = BusinessRepository(application)

    private val _huddleId = MutableLiveData<Int>()
    val huddleId: LiveData<Int> = _huddleId

    private val _sellHuddleLoading = MutableLiveData<Boolean>(false)
    val sellHuddleLoading: LiveData<Boolean> = _sellHuddleLoading

    val showSuccess = LiveEvent<String>()
    val errorMessage = LiveEvent<String>()
    val errorCode = LiveEvent<Int>()

    companion object {
        const val SELL_HUDDLE_ERROR_CODE = 422
    }

    fun setHuddleId(it: Int) {
        _huddleId.postValue(it)
    }

    val selectedSellHuddle = _huddleId.switchMap {
        huddleRepo.getSelectedSellHuddle(it)
    }

    fun buyHuddle() {
        viewModelScope.launch {
            _sellHuddleLoading.postValue(true)
            when (val result: ResultOf<APIResponse<String>> = huddleRepo.buyHuddle(_huddleId.value!!.toInt())) {
                is ResultOf.APIError -> {
//                    errorMessage.postValue(result.error.message)
                    if(result.code==SELL_HUDDLE_ERROR_CODE)
                        _sellHuddleLoading.postValue(false)
                        errorCode.postValue(result.code)
                }

                is ResultOf.Error -> {
                    _sellHuddleLoading.postValue(false)
                }

                is ResultOf.Success -> {
                    _sellHuddleLoading.postValue(false)
                    showSuccess.postValue(result.value.message)

                }
            }
        }
    }




}