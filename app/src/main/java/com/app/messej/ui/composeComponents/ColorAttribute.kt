package com.app.messej.ui.composeComponents

import android.util.TypedValue
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource

/**
Used this for getting color from the attribute
 */
@Composable
@ReadOnlyComposable
fun colorAttributeResource(id: Int) : Color {
    val context = LocalContext.current
    val typedValue = TypedValue()
    context.theme.resolveAttribute(id, typedValue, true)
    return colorResource(typedValue.resourceId)
}