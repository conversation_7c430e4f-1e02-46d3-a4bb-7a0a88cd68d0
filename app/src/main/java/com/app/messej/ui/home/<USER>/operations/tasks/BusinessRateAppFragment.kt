package com.app.messej.ui.home.businesstab.operations.tasks

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.NavigationRateArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentBusinessRateAppBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.yalantis.ucrop.UCrop

class BusinessRateAppFragment : BottomSheetDialogFragment() {


    lateinit var binding: FragmentBusinessRateAppBinding
    val viewModel: BusinessRateViewModel by navGraphViewModels(R.id.navigation_rate)
    private val commonViewModel: BusinessOperationsTaskViewModel by activityViewModels()
    private val args: NavigationRateArgs by navArgs()
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_rate_app, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {

        viewModel.isUpdateMode.observe(viewLifecycleOwner) {
            return@observe
        }

        viewModel.onTriggerCrop.observe(viewLifecycleOwner) {
            imageCropResult.launch(it.getIntent(requireContext()))
        }

        viewModel.uri.observe(viewLifecycleOwner) {
            it?.let {
                findNavController().navigateSafe(BusinessRateAppFragmentDirections.actionRateBusinessFragmentToRateImageUploadFragment())
            }
        }


    }

    private fun setup() {
        viewModel.setUpdateMode(args.isUpdateMode)
        showAppReviewAlertDialog()
    }


    private val selectImageFromGalleryResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                viewModel.addImage(uri)
            }
        }
    }

    private val imageCropResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let {
                val resultUri = UCrop.getOutput(it) ?: return@let
                viewModel.setCropUri(resultUri)
            }
        } else {
            viewModel.onCropCancelled()
        }
    }

    private fun showAppReviewAlertDialog() {
        binding.actionAttachPhoto.setOnClickListener { openGalleryForImage() }
        binding.btnDialogClose.setOnClickListener {
            findNavController().popBackStack()
        }
        val packageName = requireContext().packageName
        binding.actionEditRating.setOnClickListener {
            if (viewModel.isUpdateMode.value == false) {
                try {
                    startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName")))
                } catch (e: ActivityNotFoundException) {
                    startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$packageName")))
                }
            } else {
                openGalleryForImage()
            }
        }
    }

    private fun openGalleryForImage() {
        val galleryIntent = Intent(Intent.ACTION_GET_CONTENT)
        galleryIntent.type = "image/*"

        selectImageFromGalleryResult.launch(galleryIntent)
    }

}