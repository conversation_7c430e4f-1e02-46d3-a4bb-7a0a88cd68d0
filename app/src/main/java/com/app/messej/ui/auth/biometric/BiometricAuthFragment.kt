package com.app.messej.ui.auth.biometric

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.databinding.FragmentBiometricAuthBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BiometricAuthFragment : Fragment() {

    private var biometricPrompt: BiometricPrompt? = null
    private lateinit var binding: FragmentBiometricAuthBinding

    val viewModel: BiometricViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_biometric_auth, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
        setup()
    }

    private fun setup() {
        initBiometricPrompt()
        binding.bioMetricButton.setOnClickListener {
            authenticate()
        }
    }

    private fun observe() {

    }

    private fun initBiometricPrompt() {
        biometricPrompt = BiometricPrompt(
            this,
            object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    super.onAuthenticationError(errorCode, errString)
                    val cancelled = errorCode in arrayListOf(
                        BiometricPrompt.ERROR_CANCELED,
                        BiometricPrompt.ERROR_USER_CANCELED,
                        BiometricPrompt.ERROR_NEGATIVE_BUTTON
                    )
                    if (cancelled) {
                        // handle authentication cancelled
                        binding.bioMetricSubTitle.text = resources.getString(R.string.biometric_error_message)
                    } else {
                        // handle authentication failed
                        binding.bioMetricSubTitle.text = resources.getString(R.string.biometric_error_message)
                    }
                }
                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    super.onAuthenticationSucceeded(result)
                    findNavController().navigateSafe(BiometricAuthFragmentDirections.actionGlobalHomeFragment())

                }
                override fun onAuthenticationFailed() {
                    super.onAuthenticationFailed()
                    // this method will be invoked on unsuccessful intermediate attempts (e.g. unrecognized fingerprint)
                    binding.bioMetricSubTitle.text = resources.getString(R.string.biometric_error_message)
                }
            }
        )
    }

    private fun authenticate() {
        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle(resources.getString(R.string.biometric_unlock_title))
            .setSubtitle("")
            .setDescription(resources.getString(R.string.biometric_unlock_sub_title))
            .setNegativeButtonText(resources.getText(R.string.common_cancel))
            .setAllowedAuthenticators(BiometricManager.Authenticators.BIOMETRIC_STRONG)
            .build()
        biometricPrompt?.authenticate(promptInfo)
    }


}