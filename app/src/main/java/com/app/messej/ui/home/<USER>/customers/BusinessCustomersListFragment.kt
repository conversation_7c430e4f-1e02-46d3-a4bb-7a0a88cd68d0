package com.app.messej.ui.home.businesstab.customers

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.fragment.app.activityViewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.R
import com.app.messej.databinding.FragmentBusinessCustomersListBinding
import com.app.messej.ui.home.businesstab.BusinessCustomersFragment
import com.app.messej.ui.home.businesstab.BusinessCustomersViewModel
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.google.android.material.tabs.TabLayoutMediator
import jp.wasabeef.transformers.glide.BlurTransformation

class BusinessCustomersListFragment : Fragment() {

    private val viewModel: BusinessCustomersViewModel by activityViewModels()
    private lateinit var binding: FragmentBusinessCustomersListBinding

    private lateinit var mBusinessCustomerPagerAdapter: FragmentStateAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.getBoolean("isCancelable", false)?.let { viewModel.setListExpanded(it) }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_customers_list, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        actionCancel()
        setUI()
        actionViewAll()
    }

    private fun setup() {
        mBusinessCustomerPagerAdapter = object : FragmentStateAdapter(this) {

            override fun getItemCount(): Int = 1

            override fun createFragment(position: Int): Fragment {
                return when (position) {
                    0 -> BusinessCustomersDearsFragment()
                     // Hide the Fans Customers
                   /* 1 -> BusinessCustomersFansFragment()*/
                    else -> throw java.lang.IllegalArgumentException("There should only be 2 tabs")
                }
            }
        }

        binding.customerPager.apply {
            adapter = mBusinessCustomerPagerAdapter
            isUserInputEnabled = false
            
        }

        TabLayoutMediator(binding.customerTab, binding.customerPager) { tab, position ->
            when (position) {
                0 -> {
                    tab.text = resources.getString(R.string.business_customers)
                }
                // Hide the Fans Customers text
              /*  1 -> {
                    tab.text = resources.getString(R.string.title_potential_customers)
                }*/
            }
        }.attach()


    }

    private fun setUI() {
        if (arguments?.getBoolean("isCancelable", false) == true) {
            setBlurredBackground()
        }
    }

    private fun setBlurredBackground() {
        Glide.with(requireContext()).asDrawable().load(ResourcesCompat.getDrawable(resources, R.drawable.bg_business, null))
            .apply(RequestOptions.bitmapTransform(BlurTransformation(requireContext(), 25, 10, true))).into(object : CustomTarget<Drawable>() {
                override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                    binding.layoutBusiness.background = resource
                }

                override fun onLoadCleared(placeholder: Drawable?) {}
            })
    }

    private fun actionCancel() {
        binding.actionCancel.setOnClickListener {
            viewModel.setListExpanded(false)
            replaceFragment()
        }
    }

    private fun actionViewAll() {
        binding.actionViewAll.setOnClickListener {
            if(viewModel.dearsCount.value!!>0){
                viewModel.customerListViewAll()
                viewModel.setListExpanded(true)
            }

        }
    }

    private fun replaceFragment() {
        val fm: FragmentManager = parentFragmentManager
        val ft: FragmentTransaction = fm.beginTransaction()
        ft.replace(R.id.customer_list_layout, BusinessCustomersFragment.newInstance())
        ft.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
        ft.commit()
    }

    companion object {

        @JvmStatic
        fun newInstance(isExpanded: Boolean) = BusinessCustomersListFragment().apply {
            arguments = Bundle().apply {
                putBoolean("isCancelable", isExpanded)
            }
        }
    }
}