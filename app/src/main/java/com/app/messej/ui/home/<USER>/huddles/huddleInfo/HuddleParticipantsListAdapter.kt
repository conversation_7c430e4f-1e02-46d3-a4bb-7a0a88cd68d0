package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.isVisible
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.databinding.ItemHuddleParticipantsBinding


class HuddleParticipantsListAdapter(private val huddleType: HuddleType, private val mListener: ItemListener) : PagingDataAdapter<Participant, HuddleParticipantsListAdapter.HuddleParticipantsListViewHolder>(
    StarsDiff
) {

    interface ItemListener {
        fun onItemClick(item: Participant, position: Int, currentUserId: Int?)
        fun onMenuItemClick(item: Participant, position: Int, menuItem: MenuItem)
    }

    var isCurrentUserManager: Boolean = false
    var isCurrentUserElevated:Boolean = false
    var currentUserId: Int? = null
    var isTribe: Boolean = false
    var isCurrentUserPremium: Boolean = false
    var isCurrentUserEmpoweredToBlock: Boolean = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        HuddleParticipantsListViewHolder(
            ItemHuddleParticipantsBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: HuddleParticipantsListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }

    inner class HuddleParticipantsListViewHolder(private val binding: ItemHuddleParticipantsBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: Participant, position: Int) = with(binding) {
            Log.d("MembersTag", "bind: ${item.name}")
            user = item
            binding.userNote.text = if (item.status == Participant.ParticipantStatus.ADMIN_BLOCKED) {
                binding.root.context.getString(R.string.huddle_blocked)
            }
            else if (item.adminStatus == Participant.AdminStatus.INVITED) {
                binding.root.context.getString(R.string.common_invited)
            } else null

            participantsActionButton.isVisible = (isCurrentUserElevated || isCurrentUserEmpoweredToBlock) && currentUserId != item.id && item.isManager!=true

            userNote.isVisible = isCurrentUserElevated || isCurrentUserEmpoweredToBlock

            currentUser = currentUserId

            val participantIsPresident = item.userCitizenship == UserCitizenship.PRESIDENT

            participantsActionButton.setOnClickListener {

                val popup = PopupMenu(it.context, it)
                popup.inflate(R.menu.menu_huddle_participants_list)
                popup.menu.apply {
                    findItem(R.id.appoint_as_admin).isVisible = false
                    findItem(R.id.cancel_invitation).isVisible = false
                    findItem(R.id.dismiss_admin).isVisible = false

                    if (!isTribe && isCurrentUserElevated) {
                        if (item.isAdmin == true) {
                            findItem(R.id.dismiss_admin).isVisible = true
                        } else if (item.adminStatus == Participant.AdminStatus.INVITED) {
                            findItem(R.id.cancel_invitation).isVisible = true
                        } else if (item.membership == UserType.PREMIUM && item.status != Participant.ParticipantStatus.ADMIN_BLOCKED) {
                            findItem(R.id.appoint_as_admin).isVisible = true
                        }
                    }

                    val isPublicHuddle = huddleType == HuddleType.PUBLIC
                    findItem(R.id.block_participant).isVisible = false
                    findItem(R.id.unblock_participant).isVisible = false

                    if ((isCurrentUserEmpoweredToBlock && isPublicHuddle) || isCurrentUserElevated) {
                        if(participantIsPresident){
                            findItem(R.id.block_participant).isVisible = false
                            findItem(R.id.unblock_participant).isVisible = false
                        }else {
                            if (item.status == Participant.ParticipantStatus.ADMIN_BLOCKED) {
                                findItem(R.id.unblock_participant).isVisible = !item.citizenship.isGolden
                            } else {
                                findItem(R.id.block_participant).isVisible =  !item.citizenship.isGolden
                            }
                        }
                    }
                    binding.root.context.apply {
                        findItem(R.id.block_participant).title = getString(if (isPublicHuddle) R.string.huddle_participants_menu_block_text else R.string.group_participants_menu_block_text)
                        findItem(R.id.unblock_participant).title = getString(if (isPublicHuddle) R.string.huddle_participants_menu_unblock_text else R.string.group_participants_menu_unblock_text)
                    }

                    findItem(R.id.ban_commenting).isVisible =
                        isPublicHuddle && item.canComment == true && item.status != Participant.ParticipantStatus.ADMIN_BLOCKED && (isCurrentUserPremium && isCurrentUserElevated) && !item.citizenship.isGolden
                    findItem(R.id.unban_commenting).isVisible =
                        isPublicHuddle && item.canComment == false && item.status != Participant.ParticipantStatus.ADMIN_BLOCKED && (isCurrentUserPremium && isCurrentUserElevated) && !item.citizenship.isGolden

                    findItem(R.id.ban_posting).isVisible =
                        isPublicHuddle && item.canPost == true && item.status != Participant.ParticipantStatus.ADMIN_BLOCKED && (isCurrentUserPremium && isCurrentUserElevated) && !item.citizenship.isGolden
                    findItem(R.id.unban_posting).isVisible =
                        isPublicHuddle && item.canPost == false && item.status != Participant.ParticipantStatus.ADMIN_BLOCKED && (isCurrentUserPremium && isCurrentUserElevated) && !item.citizenship.isGolden

                    findItem(R.id.ban_replying).isVisible =
                        isPublicHuddle && item.canReply == true && item.status != Participant.ParticipantStatus.ADMIN_BLOCKED && (isCurrentUserPremium && isCurrentUserElevated) && !item.citizenship.isGolden
                    findItem(R.id.unban_replying).isVisible =
                        isPublicHuddle && item.canReply == false && item.status != Participant.ParticipantStatus.ADMIN_BLOCKED && (isCurrentUserPremium && isCurrentUserElevated) && !item.citizenship.isGolden
                    findItem(R.id.send_private_message).isVisible = isCurrentUserPremium == true
                    findItem(R.id.remove_participant).isVisible = isCurrentUserElevated
                    binding.root.context.apply {
                        findItem(R.id.remove_participant).title = getString(if (isPublicHuddle) R.string.huddle_participants_menu_remove_text else R.string.group_participants_menu_remove_text)
                    }
                }
                popup.setOnMenuItemClickListener { menuItem ->
                    mListener.onMenuItemClick(item, position, menuItem)
                    true
                }
                popup.show()
            }
            binding.root.setOnClickListener {
                mListener.onItemClick(item, position, currentUserId)
            }
        }
    }

    object StarsDiff : DiffUtil.ItemCallback<Participant>() {
        override fun areItemsTheSame(oldItem: Participant, newItem: Participant) =
            oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: Participant, newItem: Participant) =
            oldItem == newItem
    }
}