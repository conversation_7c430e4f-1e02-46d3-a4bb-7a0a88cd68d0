package com.app.messej.ui.auth.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterSuperstarFilterBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.google.android.material.tabs.TabLayoutMediator

class RegisterSuperstarFilterFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentRegisterSuperstarFilterBinding

    private val viewModel: RegisterSuperstarViewModel by navGraphViewModels(R.id.navigation_register_superstar)

    private lateinit var mFilterPagerAdapter: FragmentStateAdapter

//    private var mAdapter: RegisterSuperstarListQuickAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_superstar_filter, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // for action menu
        addAsMenuHost()
        observe()
        setup()
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        // Add menu items here
        menuInflater.inflate(R.menu.menu_register_select_superstar_filter, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        // Handle the menu selection
        return when (menuItem.itemId) {
            R.id.superstarFilterClear -> {
                viewModel.clearAllFilters()
                true
            }
            else -> false
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
        activity?.actionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
            setHomeButtonEnabled(false)
        }
        viewModel.setupTempFilters()
    }

    private var tabLayoutMediator: TabLayoutMediator? = null

    private fun setup() {

        mFilterPagerAdapter = object : FragmentStateAdapter(this) {

            override fun getItemCount(): Int = 2

            override fun createFragment(position: Int): Fragment {
                // Return a NEW fragment instance in createFragment(int)
                val fragment = when (position) {
                    0 -> RegisterSuperstarFilterCountryFragment()
                    1 -> RegisterSuperstarFilterGenderFragment()
                    else -> throw java.lang.IllegalArgumentException("There should only be 2 tabs")
                }
                return fragment
            }
        }

        binding.filterPager.apply {
            adapter = mFilterPagerAdapter
            isUserInputEnabled = false
            
        }

        val tabM = TabLayoutMediator(binding.filterTab, binding.filterPager) { tab, position ->
            val countrySel = viewModel.temporaryCountrySelection.value.orEmpty().size
    //        val genderSel = viewModel.temporaryGenderSelection.value.orEmpty().size
            updateApplyButton()
            tab.text = when (position) {
                0 -> if(countrySel>0) resources.getString(R.string.register_upgrade_superstar_filter_country,countrySel) else resources.getString(R.string.common_country)
                1 -> resources.getString(R.string.common_gender)
                else -> ""
            }
        }
        tabM.attach()
        tabLayoutMediator = tabM

        binding.filterPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                // TODO()
            }
        })

        binding.filterApplyButton.setOnClickListener {
            viewModel.applyFilters()
            findNavController().popBackStack()
        }

    }

    private fun observe() {

        viewModel.temporaryCountrySelection.observe(viewLifecycleOwner) {
            tabLayoutMediator?.let {
                it.detach()
                it.attach()
            }
        }
        viewModel.temporaryGenderSelection.observe(viewLifecycleOwner) {
            tabLayoutMediator?.let {
                it.detach()
                it.attach()
            }
        }
    }

    private fun updateApplyButton() {
        binding.filterApplyButton.isEnabled = (viewModel.temporaryGenderSelection.value?.isEmpty() == false) || (viewModel.temporaryCountrySelection.value?.isEmpty() == false)
    }

}