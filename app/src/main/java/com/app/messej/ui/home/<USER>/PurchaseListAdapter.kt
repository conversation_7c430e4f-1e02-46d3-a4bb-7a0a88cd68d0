package com.app.messej.ui.home.gift

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.api.gift.PointsPurchase
import com.app.messej.databinding.ItemGiftPurchaseHistoryLayoutBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class PurchaseListAdapter(items: MutableList<PointsPurchase>, private val listener: ItemClickListener) :
    BaseQuickAdapter<PointsPurchase, BaseDataBindingHolder<ItemGiftPurchaseHistoryLayoutBinding>>(R.layout.item_gift_purchase_history_layout, items) {
    //PurchaseListAdapter
    object GiftDiff : DiffUtil.ItemCallback<PointsPurchase>() {
        override fun areItemsTheSame(oldItem: PointsPurchase, newItem: PointsPurchase): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: PointsPurchase, newItem: PointsPurchase): Boolean {
            return oldItem.id == newItem.id
        }

    }

    override fun convert(holder: BaseDataBindingHolder<ItemGiftPurchaseHistoryLayoutBinding>, item: PointsPurchase) {
        holder.dataBinding?.apply {
            giftPurchaseHistory = item
            hideDate = true
            hideButton = false

            buttonBuyCoins.setOnClickListener {
                listener.onClick(item, holder.bindingAdapterPosition)
            }

        }
    }
    interface  ItemClickListener{
        fun onClick(packages: PointsPurchase, position: Int)
    }

}