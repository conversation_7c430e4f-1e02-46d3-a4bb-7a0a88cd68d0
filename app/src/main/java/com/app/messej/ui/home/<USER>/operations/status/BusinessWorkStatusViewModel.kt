package com.app.messej.ui.home.businesstab.operations.status

import android.app.Application
import android.net.Uri
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.R
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.business.PayoutEligibility
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.enums.BusinessWorkStatusType
import com.app.messej.data.model.status.AppRating
import com.app.messej.data.model.status.BusinessActivityStatus
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.EnumUtil.except
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.io.File


class BusinessWorkStatusViewModel(app: Application): AndroidViewModel(app) {

    private val repository = BusinessRepository(app)
    private val chatRepo = ChatRepository(getApplication())
    private val profRepo= ProfileRepository(getApplication())
    private val accountRepo = AccountRepository(getApplication())

//    val businessWorkStatusItems = BusinessWorkStatusType.entries.toList()

    val businessWorkStatusItems = accountRepo.userFlow.map {
        val list = if (it?.citizenship?.isFreeType == true) {
            BusinessWorkStatusType.entries.except(BusinessWorkStatusType.ETribe).toList()
        } else BusinessWorkStatusType.entries.toList()
        return@map list
    }

    private val _activityStatus = MutableLiveData<BusinessActivityStatus?>(null)
    val activityStatus: LiveData<BusinessActivityStatus?> = _activityStatus

    val onStatusError = LiveEvent<String>()

    private val _user = MutableLiveData<CurrentUser>()
    val user: LiveData<CurrentUser> = _user

    private val _apiResponse = MutableLiveData<String?>()
    val apiResponse: LiveData<String?> = _apiResponse

    var payoutEligibility = MutableLiveData<PayoutEligibility?>(null)

    private val _eligibilityCheckApiFail = MutableLiveData<String?>(null)
    private  val eligibilityCheckApiFail: LiveData<String?> = _eligibilityCheckApiFail

    private val _isEditVisible = MutableLiveData(false)
    val isEditVisible: LiveData<Boolean?> = _isEditVisible

    var screenshotUploading = LiveEvent<Boolean>()
    var statusLoading= LiveEvent<Boolean>()

    var isScreenShotUploaded = LiveEvent<Boolean>()

    private val _businessWorkStatusViewState = MutableLiveData<MultiStateView.ViewState>(MultiStateView.ViewState.LOADING)
    val businessWorkStatusViewState: LiveData<MultiStateView.ViewState> = _businessWorkStatusViewState

    private var imageCaptureTempFile: File? = null
    fun setOperations() {
        statusLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = repository.getFlashAtActivityDetails()) {
                is ResultOf.APIError -> {
                    statusLoading.postValue(false)
                    onStatusError.postValue(result.error.message)
                    _businessWorkStatusViewState.postValue(MultiStateView.ViewState.ERROR)
                }
                is ResultOf.Error -> {
                    statusLoading.postValue(false)
                    onStatusError.postValue(result.exception.localizedMessage)
                    _businessWorkStatusViewState.postValue(MultiStateView.ViewState.ERROR)
                }
                is ResultOf.Success -> {
                    _activityStatus.postValue(result.value)
                    statusLoading.postValue(false)
                    _businessWorkStatusViewState.postValue(MultiStateView.ViewState.CONTENT)
//                    getUserActivityDetails(result.value)
                }
            }
        }
    }

    val haveHundredFlax=_activityStatus.map {
        it?:return@map null
        it.flaxBalance?.isSatisfied
    }.distinctUntilChanged()

    val minimumRedeemableFlaxRequired=_activityStatus.map {
        it?:return@map null
        it.flaxBalance?.requiredFlaxBalance
    }.distinctUntilChanged()

    val inReview=_activityStatus.map {
        it?:return@map null
        it.appRating?.status==AppRating.RatingStatus.NEW
    }

    val userCitizenship = activityStatus.map {
        it?:return@map null
        it.citizenship
    }.distinctUntilChanged()

    val areYouEligible =_activityStatus.map {
        it?:return@map null
        it.eligibility == true /*&& it.tasksCompleted==true*/ /*eligibility always false here*/
    }.distinctUntilChanged()

    private fun getUserActivityDetails(operation: BusinessOperation) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = repository.getBusinessActivityDetails(operation)) {
                is ResultOf.APIError -> {
                    onStatusError.postValue(result.error.message)
                    statusLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    statusLoading.postValue(false)
                    onStatusError.postValue(result.exception.localizedMessage)

                }
                is ResultOf.Success -> {
                    _activityStatus.postValue(result.value)
                    statusLoading.postValue(false)
                }
            }
        }
    }


    fun addImage(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            imageCaptureTempFile = file
            cropAttachedMedia()

        }
    }


    val onTriggerCrop = LiveEvent<UCrop>()

    private fun cropAttachedMedia() {
        viewModelScope.launch(Dispatchers.IO) {
            val media = imageCaptureTempFile?: return@launch
            val src = chatRepo.getUriForFile(media)
            val dest = chatRepo.getUriForFile(chatRepo.createTempImageFile())

            val options = UCrop.Options().apply {
                setCircleDimmedLayer(false)
                val color = ContextCompat.getColor(getApplication(), R.color.colorPrimary)
                setToolbarColor(color)
                setStatusBarColor(color)
                setToolbarTitle(getApplication<Application>().applicationContext.getString(R.string.common_crop))
            }
            val crop = UCrop.of(src, dest)
                .withAspectRatio(1f,1f)
                .withOptions(options)
            onTriggerCrop.postValue(crop)
        }
    }

    fun onCropCancelled() {
        imageCaptureTempFile = null
    }

    fun getImageUriForCapture(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            uploadAppReviewImage(file)
        }
    }

    private fun uploadAppReviewImage(image: File) {
        screenshotUploading.postValue(true)
        viewModelScope.launch(Dispatchers.IO){
            val compressed = profRepo.compressImage(image)
            when(repository.uploadAppReviewImage(
                file = compressed
            )){
                is ResultOf.APIError -> {
                    isScreenShotUploaded.postValue(false)
                }
                is ResultOf.Error -> {
                    isScreenShotUploaded.postValue(false)
                }
                is ResultOf.Success -> {

                    _isEditVisible.postValue(false)
                    isScreenShotUploaded.postValue(true)
                }
            }
            screenshotUploading.postValue(false)
        }
    }

}