package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MutableLiveData
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumLiveUsersListBottomSheetBinding
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.ViewUtils

abstract class PodiumLiveUsersListBaseFragment : ExpandableListBottomSheetDialogFragment() {

    protected lateinit var binding: FragmentPodiumLiveUsersListBottomSheetBinding

    protected var mAdapter: PodiumLiveUsersListAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_users_list_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
    }

    fun setup() {
        initAdapter()

        binding.actionRefreshList.setOnClickListener {
            it.animate().rotation(it.rotation + 360f).setInterpolator(AccelerateDecelerateInterpolator()) //to animate the refresh button for UX
            mAdapter?.refresh()
            refreshData()
        }

        binding.actionClose.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    protected abstract fun refreshData()

    abstract val actionListener: PodiumLiveUsersListAdapter.PodiumActionListener

    abstract val loadingStateLiveData: MutableLiveData<Boolean>

    private fun initAdapter() {
        mAdapter = PodiumLiveUsersListAdapter(actionListener)

        val layoutMan = LinearLayoutManager(context)

        binding.liveUsers.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                loadingStateLiveData.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }
}