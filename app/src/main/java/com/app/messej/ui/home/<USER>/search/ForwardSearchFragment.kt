package com.app.messej.ui.home.forward.search

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.forward.Forward
import com.app.messej.data.model.api.forward.ForwardRequest
import com.app.messej.data.model.enums.ForwardType
import com.app.messej.databinding.FragmentForwardSearchBinding
import com.app.messej.databinding.LayoutEmptyPollsBinding
import com.app.messej.ui.home.forward.ForwardCommonViewModel
import com.app.messej.ui.home.forward.ForwardHomeFragmentDirections
import com.app.messej.ui.home.forward.adapter.ForwardAdapter
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.FragmentShareExtensions.setExternalShareRequest
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class ForwardSearchFragment : Fragment() {
    private lateinit var binding: FragmentForwardSearchBinding
    private val forwardSearchViewModel: ForwardSearchViewModel by viewModels()
    private val commonForwardViewModel: ForwardCommonViewModel by navGraphViewModels(R.id.navigation_forward)
    private var mAdapter: ForwardAdapter? = null
    val args: ForwardSearchFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_forward_search, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = forwardSearchViewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
        forwardSearchViewModel.selectedPosition.observe(viewLifecycleOwner) {
            mAdapter?.notifyItemChanged(it)
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                setNavigationIcon(R.drawable.ic_close)
                setNavigationIconTint(R.attr.toolbarTextColor)
            }
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        val layoutMan = LinearLayoutManager(context)

        binding.forwardList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }
    }

    private fun setup() {
        forwardSearchViewModel.setTab(args.tab)
        commonForwardViewModel.enableSend.observe(viewLifecycleOwner) {
            forwardSearchViewModel.setOptionSelected(it)
        }
        initAdapter()
        binding.fabForward.setOnClickListener {
            commonForwardViewModel.shareMessage()
        }
        mAdapter = ForwardAdapter(object : ForwardAdapter.ActionListener {
            override fun onItemClick(item: Forward, position: Int) {
                onAdapterClick(item, position)
            }
        })

        binding.customActionBar.apply {
            commonForwardViewModel.clearList()
            keyword = forwardSearchViewModel.searchKeyword
            showKeyboard(searchBox)
        }

        val layoutMan = LinearLayoutManager(context)
        mAdapter?.apply {
            addLoadStateListener { loadState ->
                Log.d("MYLoadState", loadState.toString())

                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
                forwardSearchViewModel.setLoading(loadState.append is LoadState.Loading || !loadState.prepend.endOfPaginationReached || !loadState.prepend.endOfPaginationReached)
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
        binding.forwardList.layoutManager = layoutMan
        binding.forwardList.adapter = mAdapter

    }

    fun observe() {
        forwardSearchViewModel.forwardSearchList.observe(viewLifecycleOwner) {
            mAdapter?.submitData(lifecycle, it)
        }

        commonForwardViewModel.navigateToNextFragment.observe(viewLifecycleOwner) { shareDetails ->

            val action = when (shareDetails) {
                is ForwardCommonViewModel.ShareRequest.PrivateChatRequest -> {
                    ForwardHomeFragmentDirections.actionGlobalNavigationChatPrivate(
                        roomId = shareDetails.roomId,
                        receiver = shareDetails.receiverId,
                    )
                }

                is ForwardCommonViewModel.ShareRequest.GroupChatRequest -> {
                    when (shareDetails.forwardType) {
                        ForwardType.GROUPS -> {
                            ForwardHomeFragmentDirections.actionGlobalNavigationChatGroup(
                                huddleId = shareDetails.roomId.toInt(),
                            )
                        }

                        ForwardType.HUDDLES -> {
                            ForwardHomeFragmentDirections.actionGlobalNavChatHuddle(
                                huddleId = shareDetails.roomId.toInt(),
                            )
                        }

                        else -> throw IllegalArgumentException("Invalid forward type for GroupChatRequest")
                    }
                }
            }

            val mediaTypeArg = args.mediaType
            if (mediaTypeArg != null) {
                setExternalShareRequest(message = args.textMessage, mediaType = args.mediaType, fileUri = args.fileUri)
            }
            findNavController().popBackStack(R.id.navigation_forward, true)
            (activity as MainActivity).navController.navigateSafe(action)
        }

        commonForwardViewModel.onMessageForwarded.observe(viewLifecycleOwner) {
            showToast(R.string.title_success_fully_forwarded, Toast.LENGTH_SHORT)

        }
        commonForwardViewModel.onError.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()

        }

        commonForwardViewModel.onLimitExceeds.observe(viewLifecycleOwner) {
            it?.let {
                if (it) {
                    Toast.makeText(requireContext(), getString(R.string.title_forward_limit_exceeds), Toast.LENGTH_SHORT).show()
                }
            }
        }

        forwardSearchViewModel.tab.observe(viewLifecycleOwner) {
            val emptyView: View? = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)
            val emptyViewBinding = LayoutEmptyPollsBinding.bind(emptyView!!)
            when (it) {
                ForwardType.MESSAGES -> {
                    emptyViewBinding.pollsEmptyTitle.text = getString(R.string.title_forward_empty_chat)
                }

                ForwardType.GROUPS,
                ForwardType.HUDDLES,
                -> {
                    emptyViewBinding.pollsEmptyTitle.text = getString(R.string.title_forward_empty_huddle)
                }
            }
        }
    }

    private fun onAdapterClick(forward: Forward, position: Int) {
        if (commonForwardViewModel.forwardRequestList.size <= (commonForwardViewModel.messageLimit - 1)) {
            forward.isSelected = forward.isSelected == false
            if (forward.isSelected == true) {
                commonForwardViewModel.addForwardList(ForwardRequest(forward = forward, forwardSearchViewModel.tab.value))
            } else {
                commonForwardViewModel.removeForwardList(ForwardRequest(forward = forward, forwardSearchViewModel.tab.value))
            }
            mAdapter?.notifyItemChanged(position)
        } else {
            if (commonForwardViewModel.forwardRequestList.size == commonForwardViewModel.messageLimit) {
                if (forward.isSelected == true) {
                    forward.isSelected = forward.isSelected == false
                    commonForwardViewModel.removeForwardList(ForwardRequest(forward = forward, forwardSearchViewModel.tab.value))
                    mAdapter?.notifyItemChanged(position)
                } else {
                    commonForwardViewModel.setLimitExceeds()
                }
            } else {
                commonForwardViewModel.setLimitExceeds()
            }
        }
    }
}
