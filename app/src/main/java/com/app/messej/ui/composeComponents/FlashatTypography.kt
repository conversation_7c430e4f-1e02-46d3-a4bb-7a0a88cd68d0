package com.app.messej.ui.composeComponents

import androidx.compose.material.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.app.messej.R

private val Exo2 = FontFamily(Font(R.font.exo_2))
private val Nunito = FontFamily(Font(R.font.nunito))

private val FlashatTypography = Typography(
    h1 = TextStyle(
        fontFamily = Exo2,
        fontSize = 50.sp
    ),
    h2 = TextStyle(
        fontFamily = Exo2,
        fontSize = 40.sp,
        fontWeight = FontWeight.Bold
    ),
    h3 = TextStyle(
        fontFamily = Exo2,
        fontSize = 30.sp,
        fontWeight = FontWeight.Bold
    ),
    h4 = TextStyle(
        fontFamily = Nunito,
        fontSize = 24.sp,
        fontWeight = FontWeight.Bold
    ),
    h5 = TextStyle(
        fontFamily = Nunito,
        fontSize = 20.sp,
        fontWeight = FontWeight.Bold
    ),
    h6 = TextStyle(
        fontFamily = Nunito,
        fontSize = 16.sp,
        fontWeight = FontWeight.Bold
    ),
    subtitle1 = TextStyle(
        fontFamily = Nunito,
        fontSize = 16.sp,
        fontWeight = FontWeight.Bold
    ),
    subtitle2 = TextStyle(
        fontFamily = Nunito,
        fontSize = 14.sp,
        fontWeight = FontWeight.Bold
    ),
    body1 = TextStyle(
        fontFamily = Nunito,
        fontSize = 16.sp,
        fontWeight = FontWeight.Normal
    ),
    body2 = TextStyle(
        fontFamily = Nunito,
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal
    ),
    button = TextStyle(
        fontFamily = Nunito,
        fontSize = 14.sp,
        fontWeight = FontWeight.Bold
    ),
    caption = TextStyle(
        fontFamily = Nunito,
        fontSize = 13.sp,
        fontWeight = FontWeight.Normal
    ),
    overline = TextStyle(
        fontFamily = Nunito,
        fontSize = 12.sp,
        fontWeight = FontWeight.Normal
    )
)

object FlashatComposeTypography {
    val defaultType = FlashatTypography
    val captionBold = defaultType.caption.copy(fontWeight = FontWeight.Bold)
    val overLineBold = defaultType.overline.copy(fontWeight = FontWeight.Bold)
    val overLineSmaller = defaultType.overline.copy(fontSize = 10.sp)
    val overLineSmallerItalic = defaultType.overline.copy(fontStyle = FontStyle.Italic)
    val overLineSmallerBold = overLineSmaller.copy(fontWeight = FontWeight.Bold)
    val overLineTiny = defaultType.overline.copy(fontSize = 8.sp)
    val overLineTinyBold = defaultType.overline.copy(fontWeight = FontWeight.Bold)
    val h2Nunito = defaultType.h2.copy(fontFamily = Nunito)
    val bodyItalics = defaultType.body1.copy(fontStyle = FontStyle.Italic)
    val body2Italics = defaultType.body2.copy(fontStyle = FontStyle.Italic)
    val h3Italics = defaultType.h3.copy(fontStyle = FontStyle.Italic)
    val h4Italics = defaultType.h4.copy(fontStyle = FontStyle.Italic)
    val h5Italics = defaultType.h5.copy(fontStyle = FontStyle.Italic)
}
