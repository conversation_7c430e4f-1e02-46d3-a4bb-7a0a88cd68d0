package com.app.messej.ui.auth.profile

import android.content.res.ColorStateList
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.navigation.ui.AppBarConfiguration
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterSuperstarSelectBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.RecyclerViewLoadMore
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions.bitmapTransform
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.google.android.material.chip.Chip
import com.google.android.material.progressindicator.CircularProgressIndicator
import jp.wasabeef.transformers.glide.CropCircleTransformation

class RegisterSuperstarFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentRegisterSuperstarSelectBinding

    private val viewModel: RegisterSuperstarViewModel by navGraphViewModels(R.id.navigation_register_superstar)

    private var mAdapter: RegisterSuperstarListQuickAdapter? = null
    private var mLoadMore: RecyclerViewLoadMore? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_superstar_select, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // for action menu
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        // Add menu items here
        menuInflater.inflate(R.menu.menu_register_select_superstar, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        // Handle the menu selection
        return when (menuItem.itemId) {
            R.id.registerSuperstarFilterFragment -> {
                if(viewModel.setSuperstarLoading.value==true) return true
                findNavController().navigateSafe(RegisterSuperstarFragmentDirections.actionRegisterSuperstarFragmentToRegisterSuperstarFilterFragment())
                true
            }
            else -> false
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar, AppBarConfiguration(setOf(R.id.registerSuperstarFragment)))
        activity?.actionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
            setHomeButtonEnabled(false)
        }
    }

    private fun setup() {
        initAdapter()
        viewModel.triggerInitialSuggestions()

        binding.textInputSearch.editText?.apply {
//            setOnEditorActionListener { v, actionId, event ->
//                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
//                    viewModel.triggerResearch()
//                    return@setOnEditorActionListener true
//                }
//                return@setOnEditorActionListener false
//            }
        }

        binding.selectSuperstarNextButton.setOnClickListener {
            val user = viewModel.selectedSuperstar.value?: return@setOnClickListener
            val action = RegisterSuperstarFragmentDirections.actionRegisterSuperstarFragmentToRegisterSuperstarConfirmFragment(user.name,user.thumbnail,user.premiumUser)
            findNavController().navigateSafe(action)
        }

        setFragmentResultListener(RegisterSuperstarConfirmFragment.SUPERSTAR_CONFIRM_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(RegisterSuperstarConfirmFragment.SUPERSTAR_CONFIRM_RESULT_KEY)
            if (result) viewModel.setSuperstar()
        }
    }

    private fun observe() {
        viewModel.onResetSuggestions.observe(viewLifecycleOwner) {
            mLoadMore?.loadMoreEnabled = false
        }
        viewModel.selectableSuggestions.observe(viewLifecycleOwner) {
            mAdapter?.apply {
                Log.d("SUG", "observe: list go ${data.size} to ${it?.size}")
                if (data.size == 0 || it?.size==0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }

        viewModel.onSuperstarPreselected.observe(viewLifecycleOwner) {
            val user = viewModel.selectedSuperstar.value?: return@observe
            val action = RegisterSuperstarFragmentDirections.actionRegisterSuperstarFragmentToRegisterSuperstarConfirmFragment(user.name,user.thumbnail,user.premiumUser)
            findNavController().navigateSafe(action)
        }

        viewModel.onLoadPage.observe(viewLifecycleOwner) { it ->
            it?.let { resp ->
                mLoadMore?.apply{
                    updateLoadMoreIndicator(false)
                    setLoaded()
                    loadMoreEnabled = resp.nextPage
                }
            }
        }

        viewModel.onLoadMoreError.observe(viewLifecycleOwner) {
            mLoadMore?.loadMoreEnabled = false
            updateLoadMoreIndicator(false)
        }

        viewModel.selectedCountries.observe(viewLifecycleOwner) {
            updateFilterChips()
        }
        viewModel.selectedGenders.observe(viewLifecycleOwner) {
            updateFilterChips()
        }
        viewModel.showAllFilters.observe(viewLifecycleOwner) {
            updateFilterChips()
        }

        viewModel.setSuperstarLoading.observe(viewLifecycleOwner) {
            binding.progress.visibility = if(it) View.VISIBLE else View.GONE
            updateNextButton()
        }
        viewModel.superstarStageValid.observe(viewLifecycleOwner) {
            updateNextButton()
        }

        viewModel.setSuperstarError.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(activity, it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onSetSuperstarComplete.observe(viewLifecycleOwner) {
//            Toast.makeText(activity, resources.getString(R.string.register_create_profile_success_toast), Toast.LENGTH_SHORT).show()
//            activity?.let { act->
//                startActivity(Intent(act, HomeActivity::class.java))
//                act.finish()
                val user = viewModel.selectedSuperstar.value?: return@observe
                val action = RegisterSuperstarFragmentDirections.actionRegisterSuperstarFragmentToRegisterUpgradeSuperstarFragment(user.name)
                findNavController().navigateSafe(action)
//            }
        }
    }

    private fun updateNextButton() {
        binding.selectSuperstarNextButton.isEnabled =
            (viewModel.setSuperstarLoading.value == false) && (viewModel.superstarStageValid.value == true)
    }

    companion object {
        private const val MAX_CHIPS = 6
    }

    private fun updateFilterChips() {
        binding.chipGroup.removeAllViews()

        var runningCount = 0
        var totalCount = 0

        totalCount+= viewModel.selectedCountries.value?.size?:0
        totalCount+= viewModel.selectedGenders.value?.size?:0

        val showAll = viewModel.showAllFilters.value==true

        val runUnto = if (totalCount> MAX_CHIPS && showAll) totalCount else MAX_CHIPS

        viewModel.selectedGenders.value.orEmpty().forEach { g ->
            if (runningCount>=runUnto) return@forEach
            runningCount++

            val chip = layoutInflater.inflate(R.layout.item_superstar_filter_chip,
                                              binding.chipGroup,
                                              false) as Chip
            chip.text = resources.getString(g.name)
            chip.tag = g.code
            chip.chipIcon = ContextCompat.getDrawable(requireContext(),g.image)
            chip.chipIconTint = ColorStateList.valueOf(ContextCompat.getColor(requireContext(),R.color.textColorSecondaryLight))
            chip.isCheckable = false
            binding.chipGroup.addView(chip)
            chip.setOnCloseIconClickListener {
                if(viewModel.setSuperstarLoading.value==true) return@setOnCloseIconClickListener
                viewModel.removeGenderFilter(g)
            }
        }

        viewModel.selectedCountries.value.orEmpty().forEach { c ->
            if (runningCount>=runUnto) return@forEach
            runningCount++

            val chip = layoutInflater.inflate(R.layout.item_superstar_filter_chip,
                binding.chipGroup,
                false) as Chip
            chip.text = c.name
            chip.tag = c.nameCode
//            chip.chipIcon = ContextCompat.getDrawable(requireContext(),c.flagID)
            Glide.with(requireContext())
                .asDrawable()
                .load(c.flagID)
                .apply(
                    bitmapTransform(
                        CropCircleTransformation()
                    )
                )
                .into(object : CustomTarget<Drawable>(){
                    override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                        chip.chipIcon = resource
                    }

                    override fun onLoadCleared(placeholder: Drawable?) { }
                })
            chip.isCheckable = false
            binding.chipGroup.addView(chip)
            chip.setOnCloseIconClickListener {
                if(viewModel.setSuperstarLoading.value==true) return@setOnCloseIconClickListener
                viewModel.removeCountryFilter(c)
            }
        }

        if (totalCount > MAX_CHIPS) {
            val chip = layoutInflater.inflate(R.layout.item_superstar_filter_more_chip,
                                              binding.chipGroup,
                                              false) as Chip
            chip.text = if (showAll) resources.getString(R.string.common_hide) else resources.getString(R.string.superstar_filter_more_count,totalCount-MAX_CHIPS)
            chip.tag = -11
            chip.isCheckable = false
            binding.chipGroup.addView(chip)
            chip.setOnClickListener {
                viewModel.toggleFilterShowAll(!showAll)
            }
        }
    }

    private var loadingProgress: CircularProgressIndicator? = null

    private fun initAdapter() {
//        if(mAdapter != null) {
//            return
//        }
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = RegisterSuperstarListQuickAdapter(mutableListOf())

        val layoutMan = LinearLayoutManager(context)

        mLoadMore = RecyclerViewLoadMore(layoutMan)

        mLoadMore?.setOnLoadMoreListener(object: RecyclerViewLoadMore.OnLoadMoreListener {
            override fun onLoadMore() {
                updateLoadMoreIndicator(true)
                viewModel.loadMoreSuggestions()
            }
        })

        binding.superstarList.apply {
            layoutManager = layoutMan
            addOnScrollListener(mLoadMore as RecyclerViewLoadMore)
            setHasFixedSize(true)
            adapter = mAdapter
        }
        mAdapter!!.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true

            val loading = layoutInflater.inflate(R.layout.item_loadmore_footer,binding.superstarList,false)
            loadingProgress = loading.findViewById(R.id.progressBar)
            setFooterView(loading)

            setOnItemClickListener { adapter, view, position ->
                if(viewModel.setSuperstarLoading.value==true) return@setOnItemClickListener
                val user = (adapter as RegisterSuperstarListQuickAdapter).data[position]
                viewModel.selectSuperstar(user.user)
            }
            setDiffCallback(RegisterSuperstarListQuickAdapter.DiffCallback())
        }
    }

    private fun updateLoadMoreIndicator(show: Boolean) {
        loadingProgress?.visibility = if(show) View.VISIBLE else View.INVISIBLE
    }

}