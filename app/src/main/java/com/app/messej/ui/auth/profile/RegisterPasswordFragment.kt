package com.app.messej.ui.auth.profile

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.databinding.FragmentRegisterCreatePasswordBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class RegisterPasswordFragment : Fragment() {

    private lateinit var binding: FragmentRegisterCreatePasswordBinding

    private val viewModel: RegisterPasswordViewModel by viewModels()

    private val args: RegisterPasswordFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_create_password, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setArgs(args.countryCode, args.phoneNumber,args.email,args.otpRequestMode)
        // for handling menu clicks
        addAsMenuHost()
        observe()
        setup()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
        if (args.otpRequestMode == OTPRequestMode.RESET_EMAIL || args.otpRequestMode == OTPRequestMode.RESET_MOBILE) {
            binding.customActionBar.toolbar.title = getString(R.string.forgot_password_new_password)
        } else if (args.otpRequestMode != OTPRequestMode.REGISTER_MOBILE && args.otpRequestMode != OTPRequestMode.REGISTER_EMAIL) {
            binding.customActionBar.toolbar.title = getString(R.string.forgot_password_change_password)
        }
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed:")
                showBackPressAlert()
            }
        })
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.apply {
            setNavigationOnClickListener {
                showBackPressAlert()
            }
        }
    }

    private fun setup() {
       if (args.otpRequestMode==OTPRequestMode.RESET_EMAIL||args.otpRequestMode==OTPRequestMode.RESET_MOBILE){
            binding.createPasswordNextButton.text = getString(R.string.forgot_password_change_password)
       }
        binding.createPasswordNextButton.setOnClickListener {
            when(args.otpRequestMode){
                OTPRequestMode.REGISTER_MOBILE ->  viewModel.setPassword()
                OTPRequestMode.REGISTER_EMAIL->viewModel.setEmailPassword()
                OTPRequestMode.RESET_MOBILE -> viewModel.resetPassword()
                OTPRequestMode.RESET_EMAIL -> viewModel.resetPassword()
                else -> {}
            }
        }

//        binding.textInputConfirmPassword.editText?.apply {
//            setOnFocusChangeListener { _, hasFocus ->
//                if (!hasFocus && !this.text.isNullOrEmpty()) {
//                    viewModel.didEnterConfirmPassword.postValue(true)
//                } else {
//                    viewModel.didEnterConfirmPassword.postValue(false)
//                }
//            }
//        }
//
//        binding.root.rootView.apply {
//            setOnTouchListener { v, _ ->
//                binding.textInputConfirmPassword.editText!!.clearFocus()
//                v.performClick()
//            }
//        }

        binding.textInputConfirmPassword.editText?.apply {
            setOnEditorActionListener {_, actionId, _ ->
                if (!this.text.isNullOrEmpty() && actionId == EditorInfo.IME_ACTION_DONE) {
                    viewModel.didEnterConfirmPassword.postValue(true)
                    true
                }
                else {
                    viewModel.didEnterConfirmPassword.postValue(false)
                    false
                }
            }
        }
    }

    fun observe() {
        viewModel.showPasswordMatchError.observe(viewLifecycleOwner) {
            binding.textInputConfirmPassword.error = if (it) {
                binding.textInputConfirmPassword.isErrorEnabled = true
                resources.getString(R.string.register_create_password_match_error)
            } else {
                binding.textInputConfirmPassword.isErrorEnabled =  false; null
            }
        }

        viewModel.onSetPasswordComplete.observe(viewLifecycleOwner){
            if (it) {
                Toast.makeText(requireContext(), R.string.register_set_password_successful_text, Toast.LENGTH_SHORT).show()
                findNavController().navigateSafe(RegisterPasswordFragmentDirections.actionCreatePasswordFragmentToCreateProfileFragment())
            }
        }
        viewModel.onPasswordResetComplete.observe(viewLifecycleOwner){
            if (it) {
                viewModel.exitPasswordStage()
                Toast.makeText(requireContext(), R.string.register_set_password_successful_text, Toast.LENGTH_SHORT).show()
                findNavController().navigateSafe(RegisterPasswordFragmentDirections.actionGlobalNavGraphLogin())
            }
        }

        viewModel.setPasswordError.observe(viewLifecycleOwner){
            binding.textInputConfirmPassword.isErrorEnabled=true
            binding.textInputConfirmPassword.error=it
        }

        viewModel.onExitPasswordStage.observe(viewLifecycleOwner) {
            Log.d("N2LG", "observe: onExitPasswordStage")
            findNavController().navigateSafe(RegisterPasswordFragmentDirections.actionGlobalNavGraphLogin())
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.exitPasswordStage()
    }

    private fun showBackPressAlert() {
        confirmAction(
            message = R.string.register_set_password_leave_text,
            positiveTitle = R.string.register_set_password_leave_button,
            negativeTitle = R.string.common_cancel
        ) {
            viewModel.exitPasswordStage()
        }
    }

}