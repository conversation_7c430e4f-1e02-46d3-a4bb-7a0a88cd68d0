package com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge

import androidx.annotation.IntRange
import com.app.messej.data.model.api.podium.challenges.BoxData.BoxChallengeBoardData
import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.SlotState
import com.app.messej.data.model.enums.ConFourGameStatus
import com.google.gson.annotations.SerializedName

class BoxChallengeBoardModel {
    companion object {
        const val ROWS = 6
        const val COLS = 6

        private const val DOT = " ○ "
        private const val VERTICAL_LINE = " ┆ "
        private const val HORIZONTAL_LINE = " ┄ "
        private const val VERTICAL_LINE_FILLED = " ║ "
        private const val HORIZONTAL_LINE_FILLED = "═══"
        private const val BOX_EMPTY = "   "
        private const val BOX_P1 = " 1 "
        private const val BOX_P2 = " 2 "

        val dummy: BoxChallengeBoardModel
            get() = BoxChallengeBoardModel().apply {
                verticalLinesArray[0][0] = SlotState.PLAYER1
                verticalLinesArray[0][1] = SlotState.PLAYER1
                horizontalLinesArray[0][0] = SlotState.PLAYER2
                horizontalLinesArray[1][0] = SlotState.PLAYER2
                boxesArray[0][0] = SlotState.PLAYER1

                verticalLinesArray[1][1] = SlotState.PLAYER1
                verticalLinesArray[1][2] = SlotState.PLAYER1
                horizontalLinesArray[1][1] = SlotState.PLAYER2
                horizontalLinesArray[2][1] = SlotState.PLAYER2
                boxesArray[1][1] = SlotState.PLAYER2

                verticalLinesArray[5][5] = SlotState.PLAYER1
                verticalLinesArray[5][6] = SlotState.PLAYER1
                horizontalLinesArray[5][5] = SlotState.PLAYER2
                horizontalLinesArray[6][5] = SlotState.PLAYER2
                boxesArray[5][5] = SlotState.PLAYER1

                horizontalLinesArray[4][2] = SlotState.PLAYER2
                verticalLinesArray[4][2] = SlotState.PLAYER2
            }

    }

    sealed class Line {
        abstract val row: Int
        abstract val col: Int
        abstract val direction: LineDirection
        data class Horizontal(override val row: Int, override val col: Int) : Line() {
            override val direction: LineDirection
                get() = LineDirection.HORIZONTAL
        }
        data class Vertical(override val row: Int, override val col: Int) : Line() {
            override val direction: LineDirection
                get() = LineDirection.VERTICAL
        }
    }

    enum class LineDirection {
        @SerializedName("V")
        VERTICAL, @SerializedName("H")
        HORIZONTAL;

        override fun toString(): String {
            return javaClass.getField(name).getAnnotation(SerializedName::class.java)?.value ?: ""
        }
    }

    data class Box(
        val row: Int,
        val col: Int
    )

    private var verticalLinesArray = Array(ROWS) { Array(COLS+1) { SlotState.EMPTY } }

    private var horizontalLinesArray = Array(ROWS+1) { Array(COLS) { SlotState.EMPTY } }

    private var boxesArray = Array(ROWS) { Array(COLS) { SlotState.EMPTY } }

    fun copy(): BoxChallengeBoardModel {
        val copy = BoxChallengeBoardModel()
        copy.verticalLinesArray = verticalLinesArray
        copy.horizontalLinesArray = horizontalLinesArray
        copy.boxesArray = boxesArray
        return copy
    }

    var board: BoxChallengeBoardData
        get() = BoxChallengeBoardData(
            boxes = boxesArray.map { it.asList() },
            horizontalLines = horizontalLinesArray.map { it.asList() },
            verticalLines = verticalLinesArray.map { it.asList() }
        )
        set(value) {
            boxesArray = value.boxes.map {
                it.toTypedArray()
            }.toTypedArray()
            horizontalLinesArray = value.horizontalLines.map {
                it.toTypedArray()
            }.toTypedArray()
            verticalLinesArray = value.verticalLines.map {
                it.toTypedArray()
            }.toTypedArray()
        }

    private fun drawHorizontalLine(
        @IntRange(from = 0, to = ROWS.toLong()) row: Int,
        @IntRange(from = 0, to = COLS.toLong()-1) col: Int,
        player: ChallengePlayer
    ): Boolean {
        if(horizontalLinesArray[row][col].notEmpty) return false
        horizontalLinesArray[row][col] = player.slotState
        return true
    }

    private fun drawVerticalLine(
        @IntRange(from = 0, to = ROWS.toLong()-1) row: Int,
        @IntRange(from = 0, to = COLS.toLong()) col: Int,
        player: ChallengePlayer
    ): Boolean {
        if(verticalLinesArray[row][col].notEmpty) return false
        verticalLinesArray[row][col] = player.slotState
        return true
    }

    sealed class LineResult {
        object Invalid: LineResult()
        object None: LineResult()
        data class Boxed(val box: Box): LineResult()
        data class DoubleBoxed(val box1: Box, val box2: Box): LineResult()
    }

    private fun checkIfBoxFormed(row: Int, col: Int): Boolean {
        return verticalLinesArray[row][col].notEmpty && verticalLinesArray[row][col+1].notEmpty &&
                horizontalLinesArray[row][col].notEmpty && horizontalLinesArray[row+1][col].notEmpty
    }

    fun mockDrawLine(line: Line, player: ChallengePlayer): Pair<LineResult, ConFourGameStatus> {
        val tempBoardObj = BoxChallengeBoardModel()
        tempBoardObj.boxesArray = boxesArray.map { it.clone() }.toTypedArray()
        tempBoardObj.horizontalLinesArray = horizontalLinesArray.map { it.clone() }.toTypedArray()
        tempBoardObj.verticalLinesArray = verticalLinesArray.map { it.clone() }.toTypedArray()
        val result = tempBoardObj.drawLine(line, player)
        return Pair(result, tempBoardObj.gameStatus)
    }

    fun drawLine(line: Line, player: ChallengePlayer): LineResult {
        val filledBoxes = mutableListOf<Box>()
        when(line) {
            is Line.Horizontal -> {
                val drawn = drawHorizontalLine(line.row, line.col,player)
                if (!drawn) return LineResult.Invalid
                if (line.row>0 && checkIfBoxFormed(line.row-1,line.col)) filledBoxes.add(Box(line.row-1,line.col))
                if (line.row<ROWS && checkIfBoxFormed(line.row,line.col)) filledBoxes.add(Box(line.row,line.col))
            }
            is Line.Vertical -> {
                val drawn = drawVerticalLine(line.row, line.col,player)
                if (!drawn) return LineResult.Invalid
                if (line.col>0 && checkIfBoxFormed(line.row,line.col-1)) filledBoxes.add(Box(line.row,line.col-1))
                if (line.col<COLS && checkIfBoxFormed(line.row,line.col)) filledBoxes.add(Box(line.row,line.col))
            }
        }
        filledBoxes.forEach {
            boxesArray[it.row][it.col] = player.slotState
        }
        return when(filledBoxes.size) {
            0 -> return LineResult.None
            1 -> return LineResult.Boxed(filledBoxes[0])
            2 -> return LineResult.DoubleBoxed(filledBoxes[0],filledBoxes[1])
            else -> throw Exception("More than 2 boxes formed. Which is witchCraft.")
        }
    }

    override fun toString(): String {
        val builder = StringBuilder()

        for (r in 0 until ROWS) {
            // Print horizontal lines with box-drawing characters
            for (c in 0 until COLS) {
                builder.append(DOT)
                builder.append(if(horizontalLinesArray[r][c].notEmpty) HORIZONTAL_LINE_FILLED else HORIZONTAL_LINE)
            }
            builder.append(DOT)
            builder.append('\n')
            for (c in 0 until COLS) {
                builder.append(if(verticalLinesArray[r][c].notEmpty) VERTICAL_LINE_FILLED else VERTICAL_LINE)
                builder.append(when(boxesArray[r][c]){
                    SlotState.EMPTY -> BOX_EMPTY
                    SlotState.PLAYER1 -> BOX_P1
                    SlotState.PLAYER2 -> BOX_P2
                })
            }
            builder.append(if(verticalLinesArray[r][COLS].notEmpty) VERTICAL_LINE_FILLED else VERTICAL_LINE)
            builder.append('\n')

            if (r == ROWS-1) {
                for (c in 0 until COLS) {
                    builder.append(DOT)
                    builder.append(if(horizontalLinesArray[ROWS][c].notEmpty) HORIZONTAL_LINE_FILLED else HORIZONTAL_LINE)
                }
                builder.append(DOT)
                builder.append('\n')
            }
        }
        return builder.toString()
    }

    /**
     * Returns true if the board is full
     */
    val isFull: Boolean
        get() = boxesArray.none { row ->
            row.any {
                it == SlotState.EMPTY
            }
        }

    fun countBoxes(state: SlotState): Int {
        return boxesArray.sumOf { row -> row.count{ it == state } }
    }

    fun getWinner(): ChallengePlayer? {
        val p1 = countBoxes(SlotState.PLAYER1)
        val p2 = countBoxes(SlotState.PLAYER2)
        return if(p1>p2) ChallengePlayer.PLAYER1
        else if(p2>p1) ChallengePlayer.PLAYER2
        else null
    }

    val gameStatus: ConFourGameStatus
        get() {
            return if (isFull) {
                if (countBoxes(SlotState.PLAYER1)==countBoxes(SlotState.PLAYER2)) ConFourGameStatus.DRAW
                else ConFourGameStatus.WON
            } else ConFourGameStatus.NONE
        }
}