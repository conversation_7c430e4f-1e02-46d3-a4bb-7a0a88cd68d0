package com.app.messej.ui.home.publictab.podiums.manage

import android.app.Dialog
import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.PodiumPauseGift
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

abstract class BasePodiumActionsBottomSheetFragment : BottomSheetDialogFragment() {

    protected val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)
    protected val userStatsViewModel: PodiumUserStatsViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isFitToContents = true
                isDraggable = true
                skipCollapsed = true
                isCancelable=true
            }
        }
        return dialog
    }

    protected fun appointAdmin(speaker: AbstractUser) {
        confirmAction(
            title = null, message = getString(R.string.podium_admin_invite_confirmation_message_modified, viewModel._adminIdList.size.toString())
        ) {
            if(!viewModel.isAdminCountReachLimit()) {
                viewModel.appointAsAdmin(speaker.id)
            }else{
                confirmAction(
                    title = null, message = getString(R.string.title_podium_max_admin), positiveTitle = R.string.podium_action_manage_admins
                ) {
                    val action = PodiumSpeakerActionsBottomSheetFragmentDirections.actionPodiumSpeakerActionsBottomSheetFragmentToPodiumAdminsBottomSheetFragment()
                    findNavController().navigateSafe(action)
                }
            }
        }
    }

    protected fun dismissAdmin(speaker: AbstractUser) {
        confirmAction(
            title = null, message = getString(R.string.podium_admin_dismiss_confirmation_message)
        ) {
            viewModel.dismissAsAdmin(speaker.id)
        }
    }

    protected fun cancelAdminInvite(speaker: AbstractUser) {
        confirmAction(
            title = null, message = getString(R.string.podium_cancel_admin_invite_confirmation_message)
        ) {
            viewModel.cancelAdminInvite(speaker.id)
        }
    }

    protected fun withdrawAdmin(speaker: AbstractUser) {
        viewModel.dismissAsAdmin(speaker.id)
    }

    protected fun pauseUserGift(userId:Int){
        val paused = viewModel.isUserGiftPaused(userId)
        val messageRes = if (paused) R.string.podium_resume_gift_confirmation else R.string.podium_pause_gift_user_confirmation
        val positiveTitleRes = if (paused) R.string.podium_resume else R.string.podium_pause
        val actionType = if (paused) PodiumPauseGift.PODIUM_RESUME_GIFT else PodiumPauseGift.PODIUM_PAUSE_GIFT
        confirmAction(
            title = null,
            message = messageRes,
            positiveTitle = positiveTitleRes,
            negativeTitle = R.string.common_cancel
        ) {
            viewModel.pauseUserGift(userId, actionType)
        }
    }


}