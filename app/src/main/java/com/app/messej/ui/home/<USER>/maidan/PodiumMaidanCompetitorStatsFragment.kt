package com.app.messej.ui.home.publictab.maidan

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumMaidanCompetitorStatsBinding
import com.app.messej.ui.utils.ViewUtils

class PodiumMaidanCompetitorStatsFragment : Fragment() {

    private lateinit var binding: FragmentPodiumMaidanCompetitorStatsBinding
    private var mMaidanCompetitorStatsAdapter: PodiumMaidanCompetitorStatsAdapter? = null
    private val viewModel: PodiumMaidanStatsViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_maidan_competitor_stats, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {

        (activity as MainActivity?)?.setupActionBar(binding.appbar.toolbar)

        mMaidanCompetitorStatsAdapter = PodiumMaidanCompetitorStatsAdapter()

        binding.rvCompetitorStats.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mMaidanCompetitorStatsAdapter
        }

        mMaidanCompetitorStatsAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
            }
        }
    }

    private fun observe() {

        viewModel.competitorStatsPager.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mMaidanCompetitorStatsAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }
    }
}