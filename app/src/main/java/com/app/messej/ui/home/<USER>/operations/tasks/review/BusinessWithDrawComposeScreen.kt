package com.app.messej.ui.home.businesstab.operations.tasks.review

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import com.app.messej.R
import com.app.messej.data.model.enums.PayoutAccountType
import com.app.messej.ui.composeComponents.CustomLargeRoundButton
import com.app.messej.ui.composeComponents.CustomOutlinedButton
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros

interface BusinessWithDrawListeners {
    fun onAccept()
    fun onLater()
}

@Composable
fun BusinessWithDrawComposeScreen(
    viewModel: BusinessWithDrawViewModel,
    listener: BusinessWithDrawListeners
) {
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    val payoutEligibility by viewModel.payoutEligibility.observeAsState()
    val isAcceptButtonEnabled by viewModel.isSubmitButtonEnabled.observeAsState(initial = false)
    val isEgyptianUser = viewModel.isEgyptianUser
    val selectedAccountType by viewModel.selectedAccountType.observeAsState()
    val errorType by viewModel.withdrawErrorTypes.observeAsState()
    val primaryAccountTextFieldState = viewModel.primaryAccountTextFieldState
    val socialAccountTextFieldState = viewModel.socialAccountTextFieldState
    val isLoading by viewModel.isLoading.observeAsState(initial = false)

    val redeemedAmount by viewModel.redeemFlax.observeAsState(initial = 0.0)
    val processingFee by viewModel.processingFee.observeAsState(initial = 0.0)
    val transferFee by viewModel.transferFee.observeAsState(initial = 0.0)
    val egyptOfficeFee by viewModel.egyptOfficeFee.observeAsState(initial = 0.0)
    val flixRateToday by viewModel.flaxRateToday.observeAsState(initial = 0.0)
    val payoutValue by viewModel.payOutValue.observeAsState(initial = 0.0)

    val minimumPP = payoutEligibility?.minimumPP.formatDecimalWithRemoveTrailingZeros()
    val maximumPP = payoutEligibility?.maximumPP.formatDecimalWithRemoveTrailingZeros()
    val haveTwoAccounts = payoutEligibility?.socialPayOutInfo != null && payoutEligibility?.normalPayOutInfo != null

    Column(
        modifier = Modifier
            .verticalScroll(state = scrollState)
            .animateContentSize()
            .padding(all = dimensionResource(id = R.dimen.activity_margin))
            .fillMaxWidth()
    ) {
        //Select One Account Text
        if (haveTwoAccounts) {
            Text(
                text = stringResource(id = R.string.title_select_account),
                style = FlashatComposeTypography.defaultType.subtitle1,
                color = colorResource(id = R.color.textColorPrimary)
            )
        }
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.element_spacing)
        )
        //Primary Account View
        if (payoutEligibility?.normalPayOutInfo != null) {
            WithdrawAccountView(
                isSelected = selectedAccountType == PayoutAccountType.PrimaryAccount,
                icon = R.drawable.im_flashat_logo,
                isRadioButtonVisible = haveTwoAccounts,
                title = stringResource(id = R.string.common_primary_account),
                noteText = if (isEgyptianUser) stringResource(id = R.string.payout_egypt_user_note,minimumPP, maximumPP) else stringResource(id = R.string.payout_non_egypt_user_note),
                amountTextFieldState = primaryAccountTextFieldState,
                errorText = errorType?.setError(context = context, minimumAmount = minimumPP, maximumAmount = payoutEligibility?.normalPayOutInfo?.ppValue.formatDecimalWithRemoveTrailingZeros()),
                onValueChange = { viewModel.setCustomAmount(it) },
                onClick = { viewModel.setAccountType(type = PayoutAccountType.PrimaryAccount) },
            )
        }

        //Social Affairs Account View
        if (payoutEligibility?.socialPayOutInfo != null) {
            CustomVerticalSpacer(
                space = dimensionResource(id = R.dimen.activity_margin)
            )
            WithdrawAccountView(
                isSelected = selectedAccountType == PayoutAccountType.SocialAccount,
                icon = R.drawable.ic_social_affairs,
                isRadioButtonVisible = haveTwoAccounts,
                title = stringResource(id = R.string.common_social_support_account),
                noteText = if (isEgyptianUser) stringResource(id = R.string.payout_egypt_user_note,minimumPP, maximumPP) else stringResource(id = R.string.payout_non_egypt_social_note,minimumPP),
                amountTextFieldState = socialAccountTextFieldState,
                errorText = errorType?.setError(context = context, minimumAmount = minimumPP, maximumAmount = payoutEligibility?.socialPayOutInfo?.ppValue.formatDecimalWithRemoveTrailingZeros()),
                onValueChange = { viewModel.setCustomAmount(it) },
                onClick = { viewModel.setAccountType(type = PayoutAccountType.SocialAccount) }
            )
        }
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        //The Procedure Text
        Text(
            text = stringResource(id = R.string.business_task_details_procedure_title),
            style = FlashatComposeTypography.defaultType.subtitle1,
            color = colorResource(id = R.color.textColorPrimary)
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.element_spacing)
        )
        //The Procedure View
        PayoutProcedureView(
            haveTwoAccounts = haveTwoAccounts
        )

        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        //Redeem Offer Text
        Text(
            text = stringResource(id = R.string.title_flax_statement),
            style = FlashatComposeTypography.defaultType.subtitle1,
            color = colorResource(id = R.color.textColorPrimary)
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.element_spacing)
        )
        //Redeem Offer View
        PayoutRedeemOfferView(
            isEgyptUser = isEgyptianUser,
            redeemedAmount = redeemedAmount,
            processingFee = processingFee,
            transferFee = transferFee,
            egyptOfficeFee = egyptOfficeFee,
            flixRateToday = flixRateToday,
            receivableAmount = payoutValue
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        //Accept Button
        CustomLargeRoundButton(
            text = R.string.common_accept,
            isEnabled = isAcceptButtonEnabled,
            isLoading = isLoading,
            onClick = listener::onAccept,
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        //Later Button
        CustomOutlinedButton(
            text = R.string.common_later,
            textColor = colorResource(id = R.color.colorPrimary),
            isEnabled = !isLoading,
            onClick = listener::onLater
        )
    }
}