package com.app.messej.ui.chat

import android.util.Log
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner

/**
 * Holds the currently active chat screen in a lifecycle aware fashion
 */
object ActiveChatTracker {

    sealed class ActiveChatScreen {
        data class PrivateChat(val roomId: String): ActiveChatScreen()
        data class GroupChat(val huddleId: Int): ActiveChatScreen()
        data class HuddlePostComment(val huddleId: Int, val postId: String): ActiveChatScreen()
        data class BroadCastChat(val broadcasterId: Int): ActiveChatScreen()
    }

    private var activeScreen: ActiveChatScreen? = null

    private var activeLifecycle: Lifecycle? = null

    fun registerActiveScreen(screen: ActiveChatScreen, owner: LifecycleOwner) {
        Log.w("ACTIVE_CHAT", "registerActiveScreen: $screen")
        activeScreen = screen
        activeLifecycle = owner.lifecycle.apply {
            addObserver(object: LifecycleEventObserver {
                override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                    if (!event.targetState.isAtLeast(Lifecycle.State.INITIALIZED)) {
                        removeActiveScreen()
                    }
                }
            })
        }
    }

    fun isActive(screen: ActiveChatScreen): Boolean {
        Log.w("ACTIVE_CHAT", "isActive: ${getActiveScreen()} | $screen | ${screen== getActiveScreen()}")
        return screen == getActiveScreen()
    }

    fun getActiveScreen(): ActiveChatScreen? {
        activeScreen?.let { screen ->
            if (activeLifecycle?.currentState?.isAtLeast(Lifecycle.State.RESUMED)==true) {
                return screen
            }
        }
        if (activeLifecycle?.currentState==Lifecycle.State.DESTROYED) {
            removeActiveScreen()
        }
        return null
    }

    fun removeActiveScreen() {
        Log.w("ACTIVE_CHAT", "removeActiveScreen: $activeScreen")
        activeScreen = null
        activeLifecycle = null
    }

}