package com.app.messej.ui.home.businesstab.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.BuyFlaxRates
import com.app.messej.databinding.ItemCoinPurchaseHistoryLayoutBinding


class DealsCoinPurchaseHistoryPagerAdapter() : PagingDataAdapter<BuyFlaxRates, DealsCoinPurchaseHistoryPagerAdapter.DealsFlaxPurchaseHistoryViewHolder>(TransactionsDiff) {


    override fun onBindViewHolder(holder: DealsFlaxPurchaseHistoryViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = DealsFlaxPurchaseHistoryViewHolder(
        ItemCoinPurchaseHistoryLayoutBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    inner class DealsFlaxPurchaseHistoryViewHolder(private val binding: ItemCoinPurchaseHistoryLayoutBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: BuyFlaxRates) = with(binding) {
            binding.apply {
                buyFlaxHistory = item
            }
        }
    }


    object TransactionsDiff : DiffUtil.ItemCallback<BuyFlaxRates>() {
        override fun areItemsTheSame(oldItem: BuyFlaxRates, newItem: BuyFlaxRates) = oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: BuyFlaxRates, newItem: BuyFlaxRates) = oldItem == newItem
    }

}