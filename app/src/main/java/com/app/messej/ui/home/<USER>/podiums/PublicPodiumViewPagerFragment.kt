package com.app.messej.ui.home.publictab.podiums

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.home.publictab.podiums.friends.PodiumLiveFriendsFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PublicPodiumViewPagerFragment : PublicPodiumBaseFragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_base, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun setup() {
        mPodiumPagerAdapter = object: FragmentStateAdapter(this) {
            override fun getItemCount(): Int = PodiumTab.entries.size

            override fun createFragment(position: Int): Fragment {
                val tab = PodiumTab.entries[position]
//                return PodiumInnerListFragment().apply {
//                    arguments = PodiumInnerListFragment.getTabBundle(tab)
//                }

                return when (tab) {
                    PodiumTab.LIVE_PODIUM,PodiumTab.MY_PODIUM  -> {
                        PodiumInnerListFragment().apply {
                        arguments = PodiumInnerListFragment.getTabBundle(tab)
                     }
                    }
                    PodiumTab.LIVE_FRIENDS -> PodiumLiveFriendsFragment()
                }
            }
        }
        super.setup()
    }

    override fun navigateToSearch() {
        findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToPodiumSearchFragment(viewModel.currentTab.value?:PodiumTab.MY_PODIUM))
    }
}