package com.app.messej.ui.home.deleteaccount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentDeleteAccountBinding
import com.app.messej.ui.auth.AuthOTPFragment
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class DeleteAccountFragment : Fragment(),DeleteAccountEmailFragment.AccountDeletedListener,DeleteAccountMobileFragment.AccountDeletedListener {
    private var deleteSheet: DeleteAccountBottomSheet?=null
    private lateinit var binding: FragmentDeleteAccountBinding
    val viewModel:DeleteAccountViewModel by viewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_delete_account, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.text = getString(R.string.delete_account_title)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        setFragmentResultListener(AuthOTPFragment.OTP_REQUEST_KEY) { _, bundle ->
          Toast.makeText(requireActivity(),"deleted",Toast.LENGTH_SHORT).show()
            findNavController().navigateSafe(DeleteAccountFragmentDirections.actionGlobalNavGraphLogin())
        }
        binding.actionDeleteAccount.setOnClickListener {
            deleteSheet = DeleteAccountBottomSheet()
            deleteSheet?.isCancelable=false
            deleteSheet?.show(requireActivity().supportFragmentManager, TAG)
        }

    }

    override fun onAccountDeleted() {
        findNavController().navigateSafe(DeleteAccountFragmentDirections.actionGlobalNavGraphLogin())
    }

    override fun onRequestBottomSheetDismiss() {
    }

    companion object {
        fun newInstance() = DeleteAccountEmailFragment()
        const val TAG = "DeleteAccountBottomSheet"
    }

    private fun observe() {
        viewModel.cancelClick.observe(viewLifecycleOwner){
            if(it){
               deleteSheet?.dismissNow()
            }
        }
        viewModel.accountDeleted.observe(viewLifecycleOwner){
            if(it){
                deleteSheet?.dismissNow()
            }
        }
    }

}