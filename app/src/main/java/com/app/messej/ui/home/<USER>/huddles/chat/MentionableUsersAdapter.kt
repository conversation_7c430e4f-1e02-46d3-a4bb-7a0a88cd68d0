package com.app.messej.ui.home.publictab.huddles.chat

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.Filter
import com.app.messej.R
import com.app.messej.data.model.api.huddles.HuddleMentionableUsersResponse.MentionableUser
import com.app.messej.databinding.ItemPostMentionSuggestionBinding
import com.app.messej.ui.utils.MentionTokenizer
import java.util.Locale

class MentionableUsersAdapter(
    private val mContext: Context,
    participants: List<MentionableUser>,
) : ArrayAdapter<MentionableUser>(mContext, R.layout.item_post_mention_suggestion, participants) {

    private var filteredPremiumUser = listOf<MentionableUser>()

    // private val users = participants

    override fun getItem(position: Int) = filteredPremiumUser[position]
    override fun getCount() = filteredPremiumUser.size

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val binding = convertView?.tag as? ItemPostMentionSuggestionBinding ?: ItemPostMentionSuggestionBinding.inflate(LayoutInflater.from(mContext), parent, false)
        getItem(position).let { user ->
            binding.user = user
        }
        return binding.root
    }

    override fun getFilter() = filter

    private var filter: Filter = object : Filter() {

        override fun performFiltering(constraint: CharSequence?): FilterResults {
            val query = if (!constraint.isNullOrEmpty()) autocomplete(constraint.toString()) else participants
            return FilterResults().apply {
                values = query
                count = query.size
            }
        }

        private fun autocomplete(input: String) = participants.filter {
            val token = input.removePrefix(MentionTokenizer.TOKEN_START_CHAR.toString()).lowercase(Locale.getDefault())
            it.userNickNameOrName.lowercase(Locale.getDefault()).contains(token) || it.username.lowercase(Locale.getDefault()).contains(token)
        }

        override fun publishResults(constraint: CharSequence?, results: FilterResults) {
            filteredPremiumUser = (results.values?: listOf<MentionableUser>()) as List<MentionableUser>
            notifyDataSetInvalidated()
        }

        override fun convertResultToString(result: Any) = "${MentionTokenizer.TOKEN_START_CHAR}${(result as MentionableUser).username}"
    }

}