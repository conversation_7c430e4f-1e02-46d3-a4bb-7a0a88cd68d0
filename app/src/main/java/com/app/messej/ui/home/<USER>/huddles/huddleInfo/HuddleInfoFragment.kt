package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.ReportedTab
import com.app.messej.databinding.FragmentHuddleInfoBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.syncToExpandingTitle
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.bumptech.glide.Glide
import com.stfalcon.imageviewer.StfalconImageViewer


class HuddleInfoFragment : Fragment(), MenuProvider {

    private val args: HuddleInfoFragmentArgs by navArgs()

    private lateinit var binding: FragmentHuddleInfoBinding

    private val viewModel : HuddleInfoViewModel by navGraphViewModels(R.id.nav_huddle_info)

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_info, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
        Log.d("huddleInfo", "onViewCreated: ")
    }

    override fun onStart() {
        super.onStart()
        val actionBar = binding.toolbar
        (activity as MainActivity).setupActionBar(actionBar)
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        Log.d("HIF", "onCreateMenu: ${viewModel.huddle.value?.huddleRole}")
        if (viewModel.huddle.value?.huddleRole?.isElevated==true) {
            menuInflater.inflate(R.menu.menu_huddle_info, menu)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_edit -> {
                findNavController().navigateSafe(HuddleInfoFragmentDirections.actionPublicHuddleInfoFragmentToPublicEditHuddleDetailsFragment(args.huddleId))
            }
            else -> return false
        }
        return true
    }

    private fun setup() {
        viewModel.setHuddleId(args.huddleId)

        syncToExpandingTitle(binding.huddleName,binding.collapsingToolbarLayout)
        binding.participantsLayout.setOnClickListener {
            val type = viewModel.huddle.value?.huddleType?: HuddleType.PRIVATE
            findNavController().navigateSafe(HuddleInfoFragmentDirections.actionPublicHuddleInfoFragmentToNavigationHuddleParticipants(args.huddleId,type))
        }

        binding.requestsLayout.setOnClickListener {
            findNavController().navigateSafe(HuddleInfoFragmentDirections.actionGlobalManageRequestInviteFragment(args.huddleId))
        }

        binding.reportedMessagesLayout.setOnClickListener {
            val type = viewModel.huddle.value?.huddleType?: HuddleType.PRIVATE
            findNavController().navigateSafe(HuddleInfoFragmentDirections.actionPublicHuddleInfoFragmentToPublicHuddleReportedBaseFragment(args.huddleId, type,ReportedTab.TAB_MESSAGES))
        }

        binding.huddleLinkLayout.setOnClickListener {
            shareHuddle()
        }

        binding.huddleLinkButton.setOnClickListener {
            shareHuddle()
        }

        binding.huddleDeleteLeaveButton.setOnClickListener {
            val huddle = viewModel.huddle.value?: return@setOnClickListener
           findNavController().navigateSafe(HuddleInfoFragmentDirections.actionGlobalHuddleDeleteLeaveConfirmFragment(huddle.isManager,huddle.huddleType))
        }

        binding.huddleDeleteMinisterButton.setOnClickListener {
            val huddle = viewModel.huddle.value?: return@setOnClickListener
            findNavController().navigateSafe(HuddleInfoFragmentDirections.actionGlobalHuddleDeleteLeaveConfirmFragment(true,huddle.huddleType))
        }

        binding.muteButton.setOnCheckedChangeListener { switch, isChecked ->
            if (switch.isPressed) {
                viewModel.muteUnmuteHuddle(isChecked)
            }
        }

        binding.linkSharingSwitchButton.setOnCheckedChangeListener { switch, isChecked ->
            if (switch.isPressed) {
                viewModel.onOffLinkSharing(isChecked)
            }
        }

        binding.requestToJoinSwitchButton.setOnCheckedChangeListener { switch, isChecked ->
            if (switch.isPressed) {
                viewModel.onOffRequestToJoin(isChecked)
            }
        }

        binding.huddleDp.setOnClickListener {
            val dp = viewModel.huddle.value?.thumbnail?: return@setOnClickListener
            StfalconImageViewer.Builder(context, listOf(dp)) { imageView, image ->
                    Glide.with(requireContext())
                        .load(image)
                        .into(imageView)
                }
                .withTransitionFrom(binding.huddleDp)
                .withHiddenStatusBar(false)
                .build()
                .show()
        }
        binding.actionSellHuddle.setOnClickListener {
            if (binding.actionSellHuddle.text.equals(resources.getString(R.string.text_Button_sell_huddle))){
                Toast.makeText(requireContext(), getString(R.string.sell_huddle_more_than_200_participants,viewModel.huddle.value?.huddleSellMinParticipantCount?:0.0), Toast.LENGTH_SHORT).show()
            }else{
                findNavController().navigateSafe(HuddleInfoFragmentDirections.actionPublicHuddleInfoFragmentToHuddleSellFragment(args.huddleId, viewModel.huddle.value!!.huddleForSale, viewModel.huddle.value!!.huddleSellFlax.toString()))
            }
        }
    }

    private fun shareHuddle() {
        val inviteText = if (viewModel.huddle.value!!.isPrivate) resources.getString(R.string.huddle_info_share_text_group,  viewModel.huddle.value?.name, viewModel.huddle.value?.inviteLink)
        else resources.getString(R.string.huddle_info_share_text, viewModel.huddle.value?.name,viewModel.huddle.value?.inviteLink)

        if (inviteText.isNotEmpty()) {
            val sendIntent: Intent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_TEXT, inviteText)
                type = "text/plain"
            }
            val shareIntent = Intent.createChooser(sendIntent, null)
            startActivity(shareIntent)
        }
    }

    private fun observe() {
        viewModel.huddle.observe(viewLifecycleOwner){
            binding.huddle = it
            binding.huddleType = resources.getString(getHuddleTypeString())
            activity?.invalidateOptionsMenu()
        }

        viewModel.profileInfoLoading.observe(viewLifecycleOwner) {
            binding.contentShimmer.apply {
                if (it) startShimmer() else hideShimmer()
            }
            binding.headerShimmer.apply {
                if (it) startShimmer() else hideShimmer()
            }
        }

        viewModel.onHuddleLeft.observe(viewLifecycleOwner){
            findNavController().popBackStack(if(it==HuddleType.PRIVATE) R.id.nav_chat_group else R.id.nav_chat_huddle, true)
        }

        viewModel.onHuddleMuted.observe(viewLifecycleOwner){
            val type = resources.getString(getHuddleTypeString())
            if (it){
                Toast.makeText(requireContext(), resources.getString(R.string.huddle_info_huddle_muted,type), Toast.LENGTH_SHORT).show()
            }else{
                Toast.makeText(requireContext(), resources.getString(R.string.huddle_info_huddle_unmuted,type), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.onHuddleInfoError.observe(viewLifecycleOwner) {
            //If failed load huddle information, the screen will exit
            if (it){
                Toast.makeText(requireContext(), resources.getString(R.string.huddle_load_failed), Toast.LENGTH_SHORT).show()
                findNavController().popBackStack()
            }
        }

        setFragmentResultListener(HuddleDeleteLeaveConfirmFragment.HUDDLE_DELETE_LEAVE_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(HuddleDeleteLeaveConfirmFragment.HUDDLE_DELETE_LEAVE_RESULT_KEY)
            if (result) viewModel.leaveOrDeleteHuddle()
        }

        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<Boolean>(HuddleInfoViewModel.HUDDLE_UPDATED)?.observe(
            viewLifecycleOwner) { updated ->
            if (updated) viewModel.getHuddleInfo(args.huddleId)
        }

        setFragmentResultListener(SellHuddleFragment.SELL_HUDDLE_CREATE_MODE){ _, bundle->
            val huddleID= bundle.getInt(SellHuddleFragment.SELL_HUDDLE_SUCCESS)
                viewModel.reFreshHuddleInfo(huddleID)
        }
    }

    private fun getHuddleTypeString(): Int {
        return if(viewModel.huddle.value?.isPrivate==true) R.string.huddle_type_private else R.string.huddle_type_public
    }
}