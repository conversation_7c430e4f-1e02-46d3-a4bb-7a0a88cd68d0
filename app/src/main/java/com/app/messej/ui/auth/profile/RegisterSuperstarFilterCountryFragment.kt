package com.app.messej.ui.auth.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.SelectableCountry
import com.app.messej.databinding.ItemFilterCountryBinding
import com.app.messej.databinding.LayoutRegisterSuperstarFilterCountryBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.SlideInBottomAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.google.android.material.checkbox.MaterialCheckBox

class RegisterSuperstarFilterCountryFragment : Fragment() {

    private lateinit var binding: LayoutRegisterSuperstarFilterCountryBinding

    private val viewModel: RegisterSuperstarViewModel by navGraphViewModels(R.id.navigation_register_superstar)

    private var mAdapter: BaseQuickAdapter<SelectableCountry, BaseDataBindingHolder<ItemFilterCountryBinding>>? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_register_superstar_filter_country, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        binding.checkboxSelectAll.addOnCheckedStateChangedListener(checkBoxListener)
        viewModel.checkAllCountriesSelected()

        initAdapter()
    }

    private fun observe() {
        viewModel.filteredCountryList.observe(viewLifecycleOwner) {
            mAdapter?.setDiffNewData(it.toMutableList())
        }

        viewModel.isAllSelected.observe(viewLifecycleOwner) {
            binding.checkboxSelectAll.apply {
                removeOnCheckedStateChangedListener(checkBoxListener)
                checkedState = if(it) {
                    MaterialCheckBox.STATE_CHECKED
                } else {
                    MaterialCheckBox.STATE_UNCHECKED
                }
                addOnCheckedStateChangedListener(checkBoxListener)
            }
        }
    }

    private val checkBoxListener = MaterialCheckBox.OnCheckedStateChangedListener { checkBox, state ->
        if (state == MaterialCheckBox.STATE_CHECKED) {
            viewModel.selectAllCountries(true)
        } else {
            viewModel.selectAllCountries(false)
        }
    }

    private fun initAdapter() {

//        binding.countryList.loadCountries { selectedCountry: CPCountry ->
//            // your code to handle selected country
//        }
        if(mAdapter != null) {
            return
        }

        mAdapter = object: BaseQuickAdapter<SelectableCountry, BaseDataBindingHolder<ItemFilterCountryBinding>>(R.layout.item_filter_country) {
            override fun convert(holder: BaseDataBindingHolder<ItemFilterCountryBinding>, item: SelectableCountry) {
                holder.dataBinding?.apply {
                    country = item
                }
            }
        }

        binding.countryList.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(true)
            adapter = mAdapter
        }
        mAdapter!!.apply {
            animationEnable = true
            adapterAnimation = SlideInBottomAnimation()
            isAnimationFirstOnly = true

            setOnItemClickListener { _, _, position ->
                viewModel.toggleCountrySelection(position)
            }
            setDiffCallback(object: DiffUtil.ItemCallback<SelectableCountry>() {
                override fun areItemsTheSame(oldItem: SelectableCountry,newItem: SelectableCountry): Boolean {
                    return oldItem.obj.nameCode == newItem.obj.nameCode
                }

                override fun areContentsTheSame(oldItem: SelectableCountry,
                                                newItem: SelectableCountry): Boolean {
                    return oldItem.selected == newItem.selected
                }
            })
        }
    }
}