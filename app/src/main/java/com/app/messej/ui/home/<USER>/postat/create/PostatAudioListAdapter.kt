package com.app.messej.ui.home.publictab.postat.create

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.databinding.ItemPostatMusicListBinding
import com.app.messej.ui.home.publictab.postat.create.CreatePostatViewModel.SelectableAudioUIModel

class PostatAudioListAdapter(private val listener: AudioActionListener, private val lifecycle: LifecycleOwner) : PagingDataAdapter<SelectableAudioUIModel, PostatAudioListAdapter.PostatAudioVH>(PodiumDiff) {

    interface AudioActionListener {
        fun onAudioClick(music: SelectableAudioUIModel, layoutPosition: Int)
    }
    inner class PostatAudioVH(private val binding: ItemPostatMusicListBinding): RecyclerView.ViewHolder(binding.root){
        fun bind(item: SelectableAudioUIModel) = with(binding) {
            music = item
            musicHolder.setOnClickListener {
                listener.onAudioClick(item , layoutPosition)
            }
        }
    }
    object PodiumDiff : DiffUtil.ItemCallback<SelectableAudioUIModel>(){
        override fun areItemsTheSame(oldItem: SelectableAudioUIModel, newItem: SelectableAudioUIModel) =
            oldItem.audio.mediaMeta.musicId == newItem.audio.mediaMeta.musicId

        override fun areContentsTheSame(oldItem: SelectableAudioUIModel, newItem: SelectableAudioUIModel): Boolean {
            return oldItem.audio == newItem.audio && oldItem.audioSelected == newItem.audioSelected
        }

    }

    override fun onBindViewHolder(holder: PostatAudioVH, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PostatAudioVH {
        val binding = ItemPostatMusicListBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        binding.lifecycleOwner = lifecycle
        return PostatAudioVH(binding)
    }

}