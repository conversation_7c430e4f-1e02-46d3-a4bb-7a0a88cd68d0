package com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.legal.LegalAffairsPendingFineResponse
import com.app.messej.databinding.ItemPayFineBinding

class PayFineAdapter : PagingDataAdapter<LegalAffairsPendingFineResponse.LegalAffairsFine, PayFineAdapter.ListViewHolder>(PayFineDiffUtil) {

    override fun onBindViewHolder(holder: ListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(item = it, position = position) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ListViewHolder {
        val binding = ItemPayFineBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ListViewHolder(binding = binding)
    }

    inner class ListViewHolder(private val binding: ItemPayFineBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: LegalAffairsPendingFineResponse.LegalAffairsFine?, position: Int) {
            binding.apply {
                violation = item?.category
                accusation = item?.reason
                fineAmount = "${item?.amount}"
            }
        }
    }


    object PayFineDiffUtil : DiffUtil.ItemCallback<LegalAffairsPendingFineResponse.LegalAffairsFine>() {
        override fun areItemsTheSame(oldItem: LegalAffairsPendingFineResponse.LegalAffairsFine, newItem: LegalAffairsPendingFineResponse.LegalAffairsFine): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: LegalAffairsPendingFineResponse.LegalAffairsFine, newItem: LegalAffairsPendingFineResponse.LegalAffairsFine): Boolean {
            return oldItem == newItem
        }
    }
}