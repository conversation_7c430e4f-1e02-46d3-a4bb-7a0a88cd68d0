package com.app.messej.ui.home.publictab.podiums.challenges.knowledgerace

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.databinding.FragmentKnowledgeRaceSettingsBottomSheetBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class KnowledgeRaceSettingsBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentKnowledgeRaceSettingsBottomSheetBinding
    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_knowledge_race_settings_bottom_sheet, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
    }

    private fun setUp() {
        binding.switchMusic.setOnCheckedChangeListener { _, isChecked ->
            if (viewModel.knowledgeRaceSettings.value?.music == isChecked) return@setOnCheckedChangeListener
            viewModel.updateSettings(music = isChecked)
        }
        binding.switchSoundEffect.setOnCheckedChangeListener { _, isChecked ->
            if (viewModel.knowledgeRaceSettings.value?.soundEffects == isChecked) return@setOnCheckedChangeListener
            viewModel.updateSettings(soundEffects = isChecked)
        }
        binding.btnLanguages.setOnClickListener {
            showMoreMenu(it)
        }
    }

    private fun showMoreMenu(v: View): PopupMenu {

        val popup = PopupMenu(requireContext(), v)
        popup.menuInflater.inflate(R.menu.menu_knowledge_race_languages, popup.menu)
        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.arabic -> {
                    viewModel.updateSettings(language = AppLocale.ARABIC)
                }

                R.id.english -> {
                    viewModel.updateSettings(language = AppLocale.ENGLISH)
                }

                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
        return popup
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog =  super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.apply {
                isFitToContents = true
                isDraggable = true
                skipCollapsed = true
                isCancelable=true
            }
        }
        return dialog
    }
}