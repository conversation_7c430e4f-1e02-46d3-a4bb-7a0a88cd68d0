package com.app.messej.ui.home.publictab.podiums.challenges

import android.animation.Animator
import android.content.res.AssetFileDescriptor
import android.content.res.ColorStateList
import android.media.MediaPlayer
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.animation.OvershootInterpolator
import androidx.annotation.RawRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.challenges.PenaltyData
import com.app.messej.data.model.api.podium.challenges.PenaltyKickTarget
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge.ChallengeStatus
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore.PenaltyPlayerRole
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore.PlayerColor
import com.app.messej.data.model.socket.PodiumChallengePenaltyKickResultPayload
import com.app.messej.data.model.socket.PodiumChallengePenaltyTargetPayload
import com.app.messej.databinding.LayoutPodiumChallengeBoardPenaltyBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardPenaltyPlayerInfoBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardPenaltySpeakerOverlayBinding
import com.app.messej.databinding.LayoutPodiumChallengeBoardPenaltySpeakerOverlayTargetBinding
import com.app.messej.databinding.LayoutPodiumChatOverlayUserJoinedBinding
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.awaitEnd
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeIn
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeOut
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumPenaltyChallengePresenter.KickResultAnimation.Companion.animation
import com.app.messej.ui.home.publictab.podiums.challenges.penalty.PenaltyViewer.PenaltyScene
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.app.messej.ui.utils.Synchronize
import com.app.messej.ui.utils.ViewUtils.fillAddView
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.time.ZonedDateTime
import kotlin.coroutines.resume

class PodiumPenaltyChallengePresenter(holder: ViewGroup, challenge: PodiumChallenge, challengeListener: ChallengeEventListener, private val penaltyListener: PenaltyChallengeEventListener): PodiumChallengePresenter(holder, challenge, challengeListener) {

    override lateinit var liveBinding: LayoutPodiumChallengeBoardPenaltyBinding
    private var playerOverlayBinding: LayoutPodiumChallengeBoardPenaltySpeakerOverlayBinding? = null
    private var chatOverlayBinding: LayoutPodiumChatOverlayUserJoinedBinding? = null

    private lateinit var player: MediaPlayer

    interface PenaltyChallengeEventListener {
        fun showHowToPlayOverlay(millis: Long)
        fun onSelectTarget(userId: Int, target: PenaltyKickTarget)
        fun setPlayerReady(userId: Int)
    }

    override val challengeTitle: Int
        get() = R.string.podium_challenge_penalty

    companion object {

        @JvmStatic
        @BindingAdapter("playerRole","playerColor")
        fun setPlayerIcon(view: AppCompatImageView, role: PenaltyPlayerRole?, color: PlayerColor?) {
            val res = when (role) {
                PenaltyPlayerRole.KICKER -> when (color) {
                    PlayerColor.PURPLE -> R.drawable.ic_penalty_kicker_primary
                    PlayerColor.YELLOW -> R.drawable.ic_penalty_kicker_secondary
                    null -> null
                }
                PenaltyPlayerRole.GOALIE -> when (color) {
                    PlayerColor.PURPLE -> R.drawable.ic_penalty_goalie_primary
                    PlayerColor.YELLOW -> R.drawable.ic_penalty_goalie_secondary
                    null -> null
                }
                null -> null
            }
            if (res != null) {
                view.visibility = View.VISIBLE
                view.setImageResource(res)
            } else {
                view.visibility = View.GONE
            }
        }

        @JvmStatic
        @BindingAdapter("roundScores","turn")
        fun setTurnStatus(view: AppCompatImageView, scores: List<PodiumChallengeScore.PenaltyTurnStatus>?, turn: Int?) {
            view.imageTintList = null
            val status = scores?.getOrNull(turn?:0)
            val colorRes = when(status) {
                PodiumChallengeScore.PenaltyTurnStatus.WON -> R.color.colorPass
                PodiumChallengeScore.PenaltyTurnStatus.LOST -> R.color.colorError
                PodiumChallengeScore.PenaltyTurnStatus.VOID -> R.color.colorAlwaysLightSurfaceSecondaryDark
                else -> R.color.colorAlwaysDarkSurfaceSecondaryDarker
            }
            view.imageTintList = colorRes.let { ContextCompat.getColor(view.context, it) }.let { ColorStateList.valueOf(it) }
        }
    }

    override fun setupView() {
        liveBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_penalty, holder, false)
        setupPlayer()
    }

    override fun onNewScoresAvailable(forceRefresh: Boolean) {
        Log.w("PCPPT", "onNewScoresAvailable: forceRefresh $forceRefresh")
        if (penaltyAnimationJob?.isActive==true) {
            Log.e("PCPPT", "onNewScoresAvailable: penalty animation is active. Skipping refresh")
            return
        }
        refreshScores()
    }

    override fun cleanup() {
        super.cleanup()
//        challengeListener.allowCustomBoard(false)
        hideOverlays()
        penaltyAnimationJob?.cancel()
        try {
            player.apply {
                stop()
                release()
            }
        } catch (_: Exception) {}
    }

    override fun onSpeakerClick(item: ActiveSpeakerUIModel, view: View, mainScreen: Boolean): Boolean {
        return true
    }

    fun onPlayerReady(userId: Int) {
        Log.w("PCPPT", "onPlayerReady: $userId")
    }

    private var activeRound: PenaltyData.PenaltyRound? = null

    fun onStartTurn(data: PenaltyData) {
        Log.w("PCPPT", "onStartTurn: $data | ${data.activeTurn} | start: ${data.turnStartTime} | end: ${data.turnEndTime} | now: ${ZonedDateTime.now()}")
        challenge.penaltyData = data
        scope.launch {
            data.activeTurn?.let {
                showTargetSelection(it)
            }
        }
    }

    private var penaltyAnimationJob: Job? = null

    override fun refreshChallengeUI(status: ChallengeStatus) {
        try {
            scope.launch {
                if (status in listOf(ChallengeStatus.GAME_OVER,ChallengeStatus.ENDED) && uiState==UIState.LIVE) {
//                    challengeListener.allowCustomBoard(false)
                    Log.w("PCPPT", "refreshChallengeUI: waiting for winner animation to finish")
                    penaltyAnimationJob?.join()
                    if (status == ChallengeStatus.GAME_OVER) {
                        animateWin()
                    }
                    hideOverlays()
                    super.refreshChallengeUI(status)
                } else {
                    super.refreshChallengeUI(status)
                }
            }
        } catch (_: Exception) {}
    }

    private enum class SceneOverlay {
        WAITING,
        SELECTION,
        TIMER,
        LOTTIE,
        ROUND_SWITCH,
        WINNER;

        fun getView(binding: LayoutPodiumChallengeBoardPenaltySpeakerOverlayBinding): View = when(this) {
            WAITING -> binding.waitingOverlay
            SELECTION -> binding.selectionOverlay
            TIMER -> binding.timerOverlay
            LOTTIE -> binding.lottieOverlay
            ROUND_SWITCH -> binding.roundOverlay.root
            WINNER -> binding.winnerOverlay.root
        }
    }

    private var currentSceneOverlay: SceneOverlay? = null

    private suspend fun showSceneOverlay(overlay: SceneOverlay?) {
        if (currentSceneOverlay==overlay) return
        playerOverlayBinding?.let { pob ->
            SceneOverlay.entries.filter { it !in listOf(overlay, currentSceneOverlay) }.forEach {
                it.getView(pob).isVisible = false
            }
            currentSceneOverlay?.getView(pob)?.fadeOut(200)
            overlay?.getView(pob)?.fadeIn()
            currentSceneOverlay = overlay
        }
    }

    override fun refreshLiveUI() {
        Log.w("PCPPT", "refreshLiveUI: $challenge")
        scope.launch {
            Log.w("PCPPT", "refreshLiveUI: status: ${challenge.status}, started: ${challenge.hasStarted}, ended: ${challenge.gameOver}, running: ${challenge.running}")

            liveBinding.content.isVisible = false
            liveBinding.liveScoreLayout.isVisible = false
            
            Log.w("PCPPT", "refreshLiveUI ${challenge.hasStarted} ${challenge.gameOver}" )
            if (challenge.hasStarted) {
//                challengeListener.allowCustomBoard(true)
                if (playerOverlayBinding==null) {
                    challengeListener.showSpeakerOverlay(true, 1f).apply {
                        val binding: LayoutPodiumChallengeBoardPenaltySpeakerOverlayBinding =
                            DataBindingUtil.inflate(layoutInflater, R.layout.layout_podium_challenge_board_penalty_speaker_overlay, holder, false)
                        this.fillAddView(binding.root)
                        refreshScores()
                        liveBinding.liveScoreLayout.isVisible = true
                        playerOverlayBinding = binding
                    }
                }
                challengeListener.showChatOverlay(true).apply {
                    val binding = LayoutPodiumChatOverlayUserJoinedBinding.inflate(layoutInflater, holder, false)
                    this.fillAddView(binding.root)
                    chatOverlayBinding = binding
                }
                if (!challenge.gameOver) {
                    showPenaltyScene(PenaltyState.Idle)
                    showSceneOverlay(null)
                    val turn = challenge.penaltyData?.activeTurn
                    Log.w("PCPPT", "refreshLiveUI: activeTurn $turn")
                    if(turn!=null) {
                        showTargetSelection(turn)
                    } else {
                        penaltyAnimationJob?.cancel()
                        penaltyAnimationJob = scope.launch {
                            Log.w("PCPPT", "refreshLiveUI: myScore $myScore, activeRound $activeRound")
                            if (myScore != null) {
                                challenge.penaltyData?.let { pd ->
                                    if (pd.currentTurn == 0) {
                                        if (activeRound == null) showSwitchRound(pd.currentRound?:PenaltyData.PenaltyRound.ROUND_ONE)
                                    } else {
                                        activeRound = pd.currentRound
                                    }
                                }
                                Log.w("PCPPT", "refreshLiveUI: Player Ready")
                                penaltyListener.setPlayerReady(myUserId)
                            }
                            showSceneOverlay(SceneOverlay.WAITING)
                        }
                    }
                } else {
                    // wait for game over event from BE
                }
            } else {
                hideOverlays()
//                challengeListener.allowCustomBoard(false)
                liveBinding.content.isVisible = true
                liveBinding.content.showStartCountdown({
                       if (challengeListener.getLiveViewModel().iAmPartOfRunningChallenge.value == true) {
                           penaltyListener.showHowToPlayOverlay(challenge.countDownRemaining?.toMillis() ?: 0)
                       }
                   }, {
                       refreshLiveUI()
                   })
            }
        }
    }

    private fun PenaltyPlayerRole.getScore(): PodiumChallengeScore? {
        return scores.find { it.playerRole == this }
    }

    private fun refreshScores() {
        Log.w("PCPPT", "refreshScores" )

        val round = challenge.penaltyData?.currentRound?: return

        fun LayoutPodiumChallengeBoardPenaltyPlayerInfoBinding.updateView(role: PenaltyPlayerRole, updateWins: (Int) -> Unit) {
            role.getScore()?.let { sc ->
                Log.w("PCPPT", "refreshScores: $sc" )
                this.player = sc
                this.currentRound = round
                updateWins.invoke(sc.winsForRound(round))
            }
        }
        liveBinding.playerOne.updateView(PenaltyPlayerRole.KICKER) {
            liveBinding.playerOneScore = it
        }
        liveBinding.playerTwo.updateView(PenaltyPlayerRole.GOALIE) {
            liveBinding.playerTwoScore = it
        }

    }

    override fun convertScore(sc: PodiumChallengeScore): String {
        return sc.coinsWon?.numberToKWithFractions()?:"0"
    }

    override fun showFinalScores(): Boolean  = false

    fun onKickResult(data: PodiumChallengePenaltyKickResultPayload) {
        try {
            penaltyAnimationJob?.cancel()
            penaltyAnimationJob = scope.launch {
                showSceneOverlay(null)
                Log.w("PCPPT", "onKickResult: $data")
                showKickResultAnimation(KickResultAnimation.COUNTDOWN)
                val state = suspendCancellableCoroutine { cont ->
                    showPenaltyScene(PenaltyState.Penalty(data.result.kickerTarget, data.result.keeperTarget, data.switchRound, data.result.status)) { st ->
                        Log.w("PCPPT", "showPenaltyScene: onEnd: $st")
                        if (isActive) {
                            try {
                                cont.resume(st)
                            } catch (e: Exception) {
                                Firebase.crashlytics.recordException(e)
                            }
                        }
                    }
                }
                challenge.penaltyData?.activeTurn?.let { turn ->
                    scores.forEach {
                        it.updateScoreBoard(turn,data.result.status)
                    }
                }
                refreshScores()
                if (state is PenaltyState.Penalty) {
                    showPenaltyScene(PenaltyState.Idle)
                    showKickResultAnimation(state.result.animation)
                    if (state.switchRound) {
                        scores.forEach {
                            it.playerRole = it.playerRole?.opposite
                        }
                        showSwitchRound(PenaltyData.PenaltyRound.ROUND_TWO)
                    }
                    if (myScore != null) {
                        Log.w("PCPPT", "showPenaltyScene: onEnd: Player ready")
                        penaltyListener.setPlayerReady(myUserId)
                    }
                }
                refreshScores()
            }
        } catch (_: Exception) {}
    }

    private var pendingScoreUpdate: PodiumChallenge? by Synchronize(null)

    private var scoreUpdateJob: Job? = null

    override fun onUpdateChallengeAndScores(cs: PodiumChallenge) {
        Log.w("PCPPT", "onUpdateChallengeAndScores: $cs")
        scoreUpdateJob?.cancel()
        scoreUpdateJob = scope.launch {
            pendingScoreUpdate = cs
            Log.w("PCPPT", "waiting for penaltyAnimationJob")
            penaltyAnimationJob?.join()
            Log.w("PCPPT", "penaltyAnimationJob done")
            super.onUpdateChallengeAndScores(cs)
            pendingScoreUpdate = null
        }
    }

    private fun showPenaltyScene(state: PenaltyState, onEnd: (PenaltyState) -> Unit = {}) {
        Log.w("PCPPT", "showPenaltyScene: $state")
        val kicker = PenaltyPlayerRole.KICKER.getScore()?.playerColor?: PlayerColor.PURPLE
        val goalie = PenaltyPlayerRole.GOALIE.getScore()?.playerColor?: PlayerColor.YELLOW
        playerOverlayBinding?.sceneViewer?.setContent {
            var endCalled: Boolean = false
            PenaltyScene(kicker, goalie, state) { st ->
                if (endCalled) return@PenaltyScene
                endCalled = true
                onEnd.invoke(st)
            }
        }
    }

    sealed class PenaltyState {
        data object Idle: PenaltyState()

        data class Penalty(
            val kickerDirection: PenaltyKickTarget?,
            val goalieDirection: PenaltyKickTarget?,
            val switchRound: Boolean,
            val result: PodiumChallengeScore.PenaltyKickResult
        ): PenaltyState()
    }

    private var selectedTarget: PenaltyKickTarget? = null
    private var confirmedTarget: PenaltyKickTarget? = null

    private fun selectTarget(target: PenaltyKickTarget?) {
        selectedTarget = target
        if (target == null) {
            confirmTarget()
        }
    }

    private fun confirmTarget() {
        confirmedTarget = selectedTarget
        playerOverlayBinding?.let { pob ->
            PenaltyKickTarget.entries.forEach {
                pob.getTarget(it).selected = it == confirmedTarget
            }
        }
    }

    interface PenaltyTargetListener {
        fun onSelectTarget(target: PenaltyKickTarget)
    }

    private fun LayoutPodiumChallengeBoardPenaltySpeakerOverlayBinding.getTarget(target: PenaltyKickTarget): LayoutPodiumChallengeBoardPenaltySpeakerOverlayTargetBinding {
        return when (target) {
            PenaltyKickTarget.BOTTOM_LEFT -> targetBottomLeft
            PenaltyKickTarget.BOTTOM_RIGHT -> targetBottomRight
            PenaltyKickTarget.CENTER -> targetTopCenter
            PenaltyKickTarget.TOP_LEFT -> targetTopLeft
            PenaltyKickTarget.TOP_RIGHT -> targetTopRight
        }
    }

    fun onSelectTarget(data: PodiumChallengePenaltyTargetPayload) {
        Log.w("PCPPT", "onSelectTarget")
        if (data.userId!=myUserId) return
        confirmTarget()
        scope.launch {
            challenge.penaltyData?.activeTurn?.let {
                showTargetSelection(it, true)
            }
        }
    }

    private suspend fun showTargetSelection(turn: PenaltyData.ActiveTurn, targetSelected: Boolean = false) {
        tickerJob?.cancel()
        Log.d("PCP", "tickerJob canceled in: showTargetSelection")
        selectTarget(null)
        val extraTime = turn.remainingTime.toMillis()-PenaltyData.TURN_DURATION_MILLIS
        if ((extraTime)>100) {
            delay(extraTime+100)
        }
        val remaining = turn.remainingTime
        Log.w("PCPPT", "showTargetSelection: remaining; $remaining")
        if (remaining.seconds<=0) return

        if (myScore==null || targetSelected) {
            showSceneOverlay(SceneOverlay.TIMER)
            playerOverlayBinding?.audienceTimer?.showChallengeTimer(remainingMs = remaining.toMillis(), redAfter = 5, timerColor = R.color.textColorOnPrimary) {
//                showSceneOverlay(null)
            }
            playerOverlayBinding?.timerText?.text = resources.getString(
                if(myScore!=null) R.string.podium_challenge_penalty_timer_text_player
                else R.string.podium_challenge_penalty_timer_text_audience
            )
            return
        }
        else {
            showSceneOverlay(SceneOverlay.SELECTION)
            playerOverlayBinding?.targetListener = object : PenaltyTargetListener {
                override fun onSelectTarget(target: PenaltyKickTarget) {
                    selectTarget(target)
                    penaltyListener.onSelectTarget(myUserId, target)
                }
            }
            playerOverlayBinding?.selectionTimer?.showChallengeTimer(remainingMs = remaining.toMillis(), redAfter = 5, timerColor = R.color.textColorOnPrimary) {
//                showSceneOverlay(null)
            }
        }
    }

    private suspend fun showSwitchRound(round: PenaltyData.PenaltyRound) {
        Log.w("PCPPT", "showSwitchRound: $round")
        val me = myScore?: return
        activeRound = round
        playerOverlayBinding?.roundOverlay?.apply {
            currentRound = round
            player = me
        }
        showSceneOverlay(SceneOverlay.ROUND_SWITCH)
        delay(5000)
        showSceneOverlay(null)
    }

    private enum class KickResultAnimation {
        GOAL, SAVE, DRAW, MISSED, COUNTDOWN;

        val lottieFile: Int
            @RawRes
            get() = when(this) {
                GOAL -> R.raw.lottie_penalty_goal
                SAVE -> R.raw.lottie_penalty_save
                DRAW -> R.raw.lottie_penalty_draw
                MISSED -> R.raw.lottie_penalty_missed
                COUNTDOWN -> R.raw.lottie_penalty_countdown
            }

        companion object {
            val PodiumChallengeScore.PenaltyKickResult.animation: KickResultAnimation
                get() = when (this) {
                    PodiumChallengeScore.PenaltyKickResult.GOAL -> GOAL
                    PodiumChallengeScore.PenaltyKickResult.SAVE -> SAVE
                    PodiumChallengeScore.PenaltyKickResult.VOID -> MISSED
                }
        }
    }

    private suspend fun showKickResultAnimation(anim: KickResultAnimation?) {
        if (anim == null) {
            showSceneOverlay(null)
            return
        }
        showSceneOverlay(SceneOverlay.LOTTIE)
        coroutineScope {
            val tasks = listOf(async {
                suspendCancellableCoroutine { cont ->
                    playerOverlayBinding?.animationView?.apply {
                        setAnimation(anim.lottieFile)
                        repeatCount = 0
                        playAnimation()
                        removeAllAnimatorListeners()
                        addAnimatorListener(object : Animator.AnimatorListener {
                            override fun onAnimationStart(animation: Animator) {}

                            override fun onAnimationEnd(animation: Animator) {
                                Log.w("PCPPT", "tasks: Lottie animation Done")
                                if (!cont.isCompleted) {
                                    cont.resume(Unit)
                                }
                            }

                            override fun onAnimationCancel(animation: Animator) {}
                            override fun onAnimationRepeat(animation: Animator) {}
                        })
                    }
                }
            }, async {
                suspendCancellableCoroutine<Unit> { cont ->
                    if (anim in listOf(KickResultAnimation.GOAL, KickResultAnimation.SAVE)) {
                        playCheer {
                            Log.w("PCPPT", "tasks: Cheer playback done")
                            if (!cont.isCompleted) {
                                cont.resume(Unit)
                            }
                        }
                    } else {
                        Log.w("PCPPT", "tasks: Cheer not needed")
                        if (!cont.isCompleted) {
                            cont.resume(Unit)
                        }
                    }
                }
            })
            tasks.awaitAll()
            Log.w("PCPPT", "tasks: Cheer and Lottie done")

            delay(1000)
        }
    }

    private fun setupPlayer() {
        val mp = MediaPlayer()

        val afd: AssetFileDescriptor = resources.openRawResourceFd(R.raw.penalty_crowd_cheer) ?: return
        mp.setDataSource(afd)
        afd.close()
        mp.prepare()
        mp.isLooping=false
        player = mp
    }

    private fun playCheer(onComplete: () -> Unit) {
        player.apply {
            if (!isPlaying) {
                seekTo(0)
                start()
            }
            setOnCompletionListener {
                onComplete.invoke()
            }
        }
    }

    private suspend fun animateWin() {
        pendingScoreUpdate?.let {
            super.onUpdateChallengeAndScores(it)
            pendingScoreUpdate = null
        }
        val winners = challengeWinners
        when(winners.size) {
            0 -> { /* DO nothing. Just make the game over */ }
            1 -> {
                val winner = winners[0]
                showSceneOverlay(SceneOverlay.WINNER)
                playerOverlayBinding?.winnerOverlay?.apply {
                    player = winner
                    winnerImage.isVisible = true
                    winnerImage.scaleX = 0.1f
                    winnerImage.scaleY = 0.1f
                    winnerImage.animate().apply {
                        interpolator = OvershootInterpolator()
                        duration = 500
                        scaleX(1f)
                        scaleY(1f)
                        start()
                        awaitEnd()
                    }
                }
                delay(5000)
            }
            2 -> showKickResultAnimation(KickResultAnimation.DRAW)
        }
    }

    override fun onUserJoined(user: PodiumParticipant) {
        chatOverlayBinding?.addToFlow(user)
    }

}