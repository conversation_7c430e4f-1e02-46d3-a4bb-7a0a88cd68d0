package com.app.messej.ui.auth.biometric

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch

class BiometricViewModel(application: Application) :AndroidViewModel(application){
    private val _biometricEnabled = MutableLiveData<Boolean>(null)
    val biometricEnabled: LiveData<Boolean?> = _biometricEnabled
    private val _biometricSuccess = MutableLiveData<Boolean>(null)
    val biometricSuccess: LiveData<Boolean?> = _biometricSuccess
    private val _biometricFailedMessage = MutableLiveData("")

    init {

        viewModelScope.launch {
            //_biometricEnabled.postValue(datastoreRepository.isBiometricEnabled())
            /*
            Currently, the setting values are being entered here,
            Once the implementation for enabling biometric authentication is completed,
            the corresponding value can be retrieved from the settings.*/
            _biometricEnabled.postValue(false)
        }
    }

    fun showBiometricPrompt(){
        _biometricEnabled.postValue(false)
    }
    fun setAuthenticationSuccess(isSuccess: Boolean) {
     _biometricSuccess.postValue(isSuccess)
    }
    fun setAuthenticationFailed(isSuccess: Boolean) {
        _biometricSuccess.postValue(isSuccess)
    }

    fun failedMassage(errString: String) {
      _biometricFailedMessage.postValue(errString)
    }

}