package com.app.messej.ui.chat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentBaseDocumentAttachBinding
import com.app.messej.ui.utils.TextFormatUtils.enableTextFormatting

abstract class BaseDocumentAttachFragment : Fragment() {

    private lateinit var binding: FragmentBaseDocumentAttachBinding

    abstract val viewModel: BaseChatViewModel

    abstract val destinationName: String?

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_base_document_attach, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        binding.destination = destinationName
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(binding.customActionBar.toolbar)
            binding.customActionBar.toolbar.apply {
                title = viewModel.chatMedia.value?.sourceFileName
            }
        }
    }

    private fun setup() {
        binding.chatSendButton.setOnClickListener {
            viewModel.prepareAndSendMessage()
        }
        if (viewModel.enableTextFormatting) {
            binding.chatInput.enableTextFormatting(requireActivity(),viewModel)
        }
    }

    private fun observe() {
        viewModel.onMessageCreated.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
    }

}