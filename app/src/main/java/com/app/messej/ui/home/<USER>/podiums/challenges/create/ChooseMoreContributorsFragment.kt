package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.view.ActionMode
import androidx.core.view.MenuProvider
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.databinding.FragmentChooseMoreContributorsBinding
import com.app.messej.databinding.LayoutActionModeSearchBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class ChooseMoreContributorsFragment : Fragment(), MenuProvider {
    private lateinit var binding: FragmentChooseMoreContributorsBinding
    private var mAdapter: ChooseMoreContributorsAdapter? = null
    private val viewModel: PodiumCreateChallengeViewModel by viewModels()
    private val liveViewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_choose_more_contributors, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.podium_live_users)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }
    private var actionMode: ActionMode? = null

    var searchBinding: LayoutActionModeSearchBinding? = null
    private fun showSearchMode(show: Boolean) {
        if (show) {
            viewModel.searchContributorKeyword.value = ""
            val callback = object : ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    val viewB: LayoutActionModeSearchBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_action_mode_search, null, false)
                    viewB.lifecycleOwner = viewLifecycleOwner
                    mode?.customView = viewB.root
                    searchBinding = viewB

                    viewB.apply {
                        keyword = viewModel.searchContributorKeyword
                        showKeyboard(searchBox)
                    }
                    return true
                }

                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    searchBinding?.apply {
                        showKeyboard(searchBox)
                    }
                    return false
                }

                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    return false
                }

                override fun onDestroyActionMode(mode: ActionMode?) {
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        } else {
            actionMode?.finish()
            actionMode = null
        }
    }
    private fun setup() {
        val pod = liveViewModel.podium.value?: return
        val challenge = liveViewModel.activeChallenge.value?: return
        viewModel.setPodium(pod,challenge)
        initAdapter()
        binding.tvSelect.setOnClickListener {
            viewModel.selectOptionEnabled.value = !(viewModel.selectOptionEnabled.value ?: false)
        }
        binding.sendFab.setOnClickListener {
            viewModel.sendContributorRequests()
        }
        binding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun observe() {
        viewModel.liveContributorSearchList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }

        viewModel.selectOptionEnabled.observe(viewLifecycleOwner) {
            it?.let { mAdapter?.changeCheckBoxVisibility(it) }
        }

        viewModel.contributionSent.observe(viewLifecycleOwner){
            findNavController().popBackStack()
        }

        liveViewModel.allSpeakers.observe(viewLifecycleOwner) {
            mAdapter?.apply {
                notifyDataSetChanged()
            }
        }
    }

    private fun initAdapter() {
        mAdapter =
            ChooseMoreContributorsAdapter(object : ChooseMoreContributorsAdapter.ItemListener {
                override fun onItemCheckChanged(item: PodiumParticipant, onChecked: Boolean) {
                    if (onChecked && !viewModel.chooseMoreContributorsList.contains(item)) {
                        viewModel.chooseMoreContributorsList.add(item)
                    } else if (!onChecked && viewModel.chooseMoreContributorsList.contains(item)) {
                        viewModel.chooseMoreContributorsList.remove(item)
                    }
                    viewModel.validateContributorSelection()
                    Log.d("CONTRIBUTORSLIST", viewModel.chooseMoreContributorsList.toString())
                }

                override fun canSelectUser(item: PodiumParticipant): Boolean {
                    return when(viewModel.activeChallenge.value?.challengeType) {
                        ChallengeType.CONFOUR -> viewModel.activeChallenge.value?.participantScores?.find { it.userId == item.id } == null
                        else -> liveViewModel.allSpeakers.value?.find { it.speaker.id == item.id } == null
                    }
                }
            }, viewModel.selectOptionEnabled.value?:false)

        val layoutMan = LinearLayoutManager(context)
        binding.liveUsers.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                if (loadState.refresh !is LoadState.Loading) {
                    binding.swipeRefresh.isRefreshing = false
                }
                binding.multiStateView.viewState = state
                binding.tvSelect.isVisible = state == MultiStateView.ViewState.CONTENT
            }
            refresh()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_home_podium, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> {
                showSearchMode(true)
            }

            else -> return false
        }
        return true
    }
    override fun onPause() {
        super.onPause()
        if (activity?.isChangingConfigurations != true) {
            showSearchMode(false)
        }
    }
}