package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.databinding.FragmentFlaxTransactionListBinding
import com.app.messej.ui.home.businesstab.adapter.DealsTransactionsPagerAdapter
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView


class FlaxTransactionListFragment : Fragment(), DealsTransactionsPagerAdapter.DealsTransactionListener {

    private lateinit var binding: FragmentFlaxTransactionListBinding
    private val viewModel: BusinessDealsListViewModel by viewModels()
    private var adapter: DealsTransactionsPagerAdapter?=null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flax_transaction_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {

        searchEmptyView()
        viewModel.setTransferType("")
        adapter = DealsTransactionsPagerAdapter(requireContext(),this, false)
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = LinearLayoutManager.VERTICAL // Set the orientation as needed
        binding.rvTransactionList.layoutManager = layoutManager
        binding.rvTransactionList.adapter = adapter

        adapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1){
                        MultiStateView.ViewState.EMPTY
                    }else {
                        MultiStateView.ViewState.CONTENT
                    }
                } else {
                    if (itemCount < 1){
                        MultiStateView.ViewState.EMPTY
                    }else {
                        MultiStateView.ViewState.CONTENT
                    }
                }
            }
        }

    }
    private fun observe() {
        viewModel.transferHistoryList.observe(viewLifecycleOwner){
            adapter?.submitData(viewLifecycleOwner.lifecycle, pagingData = it)
        }
    }

    private fun emptyView() {
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.deals_empty_title).apply {
                text = resources.getString(R.string.no_transactions_made)
            }
        }
    }
    private fun searchEmptyView() {
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_empty_flax_history)
            findViewById<AppCompatTextView>(R.id.eds_empty_message).apply {
                text = resources.getString(R.string.no_transactions_made)
            }
        }
    }

    override fun onUserClick(item: DealsTransferHistory) {
        if (item.status == DealsTransferHistory.FlaxStatus.Sent) {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(item.receiverId ?: return))
        }
        else {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(item.senderId ?: return))
        }
    }


}