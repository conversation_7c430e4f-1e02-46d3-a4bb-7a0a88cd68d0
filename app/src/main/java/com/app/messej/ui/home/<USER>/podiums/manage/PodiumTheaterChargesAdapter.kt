package com.app.messej.ui.home.publictab.podiums.manage

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import com.app.messej.databinding.ItemTheaterChargesBinding

class PodiumTheaterChargesAdapter : PagingDataAdapter<PodiumMaidanSupporter, PodiumTheaterChargesAdapter.PodiumTheaterChargesViewHolder>(DiffCallback) {

    inner class PodiumTheaterChargesViewHolder(private val binding: ItemTheaterChargesBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PodiumMaidanSupporter, position: Int) = with(binding) {
            speaker = item
            dividerItem.visibility = if (position == 0) View.GONE else View.VISIBLE
        }
    }

    object DiffCallback : DiffUtil.ItemCallback<PodiumMaidanSupporter>() {
        override fun areItemsTheSame(oldItem: PodiumMaidanSupporter, newItem: PodiumMaidanSupporter): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(
            oldItem: PodiumMaidanSupporter,
            newItem: PodiumMaidanSupporter,
        ): Boolean {
            return oldItem.charges == newItem.charges
        }
    }

    override fun onBindViewHolder(holder: PodiumTheaterChargesViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it, position)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PodiumTheaterChargesViewHolder {
        val binding = ItemTheaterChargesBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PodiumTheaterChargesViewHolder(binding)
    }
}