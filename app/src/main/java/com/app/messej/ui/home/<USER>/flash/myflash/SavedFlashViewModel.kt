package com.app.messej.ui.home.publictab.flash.myflash

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.entity.FlashVideo

class SavedFlashViewModel(application: Application) : FlashListBaseViewModel(application) {
    override val _flashFeedList: LiveData<PagingData<FlashVideo>> = flashRepo.getSavedFlashPager().liveData.cachedIn(viewModelScope)
}