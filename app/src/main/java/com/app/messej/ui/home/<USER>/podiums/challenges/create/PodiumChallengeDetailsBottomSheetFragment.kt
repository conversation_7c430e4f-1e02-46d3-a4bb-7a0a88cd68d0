package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumChallengeDetailsBinding
import com.app.messej.ui.common.PolicyDocumentViewModel
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog

class PodiumChallengeDetailsBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {
    private lateinit var binding: FragmentPodiumChallengeDetailsBinding
    private val viewModel: PolicyDocumentViewModel by viewModels()
    private val documentArgs: PodiumChallengeDetailsBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_challenge_details, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getLegalDocument(documentArgs.documentType)
        this.isCancelable = false
        setup()
        observe()
    }

    private fun setup() {
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            state = BottomSheetBehavior.STATE_EXPANDED
            isDraggable = false
        }
        viewModel.setButtonVisibility(false)
        binding.actionClose.setOnClickListener {
            dismiss()
        }
    }

    private fun observe() {
        viewModel.policyData.observe(viewLifecycleOwner) {
            binding.policyWebView.visibility = View.VISIBLE
            val html = it?.legalDocument?.description
            if (!html.isNullOrEmpty()) {
                binding.policyWebView.loadData(html, "text/html; charset=utf-8", "UTF-8")
            }
        }
        viewModel.tncError.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
            binding.policyWebView.visibility = View.GONE
        }
    }
}