package com.app.messej.ui.home.publictab.broadcast

import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.ui.chat.BaseVideoAttachFragment

class BroadcastChatVideoAttachFragment : BaseVideoAttachFragment() {
    override val viewModel: BroadcastChatViewModel by navGraphViewModels(R.id.nav_chat_broadcast)

    private val args: BroadcastChatVideoAttachFragmentArgs by navArgs()

    override val destinationName: String?
        get() = args.destination
}