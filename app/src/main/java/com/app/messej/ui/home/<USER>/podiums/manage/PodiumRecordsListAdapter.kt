package com.app.messej.ui.home.publictab.podiums.manage

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumRecord
import com.app.messej.data.model.entity.Podium
import com.app.messej.databinding.ItemPodiumRecordsExpandableItemBinding

class PodiumRecordsListAdapter(private val listener: PodiumActionListener): PagingDataAdapter<PodiumRecord, PodiumRecordsListAdapter.RecordsListViewHolder>(PodiumUserDiff)  {

    interface PodiumActionListener {
        fun onRecordsClicked(pod: PodiumRecord)
    }

    inner class RecordsListViewHolder(private val binding: ItemPodiumRecordsExpandableItemBinding): RecyclerView.ViewHolder(binding.root){
        fun bind(item: PodiumRecord) = with(binding) {
            record = item
            expanded = false
            expandArrow.setOnClickListener {view ->
                expanded = !(expanded?:false)
//                if(expanded == true) TransitionManager.beginDelayedTransition(recordView, AutoTransition())
            }
            recordStarterRole.apply {
                when (item.startedBy.role) {
                    Podium.PodiumUserRole.MANAGER -> {
                        text = resources.getString(R.string.common_manager)
                    }
                    Podium.PodiumUserRole.ADMIN -> {
                        text = resources.getString(R.string.common_admin)
                    }
                    else -> {}
                }
            }
        }
    }
    object PodiumUserDiff : DiffUtil.ItemCallback<PodiumRecord>(){
        override fun areItemsTheSame(oldItem: PodiumRecord, newItem: PodiumRecord) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PodiumRecord, newItem: PodiumRecord) = oldItem == newItem

    }

    override fun onBindViewHolder(holder: RecordsListViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecordsListViewHolder {
        val binding = ItemPodiumRecordsExpandableItemBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return RecordsListViewHolder(binding)
    }
}