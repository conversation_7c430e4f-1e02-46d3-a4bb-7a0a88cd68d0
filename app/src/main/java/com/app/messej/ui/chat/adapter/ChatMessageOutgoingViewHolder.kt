package com.app.messej.ui.chat.adapter

import android.content.res.ColorStateList
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.databinding.ItemChatMessageOutgoingBinding
import com.app.messej.ui.chat.ChatMessageUIModel


class ChatMessageOutgoingViewHolder(val binding: ItemChatMessageOutgoingBinding, userId: Int, allowReplySwipe: Boolean, private var mListener
: ChatAdapter.ChatClickListener) : ChatMessageViewHolder(binding.root,userId, allowReplySwipe, mListener) {

    override fun bind(item: ChatMessageUIModel): Unit = with(binding) {
        super.bind(item)
        val cm = (item as ChatMessageUIModel.ChatMessageModel)

        message = cm.message
        selected = item.selected

        val messageSpan = formatAndHighlightText(chatMessage.context, cm)
        chatMessage.setText(messageSpan?:"", TextView.BufferType.SPANNABLE)
        chatMessage.setExpanded(false)
        chatMessage.apply {
            setOnStateChangeListener { expanded ->
                cm.expanded = expanded
            }
            setExpanded(cm.expanded)
        }
        when (val msg = cm.message) {
            is HuddleChatMessage -> {
                showLikes = true
                totalLikes = msg.totalLikes
            }
            is PrivateChatMessage -> {
                showLikes = false
                totalLikes = 0
            }
            is BroadcastMessage -> {
                showLikes = true
                totalLikes = msg.totalLikes
                starred = msg.starred
            }
        }

        sendStatus.apply {
            if ((cm.message.sendStatus != AbstractChatMessage.SendStatus.NONE)){
                setImageResource(R.drawable.ic_access_time)
                imageTintList = ContextCompat.getColor(context, R.color.colorChatTextSecondaryLight).let { ColorStateList.valueOf(it) }
            } else if (cm.message.isRead && cm.message !is HuddleChatMessage) {
                setImageResource(R.drawable.ic_check_all)
                imageTintList = ContextCompat.getColor(context, R.color.chatMessageTickColorPurple).let { ColorStateList.valueOf(it) }
            } else if (cm.message.isDelivered){
                imageTintList = ContextCompat.getColor(context, R.color.chatMessageTickColorYellow).let { ColorStateList.valueOf(it) }
                setImageResource(R.drawable.ic_check_all)
            }else {
                imageTintList = ContextCompat.getColor(context, R.color.chatMessageTickColorYellow).let { ColorStateList.valueOf(it) }
                setImageResource(R.drawable.ic_check)
            }
//            if (cm.message.isRead) {
//                setImageResource(R.drawable.ic_check_all)
//                imageTintList = ContextCompat.getColor(context, R.color.colorPrimary).let { ColorStateList.valueOf(it) }
//            } else if (cm.message.isDelivered){
//                imageTintList = ContextCompat.getColor(context, R.color.textColorSecondaryLight).let { ColorStateList.valueOf(it) }
//                setImageResource(R.drawable.ic_check_all)
//            }else {
//                imageTintList = ContextCompat.getColor(context, R.color.textColorSecondaryLight).let { ColorStateList.valueOf(it) }
//                if (cm.message.sendStatus == AbstractChatMessage.SendStatus.NONE) R.drawable.ic_check else R.drawable.ic_access_time
//            }
        }
//        sendStatus.setImageResource(if (cm.message.isRead) {
//            R.drawable.ic_check_all
//        }else
//            if (cm.message.sendStatus == AbstractChatMessage.SendStatus.NONE) R.drawable.ic_check else R.drawable.ic_access_time)
        val color = setChatBubbleColor(binding.chatBubble, cm)
        loadMedia(mediaHolder, cm, color)
        loadReply(replyHolder, cm, color)

        btnOutgoingForward.setOnClickListener {
            mListener.onForwardClick(item.message,layoutPosition)
        }

        chatHolder.setOnClickListener { mListener.onItemClick(item.message, layoutPosition) }
        chatHolder.setOnLongClickListener {
            mListener.onItemLongClick(item.message, layoutPosition)
            true
        }

        chatMessage.setOnClickListener {
            if(mListener.onItemClick(item.message, layoutPosition)) return@setOnClickListener
            if(chatMessage.isExpanded()) return@setOnClickListener
            chatMessage.setExpanded(true)
        }
        chatMessage.setOnLongClickListener {
            mListener.onItemLongClick(item.message, layoutPosition)
            true
        }
        setSwipeListener(swipeLayout,item.message,layoutPosition)
    }

    override fun getHighlightView() = binding.highlightView
}