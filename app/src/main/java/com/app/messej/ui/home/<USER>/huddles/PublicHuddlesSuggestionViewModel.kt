package com.app.messej.ui.home.publictab.huddles

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.SuggestedHuddle
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class PublicHuddlesSuggestionViewModel(application: Application): AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val huddleRepo = HuddlesRepository(getApplication())

    val user: CurrentUser get() = accountRepo.user

    var searchKeyword = MutableLiveData<String>(null)
    sealed class SuggestionMode {
        object Suggestion : SuggestionMode()
        data class Search(val keyword: String) : SuggestionMode()
    }

    private val suggestionMode = MutableLiveData<SuggestionMode>(SuggestionMode.Suggestion)

    init {
        viewModelScope.launch {
            suggestionMode.postValue(SuggestionMode.Suggestion)
            searchKeyword.asFlow().debounce(500L).collect {
                it?: return@collect
                if (it.isNotBlank()){
                    //avoid posting the same value
                    suggestionMode.value?.let { cur ->
                        if (cur is SuggestionMode.Search && cur.keyword== it) return@collect
                    }
                    suggestionMode.postValue(SuggestionMode.Search(it))
                }else{
                    suggestionMode.value?.let { cur ->
                        if (cur is SuggestionMode.Suggestion) return@collect
                    }
                    suggestionMode.postValue(SuggestionMode.Suggestion)
                }
            }
        }
    }

    val huddlesSearchSuggestionList: LiveData<PagingData<SuggestedHuddle>> = suggestionMode.switchMap {
        it?: return@switchMap null
        initiateSearch(it)
    }

    private fun initiateSearch(mode: SuggestionMode): LiveData<PagingData<SuggestedHuddle>> {
        return when(mode){
            is SuggestionMode.Search ->{
                huddleRepo.getHuddlesSuggestions(mode.keyword).liveData.cachedIn(viewModelScope)
            }
            SuggestionMode.Suggestion -> {
                huddleRepo.getHuddlesSuggestions("").liveData.cachedIn(viewModelScope)
            }
        }
    }

    fun resetSearch(){
        searchKeyword.postValue("")
    }

}