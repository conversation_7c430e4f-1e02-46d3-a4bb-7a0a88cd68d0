package com.app.messej.ui.home.publictab.common

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumShareBottomSheetBinding
import com.app.messej.ui.utils.FragmentExtensions.copy
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class InternalShareBottomSheet: BottomSheetDialogFragment() {

    private lateinit var binding: FragmentPodiumShareBottomSheetBinding
    private val navArgs: InternalShareBottomSheetArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_share_bottom_sheet, container, false)
        binding.apply {
            lifecycleOwner = viewLifecycleOwner
        }
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL,R.style.Widget_Flashat_SettingsBottomSheet)
    }

    private fun setup() {
        binding.shareContent.text = navArgs.message
        binding.actionCopy.setOnClickListener {
            copy(navArgs.message)
        }
        binding.actionMoreMenu.setOnClickListener{
            sharePodium(navArgs.message)
        }
        binding.actionFlashat.setOnClickListener{
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalForwardHomeFragment(navArgs.source, textMessage = navArgs.message))
        }
    }

    private fun sharePodium(message:String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(
                Intent.EXTRA_TEXT, message
            )
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, null)
        startActivity(shareIntent)
    }

}