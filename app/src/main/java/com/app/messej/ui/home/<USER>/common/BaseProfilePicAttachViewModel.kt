package com.app.messej.ui.home.publictab.common

import android.app.Application
import android.net.Uri
import androidx.annotation.CallSuper
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.repository.ChatRepository
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

abstract class BaseProfilePicAttachViewModel(application: Application): AndroidViewModel(application) {

    private val chatRepo = ChatRepository(application)

    private var imageCaptureTempFile: File? = null

    protected var finalImage: File? = null

    val finalImagePath = MutableLiveData<String?>()

    suspend fun getImageUriForCapture(): Uri = withContext(Dispatchers.IO) {
        val file = chatRepo.createTempImageFile()
        imageCaptureTempFile = file
        chatRepo.getUriForFile(file)
    }

    fun addCapturedImage(){
        viewModelScope.launch {
            imageCaptureTempFile?.let { file ->
                cropAttachedMedia()
            }
        }
    }

    fun addImage(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            imageCaptureTempFile = file
            cropAttachedMedia()
        }
    }

    @CallSuper
    open fun addCroppedImage(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            finalImage = file
            imageCaptureTempFile = null
            finalImagePath.postValue(file.absolutePath)
        }
    }

    fun clearImage() {
        finalImage = null
        imageCaptureTempFile = null
        finalImagePath.postValue(null)
    }

    fun onCropCancelled() {
        imageCaptureTempFile = null
    }

    val onTriggerCrop = LiveEvent<Pair<Uri,Uri>>()

    private fun cropAttachedMedia() {
        viewModelScope.launch(Dispatchers.IO) {
            val media = imageCaptureTempFile?: return@launch
            val src = chatRepo.getUriForFile(media)
            val dest = chatRepo.getUriForFile(chatRepo.createTempImageFile())
            onTriggerCrop.postValue(Pair(src,dest))
        }
    }
}