package com.app.messej.ui.home.publictab.huddles.chat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.databinding.FragmentPublishInFlashConfirmationBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class PublishInFlashConfirmationFragment : BottomSheetDialogFragment() {

    lateinit var binding: FragmentPublishInFlashConfirmationBinding

    companion object {
        const val POST_TO_FLASH_REQUEST_KEY = "postToFlashKey"
        const val POST_TO_FLASH_RESULT_KEY = "postToFlashValue"
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_publish_in_flash_confirmation, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        binding.huddleAndFlashButton.setOnClickListener {
            setFragmentResult(POST_TO_FLASH_REQUEST_KEY, bundleOf(POST_TO_FLASH_RESULT_KEY to true))
            findNavController().popBackStack()
        }

        binding.huddleOnlyButton.setOnClickListener {
            setFragmentResult(POST_TO_FLASH_REQUEST_KEY, bundleOf(POST_TO_FLASH_RESULT_KEY to false))
            findNavController().popBackStack()
        }

        binding.actionClose.setOnClickListener {
            findNavController().popBackStack()
        }
    }

}