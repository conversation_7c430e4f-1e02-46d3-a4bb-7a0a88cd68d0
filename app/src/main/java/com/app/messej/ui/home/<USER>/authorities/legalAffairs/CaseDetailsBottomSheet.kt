package com.app.messej.ui.home.publictab.authorities.legalAffairs

import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import androidx.annotation.OptIn
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toDrawable
import androidx.core.os.bundleOf
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.extractor.DefaultExtractorsFactory
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.api.legal.AbstractCaseDetails
import com.app.messej.data.model.api.legal.CaseDetails
import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.enums.CaseVerdictAction
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.ReportCaseStatus
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.databinding.FragmentCaseDetailsBottomSheetBinding
import com.app.messej.databinding.ItemCaseDetailIdCardViewBinding
import com.app.messej.databinding.ItemCaseDetailsProofBinding
import com.app.messej.databinding.ItemCaseProofMediaBinding
import com.app.messej.databinding.ItemGuiltyAlertViewBinding
import com.app.messej.databinding.ItemReportMediaAudioBinding
import com.app.messej.databinding.LayoutDefenseContentViewerBinding
import com.app.messej.databinding.LayoutListStateErrorBinding
import com.app.messej.databinding.LayoutReportContentViewerBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils.displayText
import com.app.messej.ui.home.publictab.authorities.legalAffairs.CaseDetailsPreviewViewPagerAdapter.FuturePlaybackObject
import com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine.PayFineFragment.Companion.PAY_FINE_REQUEST_KEY
import com.app.messej.ui.home.publictab.postat.FeedPostatAdapter.Companion.POSTAT_ASPECT_DEFAULT
import com.app.messej.ui.home.publictab.postat.FeedPostatAdapter.Companion.POSTAT_ASPECT_MAX
import com.app.messej.ui.home.publictab.postat.FeedPostatAdapter.Companion.POSTAT_ASPECT_MIN
import com.app.messej.ui.legal.report.ReportUtils.setup
import com.app.messej.ui.profile.PublicUserProfileFragmentArgs
import com.app.messej.ui.profile.PublicUserProfileViewModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.BindingExtensions.setupIDCard
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.FragmentExtensions.showAlertWithSingleButton
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.bumptech.glide.Glide
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.slider.Slider
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference


class CaseDetailsBottomSheet : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentCaseDetailsBottomSheetBinding
    private val args: CaseDetailsBottomSheetArgs by navArgs()
    private val viewModel: CaseDetailsViewModel by viewModels()
    private val userViewModel: PublicUserProfileViewModel by viewModels()
    private lateinit var userIdCardArgs:  PublicUserProfileFragmentArgs

    private var idCardBinding: ItemCaseDetailIdCardViewBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_case_details_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addObservers()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_CaseDetailsBottomSheet)
    }

    override fun getTheme(): Int {
        return R.style.Widget_Flashat_CaseDetailsBottomSheet
    }

    abstract class DataPresenter {
        abstract fun getVoteResultText(res: AbstractCaseDetails.VoteResult?): String
        abstract fun getVerdictDesc(res: AbstractCaseDetails?): String
        abstract fun shouldPayFine(res: AbstractCaseDetails?): Boolean
    }

    private fun setup() {
        viewModel.setCaseID(args.caseID)

        binding.preview.userLayout.setOnClickListener {
            if (!binding.preview.idCardButton.isVisible) return@setOnClickListener
            showIdCard()
        }

        binding.presenter = object: DataPresenter() {

            override fun getVoteResultText(res: AbstractCaseDetails.VoteResult?): String {
                return res?.displayText(resources).orEmpty()
            }

            override fun getVerdictDesc(res: AbstractCaseDetails?): String {
                res?: return ""
                val plaintiff = res.reporterId==viewModel.user.id
                val defendant = res.userId==viewModel.user.id
                return if(res.caseStatus==ReportCaseStatus.VERDICT) {
                    return if (defendant) {
                        when (res.actionTaken) {
                            CaseVerdictAction.DELETE_AND_FINE -> if (res.reportedContentType == ReportContentType.PODIUM) getString(R.string.case_detail_verdict_podium_desc_guilty_fine, "${res.applicableFee ?: 0.0}") else getString(R.string.case_detail_verdict_desc_guilty_fine, "${res.applicableFee ?: 0.0}")
                            CaseVerdictAction.DELETE_CONTENT -> if (res.reportedContentType == ReportContentType.PODIUM) getString(R.string.case_detail_verdict_desc_guilty_delete_podium_content) else getString(R.string.case_detail_verdict_desc_guilty_delete_content)
                            CaseVerdictAction.ONLY_FINE -> getString(R.string.case_detail_verdict_desc_guilty_user_fine, "${res.applicableFee ?: 0.0}")
                            CaseVerdictAction.BAN_AND_FINE_TO_RESTORE -> getString(R.string.case_detail_verdict_desc_guilty_user_ban_and_fine, "${res.applicableFee ?: 0.0}")
                            CaseVerdictAction.BLACKLIST_AND_FINE -> "" // wont get here hopefully
                            CaseVerdictAction.NO_ACTION -> getString(R.string.case_detail_verdict_desc_not_guilty)
                            CaseVerdictAction.COMPENSATE_USER -> getString(R.string.case_detail_verdict_desc_not_guilty_compensation, "${res.applicableFee ?: 0.0}")
                            CaseVerdictAction.REMOVE_BAN -> getString(R.string.case_detail_verdict_desc_not_guilty)
                            CaseVerdictAction.NO_FINE -> ""
                            null -> ""
                        }
                    } else if (plaintiff) {
                        when (res.actionTaken) {
                            CaseVerdictAction.COMPENSATE_USER -> {
                                if (res.userReport) getString(R.string.case_detail_verdict_desc_not_guilty_user_fine,"${res.applicableFee ?: 0.0}")
                                else getString(R.string.case_detail_verdict_desc_not_guilty_content_fine,"${res.applicableFee ?: 0.0}")
                            }

                            else -> getString(R.string.legal_affairs_final_judgement,res.actionTaken?.displayText(reportContentType = res.reportedContentType))
                        }
                    } else getString(R.string.legal_affairs_final_judgement,res.actionTaken?.displayText(reportContentType = res.reportedContentType))
                } else if (res.caseStatus==ReportCaseStatus.CLOSED) {
                    getString(R.string.legal_affairs_final_judgement,res.actionTaken?.displayText(reportContentType = res.reportedContentType))
                } else ""
            }

            override fun shouldPayFine(res: AbstractCaseDetails?): Boolean {
                res?: return false
                return res.hasFineFor(viewModel.user.id)
            }
        }

        binding.proofExpanded = false

        binding.proofTitleHolder.setOnClickListener {
            binding.proofExpanded = binding.proofExpanded==false
        }

        binding.btnIbGuilty.setOnClickListener {
            ensureInteractionAllowed {
                confirmIBVote(true)
            }
        }
        binding.btnIbNotGuilty.setOnClickListener {
            ensureInteractionAllowed {
                confirmIBVote(false)
            }
        }

        binding.btnHireAdvocate.setOnClickListener {
            ensureInteractionAllowed {
                hireAdvocate()
            }
        }
        
        binding.btnDefend.setOnClickListener {
            ensureInteractionAllowed {
                findNavController().navigateSafe(CaseDetailsBottomSheetDirections.actionCaseDetailBottomSheetToDefendCaseFragment(caseId = args.caseID))
            }
        }

        binding.btnDecline.setOnClickListener {
            dismiss()
        }

        binding.btnIbIgnore.setOnClickListener {
            dismiss()
        }

        binding.btnJuryGuilty.setOnClickListener {
            ensureInteractionAllowed {
                confirmJuryVote(true)
            }
        }
        binding.btnJuryNotGuilty.setOnClickListener {
            ensureInteractionAllowed {
                confirmJuryVote(false)
            }
        }

        binding.btnPayFine.setOnClickListener {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(reportId = args.caseID, fineCategory = null))
        }

        setEmptyView()
    }

    private fun setEmptyView() {
        val errorView: View = binding.multistateview.getView(MultiStateView.ViewState.ERROR) ?: return
        val errorViewBinding = LayoutListStateErrorBinding.bind(errorView)
        errorViewBinding.prepare(
            message = R.string.default_eds_error_message,
            action = R.string.common_retry
        ) {
            viewModel.refreshCaseDetails()
        }
    }

    private fun addObservers() {

        viewModel.caseDetailsViewState.observe(viewLifecycleOwner) {
            binding.multistateview.viewState = it?:MultiStateView.ViewState.CONTENT
        }

        viewModel.caseDetails.observe(viewLifecycleOwner) {
            it?: return@observe
            populateCaseDetails(it)
            userViewModel.setUserId(
                id = it.userId,
                context = UserProfileContext.GENERAL,
                isPopupButtonVisible = false
            )
            userIdCardArgs = PublicUserProfileFragmentArgs(
                id = it.userId,
                context = UserProfileContext.GENERAL,
                popOnAction = false
            )
        }
        viewModel.contentPreview.observe(viewLifecycleOwner) {
            it?: return@observe
            binding.preview.setup(it,it.user?.id==viewModel.user.id)

            binding.preview.previewMedia.apply {
                isClickable = true
                setOnClickListener {
                    @UnstableApi
                    previewMedia(viewModel.caseDetails.value?.mediaMetas?: return@setOnClickListener)
                }
            }
        }

        viewModel.onActionError.observe(viewLifecycleOwner) { errorMessage ->
            showToast(message = errorMessage)
        }

        viewModel.hireAdvocateSuccessfully.observe(viewLifecycleOwner) {
            if (it) showToast(message = getString(R.string.case_detail_hire_advocate_success_message, "${viewModel.caseDetails.value?.advocateFee ?: 0}"))
        }

        viewModel.onActionDone.observe(viewLifecycleOwner) {
            setFragmentResult()
        }

        userViewModel.profile.observe(viewLifecycleOwner) { profile ->
            idCardBinding?.layoutIdCard?.setupIDCard(
                profile = profile,
                context = requireContext(),
                lifecycleOwner = viewLifecycleOwner,
                publicUserProfileArgs = userIdCardArgs,
                viewModel = userViewModel
            )
        }

        viewModel.accountDetails.observe(viewLifecycleOwner) {
            Log.d(TAG, "Coin Balance -> ${it?.currentCoin}")
        }

        viewModel.isJuryVoteCompleted.observe(viewLifecycleOwner) {
            showJuryVoteSuccessAlert()
        }

        setFragmentResultListenerOnActivity(PAY_FINE_REQUEST_KEY) { _, _ ->
            setFragmentResult()
        }

    }

    private fun setFragmentResult() {
        requireActivity().supportFragmentManager.setFragmentResult(
            CASE_DETAIL_REQUEST_KEY, bundleOf()
        )
        dismiss()
    }

    private fun populateCaseDetails(case: CaseDetails) {
        refreshProofs(case.proofFiles)
        case.advocateDefenses.orEmpty().let { ad ->
            if(ad.isEmpty()) {
                binding.auStatus.text = getString(R.string.legal_affairs_no_advocates)
                binding.auStatus.setTextColor(ContextCompat.getColor(requireContext(),R.color.colorError))
            }
            else {
                binding.auStatus.text = getString(R.string.legal_affairs_defended)
                binding.auStatus.setTextColor(ContextCompat.getColor(requireContext(),R.color.colorPass))
            }
            binding.defenseHolder.removeAllViews()
            ad.forEachIndexed { i, defense ->
                val b = DataBindingUtil.inflate<ItemCaseDetailsProofBinding>(layoutInflater,R.layout.item_case_details_proof,binding.defenseHolder,false)
                b.proofName.text = getString(R.string.legal_affairs_petition,(i+1).toString())
                b.fileType.setImageResource(R.drawable.ic_proof_file)
                b.root.setOnClickListener {
                    showDefense(defense = defense, petitionCount = i + 1)
                }
                binding.defenseHolder.addView(b.root)
            }
        }
    }

    private fun refreshProofs(proofs: List<CaseDetails.ProofFile>) {
        binding.proofHolder.removeAllViews()
        proofs.forEachIndexed { i, f ->
            val b = DataBindingUtil.inflate<ItemCaseDetailsProofBinding>(layoutInflater,R.layout.item_case_details_proof,binding.proofHolder,false)
            b.proofName.text = getString(R.string.legal_affairs_proof_file,(i+1).toString())
            b.fileType.setImageResource(
                when (f.mediaType) {
                    MediaType.IMAGE -> R.drawable.ic_attach_gallery
                    MediaType.VIDEO -> R.drawable.ic_attach_video
                    else -> 0
                }
            )
            b.root.setOnClickListener {
                showProof(f)
            }
            binding.proofHolder.addView(b.root)
        }
    }

    private fun showDefense(defense: CaseDetails.AdvocateDefense, petitionCount: Int) {
        val defenseContentBinding = DataBindingUtil.inflate<LayoutDefenseContentViewerBinding>(layoutInflater, R.layout.layout_defense_content_viewer, null, false)

        val defenseProofAdapter = DefenseProofViewPagerAdapter(
            defenseProofFiles = defense.proofFiles,
            onClick = {
                showProof(proof = it ?: return@DefenseProofViewPagerAdapter)
            }
        )

        defenseContentBinding.apply {
            this.petitionCount = petitionCount
            this.defense = defense.defense
            this.isProofAvailable = !defense.proofFiles.isEmpty()
            defenseProofViewPager.adapter = defenseProofAdapter
            dotsIndicator.attachTo(defenseProofViewPager)
            dotsIndicator.isVisible = defense.proofFiles.size > 1
        }

        val alertDialog = MaterialAlertDialogBuilder(requireContext())
            .setView(defenseContentBinding.root)
            .create()
        alertDialog.show()

        defenseContentBinding.actionClose.setOnClickListener {
            alertDialog.dismiss()
        }
    }

    private fun showProof(proof: CaseDetails.ProofFile) {
        val layout = DataBindingUtil.inflate<ItemCaseProofMediaBinding>(layoutInflater,R.layout.item_case_proof_media,null,false).apply {
            imageView.isVisible = false
            playerView.isVisible = false

            when(proof.mediaType) {
                MediaType.IMAGE -> {
                    Log.w(TAG, "showProof: image")
                    imageView.isVisible = true
                    Glide.with(requireContext()).load(proof.url).into(imageView)
                }
                MediaType.VIDEO -> {
                    playerView.isVisible = true
                    setupPlayer()
                    val vPlayer = player ?: return
                    playerView.player = vPlayer
                    vPlayer.setupWithMedia(MediaItem.fromUri(proof.url))
                }
                else -> { }
            }
        }
        Log.w(TAG, "showProof: $proof | ${proof.mediaType}")

        MaterialAlertDialogBuilder(requireContext())
            .setView(layout.root)
            .setPositiveButton(R.string.common_close) { dialog, which ->
                dialog.dismiss()
            }
            .setOnDismissListener {
                stopPlayer()
            }
            .show()
    }

    private fun CaseVerdictAction.displayText(reportContentType: ReportContentType): String = when(this) {
        // Guilty Case
        CaseVerdictAction.DELETE_CONTENT ->
            if (reportContentType == ReportContentType.PODIUM) getString(R.string.case_detail_delete_podium)
            else getString(R.string.case_detail_delete_content)

        CaseVerdictAction.DELETE_AND_FINE -> getString(R.string.case_detail_delete_and_fine)
        CaseVerdictAction.BAN_AND_FINE_TO_RESTORE -> getString(R.string.case_detail_ban_and_fine_to_restore)
        CaseVerdictAction.ONLY_FINE -> getString(R.string.case_detail_only_fine)
        CaseVerdictAction.BLACKLIST_AND_FINE -> getString(R.string.case_detail_black_list_and_fine)

        //Not guilty Case
        CaseVerdictAction.NO_ACTION, CaseVerdictAction.REMOVE_BAN -> getString(R.string.case_detail_only_fine)
        CaseVerdictAction.COMPENSATE_USER -> getString(R.string.case_detail_fine_and_compensate)
        CaseVerdictAction.NO_FINE -> getString(R.string.case_detail_no_action)
    }

    private fun CaseVerdictAction.displayDesc(reportContentType: ReportContentType, reportType: ReportType): String = when(this) {
        // Guilty Case
        CaseVerdictAction.DELETE_CONTENT ->
            if (reportContentType == ReportContentType.PODIUM) getString(R.string.case_detail_delete_podium_description)
            else getString(R.string.case_detail_delete_content_description)

        CaseVerdictAction.DELETE_AND_FINE -> if (reportContentType == ReportContentType.PODIUM) getString(R.string.case_detail_delete_and_fine_podium_description)
        else getString(R.string.case_detail_delete_and_fine_description)

        CaseVerdictAction.BAN_AND_FINE_TO_RESTORE -> getString(R.string.case_detail_ban_and_fine_to_restore_description)
        CaseVerdictAction.ONLY_FINE -> getString(R.string.case_detail_only_fine_description)
        CaseVerdictAction.BLACKLIST_AND_FINE -> getString(R.string.case_detail_black_list_and_fine_description)

        //Not guilty Case
        CaseVerdictAction.NO_ACTION -> getString(
            when(reportContentType) {
                ReportContentType.USER -> R.string.case_detail_not_guilty_report_user_fine_description
                ReportContentType.PODIUM -> R.string.case_detail_not_guilty_report_podium_fine_description
                else -> R.string.case_detail_not_guilty_report_content_fine_description
            }
        )
        CaseVerdictAction.COMPENSATE_USER -> if (reportType == ReportType.BAN) getString(R.string.case_detail_not_guilty_banned_user_compensate_description)
            else getString(
            when(reportContentType) {
                ReportContentType.USER -> R.string.case_detail_not_guilty_report_user_compensate_description
                ReportContentType.PODIUM -> R.string.case_detail_not_guilty_report_podium_compensate_description
                else -> R.string.case_detail_not_guilty_report_content_compensate_description
            }
        )

        CaseVerdictAction.REMOVE_BAN -> getString(R.string.case_detail_not_guilty_banned_user_fine_description)

        CaseVerdictAction.NO_FINE -> if (reportType == ReportType.BAN) getString(R.string.case_detail_not_guilty_banned_user_no_action_description)
        else getString(
            when(reportContentType) {
                ReportContentType.USER -> R.string.case_detail_not_guilty_report_user_no_action_description
                ReportContentType.PODIUM -> R.string.case_detail_not_guilty_podium_no_action_description
                else -> R.string.case_detail_not_guilty_content_no_action_description
            }
        )
    }

    @DrawableRes
    private fun CaseVerdictAction.iconRes(): Int = when(this) {
        CaseVerdictAction.DELETE_AND_FINE,
        CaseVerdictAction.ONLY_FINE,
        CaseVerdictAction.COMPENSATE_USER,
        CaseVerdictAction.NO_ACTION,
        CaseVerdictAction.REMOVE_BAN
            -> R.drawable.ic_cash_outline
        CaseVerdictAction.DELETE_CONTENT -> R.drawable.ic_trash_outline
        CaseVerdictAction.BAN_AND_FINE_TO_RESTORE -> R.drawable.ic_user_ban
        CaseVerdictAction.BLACKLIST_AND_FINE -> R.drawable.ic_file_case_black_list_and_fine
        CaseVerdictAction.NO_FINE -> R.drawable.ic_no_action
    }

    private fun confirmJuryVote(guilty: Boolean) {
        val case = viewModel.caseDetails.value?: return
        val (firstAction: CaseVerdictAction, secondAction: CaseVerdictAction) = when(case.reportedContentType) {
            ReportContentType.USER -> {
                if (case.reportType==ReportType.BAN) {
                    if (guilty) Pair(CaseVerdictAction.BLACKLIST_AND_FINE, CaseVerdictAction.ONLY_FINE)
                    else Pair(CaseVerdictAction.REMOVE_BAN, CaseVerdictAction.COMPENSATE_USER)
                } else {
                    if (guilty) Pair(CaseVerdictAction.BAN_AND_FINE_TO_RESTORE, CaseVerdictAction.ONLY_FINE)
                    else Pair(CaseVerdictAction.NO_ACTION, CaseVerdictAction.COMPENSATE_USER)
                }
            }
            else -> {
                if (guilty) Pair(CaseVerdictAction.DELETE_CONTENT, CaseVerdictAction.DELETE_AND_FINE)
                else Pair(CaseVerdictAction.NO_ACTION, CaseVerdictAction.COMPENSATE_USER)
            }
        }

        val guiltyBinding = ItemGuiltyAlertViewBinding.inflate(LayoutInflater.from(requireContext()))

        guiltyBinding.isNotGuilty = !guilty
        guiltyBinding.noActionMessage.text = CaseVerdictAction.NO_FINE.displayDesc(reportContentType = case.reportedContentType, reportType = case.reportType)

        guiltyBinding.titleFirst.text = firstAction.displayText(reportContentType = case.reportedContentType)
        guiltyBinding.btnFirst.text = firstAction.displayText(reportContentType = case.reportedContentType)
        guiltyBinding.messageFirst.text = firstAction.displayDesc(reportContentType = case.reportedContentType, reportType = case.reportType)
        guiltyBinding.icFirst.setImageResource(firstAction.iconRes())

        guiltyBinding.titleSecond.text = secondAction.displayText(reportContentType = case.reportedContentType)
        guiltyBinding.btnSecond.text = secondAction.displayText(reportContentType = case.reportedContentType)
        guiltyBinding.messageSecond.text = secondAction.displayDesc(reportContentType = case.reportedContentType, reportType = case.reportType)
        guiltyBinding.icSecond.setImageResource(secondAction.iconRes())

        val materialAlertDialog = MaterialAlertDialogBuilder(requireContext(), R.style.TransparentMaterialAlertDialog)
            .setView(guiltyBinding.root)
            .create()
        materialAlertDialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        materialAlertDialog.show()

        guiltyBinding.actionClose.setOnClickListener {
            materialAlertDialog.dismiss()
        }

        guiltyBinding.btnNotGuiltyNoAction.setOnClickListener {
            viewModel.voteJury(
                guilty = guilty,
                action = CaseVerdictAction.NO_FINE,
                onSuccess = { materialAlertDialog.dismiss() }
            )
        }

        guiltyBinding.btnFirst.setOnClickListener {
            viewModel.voteJury(
                guilty = guilty,
                action = firstAction,
                onSuccess = { materialAlertDialog.dismiss() }
            )
        }

        guiltyBinding.btnSecond.setOnClickListener {
            viewModel.voteJury(
                guilty = guilty,
                action = secondAction,
                onSuccess = { materialAlertDialog.dismiss() }
            )
        }
    }

    private fun confirmIBVote(guilty: Boolean) {
        confirmAction(
            message = R.string.legal_affairs_ib_vote_alert_message
        ) {
            viewModel.voteIB(guilty)
        }
    }

    private fun hireAdvocate() {
        val isUserHaveInsufficientCoinBalance = (viewModel.accountDetails.value?.currentCoin ?: 0.0) < (viewModel.caseDetails.value?.advocateFee ?: 0).toDouble()
        if (isUserHaveInsufficientCoinBalance) {
            confirmAction(
                message = getString(R.string.legal_affairs_pay_advocate_fee_insufficient_alert),
                positiveTitle = R.string.title_buy_coins,
                negativeTitle = R.string.common_close
            ) {
               findNavController().navigateSafe(
                   CaseDetailsBottomSheetDirections.actionGlobalBuyflaxFragment(
                       hideActionBar = false, isBuyCoin = true
                   )
               )
            }
        } else {
            confirmAction(
                message = getString(R.string.legal_affairs_pay_advocate_alert_message, "${viewModel.caseDetails.value?.advocateFee}")
            ) {
                viewModel.hireAdvocate(reportId = args.caseID)
            }
        }
    }

    private fun showJuryVoteSuccessAlert() {
        showAlertWithSingleButton(
            message = R.string.legal_affairs_guilty_not_guilty_success_message,
            isButtonTextRedColor = true,
            isCancelable = false,
            onClick = { viewModel.onActionDone.postValue(true) }
        )
    }

    companion object {
        const val TAG = "CDBS"
        const val CASE_DETAIL_REQUEST_KEY = "case_detail_request"

        @JvmStatic
        @BindingAdapter("timelineState","timelineCurrent")
        fun setGoneFromBoolean(view: AppCompatImageView, status: ReportCaseStatus, current: ReportCaseStatus?) {
            view.setImageResource(if (current==status) R.drawable.ic_case_timeline_dot_active else R.drawable.ic_case_timeline_dot)
        }
    }

    override fun onPause() {
        super.onPause()
        stopPlayer()
    }
    private var futurePlaybackObject: WeakReference<FuturePlaybackObject>? = null

    private var mediaTicker: Job? = null

    private fun stopTicker() {
        mediaTicker?.cancel()
        mediaTicker = null
    }

    private fun startTicker(tick: (() -> Unit)? = null) {
        Log.d("TIMER", "startTicker")
        mediaTicker?.cancel()
        mediaTicker = lifecycleScope.launch(Dispatchers.Main) {
            while (isActive) {
                tick?.invoke()
                delay(200)
            }
        }
    }

    @UnstableApi
    private fun previewAudio(item: PostatMedia) {
        val layout = DataBindingUtil.inflate<ItemReportMediaAudioBinding>(layoutInflater,R.layout.item_report_media_audio,null,false)
        val mediaMeta = item.asMediaMeta
        val playerInfo = MediaPlayerInfo("0",item.s3Key.orEmpty(),mediaMeta,0)

        setupPlayer()
        val audioPlayer = player?: return
        layout.apply {
            info = playerInfo
            mediaSlider.clearOnSliderTouchListeners()

            audioPlayButton.setOnClickListener {
                if (audioPlayer.isPlaying) audioPlayer.pause()
                else audioPlayer.play()
            }
            // AAC files currently do not fully support seeking: https://developer.android.com/media/media3/exoplayer/progressive
            mediaSlider.addOnSliderTouchListener(object : Slider.OnSliderTouchListener {
                override fun onStartTrackingTouch(slider: Slider) {}
                override fun onStopTrackingTouch(slider: Slider) {
                    Log.w(TAG, "onStopTrackingTouch: seeking to ${slider.value.toLong()} * 1000")
                    audioPlayer.seekTo((slider.value * 1000L).toLong())
                    playerInfo.progress = slider.value
                }
            })
        }

        MaterialAlertDialogBuilder(requireContext())
            .setView(layout.root)
            .setPositiveButton(R.string.common_close) { dialog, which ->
                dialog.dismiss()
            }
            .setOnDismissListener {
                stopPlayer()
            }
            .show()

        audioPlayer.addListener(object: Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                Log.d(TAG, "onIsPlayingChanged: $isPlaying")
                playerInfo.state = if (isPlaying) {
                    startTicker {
                        try {
                            playerInfo.progress = audioPlayer.currentPosition.div(1000F)
                        } catch (_: Exception) {
                            stopTicker()
                        }
                    }
                    MediaPlayerInfo.MediaState.PLAYING
                } else {
                    stopTicker()
                    MediaPlayerInfo.MediaState.PAUSED
                }
            }
        })

        val media = MediaItem.fromUri(item.mediaUrl?: return)
        val dataSourceFactory: DataSource.Factory = DefaultHttpDataSource.Factory()
        val extractorsFactory = DefaultExtractorsFactory().setConstantBitrateSeekingEnabled(true)
        // Create a progressive media source pointing to a stream uri.
        val mediaSource: MediaSource = ProgressiveMediaSource.Factory(dataSourceFactory,extractorsFactory)
                .createMediaSource(media)
        audioPlayer.setupWithMedia(mediaSource)

    }
    @UnstableApi
    private fun previewMedia(items: List<PostatMedia>) {

        if (items.size==1 && items[0].mediaType==MediaType.AUDIO) {
            previewAudio(items[0])
            return
        }

        val layout = DataBindingUtil.inflate<LayoutReportContentViewerBinding>(layoutInflater,R.layout.layout_report_content_viewer,null,false)

        fun playMediaIfReady() {
            setupPlayer()
            val videoPlayer = player?: return
            fun play(fpo: FuturePlaybackObject) {
                val media = MediaItem.fromUri(fpo.media.mediaUrl?: return)
                videoPlayer.setupWithMedia(media)
                fpo.onPlay.invoke(videoPlayer)
            }
            var fpo = futurePlaybackObject?.get()
            if (fpo==null || fpo.pos!=layout.mediaViewPager.currentItem) {
                Log.d(TAG, "playMediaIfReady: trying to find current FPO")
                val vh = (layout.mediaViewPager[0] as RecyclerView).findViewHolderForAdapterPosition(layout.mediaViewPager.currentItem)
//                Log.w(TAG, "playMediaIfReady: found ViewHolder for pos ${binding.mediaViewPager.currentItem}")
                fpo = (vh as? CaseDetailsPreviewViewPagerAdapter.MediaPagerViewHolder)?.bindPlayer()
                Log.d(TAG, "fpo is $fpo")
            }
            fpo?: return
            Log.w(TAG, "playMediaIfReady: found FPO: ${fpo.pos}")
            play(fpo)
        }

        layout.dotsIndicator.isVisible = items.size > 1

        val firstAspect = items.firstOrNull()?.resolution?.aspectRatio?: POSTAT_ASPECT_DEFAULT

        val coercedAspect = firstAspect.coerceIn(POSTAT_ASPECT_MIN, POSTAT_ASPECT_MAX)
        (layout.mediaHolder.layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = (coercedAspect).toString()

        val previewAdapter = CaseDetailsPreviewViewPagerAdapter(object : CaseDetailsPreviewViewPagerAdapter.PlayerActionListener {
            override fun registerForFuturePlayback(obj: FuturePlaybackObject) {
                if (futurePlaybackObject?.get()?.pos==obj.pos) return
                futurePlaybackObject = WeakReference(obj)
                Log.w(TAG, "registerForFuturePlayback: pos: ${obj.pos} | current: ${layout.mediaViewPager.currentItem}")
                if (obj.pos==layout.mediaViewPager.currentItem) playMediaIfReady()
            }

            override fun detachFuturePlayback(pos: Int) {
                if (futurePlaybackObject?.get()?.pos == pos) {
                    Log.w(TAG, "detachFuturePlayback: $pos")
                    futurePlaybackObject = null
                }
            }
        }, items.toMutableList())

        layout.mediaViewPager.apply {
            adapter = previewAdapter
            layout.dotsIndicator.attachTo(this)

            registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    Log.w(TAG, "onPageSelected: $position")
                    super.onPageSelected(position)
                    futurePlaybackObject?.get().let { fpo ->
                        if (fpo != null) {
                            if (fpo.pos != position) {
                                Log.w(TAG, "onPageSelected: stopping ${fpo.pos}")
                                stopPlayer()
                                fpo.onStop.invoke()
                                futurePlaybackObject = null
                            }
                        } else stopPlayer()
                    }
                    playMediaIfReady()
                }
            })
        }

        MaterialAlertDialogBuilder(requireContext())
            .setView(layout.root)
            .setPositiveButton(R.string.common_close) { dialog, which ->
                dialog.dismiss()
            }
            .setOnDismissListener {
                stopPlayer()
                futurePlaybackObject = null
            }
            .show()
    }

    private fun stopMediaPlayback() {
        futurePlaybackObject?.get()?.let { fpo ->
            Log.d(TAG, "stopMediaPlayback: found FPO: ${fpo.pos}")
            fpo.onStop.invoke()
        }
    }

    private fun showIdCard() {
        idCardBinding = ItemCaseDetailIdCardViewBinding.inflate(LayoutInflater.from(requireContext()))

        idCardBinding?.layoutIdCard?.setupIDCard(
            profile = userViewModel.profile.value,
            context = requireContext(),
            lifecycleOwner = viewLifecycleOwner,
            publicUserProfileArgs = userIdCardArgs,
            viewModel = userViewModel
        )

        val materialAlertDialog = MaterialAlertDialogBuilder(requireContext(), R.style.TransparentMaterialAlertDialog)
            .setView(idCardBinding?.root)
            .create()
        materialAlertDialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        materialAlertDialog.show()

        idCardBinding?.actionClose?.setOnClickListener {
            materialAlertDialog.dismiss()
        }
    }

    private var player: ExoPlayer? = null

    private fun releasePlayer() {
        stopMediaPlayback()
        futurePlaybackObject = null
        player?.apply {
            stop()
            release()
            player = null
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer() {
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build()
        }
        player?.apply {
            repeatMode = Player.REPEAT_MODE_ONE
            playWhenReady = true
            prepare()
        }
    }

    private fun stopPlayer() {
        player?.stop()
    }

    @OptIn(UnstableApi::class)
    fun ExoPlayer.setupWithMedia(source: MediaSource) {
        Log.w(TAG, "setupPlayerWithMedia")
        clearMediaItems()
        setMediaSource(source)
        prepare()
        play()
    }

    @OptIn(UnstableApi::class)
    fun ExoPlayer.setupWithMedia(mediaItem: MediaItem) {
        Log.w(TAG, "setupPlayerWithMedia")
        clearMediaItems()
        setMediaItem(mediaItem)
        prepare()
        play()
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }
}