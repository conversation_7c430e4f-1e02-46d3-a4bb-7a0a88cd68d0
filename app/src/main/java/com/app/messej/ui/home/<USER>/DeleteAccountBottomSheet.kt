package com.app.messej.ui.home.deleteaccount

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.R
import com.app.messej.databinding.FragmentDeleteBottomSheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.tabs.TabLayoutMediator

class DeleteAccountBottomSheet: BottomSheetDialogFragment(),DeleteAccountEmailFragment.AccountDeletedListener,DeleteAccountMobileFragment.AccountDeletedListener {
    private var deleteAccountAdapter: FragmentStateAdapter?=null
    lateinit  var binding:FragmentDeleteBottomSheetBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_delete_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    override fun onAccountDeleted() {
    }

    override fun onRequestBottomSheetDismiss() {
        dismiss()  // Dismiss the bottom sheet fragment
    }

    private fun setup() {
        deleteAccountAdapter = object : FragmentStateAdapter(this) {

            override fun getItemCount(): Int = 2
            override fun createFragment(position: Int): Fragment {
                // Return a NEW fragment instance in createFragment(int)
                val fragment = when (position) {
                    0 -> DeleteAccountMobileFragment.newInstance()
                    1 -> DeleteAccountEmailFragment.newInstance()
                    else -> throw java.lang.IllegalArgumentException("There should only be 2 tabs")
                }
                return fragment
            }
        }

        binding.deleteAccountPager.adapter = deleteAccountAdapter
        binding.deleteAccountPager.isUserInputEnabled = true
        TabLayoutMediator(binding.deleteAccountTab, binding.deleteAccountPager) { tab, position ->
            when(position) {
                0 -> tab.text = resources.getString(R.string.forgot_password_mode_mobile)
                1 -> tab.text = resources.getString(R.string.forgot_password_mode_email)
            }
        }.attach()

    }

}