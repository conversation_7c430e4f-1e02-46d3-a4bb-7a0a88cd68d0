package com.app.messej.ui.home.businesstab

import android.app.Application
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.FieldData
import com.app.messej.data.model.PaymentRequest
import com.app.messej.data.model.api.PaymentMethodFormResponse
import com.app.messej.data.model.api.PaymentMethodResponse
import com.app.messej.data.model.api.business.FinalPaymentResponse
import com.app.messej.data.model.api.business.InputData
import com.app.messej.data.model.api.business.SubmittedPaymentFormResponse
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.model.enums.InputType
import com.app.messej.data.model.enums.PayoutFormErrorType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil.containsSpecialCharacters
import com.app.messej.ui.home.publictab.common.BaseProfilePicAttachViewModel
import com.app.messej.ui.utils.LocaleUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PaymentMethodViewModel (application: Application): BaseProfilePicAttachViewModel(application) {
    private var businessRepo = BusinessRepository(application)
    private val podiumRepo = PodiumRepository(getApplication())

    // Add user property to access current user data
    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    // Check if user is Egyptian
    val isEgyptianUser: Boolean get() = user.isEgyptianUser

    private val _paymentMethods = MutableLiveData<List<PaymentMethodResponse.PaymentMethod>>()
    val paymentMethods: LiveData<List<PaymentMethodResponse.PaymentMethod>> = _paymentMethods

    private val _fieldDataList = mutableListOf<FieldData>()

    private val _paymentMethodForm = MutableLiveData<PaymentMethodFormResponse?>()
    val paymentMethodFormLiveData: LiveData<PaymentMethodFormResponse?> = _paymentMethodForm

    //live data for prefilled data
    private val _prefillData = MutableLiveData<List<InputData>>()
    val prefillData: LiveData<List<InputData>> = _prefillData

    val paymentMethodSubmitSuccess = LiveEvent<Boolean>()

    //live event for prefilled data
    private val prefillDataEvent = LiveEvent<Boolean>()

    //live data for payment method submission loader
    private val _paymentMethodSubmissionLoader = MutableLiveData<Boolean>(false)
    val paymentMethodSubmissionLoader: LiveData<Boolean> = _paymentMethodSubmissionLoader



    private val _errorLiveData = MutableLiveData<String>()
    val errorLiveData: LiveData<String> = _errorLiveData

    val passportImageErrorFlag = MutableLiveData<Boolean>(false)

    var isDataPersist = MutableLiveData<Boolean>(false)

    private val _paymentMethodDataLoader = MutableLiveData(false)
    val paymentMethodDataLoader: LiveData<Boolean> = _paymentMethodDataLoader

    val phoneNumberError = MutableLiveData<String>()
    val phoneNumber = MutableLiveData<String?>(null)

    private val _dataLoading = MutableLiveData(false)
    val dataLoading: LiveData<Boolean> = _dataLoading

    val phoneCountryCode = MutableLiveData<String?>(null)

    private var _isPhoneValid = MutableLiveData<Boolean?>(null)
    val isPhoneValid: LiveData<Boolean?>
        get() = _isPhoneValid

    fun setPhoneCountryCode(selectedCountryCode: String?) {
        phoneCountryCode.postValue(selectedCountryCode)
    }

    fun setPhoneNumberValid(valid: Boolean) {
        _isPhoneValid.postValue(valid)
    }




    val validationResult = LiveEvent<Pair<Int, PayoutFormErrorType?>>()

    private val _corruptedPaymentMethodLoader = MutableLiveData<Boolean>(false)
    val corruptedPaymentMethodLoader: LiveData<Boolean> = _corruptedPaymentMethodLoader

    private val _corruptedPaymentMethodFormLiveData: MediatorLiveData<PaymentMethodFormResponse> by lazy {
        val med = MediatorLiveData<PaymentMethodFormResponse>()
        _corruptedPaymentMethodLoader.postValue(true)

fun update() {
    val paymentMethodForm = _paymentMethodForm.value
    val prefillData = _prefillData.value

    if (paymentMethodForm != null && prefillData != null) {
        paymentMethodForm.inputs.forEach { input ->
            prefillData.find { it.payment_form_id == input.id }?.let { prefill ->
                input.inputData = prefill.input_data
                updateFormDataField(prefill.payment_form_id, prefill.input_data)
            }
        }
        _corruptedPaymentMethodLoader.postValue(false)
        med.postValue(paymentMethodForm)
    }
}
        // Observe both sources
        med.addSource(_paymentMethodForm) { update() }
        med.addSource(_prefillData) { update() }

        med
    }


    val corruptedPaymentMethodFormLiveData: LiveData<PaymentMethodFormResponse> = _corruptedPaymentMethodFormLiveData



    fun validateFields() : Boolean {


        var isValid = true


        _fieldDataList.forEach { item ->
            when (item.inputType) {
                InputType.TEXT -> {
                    if (item.value.isNullOrEmpty()) {
                        isValid = false
                        validationResult.value = Pair(item.id, PayoutFormErrorType.REQUIRED)
                        return@forEach
                    } else if (item.value?.containsSpecialCharacters() == true && LocaleUtil.getAppLocale().apiCode == AppLocale.ENGLISH.apiCode){ isValid = false
                        validationResult.value = Pair(item.id, PayoutFormErrorType.SPECIAL_CHARACTERS)
                        return@forEach
                    }
                }

                InputType.DROP_DOWN -> {
                    if (item.value.isNullOrEmpty()) {
                        isValid = false
                        validationResult.value = Pair(item.id, PayoutFormErrorType.SELECT_OPTION)
                        return@forEach
                    }
                }

                InputType.PHONE -> {
                    if (item.value.isNullOrEmpty()) {
                        isValid = false
                        validationResult.value = Pair(item.id, PayoutFormErrorType.REQUIRED)
                        return@forEach
                    }
                    else if (isPhoneValid.value == false){
                        isValid = false
                        validationResult.value = Pair(item.id, PayoutFormErrorType.INVALID_PHONE_NUMBER)
                        return@forEach

                    }

                }
                InputType.FILE -> {
                    if (finalImagePath.value.isNullOrEmpty()) {
                        isValid = false
                        validationResult.value = Pair(item.id, null)
                        return@forEach
                    }
                }


                else -> {
                    // Handle unknown input types
                }
            }
        }

        return isValid
    }

    override fun addCroppedImage(uri: Uri) {
        super.addCroppedImage(uri)
    }

    fun updateFormDataField(fieldId: Int, value: String) {
        _fieldDataList.find { it.id == fieldId }?.let {
            it.value = value
        }
    }


    private fun initializeFieldData(inputs: List<PaymentMethodFormResponse.Input>) {
        _fieldDataList.clear()
        for (input in inputs) {
            try{
                val fieldData = FieldData(input.id, input.inputType)
                _fieldDataList.add(fieldData)
            }catch (e: Exception) {
            }
        }
    }





    fun getPaymentMethodForm(paymentMethodId: Int?) {
        paymentMethodId?: return
        viewModelScope.launch(Dispatchers.IO) {
            _paymentMethodDataLoader.postValue(true)
            val methodId = paymentMethodId.toString()
            when (val result: ResultOf<PaymentMethodFormResponse> = businessRepo.getPaymentMethodForm(methodId)) {
                is ResultOf.Success -> {
                    _paymentMethodForm.postValue(result.value)
                    initializeFieldData(result.value.inputs)
                    _paymentMethodDataLoader.postValue(false)
                    prefillDataEvent.postValue(true)

                }

                is ResultOf.APIError -> {
                    _paymentMethodDataLoader.postValue(false)
                }

                is ResultOf.Error -> {
                    _paymentMethodDataLoader.postValue(false)
                }
            }
            _paymentMethodDataLoader.postValue(false)

        }
    }


    private val _paymentFormResponse = MutableLiveData<FinalPaymentResponse>()
    val paymentFormResponse: LiveData<FinalPaymentResponse> = _paymentFormResponse

    fun submitPaymentMethodForm(payoutId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            _paymentMethodSubmissionLoader.postValue(true)
            val compressed = finalImage?.let { podiumRepo.compressImage(it) }
            val fileId = _paymentMethodForm.value?.inputs?.find {
                it.inputType == InputType.FILE
            }
            val paymentMethodId = _paymentMethodForm.value?.paymentMethodId
            val isPersist = isDataPersist.value?: false
            val list: List<PaymentRequest.Input>
            _fieldDataList.removeIf { it.id == fileId?.id }
            //remove file id from list
            if (compressed != null){
                list = convertMapToList(_fieldDataList,null)}
            else {
                 list = convertMapToList(_fieldDataList,fileId)
            }

            when (val result: ResultOf<FinalPaymentResponse> = businessRepo.submitPaymentMethodForm(compressed, list, paymentMethodId, isPersist, fileId?.id, payoutId)) {
                is ResultOf.Success -> {
                    _paymentFormResponse.postValue(result.value)
                    paymentMethodSubmitSuccess.postValue(true)
                    _paymentMethodSubmissionLoader.postValue(false)
                }

                is ResultOf.APIError -> {
                    _paymentMethodSubmissionLoader.postValue(false)
                    _errorLiveData.postValue(result.error.message)
                }

                is ResultOf.Error -> {
                    _paymentMethodSubmissionLoader.postValue(false)
                }
            }
            _paymentMethodSubmissionLoader.postValue(false)
        }
    }

    // get already submitted payment method form
    fun getPaymentMethodFormData(paymentMethodId: Int?) {
        paymentMethodId?: return
        viewModelScope.launch(Dispatchers.IO) {
            _paymentMethodDataLoader.postValue(true)
            val methodId = paymentMethodId.toString()
            when (val result: ResultOf<SubmittedPaymentFormResponse> = businessRepo.getPaymentMethodDetails(methodId)) {
                is ResultOf.Success -> {
                    _prefillData.postValue(result.value.input_data)
                    _paymentMethodDataLoader.postValue(false)

                }

                is ResultOf.APIError -> {
                    _paymentMethodDataLoader.postValue(false)
                }

                is ResultOf.Error -> {
                    _paymentMethodDataLoader.postValue(false)
                }
            }

        }
    }

    private fun convertMapToList(fieldMap: MutableList<FieldData>, fileId: PaymentMethodFormResponse.Input?): List<PaymentRequest.Input> {
        val inputList = fieldMap.map {
            PaymentRequest.Input(inputData = it.value?:"", paymentFormId = it.id)
        }.toMutableList() ?: mutableListOf()

        fileId?.let {
            inputList.add(PaymentRequest.Input(inputData = it.inputData.toString(), paymentFormId = it.id))
        }
        return inputList
    }

    fun updateImageLiveData(image: String) {
        finalImagePath.postValue(image)
    }
}