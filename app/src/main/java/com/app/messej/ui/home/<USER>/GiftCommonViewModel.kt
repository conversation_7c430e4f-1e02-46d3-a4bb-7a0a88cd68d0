package com.app.messej.ui.home.gift

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.gift.GiftResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.GiftTabs
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.GiftRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class GiftCommonViewModel(application: Application) : AndroidViewModel(application) {

    private val giftRepo = GiftRepository(application)
    private val accountRepo: AccountRepository = AccountRepository(application)

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    val isUserPremium = accountDetails.map {
        it ?: return@map false
        it.isPremium
    }.distinctUntilChanged()
    val user: CurrentUser get() = accountRepo.user

    private val _currentTab = MutableLiveData<GiftTabs?>(null)
    val currentTab: LiveData<GiftTabs?> = _currentTab.distinctUntilChanged()

    private val _giftListData = MutableLiveData<GiftResponse?>()
    val giftListData: LiveData<GiftResponse?> = _giftListData


    private val _processWait = MutableLiveData(false)
    val processWait: LiveData<Boolean?> = _processWait

    val _purchaseHistoryLoading = MutableLiveData(false)
    val purchaseHistoryLoading: LiveData<Boolean> = _purchaseHistoryLoading

    var isPremiumUser = if(accountRepo.loggedIn) accountRepo.isPremiumUser else null
        private set
    var isResidentUser = if(accountRepo.loggedIn) accountRepo.isResident else null
    private set

    var isVisitor = if(accountRepo.loggedIn) accountRepo.isVisitor else null
    private set

    fun setCurrentTab(tab: GiftTabs, skipIfSet: Boolean = false) {
        Log.w("PHVM", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
    }

    fun setRestricted(isRestricted: Boolean) {
        _processWait.postValue(isRestricted)

    }
    fun getGiftList() {
        _purchaseHistoryLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = giftRepo.getGiftPurchaseList()) {
                is ResultOf.Success -> {
                    _giftListData.postValue(result.value)
                    _purchaseHistoryLoading.postValue(false)
                }
                is ResultOf.APIError -> {
                    _purchaseHistoryLoading.postValue(false)
                }
                is ResultOf.Error -> {

                }
            }
        }
    }


}