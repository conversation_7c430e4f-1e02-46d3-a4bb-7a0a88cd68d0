package com.app.messej.ui.auth.profile

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.databinding.LayoutRegisterSuperstarFilterGenderBinding
import com.app.messej.ui.common.GenderQuickAdapter
import com.chad.library.adapter.base.animation.SlideInBottomAnimation

class RegisterSuperstarFilterGenderFragment : Fragment() {

    private lateinit var binding: LayoutRegisterSuperstarFilterGenderBinding

    private val viewModel: RegisterSuperstarViewModel by navGraphViewModels(R.id.navigation_register_superstar)

    private var mAdapter: GenderQuickAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_register_superstar_filter_gender, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun observe() {
        viewModel.temporaryGenderSelection.observe(viewLifecycleOwner) { sel ->
            mAdapter?.let { adapter ->
                val data = adapter.data
                data.forEach { dat->
                    dat.selected = sel.find { it.code == dat.code }!=null
                }
                adapter.notifyDataSetChanged()
            }
        }
    }

    private fun initAdapter() {

        if(mAdapter != null) {
            return
        }

        mAdapter = GenderQuickAdapter()

        binding.genderList.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(true)
            adapter = mAdapter
        }
        mAdapter!!.apply {
            animationEnable = true
            adapterAnimation = SlideInBottomAnimation()
            isAnimationFirstOnly = true

            setOnItemClickListener { _, _, position ->
                val gender = mAdapter?.data?.get(position)?:return@setOnItemClickListener
//                gender.selected = !gender.selected
//                mAdapter?.notifyItemChanged(position)
                viewModel.toggleGenderSelection(gender)
            }
        }
    }
}