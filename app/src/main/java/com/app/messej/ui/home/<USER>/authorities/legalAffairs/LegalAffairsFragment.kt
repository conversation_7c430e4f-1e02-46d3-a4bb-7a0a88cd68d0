package com.app.messej.ui.home.publictab.authorities.legalAffairs

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight.Companion.W500
import androidx.compose.ui.text.font.FontWeight.Companion.W700
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.AdvocatesUnionFilter
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.databinding.FragmentLegalAffairsPremiumBinding
import com.app.messej.ui.home.publictab.authorities.legalAffairs.advocatesUnionAndJury.AdvocatesUnionAndJuryFragment
import com.app.messej.ui.home.publictab.authorities.legalAffairs.investigationBureau.InvestigationFragment
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import java.util.Locale

class LegalAffairsFragment : Fragment(), MenuProvider {

    private lateinit var binding:FragmentLegalAffairsPremiumBinding
    val viewModel: LegalAffairsCommonViewModel by activityViewModels()
    private val args: LegalAffairsFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_legal_affairs_premium, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addAsMenuHost()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this, customBackButton = true)
                if (!viewModel.user.premiumUser) {
                    navigationIcon?.setTint(android.graphics.Color.WHITE)
                }
                setNavigationOnClickListener {
                    onBackPress()
                }
            }
        }
        handleOnBackPressed()
    }

    private fun setup() {
        viewModel.apply {
            setLegalAffairsMainTab(tab = args.defaultMainTab)
            setViolationSubTab(tab = args.defaultViolationSubTab)
            if (args.defaultMyLegalRecordTab != LegalAffairTabs.Violations) {
                setLegalAffairSubTab(tab = args.defaultMyLegalRecordTab)
            }
        }
        if (viewModel.user.premiumUser) { setTopHangingItems() }
        setupClickListeners()
        observe()
        binding.gradientArrayId = R.array.authorities_gradient_colors
        binding.isPremiumUser = viewModel.user.premiumUser
    }

    private fun setupClickListeners() {
        binding.apply {
            layoutFileCase.cardView.setOnClickListener {
                findNavController().navigateSafe(
                    LegalAffairsFragmentDirections.actionLegalAffairsFragmentToLegalAffairsFileCaseFragment()
                )
            }
        }
    }

    private fun setTopHangingItems() {
        binding.composeViewHangItems.setContent {
            val tab = viewModel.legalAffairsMainTab.observeAsState().value ?: LegalAffairsMainTab.MyLegalRecords
            val items = viewModel.bannersList.collectAsStateWithLifecycle(emptyList())
            LegalAffairsHangItems(
                tab = tab,
                onItemClick = viewModel::setLegalAffairsMainTab,
                hangItems = items.value
            )
        }
    }

    private fun observe() {
        viewModel.legalAffairsMainTab.observe(viewLifecycleOwner) { tab ->
            replaceFrameLayout(currentTab = tab)
            setToolBarTitle(currentTab = tab)
        }
    }

    private fun handleOnBackPressed() {
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                onBackPress()
            }
        })
    }

    private fun onBackPress() {
        when(viewModel.legalAffairsMainTab.value) {
            LegalAffairsMainTab.MyLegalRecords -> { findNavController().navigateUp() }
            else -> viewModel.setLegalAffairsMainTab(LegalAffairsMainTab.MyLegalRecords)
        }
    }

    private fun replaceFrameLayout(currentTab : LegalAffairsMainTab) {
        val fragment = when(currentTab) {
            LegalAffairsMainTab.MyLegalRecords -> { LegalAffairsMainBottomViewFragment() }
            LegalAffairsMainTab.InvestigationBureau -> { InvestigationFragment() }
            LegalAffairsMainTab.AdvocatesUnion -> {
                AdvocatesUnionAndJuryFragment().apply {
                    arguments = AdvocatesUnionAndJuryFragment.setArguments(
                        isAdvocatesUnionView = true,
                        isAdvocateUnionDefendView = args.defaultAdvocateFilter == AdvocatesUnionFilter.Defended
                    )
                }
            }
            LegalAffairsMainTab.Jury -> {
                AdvocatesUnionAndJuryFragment().apply {
                    arguments = AdvocatesUnionAndJuryFragment.setArguments(
                        isAdvocatesUnionView = false,
                        //Navigate to Jury closed screen when the argument from notification is jury
                        isJuryClosedCaseView = args.defaultMainTab == LegalAffairsMainTab.Jury
                    )
                }
            }
        }

        childFragmentManager
            .beginTransaction()
            .replace(R.id.frame_layout__bottom_view_legal_affairs, fragment)
            .commit()
    }

    private fun setToolBarTitle(currentTab : LegalAffairsMainTab) {
        binding.customActionBar.toolBarTitle.text = if (!viewModel.user.premiumUser) getString(R.string.legal_affairs)
        else when(currentTab) {
            LegalAffairsMainTab.MyLegalRecords -> getString(R.string.legal_affairs)
            LegalAffairsMainTab.InvestigationBureau -> getString(R.string.legal_affairs_investigation_bureau)
            LegalAffairsMainTab.AdvocatesUnion -> getString(R.string.legal_affairs_advocates_union_title)
            LegalAffairsMainTab.Jury -> getString(R.string.legal_affairs_jury)
        }
//        val actionBar = (activity as MainActivity?)?.supportActionBar
//        actionBar?.title = if (!args.isPremiumUser) getString(R.string.legal_affairs)
//        else when(currentTab) {
//            LegalAffairsMainTab.MainView -> getString(R.string.legal_affairs)
//            LegalAffairsMainTab.InvestigationBureau -> getString(R.string.legal_affairs_investigation_bureau)
//            LegalAffairsMainTab.AdvocatesUnion -> getString(R.string.legal_affairs_advocates_union_title)
//            LegalAffairsMainTab.Jury -> getString(R.string.legal_affairs_jury)
//        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.meu_legal_affairs_about, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.legal_affairs_about -> {
                findNavController().navigateSafe(
                    LegalAffairsFragmentDirections.actionGlobalPolicyFragment(
                        documentType = DocumentType.LEGAL_AFFAIRS_ABOUT,
                        isButtonVisible = false
                    )
                )
            }
        }
        return true
    }
}

@Composable
fun LegalAffairsHangItems(
    tab: LegalAffairsMainTab,
    onItemClick: (tab: LegalAffairsMainTab) -> Unit,
    hangItems:List<LegalAffairsMainTab>
) {
    Row(
        modifier = Modifier
            .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(space = 13.dp)
    ) {
        repeat(times = hangItems.size) { position ->
            val item = hangItems.get(index = position)
            val isSelected = when {
                position == 0 && tab == LegalAffairsMainTab.InvestigationBureau -> true
                position == 1 && tab == LegalAffairsMainTab.AdvocatesUnion -> true
                position == 2 && tab == LegalAffairsMainTab.Jury -> true
                tab == LegalAffairsMainTab.MyLegalRecords -> null
                else -> false
            }
            val targetValue = when(isSelected) {
                true -> 1.1F // Current board is selected
                false -> 0.9F // Current board is not selected
                null -> 1F // All boards are not selected
            }
            val animatedScaleValue by animateFloatAsState(
                targetValue = targetValue,
                animationSpec = tween(durationMillis = 400)
            )
            HangItem(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(weight = 1F)
                    .scale(scale = animatedScaleValue)
                    .padding(horizontal = if (hangItems.size == 2) 24.dp else 0.dp),
//                    .weight(weight = animatedWeight),
                isSelected  = isSelected,
                title = when(item) {
                    LegalAffairsMainTab.InvestigationBureau -> R.string.legal_affairs_investigation_bureau
                    LegalAffairsMainTab.AdvocatesUnion -> R.string.legal_affairs_advocates_union
                    else -> R.string.legal_affairs_jury },
                icon = when(item) {
                    LegalAffairsMainTab.InvestigationBureau -> R.drawable.ic_investigation_bureau
                    LegalAffairsMainTab.AdvocatesUnion -> R.drawable.ic_advocates_union
                    else -> R.drawable.ic_jury
                },
                onClick = { onItemClick(item) }
            )
        }
    }
}

@Composable
fun HangItem(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = { },
    @StringRes title: Int,
    @DrawableRes icon: Int,
    isSelected: Boolean?
) {
    val gradientTextColor = listOf(Color(0xFFFE8B07), Color(0xFFFFFD05))
    val shadowColor = Color(0xFFFFFD2A)
    Column (
        modifier = modifier
            .clickable { onClick() }
            .fillMaxWidth(),
//            .aspectRatio(ratio = 0.70F),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_clip),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .graphicsLayer {
                    shadowElevation = 4.dp.toPx()
                    shape = RectangleShape
                }
                .drawBehind {
                    if (isSelected != true) return@drawBehind
                    drawIntoCanvas { canvas ->
                        val paint = Paint().apply {
                            color = shadowColor
                        }
                        val frameworkPaint = paint.asFrameworkPaint()
                        frameworkPaint.setShadowLayer(35F, 0f, 0f, shadowColor.toArgb())
                        canvas.drawRoundRect(
                            left = 0f,
                            top = 0f,
                            right = size.width,
                            bottom = size.height,
                            radiusX = 0f,
                            radiusY = 0f,
                            paint = paint
                        )
                    }
                }
                .background(color = colorResource(id = R.color.black), shape = RectangleShape)
                .border(width = 5.dp, color = colorResource(id = R.color.colorLegalAuthoritiesBannerBackground))
                .padding(horizontal = dimensionResource(id = R.dimen.element_spacing), vertical = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                painter = painterResource(id = icon),
                tint = Color.Unspecified,
                modifier = Modifier.size(size = 34.dp),
                contentDescription = null
            )
            Text(
                text = stringResource(id = title).uppercase(Locale.ROOT),
                textAlign = TextAlign.Center,
                minLines = 2,
                fontWeight = W700,
                overflow = TextOverflow.Ellipsis,
                fontFamily = FontFamily(fonts = listOf(Font(R.font.nunito))),
                modifier = Modifier
                    .padding(top = dimensionResource(id = R.dimen.element_spacing))
                    .padding(horizontal = dimensionResource(id = R.dimen.element_spacing))
                    .fillMaxWidth(),
                style = TextStyle(
//                    fontSize = if (isSelected == null || isSelected == true) 11.sp else 8.sp,
                    fontSize = 11.sp,
                    fontWeight = W500,
                    brush = Brush.verticalGradient(colors = gradientTextColor)
                )
            )
        }
    }
}