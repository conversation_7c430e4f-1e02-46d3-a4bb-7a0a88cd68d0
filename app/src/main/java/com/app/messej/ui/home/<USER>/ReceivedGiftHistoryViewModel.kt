package com.app.messej.ui.home.gift

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.repository.GiftRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ReceivedGiftHistoryViewModel(application: Application) : AndroidViewModel(application) {
    private val giftRepository: GiftRepository = GiftRepository(application)

    private val _giftItem = MutableLiveData<GiftItem>()
    val giftItem: LiveData<GiftItem> = _giftItem


    private val _giftId = MutableLiveData<Int>()
    val giftId: LiveData<Int> = _giftId


     val giftReceivedValue=_giftId.switchMap {
        giftRepository.getGiftSendersHistoryPager(it, true).liveData.cachedIn(viewModelScope)
    }

    fun setArgs(giftId: Int) {
        _giftId.value = giftId
        getGiftDetails(giftId,false)
    }

    private fun getGiftDetails(sendersId: Int, sendersHistory: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = giftRepository.getGiftDetailsInSendersHistory(sendersId, sendersHistory)) {
                is ResultOf.Success -> {
                    _giftItem.postValue(result.value)
                }

                is ResultOf.APIError -> {

                }

                is ResultOf.Error -> {}
            }
        }
    }
}
