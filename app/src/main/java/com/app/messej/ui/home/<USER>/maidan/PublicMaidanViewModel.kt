package com.app.messej.ui.home.publictab.maidan

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.podium.PodiumResponse
import com.app.messej.data.model.api.settings.PodiumPrivacyResponse
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.MaidanTab
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch

class PublicMaidanViewModel(application: Application) : AndroidViewModel(application) {
    private val podiumRepository = PodiumRepository(application)
    private val profileRepo = ProfileRepository(application)
    private val settingsRepo = SettingsRepository(application)

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val _currentTab = MutableLiveData(MaidanTab.CHALLENGE)
    val currentTab: LiveData<MaidanTab?> = _currentTab.distinctUntilChanged()

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    fun setCurrentTab(tab: MaidanTab, skipIfSet: Boolean = false) {
        Log.w("PHVM", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
    }

    private val _maidanMeta = MutableLiveData<PodiumResponse.MaidanMeta?>(null)
    val maidanMeta: LiveData<PodiumResponse.MaidanMeta?> = _maidanMeta

    private val _maidanChallengeFriendsCount = MutableLiveData<Int?>(null)
    val maidanChallengeFriendsCount: LiveData<Int?> = _maidanChallengeFriendsCount

    private val _maidanWatchFriendsCount = MutableLiveData<Int?>(null)
    val maidanWatchFriendsCount: LiveData<Int?> = _maidanWatchFriendsCount

    private val _challengeSubTab = MutableLiveData(MaidanTab.MaidanSubTab.FRIENDS)
    val challengeSubTab: LiveData<MaidanTab.MaidanSubTab> = _challengeSubTab.distinctUntilChanged()

    private val _maidanChallengeList = challengeSubTab.switchMap { st ->
        podiumRepository.getMaidanListingPager(MaidanTab.CHALLENGE,st) {
            _maidanMeta.postValue(it)
            _maidanChallengeFriendsCount.postValue(it.friendsCount)
        }.liveData.cachedIn(viewModelScope)
    }

    val maidanChallengeList: LiveData<PagingData<Podium>> by lazy {
        val med = MediatorLiveData<PagingData<Podium>>()
        fun update() {
            val data = _maidanChallengeList.value?.map {
                it.managerName = _nickNames.value.nickNameOrName(it.managerId,it.managerName)
                it.competitorUserId?.let { cid ->
                    it.competitorName = _nickNames.value.nickNameOrName(cid,it.competitorName.orEmpty())
                }
                it
            }
            med.postValue(data)
        }
        med.addSource(_maidanChallengeList) { update() }
        med.addSource(_nickNames) { update() }
        med
    }

    private val _watchSubTab = MutableLiveData(MaidanTab.MaidanSubTab.FRIENDS)
    val watchSubTab: LiveData<MaidanTab.MaidanSubTab> = _watchSubTab.distinctUntilChanged()

    private val _maidanWatchList = watchSubTab.switchMap { st ->
        podiumRepository.getMaidanListingPager(MaidanTab.WATCH,st) {
            _maidanMeta.postValue(it)
            _maidanWatchFriendsCount.postValue(it.friendsCount)
        }.liveData.cachedIn(viewModelScope)
    }

    val maidanWatchList: LiveData<PagingData<Podium>> by lazy {
        val med = MediatorLiveData<PagingData<Podium>>()
        fun update() {
            val data = _maidanWatchList.value?.map {
                it.managerName = _nickNames.value.nickNameOrName(it.managerId,it.managerName)
                it.competitorUserId?.let { cid ->
                    it.competitorName = _nickNames.value.nickNameOrName(cid,it.competitorName.orEmpty())
                }
                it
            }
            med.postValue(data)
        }
        med.addSource(_maidanWatchList) { update() }
        med.addSource(_nickNames) { update() }
        med
    }

    fun setSubTab(tab: MaidanTab, subTab: MaidanTab.MaidanSubTab) {
        when(tab) {
            MaidanTab.CHALLENGE -> _challengeSubTab.value = subTab
            MaidanTab.WATCH -> _watchSubTab.value = subTab
            else -> {}
        }
    }

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    val onOptInFailed = LiveEvent<String>()
    val onOptInOut = LiveEvent<Boolean>()

    fun toggleOptIn(optIn: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = settingsRepo.setPodiumPrivacy(PodiumPrivacyResponse(receiveChallengeInvitation = optIn))) {
                    is ResultOf.Success -> {
                        onOptInOut.postValue(optIn)
                        _maidanMeta.postValue(_maidanMeta.value?.copy(receiveChallengeInvitation = optIn))
                    }
                    else -> {
                        _maidanMeta.postValue(_maidanMeta.value)
                        onOptInFailed.postValue(result.errorMessage())
                    }
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "leave: error: ${e.message}")
            }
        }
    }

    fun toggleNotify(notify: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = settingsRepo.setPodiumPrivacy(PodiumPrivacyResponse(receiveChallengeStartNotification = notify))) {
                    is ResultOf.Success -> {
                        _maidanMeta.postValue(_maidanMeta.value?.copy(notifyStartChallenge = notify))
                    }
                    else -> {
                        _maidanMeta.postValue(_maidanMeta.value)
                        onOptInFailed.postValue(result.errorMessage())
                    }
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "leave: error: ${e.message}")
            }
        }
    }

    val hasCoinsToStartChallenge = LiveEvent<Boolean>()

    fun checkStatus() {
        viewModelScope.launch(Dispatchers.IO) {
            val account = accountRepo.getAccountDetailsFlow().firstOrNull()
            val fee = 100.00
            if (account != null && account.currentCoin >= fee) {
                hasCoinsToStartChallenge.postValue(true)
            } else {
                hasCoinsToStartChallenge.postValue(false)
            }
        }
    }
}