package com.app.messej.ui.home.publictab.huddles.chat

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.filter
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.chat.BaseChatDisplayViewModel
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.utils.CountryListUtil

open class HuddleMyPostsViewModel(application: Application) : BaseChatDisplayViewModel(application) {

    protected val huddleRepo = HuddlesRepository(application)

    private val _postCount = MutableLiveData(0)
    val postCount: LiveData<Int> = _postCount

    private val _replyCount = MutableLiveData(0)
    val replyCount: LiveData<Int> = _replyCount

    private val postCountCallback: (Int, Int) -> Unit = { posts, replies ->
        _postCount.postValue(posts)
        _replyCount.postValue(replies)
    }

    fun onStreamingVideo(message: AbstractChatMessageWithMedia,uri: String, pos: Int) {
        stopMediaPlayback()
        val info = MediaPlayerInfo.from(message.message,uri,pos).apply {
            state = MediaPlayerInfo.MediaState.PLAYING
        }
        _nowPlayingMedia = info
        nowPlayingMedia.postValue(info)
        pendingItemChange = pos
    }

    suspend fun getVideoStreamingURL(msg: AbstractChatMessageWithMedia, pos: Int): String? {
        msg.message.mediaMeta?.let { meta ->
            if (meta.mediaType == MediaType.VIDEO) {
                val result = chatRepo.getMediaDownloadURL(msg.message)
                if (result is ResultOf.Success) {
                    return result.value.signedUrl
                }
            }
        }
        return null
    }

    private val _countryList = MutableLiveData<Map<String,Int>>()

    init {
        _countryList.postValue(CountryListUtil.getCustomCountryMap())
    }

    override val _chatList = huddleRepo.getMyPostsPager(postCountCallback).liveData.map { pagingData: PagingData<HuddleChatMessageWithMedia> ->
        val flags = _countryList.value
        var pgdata: PagingData<ChatMessageUIModel> = pagingData.map { msg ->
            ChatMessageUIModel.ChatMessageModel(msg).apply {
                if (flags!=null) {
                    msg.message.senderDetails?.countryCode?.let { cc ->
                        countryFlag = flags[cc]
                    }
                }
            }
        }
        pgdata = pgdata.filter {
            val msg = if(it is ChatMessageUIModel.ChatMessageModel) it.message as HuddleChatMessage else null
            msg?: return@filter false
            return@filter !msg.deleted && !msg.isActivity && msg.senderDetails?.deletedAccount!=true
        }
        pgdata
    }.cachedIn(viewModelScope)

    override val showChats: LiveData<Boolean>
        get() = MutableLiveData(true)

    override fun getSenderForReply(msg: AbstractChatMessage) = null
}