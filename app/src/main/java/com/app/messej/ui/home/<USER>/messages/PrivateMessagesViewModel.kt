package com.app.messej.ui.home.privatetab.messages

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

@OptIn(FlowPreview::class)
class PrivateMessagesViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val huddleRepo = HuddlesRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())

    var searchKeyword = MutableLiveData<String>(null)
    private val searchTerm = MutableLiveData("")

    var tabType: MutableLiveData<PrivateChat.PrivateMessageTabType?> = MutableLiveData(null)

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                it?: return@collect
                if (it.isBlank()){
                    searchTerm.postValue("")
                }else{
                    searchTerm.postValue(it)
                }
            }
        }
    }

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    val user: CurrentUser get() = accountRepo.user

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val _chatList = tabType.switchMap {
        tabType
        huddleRepo.getPrivateChatsPager(tabType = if (user.premium) it else null).liveData.cachedIn(viewModelScope)
    }

    val chatList: MediatorLiveData<PagingData<PrivateMessagesAdapter.PrivateChatUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<PrivateMessagesAdapter.PrivateChatUIModel>?>(null)
        fun updateIntrudersList() {
            _dataLoading.value = false
            val list: PagingData<PrivateMessagesAdapter.PrivateChatUIModel>? = _chatList.value?.map { chat ->
                _nickNames.value?.findNickName(chat.receiver)?.let {
                    chat.receiverDetails.name = it
                }
                PrivateMessagesAdapter.PrivateChatUIModel.ChatUIModel(chat, _selectedChatsList.find { return@find it.id==chat.id }!=null)
            }
            chatList.postValue(list)
        }
        med.addSource(_chatList) { updateIntrudersList() }
        med.addSource(_selectedChats) { updateIntrudersList() }
        med.addSource(_nickNames) { updateIntrudersList() }
        med
    }

    fun resetSearch() {
        searchKeyword.postValue("")
    }

    val onNavigateToPrivateMessage = LiveEvent<Pair<String,Int>>()

    fun navigateToPrivateMessage(receiver: Int) {
        viewModelScope.launch {
            val roomId = HuddlesRepository(getApplication()).getPrivateChatRoomId(receiver)
            onNavigateToPrivateMessage.postValue(Pair(roomId,receiver))
        }
    }

    fun setTabType(tab: PrivateChat.PrivateMessageTabType) {
        tabType.postValue(tab)
    }

    // Selection Mode
    private val _chatListSelectionMode = MutableLiveData(false)
    val chatListSelectionMode: LiveData<Boolean> = _chatListSelectionMode.distinctUntilChanged()

    var _selectedChatsList: MutableList<PrivateChat> = mutableListOf()
    val _selectedChats = MutableLiveData< List <PrivateChat> >(listOf())

    fun enterSelectionMode(chat: PrivateChat, pos: Int) {
        if (tabType.value == PrivateChat.PrivateMessageTabType.INTRUDER)
            if (_chatListSelectionMode.value==false) {
            Log.d("BHLVM", "enterSelectionMode: ")
            _chatListSelectionMode.value = true
            _selectedChatsList = mutableListOf()
        }
        selectChat(chat,pos)
    }

    fun exitSelectionMode() {
        Log.d("BHLVM", "exitSelectionMode: ")
        _chatListSelectionMode.value = false
        _selectedChatsList = mutableListOf()
        _selectedChats.value = _selectedChatsList.toList()
    }

    fun selectChat(chat: PrivateChat, pos: Int): Boolean {
        if(_chatListSelectionMode.value == false) return false
        _selectedChatsList.apply {
            if (this.size == 0) //to limit the selection to one as delete only support single chat id. Can be changed in the future
                if (!removeIf { chat.id == it.id }) {
                add(chat)
            }
            _selectedChats.value = toList()
        }
//        onItemChange.postValue(pos)
        return true
    }

    fun deleteChatRequest(){
        viewModelScope.launch(Dispatchers.IO){
            when(val result: ResultOf<String> =
                huddleRepo.privateChatIntruderDeleteAction(_selectedChatsList[0].roomId)){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    viewModelScope.launch {
                        exitSelectionMode()
                    }
                }
            }
        }
    }
}