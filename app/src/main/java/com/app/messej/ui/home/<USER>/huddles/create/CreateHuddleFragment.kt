package com.app.messej.ui.home.publictab.huddles.create

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.databinding.FragmentCreateHuddleBinding
import com.app.messej.ui.auth.common.HuddleLanguageDropDownAdapter
import com.app.messej.ui.common.CategoryDropdownAdapter
import com.app.messej.ui.home.publictab.common.BaseProfilePicAttachFragment
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.ATTACH_SOURCE_RESULT_KEY
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_CAMERA
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_GALLERY
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView


class CreateHuddleFragment : BaseProfilePicAttachFragment() {

    private val args : CreateHuddleFragmentArgs by navArgs()
    private lateinit var binding: FragmentCreateHuddleBinding

    override val viewModel : CreateHuddleViewModel by viewModels()

    override val bindingRoot: View
        get() = binding.root

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_create_huddle, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()

        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed:")
                showBackPressAlert()
            }
        })
        (activity as MainActivity).setupActionBar(binding.toolbar)
        binding.toolbar.apply {
            setNavigationOnClickListener {
                showBackPressAlert()
            }
        }

    }

    private fun setup() {
        viewModel.setHuddleType(args.huddleType)
        binding.huddlePolicyButton.text = if(args.huddleType == HuddleType.PRIVATE) getString(R.string.settings_title_groups_policy) else getString(R.string.settings_title_huddle_policy)

        binding.huddleDp.setOnClickListener {
            if(viewModel.getCategoryListLoading.value==true || viewModel.createHuddleLoading.value==true) return@setOnClickListener
            val action = CreateHuddleFragmentDirections.actionGlobalProfileImageAttachSourceFragment()
            findNavController().navigateSafe(action)
        }

        binding.createHuddleNextButton.setOnClickListener {
            viewModel.createHuddle()
        }

        binding.textInputName.editText?.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                viewModel.didEnterName.postValue(true)
            }
        }
        binding.huddlePolicyButton.setOnClickListener {
            val documentType = if(args.huddleType == HuddleType.PRIVATE) DocumentType.GROUP_POLICY else DocumentType.HUDDLE_POLICY
            findNavController().navigateSafe(CreateHuddleFragmentDirections.actionGlobalPolicyFragment(documentType, false))
        }
    }

    private fun observe() {
        viewModel.huddleCategoryList.observe(viewLifecycleOwner) { cats ->
            if (cats != null) {
                val adapter = CategoryDropdownAdapter(requireContext(), cats)
                (binding.categoryDropdown.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.setCategoryId(item.categoryId)
                    }
                    viewModel.category.value?.let { cat ->
                        val index = cats.indexOfFirst { it.categoryId == cat }
                        if (index==-1) return@let
                        adapter.setSelectedPos(index)
                        setText(cats[index].name)
                    }
                }
            }
        }

        viewModel.huddleLanguageList.observe(viewLifecycleOwner) { languages ->
            if (languages != null) {
                val adapter = HuddleLanguageDropDownAdapter(requireContext(), languages)
                (binding.languageDropdown.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.setLanguage(item)
                    }
                    viewModel.huddleLanguage.value?.let { lang ->
                        val index = languages.indexOfFirst { it.englishName == lang }
                        if (index==-1) return@let
                        adapter.setSelectedPos(index)
                        setText(languages[index].name)
                    }
                }
            }

        }

        viewModel.createHuddleCompleted.observe(viewLifecycleOwner) {
            if (it!=null) {
                findNavController().navigateSafe(CreateHuddleFragmentDirections.actionCreateHuddleFragmentToAddParticipantsFragment(it.id,true))
            }
        }

        setFragmentResultListener(ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when(bundle.getString(ATTACH_SOURCE_RESULT_KEY)) {
                SRC_GALLERY -> selectImageFromGallery()
                SRC_CAMERA -> takeImage()
            }
        }

        viewModel.nameError.observe(viewLifecycleOwner) {
            binding.textInputName.error = when (it) {
                CreateHuddleViewModel.Companion.NameError.GT_MAX -> {
                    binding.textInputName.isErrorEnabled = true
                    resources.getString(R.string.register_create_profile_error_name_max)
                }
                else -> {
                    binding.textInputName.isErrorEnabled = false
                    null
                }
            }
        }

        viewModel.getCategoryListLoading.observe(viewLifecycleOwner) {
            if (it) binding.multiStateView.viewState = MultiStateView.ViewState.LOADING
            else binding.multiStateView.viewState = MultiStateView.ViewState.CONTENT
        }
    }

    private fun showBackPressAlert() {
        confirmAction(
            title = R.string.create_huddle_unsaved_alert_title,
            message = R.string.create_huddle_unsaved_alert_text,
            positiveTitle = R.string.common_ok,
            negativeTitle = R.string.common_cancel
        ) {
            if(view!=null && viewLifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                findNavController().popBackStack()
            }
        }
    }
}