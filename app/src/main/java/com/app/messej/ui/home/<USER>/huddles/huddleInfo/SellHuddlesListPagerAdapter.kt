package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.huddles.HuddleForSale
import com.app.messej.databinding.ItemHuddleSalesListBinding

class SellHuddlesListPagerAdapter(val listener: ActionListener) : PagingDataAdapter<HuddleForSale, SellHuddlesListPagerAdapter.SellHuddlesListViewHolder>(TransactionsDiff) {
    interface ActionListener {
        fun buyNowClick(item: HuddleForSale)
        fun goToHuddleClick(item: HuddleForSale)
    }

    override fun onBindViewHolder(holder: SellHuddlesListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = SellHuddlesListViewHolder(
        ItemHuddleSalesListBinding.inflate(LayoutInflater.from(parent.context), parent, false)
    )

    inner class SellHuddlesListViewHolder(private val binding: ItemHuddleSalesListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: HuddleForSale) = with(binding) {
            binding.apply {
                sellHuddlesList = item
                actionGotoHuddle.setOnClickListener {
                listener.goToHuddleClick(item)
                }
                actionBuyNow.setOnClickListener {
                    listener.buyNowClick(item)
                }
            }
        }
    }


    object TransactionsDiff : DiffUtil.ItemCallback<HuddleForSale>() {
        override fun areItemsTheSame(oldItem: HuddleForSale, newItem: HuddleForSale) = oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: HuddleForSale, newItem: HuddleForSale) = oldItem == newItem
    }

}