package com.app.messej.ui.home.publictab.flash.userflash

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.databinding.FragmentFlashUserBinding
import com.app.messej.ui.home.publictab.flash.myflash.FlashListBaseFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class UserFlashFragment : FlashListBaseFragment() {

    override val viewModel: UserFlashViewModel by navGraphViewModels(R.id.nav_flash_user)

    private val args: UserFlashFragmentArgs by navArgs()

    private lateinit var binding: FragmentFlashUserBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_user, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        viewModel.setUserId(args.userId)
        super.onViewCreated(view, savedInstanceState)
        binding.customActionBar.chatDp.setOnClickListener {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(args.userId))
        }
    }

    override val multiStateView: MultiStateView
        get() = binding.multiStateView

    override val flashList: RecyclerView
        get() = binding.flashList

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
    }

    override fun onFlashClicked(flash: FlashVideo, pos: Int) {
        if (!flash.isBlocked && !flash.isDeleted) {
            findNavController().navigateSafe(UserFlashFragmentDirections.actionUserFlashFragmentToUserFlashPlayerFragment())
        }
    }
}