package com.app.messej.ui.home.businesstab.operations.tasks

import android.app.Application
import android.os.CountDownTimer
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.auth.VerifyResponse
import com.app.messej.data.model.entity.BusinessTaskOne
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.model.enums.TaskOneEmptyMode
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BusinessOperationTaskOneViewModel(application: Application) : AndroidViewModel(application) {

    private val businessRepo = BusinessRepository(application)

    private val authRepo: AuthenticationRepository = AuthenticationRepository(getApplication())
    val taskOneData: LiveData<BusinessTaskOne?> = businessRepo.getTaskOneDetails()

    private val _emailAvailabilityError = MutableLiveData<String?>(null)
    val emailAvailabilityError: LiveData<String?> = _emailAvailabilityError

    private val _phoneAvailabilityError = MutableLiveData<String?>(null)
    val phoneAvailabilityError: LiveData<String?> = _phoneAvailabilityError

    val verifyApiMessage = LiveEvent<String?>()

    private val _showAboutInvalidError = MutableLiveData<Boolean?>(null)
    val showAboutAvailabilityError: LiveData<Boolean?> = _showAboutInvalidError

    private val _isNextButtonEnabled = MutableLiveData<Boolean?>(null)
    val isNextButtonEnabled: LiveData<Boolean?> = _isNextButtonEnabled

    val showErrorDialogue= LiveEvent<Boolean?>()

    private val _taskOneEmptyMode = MutableLiveData<TaskOneEmptyMode?>(null)
    val taskOneEmptyMode: LiveData<TaskOneEmptyMode?> = _taskOneEmptyMode

    private val _isCheckedLiveData = MutableLiveData(false)

    private val _canRequestOTPResend = MutableLiveData(true)
    val canRequestOTPResend: LiveData<Boolean> = _canRequestOTPResend

    val isCheckedLiveData: LiveData<Boolean>
        get() = _isCheckedLiveData

     private val emailErrorLiveData = MutableLiveData(true)

    val emailValid: LiveData<Boolean>
        get() = emailErrorLiveData

    private var _isPaypalIsVerified = MutableLiveData<Boolean>(false)
    val isPaypalIsVerified: LiveData<Boolean>
        get() = _isPaypalIsVerified


    private var _isPhoneValid = MutableLiveData<Boolean?>(null)
    val isPhoneValid: LiveData<Boolean?>
        get() = _isPhoneValid

    private var _resend = MutableLiveData<Boolean?>(false)
    val resend: LiveData<Boolean?>
        get() = _resend


    private val _emailData = MutableLiveData<String?>()
    val emailData: LiveData<String?>
        get() = _emailData

    private val _otpRequestMode = MutableLiveData<OTPRequestMode?>()
    val otpRequestMode: LiveData<OTPRequestMode?>
        get() = _otpRequestMode

    private val _paypalId = MutableLiveData<String>()
    val paypalId: LiveData<String>
        get() = _paypalId

    private val _phone = MutableLiveData<String>()
    val phone: LiveData<String>
        get() = _phone

    private val _didRequestOTP = MutableLiveData(false)
    val didRequestOTP: LiveData<Boolean>
        get() = _didRequestOTP

    val isRequestOtp= LiveEvent<Boolean?>()


    private val _alreadyVerified= MutableLiveData<Boolean>(null)
    val alreadyVerified: LiveData<Boolean?>
        get() = _alreadyVerified

    private val _requestOTPError = MutableLiveData<String>()
    val requestOTPError: LiveData<String>
        get() = _requestOTPError

    private val _isProfileImageUploaded = MutableLiveData<Boolean>()
    val isProfileImageUploaded: LiveData<Boolean>
        get() = _isProfileImageUploaded

    private val _otpVerified = MutableLiveData<Boolean?>(null)
    val otpVerified: LiveData<Boolean?>
        get() = _otpVerified
    val otpRequestComplete = MediatorLiveData<OTPRequestMode?>()


    private val _didShowPhoneError = MutableLiveData<Boolean>(null)
    private val didShowPhoneError: LiveData<Boolean?>
        get() = _didShowPhoneError


    private val _didShowEmailError = MutableLiveData(false)
    val didShowEmailError: LiveData<Boolean>
        get() = _didShowEmailError

    private val _isEmailValid = MutableLiveData(false)
    val isEmailValid: LiveData<Boolean>
        get() = _isEmailValid

    private val _otpTimeout = MutableLiveData<Long>(0)
    val otpTimeout: LiveData<Long> = _otpTimeout

    private var timer: CountDownTimer? = null

    private val _dpUploading = MutableLiveData<Boolean>(false)
    val dpUploading: LiveData<Boolean> = _dpUploading

    private val _isEmailHelperTextVisible = MutableLiveData(false)
    val isEmailHelperTextVisible: LiveData<Boolean> = _isEmailHelperTextVisible

    private val _isMobileHelperTextVisible = MutableLiveData(false)
    val isMobileHelperTextVisible: LiveData<Boolean> = _isMobileHelperTextVisible

    private val _isOtpNextButtonEnabled = MutableLiveData(false)
    val isOtpNextButtonEnabled: LiveData<Boolean> = _isOtpNextButtonEnabled


    val enteredEmail = MutableLiveData<String>()
    val enteredAbout = MutableLiveData<String>()
    val enteredMobile = MutableLiveData<String>()


    private val _countryCode = MutableLiveData<String?>(null)
    val countryCode: LiveData<String?> = _countryCode

    val optTimeoutString = _otpTimeout.map {
        return@map DateTimeUtils.formatSeconds(it)
    }

    fun handleAboutTextChange(about: String) {
       if (about.isNotEmpty()) {
            _showAboutInvalidError.postValue(false)
        } else {
           _showAboutInvalidError.postValue(true)
        }
    }

    fun setEmailHelperText(isVisible:Boolean){
      _isEmailHelperTextVisible.postValue(isVisible)
    }

    fun setOTPNextButtonEnabled(isEnabled:Boolean){
        _isOtpNextButtonEnabled.postValue(isEnabled)
    }

    fun setMobileHelperText(isVisible:Boolean){
        _isMobileHelperTextVisible.postValue(isVisible)
    }

    fun didShowEmailError(isShow: Boolean) {
        _didShowEmailError.postValue(isShow)
    }
    fun didShowMobileError(isShow: Boolean) {
        _didShowPhoneError.postValue(isShow)
    }

    fun handleCheckBox(isChecked: Boolean) {
        if (isChecked) {
            _emailData.postValue(taskOneData.value?.email)
        } else {
            _emailData.postValue("")
        }
    }

    fun resetOtpTimerValue() {
        timer?.cancel()
        _otpTimeout.postValue(0)
    }

    private fun countDownWaitTime(wait: Long) {
        timer?.cancel()
        if (wait <= 0) {
            _otpTimeout.postValue(0)
        }
        _otpTimeout.postValue(wait)
        _canRequestOTPResend.postValue(false)
        viewModelScope.launch(Dispatchers.Main) {
            timer = object : CountDownTimer(wait * 1000, 1000) {

                override fun onTick(millisUntilFinished: Long) {
                    _otpTimeout.postValue(millisUntilFinished / 1000)
                }

                override fun onFinish() {
                    _otpTimeout.postValue(0)
                    _canRequestOTPResend.postValue(true)
                }
            }.start()
        }
    }

    init {
        taskOneData.observeForever {
            it?.let {
                _isPaypalIsVerified.postValue(it.paypalVerified ?: false)
                _didShowEmailError.postValue(it.paypalVerified)
                setNextButtonState()
                if (it.paypalVerified == true) {
                    setPaypalVerified(true)
                }
            }
        }

        fun didShowEmailError(isShow: Boolean) {
            _didShowEmailError.postValue(isShow)
        }
   /*     otpRequestComplete.addSource(_didRequestOTP) { didRequestOTPValue ->
            val paypalIdValue = _paypalId.value
            val phone=_phone.value
            if (didRequestOTPValue == true &&  phone!=null) {
                otpRequestComplete.postValue(_otpRequestMode.value)
            }
        }
        otpRequestComplete.addSource(_phone) { phoneValue ->
            val didRequestOTPValue = _didRequestOTP.value
            val paypalIdValue = _paypalId.value

            if (didRequestOTPValue == true && (paypalIdValue != null || phoneValue != null)) {
                otpRequestComplete.postValue(_otpRequestMode.value)
            }
        }
*/
    }

    private val _showEmailInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(_didShowEmailError) { shouldShowEmailError() }
        med.addSource(_isEmailValid) { shouldShowEmailError() }
        med
    }

    private val _showPhoneInvalidError: MediatorLiveData<Boolean?> by lazy {
        val med = MediatorLiveData<Boolean?>()
        med.addSource(_didShowPhoneError) { shouldShowPhoneError() }
        med.addSource(_isPhoneValid) { shouldShowPhoneError() }
        med
    }



   /* private val _isNextButtonEnabled: MediatorLiveData<Boolean?> by lazy {
        val med = MediatorLiveData<Boolean?>()
        med.addSource(_showPhoneInvalidError) { shouldShowNextButton() }
        med.addSource(_showEmailInvalidError) { shouldShowNextButton() }
        med.addSource(_showAboutInvalidError){shouldShowNextButton()}
        med
    }*/


/*    private fun shouldShowNextButton() {
      when(_taskOneEmptyMode.value){
          TaskOneEmptyMode.EMPTY_EMAIL -> {
              if(_showEmailInvalidError.value==false){
                  _isNextButtonEnabled.postValue(true)
              }else{
                  _isNextButtonEnabled.postValue(false)
              }
          }
          TaskOneEmptyMode.EMPTY_MOBILE -> {
              if(_showPhoneInvalidError.value==false){
                  _isNextButtonEnabled.postValue(true)
              }else{
                  _isNextButtonEnabled.postValue(false)
              }
          }
          TaskOneEmptyMode.EMPTY_ABOUT -> {
              if(_showAboutInvalidError.value==false){
                  _isNextButtonEnabled.postValue(true)
              }else{
                  _isNextButtonEnabled.postValue(false)
              }
          }
          TaskOneEmptyMode.EMPTY_MOBILE_AND_ABOUT -> {
              if (_showPhoneInvalidError.value == false ) {
                  _isNextButtonEnabled.postValue(true)
              }else{
                  _isNextButtonEnabled.postValue(false)
              }
          }
          TaskOneEmptyMode.EMPTY_EMAIL_AND_ABOUT ->{
              if (_showEmailInvalidError.value == false) {
                  _isNextButtonEnabled.postValue(true)
              }else{
                  _isNextButtonEnabled.postValue(false)
              }
          }
          null -> {

          }
      }
    }*/


    fun checkEmailValidity() {
        clearEmailError()
        if (UserInfoUtil.isEmailValid(enteredEmail.value.toString()) && enteredEmail.toString().isNotEmpty()) {
            _isEmailValid.postValue(true)
        } else {
            _isEmailValid.postValue(false)
        }
    }

     fun clearPhoneError() {
          _showPhoneInvalidError.postValue(false)
         _phoneAvailabilityError.postValue("")
    }

    fun clearEmailError() {
        _showEmailInvalidError.postValue(false)
        _emailAvailabilityError.postValue("")
    }

    val showEmailInvalidError: LiveData<Boolean?> = _showEmailInvalidError
    val showPhoneInvalidError: LiveData<Boolean?> = _showPhoneInvalidError
/*    val isNextButtonEnabled: LiveData<Boolean?> = _isNextButtonEnabled*/


    private fun shouldShowEmailError() {
        if (didShowEmailError.value == false|| enteredEmail.value.isNullOrEmpty()) {
            _showEmailInvalidError.postValue(false)
            return
        }
        _showEmailInvalidError.postValue(_isEmailValid.value == false)
    }


    private fun shouldShowPhoneError() {
        if(didShowPhoneError.value!=null && enteredMobile.value!= null){
            if (didShowPhoneError.value == false || enteredMobile.value.isNullOrEmpty()) {
                _showPhoneInvalidError.postValue(false)
                return
            }
            _showPhoneInvalidError.postValue(_isPhoneValid.value == false)
        }
    }

    fun setCountryCode(selectedCountryCode: String?) {
        _countryCode.postValue(selectedCountryCode)
    }

    fun requestOtp() {
        val userEmail = taskOneData.value?.email ?: ""
        val userAbout = taskOneData.value?.about ?: ""
        val userMobile = taskOneData.value?.phone ?: ""
        if (userEmail.isNotEmpty() || userAbout.isNotEmpty() || userMobile.isNotEmpty()) {
            if (taskOneData.value?.profileCompletePercentage == 100) {
                _alreadyVerified.postValue(true)
            } else {
                if (userEmail.isNotEmpty()) {
                    setOtpRequestMode(OTPRequestMode.VERIFY_MOBILE)
                } else if (userMobile.isNotEmpty()) {
                    setOtpRequestMode(OTPRequestMode.VERIFY_EMAIL)
                }
                if (_isProfileImageUploaded.value == true) {
                    when (_taskOneEmptyMode.value) {
                        TaskOneEmptyMode.EMPTY_EMAIL -> {
                            if (enteredEmail.value.isNullOrEmpty()) {
                                showErrorDialogue.postValue(true)
                            } else if (!UserInfoUtil.isEmailValid(enteredEmail.value!!)) {
                                _showEmailInvalidError.postValue(true)
                            } else {
                                verifyPapalID()
                            }
                        }

                        TaskOneEmptyMode.EMPTY_MOBILE -> {

                            if (enteredMobile.value.isNullOrEmpty()) {
                                showErrorDialogue.postValue(true)
                            } else if (!_isPhoneValid.value!!) {
                                _showPhoneInvalidError.postValue(true)
                            } else {
                                verifyPapalID()
                            }
                        }

                        TaskOneEmptyMode.EMPTY_ABOUT -> if (enteredAbout.value.isNullOrEmpty()) {
                            showErrorDialogue.postValue(true)
                        } else {
                            verifyPapalID()
                        }

                        TaskOneEmptyMode.EMPTY_MOBILE_AND_ABOUT -> {
                            if (enteredAbout.value.isNullOrEmpty() || enteredMobile.value.isNullOrEmpty()) {
                                showErrorDialogue.postValue(true)
                            } else if (!_isPhoneValid.value!!) {
                                _showPhoneInvalidError.postValue(true)
                            } else {
                                verifyPapalID()
                            }
                        }

                        TaskOneEmptyMode.EMPTY_EMAIL_AND_ABOUT -> {
                            if (enteredEmail.value.isNullOrEmpty() || enteredAbout.value.isNullOrEmpty()) {
                                showErrorDialogue.postValue(true)
                            } else if (UserInfoUtil.isEmailValid(enteredEmail.value!!)) {
                                _showEmailInvalidError.postValue(true)
                            } else {
                                verifyPapalID()
                            }

                        }

                        else -> {}
                    }
                } else {
                    showErrorDialogue.postValue(true)
                }
            }

        } else {
            if (taskOneData.value?.profileCompletePercentage == 100) {
                _alreadyVerified.postValue(true)
            } else {
                _alreadyVerified.postValue(false)
                showErrorDialogue.postValue(true)
            }
        }
    }

    private fun verifyPapalID() {
        _didRequestOTP.postValue(false)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<APIResponse<VerifyResponse>> = businessRepo.verifyPaypalId(
                userMobile =  taskOneData.value?.phone ?: "",
                userAbout = taskOneData.value?.about ?: "",
                userEmail =  taskOneData.value?.email ?: "",
                enteredMobile = UserInfoUtil.removeLeadingZeroes(enteredMobile.value)?: "",
                enteredEmail = enteredEmail.value ?: "",
                enteredAbout = enteredAbout.value ?: "",
                resend = _resend.value?:false,
                countryCode = countryCode.value ?: "",
                mode = _taskOneEmptyMode.value!!
            )){
                is ResultOf.Success -> {
                    _didRequestOTP.postValue(true)
                    if(_taskOneEmptyMode.value!=TaskOneEmptyMode.EMPTY_ABOUT) {
                        countDownWaitTime(result.value.result.waitTime.toLong())
                        otpRequestComplete.postValue(_otpRequestMode.value)
                    }
                    businessRepo.getBusinessTaskOne()
                    verifyApiMessage.postValue(result.value.message)
                    _phone.postValue(enteredMobile.value?:"")
                    isRequestOtp.postValue(true)
                    emailErrorLiveData.postValue(false)
                    _resend.postValue(true)
                }
                is ResultOf.APIError -> {

                    _isPhoneValid.postValue(false)
                    _didShowPhoneError.postValue(true)
                    _showPhoneInvalidError.postValue(true)
                    _showEmailInvalidError.postValue(true)
                    _didShowEmailError.postValue(true)
                    _phoneAvailabilityError.postValue(result.error.message)
                    _emailAvailabilityError.postValue(result.error.message)
                    isRequestOtp.postValue(false)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }

        }
    }


    fun verifyMobileOTP(otp: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val mode = _otpRequestMode.value!!
            val phone = enteredMobile.value ?: ""
            val mail = enteredEmail.value ?: ""
            val countryCode = _countryCode.value ?: ""

            val result = when (mode) {
                OTPRequestMode.VERIFY_EMAIL -> authRepo.verifyEmailOTP(requestMode = mode, email = mail, otp = otp)
                OTPRequestMode.VERIFY_MOBILE -> authRepo.verifyMobileOTP(requestMode = mode, phoneNumber = UserInfoUtil.removeLeadingZeroes(phone)!!, otp = otp, countryCode = countryCode)
                else -> null // or any other default value
            }
            when (result) {
                is ResultOf.Success -> {
                    _otpVerified.postValue(true)

                }
                is ResultOf.APIError -> {
                    if (result.error.message.isNullOrEmpty()){
                        _requestOTPError.postValue("Internal Server Error")
                    }else{
                        _requestOTPError.postValue(result.error.message)
                    }
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }

                else -> {}
            }
        }
    }


    private fun reSendRequest(paypalId: String) {
        _didRequestOTP.postValue(false)

        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<APIResponse<VerifyResponse>> = businessRepo.resendRequest(paypalId)) {
                is ResultOf.Success -> {
                    _didRequestOTP.postValue(true)
                    result.value.result.waitTime.let { countDownWaitTime(it.toLong()) }
                }
                is ResultOf.APIError -> {
                    _didRequestOTP.postValue(false)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }

        }

    }


    fun reSendOTP() {
        if(_canRequestOTPResend.value!!) {
            _resend.postValue(true)
            requestOtp()
        }
    }
    fun resetOtpVerifiedMessage(){
        _otpVerified.postValue(null)
    }

    private fun setOtpRequestMode(mode: OTPRequestMode) {
        _otpRequestMode.postValue(mode)
    }

    fun setPaypalVerified(isVerified: Boolean) {
        if (isVerified) {
            _isPaypalIsVerified.postValue(true)
            _didShowEmailError.postValue(true)
        }
    }

    fun setEmailEdit(isEditable: Boolean) {
        _didShowEmailError.postValue(isEditable)
        //_isNextButtonEnabled.postValue(isEditable)
    }

    fun setBottomHideBottomSheet() {
        _otpTimeout.postValue(0)
        _didRequestOTP.postValue(false)
    }


    fun setPhoneNumberValid(valid: Boolean) {
        _isPhoneValid.postValue(valid)

    }
    fun setNextButtonState() {
        if (taskOneData.value?.paypalVerified == true && taskOneData.value?.profileCompletePercentage == 100) {
         //   _isNextButtonEnabled.postValue(true)
        } else {
          //  _isNextButtonEnabled.postValue(false)
        }
    }

    fun reloadTaskOneData() {
       loadBusinessTaskOne()
    }

    fun setEmptyMode(taskOneMode: TaskOneEmptyMode) {
        _taskOneEmptyMode.postValue(taskOneMode)
    }

    private fun loadBusinessTaskOne(){
        _dpUploading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<BusinessTaskOne> = businessRepo.getBusinessTaskOne()) {
                is ResultOf.Success -> {
                    _dpUploading.postValue(false)
                }
                is ResultOf.APIError -> {
                    _dpUploading.postValue(false)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }

        }
    }

    fun setProfilePhotoUploadStatus(isUploaded: Boolean) {
     _isProfileImageUploaded.postValue(isUploaded)
    }

    fun setNextButtonEnabled() {
        _isNextButtonEnabled.postValue(true)
    }

}
