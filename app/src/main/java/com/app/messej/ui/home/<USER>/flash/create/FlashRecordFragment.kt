package com.app.messej.ui.home.publictab.flash.create

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.util.Rational
import android.view.HapticFeedbackConstants
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.Surface
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.AccelerateInterpolator
import android.view.animation.AnticipateOvershootInterpolator
import android.view.animation.LinearInterpolator
import android.view.animation.OvershootInterpolator
import android.widget.FrameLayout
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.Camera
import androidx.camera.core.CameraInfoUnavailableException
import androidx.camera.core.CameraSelector
import androidx.camera.core.DisplayOrientedMeteringPointFactory
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.MeteringPointFactory
import androidx.camera.core.Preview
import androidx.camera.core.SurfaceOrientedMeteringPointFactory
import androidx.camera.core.UseCaseGroup
import androidx.camera.core.ViewPort
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.video.FallbackStrategy
import androidx.camera.video.FileOutputOptions
import androidx.camera.video.Quality
import androidx.camera.video.QualitySelector
import androidx.camera.video.Recorder
import androidx.camera.video.Recording
import androidx.camera.video.VideoCapture
import androidx.camera.video.VideoRecordEvent
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.TempFlashMedia
import com.app.messej.databinding.FragmentFlashRecordBinding
import com.app.messej.ui.utils.FragmentExtensions.afterMeasured
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkAudioPermission
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


class FlashRecordFragment : Fragment() {

    private lateinit var binding: FragmentFlashRecordBinding

    val args: FlashRecordFragmentArgs by navArgs()

    private lateinit var timerSheetBehavior: BottomSheetBehavior<FrameLayout>

    private val viewModel: FlashRecordViewModel by navGraphViewModels(R.id.nav_flash_record)

    private var cameraProvider : ProcessCameraProvider? = null

    private lateinit var scaleGestureDetector: ScaleGestureDetector

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_record, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        timerSheetBehavior = BottomSheetBehavior.from(binding.timerBottomSheet)
        toggleTimerSheet(false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setRecordLimit(limit = args.flashDuration)
        args.drafId?.also {
            viewModel.prepareDraft(it)
        }?:run {
            setup()
        }
        observe()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setup() {
        checkCameraPermission(binding.root) {
            checkAudioPermission(binding.root) {
                val cameraProviderFuture = ProcessCameraProvider.getInstance(requireContext())
                cameraProviderFuture.addListener({
                     cameraProvider = cameraProviderFuture.get().apply {
                         bindPreview(this)
                     }
                 }, ContextCompat.getMainExecutor(requireContext()))
            }
        }

        scaleGestureDetector = ScaleGestureDetector(requireContext(), object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scale = camera?.cameraInfo?.zoomState?.value?.zoomRatio?.times(detector.scaleFactor)?:1f
                camera?.cameraControl?.setZoomRatio(scale)
                return true
            }
        })

        binding.previewView.afterMeasured {
            binding.previewView.setOnTouchListener { _, event ->
                return@setOnTouchListener when (event.action) {
                    MotionEvent.ACTION_UP -> {
                        triggerAutoFocus(event.x, event.y)
                        true
                    }
                    MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> scaleGestureDetector.onTouchEvent(event)
                    else -> false
                }
            }
        }

        binding.flipButton.setOnClickListener {
            cameraProvider?.let {
                animateCameraFlip()
                bindPreview(it,true)
            }
        }

        binding.flashButton.setOnClickListener { viewModel.toggleFlash() }

        binding.timerButton.setOnClickListener {
            viewModel.initTimerSelection()
            toggleTimerSheet(true)
        }

        binding.timerSheet.apply {
            timer3sButton.setOnClickListener { viewModel.selectTimer(FlashRecordViewModel.FlashRecordTimer.SECONDS_3) }
            timer5sButton.setOnClickListener { viewModel.selectTimer(FlashRecordViewModel.FlashRecordTimer.SECONDS_5) }
            timer10sButton.setOnClickListener { viewModel.selectTimer(FlashRecordViewModel.FlashRecordTimer.SECONDS_10) }
            timerSetButton.setOnClickListener { viewModel.setTimer(); toggleTimerSheet(false) }
            timerCancelButton.setOnClickListener { toggleTimerSheet(false) }
        }

        binding.closeButton.setOnClickListener { findNavController().popBackStack() }

        binding.recordButton.setOnClickListener {
            checkCameraPermission(binding.root) {
                checkAudioPermission(binding.root) {
                    val proceed: () -> Unit = {
                        viewModel.startStopRecording {
                            stopRecording()
                        }
                    }
                    if (viewModel.flashRecording.value != null) {
                        showRecordingOverwriteAlert(proceed) {
                            goToNextScreen()
                        }
                    } else proceed.invoke()
                }
            }
        }

        binding.recordingNextButton.setOnClickListener {
            goToNextScreen()
        }

        binding.recordingDeleteButton.setOnClickListener {
            showRecordingDeleteAlert {
                viewModel.clearRecording()
                viewModel.clearVideoPostDetails()
            }
        }

        binding.galleryImportButton.setOnClickListener {
            selectMediaFromGallery()
        }

    }

    private fun goToNextScreen() {
        findNavController().navigateSafe(FlashRecordFragmentDirections.actionFlashRecordFragmentToFlashRecordEditFragment())
    }

    private fun showRecordingOverwriteAlert(onRecord: () -> Unit, onNext: () -> Unit) {
        MaterialAlertDialogBuilder(requireContext()).setTitle(R.string.flash_rerecord_clip_alert_title)
            .setMessage(getText(R.string.flash_rerecord_clip_alert_message))
            .setPositiveButton(getText(R.string.common_restart)) { dialog, _ ->
                dialog.dismiss()
                onRecord.invoke()
            }.setNegativeButton(getText(R.string.common_next)) { dialog, _ ->
                dialog.dismiss()
                onNext.invoke()
            }.show()
    }

    private fun showRecordingDeleteAlert(onConfirm: () -> Unit) {
        MaterialAlertDialogBuilder(requireContext()).setTitle(R.string.flash_discard_clip_alert_title)
            .setMessage(getText(R.string.flash_discard_clip_alert_message))
            .setPositiveButton(getText(R.string.common_discard)) { dialog, _ ->
                dialog.dismiss()
                onConfirm.invoke()
            }.setNegativeButton(getText(R.string.common_keep)) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }

    private fun observe() {
        viewModel.onDraftLoaded.observe(viewLifecycleOwner) {
            findNavController().navigateSafe(FlashRecordFragmentDirections.actionFlashRecordFragmentToFlashRecordFinalizeFragment())
        }
        viewModel.torchMode.observe(viewLifecycleOwner) {
            when(it) {
                FlashRecordViewModel.FlashTorchMode.ON -> {
                    binding.flashButton.apply {
                        setText(R.string.flash_control_torch_on)
                        icon = ContextCompat.getDrawable(requireContext(),R.drawable.ic_flash_record_flash_on)
                        camera?.cameraControl?.enableTorch(true)
                    }
                }
                FlashRecordViewModel.FlashTorchMode.OFF -> {
                    binding.flashButton.apply {
                        setText(R.string.flash_control_torch_off)
                        icon = ContextCompat.getDrawable(requireContext(),R.drawable.ic_flash_record_flash_off)
                        camera?.cameraControl?.enableTorch(false)
                    }
                }
                null -> {}
            }
        }

        viewModel.recordTimer.observe(viewLifecycleOwner) {
            binding.timerButton.text = when(it) {
                FlashRecordViewModel.FlashRecordTimer.SECONDS_3 -> getString(R.string.flash_control_timer_3s)
                FlashRecordViewModel.FlashRecordTimer.SECONDS_5 -> getString(R.string.flash_control_timer_5s)
                FlashRecordViewModel.FlashRecordTimer.SECONDS_10 -> getString(R.string.flash_control_timer_10s)
                null -> ""
            }
        }
        viewModel.onTimerSet.observe(viewLifecycleOwner) {
            it?.let {
                showToast(R.string.flash_timer_set_successfully,Toast.LENGTH_SHORT)
            }
        }

        viewModel.recordTimerBeat.observe(viewLifecycleOwner) {
            showTimerBeat(it)
        }

        viewModel.onPrepareRecording.observe(viewLifecycleOwner) {
            toggleRecordButton(true)
        }
        viewModel.onStartRecording.observe(viewLifecycleOwner) {
            Log.w("FRVM", "onStartRecording: starting record ${viewModel.recordingLimit.value}")
            startRecording(it, viewModel.recordingLimit.value?:0)
        }

        viewModel.onRecordingFinished.observe(viewLifecycleOwner) {
            toggleRecordButton(false)
            cancelRecordingCountDown()
        }
        viewModel.onGalleryVideoAdded.observe(viewLifecycleOwner) {
            findNavController().navigateSafe(FlashRecordFragmentDirections.actionFlashRecordFragmentToFlashRecordEditFragment())
        }
        viewModel.onRecordingCancelled.observe(viewLifecycleOwner) {
            cancelRecordingCountDown()
            toggleRecordButton(false)
        }

        viewModel.videoFileIsCorrupted.observe(viewLifecycleOwner){
            showToast(R.string.video_does_not_exist, Toast.LENGTH_SHORT)
        }
    }

    private fun toggleTimerSheet(show: Boolean) {
        timerSheetBehavior.state = if (show) BottomSheetBehavior.STATE_EXPANDED else BottomSheetBehavior.STATE_HIDDEN
    }

    private var activeRecording: Recording? = null

    private fun startRecording(rec: TempFlashMedia, limit: Int) {
        if (limit <= 0) return
        if (activeRecording!=null) return
        checkAudioPermission(binding.root) {
            val limitMillis = limit * 1000L

            val output = FileOutputOptions.Builder(rec.file).setDurationLimitMillis(limitMillis).build()

            val capture = videoCapture ?: return@checkAudioPermission

            activeRecording = capture.output.prepareRecording(requireContext(), output).withAudioEnabled().start(ContextCompat.getMainExecutor(requireContext())) { videoRecordEvent ->
                when (videoRecordEvent) {
                    is VideoRecordEvent.Start -> countDownRecording(limitMillis)
                    is VideoRecordEvent.Pause -> {}
                    is VideoRecordEvent.Resume -> {}
                    is VideoRecordEvent.Finalize -> {
                        Log.w("FREF", "observe: VideoRecordEvent.Finalize: ${videoRecordEvent.outputResults.outputUri} of ${videoRecordEvent.recordingStats.recordedDurationNanos/1000000}ms")
                        if (videoRecordEvent.error in listOf(VideoRecordEvent.Finalize.ERROR_NONE, VideoRecordEvent.Finalize.ERROR_DURATION_LIMIT_REACHED) ){
                            viewModel.finishRecording()
                            activeRecording = null
                        }
                        else {
                            Log.e("FREF", "observe: VideoRecordEvent.Finalize: Error - ${videoRecordEvent.error}")
                            viewModel.clearRecording()
                        }
                    }
                }
                // All events, including VideoRecordEvent.Status, contain RecordingStats.
                // This can be used to update the UI or track the recording duration.
//                val recordingStats = videoRecordEvent.recordingStats;
            }
        }
    }

    private fun stopRecording(after: () -> Unit = {}) {
        activeRecording?.let {
            it.stop()
            activeRecording = null
            after.invoke()
        }
    }

    override fun onPause() {
        super.onPause()
        stopRecording {
            viewModel.clearRecording()
            cameraProvider?.unbindAll()
        }
    }

    override fun onResume() {
        super.onResume()
        cameraProvider?.let {
            bindPreview(it,false)
        }
    }

    private var animator: ValueAnimator? = null

    private fun cancelRecordingCountDown() {
        animator?.let {
            it.cancel()
            binding.recordProgress.progress = 0
            animator = null
        }
    }
    private fun countDownRecording(millis: Long) {
        cancelRecordingCountDown()
        animator = ValueAnimator.ofInt(0, 100).apply {
            duration = millis
            interpolator = LinearInterpolator()
            addUpdateListener { animation ->
                binding.recordProgress.setProgress(animation.animatedValue as Int, true)
                if (animation.animatedFraction == 1f) {
                    binding.recordProgress.progress = 0
                    animator = null
                }
            }
            start()
        }
    }

    private fun Animator.onEnd(cb: () -> Unit) {
        addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                cb.invoke()
            }
        })
    }

    private var timerBeatAnimator: AnimatorSet? = null
    private fun showTimerBeat(sec: Int) {
        fun cleanup() {
            binding.timerBeat.isVisible = false
            timerBeatAnimator?.let {
                it.cancel()
                timerBeatAnimator = null
            }
        }
        cleanup()
        Log.w("FRVM", "showTimerBeat: $sec")

        val scaleXAnimator = ObjectAnimator.ofFloat(binding.timerBeat, "scaleX", 0.6f, 1f).apply {
            interpolator = OvershootInterpolator(2.5f)
        }
        val scaleYAnimator = ObjectAnimator.ofFloat(binding.timerBeat, "scaleY", 0.6f, 1f).apply {
            interpolator = OvershootInterpolator(2.5f)
        }
        val alphaAnimator = ObjectAnimator.ofFloat(binding.timerBeat, "alpha", 0f, 0.8f, 0.8f)

        timerBeatAnimator = AnimatorSet().apply {
            playTogether(scaleXAnimator, scaleYAnimator, alphaAnimator)
//            interpolator = OvershootInterpolator(2.5f)
            duration = 100
            binding.timerBeat.isVisible = true
            binding.timerBeat.text = sec.toString()
            start()
            onEnd {
                ObjectAnimator.ofFloat(binding.timerBeat, "alpha", 0.8f, 0f).apply {
                    interpolator = AccelerateInterpolator(1f)
                    startDelay = 200
                    duration = 500
                    start()
                    onEnd { cleanup() }
                }
            }
        }
    }

    private fun toggleRecordButton(start: Boolean) {
        binding.recordButton.apply {
            if (isAnimating) cancelAnimation()
            if (start) {
                activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                setMinProgress(0.0f)
                setMaxProgress(0.5f)
                speed = 2f
                performHapticFeedback(if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) HapticFeedbackConstants.CONFIRM else HapticFeedbackConstants.KEYBOARD_TAP)

                playAnimation()
            } else {
                activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                setMinProgress(0.5f)
                setMaxProgress(1.0f)
                speed = 2f
                performHapticFeedback(if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) HapticFeedbackConstants.REJECT else HapticFeedbackConstants.KEYBOARD_TAP)
                playAnimation()
            }
        }
    }

    private var camera: Camera? = null

    private var videoCapture: VideoCapture<Recorder>? = null

    private fun bindPreview(cameraProvider : ProcessCameraProvider, toggleLensFacing: Boolean = false) {
        cameraProvider.unbindAll()
        // Preview
        val preview : Preview = Preview.Builder()
            .build()
        preview.surfaceProvider = binding.previewView.surfaceProvider

        // Capture
        val qualitySelector = QualitySelector.fromOrderedList(
            listOf(Quality.FHD, Quality.HD, Quality.SD),
            FallbackStrategy.lowerQualityOrHigherThan(Quality.SD))
        val rec = Recorder.Builder()
            .setQualitySelector(qualitySelector)
            .build()
        val capture = VideoCapture.withOutput(rec)
        videoCapture = capture
        if (toggleLensFacing) {
            viewModel.toggleCamera()
        }

        val viewPort = binding.previewView.viewPort?:ViewPort.Builder(Rational(9,16),Surface.ROTATION_0).build()

        val useCaseGroup = UseCaseGroup.Builder()
            .addUseCase(preview)
            .addUseCase(capture)
            .setViewPort(viewPort)
            .build()
        val cameraSelector = if(viewModel.isFrontCamera.value==true) CameraSelector.DEFAULT_FRONT_CAMERA else CameraSelector.DEFAULT_BACK_CAMERA
        camera = cameraProvider.bindToLifecycle(viewLifecycleOwner, cameraSelector, useCaseGroup)
    }

    private fun animateCameraFlip() {
        ObjectAnimator.ofFloat(binding.flipAnimateIcon, "rotation", 0f, 180f).apply {
            interpolator = AnticipateOvershootInterpolator(1f)
            duration = 600
            val tintBackup = binding.flipButton.iconTint
            binding.flipButton.isEnabled = false
            binding.flipButton.iconTint = ColorStateList.valueOf(ContextCompat.getColor(requireContext(), R.color.transparent))
            binding.flipAnimateIcon.isVisible = true
            start()
            onEnd {
                binding.flipAnimateIcon.rotation = 0f
                binding.flipAnimateIcon.isVisible = false
                binding.flipButton.isEnabled = true
                binding.flipButton.iconTint = tintBackup
            }
        }
    }

    private fun triggerAutoFocus(x: Float, y: Float) {
        val cam = camera?: return
        val factory: MeteringPointFactory = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val display = activity?.display?: return
            DisplayOrientedMeteringPointFactory(
                display, cam.cameraInfo, binding.previewView.width.toFloat(), binding.previewView.height.toFloat()
            )
        } else {
            SurfaceOrientedMeteringPointFactory(
                binding.previewView.width.toFloat(), binding.previewView.height.toFloat()
            )
        }

        val autoFocusPoint = factory.createPoint(x, y)
        try {
            cam.cameraControl.startFocusAndMetering(
                FocusMeteringAction.Builder(
                    autoFocusPoint,
                    FocusMeteringAction.FLAG_AF
                ).apply {
                    //focus only when the user tap the preview
                    disableAutoCancel()
                }.build()
            )
            fun toggleFocusRing(show: Boolean, onEnd: () -> Unit = {}) {
                ObjectAnimator.ofFloat(binding.focusRing, "alpha", if(show) 0f else 1f, if(show) 1f else 0f).apply {
                    duration = if(show) 200 else 500
                    start()
                    onEnd { onEnd.invoke() }
                }
            }
            binding.focusRing.apply {
                translationX = x
                translationY = y
                isVisible = true
                toggleFocusRing(true) {
                    lifecycleScope.launch {
                        delay(1000)
                        toggleFocusRing(false) {
                            isVisible = false
                        }
                    }
                }
            }
        } catch (e: CameraInfoUnavailableException) {
            Log.d("ERROR", "cannot access camera", e)
        }
    }

    private fun selectMediaFromGallery() {
        selectVideoFromGalleryResult.launch(arrayOf("video/*"))
    }

    private val selectVideoFromGalleryResult =
        registerForActivityResult(ActivityResultContracts.OpenDocument()) { uri: Uri? ->
            uri?.let {
                viewModel.addVideo(it)
            }
        }

    override fun onStop() {
        activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        super.onStop()
    }

}