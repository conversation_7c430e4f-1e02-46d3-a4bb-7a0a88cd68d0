package com.app.messej.ui.home.businesstab.operations.tasks

import android.graphics.Typeface
import android.os.Bundle
import android.text.BidiFormatter
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.FragmentBusinessOtpBinding
import com.app.messej.ui.home.businesstab.operations.status.BusinessWorkStatusBaseFragment
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import `in`.aabhasjindal.otptextview.OTPListener

class BusinessOtpFragment : BottomSheetDialogFragment() {

    lateinit var binding: FragmentBusinessOtpBinding
    val viewModel: BusinessOperationTaskOneViewModel  by navGraphViewModels(R.id.navigation_task_one)
    private val commonViewModel: BusinessOperationsTaskViewModel by activityViewModels()
    private val args: BusinessOtpFragmentArgs by navArgs()
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_otp, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        when (args.otpRequestMode) {
            OTPRequestMode.VERIFY_MOBILE -> {
                val text = String.format(
                    getString(R.string.register_otp_label_info),
                    BidiFormatter.getInstance().unicodeWrap(UserInfoUtil.addPlusToCountryCode(viewModel.countryCode.value!!) + viewModel.enteredMobile.value?.replace(" ", ""))
                )
                binding.operationSendOtpTitle.text = text.highlightOccurrences(viewModel.taskOneData.value?.phone.toString()) { StyleSpan(Typeface.BOLD) }
            }

            OTPRequestMode.VERIFY_EMAIL -> {
                val text = String.format(getString(R.string.register_otp_label_info), BidiFormatter.getInstance().unicodeWrap(viewModel.enteredEmail.value))
                binding.operationSendOtpTitle.text = text.highlightOccurrences(viewModel.taskOneData.value?.email.toString()) { StyleSpan(Typeface.BOLD) }
            }

            else -> {}
        }

        binding.operationOtpView.otpListener = object : OTPListener {
            override fun onInteractionListener() {

            }
            override fun onOTPComplete(otp: String) {
                viewModel.setOTPNextButtonEnabled(true)
            }
        }

        binding.operationChangeEmail.setOnClickListener {
            viewModel.setBottomHideBottomSheet()
            findNavController().popBackStack()
        }

        binding.taskOtpNextButton.setOnClickListener {
            binding.operationOtpView.otp?.takeIf { it.isNotEmpty() }?.let { otp ->
                when (viewModel.otpRequestMode.value) {
                    OTPRequestMode.VERIFY_EMAIL -> viewModel.verifyMobileOTP(otp)
                    OTPRequestMode.VERIFY_MOBILE -> viewModel.verifyMobileOTP(otp)
                    else -> {}
                }

            } ?: run {
                Toast.makeText(requireActivity(), "Empty OTP", Toast.LENGTH_SHORT).show()
            }
        }


        binding.btnBottomSheetClose.setOnClickListener {
            dialog?.dismiss()
        }
        binding.taskOtpNextButton.setOnClickListener {
            binding.operationOtpView.otp?.takeIf { it.isNotEmpty() }?.let { otp ->
                when (viewModel.otpRequestMode.value) {
                    OTPRequestMode.VERIFY_EMAIL -> viewModel.verifyMobileOTP(otp)
                    OTPRequestMode.VERIFY_MOBILE -> viewModel.verifyMobileOTP(otp)
                    else -> {}
                }

            } ?: run {
                Toast.makeText(requireActivity(), "Empty OTP", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun observe() {
        viewModel.otpVerified.observe(viewLifecycleOwner) {
            it?.let {
//                findNavController().popBackStack(R.id.navigation_task_one,false)
                setFragmentResult(BusinessWorkStatusBaseFragment.STATUS_CHANGE_REQUEST_KEY, bundleOf(BusinessWorkStatusBaseFragment.STATUS_CHANGE_RESULT_KEY to true))
                if (it) {
                    Toast.makeText(requireContext(), getString(R.string.profile_update_success_toast), Toast.LENGTH_SHORT).show()
                    findNavController().popBackStack()
                    binding.bottomSheetCard.visibility = View.GONE
                    commonViewModel.getBusinessOperations()
                    viewModel.setPaypalVerified(true)
                    viewModel.setNextButtonState()
                    viewModel.resetOtpVerifiedMessage()
//                    dismiss()
                    findNavController().navigateUp()
                }
            }
        }

    }


}