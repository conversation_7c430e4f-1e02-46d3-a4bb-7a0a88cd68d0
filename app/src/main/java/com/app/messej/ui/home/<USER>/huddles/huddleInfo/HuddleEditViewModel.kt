package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import android.net.Uri
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.Category
import com.app.messej.data.model.api.huddles.HuddleInfo
import com.app.messej.data.model.api.huddles.HuddleLanguage
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.TribeEditType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class HuddleEditViewModel(application: Application) : AndroidViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())
    private val accountRepo = AccountRepository(getApplication())
    private val chatRepo = ChatRepository(getApplication())

    private val huddleID = MutableLiveData<Int?>(null)

    val onTribeDialogueDismiss= LiveEvent<Boolean>()
    private val _tribeEditType = MutableLiveData(TribeEditType.EDIT_NAME)
    val tribeEditType: LiveData<TribeEditType> = _tribeEditType

    private val _isTribeNextButtonEnabled = MutableLiveData(false)
    val isTribeNextButtonEnabled: LiveData<Boolean> = _isTribeNextButtonEnabled

    private val _isTribeEdit = MutableLiveData(false)
    val isTribeEdit: LiveData<Boolean> = _isTribeEdit

    private val _tribeUseUserProfilePhoto = MutableLiveData(false)
    val isTribeUserProfilePhoto: LiveData<Boolean> = _tribeUseUserProfilePhoto

    companion object {
        enum class NameError {
            NONE, LT_MIN, GT_MAX
        }
        private const val NAME_MIN_LENGTH = 3
        private const val NAME_MAX_LENGTH = 50
        private const val DEFAULT_LANGUAGE="english"
    }

    fun setHuddleId(id: Int) {
        if (huddleID.value == id) return
        getHuddleDetails(id)
        huddleID.postValue(id)
    }

    private val _huddleLanguageList : MutableLiveData<MutableList<HuddleLanguage>> = MutableLiveData(null)
    val huddleLanguageList: LiveData<MutableList<HuddleLanguage>> = _huddleLanguageList

    private val _huddleInfo = MutableLiveData<HuddleInfo>(null)
    val huddle: LiveData<HuddleInfo?> = _huddleInfo

    val name = MutableLiveData<String>()
    val bio = MutableLiveData<String?>()
    val category = MutableLiveData<Int?>()
    val image = MutableLiveData<Boolean>(false)
    val huddleLanguage = MutableLiveData<String?>()

    val requestToJoin = MutableLiveData<Boolean>(false)
    val didEnterName = MutableLiveData<Boolean>(false)

    val onHuddleInfoError = LiveEvent<String?>()
    val huddleUpdated = LiveEvent<Boolean>()

    /**
     * Edit huddle information
     */

    val userHasProfilePic = accountRepo.userProfileFlow.map { it?.profilePhoto!=null }.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val _editHuddleInfoStageValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun validateEditHuddleInfoStage() {
            _editHuddleInfoStageValid.postValue(
                name.value.orEmpty().isNotBlank() &&
                        _nameError.value == NameError.NONE &&
                        category.value != null && huddleLanguage.value!=null&&
                        hasFieldsChanged())
        }
        med.addSource(name) { validateEditHuddleInfoStage() }
        med.addSource(bio) { validateEditHuddleInfoStage() }
        med.addSource(category) { validateEditHuddleInfoStage() }
        med.addSource(huddleLanguage){validateEditHuddleInfoStage()}
        med.addSource(image) {validateEditHuddleInfoStage() }
        med
    }

    fun onSameProfileClick(){
         _tribeUseUserProfilePhoto.postValue(true)
        _tribeEditType.postValue(TribeEditType.EDIT_CATEGORY)
    }
    fun  setTribeButtonEnabled(isEnabled:Boolean){
        _isTribeNextButtonEnabled.postValue(isEnabled)
    }

    val editHuddleInfoStageValid: LiveData<Boolean> = _editHuddleInfoStageValid

    private fun hasFieldsChanged(): Boolean {
        return name.value != _huddleInfo.value?.name ||
                bio.value != _huddleInfo.value?.about ||
                category.value != _huddleInfo.value?.categoryId ||
                huddleLanguage.value != _huddleInfo.value?.huddleLanguage ||
                image.value != false
    }

    private fun checkHuddleName() {
        _huddleNameValid.postValue(
            name.value.orEmpty().isNotBlank() && _nameError.value == NameError.NONE
        )
    }

    private val _huddleNameValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        med.addSource(name) { checkHuddleName() }
        med.addSource(didEnterName) {checkHuddleName()}
        med
    }

    val huddleNameValid: LiveData<Boolean> = _huddleNameValid

    private val _nameError: MediatorLiveData<NameError> by lazy {
        val med: MediatorLiveData<NameError> = MediatorLiveData(NameError.NONE)
        med.addSource(name) { validateName() }
        med.addSource(didEnterName) { validateName() }
        med
    }
    val nameError: LiveData<NameError> = _nameError

    private fun getHuddleDetails(id: Int) {
        _profileInfoLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            getHuddleInfo(id)
            getHuddleCategoriesList()
            getHuddleLanguageList()
            _profileInfoLoading.postValue(false)
        }

    }
    private fun validateName() {
        if(didEnterName.value==true) {
            name.value.orEmpty().let {
                if (it.length < NAME_MIN_LENGTH)  _nameError.postValue(NameError.LT_MIN)
                else if (it.length > NAME_MAX_LENGTH) _nameError.postValue(NameError.GT_MAX)
                else _nameError.postValue(NameError.NONE)
            }
        } else _nameError.postValue(NameError.NONE)
    }

    private val _profileInfoLoading = MutableLiveData(true)
    val profileInfoLoading: LiveData<Boolean> = _profileInfoLoading

    private val _saveHuddleLoading = MutableLiveData(false)
    val saveHuddleLoading: LiveData<Boolean> = _saveHuddleLoading

    private val _saveButtonEnable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            med.postValue(_saveHuddleLoading.value==false && _huddleNameValid.value==true && _editHuddleInfoStageValid.value == true)
        }
        med.addSource(_saveHuddleLoading) { check() }
        med.addSource(_huddleNameValid) { check() }
        med.addSource(_editHuddleInfoStageValid) { check() }
        med
    }
    val saveButtonEnable: LiveData<Boolean> = _saveButtonEnable
    private var imageCaptureTempFile: File? = null
    var     finalImage: File? = null

    private val _localDp = MutableLiveData<String>()

    val huddleDp : MediatorLiveData<String> by lazy {
        val med = MediatorLiveData<String>()
        fun selectDp() {
            med.postValue(_localDp.value?: _huddleInfo.value?.thumbnail)
        }
        med.addSource(_localDp) { selectDp() }
        med.addSource(_huddleInfo) { selectDp() }
        med
    }

    private suspend fun getHuddleInfo(huddleId: Int) {
        when (val result: ResultOf<HuddleInfo> = huddleRepo.getFullHuddleInfo(huddleId)) {
            is ResultOf.APIError -> {
                onHuddleInfoError.postValue(result.error.message)
            }
            is ResultOf.Error -> {
                onHuddleInfoError.postValue(null)
            }
            is ResultOf.Success -> {
                val huddle = result.value
                requestToJoin.postValue(huddle.requestToJoin)
                name.postValue(huddle.name)
                bio.postValue(huddle.about)
                category.postValue(huddle.categoryId)
                huddleLanguage.postValue(huddle.huddleLanguage)
                _huddleInfo.postValue(huddle)
                image.postValue(false)
            }
        }
    }

     private fun getHuddleLanguageList() {
        _profileInfoLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO){
            when(val result = huddleRepo.getLanguages()){
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {
                }
                is ResultOf.Success -> {
                    val languageList = result.value.toMutableList()
                    _huddleLanguageList.postValue(languageList)
                    if (huddleLanguage.value==null) {
                        huddleLanguage.postValue(DEFAULT_LANGUAGE)
                    }
                }
            }
            _profileInfoLoading.postValue(false)
        }
    }

    private val _huddleCategoryList : MutableLiveData<MutableList<Category>> = MutableLiveData(null)
    val huddleCategoryList: LiveData<MutableList<Category>> = _huddleCategoryList

    private suspend fun getHuddleCategoriesList() {
        val type = if (huddle.value?.isPrivate == true) HuddleType.PRIVATE else HuddleType.PUBLIC
        when (val result = huddleRepo.getHuddleCategories(type)) {
            is ResultOf.APIError -> {

            }

            is ResultOf.Error -> {

            }

            is ResultOf.Success -> {
                _huddleCategoryList.postValue(result.value.categories)
            }
        }
    }

    fun setLanguage(language: HuddleLanguage?) {
        huddleLanguage.value=language?.englishName
    }

    fun onTribeClick(){
        when(_tribeEditType.value){
            TribeEditType.EDIT_NAME -> {
                _isTribeEdit.postValue(true)
                _tribeEditType.postValue(TribeEditType.EDIT_DP)
            }
            TribeEditType.EDIT_DP -> {
                _tribeEditType.postValue(TribeEditType.EDIT_CATEGORY)
            }
            TribeEditType.EDIT_CATEGORY -> {
                _tribeEditType.postValue(TribeEditType.EDIT_NAME)
                updateHuddleDetails()
            }
            else -> {}
        }

    }

    fun setCategoryId(categoryId: Int) {
        category.postValue(categoryId)
    }

    fun updateHuddleDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            _saveHuddleLoading.postValue(true)
            val compressed = finalImage?.let { huddleRepo.compressImage(it) }?: null
            when (val result: ResultOf<PublicHuddle> = huddleRepo.updateHuddle(
                huddleId = huddle.value?.id!!,
                compressed,
                name = name.value,
                bio = bio.value,
                category = category.value,
                language=huddleLanguage.value,
                requestToJoin = huddle.value?.requestToJoin,
                participantShare = huddle.value?.participantShare,
                isRemoveGroupPhoto =  compressed != null,
                setProfilePicAsDP = _tribeUseUserProfilePhoto.value!!
            )) {
                is ResultOf.APIError -> {
                    _saveHuddleLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    _saveHuddleLoading.postValue(false)

                }
                is ResultOf.Success -> {
                    huddleUpdated.postValue(true)
                    _saveHuddleLoading.postValue(false)
                    if(_isTribeEdit.value==true){
                        onTribeDialogueDismiss.postValue(true)
                        _isTribeEdit.postValue(false)
                        _tribeUseUserProfilePhoto.postValue(false)
                    }
                    finalImage = null
                }
            }
        }
    }

    suspend fun getImageUriForCapture(): Uri = withContext(Dispatchers.IO) {
        val file = chatRepo.createTempImageFile()
        imageCaptureTempFile = file
        chatRepo.getUriForFile(file)
    }

    fun addCapturedImage(){
        viewModelScope.launch {
            imageCaptureTempFile?.let { file ->
                cropAttachedMedia()
            }
        }
    }

    fun addImage(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            imageCaptureTempFile = file
            cropAttachedMedia()
        }
    }

    fun addCroppedImage(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            finalImage = file
            imageCaptureTempFile = null
            _localDp.postValue(file.absolutePath)
            image.postValue(true)
        }
    }

    fun onCropCancelled() {
        imageCaptureTempFile = null
    }

    val onTriggerCrop = LiveEvent<Pair<Uri,Uri>>()

    private fun cropAttachedMedia() {
        viewModelScope.launch(Dispatchers.IO) {
            val media = imageCaptureTempFile?: return@launch
            val src = chatRepo.getUriForFile(media)
            val dest = chatRepo.getUriForFile(chatRepo.createTempImageFile())
            onTriggerCrop.postValue(Pair(src,dest))
        }
    }
}