package com.app.messej.ui.home.gift

import androidx.viewbinding.ViewBinding
import com.app.messej.data.model.api.gift.GiftResponse
import com.app.messej.databinding.LayoutGiftListingHeaderFreeBinding
import com.app.messej.databinding.LayoutGiftListingHeaderPremiumBinding
import com.app.messej.databinding.LayoutGiftListingHeaderResidentBinding
import java.math.BigDecimal
import java.util.Locale

sealed class GiftHeaderHolder {
    abstract val binding: ViewBinding
    abstract var giftList: GiftResponse?


    class PremiumHeader(override val binding: LayoutGiftListingHeaderPremiumBinding,private val listener: ClickListener) : GiftHeaderHolder() {

        override var giftList: GiftResponse?
            get() = binding.gift
            set(value) {
                binding.gift = value
                value?.receivedCoins?.let {
                    binding.coinBalance = String.format(Locale.US,"%.2f", BigDecimal(it))
                }
                value?.flax?.let {
                    binding.flaxBalance = String.format(Locale.US, "%.2f", BigDecimal(it))
                }
            }

        interface ClickListener {
            fun convertCoinToFlax()
            fun convertFlaxToCoin()

        }

        init {
            binding.actionCoinToFlax.setOnClickListener {
                listener.convertCoinToFlax()
            }
            binding.actionFlaxToCoin.setOnClickListener {
                listener.convertFlaxToCoin()
            }

        }




    }

    class ResidentHeader(override val binding: LayoutGiftListingHeaderResidentBinding, private val listener: ClickListener) : GiftHeaderHolder() {

        override var giftList: GiftResponse?
            get() = binding.gift
            set(value) {
                binding.gift = value
                value?.receivedCoins?.let {
                    binding.coinBalance = String.format(Locale.US,"%.2f", BigDecimal(it))
                }
                value?.flax?.let {
                    binding.flaxBalance = String.format(Locale.US, "%.2f", BigDecimal(it))
                }
            }

        interface ClickListener {
            fun convertCoinToFlax()
            fun convertFlaxToCoin()

        }

        init {
            binding.actionCoinToFlax.setOnClickListener {
                listener.convertCoinToFlax()
            }
            binding.actionFlaxToCoin.setOnClickListener {
                listener.convertFlaxToCoin()
            }

        }




    }



    class FreeHeader(override val binding: LayoutGiftListingHeaderFreeBinding) : GiftHeaderHolder() {
        override var giftList: GiftResponse?
            get() = binding.gift
            set(value) {
                binding.gift = value
                value?.receivedCoins?.let {
                    binding.coinBalance = String.format(Locale.US,"%.2f", BigDecimal(it))
                }
                value?.flax?.let {
                    binding.flaxBalance = String.format(Locale.US, "%.2f", BigDecimal(it))
                }
            }

    }
}