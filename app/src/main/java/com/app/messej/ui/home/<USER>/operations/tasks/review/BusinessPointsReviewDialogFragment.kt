package com.app.messej.ui.home.businesstab.operations.tasks.review

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentBusinessPointsReviewDialogBinding
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.businesstab.HomeBusinessFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class BusinessPointsReviewDialogFragment : BottomSheetDialogFragment() {
    private val commonViewModel: CommonHomeViewModel by activityViewModels()
    private val viewModel:BusinessWithDrawViewModel by activityViewModels ()
    private lateinit var binding: FragmentBusinessPointsReviewDialogBinding


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_points_review_dialog, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = false
        setUpOnClickListeners()
        checkEligibilityForPointsReview()
        observe()
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState)
    }

    private fun observe() {
       viewModel.onPayoutSuccess.observe(viewLifecycleOwner){
           it?.let {
               if(it){
                   this.dialog?.dismiss()
               }
           }
       }
    }

    private fun setUpOnClickListeners() {


        binding.actionTask.setOnClickListener {
            if(commonViewModel.user?.premium==true){
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalHomeBusinessFragment(destination = HomeBusinessFragment.TAB_OPERATIONS))
            }else{
                findNavController().navigateSafe(BusinessPointsReviewDialogFragmentDirections.actionBusinessPointsReviewDialogFragmentToBusinessWorkStatusStandAloneFragment())
            }
        }

        binding.actionClose.setOnClickListener { dismiss() }
        binding.actionLater.setOnClickListener { dismiss() }
        binding.actionSubmit.setOnClickListener {
            findNavController().navigateSafe(BusinessPointsReviewDialogFragmentDirections.actionBusinessOperationDialogueToWithDrawFragment())
        }
        binding.actionPpRegulations.setOnClickListener {
            //TODO fix error before build
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPolicyFragment(DocumentType.PP_RULES_REGULATIONS, true))
    }
    }
    private fun checkEligibilityForPointsReview() {
        viewModel.payoutEligibility.value.let {
            if (it?.eligibility == true) {
                    binding.layoutNotEligible.isVisible = false
                    binding.layoutEligible.isVisible = true
                } else {
                    binding.layoutNotEligible.isVisible = true
                    binding.layoutEligible.isVisible = false
//                    binding.pointsReviewNotEligibleTitle.text = it?.reason
                }
            }

        }
    }
