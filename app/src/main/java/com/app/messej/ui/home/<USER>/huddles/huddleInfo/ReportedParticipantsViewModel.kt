package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.repository.HuddlesRepository

class ReportedParticipantsViewModel(application: Application): AndroidViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())

    val showCompactLoading = MutableLiveData(false)

    private val huddleID = MutableLiveData<Int?>(null)
    private val reportID = MutableLiveData<Int?>(null)
    private val reportType = MutableLiveData<ReportToManagerType>(null)

    private val _reportedParticipantsCount = MutableLiveData<Int>(0)
    val reportedParticipantsCount: LiveData<Int> = _reportedParticipantsCount

    fun setReportId(huddleId: Int, reportId: Int, count: Int = 0, reportedType: ReportToManagerType) {
        huddleID.value = huddleId
        reportID.value = reportId
        reportType.value = reportedType
        _reportedParticipantsCount.value = count
    }

    val reportedParticipantsList = reportID.switchMap {
        reportID.value ?: return@switchMap null
        huddleRepo.getHuddlesReportedMessagesParticipants(
            huddleId = huddleID.value?: return@switchMap null,
            reportedId = reportID.value?: return@switchMap null,
            reportType = reportType.value?: return@switchMap null
        ).liveData.cachedIn(viewModelScope)
    }
}