package com.app.messej.ui.home.gift

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.gift.GiftPointToFlaxConvertRequest
import com.app.messej.data.model.api.gift.GiftPointToFlaxConvertResponse
import com.app.messej.data.model.api.gift.GiftResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.GiftConversion
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.GiftRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class GiftConvertViewModel(application: Application) : AndroidViewModel(application) {
    private val giftRepository = GiftRepository(application)
    val businessRepo = BusinessRepository(application)
    val accountRepo = AccountRepository(application)
    val profRepo = ProfileRepository(application)

    companion object {
        enum class ValueError {
            NONE, PT_MAX, COIN_MAX, FLAX_MAX
        }

    }

    private val value = MutableLiveData("")  //old point
    val convertedValue = MutableLiveData("") //old flax
    val didEnterValue = MutableLiveData(false)

    val coinToFlaxFormatError = MutableLiveData(false)
    val valueMaxConversionLimit = MutableLiveData<Double>()
    val coinFlaxConversionValue = MutableLiveData<Double>()
    val successMessage = LiveEvent<String>()
    val errorMessage = LiveEvent<String>()
    val dialogDismiss = LiveEvent<Boolean>()

    private val _conversionMode = MutableLiveData<GiftConversion?>(null)
    val conversionMode: LiveData<GiftConversion?> = _conversionMode

    private val _giftResponse = MutableLiveData<GiftResponse?>(null)
    val giftResponse: LiveData<GiftResponse?> = _giftResponse

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    private val _isButtonEnabled = MutableLiveData<Boolean?>(true)
    val isButtonEnabled: LiveData<Boolean?> = _isButtonEnabled


    private val minValue = value.map {
        if (it.isNullOrEmpty()) return@map 0
        else (it.toDouble()) >= (valueMaxConversionLimit.value ?: 0.0)
    }

    private val _valueValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun checkHuddleName() {
            _valueValid.postValue(
                value.value.orEmpty().isNotBlank() && minValue.value == true && validateFlaxAmount() == true
            )
        }
        med.addSource(minValue) { checkHuddleName() }
        med
    }
    val valueValid: LiveData<Boolean> = _valueValid

    private val _valueError: MediatorLiveData<ValueError> by lazy {
        val med: MediatorLiveData<ValueError> = MediatorLiveData(ValueError.NONE)
        fun check() {
            if (didEnterValue.value == false) {
                med.postValue(ValueError.NONE)
            } else if (minValue.value == false) {
                med.postValue(ValueError.PT_MAX)
            } else med.postValue(ValueError.NONE)

        }
        med.addSource(minValue) { check() }
        med.addSource(didEnterValue) { check() }
        med
    }
    val valueError: LiveData<ValueError> = _valueError

    val convertButtonEnable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            med.postValue(_valueValid.value == true && coinToFlaxFormatError.value == false && !convertedValue.value.isNullOrEmpty() && _isButtonEnabled.value == true)
        }
        med.addSource(_valueValid) { check() }
        med.addSource(coinToFlaxFormatError) { check() }
        med.addSource(convertedValue) { check()}
        med.addSource(_isButtonEnabled) { check() }
        med
    }

    val conversionRate: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            if (_giftResponse.value?.userGiftData != null) {

                val conversionValue = conversionMode.value

                if (conversionValue != null && conversionValue == GiftConversion.COIN_TO_FLAX) {
                    giftResponse.value?.userGiftData?.let { updateMinConversionValue(it.minimumCoinConversion) }
                } else {
                    giftResponse.value?.userGiftData?.let { updateMinConversionValue(it.minimumFlaxConversion) }
                }
                giftResponse.value?.userGiftData?.let { setCoinFlaxConversionValue(it.coinFlaxConversionValue) }
            }
        }
        med.addSource(_giftResponse) { check() }
        med.addSource(_conversionMode) { check() }
        med
    }

    fun giftConversionAction(isC2F: Boolean) {
        _isButtonEnabled.value = false
        viewModelScope.launch(Dispatchers.IO) {
            val giftPointToFlaxConvertRequest = if (isC2F) {
                GiftPointToFlaxConvertRequest(action = "p2f", coins = value.value?.toInt())
            } else {
                GiftPointToFlaxConvertRequest(action = "f2p", flax = value.value?.toDouble())
            }

            when (val result: ResultOf<APIResponse<GiftPointToFlaxConvertResponse>> = giftRepository.giftConversion(giftPointToFlaxConvertRequest)) {
                is ResultOf.Success -> {
                    val success = if (isC2F) {
                        result.value.result.convertedFlax.toString()
                    } else {
                        result.value.result.convertedCoins.toString()
                    }
                    clearData()
                    successMessage.postValue(success)
                    dialogDismiss.postValue(true)
                    profRepo.getAccountDetails()
                    _isButtonEnabled.postValue(true)
                }

                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                    dialogDismiss.postValue(true)
                    _isButtonEnabled.postValue(true)
                }

                is ResultOf.Error -> {
                    // Handle other errors
                    _isButtonEnabled.postValue(true)
                }
            }

        }
    }

    fun setValue(data: String?) {
        if (data == null) return
        value.value = data
        // Handle the updated value here
        if (data.isNotEmpty()) {
            val inDouble = data.toDouble()
            val conversionMode = conversionMode.value

            val conversionValue = coinFlaxConversionValue.value?: return
            when (conversionMode) {
                GiftConversion.COIN_TO_FLAX -> {
                    if (inDouble % 100.0 == 0.0) {
                        coinToFlaxFormatError.postValue(false)
                        convertedValue.postValue(inDouble.div(conversionValue).formatDecimalWithRemoveTrailingZeros())
                    } else {
                        coinToFlaxFormatError.postValue(true)
                        convertedValue.postValue("")
                    }
                }
                GiftConversion.FLAX_TO_COIN -> {
                    coinToFlaxFormatError.postValue(false)
                    convertedValue.postValue(inDouble.times(conversionValue).formatDecimalWithRemoveTrailingZeros())
                }
                else -> {}
            }
        } else {
            coinToFlaxFormatError.postValue(false)
        }
    }

    fun clearText() {
        coinToFlaxFormatError.postValue(false)
        convertedValue.postValue("")
    }

    fun updateMinConversionValue(minValue: Double) {
        valueMaxConversionLimit.postValue(minValue)
    }

    fun setConversionMode(mode: GiftConversion) {
        _conversionMode.postValue(mode)
    }

    fun setCoinFlaxConversionValue(coinFlaxConversion: Double) {
        coinFlaxConversionValue.postValue(coinFlaxConversion)
    }

    fun setGiftResponse(response: GiftResponse?) {
        _giftResponse.postValue(response)
    }

    private fun clearData() {
        convertedValue.postValue("")
        value.postValue("")
    }

    private val _CoinFlaxError = MediatorLiveData(ValueError.NONE)
    val coinFlaxError: LiveData<ValueError> = _CoinFlaxError

    private fun validateFlaxAmount(): Boolean {
        val conversionValue = conversionMode.value
        if (conversionValue != null && conversionValue == GiftConversion.COIN_TO_FLAX) {
            if (value.value!!.toDouble() > (_accountDetails.value?.currentCoin ?: 0.0)) {
                _CoinFlaxError.postValue(ValueError.COIN_MAX)
                return false
            } else {
                _CoinFlaxError.postValue(ValueError.NONE)
                return true
            }
        } else {
            if (value.value!!.toDouble() > (accountDetails.value?.currentFlix ?: 0.0)) {
                _CoinFlaxError.postValue(ValueError.FLAX_MAX)
                return false
            } else {
                _CoinFlaxError.postValue(ValueError.NONE)
                return true
            }
        }
    }

}