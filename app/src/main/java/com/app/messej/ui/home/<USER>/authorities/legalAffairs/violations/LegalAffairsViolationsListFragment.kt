package com.app.messej.ui.home.publictab.authorities.legalAffairs.violations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.databinding.FragmentViolationsListBinding
import com.app.messej.ui.home.publictab.authorities.AuthoritiesUtils
import com.app.messej.ui.home.publictab.authorities.legalAffairs.CaseDetailsBottomSheet.Companion.CASE_DETAIL_REQUEST_KEY
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsCommonViewModel
import com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsFragmentDirections
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils

class LegalAffairsViolationsListFragment : Fragment() {

    private lateinit var listBinding: FragmentViolationsListBinding
    private lateinit var mAdapter: LegalAffairsCaseListAdapter
    private val viewModel : LegalAffairsCommonViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        listBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_violations_list, container, false)
        listBinding.lifecycleOwner = viewLifecycleOwner
        return listBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {
        initAdapter()
        observe()
    }

    private fun initAdapter() {
        mAdapter = LegalAffairsCaseListAdapter(object : LegalAffairsCaseListAdapter.ItemClickListener {
            override fun getUserId() = viewModel.user.id
            override fun onClick(case: LegalRecordsResponse.ReportCase) {
                findNavController().navigateSafe(
                    LegalAffairsFragmentDirections.actionLegalAffairsFragmentToCaseDetailBottomSheet(case.id)
                )
            }
        })

        val linearLayoutManager = LinearLayoutManager(context)
        listBinding.recyclerView.apply {
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }

        mAdapter.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                listBinding.multiStateView.viewState = state
            }
        }

        AuthoritiesUtils.setupListEmptyView(
            multiStateView = listBinding.multiStateView
        )
    }

    private fun observe() {
        viewModel.violationList.observe(viewLifecycleOwner) {
            mAdapter.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.currentSelectedLegalAffairsSubTab.observe(viewLifecycleOwner) {
            // Used to refresh the open, closed, fined, statistics list when returning to the tab violations.
            if (it == LegalAffairTabs.Violations)  mAdapter.refresh()
        }

        setFragmentResultListenerOnActivity(CASE_DETAIL_REQUEST_KEY) { _, _ ->
            mAdapter.refresh()
        }
    }

}