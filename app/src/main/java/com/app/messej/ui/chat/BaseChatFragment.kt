package com.app.messej.ui.chat

import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.enums.AttachmentSource
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.FragmentChatScreenBinding
import com.app.messej.databinding.LayoutListStateEmptyChatBinding
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.DataFormatHelper.pxToDp
import com.app.messej.ui.utils.FragmentShareExtensions.getExternalShareResult
import com.app.messej.ui.utils.PermissionsHelper.checkAudioPermission
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.app.messej.ui.utils.TextFormatUtils.enableTextFormatting
import com.devlomi.record_view.OnRecordListener
import com.devlomi.record_view.RecordButton
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.launch

abstract class BaseChatFragment: BaseChatDisplayFragment() {

    lateinit var binding: FragmentChatScreenBinding

    abstract override val viewModel: BaseChatViewModel

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_chat_screen, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.chatList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView?
        get() = binding.multiStateView

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        setEmptyView()
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {

        binding.chatSendButton.setOnClickListener {
            viewModel.chatMedia.value?.let {
                if (it.mediaType in arrayOf(MediaType.IMAGE,MediaType.VIDEO)) viewModel.clearChatMedia()
            }
            viewModel.prepareAndSendMessage()
            hideFab()
        }
        binding.chatAttachButton.setOnClickListener {
            showAttachDialog()
        }

        setupRecordView()

        binding.replyLayout.replyClearButton.setOnClickListener {
            viewModel.clearReplyTo()
        }

        binding.scrollToBottomButton.setOnClickListener {
            hideFab()
            binding.chatList.layoutManager?.scrollToPosition(0)
        }

        if (viewModel.enableTextFormatting) {
            binding.chatInput.enableTextFormatting(requireActivity(),viewModel)
        }

        binding.chatList.addOnScrollListener(object: RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState==RecyclerView.SCROLL_STATE_SETTLING || newState==RecyclerView.SCROLL_STATE_IDLE) {
                    val offset = recyclerView.computeVerticalScrollOffset()
                    val extent = recyclerView.computeVerticalScrollExtent()
                    val range = recyclerView.computeVerticalScrollRange()
                    val pixels = range - extent - offset
                    val dps = pixels.pxToDp(requireContext())
                    Log.d("BCF", "onScrolled: $pixels -> $dps")
                    val fab = binding.scrollToBottomButton
                    if (dps>1000 && !fabShown) {
                        fab.show()
                        fabShown = true
                    }
                    else if (dps<500 && fabShown) {
                        hideFab()
                    }
                }
            }
        })

        getExternalShareResult { message, mediaType, fileUri ->
            viewModel.handleExternalShare(message = message, mediaType = mediaType, fileUri = fileUri)
        }
    }

    interface EDSProvider {
        @get:StringRes val message: Int
        @get:DrawableRes val image: Int
        @get:StringRes val action: Int?
        fun onActionClick() {}
    }

    open val edsProvider: EDSProvider = object: EDSProvider {
        override val message = R.string.chat_eds_message
        override val image = R.drawable.im_eds_chat
        override val action = null
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?: return
        val emptyViewBinding = LayoutListStateEmptyChatBinding.bind(emptyView)
        emptyViewBinding.apply {
            edsEmptyImage.setImageResource(R.drawable.im_eds_chat)
            edsEmptyMessage.text = resources.getString(R.string.chat_eds_message)
//            action = null
//            edsEmptyAction.setOnClickListener {
//                edsProvider.onActionClick()
//            }
        }
//        binding.multiStateView.setViewForState(emptyViewBinding.root,MultiStateView.ViewState.EMPTY)
    }

    private var fabShown = false

    private fun hideFab() {
        if(binding.scrollToBottomButton.isShown) {
            binding.scrollToBottomButton.hide()
            fabShown = false
        }
    }

    private fun observe() {

        setFragmentResultListener(ChatAttachSourceFragment.ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when(ChatAttachSourceFragment.getSource(bundle)) {
                AttachmentSource.GALLERY -> selectMediaFromGallery()
                AttachmentSource.CAMERA_PHOTO -> takeImage()
                AttachmentSource.CAMERA_VIDEO -> takeVideo()
                AttachmentSource.LOCATION -> navigateToLocationSelect()
                AttachmentSource.DOCUMENT -> selectDocument()
                null -> {}
            }
        }

        viewModel.onAddChatMedia.observe(viewLifecycleOwner) {
            when(it) {
                MediaType.IMAGE -> showImagePreview()
                MediaType.AUDIO -> {}
                MediaType.VIDEO -> showVideoPreview()
                MediaType.DOCUMENT -> showDocumentPreview()
                else -> {}
            }
        }

        viewModel.canReplyToSelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_reply)?.isVisible = it && !isAdminMessage
        }

        viewModel.canReportSelection.observe(viewLifecycleOwner) {
            actionMode?.menu?.findItem(R.id.action_report)?.isVisible = it
        }

        viewModel.chatReplyTo.observe(viewLifecycleOwner) {
            it?.let { reply ->
                binding.replyLayout.prepare(reply)
            }
        }

        viewModel.audioRecording.observe(viewLifecycleOwner) {
            if (it) {
                activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            } else {
                activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
        }
        
//        viewModel.onAudioRecordingLimitReached.observe(viewLifecycleOwner) {
//            Log.w("REC", "onAudioRecordingLimitReached")
//            if(it) binding.recordView.finishRecord()
//        }

        viewModel.canDeleteSelectionForEveryone.observe(viewLifecycleOwner){
            if (it) 1 else 0
        }

        viewModel.onAudioLessThanASecond.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), getString(R.string.chat_mic_hold_message), Toast.LENGTH_LONG).show()
        }
    }

    override val provideAdapter: ChatAdapter
        get() = ChatAdapter(layoutInflater, viewModel.user.id, allowReplySwipe(), this)

    override fun allowReplySwipe() = true

    override fun onItemLongClick(item: AbstractChatMessage, position: Int) {
        if (viewModel.enableChatInteraction.value!=true) return
        super.onItemLongClick(item, position)
    }

    override fun onMediaUpload(msg: AbstractChatMessageWithMedia, position: Int) {
        if (viewModel.selectMessage(msg.message,position)) return
        viewModel.onTriggerUpload(msg)
    }

    override fun onItemReply(item: AbstractChatMessage, position: Int) {
        Log.d("BCF", "onItemReply: replying to - ${item.displayMessage}")
        if (viewModel.enableChatInteraction.value!=true) return
        viewModel.replyToItem(item)
    }

    override fun onItemLike(item: AbstractChatMessage, position: Int) {
        if (viewModel.enableChatInteraction.value!=true) return
        viewModel.likeItem(item)
    }

    protected open fun canAcceptSharedMedia(mediaType: MediaType): Boolean = true

    abstract fun showAttachDialog()

    abstract fun showImagePreview()

    abstract fun showVideoPreview()
    abstract fun showDocumentPreview()

    protected abstract fun navigateToLocationSelect()

    override fun onPause() {
        super.onPause()
        if (viewModel.audioRecording.value==true) {
            binding.recordView.cancelRecord()
        }
        viewModel.cancelAudioRecording()
    }

    private val takeImageResult =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { isSuccess ->
            if (isSuccess) {
                viewModel.addCapturedImage()
            }
        }

    private fun takeImage() {
        checkCameraPermission(bindingRoot) {
            lifecycleScope.launch {
                val uri = viewModel.getImageUriForCapture()
                takeImageResult.launch(uri)
            }
        }
    }

    private val selectImageOrVideoFromGalleryResult =
        registerForActivityResult(ActivityResultContracts.OpenDocument()) { uri: Uri? ->
//        registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri: Uri? ->
            uri?.let {
                viewModel.addMedia(uri)
            }
        }

    private fun selectMediaFromGallery() {
//        val request = if(viewModel.user.premium) ActivityResultContracts.PickVisualMedia.ImageAndVideo else ActivityResultContracts.PickVisualMedia.ImageOnly
//        selectImageOrVideoFromGalleryResult.launch(PickVisualMediaRequest(request))
        val request = if(viewModel.user.premium) arrayOf("image/*", "video/*") else arrayOf("image/*")
        selectImageOrVideoFromGalleryResult.launch(request)
    }

    private val selectDocumentResult =
        registerForActivityResult(ActivityResultContracts.OpenDocument()) { uri: Uri? ->
            uri?.let {
                viewModel.addDocument(uri)
            }
        }

    private fun selectDocument() {
        selectDocumentResult.launch(arrayOf("audio/*", "image/*", "video/*", "application/*", "text/*", "font/*"))
    }

    private val takeVideoResult =
        registerForActivityResult(object: ActivityResultContracts.CaptureVideo() {
            override fun createIntent(context: Context, input: Uri): Intent {
                val intent = super.createIntent(context, input)
                Log.d("CVIDEO", "createIntent: ${viewModel.videoDurationLimit}")
                viewModel.videoDurationLimit.let {
                    intent.putExtra(MediaStore.EXTRA_DURATION_LIMIT, it) // Duration in Seconds
                }
//                intent.putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 0); // Quality Low
//                intent.putExtra(MediaStore.EXTRA_SIZE_LIMIT, 5491520L); // 5MB
                return intent
            }
        }) { isSuccess ->
            if (isSuccess) {
                viewModel.addCapturedVideo()
            }
        }
    private fun takeVideo() {
        checkCameraPermission(bindingRoot) {
            lifecycleScope.launch {
                val uri = viewModel.getVideoUriForCapture()
                takeVideoResult.launch(uri)
            }
        }
    }

    // Audio Recorder

    private fun setupRecordView() {
        binding.chatVoiceButton.apply {
            setScaleUpTo(1.5f)
            setRecordView(binding.recordView)
        }

        binding.recordView.apply {
            setLockEnabled(true)
            viewModel.voiceRecordLimit?.let {
                timeLimit = it.toLong()*1000
            }
            setRecordLockImageView(binding.recordLock)
            setSlideToCancelTextColor(ContextCompat.getColor(requireContext(),R.color.textColorSecondaryLight))
            setTrashIconColor(ContextCompat.getColor(requireContext(),R.color.textColorSecondary))
            setSmallMicColor(ContextCompat.getColor(requireContext(),R.color.colorError))
            setOnRecordListener(object : OnRecordListener {
                override fun onStart() {
                    binding.chatInput.clearFocus()
                    Log.d("REC", "onStart")
                    binding.chatVoiceButton.requestFocus()
                    viewModel.startAudioRecording()
                    binding.chatVoiceButton.setRecordButtonActive(true)
                }

                override fun onCancel() {
                    Log.d("REC", "onCancel")
                    binding.chatVoiceButton.setRecordButtonActive(false)
                }

                override fun onFinish(recordTime: Long, limitReached: Boolean) {
                    viewModel.finishAudioRecording()
                    binding.chatVoiceButton.setRecordButtonActive(false)
                }

                override fun onLessThanSecond() {
                    Log.d("REC", "onLessThanSecond")
                    Toast.makeText(requireContext(), getString(R.string.chat_mic_hold_message), Toast.LENGTH_LONG).show()
                    viewModel.cancelAudioRecording()
                    binding.chatVoiceButton.setRecordButtonActive(false)
                }

                override fun onLock() {
                    Log.d("REC", "onLock")
                    viewModel.onLockAudioRecording()
                }
            })
            setOnBasketAnimationEndListener {
                viewModel.cancelAudioRecording()
            }

            setRecordPermissionHandler { checkAudioPermission(bindingRoot) }
        }

        binding.recordLock.apply {
            setDefaultCircleColor(ContextCompat.getColor(requireContext(),R.color.textInputBackground))
            setCircleLockedColor(ContextCompat.getColor(requireContext(),R.color.textInputBackground))
            setLockColor(ContextCompat.getColor(requireContext(),R.color.textColorSecondaryLight))
        }

        binding.audioPreview.audioPreviewPlayButton.setOnClickListener {
            viewModel.playPauseRecording()
        }
        binding.audioPreview.audioPreviewDeleteButton.setOnClickListener {
            viewModel.clearChatMedia()
        }
        binding.audioPreview.audioPreviewSendButton.setOnClickListener {
            viewModel.prepareAndSendMessage()
        }

        viewModel.nowPlaying.observe(viewLifecycleOwner) {
            binding.audioPreview.info = if (it?.listPosition== MediaPlayerInfo.LIST_POS_RECORDING) it else null
        }
    }

    private fun RecordButton.setRecordButtonActive(active: Boolean) {
        this.setBackgroundResource(if(active) R.drawable.bg_chat_record_button_active else R.drawable.bg_chat_record_button)
        val iconTint = if(active) R.color.textColorOnPrimary else R.color.colorChatEntryButtons
        this.imageTintList = ColorStateList.valueOf(ContextCompat.getColor(context, iconTint))
    }


}