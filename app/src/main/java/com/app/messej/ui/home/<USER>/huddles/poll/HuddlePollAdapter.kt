package com.app.messej.ui.home.publictab.huddles.poll

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.api.poll.Answer
import com.app.messej.data.model.enums.PollType
import com.app.messej.databinding.ItemPollProgressBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class HuddlePollAdapter(pollList: MutableList<Answer>, private  val listener: ItemClickListener?, selectedIndex:Int, private var isEditMode:Boolean, val type: PollType) : BaseQuickAdapter<Answer, BaseDataBindingHolder<ItemPollProgressBinding>>(
    R.layout.item_poll_progress, pollList
) {
    private var selectedPosition: Int = -1
    init {
        setSelectedPosition(selectedIndex)
    }
    override fun convert(
        holder: BaseDataBindingHolder<ItemPollProgressBinding>,
        item: Answer
    ) {
        holder.dataBinding?.apply {
            answer = item
            showPercentage = true
            isEdit=isEditMode
            showRadioButton=!isEditMode
            showCount = when (type) {
                PollType.POLL_RESULT -> {
                    true
                }
                else -> {
                    false
                }
            }
            count= (item.voteCount).toString()
            isEdit=isEditMode
            showRadioButton = isEditMode
            pollType=type
            isAnswerSelected=selectedPosition == holder.bindingAdapterPosition
            radioButton.apply {
                setOnClickListener {
                   if(!isSelected) {
                       listener?.onClick(item,holder.bindingAdapterPosition)
                       setSelectedPosition(holder.bindingAdapterPosition)
                   }
                }

            }
        }
    }

   fun  setEditMode(mode:Boolean){
       this.isEditMode =mode
       notifyDataSetChanged()
    }

    private fun setSelectedPosition(position: Int) {
        if (selectedPosition != position) {
            val previousSelection = selectedPosition
            selectedPosition = position
            notifyItemChanged(previousSelection)
            notifyItemChanged(position)
        }
    }

    fun setCastVote() {
      this.isEditMode=false
        notifyDataSetChanged()
    }

    class DiffCallback : DiffUtil.ItemCallback<Answer>() {
        override fun areItemsTheSame(oldItem: Answer, newItem: Answer): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Answer, newItem: Answer): Boolean {
            return oldItem == newItem
        }
    }

    interface  ItemClickListener{
        fun onClick(answer: Answer, position:Int)
    }
}
