package com.app.messej.ui.home.publictab.flash.myflash

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.databinding.FragmentFlashInnerBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView

class MyFlashArchiveFragment : FlashListBaseFragment() {

    override val viewModel: MyFlashArchiveViewModel by navGraphViewModels(R.id.nav_flash_archive)

    private lateinit var binding: FragmentFlashInnerBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_inner, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override val multiStateView: MultiStateView
        get() = binding.multiStateView

    override val flashList: RecyclerView
        get() = binding.flashList

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
    }

    override fun LayoutListStateEmptyBinding.setEmptyView() {
        this.prepare(
            image = R.drawable.im_eds_my_flash,
            message = R.string.flash_archived_eds
        )
    }

    override fun observe() {
        super.observe()

        viewModel.onFlashUnarchived.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_action_unarchive_toast)
            mAdapter?.refresh()
        }

        viewModel.onFlashDeleted.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_action_delete_toast)
            mAdapter?.refresh()
        }
    }

    override fun onFlashClicked(flash: FlashVideo, pos: Int) {
        findNavController().navigateSafe(MyFlashArchiveFragmentDirections.actionMyFlashArchiveFragmentToMyFlashArchivePlayerFragment())
    }

    override fun showLongPressMenu(flash: FlashVideo, view: View) {
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_flash_archive_select, popup.menu)
        popup.setForceShowIcon(true)

        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_unarchive-> viewModel.unarchiveFlash(flash)
                R.id.action_delete -> showRecordingDeleteAlert {
                    viewModel.deleteFlash(flash)
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun showRecordingDeleteAlert(onConfirm: () -> Unit) {
        MaterialAlertDialogBuilder(requireContext()).setTitle(R.string.flash_my_flash_delete_confirm_title)
            .setMessage(getText(R.string.flash_my_flash_delete_confirm_message))
            .setPositiveButton(getText(R.string.common_delete)) { dialog, _ ->
                dialog.dismiss()
                onConfirm.invoke()
            }.setNegativeButton(getText(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }
}