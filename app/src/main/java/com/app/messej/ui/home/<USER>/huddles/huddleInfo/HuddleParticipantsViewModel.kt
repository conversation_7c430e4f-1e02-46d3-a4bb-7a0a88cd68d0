package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.huddles.HuddleInfo
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.ParticipantsSearchType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

@OptIn(FlowPreview::class)
class HuddleParticipantsViewModel(application: Application) : AndroidViewModel(application) {

    private val huddleRepo = HuddlesRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())
    private val accountRepo = AccountRepository(getApplication())

    val user: CurrentUser get() = accountRepo.user

    private val huddleID = MutableLiveData<Int?>(null)
    private val huddleType = MutableLiveData<HuddleType?>(null)

    fun setHuddleId(id: Int, type: HuddleType) {
        huddleID.postValue(id)
        huddleType.postValue(type)
    }

    val showCompactLoading = MutableLiveData(false)

    val onChatIDComplete= LiveEvent<Pair<String, Int>>()

    val isAdminOrManager = MutableLiveData(false)

    private val _huddleInfo = MutableLiveData<HuddleInfo>(null)

    val filters = MutableLiveData<MutableMap<ParticipantsSearchType, Boolean>>(mutableMapOf())

    private val _participantsAPIRequestActionError = MutableLiveData<String?>(null)
    val participantsAPIRequestActionError: LiveData<String?> = _participantsAPIRequestActionError


    data class ParticipantAPIPackage(val huddleId: Int, val searchTerm: String, val filters: List<ParticipantsSearchType>)

    private val apiPackage: MediatorLiveData<ParticipantAPIPackage?> by lazy {
        val med = MediatorLiveData<ParticipantAPIPackage?>()
        fun updateAPIPackage() {
            med.postValue(
                if (_huddleInfo.value != null) ParticipantAPIPackage(
                    huddleId = _huddleInfo.value?.id ?: return, searchTerm = searchTerm.value ?: "", filters = getFilterList()
                ) else null
            )
        }
        med.addSource(_huddleInfo) { updateAPIPackage() }
        med.addSource(searchTerm) { updateAPIPackage() }
        med.addSource(filters) { updateAPIPackage() }
        med
    }

    var searchKeyword = MutableLiveData<String>(null)
    private val searchTerm = MutableLiveData<String>("")

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                it ?: return@collect
                if (it.isEmpty()) {
                    searchTerm.postValue("")
                } else {
                    searchTerm.postValue(it)
                }
            }
        }
    }

    val huddle: LiveData<HuddleInfo?> = huddleID.switchMap {
        it ?: return@switchMap null
        getHuddleInfo(it)
        _huddleInfo
    }

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val _participantsList = apiPackage.switchMap {
        huddleID.value ?: return@switchMap null
        it?: return@switchMap null
        huddleRepo.getHuddleParticipantsList(huddleID.value!!, it.searchTerm, getFilterList()).liveData.cachedIn(viewModelScope)
    }

    val participantsList: MediatorLiveData<PagingData<Participant>> by lazy {
        val med = MediatorLiveData<PagingData<Participant>>()
        fun update() {
            val data = _participantsList.value?.map { pc ->
                _nickNames.value?.findNickName(pc.id)?.let {
                    pc.name = it
                }
                pc
            }
            med.postValue(data)
        }
        med.addSource(_participantsList) { update() }
        med.addSource(_nickNames) { update() }
        med
    }

    private fun getHuddleInfo(huddleId: Int) {
        showCompactLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<HuddleInfo> = huddleRepo.getFullHuddleInfo(huddleId)) {
                is ResultOf.APIError -> {
                    showCompactLoading.postValue(false)
                }
                is ResultOf.Error -> {
                    showCompactLoading.postValue(false)
                }
                is ResultOf.Success -> {
                    val huddle = result.value
                    // setting public under the assumption that private huddles will always be offline
                    _huddleInfo.postValue(huddle)
                    isAdminOrManager.postValue(huddle.isAdmin == true || huddle.isManager == true)
                    showCompactLoading.postValue(false)
                }
            }
        }
    }

    private fun getFilterList(): List<ParticipantsSearchType> {
        return filters.value.orEmpty().filter { it.value }.map { it.key }
    }

    fun toggleFilter(value: ParticipantsSearchType) {
        val filtersMap = filters.value ?: mutableMapOf()
        val current = filtersMap[value] ?: false
        filtersMap[value] = !current
        filters.postValue(filtersMap)
    }

    val onFailedToSendAdminInvite = LiveEvent<Boolean>()

    fun canSendAdminRequest(item: Participant, cancel: Boolean) {
        if (!user.premium) {
            if (_huddleInfo.value?.canSendOrAcceptAdminInvite == true) {
                appointAsAdmin(item, cancel)
                onFailedToSendAdminInvite.postValue(false)
            } else onFailedToSendAdminInvite.postValue(true)
        } else appointAsAdmin(item, cancel)
    }

    fun appointAsAdmin(item: Participant, cancel: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
                when (val result = huddleRepo.sendOrCancelAdminInvite(huddleId = huddle.value?.id!!, memberId = item.id, cancel)) {
                    is ResultOf.Success -> {
                        getHuddleInfo(huddle.value?.id!!)
                        searchTerm.postValue("")
                    }

                    is ResultOf.APIError -> {
                        _participantsAPIRequestActionError.postValue(result.error.message)
                    }

                    is ResultOf.Error -> {
                        _participantsAPIRequestActionError.postValue("")
                    }
                }
        }
    }

    fun blockUnblockUser(memberId: Int, action: Participant.ParticipantsActionTypes) {
        viewModelScope.launch(Dispatchers.IO) {
            val list: ArrayList<Int> = arrayListOf()
            list.add(memberId)
            when (val result = huddleRepo.blockOrUnblockHuddleUser(huddleId = huddle.value?.id!!, action = action, userList = list)) {
                is ResultOf.Success -> {
                    searchTerm.postValue("")
                }
                is ResultOf.APIError -> {
                    _participantsAPIRequestActionError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    _participantsAPIRequestActionError.postValue("")
                }
            }
        }
    }

    fun removeUser(item: Participant) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.removeHuddleUser(huddleId = huddle.value?.id!!, userId = item.id)) {
                is ResultOf.Success -> {
                    searchTerm.postValue("")
                }
                is ResultOf.APIError -> {
                    _participantsAPIRequestActionError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    _participantsAPIRequestActionError.postValue("")
                }
            }
        }
    }

    fun generateRoomID(receiver: Int) {
        val sender = accountRepo.user.id
        onChatIDComplete.postValue(Pair(PrivateChatMessage.getChatRoomId(sender, receiver),receiver))
    }

    fun dismissAdmin(item: Participant) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.dismissAdmin(huddleId = huddle.value?.id!!, userId = item.id)) {
                is ResultOf.Success -> {
                    searchTerm.postValue("")
                }
                is ResultOf.APIError -> {
                    _participantsAPIRequestActionError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    _participantsAPIRequestActionError.postValue("")
                }
            }
        }
    }

    val onRestrictedFromHuddle = LiveEvent<String?>()

    fun banUser(participantsStatus: Participant.ParticipantStatus, isBanned:Boolean, userid:Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.banUnbanParticipant(huddleId = huddle.value?.id!!, userId = userid, status = participantsStatus, isBan = isBanned)) {
                is ResultOf.Success -> {
                    searchTerm.postValue("")
                    onRestrictedFromHuddle.postValue(result.value)
                }
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
            }
        }
    }

}