package com.app.messej.ui.home.publictab.flash.userflash

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.flash.myflash.FlashListBaseViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class UserFlashViewModel(application: Application) : FlashListBaseViewModel(application) {

    private val _userId = MutableLiveData<Int?>(null)

    fun setUserId(id: Int) {
        _userId.postValue(id)
        getUserProfile(id)
    }

    private val _profile = _userId.switchMap {
        it ?: return@switchMap MutableLiveData<OtherUser?>(null)
        profileRepo.getLocalOtherUserProfile(it)
    }

    private val nickName = _userId.switchMap {
        if(it!=null) profileRepo.getNickNameLiveData(it) else null
    }

    val profile: MediatorLiveData<OtherUser?> by lazy {
        val med = MediatorLiveData<OtherUser?>()
        fun update() {
            var user = _profile.value
            user?.let {
                nickName.value?.nickName?.let { nn ->
                    if(nn.isNotEmpty()) {
                        user = it.copy(
                            name = nn
                        )
                    }
                }
            }
            med.postValue(user)
        }
        med.addSource(_profile) { update() }
        med.addSource(nickName) { update() }
        med
    }

    private fun getUserProfile(id: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when(val result = profileRepo.getPublicUserDetails(id)) {
                is ResultOf.Success -> {
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }


    override val _flashFeedList: LiveData<PagingData<FlashVideo>> = _userId.switchMap {
        return@switchMap it?.let {
            flashRepo.getUserFlashPager(it).liveData.cachedIn(viewModelScope)
        }
    }
}