package com.app.messej.ui.home.publictab.postat.mypostat

import android.app.Application
import android.util.Log
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.paging.TerminalSeparatorType
import androidx.paging.cachedIn
import androidx.paging.filter
import androidx.paging.insertHeaderItem
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.api.postat.PostatRuleLimitResponse
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.repository.worker.PostatUploadWorker
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.postat.PostatFeedBaseViewModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class MyPostatViewModel(application: Application) : PostatFeedBaseViewModel(application) {

    private val _myPostatList = postatRepo.getMyPostatPager().liveData.cachedIn(viewModelScope)

    sealed class MyPostatUIModel{
        data class PostatItem(val postat: Postat) : MyPostatUIModel()
        data object SeparatorItem : MyPostatUIModel()
    }

    val myPostatList = _myPostatList.map { pagingData ->
        Log.d("MyPostat", "Mapping pagingData: $pagingData") // Log before mapping
        pagingData.map {
            Log.d("MyPostat", "Mapping PostatItem: $it") // Log each PostatItem
            MyPostatUIModel.PostatItem(it) as MyPostatUIModel
        }.insertHeaderItem(
            terminalSeparatorType = TerminalSeparatorType.SOURCE_COMPLETE,
            item = MyPostatUIModel.SeparatorItem
        )
    }

    val isUserBlockedFromPostat: Boolean
        get() = accountRepo.user.userFunctionalityBlocks?.postatPostsBlock == true

    override val _postatList = _myPostatList.map { pg ->
        pg.filter { it.type != Postat.PostatType.DRAFT }
    }

    fun startPostatSync() {
        viewModelScope.launch {
            PostatUploadWorker.startIfNotRunning()
        }
    }

    val postatLimitData = LiveEvent<PostatRuleLimitResponse>()
    var postatMessageId : String? = null

    fun checkPostatCreationLimit(postatId: String? = null){
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<PostatRuleLimitResponse> = postatRepo.getCreatePostatEligible(user.id)) {
                is ResultOf.Success -> {
                    postatLimitData.postValue(result.value)
                    postatMessageId = postatId
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }
}