package com.app.messej.ui.home.businesstab.operations.tasks.dears

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.MenuProvider
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.databinding.FragmentBusinessOperationsDearsListBinding
import com.app.messej.ui.home.businesstab.HomeBusinessFragmentDirections
import com.app.messej.ui.home.publictab.broadcast.PublicBroadcastListAdapter
import com.app.messej.ui.home.publictab.broadcast.PublicBroadcastViewModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView

class BusinessOperationsDearsList : Fragment(), MenuProvider {

    private lateinit var binding: FragmentBusinessOperationsDearsListBinding
    private val viewModel: PublicBroadcastViewModel by activityViewModels()
    private var mAdapter: PublicBroadcastListAdapter? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_operations_dears_list, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        binding.broadcastFab.setOnClickListener {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavigationChatBroadcast(BroadcastMode.ALL_DEARS, null))
        }
    }

    private fun setup() {
        addAsMenuHost()
        initAdapter()
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).apply { setImageResource(R.drawable.im_eds_dears_list) }
            findViewById<AppCompatTextView>(R.id.eds_empty_message).apply {
                text = resources.getString(R.string.broadcast_list_dears_empty_text)
            }
            findViewById<MaterialButton>(R.id.eds_empty_action).apply {
                visibility = View.GONE
            }
        }
    }

    private fun observe() {
        viewModel.broadcastDearsList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }
    }

    private fun initAdapter() {
        mAdapter = PublicBroadcastListAdapter(object :PublicBroadcastListAdapter.ItemListener{
            override fun onItemClick(user: UserRelative) {
//                val userType = PublicUserProfileLoaderFragment.getUserType(user.citizenship)
                findNavController().navigateSafe(HomeBusinessFragmentDirections.actionGlobalPublicUserProfileFragment(user.id))
            }
        })
        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }

        val layoutMan = LinearLayoutManager(context)
        binding.dearsList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
            viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    if (mAdapter?.itemCount != 0) {
                       binding.broadcastFab.isVisible = true
                        viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }else{
                        binding.broadcastFab.isVisible = false
                    }
                }
            })
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            android.R.id.home -> {
                findNavController().popBackStack()
            }
        }
        return true
    }

}