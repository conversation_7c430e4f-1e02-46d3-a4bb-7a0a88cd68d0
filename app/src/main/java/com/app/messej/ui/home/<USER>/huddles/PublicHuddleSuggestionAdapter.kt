package com.app.messej.ui.home.publictab.huddles

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.SuggestedHuddle
import com.app.messej.databinding.ItemHuddleListBinding

class PublicHuddleSuggestionAdapter(private var mListener: HuddleClickListener): PagingDataAdapter<SuggestedHuddle, PublicHuddleSuggestionAdapter.HuddleSuggestionViewHolder>(SuggestionDiff) {

    interface HuddleClickListener {
        fun onHuddleClick(view: View, huddle: SuggestedHuddle, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        HuddleSuggestionViewHolder(
            ItemHuddleListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: HuddleSuggestionViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class HuddleSuggestionViewHolder(private val binding: ItemHuddleListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: SuggestedHuddle) = with(binding) {
            layoutHolder.setOnClickListener {
                mListener.onHuddleClick(view = it, item, layoutPosition)
            }
            huddle = item
            lastMessagePreview.huddleLastMessage.text = item.about
        }
    }

    object SuggestionDiff : DiffUtil.ItemCallback<SuggestedHuddle>() {
        override fun areItemsTheSame(oldItem: SuggestedHuddle, newItem: SuggestedHuddle) =
            oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: SuggestedHuddle, newItem: SuggestedHuddle) =
            oldItem == newItem
    }
}