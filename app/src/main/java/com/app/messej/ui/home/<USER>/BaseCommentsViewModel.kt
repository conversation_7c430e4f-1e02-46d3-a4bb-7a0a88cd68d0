package com.app.messej.ui.home.common

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.utils.CountryListUtil
import com.hadilq.liveevent.LiveEvent
import com.kennyc.view.MultiStateView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

abstract class BaseCommentsViewModel<T: PostCommentWithReplies>(application: Application) : AndroidViewModel(application) {

    protected val accountRepo = AccountRepository(application)
    protected val profileRepo = ProfileRepository(application)

    val user: CurrentUser get() = accountRepo.user

    val showCompactLoading = MutableLiveData<Boolean>(false)

    private val viewState = MutableStateFlow<MultiStateView.ViewState?>(MultiStateView.ViewState.LOADING)

    val debouncedViewState = viewState.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).distinctUntilChanged()

    fun setViewState(state: MultiStateView.ViewState) {
        viewState.value = state
    }

    var commentText = MutableLiveData("")
    var postingComment = MutableLiveData(false)

    var mentionedUserDetails = MutableLiveData<SenderDetails?>(null)

    protected val _countryList = MutableLiveData<Map<String, Int>>()

    init {
        _countryList.postValue(CountryListUtil.getCustomCountryMap())
    }

    protected val _commentsEnabled = MutableLiveData(true)
    val commentsEnabled: LiveData<Boolean> = _commentsEnabled

    private val _commentCount = MutableLiveData(0)
    val commentCount: LiveData<Int> = _commentCount

    protected val commentCountCallback: (Int) -> Unit = {
        _commentCount.postValue(it)
    }

    protected val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    protected abstract val _commentList: LiveData<PagingData<PostCommentWithReplies>>


    val commentList: Flow<PagingData<ChatMessageUIModel>> by lazy {
        val med = MediatorLiveData<PagingData<ChatMessageUIModel>>()
        fun combine() {
            val list = _commentList.value
            val nickNames = _nickNames.value
            val flags = _countryList.value
            val data: PagingData<ChatMessageUIModel>? = list?.map { item ->
                val nick = nickNames?.find { nn -> nn.userId == item.commentItem.senderId }?.nickName
                val sd = item.commentItem.senderDetails?.copy(
                    name = nick ?: item.commentItem.senderDetails?.name.orEmpty(),
                    _username = ""
                )
                ChatMessageUIModel.PostCommentWithRepliesModel(replaceSenderDetails(item, sd)).apply {
                    comment.senderDetails?.countryCode?.let { cc ->
                        countryFlag = flags?.get(cc)
                    }
                }
            }
            med.postValue(data)
        }
        med.addSource(_commentList) { combine() }
        med.addSource(_nickNames) { combine() }
        med.addSource(_countryList) { combine() }
        med.asFlow()
    }

//    protected abstract val _replayCommentList: LiveData<PagingData<T>>

//    val repliesCommentList: Flow<PagingData<ChatMessageUIModel>> by lazy {
//        val med = MediatorLiveData<PagingData<ChatMessageUIModel>>()
//        fun combine() {
//            val list = _replayCommentList.value
//            val nickNames = _nickNames.value
//            val flags = _countryList.value
//            val data: PagingData<ChatMessageUIModel>? = list?.map { item ->
//                val nick = nickNames?.find { nn -> nn.userId == item.senderId }?.nickName
//                val sd = item.senderDetails?.copy(
//                    name = nick?:item.senderDetails?.name.orEmpty(),
//                    _username = ""
//                )
//                ChatMessageUIModel.PostCommentModel(replaceSenderDetails(item, sd)).apply {
//                    comment.senderDetails?.countryCode?.let { cc ->
//                        countryFlag = flags?.get(cc)
//                    }
//                }
//            }
//            med.postValue(data)
//        }
//        med.addSource(_replayCommentList) { combine() }
//        med.addSource(_nickNames) { combine() }
//        med.asFlow()
//    }



    abstract fun replaceSenderDetails(obj: PostCommentWithReplies, senderDetails: SenderDetails?): T

    val onCommentAdded = LiveEvent<Boolean>()

    val conNewCommendAdded = MutableSharedFlow<Boolean>()
    val replyDeleted= MutableSharedFlow<Boolean>()

    fun writeComment() {
        val comment = commentText.value ?: return
        postingComment.postValue(true)
        viewModelScope.launch {
            when (if(mentionedUserDetails.value!=null) executeReplayComment(reply = comment, mentionedUserId = mentionedUserDetails.value?.id?:0) else executeWriteComment(comment)) {
                is ResultOf.Success -> {
                    commentText.postValue("")
                    onCommentAdded.postValue(true)
                    conNewCommendAdded.emit(value = true)
                    postingComment.postValue(false)

                    // Reset mentioned user details after successful reply submission
                    if (mentionedUserDetails.value != null) {
                        clearReplyState()
                    }
                }
                else -> {
                    postingComment.postValue(false)
                }
            }
        }
    }
    fun postMentionedUser(senderDetails: SenderDetails?){
        mentionedUserDetails.postValue(senderDetails?.copy(_username =senderDetails.name ))
    }

    protected abstract suspend fun executeWriteComment(comment: String): ResultOf<Unit>
    protected abstract suspend fun executeReplayComment(reply: String,mentionedUserId:Int): ResultOf<Unit>

    fun getFlag(countryCode: String): Int? {
        val flags = _countryList.value
        return flags?.get(countryCode)
    }

    // Add a new function to clear the reply state
    fun clearReplyState() {
        mentionedUserDetails.postValue(null)
    }
}
