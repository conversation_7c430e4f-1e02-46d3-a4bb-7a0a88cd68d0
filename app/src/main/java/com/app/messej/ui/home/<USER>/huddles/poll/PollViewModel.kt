package com.app.messej.ui.home.publictab.huddles.poll

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.api.poll.Answer
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.entity.PollParticipant
import com.app.messej.data.repository.PollsRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.socket.repository.HuddleChatEventRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

class PollViewModel (application: Application):AndroidViewModel(application){

    private val profileRepo = ProfileRepository(getApplication())

    private val _huddleId=MutableLiveData<Int?>(null)
    val huddleId: LiveData<Int?> = _huddleId

    private val _pollID=MutableLiveData<Int?>(null)
    val pollID: LiveData<Int?> = _pollID

    private val _alreadyAnswered=MutableLiveData<Boolean>(false)
    val alreadyAnswered: LiveData<Boolean> = _alreadyAnswered
    val isPollUpdated=LiveEvent<Boolean>()

    private val _selectedAnswer=MutableLiveData<Answer>(null)
    private val selectedAnswer: LiveData<Answer> = _selectedAnswer

    private val _isPollLoading=MutableLiveData<Boolean?>(null)
     val isPollLoading: LiveData<Boolean?> = _isPollLoading

    private val _isHide=MutableLiveData<Boolean>(true)
    val isHide: LiveData<Boolean> = _isHide

    private val _isUserIsManager=MutableLiveData<Boolean>(true)
    val isUserIsManager: LiveData<Boolean> = _isUserIsManager

    private val _poll=MutableLiveData<Poll?>(null)
    val poll: LiveData<Poll?> = _poll

    val isEndPollSuccess=LiveEvent<Boolean>()
    private val eventRepo = HuddleChatEventRepository

    private val _isEditMode=MutableLiveData<Boolean?>(null)
    val isEditMode: LiveData<Boolean?> = _isEditMode

    private val _actionMenuVisibility=MutableLiveData<Boolean>(false)
    val actionMenuVisibility: LiveData<Boolean> = _actionMenuVisibility
   private val  pollsRepository=PollsRepository(application)
    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    val participants=pollID.switchMap {
        it?: return@switchMap null
        pollsRepository.getPollParticipants(it).liveData.cachedIn(viewModelScope)
    }
    
    val participantsList: MediatorLiveData<PagingData<PollParticipant>> by lazy {
        val med = MediatorLiveData<PagingData<PollParticipant>>()
        fun update() {
            val data = participants.value?.map { pc ->
                _nickNames.value?.findNickName(pc.userId)?.let {
                    pc.name = it
                }
                pc
            }
            med.postValue(data)
        }
        med.addSource(participants) { update() }
        med.addSource(_nickNames) { update() }
        med
    }

    init {
        viewModelScope.launch {
            try {
                eventRepo.pollPayloadFlow.filter { it.pollId == poll.value?.id }.collect { payload ->
                    val huddleAnswers = payload.answers
                    val updatedAnswers = poll.value?.answers?.map { pollAnswer ->
                        huddleAnswers.find { it.id == pollAnswer.id }?.let { huddleAnswer ->
                            pollAnswer.copy(
                                answer = huddleAnswer.answer, answerPercentage = huddleAnswer.answerPercentage, timeCreated = huddleAnswer.timeCreated, timeUpdated = huddleAnswer.timeUpdated
                            )
                        } ?: pollAnswer
                    }
                    if (!updatedAnswers.isNullOrEmpty()) {
                        _poll.postValue(
                            _poll.value?.copy(
                                answers = updatedAnswers
                            )
                        )
                    }

                }
            } finally {
            }
        }
    }

    fun setArgs(huddleID:Int,answerIndex:Int,isUserIsManager:Boolean,isEditMode:Boolean) {
        _huddleId.postValue(huddleID)
        if(answerIndex==-1){
            _alreadyAnswered.postValue(true)
        }else{
            _alreadyAnswered.postValue(false)
        }
        _isUserIsManager.postValue(isUserIsManager)
        getPollDetails(huddleID,isEditMode)
    }

    private fun getPollDetails(huddleID: Int,mode:Boolean) {
        viewModelScope.launch {
            when(val result=pollsRepository.getPollsList(huddleID,"active")){
                is ResultOf.Success ->{

                    if(result.value.polls.isNotEmpty()){

                        if(_isEditMode.value==null){
                            _isEditMode.postValue(mode)
                        }else{
                            _isEditMode.postValue(false)
                        }
                        _actionMenuVisibility.postValue(mode)
                        _isPollLoading.postValue(false)
                       _poll.postValue(result.value.polls[0])

                    }else{
                        _isPollLoading.postValue(false)
                        _poll.postValue(null)
                    }
                }

                else -> {}
            }
        }

    }

    val isEditable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun editable() {
           if(_alreadyAnswered.value==false && _isEditMode.value==true){
               med.postValue(true)
           }else{
               med.postValue(false)
           }
        }
        med.addSource(_alreadyAnswered) {editable()}
        med.addSource(_isEditMode) { editable() }
        med
    }


    fun setPollID(pollID:Int){
        _pollID.postValue(pollID)
        getPollByID(pollID)
    }


    val selectedPoll = _pollID.switchMap {
        it?:return@switchMap null
        pollsRepository.getSelectedPoll(it)

    }.distinctUntilChanged()

    val visitorCount=selectedPoll.map {
        it?:return@map null
        it.visitorCount
    }.distinctUntilChanged()

    val  citizenCount=selectedPoll.map {
        it?:return@map null
        it.citizenCount
    }.distinctUntilChanged()

    val  totalAnswered=selectedPoll.map {
        it?:return@map null
        it.totalAnswered
    }.distinctUntilChanged()

    val  hasParticipants=selectedPoll.map {
        it?:return@map false
        it.totalAnswered >=1
    }.distinctUntilChanged()

    fun setSelectedAnswer(answer: Answer){
        _isPollLoading.postValue(true)
       // _selectedAnswer.postValue(answer)
          updateAnswer(answer)
    }

    fun setEditMode(mode: Boolean) {
        _isEditMode.postValue(mode)
    }

    private fun getPollByID(pollID:Int){
        _isPollLoading.postValue(true)
      viewModelScope.launch {
          when(val response=pollsRepository.getPoll(pollID)){
              is ResultOf.Success ->{
                 _isPollLoading.postValue(false)
              }
              else -> { _isPollLoading.postValue(false)
              }
          }
      }
    }

    fun endPoll(pollID: Int, huddleID: Int){
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = pollsRepository.updateHuddlePoll(pollID, huddleID)) {
                is ResultOf.Success -> {
                    isEndPollSuccess.postValue(true)

                }
                is ResultOf.APIError -> {
                    isEndPollSuccess.postValue(false)

                }
                is ResultOf.Error -> {
                    isEndPollSuccess.postValue(false)

                }
            }
        }

    }



/*    val isPollUpdated = _selectedAnswer.switchMap {
        it?:return@switchMap null
        liveData {
            val isSuccess:Boolean?= when (val result=pollsRepository.setAnswer(it.id,it.pollId)) {
                    is ResultOf.Success -> {
                       getPollDetails(_huddleId.value?:-1)
                        true
                    }

                    is ResultOf.APIError -> {
                        false
                    }

                    is ResultOf.Error -> {
                        false
                    }
                }
                emit(isSuccess)
            }
        }*/

    private fun updateAnswer(answer: Answer) {
        viewModelScope.launch {
            when (val result=pollsRepository.setAnswer(answer.id,answer.pollId)) {
                is ResultOf.Success -> {
                     isPollUpdated.postValue(true)
                    getPollDetails(_huddleId.value?:-1,false)
                }
                else -> {
                    isPollUpdated.postValue(false)
                }
            }
        }

    }

}