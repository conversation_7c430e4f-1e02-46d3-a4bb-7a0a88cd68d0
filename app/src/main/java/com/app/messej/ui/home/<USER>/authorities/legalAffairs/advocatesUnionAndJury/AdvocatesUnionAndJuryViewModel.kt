package com.app.messej.ui.home.publictab.authorities.legalAffairs.advocatesUnionAndJury

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.enums.AdvocatesUnionFilter
import com.app.messej.data.model.enums.JuryFilter
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalAffairsRepository

class AdvocatesUnionViewModel(application:Application) : AndroidViewModel(application = application) {

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val tag = "AUVM"
    private val repository = LegalAffairsRepository(application)
    private val advocatesUnionFilter = MutableLiveData<AdvocatesUnionFilter?>(null)
    val juryFilter = MutableLiveData<JuryFilter?>(null)

    fun setAdvocatesUnionFilter(filter: AdvocatesUnionFilter?) {
        advocatesUnionFilter.value = filter
    }

    fun setJuryFilter(filter: JuryFilter?) {
        juryFilter.value = filter
    }

    init {
        Log.d(tag, "view model init block")
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(tag, "viewmodel cleared")
    }

    val advocatesUnionList = advocatesUnionFilter.switchMap { tab ->
//        tab?: return@switchMap null
        repository.getLegalAffairsBoardDetails(
            recordType = tab?.serializedName(),
            tab = LegalAffairsMainTab.AdvocatesUnion.serializedName()
        ).liveData.cachedIn(viewModelScope)
    }

    val juryList = juryFilter.switchMap { tab ->
        repository
            .getLegalAffairsBoardDetails(
                recordType = tab?.key,
                tab = LegalAffairsMainTab.Jury.serializedName()
            )
            .liveData
            .cachedIn(viewModelScope)
    }

}