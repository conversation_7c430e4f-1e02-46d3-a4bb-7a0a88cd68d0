package com.app.messej.ui.home.businesstab.operations.tasks.information

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.app.messej.data.model.BusinessTaskInformativeMessage
import com.app.messej.data.model.status.BusinessActivityStatus
import com.app.messej.data.repository.BusinessRepository

class TaskInformativeMessageDialogViewModel (application: Application) : AndroidViewModel(application) {
    private var businessRepo: BusinessRepository = BusinessRepository(application)
    val businessOperation: LiveData<BusinessActivityStatus?> = businessRepo.getFlashAtActivity()

    val taskModel = MutableLiveData<BusinessTaskInformativeMessage>()
}