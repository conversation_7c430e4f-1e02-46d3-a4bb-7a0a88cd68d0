package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentManageRequestInviteBinding
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ManageRequestInviteFragment : Fragment() {
    private lateinit var binding: FragmentManageRequestInviteBinding
    private lateinit var mPagerAdapter: FragmentStateAdapter

    private val args: ManageRequestInviteFragmentArgs by navArgs()

    private val viewModel : ManageRequestInviteViewModel by viewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_manage_request_invite, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        setTabNumber()
    }

    private fun setup() {
        viewModel.setHuddleId(args.huddleId)
        mPagerAdapter = object : FragmentStateAdapter(childFragmentManager,viewLifecycleOwner.lifecycle) {

            override fun getItemCount(): Int = 2

            override fun createFragment(position: Int): Fragment {
                return when (position) {
                    0 -> ManageRequestsListFragment()
                    1 -> ManageInvitesListFragment()
                    else -> throw java.lang.IllegalArgumentException("There should only be 2 tabs")
                }
            }
        }

        binding.requestInvitePager.apply {
            isUserInputEnabled = false
            adapter = mPagerAdapter
        }

        TabLayoutMediator(binding.requestInviteTab, binding.requestInvitePager) { tab, position ->
            when (position) {
                0 -> {
                    viewModel.requestCount.observe(viewLifecycleOwner){
                        tab.text = resources.getString(R.string.huddle_request_text, it)
                    }
                }
                1 -> {
                    viewModel.inviteCount.observe(viewLifecycleOwner){
                        tab.text = resources.getString(R.string.huddle_invite_text, it)
                    }
                }
            }
        }.attach()

    }

    private fun observe() {
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
        val actionBar = (activity as MainActivity?)?.supportActionBar
    }

    private fun setTabNumber() {
        if (arguments?.isEmpty == false) {
            arguments?.let { 0 }?.let { switchToTab(it) }
        }
    }

    private fun switchToTab(number: Int) {
        lifecycleScope.launch {
            delay(50)
            withContext(Dispatchers.Main) {
                binding.requestInvitePager.setCurrentItem(number, true)
            }
        }
    }

}