package com.app.messej.ui.home.publictab.huddles


import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleTab
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.ProfileRepository
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class PublicHuddlesSearchViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val huddleRepo = HuddlesRepository(getApplication())
    private val profileRepo = ProfileRepository(getApplication())
    var searchKeyword = MutableLiveData<String>(null)
    private val searchTerm = MutableLiveData("")

    private val _currentTab = MutableLiveData<HuddleTab?>(null)
    val currentTab: LiveData<HuddleTab?> = _currentTab

    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                it?: return@collect
                if (it.isBlank()){
                    searchTerm.postValue("")
                }else{
                    searchTerm.postValue(it)
                }
            }
        }
    }

    private val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    fun setTab(tab: HuddleTab) {
        _currentTab.postValue(tab)
    }

    private data class HuddleListParams(
        val search: String?,
        val huddleTab: HuddleTab?,
    )

    private val huddleListParams: MediatorLiveData<HuddleListParams> by lazy {
        val med = MediatorLiveData<HuddleListParams>(null)
        fun updateHuddleList() {
            huddleListParams.postValue(HuddleListParams(searchTerm.value.toString(), currentTab.value))
        }
        med.addSource(searchTerm) { updateHuddleList() }
        med.addSource(currentTab) { updateHuddleList() }
        med
    }

    val user: CurrentUser get() = accountRepo.user

    private val _huddleList = huddleListParams.switchMap {
        huddleRepo.getHuddlesSearchPager(type = HuddleType.PUBLIC, searchTerm.value.toString(), currentTabInvolvement).liveData.cachedIn(viewModelScope)
    }

    private val currentTabInvolvement: HuddleInvolvement?
        get() {
            return when (currentTab.value) {
                HuddleTab.TAB_MINE -> HuddleInvolvement.MANAGER
                HuddleTab.TAB_ADMIN -> HuddleInvolvement.ADMIN
                HuddleTab.TAB_JOINED -> HuddleInvolvement.PARTICIPANT
                else -> null
            }
        }

    val huddleList: MediatorLiveData<PagingData<PublicHuddlesAdapter.HuddleUIModel>?> by lazy {
        val med = MediatorLiveData<PagingData<PublicHuddlesAdapter.HuddleUIModel>?>(null)
        fun updateHuddleList() {
            _dataLoading.value = false
            val list: PagingData<PublicHuddlesAdapter.HuddleUIModel>? = _huddleList.value?.map { item ->
                PublicHuddlesAdapter.HuddleUIModel.LocalHuddleUIModel(item, false)
            }
            huddleList.postValue(list)
        }
        med.addSource(_huddleList) { updateHuddleList() }
        med
    }

    fun resetSearch() {
        searchKeyword.postValue("")
    }
}