package com.app.messej.ui.home.publictab.flash.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentFlashFilterBinding
import com.app.messej.ui.common.CategoryDropdownAdapter

class FlashFilterFragment : Fragment() {

    private lateinit var binding: FragmentFlashFilterBinding
    private val viewModel: FlashFilterViewModel by viewModels()
    private val args: FlashFilterFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_filter, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    private fun setup() {
        viewModel.setFilterValues(args)

        binding.countryPicker.apply {
            setOnCountryChangeListener {
                viewModel.setCountry(binding.countryPicker.selectedCountryNameCode)
            }

            setCountryForNameCode(args.country)
        }

        binding.resetBtn.setOnClickListener {
            viewModel.resetValues()
        }
    }

    private fun observer() {
        viewModel.huddleCategoryList.observe(viewLifecycleOwner) { cats ->
            if (cats != null) {
                val adapter = CategoryDropdownAdapter(requireContext(), cats)
                (binding.categoryDropdown.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.setCategoryId(item.categoryId)
                    }
                    viewModel.selectedCategory.value?.let { cat ->
                        val index = cats.indexOfFirst { it.categoryId == cat }
                        if (index == -1) return@let
                        adapter.setSelectedPos(index)
                        setText(cats[index].name)
                    }
                }
            }
        }
    }

}