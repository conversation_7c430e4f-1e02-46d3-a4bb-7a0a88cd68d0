package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.entity.HuddleReportedMessage
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.model.enums.ReportedTab
import com.app.messej.data.model.enums.UserRole
import com.app.messej.databinding.FragmentReportedMessagesBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.chat.BaseChatDisplayFragment
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.bumptech.glide.Glide
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView
import com.makeramen.roundedimageview.RoundedImageView
import com.stfalcon.imageviewer.StfalconImageViewer

class ReportedMessagesFragment : BaseChatDisplayFragment() {

    private lateinit var binding: FragmentReportedMessagesBinding

    override val viewModel : ReportedMessagesViewModel by viewModels()

    companion object {
        const val HUDDLE_ID = "huddleId"

        fun getBundle(huddleId: Int) = Bundle().apply {
            putInt(HUDDLE_ID, huddleId)
        }

        fun parseBundle(bundle: Bundle?): Int? {
            val huddleId = bundle?.getInt(HUDDLE_ID)
            return huddleId
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_reported_messages, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setEmptyView()
        setup()
        observe()
    }

    private fun setup() {
        viewModel.setHuddleId(parseBundle(arguments), ReportedTab.TAB_MESSAGES)
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            message = R.string.chat_eds_message_reported
        )
    }

    private fun observe() {
        viewModel.onDeleteMessage.observe(viewLifecycleOwner) {
            if (it) {
                mAdapter?.refresh()
            }
        }
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.messagesList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView
        get() = binding.multiStateView

    override val reversedLayout: Boolean
        get() = false

    override val provideAdapter: ChatAdapter
        get() = ReportedMessagesListAdapter(layoutInflater, viewModel.user.id, this, object: ReportedMessagesListAdapter.ReportActionListener {
            override fun onDeleteAction(item: HuddleReportedMessage, position: Int) {
                showDeleteAlertDialog(item)
            }

            override fun onViewReporters(item: HuddleReportedMessage, position: Int) {
                findNavController().navigateSafe(HuddleReportsFragmentDirections.actionReportedMessagesFragmentToReportedParticipantsFragment(item.huddleId, item.reportId, item.reportsCount,
                                                                                                                                              ReportToManagerType.MESSAGE))
            }

        })

    override fun showSelectionMode(show: Boolean) {}

    override fun onMediaDownload(view: View, msg: AbstractChatMessageWithMedia, position: Int) {
        val message = msg.message as HuddleReportedMessage
        if(msg.offlineMedia==null && message.deleted) {
            Toast.makeText(requireContext(),R.string.chat_message_deleted, Toast.LENGTH_SHORT).show()
        }
        else super.onMediaDownload(view, msg, position)
    }

    override fun onMediaImageTap(view: View, msg: AbstractChatMessageWithMedia, position: Int) {
        val message = msg.message as HuddleReportedMessage
        if(msg.offlineMedia==null && message.deleted) {
            message.mediaMeta?.thumbnail?.let { media ->

                StfalconImageViewer
                    .Builder(context, listOf(media)) { imageView, image ->
                        Glide.with(requireContext())
                            .load(image)
                            .into(imageView)
                    }
                    .withTransitionFrom(view as RoundedImageView)
                    .withHiddenStatusBar(false)
                    .show()
            }
        }
        else {
            super.onMediaImageTap(view, msg, position)
        }
    }

    @SuppressLint("MissingInflatedId")
    private fun showDeleteAlertDialog(item: HuddleReportedMessage) {
        val deleteConfirmationDialogView = layoutInflater.inflate(R.layout.layout_custom_checkbox, null)
        val checkBox = deleteConfirmationDialogView.findViewById<CheckBox>(R.id.block_user_checkbox)
        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setTitle(getString(R.string.huddle_reported_message_delete_confirmation_title))
            .setMessage(getString(R.string.chat_delete_confirm_message))
            .setCancelable(false)
            .setPositiveButton(getString(R.string.common_delete)) { _, _ ->
                viewModel.deleteMessage(item, checkBox.isChecked)
            }.setNegativeButton(getString(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }

        if (item.senderRole != UserRole.MANAGER) {
            dialog.setView(deleteConfirmationDialogView)
        }

        dialog.show()
    }
}