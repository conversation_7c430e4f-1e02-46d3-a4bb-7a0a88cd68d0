package com.app.messej.ui.home.publictab.huddles.chat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.databinding.FragmentRemovedDearsBinding
import com.app.messej.databinding.LayoutConfirmCancelBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class RemovedDearsFragment : Fragment(), HuddleRemovedDearsAdapter.ActionListener {


    private lateinit var binding: FragmentRemovedDearsBinding
    private val viewModel: RemovedDearsViewModel by viewModels()
    private val args: RemovedDearsFragmentArgs by navArgs()

    private var huddleRemovedDearsAdapter: HuddleRemovedDearsAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_removed_dears, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        viewModel.setHuddleId(args.huddleId)
        initAdapter()
    }

    private fun observe() {
        viewModel.removedDears.observe(viewLifecycleOwner) {
            huddleRemovedDearsAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun initAdapter() {
        huddleRemovedDearsAdapter = HuddleRemovedDearsAdapter(this)

        val layoutManParticipant = LinearLayoutManager(context)

        binding.removeDearsList.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = huddleRemovedDearsAdapter
        }

        huddleRemovedDearsAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                binding.customActionBar.toolBarTitle.text =if(itemCount<1) getString(R.string.tribe_title_removed_dears)else getString(R.string.tribe_title_removed_dears_count,itemCount)
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.no_removed_dears)
        }
    }

    override fun addAgainClick(item: Participant) {

        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutConfirmCancelBinding>(layoutInflater, R.layout.layout_confirm_cancel, null, false)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.nickNameTitle.text = getString(R.string.are_you_sure_you_want_to_add_the_user_again_to_the_tribe)
            view.actionConfirm.text=resources.getString(R.string.common_yes)
            view.actionConfirm.setOnClickListener {
                viewModel.addRemovedUser(args.huddleId, item.id)
                Toast.makeText(requireContext(), getString(R.string.user_successfully_added_to_the_tribe_again), Toast.LENGTH_SHORT).show()
                dismiss()
            }
            view.actionCancel.setOnClickListener {
                dismiss()
            }
        }



    }


}