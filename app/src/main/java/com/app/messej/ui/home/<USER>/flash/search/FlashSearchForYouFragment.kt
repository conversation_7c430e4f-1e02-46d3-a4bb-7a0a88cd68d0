package com.app.messej.ui.home.publictab.flash.search

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.GridLayoutManager
import com.app.messej.R
import com.app.messej.databinding.FragmentFlashSearchForYouBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.publictab.flash.myflash.FlashListAdapter
import com.app.messej.ui.home.publictab.flash.myflash.FlashVideoUIModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.isTabletScreen
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class FlashSearchForYouFragment : Fragment() {

    private lateinit var binding:FragmentFlashSearchForYouBinding

    private val viewModel : FlashSearchViewModel by navGraphViewModels(R.id.nav_flash_search)

    private var mAdapter: FlashListAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_search_for_you, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            message = R.string.flash_search_feed_eds
        )
    }

    private fun observe() {
        viewModel.flashFeedList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }
        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            it?.let { vs ->
                binding.multiStateView.viewState = vs
            }
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = FlashListAdapter(object : FlashListAdapter.FlashClickListener {
            override fun onFlashClicked(pos: Int, flash: FlashVideoUIModel) {
                if (flash.flashVideo.isAvailable) {
                    viewModel.setActiveItem(pos)
                    findNavController().navigateSafe(FlashSearchFragmentDirections.actionFlashSearchFragmentToFlashSearchPlayerFragment())
                }
            }

            override fun onFlashLongPressed(pos: Int, flash: FlashVideoUIModel, view: View) {

            }
        }, true)

        val layoutMan = GridLayoutManager(requireContext(), if(isTabletScreen) 5 else 3)

        binding.flashList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                viewModel.setViewState(state)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }



}