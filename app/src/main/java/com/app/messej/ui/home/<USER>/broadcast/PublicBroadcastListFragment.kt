package com.app.messej.ui.home.publictab.broadcast

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.enums.BroadcastTab
import com.app.messej.data.model.enums.UserProfileContext
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutPublicBroadcastListBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class PublicBroadcastListFragment : Fragment() {

    protected lateinit var tab: BroadcastTab

    companion object {
        const val ARG_TAB = "tab"

        fun getTabBundle(tab: BroadcastTab) = Bundle().apply {
            putInt(ARG_TAB,tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): BroadcastTab {
            val tabInt = bundle?.getInt(ARG_TAB)?:0
            return BroadcastTab.values()[tabInt]
        }
    }

    protected lateinit var binding: LayoutPublicBroadcastListBinding

    protected val viewModel: PublicBroadcastViewModel by activityViewModels()

    private var mAdapter: PublicBroadcastListAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_public_broadcast_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        tab = parseTabBundle(arguments)
        Log.w("PBBF", "list: $tab", )
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
        setEmptyView()
    }

    private fun setEmptyView() {
        val emptyView = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        when (tab){
            BroadcastTab.TAB_DEARS -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_dears_list,
                    message = R.string.broadcast_list_dears_empty_text,
                )
            }
            BroadcastTab.TAB_FANS -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_fans_list,
                    message = R.string.broadcast_list_fans_empty_text,
                )
            }
            BroadcastTab.TAB_LIKERS -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_likers_list,
                    message = R.string.broadcast_list_likers_empty_text,
                )
            }
            else -> {}
        }
    }

    private fun observe() {
        when (tab) {
            BroadcastTab.TAB_DEARS -> {
                Log.d("PBLF", "observe: loading dears")
                viewModel.broadcastDearsList.observe(viewLifecycleOwner) { pagingData ->
                    pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
                }
            }

            BroadcastTab.TAB_FANS -> {
                Log.d("PBLF", "observe: loading fans")
                viewModel.broadcastFansList.observe(viewLifecycleOwner) { pagingData ->
                    pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
                }
            }

            BroadcastTab.TAB_LIKERS -> {
                Log.d("PBLF", "observe: loading likers")
                viewModel.broadcastLikersList.observe(viewLifecycleOwner) { pagingData ->
                    pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
                }
            }
            else -> {}
        }

    }

    private fun initAdapter() {
        mAdapter = PublicBroadcastListAdapter(object :PublicBroadcastListAdapter.ItemListener{
            override fun onItemClick(user: UserRelative) {
//                val userType = PublicUserProfileLoaderFragment.getUserType(user.citizenship)
                if (tab==BroadcastTab.TAB_LIKERS){
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(user.id,false,UserProfileContext.BROADCAST_LIST))
                }else{
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(user.id))
                }
            }
        })

        val layoutMan = LinearLayoutManager(context)
        binding.broadcastList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
                viewModel.setCanBroadcast(tab,state==MultiStateView.ViewState.CONTENT)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.w("PBLF", "showing tab = $tab", )
        if (mAdapter?.itemCount!! > 0) {
            mAdapter?.refresh()
        }
    }

}