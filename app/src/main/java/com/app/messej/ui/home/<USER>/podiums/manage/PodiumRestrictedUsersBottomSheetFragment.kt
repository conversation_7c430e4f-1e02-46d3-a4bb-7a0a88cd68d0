package com.app.messej.ui.home.publictab.podiums.manage

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.navGraphViewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.databinding.FragmentPodiumRestrictedBottomSheetBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.home.publictab.podiums.manage.PodiumBlockedViewModel.PodiumBlockedTab
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.google.android.material.button.MaterialButton

class PodiumRestrictedUsersBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private val viewModel: PodiumBlockedViewModel by viewModels()
    private val liveViewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    private lateinit var binding: FragmentPodiumRestrictedBottomSheetBinding

    private lateinit var mBlockedFrozenPagerAdapter: FragmentStateAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_restricted_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true

        setup()
        observe()
    }

    fun setup(){
        viewModel.setCurrentTab(PodiumBlockedTab.TAB_BLOCKED)
        mBlockedFrozenPagerAdapter = object: FragmentStateAdapter(this) {
            override fun getItemCount(): Int = if(liveViewModel.podiumKind.value==PodiumKind.THEATER) 1 else PodiumBlockedTab.entries.size

            override fun createFragment(position: Int): Fragment {
                return when(PodiumBlockedTab.entries[position]) {
                    PodiumBlockedTab.TAB_BLOCKED -> {
                        PodiumBlockedUsersFragment()
                    }
                    PodiumBlockedTab.TAB_FROZEN -> {
                        PodiumFrozenUsersFragment()
                    }
                }
            }
        }

        binding.podiumPager.apply {
            isUserInputEnabled = false
            adapter = mBlockedFrozenPagerAdapter
        }

        viewModel.currentTab.value?.let {
            binding.podiumPager.setCurrentItem(it.ordinal, false)
        }

        binding.btnBlockedUsers.setOnClickListener {
            viewModel.setCurrentTab(PodiumBlockedTab.TAB_BLOCKED)
            (it as MaterialButton).isChecked = true
        }

        binding.btnFrozenAccounts.setOnClickListener {
            viewModel.setCurrentTab(PodiumBlockedTab.TAB_FROZEN)
            (it as MaterialButton).isChecked = true
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun observe(){
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            if (binding.podiumPager.currentItem==it.ordinal) return@observe
            binding.podiumPager.setCurrentItem(it.ordinal,false)
        }

        liveViewModel.podiumKind.observe(viewLifecycleOwner) {
            binding.kind = it
            mBlockedFrozenPagerAdapter.notifyDataSetChanged()
        }
    }
}