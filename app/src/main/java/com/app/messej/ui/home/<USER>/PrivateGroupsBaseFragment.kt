package com.app.messej.ui.home.privatetab

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.enums.HuddleAction
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutPrivateGroupsBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.publictab.huddles.PublicHuddlesAdapter
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView


abstract class PrivateGroupsBaseFragment : Fragment(), MenuProvider {

    protected abstract var binding: LayoutPrivateGroupsBinding

    private var mAdapter: PublicHuddlesAdapter? = null

    protected val viewModel: PrivateGroupsViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()

        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            image = R.drawable.im_eds_group,
            message = if (viewModel.isVisitor) R.string.private_group_visitor_eds else R.string.private_group_eds,
            action = if (viewModel.isVisitor) null else R.string.private_group_eds_action
        ) {
            ensureInteractionAllowed {
                navigateToCreate()
            }
        }

        binding.createGroupFab.setOnClickListener {
            ensureInteractionAllowed {
                navigateToCreate()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if(mAdapter?.itemCount!! > 0) {
            mAdapter?.refresh()
        }
    }

    private fun observe() {
        viewModel.huddleList.observe(viewLifecycleOwner) {
            it?:return@observe
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        viewModel.huddleSelectionMode.observe(viewLifecycleOwner) {
            showSelectionMode(it)
        }

        viewModel.onItemChange.observe(viewLifecycleOwner) {
            mAdapter?.notifyItemChanged(it)
        }

        viewModel.actionIsPin.observe(viewLifecycleOwner) { pin ->
            actionMode?.menu?.apply {
                findItem(R.id.action_pin).setIcon(if(pin) R.drawable.ic_pin else R.drawable.ic_unpin)
            }
        }
        viewModel.actionIsMute.observe(viewLifecycleOwner) { mute ->
            actionMode?.menu?.apply {
                findItem(R.id.action_mute).setIcon(if(mute) R.drawable.ic_mute else R.drawable.ic_unmute)
            }
        }

        viewModel.onPinLimitExceed.observe(viewLifecycleOwner) {
            Toast.makeText(context, resources.getString(R.string.group_pin_limit_exceed, it), Toast.LENGTH_SHORT).show()
        }

        viewModel.onPinMuteAction.observe(viewLifecycleOwner){
            when(it){
                HuddleAction.PIN -> Toast.makeText(requireContext(), resources.getString(R.string.huddle_list_pinned), Toast.LENGTH_SHORT).show()
                HuddleAction.UNPIN -> Toast.makeText(requireContext(), resources.getString(R.string.huddle_list_unpinned), Toast.LENGTH_SHORT).show()
                HuddleAction.MUTE -> Toast.makeText(requireContext(), resources.getString(R.string.huddle_list_muted), Toast.LENGTH_SHORT).show()
                HuddleAction.UNMUTE -> Toast.makeText(requireContext(), resources.getString(R.string.huddle_list_unmuted), Toast.LENGTH_SHORT).show()
                else -> {}
            }
        }
        viewModel.onHuddleActionError.observe(viewLifecycleOwner) {
            showSnackbar(it)
        }
        viewModel.onHuddleListRequiresUpdate.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
        }
    }

    override fun onPause() {
        super.onPause()
        if(activity?.isChangingConfigurations != true) {
            viewModel.exitSelectionMode()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_home_groups,menu)
    }

    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        menu.findItem(R.id.action_more).isVisible = viewModel.user.citizenship!= UserCitizenship.VISITOR
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> {
                navigateToSearch()
            }
            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    private fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_home_groups_more, popup.menu)
        popup.setForceShowIcon(true)
        val createGroupMenuItem = popup.menu.findItem(R.id.action_create_group)
        createGroupMenuItem.isEnabled = !viewModel.isVisitor
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_create_group -> ensureInteractionAllowed { navigateToCreate() }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = PublicHuddlesAdapter(layoutInflater,viewModel.user.id, object: PublicHuddlesAdapter.ItemListener {
            override fun onItemClick(item: AbstractHuddle, position: Int) {
                if (viewModel.selectHuddle(item as PublicHuddle,position)) return
                navigateToChat(item.id)
            }

            override fun onItemLongClick(item: PublicHuddle, position: Int) {
                viewModel.enterSelectionMode(item,position)
            }

            override fun isVisitor(): Boolean {
              return true
            }

        })

        val layoutMan = LinearLayoutManager(context)

        binding.huddleList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                binding.createGroupFab.apply { if (binding.multiStateView.viewState==MultiStateView.ViewState.CONTENT && !viewModel.isVisitor) show() else hide() }
                viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition()==0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }

    var actionMode: ActionMode? = null

    private fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_huddles_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_pin -> viewModel.togglePin()
                        R.id.action_mute -> viewModel.toggleMute()
                    }
                    return false
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }

    protected abstract fun navigateToSearch()
    protected abstract fun navigateToCreate()
    protected abstract fun navigateToChat(huddleId: Int)

}