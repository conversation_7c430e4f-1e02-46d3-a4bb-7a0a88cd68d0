package com.app.messej.ui.home.publictab.maidan

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.MaidanTab
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.databinding.FragmentPodiumMaidanInnerListBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.checkIfJoinHidden
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView


class PodiumMaidanInnerListFragment : Fragment() {

    private lateinit var binding: FragmentPodiumMaidanInnerListBinding

    private var mAdapter: PodiumMaidanAdapter? = null

    private val viewModel: PublicMaidanViewModel by activityViewModels()

    private lateinit var tab: MaidanTab

    companion object {
        const val ARG_TAB = "tab"

        fun getTabBundle(tab: MaidanTab) = Bundle().apply {
            putInt(ARG_TAB, tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): MaidanTab {
            val tabInt = bundle?.getInt(ARG_TAB) ?: 0
            return MaidanTab.entries[tabInt]
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_maidan_inner_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
//        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tab = parseTabBundle(arguments)
        Log.d("PILF", "onViewCreated: $tab")
        setup()
        observe()
    }

    private var justStarted = true
    override fun onResume() {
        super.onResume()
        if (justStarted) justStarted = false
        else mAdapter?.refresh()
    }

    private fun setup() {
        setEmptyView()
        initAdapter()
        binding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
            }
        }

        binding.btnFriends.setOnClickListener {
            viewModel.setSubTab(tab,MaidanTab.MaidanSubTab.FRIENDS)
        }
        binding.btnAll.setOnClickListener {
            viewModel.setSubTab(tab,MaidanTab.MaidanSubTab.ALL)
        }
    }

    private fun observe() {
        Log.d("PHILF", "observe: loading $tab")
        when (tab) {
            MaidanTab.CHALLENGE -> viewModel.maidanChallengeList
            MaidanTab.WATCH -> viewModel.maidanWatchList
            else -> null
        }?.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }

        when (tab) {
            MaidanTab.CHALLENGE -> viewModel.challengeSubTab
            MaidanTab.WATCH -> viewModel.watchSubTab
            else -> null
        }?.observe(viewLifecycleOwner) { subTab ->
            binding.subTab = subTab
        }

        viewModel.maidanMeta.observe(viewLifecycleOwner) {
            binding.allCount = when (tab) {
                MaidanTab.CHALLENGE -> it?.joinCount
                MaidanTab.WATCH -> it?.watchCount
                else -> null
            }
        }

        when (tab) {
            MaidanTab.CHALLENGE -> viewModel.maidanChallengeFriendsCount
            MaidanTab.WATCH -> viewModel.maidanWatchFriendsCount
            else -> null
        }?.observe(viewLifecycleOwner) { count ->
            binding.friendsCount = count
        }
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
//        when (tab) {
//            PodiumTab.MY_PODIUM -> {
//                emptyViewBinding.prepare(
//                    image = R.drawable.im_eds_podium, message = R.string.podium_empty_mine, action = R.string.podium_create_prompt
//                ) {
//                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePodiumFragment())
//                }
//            }
//
//            PodiumTab.LIVE_PODIUM -> {
//                emptyViewBinding.prepare(
//                    image = R.drawable.im_eds_podium, message = R.string.podium_empty_live
//                )
//            }
//
//            PodiumTab.LIVE_FRIENDS -> null
//        }
    }

    private fun initAdapter() {
        mAdapter = PodiumMaidanAdapter(object : PodiumMaidanAdapter.PodiumActionListener {
            override fun onPodiumClicked(pod: Podium) {
                if (pod.managerId==viewModel.user.id) {
                    val id = if (pod.competitorUserId==viewModel.user.id) pod.competitorPodiumId else pod.id
                    id?: return
                    val action = PublicMaidanFragmentDirections.actionGlobalNavLivePodium(
                        id,
                        kind = PodiumKind.MAIDAN.ordinal
                    )
                    findNavController().navigateSafe(action)
                } else {
                    if (pod.canChallenge) {
                        val action = PublicMaidanFragmentDirections.actionGlobalPodiumMaidanChallengeBottomSheetFragment(pod.id, fromPodium = false)
                        findNavController().navigateSafe(action)
                    } else {
                        checkIfJoinHidden(viewModel.user) { hidden ->
                            val action = PublicMaidanFragmentDirections.actionGlobalNavLivePodium(
                                pod.id,
                                kind = PodiumKind.MAIDAN.ordinal,
                                joinHidden = hidden
                            )
                            findNavController().navigateSafe(action)
                        }
                    }
                }
            }
        })

        val layoutMan = LinearLayoutManager(context)

        binding.podiumList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                if (loadState.refresh !is LoadState.Loading) {
                    binding.swipeRefresh.isRefreshing = false
                }
                if (viewModel.currentTab.value == tab) {
                    viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
                }
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }
}