package com.app.messej.ui.home.publictab.authorities.stateAffairs

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.StateAffairsRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch

class StateAffairsMainViewModel(application: Application) : AndroidViewModel(application) {

    private val stateAffairsRepository= StateAffairsRepository(getApplication())
    private val datastore: FlashatDatastore = FlashatDatastore()

    private val _ministerCount = MutableLiveData<Int?>(null)
    val ministerCount: LiveData<Int?> = _ministerCount
    private val _ambassadorCount = MutableLiveData<Int?>(null)
    val ambassadorCount: LiveData<Int?> = _ambassadorCount

    private val _officerCount = MutableLiveData<Int?>(null)
    val officerCount: LiveData<Int?> = _officerCount


   val presidentId: LiveData<String> = datastore.getCurrentPresidentId().filterNotNull().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val stateAffairTribes by lazy { stateAffairsRepository.getStateAffairsTribesPager().liveData.cachedIn(viewModelScope)}

    val stateAffairMinisters by lazy { stateAffairsRepository.getStateAffairsCitizenshipUserPager(UserCitizenship.MINISTER){_ministerCount.postValue(it)}.liveData.cachedIn(viewModelScope)}
    val stateAffairAmbassadors by lazy { stateAffairsRepository.getStateAffairsCitizenshipUserPager(UserCitizenship.AMBASSADOR){_ambassadorCount.postValue(it)}.liveData.cachedIn(viewModelScope)}
    val stateAffairOfficers by lazy { stateAffairsRepository.getStateAffairsCitizenshipUserPager(UserCitizenship.OFFICER){_officerCount.postValue(it)}.liveData.cachedIn(viewModelScope)}


    val stateAffairSkillFullFlashaters by lazy { stateAffairsRepository.getStateAffairsSkillFullFlashtersPager().liveData.cachedIn(viewModelScope)}
    val stateAffairPopularFlashaters by lazy { stateAffairsRepository.getStateAffairsPopularFlashtersPager().liveData.cachedIn(viewModelScope)}
    val stateAffairGenerousFlashaters by lazy { stateAffairsRepository.getStateAffairsGenerousFlashtersPager().liveData.cachedIn(viewModelScope)}

    private val _StateStateStatistics = MutableLiveData<StateAffairsTotalUsers?>(null)
    val StateStateStatistics: LiveData<StateAffairsTotalUsers?> = _StateStateStatistics


    fun getStateStateStatistics(){
        viewModelScope.launch {
            when(val response= stateAffairsRepository.getStateStateStatistics()){
                is ResultOf.Success ->{
                    _StateStateStatistics.postValue(response.value)
                }
                else -> {
                }
            }
        }
    }

    val isPresidentAvailable:Boolean
    get() = presidentId.value!=null

}