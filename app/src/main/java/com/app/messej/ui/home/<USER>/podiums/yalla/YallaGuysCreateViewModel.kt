package com.app.messej.ui.home.publictab.podiums.yalla

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.YallaGuysPrizePreset
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


class YallaGuysCreateViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)
    private val accountRepo = AccountRepository(application)

    private val _podiumId = MutableLiveData<String>()
    val podiumId: LiveData<String> = _podiumId

    val user: CurrentUser get() = accountRepo.user

    fun setParams(podiumId: String) {
        _podiumId.value = podiumId
    }

    data class SelectableChallengeType(
        val type: ChallengeType,
        var isSelected: Boolean,
    )

    private val _challengeTypes = ChallengeType.forYallaGuys

    private val _selectedChallengeType = MutableLiveData<ChallengeType?>(null)

    fun selectChallengeType(type: ChallengeType) {
        if (createChallengeLoading.value==true) return
        _selectedChallengeType.postValue(type)
    }

    val challengeTypes = _selectedChallengeType.map {
        val types = _challengeTypes.map { t -> SelectableChallengeType(t, t == it) }
        return@map types
    }

    private val _prizePreset = MutableLiveData<YallaGuysPrizePreset>()
    val prizePreset: LiveData<YallaGuysPrizePreset> = _prizePreset

    private val _isParticipant = MutableLiveData<Boolean>()
    val isParticipant: LiveData<Boolean> = _isParticipant

    fun setParticipant(isChecked: Boolean) {
        _isParticipant.value = isChecked
    }

    val challengePrizeString = MutableLiveData<String>()

    fun setPrizePreset(fee: YallaGuysPrizePreset) {
        if (createChallengeLoading.value==true) return
        when (fee) {
            YallaGuysPrizePreset.CUSTOM -> challengePrizeString.postValue("")
            else -> challengePrizeString.postValue(fee.coinsStr)
        }
        _prizePreset.postValue(fee)
    }

    val isChallengePrizeValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val priceString = challengePrizeString.value.orEmpty().trim()
            val price = priceString.toIntOrNull() ?: 0

            val valid = when (_prizePreset.value) {
                YallaGuysPrizePreset.CUSTOM -> price > YallaGuysPrizePreset.MINIMUM_CUSTOM_CHALLENGE_PRIZE
                null -> false
                else -> true
            }
            med.postValue(valid)
        }
        med.addSource(_prizePreset) { update() }
        med.addSource(challengePrizeString) { update() }
        med
    }

    val showChallengePrizeError = isChallengePrizeValid.map {
        return@map if (challengePrizeString.value.isNullOrEmpty()) false
        else !it
    }

    val createButtonEnable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val prizeValid = isChallengePrizeValid.value == true
            val typeSelected = _selectedChallengeType.value != null
            med.value = prizeValid && typeSelected && createChallengeLoading.value!=true
        }
        med.addSource(isChallengePrizeValid) { update() }
        med.addSource(_selectedChallengeType) { update() }
        med.addSource(createChallengeLoading) { update() }
        med
    }

    val createChallengeLoading = MutableLiveData(false)

    val challengeCreated = LiveEvent<String>()
    val onInsufficientBalance = LiveEvent<Boolean>()
    val onChallengeCreateError = LiveEvent<String>()

    fun createChallenge() {
        createChallengeLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val challengeType = _selectedChallengeType.value ?: return@launch
            try {
                val prize = challengePrizeString.value?.toInt()?:100
                when (val result = podiumRepository.createChallenge(
                    podiumId = _podiumId.value.toString(), type = challengeType, isYalla = true, participate = isParticipant.value, prize = prize
                )) {
                    is ResultOf.Success -> {
                        result.value.let {
                            challengeCreated.postValue(prize.toString())
                        }
                    }
                    is ResultOf.APIError -> {
                        if (result.code == 402) {
                            onInsufficientBalance.postValue(true)
                        } else if(APIUtil.canShowAPIErrorMessage(result.code)) {
                            onChallengeCreateError.postValue(result.errorMessage())
                        }
                    }
                    is ResultOf.Error -> {}
                }
                createChallengeLoading.postValue(false)
            } catch (e: Exception) {
                Log.d("YGCVM", "startChallenge: ${e.message}")
            }
        }
    }
}