package com.app.messej.ui.chat.imageedit

import android.annotation.SuppressLint
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.utils.MediaUtils
import com.app.messej.databinding.FragmentPostImageEditBinding
import com.app.messej.ui.customviews.VerticalSlideColorPicker
import ja.burhanrashid52.photoeditor.PhotoEditor
import ja.burhanrashid52.photoeditor.SaveFileResult
import ja.burhanrashid52.photoeditor.shape.ShapeBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException

class PostImageEditFragment : Fragment() {

    private var mPhotoEditor: PhotoEditor?=null
    private lateinit var binding: FragmentPostImageEditBinding
    private var mSelectedColor:Int?=null
    val navArgs:PostImageEditFragmentArgs by navArgs()
    val viewModel:PostImageEditViewModel by viewModels()

    companion object {
        const val IMAGE_SAVE_REQUEST_KEY = "imageSaveRequestKey"
        const val IMAGE_SAVE_RESULT_KEY = "imageSaveResultKey"
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_post_image_edit, container, false)
        binding.lifecycleOwner=viewLifecycleOwner
        return binding.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.strokeType.observe(viewLifecycleOwner){
            binding.imgStrokeOne.setBackgroundColor(ContextCompat.getColor(requireContext(),R.color.transparent))
            binding.imgStrokeTwo.setBackgroundColor(ContextCompat.getColor(requireContext(),R.color.transparent))
            binding.imgStrokeThree.setBackgroundColor(ContextCompat.getColor(requireContext(),R.color.transparent))
            when(it){
                PostImageEditViewModel.StrokeWidth.ONE -> binding.imgStrokeOne.setBackgroundResource(R.drawable.bg_stroke_selection)
                PostImageEditViewModel.StrokeWidth.TWO -> binding.imgStrokeTwo.setBackgroundResource(R.drawable.bg_stroke_selection)
                PostImageEditViewModel.StrokeWidth.THREE -> binding.imgStrokeThree.setBackgroundResource(R.drawable.bg_stroke_selection)
                null -> {}
            }
        }
    }

    override fun onResume() {
        super.onResume()
        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                mPhotoEditor?.clearAllViews()
                savePhoto()
            }
        })
    }

    private fun setup() {
        viewModel.getDestinationFile(navArgs.destination!!)
        mPhotoEditor = PhotoEditor.Builder(requireActivity(), binding.photoEditorView).setPinchTextScalable(true).build()
        mPhotoEditor?.setBrushDrawingMode(true)
        viewModel.setStrokeType(PostImageEditViewModel.StrokeWidth.ONE)
        showBrush(false)
        val shapeBuilder = ShapeBuilder()
            .withShapeColor(ContextCompat.getColor(requireContext(),R.color.colorPrimary))
            .withShapeSize(10f)
        mPhotoEditor?.setShape(shapeBuilder)

        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                binding.photoEditorView.source.setImageURI(navArgs.source)
            }
        }
        binding.fabPhotoDone.setOnClickListener{
            savePhoto()
        }

        binding.imgPhotoEditBack.setOnClickListener {
            mPhotoEditor?.clearAllViews()
            savePhoto()
        }

        binding.imgStrokeOne.setOnClickListener{
            viewModel.setStrokeType(PostImageEditViewModel.StrokeWidth.ONE)
            val shapeBuilder = ShapeBuilder()
                .withShapeSize(10f)
                .withShapeColor(mPhotoEditor?.brushColor?:R.color.colorPrimary)
            mPhotoEditor?.setShape(shapeBuilder)

        }

        binding.imgStrokeTwo.setOnClickListener{
            viewModel.setStrokeType(PostImageEditViewModel.StrokeWidth.TWO)
            val shapeBuilder = ShapeBuilder()
                .withShapeSize(30f)
                .withShapeColor(mPhotoEditor?.brushColor?:R.color.colorPrimary)
            mPhotoEditor?.setShape(shapeBuilder)
        }

        binding.imgStrokeThree.setOnClickListener{
            viewModel.setStrokeType(PostImageEditViewModel.StrokeWidth.THREE)
            val shapeBuilder = ShapeBuilder()
                .withShapeSize(60f)
                .withShapeColor(mPhotoEditor?.brushColor?:R.color.colorPrimary)
            mPhotoEditor?.setShape(shapeBuilder)
        }
        binding.imgPhotoEditRedo.setOnClickListener {
            mPhotoEditor?.redo()
        }

        binding.imgPhotoEditUndo.setOnClickListener {
            mPhotoEditor?.undo()
        }
        binding.colorPickerView.setOnColorChangeListener(object : VerticalSlideColorPicker.OnColorChangeListener{
            @SuppressLint("ResourceAsColor")
            override fun onColorChange(selectedColor: Int) {
                if (binding.colorPickerView.visibility == View.VISIBLE) {
                    val shapeBuilder = ShapeBuilder()
                        .withShapeColor(selectedColor)
                      mPhotoEditor?.setShape(shapeBuilder)
                    binding.imgPhotoEditPaint.setBackgroundColor(selectedColor)

                }
            }
        })
        binding.imgPhotoEditPaint.setOnClickListener {
            if(binding.colorPickerView.visibility==View.INVISIBLE){
                binding.colorPickerView.visibility=View.VISIBLE
                binding.strokeLayout.visibility=View.VISIBLE
                showBrush(true)
            }else{
                binding.colorPickerView.visibility=View.INVISIBLE
                binding.strokeLayout.visibility=View.GONE
                showBrush(false)
            }
        }
    }

    // TODO check permissions the right way
    private fun savePhoto() {
        lifecycleScope.launch {
            val imageResult = if (ActivityCompat.checkSelfPermission(requireActivity(), android.Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                  mPhotoEditor?.saveAsFile(imagePath = viewModel.destinationFile?.absolutePath.toString())
            } else {
                SaveFileResult.Failure(IOException("Image Not Saved"))
            }
            when(imageResult){
                is SaveFileResult.Failure -> {
                    Toast.makeText(requireContext(),imageResult.exception.message,Toast.LENGTH_SHORT).show()
                }

                SaveFileResult.Success -> {
                    findNavController().popBackStack()
                    setFragmentResult(IMAGE_SAVE_REQUEST_KEY, bundleOf(IMAGE_SAVE_RESULT_KEY to MediaUtils.getUriForFile(requireContext(), viewModel.destinationFile!!)))
                }
                null -> Toast.makeText(requireContext(),"Save failed",Toast.LENGTH_SHORT).show()
            }
        }
    }

    @SuppressLint("ResourceAsColor")
    private fun showBrush(enableBrush: Boolean) {
        if (enableBrush) {
            val shapeBuilder = ShapeBuilder()
                .withShapeColor(mSelectedColor?:R.color.colorPrimary)
            mPhotoEditor?.setShape(shapeBuilder)
            mPhotoEditor?.setBrushDrawingMode(true)
        } else {
            binding.imgPhotoEditPaint.setBackgroundColor(ContextCompat.getColor(requireContext(),R.color.transparent))
            mPhotoEditor?.setBrushDrawingMode(false)
        }
    }

}