package com.app.messej.ui.home.publictab.flash

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.FlashEligibility
import com.app.messej.data.model.enums.FlashTab
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DDMMYYYY_DASHED
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.time.ZoneId
import java.time.ZonedDateTime

class PublicFlashViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(application)
    private val flashRepo = FlashRepository(application)

    val user: CurrentUser get() = accountRepo.user

    private var currentDateZonedTime = ZonedDateTime.now(ZoneId.systemDefault())
    private val currentDate = DateTimeUtils.format(currentDateZonedTime, FORMAT_DDMMYYYY_DASHED)

    private val _currentTab = MutableLiveData(FlashTab.default)
    val currentTab: LiveData<FlashTab?> = _currentTab.distinctUntilChanged()

    fun setCurrentTab(tab: FlashTab, skipIfSet: Boolean = false) {
        if (skipIfSet && currentTab.value != null) return
        _currentTab.value = tab
    }

    private val _flashEligibility = MutableLiveData<FlashEligibility?>(null)
    val flashEligibility : LiveData<FlashEligibility?> = _flashEligibility

    private val _actionLoading = MutableLiveData(false)
    val actionLoading : LiveData<Boolean> = _actionLoading

    val isEligibilityLoaded = LiveEvent<Boolean>()

    fun getFlashEligibilityDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            _actionLoading.postValue(true)
            val result = flashRepo.getFlashEligibilityDetails(currentDate = currentDate)
            when(result) {
                is ResultOf.Success -> {
                    _flashEligibility.postValue(result.value)
                    isEligibilityLoaded.postValue(true)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
            _actionLoading.postValue(false)
        }
    }

}