package com.app.messej.ui.home.businesstab.operations.tasks

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.business.PayoutHistory
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.ItemPayoutHistoryBinding

class PayoutHistoryAdapter :
    PagingDataAdapter<PayoutHistory, PayoutHistoryAdapter.PayoutHistoryViewHolder>(PayOutDiff) {
    inner class PayoutHistoryViewHolder(private val binding: ItemPayoutHistoryBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PayoutHistory,position:Int) = with(binding) {
            history = item
            binding.titlePayoutHistory.text = DateTimeUtils.format(item.parsedRequestedDate, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")
            binding.btnCollapseHistory.setOnClickListener{
                item.isExpanded = !item.isExpanded
                notifyItemChanged(position)
            }
        }
    }

    object PayOutDiff : DiffUtil.ItemCallback<PayoutHistory>() {
        override fun areItemsTheSame(oldItem: PayoutHistory, newItem: PayoutHistory) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PayoutHistory, newItem: PayoutHistory) = oldItem == newItem
    }

    override fun onBindViewHolder(holder: PayoutHistoryViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it,position) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PayoutHistoryViewHolder {
        val binding = ItemPayoutHistoryBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return PayoutHistoryViewHolder(binding)
    }

}