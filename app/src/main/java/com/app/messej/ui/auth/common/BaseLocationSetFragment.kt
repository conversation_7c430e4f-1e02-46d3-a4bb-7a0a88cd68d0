package com.app.messej.ui.auth.common

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.IntentSender
import android.location.Location
import android.os.Bundle
import android.text.InputType
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterLocationBinding
import com.app.messej.ui.auth.common.LocationSearchFragment.Companion.LOCATION_SEARCH_REQUEST_KEY
import com.app.messej.ui.auth.common.LocationSearchFragment.Companion.LOCATION_SEARCH_RESULT_KEY
import com.app.messej.ui.utils.FragmentExtensions.setTopInsets
import com.app.messej.ui.utils.MapUtils
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkLocationPermission
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.Granularity
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.google.android.gms.location.Priority
import com.google.android.gms.location.Priority.PRIORITY_BALANCED_POWER_ACCURACY
import com.google.android.gms.location.SettingsClient
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.GoogleMap.OnMarkerDragListener
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.Marker
import com.google.android.gms.maps.model.MarkerOptions
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.Place
import com.google.android.libraries.places.api.net.FetchPlaceRequest
import com.google.android.libraries.places.api.net.PlacesClient
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import okio.IOException
import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeUnit


abstract class BaseLocationSetFragment : Fragment(), OnMapReadyCallback {

    private lateinit var fusedLocationProviderClient: FusedLocationProviderClient
    protected lateinit var binding: FragmentRegisterLocationBinding

    protected abstract val viewModel: BaseLocationSetViewModel

    private var map: GoogleMap? = null
    private lateinit var placesClient: PlacesClient

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_location, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        checkLocationPermission(binding.root) {
            loadMap()
        }
    }

    private fun setup() {

        binding.textBox.editText?.inputType = InputType.TYPE_NULL
        binding.textBox.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    navigateToSearch()
                }
            }
        }
        binding.textBox.setStartIconOnClickListener {
            //findNavController().popBackStack()
        }

        binding.currentLocationButton.setOnClickListener {
            setToCurrentLocation()
        }

        binding.clearLocationButton.setOnClickListener {
            viewModel.clearLocation()
        }

        binding.confirmLocationButton.setOnClickListener {
            viewModel.confirmLocation()
        }

        setTopInsets(binding.locationCard)
    }

    private fun navigateToSearch() {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalLocationSearchFragment())
    }

    private fun observe() {

        viewModel.selectedLocation.observe(viewLifecycleOwner) {
            if (it != null) {

                Log.d("BLSF", "moving map to: ${it.lat!!.toDouble()}, ${it.long!!.toDouble()}")
                moveMapToLocation(it.lat!!.toDouble(), it.long!!.toDouble())
            } else {
                map?.clear()
            }
        }

        viewModel.setLocationError.observe(viewLifecycleOwner) {

        }

        setFragmentResultListener(LOCATION_SEARCH_REQUEST_KEY) { _, bundle ->
            bundle.getString(LOCATION_SEARCH_RESULT_KEY)?.let { placeId ->
                if (placeId == LocationSearchFragment.LOCATION_SEARCH_RESULT_CURRENT) {
                    Log.d("BLSF", "observe: setting current location")
                    setToCurrentLocation()
                } else {
                    Log.d("BLSF", "observe: setting place: $placeId")
                    val placeFields = listOf(Place.Field.ID, Place.Field.DISPLAY_NAME, Place.Field.LOCATION, Place.Field.FORMATTED_ADDRESS)
                    val placeRequest = FetchPlaceRequest.builder(placeId, placeFields).build()
                    MapUtils.initPlacesClient()
                    placesClient.fetchPlace(placeRequest).addOnCompleteListener { place ->
                        place.result?.place?.location?.let { loc ->
                            viewModel.selectLocation(loc)
                        }
                    }
                }
            }
        }
    }

    private fun moveMapToLocation(latitude: Double, longitude: Double) {
        map?.apply {
            animateCamera(CameraUpdateFactory.newLatLngZoom(LatLng(latitude, longitude), 13f))
            clear()
            addMarker(
                MarkerOptions().position(LatLng(latitude, longitude)).visible(true).draggable(true).flat(true).icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_map_marker))
            )
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        MapUtils.initPlacesClient()
        fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(context)
        placesClient = Places.createClient(this.requireContext())
    }

    private fun loadMap() {
        val mapFragment = childFragmentManager.findFragmentById(binding.map.id) as SupportMapFragment?
        mapFragment!!.getMapAsync(this)
    }

    override fun onMapReady(googleMap: GoogleMap) {
        map = googleMap
        googleMap.setOnMarkerDragListener(object : OnMarkerDragListener {
            override fun onMarkerDragStart(marker: Marker) {}
            override fun onMarkerDrag(marker: Marker) {}
            override fun onMarkerDragEnd(marker: Marker) {
                val latLng = marker.position
                try {
                    viewModel.selectLocation(latLng)
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        })
        map?.setOnMapLongClickListener {
            viewModel.selectLocation(it)
        }
    }

    protected fun setToCurrentLocation() {
        try {
            checkLocationPermission(binding.root) {
                getCurrentLocation { loc ->
                    viewModel.selectLocation(LatLng(loc.latitude, loc.longitude))
                }
            }
        } catch (e: SecurityException) {
            Log.e("Exception: %s", e.message, e)
        }
    }

    @SuppressLint("MissingPermission")
    private fun getCurrentLocation(callback: (Location) -> Unit) {
        confirmLocationIsEnabled {
            lifecycleScope.launch {
                try {
                    val loc = fusedLocationProviderClient.getCurrentLocation(PRIORITY_BALANCED_POWER_ACCURACY, null).await()
                    if (loc != null) {
                        Log.d("CURLOC", "getLastLocation: got current Location")
                        callback.invoke(loc)
                    } else {
                        Firebase.crashlytics.recordException(Exception("Location was null for some reason"))
                        Log.d("CURLOC", "confirmLocationIsEnabled: Location was null for some reason")
                    }
                } catch (e: ExecutionException) {
                    Firebase.crashlytics.recordException(e)
                    Log.e("CURLOC", e.message, e)
                } catch (e: InterruptedException) {
                    Firebase.crashlytics.recordException(e)
                    Log.e("CURLOC", e.message, e)
                }
//                val result = fusedLocationProviderClient.lastLocation.await()
//                if (result != null) {
//                    Log.d("BLSF", "getLastLocation: selecting location (${result.latitude},${result.longitude})")
//                    viewModel.selectLocation(LatLng(result.latitude, result.longitude))
//                } else {
//
//
//                }
            }
        }
    }

    private val locationEnableResult = registerForActivityResult( ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        Log.d("CURLOC", "locationEnableResult: got result. location enabled? ${activityResult.resultCode == Activity.RESULT_OK}")
        if (activityResult.resultCode == Activity.RESULT_OK) {
            lifecycleScope.launch {
                delay(1000)
                setToCurrentLocation()
            }
        }
    }

    private fun confirmLocationIsEnabled(callback: () -> Unit) {
        val locationRequest = LocationRequest.Builder(
            Priority.PRIORITY_HIGH_ACCURACY, TimeUnit.MINUTES.toMillis(5)
        ).apply {
            setGranularity(Granularity.GRANULARITY_PERMISSION_LEVEL)
            setDurationMillis(TimeUnit.MINUTES.toMillis(5))
            setWaitForAccurateLocation(true)
            setMaxUpdates(1)
        }.build()

        val builder = LocationSettingsRequest.Builder().addLocationRequest(locationRequest)

        val client: SettingsClient = LocationServices.getSettingsClient(requireActivity())
        Firebase.crashlytics.log("Checking if location is enabled")
        Log.d("CURLOC", "confirmLocationIsEnabled: Checking if location is enabled")
        val task = client.checkLocationSettings(builder.build())
        task.addOnSuccessListener {
            // All location settings are satisfied. The client can initialize
            // location requests here.
            Firebase.crashlytics.log("Location is enabled!!")
            Log.d("CURLOC", "confirmLocationIsEnabled: Location is enabled!!")
            callback.invoke()
        }

        task.addOnFailureListener { exception ->
            Firebase.crashlytics.log("Location is not enabled")
            Log.d("CURLOC", "confirmLocationIsEnabled: Location is not enabled")
            if (exception is ResolvableApiException) {
                // Location settings are not satisfied, but this can be fixed
                // by showing the user a dialog.

                Firebase.crashlytics.log("Exception is resolvable")
                Log.d("CURLOC", "confirmLocationIsEnabled: Exception is resolvable")
                try {
                    // Show the dialog by calling startResolutionForResult(),
                    // and check the result in onActivityResult().
                    val intentSenderRequest = IntentSenderRequest
                        .Builder(exception.resolution).build()
                    locationEnableResult.launch(intentSenderRequest)
                } catch (sendEx: IntentSender.SendIntentException) {
                    // Ignore the error.
                    Firebase.crashlytics.recordException(sendEx)
                }
            }
        }

    }
}


