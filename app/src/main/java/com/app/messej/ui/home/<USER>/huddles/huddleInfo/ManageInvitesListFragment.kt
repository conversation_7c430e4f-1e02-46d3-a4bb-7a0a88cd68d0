package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.huddles.HuddleInvitationsResponse
import com.app.messej.databinding.FragmentManageInvitesListBinding
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.button.MaterialButton
import com.kennyc.view.MultiStateView


class ManageInvitesListFragment : Fragment() {

    private lateinit var binding: FragmentManageInvitesListBinding
    private var mAdapter : ManageInvitesListAdapter? = null
    private val viewModel : ManageRequestInviteViewModel by viewModels(ownerProducer = { requireParentFragment() })



    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_manage_invites_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
    }

    private fun initAdapter() {
        mAdapter = ManageInvitesListAdapter(object : ManageInvitesListAdapter.ItemListener{

            override fun onItemClick(item: HuddleInvitationsResponse.HuddleInvitation, position: Int, menuItem: MenuItem) {
                when (menuItem.itemId) {
                    R.id.cancel_huddle_invitation -> {
                        viewModel.cancelInvitation(item.memberId)
                    }
                    else -> {}
                }
            }
        })

        val layoutMan = LinearLayoutManager(context)
        binding.invitesList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image).apply { visibility = View.GONE }
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = resources.getString(R.string.huddle_participants_no_result_found_text)
            findViewById<MaterialButton>(R.id.eds_empty_action).apply {
                visibility = View.GONE
            }
        }
    }

    private fun observe() {
        viewModel.huddleInvitationsList.observe(viewLifecycleOwner){
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

}