package com.app.messej.ui.home.deleteaccount

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class DeleteBottomSheetViewModel(application: Application): AndroidViewModel(application) {

    val countryCode = MutableLiveData<String?>(null)
    val didEnterPhoneNumber = MutableLiveData<Boolean>(false)
    val didEnterEmail = MutableLiveData<Boolean>(false)

    private var deleteRepo: SettingsRepository = SettingsRepository(application)
    // will be false even if input is empty
    private val _phoneNumberValid = MutableLiveData(false)
    private val _emailValid = MutableLiveData(false)
    private val _isMobileNextButtonVisible = MutableLiveData(false)
    private val _isEmailNextButtonVisible = MutableLiveData(false)

    private val _phoneNumber = MutableLiveData<String?>(null)
    val phoneNumber: LiveData<String?> = _phoneNumber

    private val _accountDeleteError = MutableLiveData<String?>(null)
    val accountDeleteError: LiveData<String?> = _accountDeleteError

    private val _isEmailVerified= MutableLiveData(false)
    val isEmailVerified: LiveData<Boolean?> = _isEmailVerified

    private val _isAccountDeleted= MutableLiveData(false)
    val isAccountDeleted: LiveData<Boolean?> = _isAccountDeleted

    private val _unSelectedPage= MutableLiveData(1)
    val unSelectedPage: LiveData<Int?> = _unSelectedPage
   private val authRepo = AuthenticationRepository(getApplication())

    val onAccountDeleted = LiveEvent<Boolean>()

    fun setPhoneNumberValid(valid: Boolean) {
        _phoneNumberValid.postValue(valid)
    }

    fun setEmailValid(valid: Boolean) {
        _emailValid.postValue(valid)
    }

    fun deleteUser() {
        viewModelScope.launch(Dispatchers.IO) {
             authRepo.clearLoginSessionAndData()
            _isAccountDeleted.postValue(true)
        }
    }

    // show error if phone number is invalid after entering 3 characters
    private val _showPhoneInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(didEnterPhoneNumber) { shouldShowPhoneNumberError() }
        med.addSource(_phoneNumberValid) { shouldShowPhoneNumberError() }
        med
    }

    private val _showEmailInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(didEnterEmail) { shouldShowEmailError() }
        med.addSource(_emailValid) { shouldShowEmailError() }
        med
    }

    val showPhoneInvalidError: LiveData<Boolean> = _showPhoneInvalidError
    val showEmailInvalidError: LiveData<Boolean> = _showEmailInvalidError
    val isMobileNextButtonVisible: LiveData<Boolean> = _isMobileNextButtonVisible
    val isEmailNextButtonVisible: LiveData<Boolean> = _isEmailNextButtonVisible

    private fun shouldShowPhoneNumberError() {
        _isMobileNextButtonVisible.postValue(_phoneNumberValid.value == false)
        if (didEnterPhoneNumber.value == false) {
            _showPhoneInvalidError.postValue(false)
            return
        }
        _showPhoneInvalidError.postValue(_phoneNumberValid.value == false)
    }

    private fun shouldShowEmailError() {
        _isEmailNextButtonVisible.postValue(_emailValid.value == false)
        if (didEnterEmail.value == false) {
            _showEmailInvalidError.postValue(false)
            return
        }
        _showEmailInvalidError.postValue(_emailValid.value == false)
    }

    fun deleteAccountPhone(phone: String, countryCode: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when(val result=deleteRepo.deleteAccountPhone(UserInfoUtil.removeLeadingZeroes(phone)?:"", countryCode)){
                 is ResultOf.Success->{
                     onAccountDeleted.postValue(true)
                     deleteUser()
                 }
                is ResultOf.APIError->{

                    _accountDeleteError.postValue(result.error.message)
                }
                is ResultOf.Error->{
                    _accountDeleteError.postValue(result.exception.message)
                }
            }
        }
    }


    fun deleteAccountEmail(email: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when(val result=deleteRepo.deleteAccountEmail(email)){
                is ResultOf.Success->{
                    onAccountDeleted.postValue(true)
                    deleteUser()
                }
                is ResultOf.APIError->{
                    _accountDeleteError.postValue(result.error.message)
                }
                is ResultOf.Error->{
                    _accountDeleteError.postValue(result.exception.message)
                }
            }
        }
    }
}