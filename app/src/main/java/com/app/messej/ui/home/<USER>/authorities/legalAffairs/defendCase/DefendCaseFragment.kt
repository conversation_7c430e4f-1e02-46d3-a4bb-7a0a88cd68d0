package com.app.messej.ui.home.publictab.authorities.legalAffairs.defendCase

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.FragmentDefendCaseBinding
import com.app.messej.databinding.ItemReportProofBinding
import com.app.messej.ui.legal.report.ReportViewModel.ProofMediaUIModel
import com.app.messej.ui.utils.FragmentExtensions.hideKeyboard
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.TextFormatUtils.setAsMandatory
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class DefendCaseFragment : Fragment() {

    private lateinit var binding: FragmentDefendCaseBinding
    private val viewModel: DefendCaseViewModel by viewModels()
    private val args: DefendCaseFragmentArgs by navArgs()
    private var mProofAdapter: BaseQuickAdapter<ProofMediaUIModel, BaseDataBindingHolder<ItemReportProofBinding>>? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_defend_case, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.defend_case_title)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        initAdapter()
        observe()
    }

    private val selectImageOrVideoResult =
        registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
            uri?.let {
                viewModel.addProofFile(uri = uri)
            }
        }

    private fun setup() {
        viewModel.setCaseId(id = args.caseId)
        binding.txtYourIndicement.setAsMandatory()
        setupClickListeners()
    }

    private fun setupClickListeners() {
        binding.apply {
            uploadFile.setOnClickListener {
                hideKeyboard()
                uploadFromGallery()
            }
            btnSubmit.setOnClickListener { viewModel?.defendCase() }
            btnCancel.setOnClickListener {
                showAlertDialog()
            }
        }
    }

    private fun observe() {
        viewModel.isCaseDefendedSuccessfully.observe(viewLifecycleOwner) {
            showToast(message = getString(R.string.defend_case_success))
            findNavController().navigateUp()
        }

        viewModel.isFileUploadFailed.observe(viewLifecycleOwner) {
            if (it) {
                showToast(message = getString(R.string.default_file_upload_failed))
            }
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) { msg ->
            showToast(message = msg)
        }

        viewModel.proofMedia.observe(viewLifecycleOwner){
            mProofAdapter?.apply {
                if (data.size == 0 || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }
    }

    private fun uploadFromGallery() {
        selectImageOrVideoResult.launch(PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageAndVideo))
    }

    private fun showAlertDialog() {
        val alertDialog = MaterialAlertDialogBuilder(requireContext())
            .setMessage(getString(R.string.defend_case_cancel_indicement_confirmation))
            .setPositiveButton(getString(R.string.common_confirm)) { _, _ ->
                findNavController().navigateUp()
            }
            .setNegativeButton(getString(R.string.common_cancel)) { dialog, _ -> dialog.dismiss() }
            .create()
        alertDialog.show()

        val negativeButton = alertDialog.getButton(DialogInterface.BUTTON_NEGATIVE)
        negativeButton.setTextColor(ContextCompat.getColor(requireContext(), R.color.colorError))
    }

    private fun initAdapter() {
        val differ = object : DiffUtil.ItemCallback<ProofMediaUIModel>() {
            override fun areItemsTheSame(oldItem: ProofMediaUIModel, newItem: ProofMediaUIModel): Boolean {
                return oldItem.proof.uuid == newItem.proof.uuid
            }

            override fun areContentsTheSame(oldItem: ProofMediaUIModel, newItem: ProofMediaUIModel): Boolean {
                return oldItem == newItem
            }
        }

        mProofAdapter = object: BaseQuickAdapter<ProofMediaUIModel, BaseDataBindingHolder<ItemReportProofBinding>>(R.layout.item_report_proof, mutableListOf()) {
            override fun convert(holder: BaseDataBindingHolder<ItemReportProofBinding>, item: ProofMediaUIModel) {
                holder.dataBinding?.apply {
                    model = item
                    fileType.setImageResource(
                        when (item.proof.mediaType) {
                            MediaType.IMAGE -> R.drawable.ic_attach_gallery
                            MediaType.VIDEO -> R.drawable.ic_attach_video
                            else -> 0
                        }
                    )
                    deleteButton.setOnClickListener {
                        viewModel.deleteFile(item.proof)
                    }
                }
            }
        }
        binding.proofList.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(false)
            adapter = mProofAdapter
        }
        mProofAdapter?.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = false
            setDiffCallback(differ)
        }
    }

}