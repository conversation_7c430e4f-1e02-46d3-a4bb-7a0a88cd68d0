package com.app.messej.ui.home.publictab.podiums.challenges.create

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.databinding.ItemPodiumChallengeConfourParticipantListBinding
import com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumCreateChallengeViewModel.SelectableSpeakerUIModel
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class PodiumChallengeParticipantsQuickAdapter(private val mListener: ItemListener, data: MutableList<SelectableSpeakerUIModel>) :
    BaseQuickAdapter<SelectableSpeakerUIModel, BaseDataBindingHolder<ItemPodiumChallengeConfourParticipantListBinding>>(R.layout.item_podium_challenge_confour_participant_list, data) {
    var currentUserId: Int? = null

    interface ItemListener {
        fun toggleParticipantSelection(item: SelectableSpeakerUIModel)
    }

    override fun convert(
        holder: BaseDataBindingHolder<ItemPodiumChallengeConfourParticipantListBinding>,
        item: SelectableSpeakerUIModel,
    ) {
        holder.dataBinding?.apply {
            user = item
            currentUserID = currentUserId

            checkbox.setOnClickListener{
                mListener.toggleParticipantSelection(item)
            }
            userLayout.setOnClickListener() {
                mListener.toggleParticipantSelection(item)
            }
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<SelectableSpeakerUIModel>() {
        override fun areItemsTheSame(oldItem: SelectableSpeakerUIModel, newItem: SelectableSpeakerUIModel): Boolean {
            return oldItem.speaker.id == newItem.speaker.id
        }

        override fun areContentsTheSame(
            oldItem: SelectableSpeakerUIModel,
            newItem: SelectableSpeakerUIModel,
        ): Boolean {
            return oldItem.speaker.id == newItem.speaker.id
        }
    }
}