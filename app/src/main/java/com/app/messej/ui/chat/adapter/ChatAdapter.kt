package com.app.messej.ui.chat.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.media3.ui.PlayerView
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.databinding.ItemChatGiftActivityBinding
import com.app.messej.databinding.ItemChatMessageActivityBinding
import com.app.messej.databinding.ItemChatMessageDateHeaderBinding
import com.app.messej.databinding.ItemChatMessageIncomingBinding
import com.app.messej.databinding.ItemChatMessageIncomingDeletedBinding
import com.app.messej.databinding.ItemChatMessageOutgoingBinding
import com.app.messej.databinding.ItemChatMessageOutgoingDeletedBinding
import com.app.messej.databinding.ItemChatMessageUnreadHeaderBinding
import com.app.messej.ui.chat.ChatMessageUIModel


open class ChatAdapter(private val inflater: LayoutInflater, private val userId: Int, var allowReplySwipe: Boolean, private var mListener: ChatClickListener): PagingDataAdapter<ChatMessageUIModel, ChatAdapter.ChatViewHolder>(
    ChatDiff
) {

    interface ChatClickListener {
        fun onMediaDownload(view: View, msg: AbstractChatMessageWithMedia, position: Int)
        fun onMediaUpload(msg: AbstractChatMessageWithMedia, position: Int) {}
        fun onMediaImageTap(view: View, msg: AbstractChatMessageWithMedia, position: Int)

        fun onMediaDocumentTap(view: View, msg: AbstractChatMessageWithMedia, position: Int)
        fun onMediaLocationTap(view: View, msg: AbstractChatMessageWithMedia, position: Int)
        fun onMediaVideoPlay(view: PlayerView, msg: AbstractChatMessageWithMedia, position: Int): Boolean
        fun onViewHolderCleanup(messageId: String)
        fun onMediaAudioPlay(view: View, msg: AbstractChatMessageWithMedia, position: Int)
        fun onMediaAudioSeek(view: View, msg: AbstractChatMessageWithMedia, position: Int, seekPosition: Float)
        fun onMediaAudioToggleSpeed(view: View, msg: AbstractChatMessageWithMedia, position: Int)
        fun onItemClick(item: AbstractChatMessage, position: Int): Boolean
        fun onItemLongClick(item: AbstractChatMessage, position: Int)
        fun onItemReplyClick(item: AbstractChatMessage, position: Int): Boolean { return false }
        fun onItemReply(item: AbstractChatMessage, position: Int) {}
        fun onItemLike(item: AbstractChatMessage, position: Int) {}

        fun onForwardClick(item: AbstractChatMessage, position: Int){}
        fun onGiftClick(item: SentGiftPayload, position: Int){}
    }

    companion object {

        const val ITEM_DATE_HEADER = 1
        const val ITEM_UNREAD_HEADER = 2

        const val ITEM_ACTIVITY = 5

        const val ITEM_INCOMING_TEXT = 10
        const val ITEM_INCOMING_TEXT_REPLY = 11
        const val ITEM_INCOMING_IMAGE = 12
        const val ITEM_INCOMING_AUDIO = 13
        const val ITEM_INCOMING_DELETED = 14
        const val ITEM_INCOMING_VIDEO = 15
        const val ITEM_INCOMING_DOCUMENT = 16
        const val ITEM_INCOMING_LOCATION = 17
        const val ITEM_INCOMING_STICKER = 18

        const val ITEM_OUTGOING_TEXT = 20
        const val ITEM_OUTGOING_TEXT_REPLY = 21
        const val ITEM_OUTGOING_IMAGE = 22
        const val ITEM_OUTGOING_AUDIO = 23
        const val ITEM_OUTGOING_DELETED = 24
        const val ITEM_OUTGOING_VIDEO = 25
        const val ITEM_OUTGOING_DOCUMENT = 26
        const val ITEM_OUTGOING_LOCATION = 27
        const val ITEM_OUTGOING_STICKER = 28

        const val ITEM_GIFT_ACTIVITY = 29

        fun isOfMessageType(type: Int) = type in 10..29

        fun isOfIncomingMessageType(type: Int) = type in 10..19
        fun isOfOutgoingMessageType(type: Int) = type in 20..29

        fun isOfDeletedMessageType(type: Int) = (type== ITEM_INCOMING_DELETED)||(type== ITEM_OUTGOING_DELETED)
        fun isOfMessageTypeButNotDeleted(type: Int) = isOfMessageType(type) && !isOfDeletedMessageType(type)
    }

    override fun getItemViewType(position: Int): Int {
        return when (val msg = peek(position)) {
            is ChatMessageUIModel.ChatMessageModel -> {
                val outgoing: Boolean = msg.message.belongsToUser(userId)
                val isBlockedByAdmin = msg.message is HuddleChatMessage && (msg.message as HuddleChatMessage).senderDetails?.blockedByEitherAdmin==true
                if (msg.message.deleted || msg.message.reported || isBlockedByAdmin) {
                    return if (msg.message.messageType == AbstractChatMessage.MessageType.ACTIVITY) ITEM_ACTIVITY
                    else if(outgoing) ITEM_OUTGOING_DELETED else ITEM_INCOMING_DELETED
                }
                return when (msg.message.messageType) {
                    AbstractChatMessage.MessageType.TEXT -> if (outgoing){
                        if(msg.message.hasReply) ITEM_OUTGOING_TEXT_REPLY else ITEM_OUTGOING_TEXT
                    } else {
                        if(msg.message.hasReply) ITEM_INCOMING_TEXT_REPLY else ITEM_INCOMING_TEXT
                    }
                    AbstractChatMessage.MessageType.MEDIA -> {
                        return when (msg.message.mediaMeta?.mediaType) {
                            MediaType.IMAGE -> if (outgoing) ITEM_OUTGOING_IMAGE else ITEM_INCOMING_IMAGE
                            MediaType.AUDIO -> if (outgoing) ITEM_OUTGOING_AUDIO else ITEM_INCOMING_AUDIO
                            MediaType.VIDEO -> if (outgoing) ITEM_OUTGOING_VIDEO else ITEM_INCOMING_VIDEO
                            null -> if (outgoing) ITEM_OUTGOING_TEXT_REPLY else ITEM_INCOMING_TEXT_REPLY
                            MediaType.DOCUMENT -> if (outgoing) ITEM_OUTGOING_DOCUMENT else ITEM_INCOMING_DOCUMENT
                        }
                    }
                    AbstractChatMessage.MessageType.LOCATION -> if(outgoing) ITEM_OUTGOING_LOCATION else ITEM_INCOMING_LOCATION
                    AbstractChatMessage.MessageType.STICKER -> if(outgoing) ITEM_OUTGOING_STICKER else ITEM_INCOMING_STICKER
                    AbstractChatMessage.MessageType.ACTIVITY -> ITEM_ACTIVITY
                    AbstractChatMessage.MessageType.POLL -> if (outgoing){
                        if(msg.message.hasReply) ITEM_OUTGOING_TEXT_REPLY else ITEM_OUTGOING_TEXT
                    } else {
                        if(msg.message.hasReply) ITEM_INCOMING_TEXT_REPLY else ITEM_INCOMING_TEXT
                    }
                    AbstractChatMessage.MessageType.GIFT -> ITEM_GIFT_ACTIVITY
                }
            }
            is ChatMessageUIModel.DateSeparatorModel -> ITEM_DATE_HEADER
            is ChatMessageUIModel.UnreadSeparatorModel -> ITEM_UNREAD_HEADER
            else -> throw IllegalStateException("Unknown view")
        }
    }

    /**
     * Note: Even though the media and text types use the same viewHolder,
     * they are defined as separate types to promote efficient recycling of the inflated layouts
     * i.e. an image type will only be recycled as an image type, saving on expensive inflate ops
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = when (viewType) {
        ITEM_DATE_HEADER -> ChatDateHeaderViewHolder(ItemChatMessageDateHeaderBinding.inflate(inflater, parent, false))
        ITEM_UNREAD_HEADER -> ChatUnreadHeaderViewHolder(ItemChatMessageUnreadHeaderBinding.inflate(inflater, parent, false))
        ITEM_ACTIVITY -> ChatActivityViewHolder(ItemChatMessageActivityBinding.inflate(inflater, parent, false))

        ITEM_INCOMING_TEXT, ITEM_INCOMING_TEXT_REPLY, ITEM_INCOMING_IMAGE, ITEM_INCOMING_AUDIO, ITEM_INCOMING_VIDEO, ITEM_INCOMING_DOCUMENT, ITEM_INCOMING_LOCATION, ITEM_INCOMING_STICKER -> {
            val binding = ItemChatMessageIncomingBinding.inflate(inflater, parent, false)
            binding.chatBubble.apply {
                val lp = layoutParams as ConstraintLayout.LayoutParams
                lp.width = if (viewType == ITEM_INCOMING_TEXT) ConstraintLayout.LayoutParams.WRAP_CONTENT else ConstraintLayout.LayoutParams.MATCH_CONSTRAINT
                layoutParams = lp
            }
            ChatMessageIncomingViewHolder(binding,userId, allowReplySwipe, mListener)
        }
        ITEM_INCOMING_DELETED -> ChatMessageDeletedIncomingViewHolder(ItemChatMessageIncomingDeletedBinding.inflate(inflater, parent, false),userId, mListener)
        ITEM_OUTGOING_TEXT, ITEM_OUTGOING_TEXT_REPLY, ITEM_OUTGOING_IMAGE, ITEM_OUTGOING_AUDIO, ITEM_OUTGOING_VIDEO, ITEM_OUTGOING_DOCUMENT, ITEM_OUTGOING_LOCATION, ITEM_OUTGOING_STICKER -> {
            val binding = ItemChatMessageOutgoingBinding.inflate(inflater, parent, false)
            binding.chatBubble.apply {
                val lp = layoutParams as ConstraintLayout.LayoutParams
                lp.width = if (viewType == ITEM_OUTGOING_TEXT) ConstraintLayout.LayoutParams.WRAP_CONTENT else ConstraintLayout.LayoutParams.MATCH_CONSTRAINT
                layoutParams = lp
            }
            ChatMessageOutgoingViewHolder(binding, userId, allowReplySwipe, mListener)
        }
        ITEM_OUTGOING_DELETED -> ChatMessageDeletedOutgoingViewHolder(ItemChatMessageOutgoingDeletedBinding.inflate(inflater, parent, false),userId, mListener)
        ITEM_GIFT_ACTIVITY -> ChatGiftActivityViewHolder(ItemChatGiftActivityBinding.inflate(inflater, parent, false),mListener)
        else -> throw IllegalStateException("Unknown view type")
    }

    abstract class ChatViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        abstract fun bind(item: ChatMessageUIModel)
        @CallSuper
        open fun cleanup() {
            Log.d("VPLAY", "cleanup")
        }
    }

    interface ChatHighlightViewProvider {
        fun getHighlightView(): View?
    }

    override fun onBindViewHolder(holder: ChatViewHolder, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onViewDetachedFromWindow(holder: ChatViewHolder) {
        Log.d("VPLAY", "onViewDetachedFromWindow: $holder")
        holder.cleanup()
        super.onViewDetachedFromWindow(holder)
    }


    object ChatDiff : DiffUtil.ItemCallback<ChatMessageUIModel>() {
        override fun areItemsTheSame(oldItem: ChatMessageUIModel, newItem: ChatMessageUIModel): Boolean {
            val isSameDate = oldItem is ChatMessageUIModel.DateSeparatorModel && newItem is ChatMessageUIModel.DateSeparatorModel && oldItem.date == newItem.date
            val isSameMessage = oldItem is ChatMessageUIModel.ChatMessageModel && newItem is ChatMessageUIModel.ChatMessageModel && oldItem.message.messageId == newItem.message.messageId
            val isSameComment = oldItem is ChatMessageUIModel.PostCommentModel && newItem is ChatMessageUIModel.PostCommentModel && oldItem.comment.commentId == newItem.comment.commentId
            return isSameDate || isSameMessage || isSameComment
        }

        override fun areContentsTheSame(oldItem: ChatMessageUIModel, newItem: ChatMessageUIModel): Boolean {
            return oldItem == newItem
        }
    }
}