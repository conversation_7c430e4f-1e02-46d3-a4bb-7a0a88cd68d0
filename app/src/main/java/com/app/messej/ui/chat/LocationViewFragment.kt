package com.app.messej.ui.chat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentLocationViewBinding
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.OnMapReadyCallback
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.BitmapDescriptorFactory
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.MarkerOptions

class LocationViewFragment : Fragment(), OnMapReadyCallback {

    private lateinit var binding: FragmentLocationViewBinding
    private var map: GoogleMap? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_location_view, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        loadMap()
    }

    private fun loadMap() {
        val mapFragment = childFragmentManager.findFragmentById(binding.map.id) as SupportMapFragment?
        mapFragment!!.getMapAsync(this)
    }

    override fun onMapReady(googleMap: GoogleMap) {
        map = googleMap

        try {
//            moveMapToLocation(args.latitude?.toDouble(), args.longitude?.toDouble())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun moveMapToLocation(latitude: Double?, longitude: Double?) {
        map?.apply {
            latitude?.let { lat ->
                longitude?.let { long ->
                    animateCamera(CameraUpdateFactory.newLatLngZoom(LatLng(lat, long), 16f))
                    clear()
                    addMarker(
                        MarkerOptions().position(LatLng(lat, long)).visible(true).draggable(true).flat(true).icon(BitmapDescriptorFactory.fromResource(R.drawable.ic_map_marker))
                    )
                }
            }
        }
    }

}