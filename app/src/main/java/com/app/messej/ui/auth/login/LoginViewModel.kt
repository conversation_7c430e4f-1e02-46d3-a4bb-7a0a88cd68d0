package com.app.messej.ui.auth.login

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.auth.LoginResponse
import com.app.messej.data.model.enums.ProfileCheckpoint
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.ui.utils.LocaleUtil
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class LoginViewModel(application: Application) : AndroidViewModel(application) {

    private val authRepo: AuthenticationRepository = AuthenticationRepository(getApplication())
    private val accountRepo: AccountRepository = AccountRepository(getApplication())

    private val _dataLoading = MutableLiveData<Boolean>(false)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _isMenaRegion= MutableLiveData<Boolean>(false)
    val isMenaRegion: LiveData<Boolean> = _isMenaRegion

    companion object {
        enum class LoginMode { MOBILE_NUMBER, USERNAME }

        private const val PASSWORD_MIN_DIGITS = 6
        private const val USERNAME_MIN_DIGITS = 5

        enum class NextDestination {
            HOME, PROFILE_COMPLETION, BLACKLISTED_USER
        }
    }

    private val _loginMode = MutableLiveData(LoginMode.MOBILE_NUMBER)
    val loginMode: LiveData<LoginMode> = _loginMode

    val countryCode = MutableLiveData<String?>(null)

    val phoneNumber = MutableLiveData<String>()
    val didEnterPhoneNumber = MutableLiveData<Boolean>(false)

    val userName = MutableLiveData<String>()
    val didEnterUserName = MutableLiveData<Boolean>(false)

    val password = MutableLiveData<String>()
    val didEnterPassword = MutableLiveData<Boolean>(false)

    fun setLoginMode(mode: LoginMode) {
        _loginMode.postValue(mode)

        // clear password input
        phoneNumber.postValue("")
        didEnterPhoneNumber.postValue(false)
        userName.postValue("")
        didEnterUserName.postValue(false)
        password.postValue("")
        didEnterPassword.postValue(false)
        _loginError.postValue(null)
    }
    // will be false even if input is empty
    private val _phoneNumberValid = MutableLiveData(false)

    fun setPhoneNumberValid(valid: Boolean) {
        _phoneNumberValid.postValue(valid)
    }

    // show error if phone number is invalid after entering 3 characters
    private val _showPhoneInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(didEnterPhoneNumber) { shouldShowPhoneNumberError() }
        med.addSource(_phoneNumberValid) { shouldShowPhoneNumberError() }
        med.addSource(phoneNumber) { shouldShowPhoneNumberError() }
        med
    }
    val showPhoneInvalidError: LiveData<Boolean> = _showPhoneInvalidError

    private fun shouldShowPhoneNumberError() {
        if (didEnterPhoneNumber.value==false) {
            _showPhoneInvalidError.postValue(false)
            return
        }
        _showPhoneInvalidError.postValue(_phoneNumberValid.value==false)
    }

    private val _showPasswordInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(password) { shouldShowPasswordError() }
        med.addSource(didEnterPassword) { shouldShowPasswordError() }
        med
    }
    val showPasswordInvalidError: LiveData<Boolean> = _showPasswordInvalidError
    enum class usernameOrEmail { USERNAME, EMAIL}


    private fun shouldShowPasswordError() {
        if (didEnterPassword.value==false) {
            _showPasswordInvalidError.postValue(false)
            return
        }
        _showPasswordInvalidError.postValue(!password.value.isNullOrEmpty() && password.value.orEmpty().length < PASSWORD_MIN_DIGITS)
    }

    private val _showUserNameInvalidError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(userName) { shouldShowUserNameError() }
        med.addSource(didEnterUserName) { shouldShowUserNameError() }
        med
    }
    val showUserNameInvalidError: LiveData<Boolean> = _showUserNameInvalidError

    private fun shouldShowUserNameError() {
        if (didEnterUserName.value==false) {
            _showUserNameInvalidError.postValue(false)
            return
        }
        when (checkUserInput()) {
            usernameOrEmail.USERNAME -> _showUserNameInvalidError.postValue(userName.value.orEmpty().length < USERNAME_MIN_DIGITS)
            usernameOrEmail.EMAIL -> _showUserNameInvalidError.postValue(!UserInfoUtil.isEmailValid(userName.value.toString()))
        }
    }

    private fun checkUserInput(): usernameOrEmail {
        val noSpecialPattern = Regex("^[ A-Za-z0-9_.]*\$")

        if (!userName.value.isNullOrEmpty()) {
            return if (noSpecialPattern.containsMatchIn(userName.value!!)) {
                usernameOrEmail.USERNAME
            } else usernameOrEmail.EMAIL
        }
        return usernameOrEmail.EMAIL
    }

    private val _loginValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(_loginMode) { validateLogin() }
        med.addSource(_phoneNumberValid) { validateLogin() }
        med.addSource(userName) { validateLogin() }
        med.addSource(password) { validateLogin() }
        med
    }
    val loginValid: LiveData<Boolean> = _loginValid

    private fun validateLogin() {
        var valid = password.value.orEmpty().length >= PASSWORD_MIN_DIGITS
        when(_loginMode.value) {
            LoginMode.MOBILE_NUMBER -> {
                valid = valid && _phoneNumberValid.value==true
            }
            LoginMode.USERNAME -> {
                valid = valid && !userName.value.isNullOrEmpty()
            }
            else -> {}
        }
        _loginValid.postValue(valid)
    }

    private val _loginError = MutableLiveData<String?>(null)
    val loginError: LiveData<String?> = _loginError

    fun clearLoginError() {
        _loginError.postValue(null)
    }

    val onLoginComplete = LiveEvent<NextDestination>()

    fun loginUser() {
        _dataLoading.postValue(true)
        _loginError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            val pass = password.value?: return@launch

            var result: ResultOf<LoginResponse>? = null
            when(_loginMode.value) {
                LoginMode.MOBILE_NUMBER -> {
                    val phone = UserInfoUtil.removeLeadingZeroes(phoneNumber.value!!)
                    val code = countryCode.value ?: return@launch
                    result = authRepo.signInWithMobileNumber(UserInfoUtil.removeLeadingZeroes(phone)!!, code, pass)
                }
                LoginMode.USERNAME -> {
                    val uname = userName.value ?: return@launch
                    result = authRepo.signInWithUsername(uname, pass)
                }
                else -> {}
            }

            when (result) {
                is ResultOf.Success -> {
                    val user = result.value.user
                    if (user.enforcementStatus?.blacklisted == true) {
                        onLoginComplete.postValue(NextDestination.BLACKLISTED_USER)
                    }
                    else if (user.profileCheckpoint==ProfileCheckpoint.PROFILE_CHECKPOINT_NONE) {
                        onLoginComplete.postValue(NextDestination.HOME)
                    }
                    else {
                        onLoginComplete.postValue(NextDestination.PROFILE_COMPLETION)
                    }
                }
                is ResultOf.APIError -> {
                    _loginError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
                else -> {}
            }
            _dataLoading.postValue(false)
        }
    }

    fun signInWithGoogle(cred: GoogleIdTokenCredential) {
        _dataLoading.postValue(true)
        _loginError.postValue(null)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = authRepo.signInWithGoogle(cred)) {
                is ResultOf.Success -> {
                    val user = result.value.user
                    if (user.profileCheckpoint==ProfileCheckpoint.PROFILE_CHECKPOINT_NONE) {
                        onLoginComplete.postValue(NextDestination.HOME)
                    } else {
                        onLoginComplete.postValue(NextDestination.PROFILE_COMPLETION)
                    }
                }
                is ResultOf.APIError -> {
                    _loginError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
                else -> {}
            }
            _dataLoading.postValue(false)
        }
    }

    fun checkInMENARegion(){
        viewModelScope.launch(Dispatchers.IO) {
            val countryCode=accountRepo.getCountryCode()?.countryCode
            if(countryCode!!.isNotEmpty()){
                if(LocaleUtil.checkInMenaRegion(countryCode)){
                    _isMenaRegion.postValue(true)
                }else{
                    _isMenaRegion.postValue(false)
                }
            }
        }

    }

    fun setMenaRegion(isMenaRegion: Boolean) {
       _isMenaRegion.postValue(isMenaRegion)
    }
}