package com.app.messej.ui.chat.delegates

import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.PopupMenu
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.LifecycleOwner
import com.app.messej.R
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.enums.GroupChatStatus
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.UserRole
import com.app.messej.databinding.ItemChatMessageMediaAudioBinding
import com.app.messej.databinding.ItemHuddlePinnedPostMessageBinding
import com.app.messej.databinding.LayoutHuddleAdminInviteCollapsedBinding
import com.app.messej.databinding.LayoutHuddleAdminInviteExpandedBinding
import com.app.messej.databinding.LayoutHuddleBlockedBinding
import com.app.messej.databinding.LayoutHuddleInvitedBinding
import com.app.messej.databinding.LayoutHuddleJoinRequestedBinding
import com.app.messej.databinding.LayoutHuddleNotJoinedBinding
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.chat.GroupChatViewModel
import com.app.messej.ui.chat.adapter.ChatMessageViewHolder
import com.app.messej.ui.home.publictab.huddles.chat.HuddleChatViewModel
import com.app.messej.ui.home.publictab.huddles.chat.HuddleMessageViewHolder
import com.app.messej.ui.home.publictab.huddles.chat.HuddleMessageViewHolder.Companion.setPinnedChatBubbleColor
import com.google.android.material.slider.Slider

interface GroupChatActionsDelegate {
    fun initJoinLayout(holder: ViewGroup, layoutInflater: LayoutInflater, owner: LifecycleOwner, viewModel: GroupChatViewModel, state: GroupChatStatus?, type: HuddleType, onJoinClick: () -> Unit, onAcceptInviteClick: () -> Unit)
    fun initAdminInvite(holder: ViewGroup,
                        layoutInflater: LayoutInflater,
                        owner: LifecycleOwner,
                        viewModel: GroupChatViewModel,
                        status: Pair<AbstractHuddle.HuddleAdminStatus?,AbstractHuddle.HuddleUserStatus?>)

    fun initHuddlePinnedPost(holder: ViewGroup,
                             layoutInflater: LayoutInflater,
                             owner: LifecycleOwner,
                             viewModel: HuddleChatViewModel
    )

    object GroupChatActionsDelegateImpl: GroupChatActionsDelegate {

        override fun initJoinLayout(holder: ViewGroup, layoutInflater: LayoutInflater, owner: LifecycleOwner, viewModel: GroupChatViewModel, state: GroupChatStatus?, type: HuddleType, onJoinClick: () -> Unit, onAcceptInviteClick: () -> Unit) {
            holder.apply {
                removeAllViews()
                when (state) {
                    GroupChatStatus.OPEN_TO_JOIN -> {
                        val binding = DataBindingUtil.inflate<LayoutHuddleNotJoinedBinding>(layoutInflater, R.layout.layout_huddle_not_joined, this, false)
                        binding.prompt.setText(if(type == HuddleType.PUBLIC) R.string.chat_join_open_prompt_public else R.string.chat_join_open_prompt_private)
                        binding.joinButton.setText(R.string.chat_join_open_button)
                        binding.joinButton.setOnClickListener {
//                            viewModel.instantJoinHuddle()
                            onJoinClick()
                        }
                        binding.loading = viewModel.joinActionLoading
                        binding.lifecycleOwner = owner
                        addView(binding.root)
                    }
                    GroupChatStatus.REQUEST_TO_JOIN -> {
                        val binding = DataBindingUtil.inflate<LayoutHuddleNotJoinedBinding>(layoutInflater, R.layout.layout_huddle_not_joined, this, false)
                        binding.prompt.setText(if(type == HuddleType.PUBLIC) R.string.chat_join_request_prompt_public else R.string.chat_join_request_prompt_private)
                        binding.joinButton.setText(R.string.chat_join_request_button)
                        binding.joinButton.setOnClickListener { viewModel.joinHuddle() }
                        binding.loading = viewModel.joinActionLoading
                        binding.lifecycleOwner = owner
                        addView(binding.root)
                    }
                    GroupChatStatus.JOIN_REQUESTED -> {
                        val binding = DataBindingUtil.inflate<LayoutHuddleJoinRequestedBinding>(layoutInflater, R.layout.layout_huddle_join_requested, this, false)
                        binding.cancelButton.setOnClickListener { viewModel.cancelJoinRequest() }
                        binding.loading = viewModel.joinActionLoading
                        binding.lifecycleOwner = owner
                        addView(binding.root)
                    }
                    GroupChatStatus.INVITED -> {
                        val binding = DataBindingUtil.inflate<LayoutHuddleInvitedBinding>(layoutInflater, R.layout.layout_huddle_invited, this, false)
                        binding.prompt.setText(if(type == HuddleType.PUBLIC) R.string.chat_join_invited_prompt_public else R.string.chat_join_invited_prompt_private)
                        binding.acceptButton.setOnClickListener {
                            onAcceptInviteClick()
//                            viewModel.acceptInvite()
                        }
                        binding.declineButton.setOnClickListener { viewModel.declineInvite() }
                        binding.blockButton.setOnClickListener { viewModel.blockInvite() }
                        binding.loading = viewModel.huddleActionLoading
                        binding.lifecycleOwner = owner
                        addView(binding.root)
                    }
                    GroupChatStatus.BLOCKED -> {
                        val binding = DataBindingUtil.inflate<LayoutHuddleBlockedBinding>(layoutInflater, R.layout.layout_huddle_blocked, this, false)
                        binding.prompt.setText(if(type == HuddleType.PUBLIC) R.string.chat_join_temporary_blocked else R.string.chat_join_blocked_private)
                        addView(binding.root)
                    }
                    GroupChatStatus.ADMIN_BLOCKED -> {
                        val binding = DataBindingUtil.inflate<LayoutHuddleBlockedBinding>(layoutInflater, R.layout.layout_huddle_blocked, this, false)
                        binding.prompt.setText(if(type == HuddleType.PUBLIC) R.string.chat_join_blocked_admin_public else R.string.chat_join_blocked_admin_private)
                        addView(binding.root)
                    }
                    GroupChatStatus.RESTRICTED -> {
                        val binding = DataBindingUtil.inflate<LayoutHuddleBlockedBinding>(layoutInflater, R.layout.layout_huddle_blocked, this, false)
                        binding.prompt.setText(if(type == HuddleType.PUBLIC) R.string.chat_join_temporary_blocked else R.string.chat_join_blocked_private)
                        addView(binding.root)
                    }
                    GroupChatStatus.ACTIVE -> {}
                    GroupChatStatus.UNKNOWN -> {}
                    else -> {}
                }
            }
        }

        override fun initAdminInvite(holder: ViewGroup,
                                     layoutInflater: LayoutInflater,
                                     owner: LifecycleOwner,
                                     viewModel: GroupChatViewModel,
                                     status: Pair<AbstractHuddle.HuddleAdminStatus?,AbstractHuddle.HuddleUserStatus?>) {
            holder.apply {
                removeAllViews()
                if (status.second == AbstractHuddle.HuddleUserStatus.BLOCKED_BY_ADMIN) return
                when (status.first) {
                    AbstractHuddle.HuddleAdminStatus.INVITED -> {
                        val binding = DataBindingUtil.inflate<LayoutHuddleAdminInviteCollapsedBinding>(layoutInflater, R.layout.layout_huddle_admin_invite_collapsed, this, false)
                        binding.root.setOnClickListener {
                            removeAllViews()
                            val expanded = DataBindingUtil.inflate<LayoutHuddleAdminInviteExpandedBinding>(layoutInflater, R.layout.layout_huddle_admin_invite_expanded, this, false)
                            expanded.acceptButton.setOnClickListener { viewModel.acceptAdminInvite() }
                            expanded.declineButton.setOnClickListener { viewModel.declineAdminInvite() }
                            expanded.ignoreButton.setOnClickListener { viewModel.ignoreAdminInvite() }
                            expanded.loading = viewModel.huddleActionLoading
                            expanded.lifecycleOwner = owner
                            addView(expanded.root)
                        }
                        addView(binding.root)
                    }
                    else -> {}
                }
            }
        }

        // TODO this needs to somehow share logic with UI. Too much duplication
        override fun initHuddlePinnedPost(holder: ViewGroup, layoutInflater: LayoutInflater, owner: LifecycleOwner, viewModel: HuddleChatViewModel) {
            holder.apply {
                removeAllViews()
            }
            if (viewModel.huddleStatus.value == GroupChatStatus.ADMIN_BLOCKED) return
            val chatMessageData = viewModel.pinnedPost.value ?: return
            val msg = (chatMessageData as ChatMessageUIModel.ChatMessageModel).message
            val binding = DataBindingUtil.inflate<ItemHuddlePinnedPostMessageBinding>(layoutInflater, R.layout.item_huddle_pinned_post_message, holder, false)
            val cm = chatMessageData

            binding.apply {
                val isSelfMessage = (msg.sender == viewModel.user.id)
                val hcm = msg as HuddleChatMessage
                message = hcm
                senderName = if (isSelfMessage) root.context.getString(R.string.common_you) else (cm.chat as HuddleChatMessageWithMedia).senderNickNameOrName
                isManager = viewModel.huddle.value?.role == UserRole.MANAGER
                val bubbleColor = setPinnedChatBubbleColor(binding.chatBubble, msg.senderDetails?.premium == true, isSelfMessage)

                chatActions.setOnClickListener {
                    val popup = PopupMenu(binding.root.context, binding.chatActions)
                    popup.menuInflater.inflate(R.menu.menu_chat_huddle_pinned_post_actions, popup.menu)
                    popup.setOnMenuItemClickListener { menuItem ->
                        when (menuItem.itemId) {
                            R.id.action_unpin_message -> {
                                viewModel.pinMessage(msg, false)
                            }

                            else -> return@setOnMenuItemClickListener false
                        }
                        return@setOnMenuItemClickListener true
                    }
                    popup.show()
                }
                chatBubble.setOnClickListener {
                    viewModel.onPinnedPostClick(msg)
                }
                chatFlag.setImageResource(chatMessageData.countryFlag?:0)

                chatMessage.setText(HuddleMessageViewHolder.formatAndHighlight(root.context,hcm,viewModel.user.id)?:"", TextView.BufferType.SPANNABLE)
                chatMessage.movementMethod = LinkMovementMethod.getInstance()

                msg.mediaMeta?.let { meta ->
                    when (meta.mediaType) {
                        MediaType.IMAGE, MediaType.VIDEO -> {
                            val ratio = meta.imageWidth / meta.imageHeight
                            (binding.imageVideoThumbnail.layoutParams as ConstraintLayout.LayoutParams).dimensionRatio =
                                ratio.coerceIn(ChatMessageViewHolder.IMAGE_ASPECT_MIN, ChatMessageViewHolder.IMAGE_ASPECT_MAX).toString()
                        }

                        MediaType.AUDIO -> {
                            val ab = ItemChatMessageMediaAudioBinding.inflate(LayoutInflater.from(binding.audioHolder.context), binding.audioHolder, false)
                            binding.audioHolder.addView(ab.root)
                            ab.let {
                                var mediaDownloaded = false
                                chatMessageData.chat.offlineMedia?.let {
                                    mediaDownloaded = true
                                }
                                ab.apply {
                                    downloading = !mediaDownloaded && chatMessageData.isTransferringMedia
                                    downloaded = mediaDownloaded
                                    this.info = chatMessageData.playerInfo
                                    this.meta = chatMessageData.message.mediaMeta

//                                    val highlight = ColorStateList.valueOf(ContextCompat.getColor(root.context, ChatMessageViewHolder.getChatHighlightColor(bubbleColor)))
//                                    mediaSlider.trackActiveTintList = highlight

                                    downloadButton.setOnClickListener { viewModel.downloadMedia(chatMessageData.chat, MediaPlayerInfo.LIST_POS_PINNED) }
                                    audioPlayButton.setOnClickListener {
                                        if (!mediaDownloaded) return@setOnClickListener
                                        viewModel.playMedia(chatMessageData.chat, MediaPlayerInfo.LIST_POS_PINNED)
                                    }

//                                    audioSpeedButton.backgroundTintList = highlight
                                    audioSpeedButton.setOnClickListener {
                                        chatMessageData.playerInfo?: return@setOnClickListener
                                        viewModel.cycleMediaSpeed(chatMessageData.chat)
                                    }

                                    mediaSlider.clearOnSliderTouchListeners()
                                    if (mediaDownloaded) {
                                        mediaSlider.addOnSliderTouchListener(object : Slider.OnSliderTouchListener {
                                            override fun onStartTrackingTouch(slider: Slider) {}
                                            override fun onStopTrackingTouch(slider: Slider) {
                                                viewModel.seekMedia(chatMessageData.chat, MediaPlayerInfo.LIST_POS_PINNED, slider.value)
                                            }
                                        })
                                    }
                                }
                            }
                        }

                        MediaType.DOCUMENT -> {}
                    }
                }
            }
            holder.addView(binding.root)

        }

    }
}