package com.app.messej.ui.home.publictab.myETribe.contactETribe

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentContactETribeBinding
import com.app.messej.ui.utils.FragmentExtensions.showToast

class ContactETribeFragment : Fragment() {

    private lateinit var binding: FragmentContactETribeBinding
    private val viewModel: ContactETribeViewModel by viewModels()
    private val args: ContactETribeFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_contact_e_tribe, container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.e_tribe_contact_title)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        binding.composeViewContactETribe.setContent {
            ContactETribeScreen(
                viewModel = viewModel,
                args = args,
                onCancelButtonClick = {
                    findNavController().navigateUp()
                }
            )
        }
    }

    private fun observe() {
        viewModel.errorMessage.observe(viewLifecycleOwner) { msg ->
            msg?.let { showToast(message = it) }
        }

        viewModel.isAPISuccess.observe(viewLifecycleOwner) {
            showToast(message = R.string.e_tribe_contact_success_message)
            if (it) findNavController().navigateUp()
        }
    }
}