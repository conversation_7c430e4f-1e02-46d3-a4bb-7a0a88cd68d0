package com.app.messej.ui.auth.register

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.model.enums.ProfileCheckpoint
import com.app.messej.databinding.FragmentRegisterMobileNumberBinding
import com.app.messej.ui.auth.AuthOTPFragment
import com.app.messej.ui.common.PolicyDocumentFragment.Companion.REGISTER_DOCUMENT_REQUEST_KEY
import com.app.messej.ui.common.PolicyDocumentFragment.Companion.REGISTER_DOCUMENT_RESULT_KEY
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class RegisterMobileNumberFragment : Fragment() {

    private lateinit var binding: FragmentRegisterMobileNumberBinding

    private val viewModel: RegisterMobileViewModel by activityViewModels()

    override fun onCreateView(
            inflater: LayoutInflater, container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding =
                DataBindingUtil.inflate(inflater, R.layout.fragment_register_mobile_number, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        viewModel.loggedInUser?.let { user ->
            when (user.profileCheckpoint) {
                ProfileCheckpoint.PROFILE_CHECKPOINT_NONE -> {}
                ProfileCheckpoint.PROFILE_CHECKPOINT_PASSWORD_SETUP -> findNavController().navigateSafe(
                    RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToCreatePasswordFragment(
                        user.countryCode?:"",
                        user.phone?:"",user.email?:"",OTPRequestMode.REGISTER_MOBILE)
                )
                ProfileCheckpoint.PROFILE_CHECKPOINT_PROFILE_SETUP -> findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToCreateProfileFragment())
                ProfileCheckpoint.PROFILE_CHECKPOINT_USERNAME_SETUP -> findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToCreateUsernameFragment())
//                ProfileCheckpoint.PROFILE_CHECKPOINT_LOCATION_SETUP -> findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToRegisterLocationFragment(false))
                ProfileCheckpoint.PROFILE_CHECKPOINT_SUPERSTAR_SETUP -> findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToRegisterSuperstarFragment())
                ProfileCheckpoint.PROFILE_CHECKPOINT_LOCK_SCREEN -> {}
                null -> {}
            }
        }
        binding.backToLoginButton.setOnClickListener {
            findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionGlobalNavGraphLogin())
        }

        binding.textInputMobileNumberReg.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus) {
                    viewModel.didEnterPhoneNumber.postValue(true)
                }
            }
            addTextChangedListener { viewModel.clearAvailabilityError() }
        }

        binding.tncButton.setOnClickListener {
            findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionGlobalPolicyFragment(DocumentType.TERMS_OF_USE,true))
        }

        binding.registrationCountryPicker.apply {
            setOnCountryChangeListener {
                viewModel.countryCode.postValue(selectedCountryCode)
            }
            registerCarrierNumberEditText(binding.textInputMobileNumberReg.editText)
            setPhoneNumberValidityChangeListener { valid ->
                viewModel.setPhoneNumberValid(valid)
            }
            viewModel.countryCode.value.let {
                if (it.isNullOrEmpty()) {
                    viewModel.countryCode.postValue(selectedCountryCode)
                } else {
                    setCountryForPhoneCode(it.replace("+", "").toInt())
                }
            }
        }

        binding.registrationNextButton.setOnClickListener {
            viewModel.checkMobileNumberAvailability()
        }

        binding.root.apply {
            setOnTouchListener { v, _ ->
                viewModel.didEnterPhoneNumber.postValue(true)
                v.performClick()
            }
        }
    }

    private fun updateNextButton() {
        binding.registrationNextButton.isEnabled =
                (viewModel.mobileAvailabilityLoading.value == false) && (viewModel.mobileNumberStageValid.value == true)
    }

    private fun observe() {
        viewModel.mobileAvailabilityLoading.observe(viewLifecycleOwner) {
            updateNextButton()
        }
        viewModel.mobileNumberStageValid.observe(viewLifecycleOwner) {
            updateNextButton()
        }
        viewModel.showPhoneInvalidError.observe(viewLifecycleOwner) {
            updateMobileNumberFieldError()
        }

        viewModel.mobileAvailabilityError.observe(viewLifecycleOwner) {
            updateMobileNumberFieldError()
        }

        viewModel.onCheckMobileAvailabilityComplete.observe(viewLifecycleOwner) {
            val action = RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToRegisterMobileConfirmBottomSheetFragment(viewModel.countryCode.value!!,viewModel.phoneNumber.value!!,"")
            findNavController().navigateSafe(action)
        }

        //To listen tnc accept click from RegisterTermsFragment
        setFragmentResultListener(REGISTER_DOCUMENT_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(REGISTER_DOCUMENT_RESULT_KEY)
            viewModel.tncAccepted.postValue(result)
        }

        setFragmentResultListener(RegisterMobileConfirmFragment.MOBILE_CONFIRM_RESULT_CODE) { _, bundle ->
            val result = bundle.getBoolean(RegisterMobileConfirmFragment.MOBILE_CONFIRM_RESULT_KEY)
            if (result) {
                val action = RegisterMobileNumberFragmentDirections.actionGlobalAuthOTPFragment(OTPRequestMode.REGISTER_MOBILE, viewModel.countryCode.value, viewModel.phoneNumber.value, "")
                findNavController().navigateSafe(action)
            }
        }

        setFragmentResultListener(AuthOTPFragment.OTP_REQUEST_KEY) { _, bundle ->
            val result = bundle.getString(AuthOTPFragment.OTP_RESULT_KEY)
            Log.d("NAVC", "observe: navigating to password")
            if (result == AuthOTPFragment.OTP_RESULT_SUCCESS) {
//                no need to navigate. onstart check will handle it anyway
//                findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToCreatePasswordFragment(viewModel.countryCode.value!!,viewModel.phoneNumber.value!!))
            }
        }

    }

    private fun updateMobileNumberFieldError() {
        binding.textInputMobileNumberReg.error = if(!viewModel.mobileAvailabilityError.value.isNullOrEmpty()) {
            binding.textInputMobileNumberReg.isErrorEnabled = true
            viewModel.mobileAvailabilityError.value
        } else if(viewModel.showPhoneInvalidError.value==true){
            binding.textInputMobileNumberReg.isErrorEnabled = true
            resources.getString(R.string.register_error_mobile_number)
        } else {
            binding.textInputMobileNumberReg.isErrorEnabled = false
            null
        }
    }

//    private val phoneNumberHintIntentResultLauncher =
//        registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
//            try {
//                val phoneNumber = Identity.getSignInClient(requireActivity()).getPhoneNumberFromIntent(result.data)
//                binding.registrationCountryPicker.fullNumber = phoneNumber
//            } catch(e: Exception) {
//                Log.e(Constants.FLASHAT_TAG, "Phone Number Hint failed")
//            }
//        }
//
//    private fun getPhoneNumberHint() {
//        if(viewModel.didTryPhoneNumberHint) return
//        viewModel.didTryPhoneNumberHint = true
//        val request: GetPhoneNumberHintIntentRequest = GetPhoneNumberHintIntentRequest.builder().build()
//
//        Identity.getSignInClient(requireActivity())
//            .getPhoneNumberHintIntent(request)
//            .addOnSuccessListener { result ->
//                try {
//                    phoneNumberHintIntentResultLauncher.launch(
//                        IntentSenderRequest.Builder(
//                            result.intentSender
//                        ).build()
//                    )
//                } catch(e: Exception) {
//                    Log.e(Constants.FLASHAT_TAG, "Launching the PendingIntent failed")
//                }
//            }
//            .addOnFailureListener {
//                Log.e(Constants.FLASHAT_TAG, "Phone Number Hint failed")
//            }
//    }

}
