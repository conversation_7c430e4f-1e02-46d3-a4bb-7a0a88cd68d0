package com.app.messej.ui.home.publictab.huddles.chat

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.filter
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.chat.BaseChatDisplayViewModel
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.utils.CountryListUtil
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

open class HuddleMessagesSearchViewModel(application: Application) : BaseChatDisplayViewModel(application) {


    protected val huddleRepo = HuddlesRepository(application)

    private val _huddleId = MutableLiveData<Int>()
    val huddleId: LiveData<Int> = _huddleId

    var searchKeyword = MutableLiveData<String>(null)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }
    private val _countryList = MutableLiveData<Map<String,Int>>()
    init {
        viewModelScope.launch {
            searchKeyword.asFlow().debounce(500L).collect {
                debouncedSearchText.postValue(it.orEmpty())
            }
        }

        _countryList.postValue(CountryListUtil.getCustomCountryMap())
    }
    override val _chatList = debouncedSearchText.switchMap { keyword ->
        _huddleId.value ?: return@switchMap null

        val sourceLiveData = if (!keyword.isNullOrBlank()) {
            huddleRepo.getHuddlesInnerSearchPager(huddleId.value, keyword)
        } else {
            huddleRepo.getHuddleChatsPager(huddleId.value!!, HuddleType.PUBLIC, false)
        }.liveData

        sourceLiveData.map { pagingData: PagingData<HuddleChatMessageWithMedia> ->
            val flags = _countryList.value
            var pgdata: PagingData<ChatMessageUIModel> = pagingData.map { msg ->
                ChatMessageUIModel.ChatMessageModel(msg).apply {
                    if (flags != null) {
                        msg.message.senderDetails?.countryCode?.let { cc ->
                            countryFlag = flags[cc]
                        }
                    }
                }
            }
            pgdata = pgdata.filter {
                val msg = if (it is ChatMessageUIModel.ChatMessageModel) it.message as HuddleChatMessage else null
                msg ?: return@filter false
                return@filter !msg.deleted && !msg.isActivity && msg.senderDetails?.deletedAccount != true
            }
            pgdata
        }.cachedIn(viewModelScope)
    }


    override fun shouldHighlightMessageId(id: String): Boolean {
        //highlight all messages
        return true
    }


    override val showChats: LiveData<Boolean>
        get() = MutableLiveData(true)

    override fun getSenderForReply(msg: AbstractChatMessage) = null
    fun setArgs(huddleId: Int) {
        _huddleId.value = huddleId
    }
    fun resetSearch() {
        searchKeyword.postValue("")
    }


    fun onStreamingVideo(message: AbstractChatMessageWithMedia, uri: String, pos: Int) {
        stopMediaPlayback()
        val info = MediaPlayerInfo.from(message.message, uri, pos).apply {
            state = MediaPlayerInfo.MediaState.PLAYING
        }
        _nowPlayingMedia = info
        nowPlayingMedia.postValue(info)
        pendingItemChange = pos
    }

    suspend fun getVideoStreamingURL(msg: AbstractChatMessageWithMedia, pos: Int): String? {
        msg.message.mediaMeta?.let { meta ->
            if (meta.mediaType == MediaType.VIDEO) {
                val result = chatRepo.getMediaDownloadURL(msg.message)
                if (result is ResultOf.Success) {
                    return result.value.signedUrl
                }
            }
        }
        return null
    }


}