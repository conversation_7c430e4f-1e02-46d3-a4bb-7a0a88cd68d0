package com.app.messej.ui.home.publictab.flash.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.navGraphViewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.FlashSearchTab
import com.app.messej.databinding.FragmentFlashSearchBinding
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.google.android.material.button.MaterialButton


class FlashSearchFragment : Fragment() {

    private lateinit var binding: FragmentFlashSearchBinding

    private val viewModel: FlashSearchViewModel by navGraphViewModels(R.id.nav_flash_search)

    private lateinit var mFlashSearchPagerAdapter: FragmentStateAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_search, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(this)
                setNavigationIcon(R.drawable.ic_close)
                if(!viewModel.user.premiumUser)
                    setNavigationIconTint(getColor(R.color.textColorAlwaysLightSecondary))
            }
        }
    }
    private fun setup() {
        mFlashSearchPagerAdapter = object: FragmentStateAdapter(this) {
            override fun getItemCount(): Int = FlashSearchTab.entries.size

            override fun createFragment(position: Int): Fragment {
                return when(FlashSearchTab.entries[position]) {
                    FlashSearchTab.TAB_FOR_YOU -> {
                        FlashSearchForYouFragment()
                    }
                    FlashSearchTab.TAB_ACCOUNTS -> {
                        FlashSearchAccountsFragment()
                    }
                }
            }
        }

        binding.flashSearchPager.apply {
            isUserInputEnabled = false
            adapter = mFlashSearchPagerAdapter
        }

        viewModel.currentTab.value?.let {
            binding.flashSearchPager.setCurrentItem(it.ordinal, false)
        }

        binding.btnForYou.setOnClickListener {
            viewModel.setCurrentTab(FlashSearchTab.TAB_FOR_YOU)
            (it as MaterialButton).isChecked = true
        }

        binding.btnAccounts.setOnClickListener {
            viewModel.setCurrentTab(FlashSearchTab.TAB_ACCOUNTS)
            (it as MaterialButton).isChecked = true
        }

        binding.customActionBar.apply {
            keyword = viewModel.searchKeyword
            showKeyboard(searchBox)
        }
    }

    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            if (binding.flashSearchPager.currentItem==it.ordinal) return@observe
            binding.flashSearchPager.setCurrentItem(it.ordinal,false)
        }
    }
}