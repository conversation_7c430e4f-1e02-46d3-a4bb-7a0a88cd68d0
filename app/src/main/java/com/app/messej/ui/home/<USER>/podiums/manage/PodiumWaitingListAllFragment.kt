package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.databinding.FragmentPodiumWaitingListAllBinding
import com.app.messej.databinding.LayoutActionModeSearchBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PodiumWaitingListAllFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentPodiumWaitingListAllBinding

    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

//    private var mWaitingListAdapter: BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumWaitingSpeakerAllBinding>>? = null

    private var mWaitingListAllAdapter: PodiumWaitingListAllAdapter? = null

    private var popup : PopupMenu? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_waiting_list_all, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.podium_waiting_list_title)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    private fun setup() {
        iniAdapter()
    }

    private fun observe() {
        viewModel.allWaiters.observe(viewLifecycleOwner) {
            it?.let {
                mWaitingListAllAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
            }
        }

        viewModel.hasSpaceForNewSpeaker.observe(viewLifecycleOwner) {
            Log.d("PWLAF", "observe: hasSpaceForNewSpeaker $it")
            popup?.let { pu->
                pu.menu.findItem(R.id.action_allow_to_speak).isEnabled = it
            }
        }

        viewModel.onUserAllowedToSpeak.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            mWaitingListAllAdapter?.refresh()
        }

        viewModel.onUserBlockToggled.observe(viewLifecycleOwner) {
            mWaitingListAllAdapter?.refresh()
        }
    }


    private fun iniAdapter() {

        mWaitingListAllAdapter = PodiumWaitingListAllAdapter(object : PodiumWaitingListAllAdapter.PodiumActionListener {
            override fun onWaitingMenuClicked(view: View, speaker: PodiumSpeaker) {
                showMoreMenu(view, speaker)
            }

        })

        binding.waitingListUsers.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mWaitingListAllAdapter
        }
    }

    private fun showMoreMenu(view: View, speaker: PodiumSpeaker) {
        val isSelf = speaker.id == viewModel.user.id
        popup = PopupMenu(view.context, view)
        popup?.apply {
            menuInflater.inflate(R.menu.menu_podium_waiting_list_speaker, menu)
            menu.apply {
                findItem(R.id.action_info).isVisible = !isSelf
                findItem(R.id.action_allow_to_speak).isVisible = viewModel.iAmElevated.value == true
                findItem(R.id.action_allow_to_speak).isEnabled = viewModel.hasSpaceForNewSpeaker.value == true
//            findItem(R.id.action_appoint_admin).isVisible = viewModel.iAmElevated.value == true && speaker.premiumUser
                findItem(R.id.action_appoint_admin).isVisible = false
                findItem(R.id.action_block).isVisible = viewModel.iAmElevated.value == true && !isSelf
                findItem(R.id.action_cancel_request).isVisible = isSelf
            }
            setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.action_info -> {
                        val action = PodiumWaitListActionsBottomSheetFragmentDirections.actionGlobalPublicUserProfileFragment(
                            speaker.id, false
                        )
                        findNavController().navigateSafe(action)
                    }
                    R.id.action_allow_to_speak -> {
                        confirmAction(
                            title = R.string.podium_action_allow_to_speak_confirm_title, message = R.string.podium_action_allow_to_speak_confirm_message
                        ) {
                            viewModel.allowToSpeak(speaker)
                        }
                    }
//                R.id.action_appoint_admin -> {
//
//                }
                    R.id.action_block -> {
                        confirmAction(
                            title = null, message = R.string.podium_action_block_user_confirm_message
                        ) {
                            viewModel.toggleUserBlock(speaker.id,BlockUnblockAction.BLOCK)
                        }
                    }
                    R.id.action_cancel_request -> {
                        confirmAction(
                            title = null, message = R.string.podium_action_cancel_speak_request_confirm_message
                        ) {
                            viewModel.cancelRequestToSpeak()
                        }
                    }
                    else -> return@setOnMenuItemClickListener false
                }
                return@setOnMenuItemClickListener true
            }
            setOnDismissListener {
                popup = null
            }
            show()
        }
    }


    private var actionMode: ActionMode? = null

    var searchBinding: LayoutActionModeSearchBinding? = null
    private fun showSearchMode(show: Boolean) {
        if (show) {
            viewModel.searchWaitingListKeyword.value = ""
            val callback = object : ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    val viewB: LayoutActionModeSearchBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_action_mode_search, null, false)
                    viewB.lifecycleOwner = viewLifecycleOwner
                    mode?.customView = viewB.root
                    searchBinding = viewB

                    viewB.apply {
                        keyword = viewModel.searchWaitingListKeyword
                        showKeyboard(searchBox)
                    }
                    return true
                }

                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    searchBinding?.apply {
                        showKeyboard(searchBox)
                    }
                    return false
                }

                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    return false
                }

                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.searchWaitingListKeyword.value = ""
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        } else {
            actionMode?.finish()
            actionMode = null
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_home_podium, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> {
                showSearchMode(true)
            }

            else -> return false
        }
        return true
    }
    override fun onPause() {
        super.onPause()
        if (activity?.isChangingConfigurations != true) {
            showSearchMode(false)
        }
    }
}