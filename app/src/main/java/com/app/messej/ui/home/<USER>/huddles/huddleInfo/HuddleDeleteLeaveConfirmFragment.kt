package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.databinding.FragmentHuddleDeleteLeaveConfirmBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class HuddleDeleteLeaveConfirmFragment : BottomSheetDialogFragment() {

    private val args: HuddleDeleteLeaveConfirmFragmentArgs by navArgs()

    private lateinit var binding: FragmentHuddleDeleteLeaveConfirmBinding

    companion object {
        const val HUDDLE_DELETE_LEAVE_REQUEST_KEY = "deleteOrLeaveHuddle"
        const val HUDDLE_DELETE_LEAVE_RESULT_KEY = "deleteOrLeaveHuddlePair"
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_huddle_delete_leave_confirm, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
    }

    private fun setup() {

        binding.confirmDeleteTitle.setText(if (args.isMangerOrAdmin) R.string.huddle_info_delete_title else R.string.huddle_info_leave_title)
        when(args.huddleType) {
            HuddleType.PUBLIC -> {
                binding.confirmDeleteText.setText(if (args.isMangerOrAdmin) R.string.huddle_info_delete_text else R.string.huddle_info_leave_text)
            }
            HuddleType.PRIVATE -> {
                binding.confirmDeleteText.setText(if (args.isMangerOrAdmin) R.string.huddle_info_delete_text_group else R.string.huddle_info_leave_text_group)
            }
        }

        binding.leaveOrDeleteButton.setText(if (args.isMangerOrAdmin) R.string.common_delete else R.string.common_leave)

        binding.leaveOrDeleteButton.setOnClickListener {
            findNavController().popBackStack()
            setFragmentResult(HUDDLE_DELETE_LEAVE_REQUEST_KEY, bundleOf(HUDDLE_DELETE_LEAVE_RESULT_KEY to true))
        }

        binding.cancelButton.setOnClickListener {
            findNavController().popBackStack()
        }
    }

}