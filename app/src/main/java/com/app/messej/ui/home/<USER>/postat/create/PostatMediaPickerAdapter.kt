package com.app.messej.ui.home.publictab.postat.create

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.databinding.ItemPostatMediaPickerBinding

class PostatMediaPickerAdapter(private val owner: LifecycleOwner, private val listener: PostatMediaActionListener): PagingDataAdapter<PostatDeviceMedia, PostatMediaPickerAdapter.PostatMediaVH>(PodiumDiff) {

    interface PostatMediaActionListener {
        fun onMediaSelected(view: View, media: PostatDeviceMedia)
    }

    private var isFirstItemSelected = false

    inner class PostatMediaVH(private val binding: ItemPostatMediaPickerBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PostatDeviceMedia) = with(binding) {
            media = item
            mediaHolder.setOnClickListener {
                listener.onMediaSelected(it, item)
            }

            // Automatically select the first item if not already selected
            if (!isFirstItemSelected && position == 0) {
                mediaHolder.performClick()
                isFirstItemSelected = true
            }
        }
    }

    object PodiumDiff : DiffUtil.ItemCallback<PostatDeviceMedia>() {
        override fun areItemsTheSame(oldItem: PostatDeviceMedia, newItem: PostatDeviceMedia) = oldItem.uri == newItem.uri
        override fun areContentsTheSame(oldItem: PostatDeviceMedia, newItem: PostatDeviceMedia): Boolean {
            return false
        }
    }

    override fun onBindViewHolder(holder: PostatMediaVH, position: Int) {
        getItem(position)?.let {
            holder.bind(it)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PostatMediaVH {
        val binding = ItemPostatMediaPickerBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        binding.lifecycleOwner = owner
        return PostatMediaVH(binding)
    }
}

