package com.app.messej.ui.customviews

import android.content.Context
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.BalloonSizeSpec
import com.skydoves.balloon.createBalloon

class CustomBalloonFactory(private val layoutR: View) : Balloon.Factory() {

    override fun create(context: Context, lifecycle: LifecycleOwner?): Balloon {
        return createBalloon(context) {
            setLayout(layoutR)
            setIsVisibleArrow(false)
            setMarginRight(16)
            setWidth(BalloonSizeSpec.WRAP)
            setHeight(BalloonSizeSpec.WRAP)
            setBackgroundColor(ContextCompat.getColor(context, android.R.color.transparent))
            setBalloonAnimation(BalloonAnimation.OVERSHOOT)
            setLifecycleOwner(lifecycle)
            build()
        }
    }

    companion object {
        fun withLayout(layout: View): CustomBalloonFactory {
            return CustomBalloonFactory(layout)
        }
    }
}