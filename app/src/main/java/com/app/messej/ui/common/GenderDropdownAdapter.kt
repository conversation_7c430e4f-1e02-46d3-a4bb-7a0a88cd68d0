package com.app.messej.ui.common

import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.widget.ImageViewCompat
import com.app.messej.R
import com.app.messej.ui.auth.common.GenderUIPackage
import com.app.messej.ui.auth.common.GenderUIPackage.Companion.getGenderUIList

class GenderDropdownAdapter(c: Context, genders: List<GenderUIPackage> = getGenderUIList()):
    ArrayAdapter<GenderUIPackage>(c, R.layout.item_gender_dropdown, genders) {

    private val mContext = c
    val mGenders = genders

    private var selectedPos: Int? = null
    fun setSelectedPos(pos: Int) {
        selectedPos = pos
    }

    override fun getItem(position: Int): GenderUIPackage {
        return mGenders[position]
    }

    override fun getCount(): Int {
        return mGenders.size
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var cView = convertView
        if (cView == null) {
            val inflater = (mContext as Activity).layoutInflater
            cView = inflater.inflate(R.layout.item_gender_dropdown, parent, false)
        }
        try {
            getItem(position).let { gender ->
                cView?.apply {
                    val icon: AppCompatImageView = findViewById(R.id.gender_icon)
                    val text: AppCompatTextView = findViewById(R.id.gender_name)
                    icon.setImageResource(gender.image)
                    text.setText(gender.name)
                    var color = if(selectedPos == position) R.color.textColorSecondary else R.color.textColorSecondaryLight
                    text.setTextColor(ContextCompat.getColor(mContext, color))
                    color = if(selectedPos == position) R.color.textColorSecondaryLight else R.color.textColorHint
                    ImageViewCompat.setImageTintList(icon, ColorStateList.valueOf(ContextCompat.getColor(mContext, color)))
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
        return cView!!
    }
}