package com.app.messej.ui.home.gift

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentGiftConversionHistoryBinding
import com.app.messej.ui.home.gift.adapter.GiftConversionHistoryPagerAdapter
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.kennyc.view.MultiStateView

class GiftConversionHistoryFragment : Fragment() {


    private lateinit var binding: FragmentGiftConversionHistoryBinding
    private val viewModel: GiftConversionHistoryViewModel by viewModels()
    private var conversionAdapter: GiftConversionHistoryPagerAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_conversion_history, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.gift_conversion_history)

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        initAdapter()
    }

    private fun observe() {
        viewModel.giftConversionHistory.observe(viewLifecycleOwner) {
            conversionAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

    private fun initAdapter() {
        conversionAdapter = GiftConversionHistoryPagerAdapter()

        val layoutManParticipant = LinearLayoutManager(context)

        binding.giftConversioHistory.apply {
            layoutManager = layoutManParticipant
            setHasFixedSize(true)
            adapter = conversionAdapter
        }

        conversionAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY
                    else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
            }
        }

        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.eds_empty_message).text = context.getString(R.string.no_coin_conversion_made)
            findViewById<AppCompatImageView>(R.id.eds_empty_image).setImageResource(R.drawable.bg_coin_conversion_history)
        }
    }





}