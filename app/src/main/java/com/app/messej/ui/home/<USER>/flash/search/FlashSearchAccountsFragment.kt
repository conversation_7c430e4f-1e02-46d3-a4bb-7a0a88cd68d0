package com.app.messej.ui.home.publictab.flash.search

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.huddles.FlashSearchResponse
import com.app.messej.databinding.FragmentFlashSearchAccountsBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class FlashSearchAccountsFragment : Fragment() {

    private lateinit var binding: FragmentFlashSearchAccountsBinding

    private val viewModel : FlashSearchViewModel by navGraphViewModels(R.id.nav_flash_search)

    private var mAdapter: FlashSearchAccountsAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_search_accounts, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            message = R.string.flash_search_account_eds
        )
    }

    private fun observe() {
        viewModel.flashAccountsList.observe(viewLifecycleOwner) { pagingData ->
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
        }
        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            it?.let { vs ->
                binding.multiStateView.viewState = vs
            }
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = FlashSearchAccountsAdapter(object : FlashSearchAccountsAdapter.ActionListener {
            override fun onItemClick(item: FlashSearchResponse.FlashUser) {
                findNavController().navigateSafe(FlashSearchFragmentDirections.actionGlobalNavFlashUser(item.id))
            }
        })

        val layoutMan = LinearLayoutManager(context)

        binding.flashAccountsList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                viewModel.setViewState(state)
                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }
    }
}