package com.app.messej.ui.home.publictab.maidan

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.databinding.FragmentTopSupportersBottomSheetBinding
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment

class TopSupportersBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private lateinit var binding: FragmentTopSupportersBottomSheetBinding
    private var mTopSupporterListAdapter: PodiumMaidanTopSupportersBottomSheetAdapter? = null
    private val viewModel: TopSupportersViewModel by viewModels()

    val args: TopSupportersBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_top_supporters_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.setParams(args.podiumId, args.challengeId)
        setUp()
        observe()
    }

    private fun setUp() {

        mTopSupporterListAdapter = PodiumMaidanTopSupportersBottomSheetAdapter()

        binding.topSupporters.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mTopSupporterListAdapter
        }
    }

    private fun observe() {
        viewModel.topSupportersList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mTopSupporterListAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }
    }
}