package com.app.messej.ui.home.deleteaccount

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.hadilq.liveevent.LiveEvent

class DeleteAccountViewModel(application: Application) : AndroidViewModel(application) {

    val cancelClick = LiveEvent<Boolean>()
    val accountDeleted = LiveEvent<Boolean>()
    fun setCancelClick(isCancel: Boolean) {
        cancelClick.postValue(isCancel)
    }

    fun setAccountDeleted(isAccountDeleted: Boolean) {
        accountDeleted.postValue(isAccountDeleted)
    }
}