package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.databinding.FragmentChallengeFacilitatorListBinding
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.kennyc.view.MultiStateView

class ChallengeFacilitatorListFragment : Fragment() {

    private lateinit var binding: FragmentChallengeFacilitatorListBinding
    private var mAdapter: ChallengeFacilitatorListAdapter? = null
    private val viewModel: PodiumCreateChallengeViewModel by navGraphViewModels(R.id.nav_challenge_setup)

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_challenge_facilitator_list, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.podium_challenge_facilitator_title)
    }

    private fun setup() {
        initAdapter()
        val message = when(viewModel.challengeType.value) {
            ChallengeType.MAIDAN -> ""
            ChallengeType.LIKES -> getString(R.string.podium_challenge_select_facilitator_likes)
            ChallengeType.GIFTS -> getString(R.string.podium_challenge_select_facilitator_gift)
            ChallengeType.PENALTY -> getString(R.string.podium_challenge_select_facilitator_penalty)
            ChallengeType.CONFOUR -> getString(R.string.podium_challenge_select_facilitator_confour)
            ChallengeType.FLAGS -> getString(R.string.podium_challenge_select_facilitator_flags)
            ChallengeType.BOXES -> getString(R.string.podium_challenge_select_facilitator_boxes)
            ChallengeType.KNOWLEDGE -> getString(R.string.podium_challenge_select_facilitator_knowledge)
            null -> ""
        }
        binding.selectFacilitatorDescription.text = message
        binding.actionNext.setOnClickListener {
            checkAndShowAlert()
        }
    }

    private fun checkAndShowAlert() {
        val list = viewModel.sortedFacilitatorList.value
        if (list?.size == 1) {
           showToast( R.string.podium_challenge_minimum_participant_not_met, Toast.LENGTH_SHORT)
            return
        }
        if (viewModel.user.id == viewModel.selectedFacilitator.value?.speaker?.id)  {
            viewModel.createChallenge()
            return
        }

        showFlashatDialog {
            setMessage(getString(R.string.podium_challenge_facilitator_invitation_alert, viewModel.selectedFacilitator.value?.speaker?.name))
            setCloseButtonText(R.string.common_cancel)
            setConfirmButton(R.string.common_confirm) {
                viewModel.createChallenge()
                true
            }
        }
    }

    private fun observe() {
        viewModel.sortedFacilitatorList.observe(viewLifecycleOwner) {
            mAdapter?.apply {
                if (data.size == 0 || it?.size==0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
            binding.multiStateView.viewState = if(it.isEmpty()) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
        }

        viewModel.challengeCreated.observe(viewLifecycleOwner) {
            if (it) {
                if (viewModel.selectedFacilitator.value?.speaker?.id != viewModel.user.id){
                    showToast(R.string.podium_invitation_sent, Toast.LENGTH_SHORT)
                    findNavController().popBackStack(R.id.nav_challenge_setup, true)
                } else {
                    val options = NavOptions.Builder()
                        .setPopUpTo(R.id.podiumChallengeListFragment, inclusive = true)
                        .build()
                    when (viewModel.challengeType.value) {
                        ChallengeType.FLAGS -> {
                            val action = ChallengeFacilitatorListFragmentDirections.actionPodiumLiveFragmentToPodiumChallengePrizeContributorFragment()
                            findNavController().navigateSafe(action,options)
                        }
                        ChallengeType.CONFOUR, ChallengeType.PENALTY, ChallengeType.BOXES, ChallengeType.KNOWLEDGE -> {
                            val action = ChallengeFacilitatorListFragmentDirections.actionPodiumLiveFragmentToPodiumConFourChallengeParticipantFragment()
                            findNavController().navigateSafe(action, options)
                        }
                        else -> {
                            val action = ChallengeFacilitatorListFragmentDirections.actionPodiumLiveFragmentToChallengeTimerFragment()
                            findNavController().navigateSafe(action, options)
                        }
                    }
                }
            }
        }

        viewModel.podiumDetailsLoading.observe(viewLifecycleOwner) {
            if (it)  {
                binding.selectedProfileMultiState.viewState = MultiStateView.ViewState.LOADING
                binding.multiStateView.viewState = MultiStateView.ViewState.LOADING
            }
            else {
                binding.selectedProfileMultiState.viewState = MultiStateView.ViewState.CONTENT
                binding.multiStateView.viewState = MultiStateView.ViewState.CONTENT
            }
        }
    }

    private fun initAdapter() {
        mAdapter = ChallengeFacilitatorListAdapter(mutableListOf())
        val layoutMan = LinearLayoutManager(context)

        binding.facilitatorList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter!!.apply {
            currentUserId = viewModel.user.id
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true

            setOnItemClickListener { adapter, view, position ->
                val user = (adapter as ChallengeFacilitatorListAdapter).data[position]
                viewModel.selectFacilitator(user.speaker.id)
            }
            setDiffCallback(ChallengeFacilitatorListAdapter.DiffCallback())
        }
    }
}