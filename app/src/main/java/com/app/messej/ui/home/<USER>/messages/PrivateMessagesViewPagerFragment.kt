package com.app.messej.ui.home.privatetab.messages

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.databinding.LayoutPrivateMessagesBinding
import com.app.messej.ui.home.privatetab.HomePrivateFragmentDirections
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoinFromGreenDot
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PrivateMessagesViewPagerFragment : PrivateMessagesBaseFragment() {

    override lateinit var binding: LayoutPrivateMessagesBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        tab = parseTabBundle(arguments)
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_private_messages, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun navigateToMessageSearch() {
        findNavController().navigateSafe(HomePrivateFragmentDirections.actionHomePrivateFragmentToHomePrivateMessagesSearchFragment())
    }

    override fun navigateToChat(roomId: String, receiver: Int) {
        findNavController().navigateSafe(HomePrivateFragmentDirections.actionGlobalNavigationChatPrivate(roomId, receiver))
    }

    override fun navigateToPrivacy() {
        findNavController().navigateSafe(HomePrivateFragmentDirections.actionHomePrivateFragmentToPrivateMessagesPrivacyFragment())
    }

    override fun navigateToLivePodium(item: PrivateChat) {
        val podiumId = item.receiverDetails.userLivePodiumId ?: return
        validateAndConfirmJoinFromGreenDot(podiumId, item.receiverDetails.name, viewModel.user)
    }

    override fun navigateToBlockedMessages() {
        findNavController().navigateSafe(HomePrivateFragmentDirections.actionGlobalBlockedPrivateMessagesFragment())
    }

    override fun navigateToPendingList() {
        findNavController().navigateSafe(HomePrivateFragmentDirections.actionHomePrivateFragmentToPrivateMessagePendingListFragment())
    }


}