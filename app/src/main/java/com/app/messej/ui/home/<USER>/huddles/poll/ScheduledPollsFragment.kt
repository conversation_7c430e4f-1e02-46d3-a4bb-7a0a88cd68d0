package com.app.messej.ui.home.publictab.huddles.poll

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.enums.PollType
import com.app.messej.databinding.FragmentSchedulePollBinding
import com.app.messej.databinding.LayoutEmptyPollsBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView

class ScheduledPollsFragment : Fragment() {
    private lateinit var binding: FragmentSchedulePollBinding
    private var mAdapter: ScheduledPollsListAdapter? = null
    private val viewModel: ScheduledPollsViewModel by viewModels()
    private val args: ScheduledPollsFragmentArgs by navArgs()


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_schedule_poll, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }


    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        when (args.pollType) {
            PollType.SCHEDULE_POLL -> binding.customActionBar.toolBarTitle.text = resources.getString(R.string.title_schedule_poll)
            PollType.PAST_POLL-> binding.customActionBar.toolBarTitle.text = resources.getString(R.string.title_past_poll)
            else -> {}
        }

    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.polls.observe(viewLifecycleOwner) {
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

        setFragmentResultListener(CreatePollFragment.POLL_REQUEST_KEY) { _, bundle ->
            val huddleId = bundle.getInt(CreatePollFragment.POLL_RESPONSE)
            viewModel.refreshPolls(huddleId)

        }
    }

    private fun setup() {
        viewModel.seArgs(args.huddleId,args.pollType, args.isUserManager)
        initAdapter()
        setEmptyView()
    }

    private fun setEmptyView() {
        val emptyView = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?:return
        val emptyViewBinding = LayoutEmptyPollsBinding.bind(emptyView)
        binding.multiStateView.setViewForState(emptyViewBinding.root,MultiStateView.ViewState.EMPTY)
    }

    private fun initAdapter() {
        val layoutMan = LinearLayoutManager(context)
        mAdapter = ScheduledPollsListAdapter(object : ScheduledPollsListAdapter.ActionListener{
            override fun onViewMoreClick(pollId: Int) {
                when (args.pollType) {
                    PollType.PAST_POLL ->
                    {
                        if(args.isUserManager) {
                            findNavController().navigateSafe(ScheduledPollsFragmentDirections.actionSchedulePollFragmentToPollResultFragment(pollId))
                        }
                    }
                    else -> {


                    }
                }
            }
            override fun onEditClick(poll: Poll) {
                findNavController().navigateSafe(ScheduledPollsFragmentDirections.actionSchedulePollToCreatePoll(huddleId = args.huddleId, pollId = poll.id, isUserManager = args.isUserManager, type = "Edit"))
            }
        }, args.pollType, args.isUserManager)
        binding.listPollsScheduled.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter

        }
        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)

            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }


    }
}