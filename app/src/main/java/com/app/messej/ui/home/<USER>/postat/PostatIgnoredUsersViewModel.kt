package com.app.messej.ui.home.publictab.postat

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.PostatMentionedUser
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.repository.PostatRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class PostatIgnoredUsersViewModel(application: Application) : AndroidViewModel(application) {

    val postatRepo = PostatRepository(application)
    val profileRepo = ProfileRepository(application)

  private val _blockedUsersList = postatRepo.getPostatBlockedListPager().liveData.cachedIn(viewModelScope)

    val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )
    private val nickNamesLiveData = nickNames.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val userBlockLoading = MediatorLiveData(false
  )
  val blockedUserList:MediatorLiveData<PagingData<PostatMentionedUser>> by lazy {
      val med = MediatorLiveData<PagingData<PostatMentionedUser>>()
      fun update() {
          val data = _blockedUsersList.value?.map {
              it.copy(
                  name = nickNames.nickNameOrName(it),
                  username = nickNames.nickNameOrName(it),
              )
          }
          med.postValue(data)
      }
      med.addSource(_blockedUsersList) { update() }
      med.addSource(nickNamesLiveData) { update() }
      med

  }

    val onPostatUserIgnored = LiveEvent<String>()

    fun ignorePostatUser(user: PostatMentionedUser, block: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = postatRepo.ignorePostatUser(user.id.toString(), block)) {
                is ResultOf.Success -> {
                    onPostatUserIgnored.postValue(user.name)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

}