package com.app.messej.ui.home.publictab.flash

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.enums.FlashTab
import com.app.messej.ui.home.publictab.flash.player.BaseFlashPlayerViewModel

class PublicFlashListViewModel(application: Application) : BaseFlashPlayerViewModel(application) {

    val showCompactLoading = MutableLiveData<Boolean>(false)

    private val _tab = MutableLiveData(FlashTab.default)
    val tab: LiveData<FlashTab?> = _tab.distinctUntilChanged()

    fun setTab(tab: FlashTab) {
        _tab.value = tab
    }

    override val _flashFeedList = tab.switchMap {
        it?: return@switchMap null
        if (it == FlashTab.ME) flashRepo.getMyFlashFeedPager().liveData.cachedIn(viewModelScope)
        else flashRepo.getFlashFeedPager(it).liveData.cachedIn(viewModelScope)
    }

}