package com.app.messej.ui.home.gift.bottomSheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentGiftInfoBottomsheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class GiftInfoBottomSheet : BottomSheetDialogFragment() {


    private lateinit var binding: FragmentGiftInfoBottomsheetBinding
    private val viewModel: GiftInfoBottomSheetViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_info_bottomsheet, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
    }

    private fun setUp() {
        binding.giftBack.setOnClickListener {
            dialog?.dismiss()
        }
    }
}