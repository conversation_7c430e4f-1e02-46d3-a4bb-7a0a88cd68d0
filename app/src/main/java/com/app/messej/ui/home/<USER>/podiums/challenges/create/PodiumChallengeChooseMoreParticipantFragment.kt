package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.view.ActionMode
import androidx.core.os.bundleOf
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.databinding.FragmentConfourChooseMoreParticipantBinding
import com.app.messej.databinding.LayoutActionModeSearchBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showKeyboard
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.ViewUtils

class PodiumChallengeChooseMoreParticipantFragment : Fragment(), MenuProvider {
    private lateinit var binding: FragmentConfourChooseMoreParticipantBinding
    private var mAdapter: PodiumChooseMoreParticipantAdapter? = null
    private val viewModel: PodiumCreateChallengeViewModel by navGraphViewModels(R.id.nav_challenge_setup)
    val args: PodiumChallengeChooseMoreParticipantFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_confour_choose_more_participant, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.podium_live_users)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }
    private var actionMode: ActionMode? = null

    var searchBinding: LayoutActionModeSearchBinding? = null

    private fun showSearchMode(show: Boolean) {
        if (show) {
            viewModel.searchContributorKeyword.value = ""
            val callback = object : ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    val viewB: LayoutActionModeSearchBinding = DataBindingUtil.inflate(layoutInflater, R.layout.layout_action_mode_search, null, false)
                    viewB.lifecycleOwner = viewLifecycleOwner
                    mode?.customView = viewB.root
                    searchBinding = viewB

                    viewB.apply {
                        keyword = viewModel.searchContributorKeyword
                        showKeyboard(searchBox)
                    }
                    return true
                }

                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    searchBinding?.apply {
                        showKeyboard(searchBox)
                    }
                    return false
                }

                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    return false
                }

                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.searchContributorKeyword.postValue("")
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        } else {
            actionMode?.finish()
            actionMode = null
        }
    }
    private fun setup() {
        initAdapter()

        binding.sendFab.setOnClickListener {
            if (viewModel.minimumConFourParticipantsSelected()) {
                viewModel.sendConFourParticipantInvite(args.podiumId, args.challengeId)
            } else {
                showToast(R.string.podium_challenge_minimum_participant_not_met)
            }
        }

        binding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
            }
        }
    }

    private fun observe() {
        viewModel.liveContributorSearchList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }

        viewModel.conFourInviteSent.observe(viewLifecycleOwner) {
            setFragmentResult(
                PodiumChallengeParticipantFragment.CHALLENGE_INVITE_REQUEST_KEY, bundleOf(
                    PodiumChallengeParticipantFragment.CHALLENGE_INVITE_REQUEST_PAYLOAD to it
                )
            )
            if (args.startUpFlow) {
                findNavController().popBackStack(R.id.nav_challenge_setup, true)
            } else {
                findNavController().popBackStack()
            }
        }
    }

    private fun initAdapter() {
        viewModel.chooseMoreConFourParticipantList = arrayListOf()

        fun addParticipant(item: PodiumParticipant) {
            if (viewModel.chooseMoreConFourParticipantList.find { it.id == item.id }==null) {
                viewModel.chooseMoreConFourParticipantList.add(item)
            }
        }

        mAdapter =
            PodiumChooseMoreParticipantAdapter(object : PodiumChooseMoreParticipantAdapter.ItemListener {
                override fun onItemCheckChanged(item: PodiumParticipant, onChecked: Boolean) {
                    if (onChecked && !viewModel.chooseMoreConFourParticipantList.contains(item)) {
                        addParticipant(item)
                    } else if (!onChecked && viewModel.chooseMoreConFourParticipantList.contains(item)) {
                        viewModel.chooseMoreConFourParticipantList.remove(item)
                    }
                    viewModel.validConFourParticipantSelection()
                    Log.d("CONFOURPARTICIPANTLIST", viewModel.chooseMoreConFourParticipantList.toString())
                }

                override fun isAlreadySelected(item: PodiumParticipant): Boolean {
                    val selected =  viewModel.speakersForParticipant.value?.find { item.id == it.speaker.id }?.selected == true
                    if (selected) {
                        addParticipant(item)
                    }
                    return selected
                }
            })

        val layoutMan = LinearLayoutManager(context)
        binding.liveUsers.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                if (loadState.refresh !is LoadState.Loading) {
                    binding.swipeRefresh.isRefreshing = false
                }
                val state = ViewUtils.getViewState(loadState, itemCount)
                binding.multiStateView.viewState = state
            }
            refresh()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_home_podium, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> {
                showSearchMode(true)
            }

            else -> return false
        }
        return true
    }
    override fun onPause() {
        super.onPause()
        if (activity?.isChangingConfigurations != true) {
            showSearchMode(false)
        }
    }
}