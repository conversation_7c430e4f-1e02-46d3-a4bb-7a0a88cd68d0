package com.app.messej.ui.home.businesstab.operations.tasks

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.app.messej.R
import com.app.messej.databinding.FragmentBusnessOperationHomeBinding


class BusinessOperationTaskHomeFragment : Fragment() {
    private lateinit var binding: FragmentBusnessOperationHomeBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_busness_operation_home, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return  binding.root

    }
}