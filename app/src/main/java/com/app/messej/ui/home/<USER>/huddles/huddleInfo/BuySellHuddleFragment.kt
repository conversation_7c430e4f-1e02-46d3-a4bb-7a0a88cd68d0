package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.GiftConversion
import com.app.messej.databinding.FragmentBuySellHuddleBinding
import com.app.messej.databinding.LayoutBuyHuddleConfirmProceedBinding
import com.app.messej.ui.customviews.CustomBalloonFactory
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.skydoves.balloon.Balloon
import java.util.Locale

class BuySellHuddleFragment : Fragment() {

    private lateinit var binding: FragmentBuySellHuddleBinding
    private val viewModel: BuySellHuddleViewModel by viewModels()
    private val args: BuySellHuddleFragmentArgs by navArgs()

    private lateinit var textView: AppCompatTextView
    private lateinit var profileBalloon: Balloon

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_buy_sell_huddle, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.visibility = View.GONE
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val layout = LayoutInflater.from(context).inflate(R.layout.layout_buy_huddle_flax_information, null)
        val balloonFactory = CustomBalloonFactory.withLayout(layout)
        profileBalloon = balloonFactory.create(requireContext(), viewLifecycleOwner)
        textView = profileBalloon.getContentView().findViewById(R.id.flax_amount_value)
        observe()
        setUp()
    }

    companion object {
        const val BUY_HUDDLE_RESULT_KEY = "buy_huddleResultKey"
        const val BUY_HUDDLE_RESPONSE = "buyHuddleResponse"
        const val MAX_FLAX_VISIBILITY = 1000.00
    }

    private fun setUp() {
        args.huddleId.let {
            viewModel.setHuddleId(it)
        }
        binding.textViewSellHuddleFlax.setOnClickListener {
            if (viewModel.selectedSellHuddle.value!!.flax!! >= MAX_FLAX_VISIBILITY) {
                showHuddleBadge(it)
            }
        }


        binding.actionSellHuddle.setOnClickListener {
            viewModel.buyHuddle()
        }
        binding.actionCancelSaleOffer.setOnClickListener {
            findNavController().popBackStack()
        }
    }

    private fun observe() {
        viewModel.showSuccess.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            findNavController().popBackStack()
            setFragmentResult(BUY_HUDDLE_RESULT_KEY, bundleOf(BUY_HUDDLE_RESPONSE to args.huddleId))
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }
        viewModel.errorCode.observe(viewLifecycleOwner) {
            MaterialDialog(requireContext()).show {
                val view = DataBindingUtil.inflate<LayoutBuyHuddleConfirmProceedBinding>(layoutInflater, R.layout.layout_buy_huddle_confirm_proceed, null, false)
                view.viewModel = viewModel
                customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
                cancelable(false)
                view.nickNameTitle.text = getString(R.string.buy_huddle_lower_flax_balance)
                view.nickNameTitle.textSize = 13F
                view.actionProceed.setOnClickListener {
                    findNavController().navigateSafe(BuySellHuddleFragmentDirections.actionBuyHuddleFragmentToGiftConverterFragment(GiftConversion.COIN_TO_FLAX))
                    dismiss()
                }
                view.actionCancel.setOnClickListener {
                    dismiss()
                }
            }
        }
    }

    @SuppressLint("StringFormatInvalid")
    private fun showHuddleBadge(it: View) {
        profileBalloon.showAlignLeft(anchor = it, xOff = it.x.toInt(), yOff = it.y.toInt())
        textView.text = getString(R.string.sell_huddle_flax, String.format(Locale.US, "%.0f", viewModel.selectedSellHuddle.value?.flax ?: 0.0))

    }

}