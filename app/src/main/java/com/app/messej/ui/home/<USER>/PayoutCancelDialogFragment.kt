package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import com.app.messej.databinding.FragmentPayoutCancelDialogListDialogBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class PayoutCancelDialogFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentPayoutCancelDialogListDialogBinding


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {

        binding = FragmentPayoutCancelDialogListDialogBinding.inflate(inflater, container, false)
        return binding.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        setup()
    }

    private fun setup() {
        binding.discardButton.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.cancelButton.setOnClickListener {
            findNavController().navigateSafe(PayoutCancelDialogFragmentDirections.actionGlobalHomeBusinessFragment(destination = HomeBusinessFragment.TAB_DEALS))
        }
    }
}