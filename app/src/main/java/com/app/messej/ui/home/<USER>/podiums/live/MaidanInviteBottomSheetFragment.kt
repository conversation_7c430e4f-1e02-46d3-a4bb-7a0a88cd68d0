package com.app.messej.ui.home.publictab.podiums.live

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.MaidanEditResponse
import com.app.messej.databinding.FragmentCreateMaidanBinding
import com.app.messej.ui.home.publictab.maidan.CreateMaidanViewModel
import com.app.messej.ui.home.publictab.podiums.challenges.create.ContributorSearchAdapter
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import kotlin.math.roundToInt

class MaidanInviteBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentCreateMaidanBinding
    private val viewModel: CreateMaidanViewModel by viewModels()
    private val liveViewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_create_maidan, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            state = BottomSheetBehavior.STATE_COLLAPSED
        }
        liveViewModel.lastChallenge?.let {
            viewModel.prefillData(it)
        }
        binding.cancelButton.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.createButton.setOnClickListener {
            when (liveViewModel.maidanCompetitorStatus.value) {
                PodiumLiveViewModel.MaidanCompetitorStatus.CHALLENGE_NEW -> {
                    liveViewModel.challengeAgain(
                        challengeNew = true,
                        prize = if (viewModel.challengeType.value == CreateMaidanViewModel.ChallengeType.Free) 0
                        else viewModel.priceAmountString.value.orEmpty().toInt()
                    )
                }

                PodiumLiveViewModel.MaidanCompetitorStatus.INVITE -> {
                    viewModel.inviteOtherUser(liveViewModel.podiumId.value ?: return@setOnClickListener)
                }
                else -> {}
            }

        }
    }

    private fun observe() {

        val textInputEditText = binding.textInputChallengerSearch.editText as AutoCompleteTextView
        textInputEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val searchText = s.toString()
                if (searchText.isNotEmpty()) {
                    viewModel.searchContributor(searchText)
                } else {
                    textInputEditText.dismissDropDown()
                }
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })

        viewModel.contributorsSearchList.observe(viewLifecycleOwner) {
            if (it != null) {
                val mAdapter = ContributorSearchAdapter(requireContext(), it)
                (binding.textInputChallengerSearch.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(mAdapter)
                    mAdapter.notifyDataSetChanged()
                    setOnItemClickListener { _, _, position, _ ->
                        mAdapter.getItem(position).let { podiumSpeaker ->
                            text = null
                            viewModel.onContributorSelected(podiumSpeaker)
                        }
                    }
                }
            }
        }

        viewModel.onMaidanCreated.observe(viewLifecycleOwner) {
            if ((it.challenge?.challengeFee ?: 0.0) != 0.0) {
                showToast(getString(R.string.podium_maidan_fee_debit_toast, it.challenge?.challengeFee?.roundToInt().toString().orEmpty()))
            }
            findNavController().popBackStack()
        }
        viewModel.onMaidanEdited.observe(viewLifecycleOwner) {
            when(it) {
                is MaidanEditResponse.PrizeChange.Debit ->
                    showToast(getString(R.string.podium_maidan_fee_debit_toast, it.amount.roundToInt().toString()))
                is MaidanEditResponse.PrizeChange.Refund ->
                    showToast(getString(R.string.podium_maidan_fee_refund_toast, it.amount.roundToInt().toString()))
                null -> {}
            }
            findNavController().popBackStack()
        }
        viewModel.onMaidanCreateError.observe(viewLifecycleOwner) {
            showSnackbar(it)
        }

        viewModel.createMaidanLoading.observe(viewLifecycleOwner) {

        }

        liveViewModel.challengeError.observe(viewLifecycleOwner) { msg ->
            msg?.let { showToast(message = it) }
        }

        liveViewModel.onNewMaidanCreated.observe(viewLifecycleOwner) {
            if ((it.challengeFee ?: 0.0) != 0.0) {
                showToast(getString(R.string.podium_maidan_fee_debit_toast, it.challengeFee?.roundToInt().toString()))
            }
            findNavController().popBackStack()
        }
    }
}