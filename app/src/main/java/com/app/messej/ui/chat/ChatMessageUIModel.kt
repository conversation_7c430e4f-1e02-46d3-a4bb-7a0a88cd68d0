package com.app.messej.ui.chat

import androidx.annotation.DrawableRes
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AbstractComment
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.model.entity.PostReply
import java.time.ZonedDateTime

sealed class ChatMessageUIModel {

    data class ChatMessageModel(
        val chat : AbstractChatMessageWithMedia,
        var showName: Boolean = true,

        //status chips
        var ongoingTransfer: MediaTransfer? = null,
        var playerInfo: MediaPlayerInfo? = null,
        var selected: Boolean = false,

        @DrawableRes
        var countryFlag: Int? = null,

        var highlightMatches: String? = null,
    ) : ChatMessageUIModel() {
        val message: AbstractChatMessage
            get() = chat.message

        var expanded: Boolean = false

        val isTransferringMedia: Boolean
            get() = ongoingTransfer!=null
    }

    data class DateSeparatorModel(val date: ZonedDateTime) : ChatMessageUIModel()
    data class UnreadSeparatorModel(val count: Int) : ChatMessageUIModel()
    data class PostCommentModel(val comment: AbstractComment) : ChatMessageUIModel() {
        @DrawableRes
        var countryFlag: Int? = null
    }


    data class PostCommentWithRepliesModel(val commentWithReplies: PostCommentWithReplies) : ChatMessageUIModel() {
        @DrawableRes
        var countryFlag: Int? = null


        val comment: AbstractComment
            get() = commentWithReplies.commentItem

        val reply:List<PostReply>
            get() = commentWithReplies.replies

        val hasReplies: Boolean
            get() = commentWithReplies.hasReplies


        val replyCount: Int
            get() = commentWithReplies.replyCount
    }

    data class PostCommentHeaderModel(val count: Int) : ChatMessageUIModel()

    object PostCommentEmptyModel : ChatMessageUIModel()

}