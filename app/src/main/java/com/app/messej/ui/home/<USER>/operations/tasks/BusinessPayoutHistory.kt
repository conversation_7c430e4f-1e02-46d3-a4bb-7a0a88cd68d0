package com.app.messej.ui.home.businesstab.operations.tasks

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentPayoutHistoryBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView


class BusinessPayoutHistory:Fragment() {

    private var mAdapter: PayoutHistoryAdapter?=null
    lateinit var binding: FragmentPayoutHistoryBinding
    val viewModel:PayoutHistoryViewModel by viewModels()
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_payout_history, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }


    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar, customBackButton = true)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.title_sell_flax_history)

    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
           viewModel.history.observe(viewLifecycleOwner){
               mAdapter?.submitData(lifecycle,it)
           }
    }


    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.apply {
            edsEmptyImage.setImageResource(R.drawable.bg_empty_flix)
            edsEmptyMessage.text = getString(R.string.title_empty_history)
        }
    }
    private fun setup() {
         setEmptyView()
        val layoutManager = LinearLayoutManager(requireContext())
        mAdapter = PayoutHistoryAdapter()
        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)

            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutManager.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutManager.scrollToPosition(0)
                    }
                }
            })
        }

        ( binding.list.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        binding.list.layoutManager = layoutManager
        binding.list.adapter = mAdapter
    }
}