package com.app.messej.ui.home.gift

import android.os.Bundle
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.databinding.FragmentPointsToFlaxDialogBinding
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences


class FlaxToPointsConvertDialogFragment : DialogFragment() {
    private val args:FlaxToPointsConvertDialogFragmentArgs by navArgs()
    private lateinit var binding:FragmentPointsToFlaxDialogBinding
    private val viewModel:FlaxToPointsConvertViewModel by activityViewModels()

    companion object {
        const val PURCHASE_SUCCESS = "purchase_success"
        const val PURCHASE_ERROR = "purchase_error"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_points_to_flax_dialog, container, false)
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        viewModel.seArgs(args.Point,args.flax)
        binding.buttonCancel.setOnClickListener {
            findNavController().popBackStack()
        }
        binding.buttonConfirm.setOnClickListener {
//            viewModel.giftFlaxToPointConvert()
        }
        setUpAmountText()
    }
    private fun observe() {

        viewModel.successMessage.observe(viewLifecycleOwner) { successMessage ->
            setFragmentResult(PURCHASE_SUCCESS, Bundle())
            findNavController().popBackStack()
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            setFragmentResult(PURCHASE_ERROR, Bundle())
            findNavController().popBackStack()
        }

    }

    fun setUpAmountText(){
        val coinsAmount = viewModel.pointsVar.value?:"0"
        val flaxAmount = viewModel.flaxVar.value?:"0"
        val fullText = context?.getString(R.string.point_to_flax_gift_purchase_confirmation, coinsAmount, flaxAmount)

        val spannableString = SpannableString(fullText)

        spannableString.highlightOccurrences(coinsAmount) {
            ForegroundColorSpan(resources.getColor(R.color.colorPrimary,null))
        }

        spannableString.highlightOccurrences(flaxAmount) {
            ForegroundColorSpan(resources.getColor(R.color.colorPrimary,null))
        }

        binding.textLabelOne.text = spannableString
    }

}