package com.app.messej.ui.auth.common

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.repository.ProfileRepository
import com.google.android.libraries.places.api.model.AutocompletePrediction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce

@OptIn(FlowPreview::class)
class LocationSearchViewModel(application: Application) : AndroidViewModel(application) {

    private val profileRepo = ProfileRepository(getApplication())

    var searchKeyWord = MutableLiveData<String?>(null)

    val debouncedSearchKeyword = searchKeyWord.asFlow().debounce(300L).asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val _suggestions: MutableLiveData<List<AutocompletePrediction>> = MutableLiveData(listOf())
    val suggestions: MutableLiveData<List<AutocompletePrediction>> = _suggestions

    fun updateSearchResults(list: List<AutocompletePrediction>) {
        _suggestions.value = list
    }

}