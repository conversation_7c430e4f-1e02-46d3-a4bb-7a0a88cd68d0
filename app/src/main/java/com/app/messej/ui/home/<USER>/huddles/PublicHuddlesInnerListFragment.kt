package com.app.messej.ui.home.publictab.huddles

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.enums.HuddleAction
import com.app.messej.data.model.enums.HuddleTab
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.databinding.LayoutPublicHuddlesInnerListBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.kennyc.view.MultiStateView.ViewState

class PublicHuddlesInnerListFragment : Fragment() {

    private lateinit var binding: LayoutPublicHuddlesInnerListBinding

    private var mAdapter: PublicHuddlesAdapter? = null

    private val viewModel: PublicHuddlesViewModel by activityViewModels()

    protected lateinit var tab: HuddleTab

    companion object {
        const val ARG_TAB = "tab"

        fun getTabBundle(tab: HuddleTab) = Bundle().apply {
            putInt(ARG_TAB,tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): HuddleTab {
            val tabInt = bundle?.getInt(ARG_TAB)?:0
            return HuddleTab.values()[tabInt]
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_public_huddles_inner_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tab = parseTabBundle(arguments)
        Log.d("PHILF", "onViewCreated: $tab")
        setup()
        observe()
    }

    override fun onResume() {
        super.onResume()
        if (mAdapter?.itemCount!! > 0) {
            mAdapter?.refresh()
        }
    }

    private fun setup() {
        setEmptyView()
        initAdapter()

        if (tab == HuddleTab.TAB_JOINED || tab==HuddleTab.TAB_SUGGESTED) {
            viewModel.getHuddlesRequestCount()
            binding.requestAndInvitesCard.apply {
                isVisible = true
                setOnClickListener {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalHuddleRequestListFragment())
                }
            }
            if (tab==HuddleTab.TAB_SUGGESTED){
                binding.huddlesForSaleCard.apply {
                    isVisible = viewModel.user.premium
                    setOnClickListener {
                        findNavController().navigateSafe(HomePublicFragmentDirections.actionHomePublicFragmentToSellHuddleListFragment())
                    }
                }
            }
        }
        else if (tab == HuddleTab.TAB_MINE) {
            viewModel.getHuddleMyPostSummary()
        }

    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(ViewState.EMPTY)?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.edsEmptyAction.isAllCaps = false
        when (tab){
            HuddleTab.TAB_MINE -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_huddles,
                    message = R.string.public_huddle_eds,
                    action = R.string.public_huddle_action_create_new
                ) {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreateHuddleFragment(HuddleType.PUBLIC))
                }
            }
            HuddleTab.TAB_ADMIN -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_huddles_admin,
                    message = R.string.public_huddle_eds_admin
                )
            }
            HuddleTab.TAB_JOINED -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_huddles,
                    message = R.string.public_huddle_eds,
                    action = R.string.public_huddle_action_join
                ) {
                    viewModel.setCurrentTab(HuddleTab.TAB_SUGGESTED)
                }
            }
            HuddleTab.TAB_SUGGESTED -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_huddles,
                    message = R.string.public_huddle_eds,
                )
            }
        }
    }

    private fun observe() {
        Log.d("PHILF", "observe: loading $tab")
        when (tab) {
            HuddleTab.TAB_MINE -> viewModel.huddleMineList
            HuddleTab.TAB_ADMIN -> viewModel.huddleAdminList
            HuddleTab.TAB_JOINED -> viewModel.huddleJoinedList
            HuddleTab.TAB_SUGGESTED -> viewModel.huddleSuggestionList
        }.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }

        viewModel.huddleSelectionMode.observe(viewLifecycleOwner) {
            Log.d("BHLVM", "observe: showSelectionMode for ${viewModel.currentTab.value} in $tab: $it")
            if (viewModel.currentTab.value==tab) {
               showSelectionMode(it)
            }
        }
        viewModel.onItemChange.observe(viewLifecycleOwner) {
            if (viewModel.currentTab.value!=tab) return@observe
            mAdapter?.notifyItemChanged(it)
        }

        viewModel.actionIsPin.observe(viewLifecycleOwner) { pin ->
            if (viewModel.currentTab.value!=tab) return@observe
            actionMode?.menu?.apply {
                findItem(R.id.action_pin).setIcon(if(pin) R.drawable.ic_pin else R.drawable.ic_unpin)
            }
        }
        viewModel.actionIsMute.observe(viewLifecycleOwner) { mute ->
            if (viewModel.currentTab.value!=tab) return@observe
            actionMode?.menu?.apply {
                findItem(R.id.action_mute).setIcon(if(mute) R.drawable.ic_mute else R.drawable.ic_unmute)
            }
        }

        viewModel.pinActionIsVisible.observe(viewLifecycleOwner) { tribe ->
            if (viewModel.currentTab.value!=tab) return@observe
            actionMode?.menu?.apply {
                findItem(R.id.action_pin).isVisible = tribe
            }
        }

        viewModel.onPinLimitExceed.observe(viewLifecycleOwner) {
            if (viewModel.currentTab.value!=tab) return@observe
            showSnackbar(resources.getString(R.string.huddle_pin_limit_exceed, it))
        }

        viewModel.onPinMuteAction.observe(viewLifecycleOwner){
            if (viewModel.currentTab.value!=tab) return@observe
            when(it){
                HuddleAction.PIN -> Toast.makeText(requireContext(), resources.getString(R.string.huddle_list_pinned), Toast.LENGTH_SHORT).show()
                HuddleAction.UNPIN -> Toast.makeText(requireContext(), resources.getString(R.string.huddle_list_unpinned), Toast.LENGTH_SHORT).show()
                HuddleAction.MUTE -> Toast.makeText(requireContext(), resources.getString(R.string.huddle_list_muted), Toast.LENGTH_SHORT).show()
                HuddleAction.UNMUTE -> Toast.makeText(requireContext(), resources.getString(R.string.huddle_list_unmuted), Toast.LENGTH_SHORT).show()
            }
        }
        viewModel.onHuddleActionError.observe(viewLifecycleOwner) {
            if (viewModel.currentTab.value!=tab) return@observe
            showSnackbar(it)
        }
    }

    override fun onPause() {
        super.onPause()
        if(activity?.isChangingConfigurations != true) {
            viewModel.exitSelectionMode()
        }
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = PublicHuddlesAdapter(layoutInflater,viewModel.user.id, object: PublicHuddlesAdapter.ItemListener {
            override fun onItemClick(item: AbstractHuddle, position: Int) {
                if ((item is PublicHuddle && viewModel.selectHuddle(item, position))) return
                if (item is PublicHuddle && item.isTribe && viewModel.currentTab.value == HuddleTab.TAB_MINE && item.isTribeEdit) {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalTribeEdit(item.id))
                } else {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavChatHuddle(item.id))
                }
            }

            override fun onItemLongClick(item: PublicHuddle, position: Int) {
                viewModel.enterSelectionMode(item,position)
            }

            override fun onMyPostClick() {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalHuddleMyPosts())
            }

            override fun onEdsActionCLick() {
                ensureInteractionAllowed {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreateHuddleFragment(HuddleType.PUBLIC))
                }
            }

            override fun isVisitor(): Boolean {
                return viewModel.isVisitor
            }

        })

        val layoutMan = LinearLayoutManager(context)

        binding.huddleList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.setTabDataCount(tab,itemCount)
                if (viewModel.currentTab.value==tab) {
                    viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
                }
            }
            registerAdapterDataObserver(object: RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition()==0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }

    var actionMode: ActionMode? = null

    private fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_huddles_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_pin -> viewModel.togglePin()
                        R.id.action_mute -> viewModel.toggleMute()
                    }
                    return false
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    Log.d("BHLVM", "onDestroyActionMode: ")
                    viewModel.exitSelectionMode()
                    actionMode = null
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            Log.d("BHLVM", "showSelectionMode: false")
            actionMode?.finish()
            actionMode = null
        }
    }
}