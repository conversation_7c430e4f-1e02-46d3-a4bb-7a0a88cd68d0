package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumChallengeConfourParticipantBinding
import com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumCreateChallengeViewModel.SelectableSpeakerUIModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class PodiumChallengeParticipantFragment : Fragment() {
    private lateinit var binding: FragmentPodiumChallengeConfourParticipantBinding
    private var mAdapter: PodiumChallengeParticipantsQuickAdapter? = null
    private val viewModel: PodiumCreateChallengeViewModel by navGraphViewModels(R.id.nav_challenge_setup)

    companion object {
        const val CHALLENGE_INVITE_REQUEST_KEY = "challengeRequest"
        const val CHALLENGE_INVITE_REQUEST_PAYLOAD = "challengePayLoad"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_challenge_confour_participant, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.podium_choose_participants)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    private fun setup() {
        binding.actionNext.setOnClickListener {
            viewModel.sendChallengeParticipantInvite()
        }

        binding.actionChooseMore.setOnClickListener {
            val pId = viewModel.podiumId.value ?: return@setOnClickListener
            val action = PodiumChallengeParticipantFragmentDirections.actionPodiumConFourChallengeParticipantFragmentToChooseMoreParticipantFragment(pId, viewModel.challengeId.value, true)
            findNavController().navigateSafe(action)
        }
        initAdapter()
    }

    private fun observe() {
        viewModel.speakersForParticipant.observe(viewLifecycleOwner) {
            mAdapter?.apply {
                if (data.isEmpty() || it?.size == 0) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
            binding.multiStateView.viewState = if (it.isEmpty()) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
        }

        viewModel.challengeParticipantInviteSent.observe(viewLifecycleOwner) {
                setFragmentResult(
                    CHALLENGE_INVITE_REQUEST_KEY, bundleOf(
                        CHALLENGE_INVITE_REQUEST_PAYLOAD to it)
                    )
                findNavController().popBackStack(R.id.nav_challenge_setup, true)
        }

        viewModel.challengeParticipantsCount.observe(viewLifecycleOwner) {
            //Observe this for live data handling
        }
    }

    private fun initAdapter() {
        mAdapter = PodiumChallengeParticipantsQuickAdapter((object : PodiumChallengeParticipantsQuickAdapter.ItemListener {
            @SuppressLint("NotifyDataSetChanged")

            override fun toggleParticipantSelection(item: SelectableSpeakerUIModel) {
                viewModel.toggleParticipantSelection(item)
            }
        }), mutableListOf()).apply {
            animationEnable = false
            currentUserId = viewModel.user.id
            isAnimationFirstOnly = false
            setDiffCallback(PodiumChallengeParticipantsQuickAdapter.DiffCallback())
        }

        val layoutMan = LinearLayoutManager(context)
        binding.speakerList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }
    }

}