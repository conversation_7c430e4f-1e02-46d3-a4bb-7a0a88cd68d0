package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.view.LayoutInflater
import android.view.MenuItem
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.huddles.HuddleRequestsAdminActionRequest
import com.app.messej.data.model.api.huddles.HuddleRequestsResponse
import com.app.messej.databinding.ItemHuddleRequestsListBinding

class ManageRequestsListAdapter(private val mListener: ItemListener) : PagingDataAdapter<HuddleRequestsResponse.HuddleRequest, ManageRequestsListAdapter.HuddleRequestsListViewHolder>(StarsDiff) {

    interface ItemListener {
        fun onItemClick(item: HuddleRequestsResponse.HuddleRequest, position: Int, menuItem: MenuItem)
        fun onHuddleAction(item: HuddleRequestsResponse.HuddleRequest, action: HuddleRequestsAdminActionRequest.RequestsAdminAction)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        HuddleRequestsListViewHolder(
            ItemHuddleRequestsListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: HuddleRequestsListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }

    inner class HuddleRequestsListViewHolder(private val binding: ItemHuddleRequestsListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: HuddleRequestsResponse.HuddleRequest, position: Int) = with(binding) {
            request=item
            requestsActionButton.setOnClickListener {
                val popup = PopupMenu(it.context, it)
                popup.inflate(R.menu.menu_huddle_requests_action)
                popup.setOnMenuItemClickListener { menuItem ->
                    mListener.onItemClick(item, position, menuItem)
                    true
                }
                popup.show()
            }
            declineButton.setOnClickListener {
                mListener.onHuddleAction(item, HuddleRequestsAdminActionRequest.RequestsAdminAction.ADMIN_DECLINED)
            }
            blockButton.setOnClickListener {
                mListener.onHuddleAction(item, HuddleRequestsAdminActionRequest.RequestsAdminAction.BLOCK)
            }
            acceptButton.setOnClickListener {
                mListener.onHuddleAction(item, HuddleRequestsAdminActionRequest.RequestsAdminAction.ADMIN_ACCEPTED)
            }
        }
    }

    object StarsDiff : DiffUtil.ItemCallback<HuddleRequestsResponse.HuddleRequest>() {
        override fun areItemsTheSame(oldItem: HuddleRequestsResponse.HuddleRequest, newItem: HuddleRequestsResponse.HuddleRequest) =
            oldItem.memberId == newItem.memberId

        override fun areContentsTheSame(oldItem: HuddleRequestsResponse.HuddleRequest, newItem: HuddleRequestsResponse.HuddleRequest) =
            oldItem == newItem
    }
}