package com.app.messej.ui.auth.register

import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.model.enums.ProfileCheckpoint
import com.app.messej.databinding.FragmentRegisterEmailBinding
import com.app.messej.ui.auth.AuthOTPFragment
import com.app.messej.ui.common.PolicyDocumentFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe


class RegisterEmailFragment:Fragment() {

    private lateinit var binding: FragmentRegisterEmailBinding

    private val viewModel: RegisterMobileViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_email, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        viewModel.loggedInUser?.let { user ->
            when (user.profileCheckpoint) {
                ProfileCheckpoint.PROFILE_CHECKPOINT_NONE -> {}
                ProfileCheckpoint.PROFILE_CHECKPOINT_PASSWORD_SETUP -> findNavController().navigateSafe(
                    RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToCreatePasswordFragment(
                        user.countryCode?:"",
                        user.phone?:"", user.email?:"", OTPRequestMode.REGISTER_EMAIL)
                )
                ProfileCheckpoint.PROFILE_CHECKPOINT_PROFILE_SETUP -> findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToCreateProfileFragment())
                ProfileCheckpoint.PROFILE_CHECKPOINT_USERNAME_SETUP -> findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToCreateUsernameFragment())
//                ProfileCheckpoint.PROFILE_CHECKPOINT_LOCATION_SETUP -> findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToRegisterLocationFragment(false))
                ProfileCheckpoint.PROFILE_CHECKPOINT_SUPERSTAR_SETUP -> findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToRegisterSuperstarFragment())
                ProfileCheckpoint.PROFILE_CHECKPOINT_LOCK_SCREEN -> {}
                null -> {}
            }
        }
        binding.backToLoginButton.setOnClickListener {
            binding.textInputEmailReg.error = ""
            findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionGlobalNavGraphLogin())
        }

        binding.textInputEmailReg.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus) {
                    viewModel.didShowEmailError(true)
                }
                addTextChangedListener { _ ->
                    viewModel.didShowEmailError(false)
                   viewModel.checkEmailValidity()
                }
            }
        }
        binding.root.rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val r = Rect()
            binding.root.rootView.getWindowVisibleDisplayFrame(r)
            val screenHeight: Int = binding.root.rootView.height
            val keypadHeight: Int = screenHeight - r.bottom
            if (keypadHeight < screenHeight * 0.15) {
                viewModel.didShowEmailError(true)
            }
        }

        binding.tncButton.setOnClickListener {
            findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionGlobalPolicyFragment(DocumentType.TERMS_OF_USE, true))
        }


        binding.registrationNextButton.setOnClickListener {
            viewModel.checkEmailAvailability()
        }

        binding.root.apply {
            setOnTouchListener { v, _ ->
                viewModel.didShowEmailError(true)
                v.performClick()
            }
        }
    }

    private fun updateNextButton() {
        binding.registrationNextButton.isEnabled =
            (viewModel.mobileAvailabilityLoading.value == false) && (viewModel.emailStageValid.value==true)
    }

    private fun observe() {
        viewModel.mobileAvailabilityLoading.observe(viewLifecycleOwner) {
            updateNextButton()
        }

        viewModel.emailStageValid.observe(viewLifecycleOwner){
            updateNextButton()
        }

        viewModel.emailAvailabilityError.observe(viewLifecycleOwner) {
            updateEmailFieldError()
        }

        viewModel.showEmailInvalidError.observe(viewLifecycleOwner){
            updateEmailFieldError()
        }
        viewModel.onCheckEmailComplete.observe(viewLifecycleOwner){
            Log.d("TEST","Email id : ${viewModel.email.value}")
            val action = RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToRegisterMobileConfirmBottomSheetFragment("", "",viewModel.email.value!!)
            findNavController().navigateSafe(action)
        }

        //To listen tnc accept click from RegisterTermsFragment
        setFragmentResultListener(PolicyDocumentFragment.REGISTER_DOCUMENT_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(PolicyDocumentFragment.REGISTER_DOCUMENT_RESULT_KEY)
            viewModel.tncAccepted.postValue(result)
        }

        setFragmentResultListener(RegisterMobileConfirmFragment.MOBILE_CONFIRM_RESULT_CODE) { _, bundle ->
            val result = bundle.getBoolean(RegisterMobileConfirmFragment.MOBILE_CONFIRM_RESULT_KEY)
            if (result) {
                val action = RegisterMobileNumberFragmentDirections.actionGlobalAuthOTPFragment(OTPRequestMode.REGISTER_EMAIL, null, null, viewModel.email.value)
                findNavController().navigateSafe(action)
            }
        }

        setFragmentResultListener(AuthOTPFragment.OTP_REQUEST_KEY) { _, bundle ->
            val result = bundle.getString(AuthOTPFragment.OTP_RESULT_KEY)
            Log.d("NAVC", "observe: navigating to password")
            if (result == AuthOTPFragment.OTP_RESULT_SUCCESS) {
//                no need to navigate. onstart check will handle it anyway
//                findNavController().navigateSafe(RegisterMobileNumberFragmentDirections.actionRegistrationFragmentToCreatePasswordFragment(viewModel.countryCode.value!!,viewModel.phoneNumber.value!!))
            }
        }

    }

    private fun updateEmailFieldError() {
        binding.textInputEmailReg.isErrorEnabled=true
        binding.textInputEmailReg.error = if(!viewModel.emailAvailabilityError.value.isNullOrEmpty()) {
            binding.textInputEmailReg.isErrorEnabled=true
            viewModel.emailAvailabilityError.value
        } else if(viewModel.showEmailInvalidError.value==true){
            binding.textInputEmailReg.isErrorEnabled=true
            resources.getString(R.string.forgot_password_error_email)
        } else {
            binding.textInputEmailReg.isErrorEnabled = false
            null
        }
    }


    /* private fun updateEmailAvailabilityError() {
         binding.registrationEmailError.text=viewModel.emailAvailabilityError.value
     }*/

//    private val phoneNumberHintIntentResultLauncher =
//        registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
//            try {
//                val phoneNumber = Identity.getSignInClient(requireActivity()).getPhoneNumberFromIntent(result.data)
//                binding.registrationCountryPicker.fullNumber = phoneNumber
//            } catch(e: Exception) {
//                Log.e(Constants.FLASHAT_TAG, "Phone Number Hint failed")
//            }
//        }
//
//    private fun getPhoneNumberHint() {
//        if(viewModel.didTryPhoneNumberHint) return
//        viewModel.didTryPhoneNumberHint = true
//        val request: GetPhoneNumberHintIntentRequest = GetPhoneNumberHintIntentRequest.builder().build()
//
//        Identity.getSignInClient(requireActivity())
//            .getPhoneNumberHintIntent(request)
//            .addOnSuccessListener { result ->
//                try {
//                    phoneNumberHintIntentResultLauncher.launch(
//                        IntentSenderRequest.Builder(
//                            result.intentSender
//                        ).build()
//                    )
//                } catch(e: Exception) {
//                    Log.e(Constants.FLASHAT_TAG, "Launching the PendingIntent failed")
//                }
//            }
//            .addOnFailureListener {
//                Log.e(Constants.FLASHAT_TAG, "Phone Number Hint failed")
//            }
//    }

}