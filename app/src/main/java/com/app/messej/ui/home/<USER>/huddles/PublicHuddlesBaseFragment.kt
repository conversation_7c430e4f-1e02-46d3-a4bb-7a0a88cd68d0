package com.app.messej.ui.home.publictab.huddles

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.data.model.enums.HuddleTab
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.LayoutConfirmHuddlePurchaseBinding
import com.app.messej.databinding.LayoutPublicHuddlesBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureHuddlePostingAllowed
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.google.android.material.button.MaterialButton

abstract class PublicHuddlesBaseFragment : Fragment(), MenuProvider {

    protected abstract var binding: LayoutPublicHuddlesBinding

    protected val viewModel: PublicHuddlesViewModel by activityViewModels()

    private lateinit var mHuddlesPagerAdapter: FragmentStateAdapter

    private var dialogue: MaterialDialog?=null


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.w("MNUF", "PHF: addAsMenuHost")
        addAsMenuHost()
        setup()
        observe()
    }

    private fun setup() {
        mHuddlesPagerAdapter = object : FragmentStateAdapter(this) {
            override fun getItemCount(): Int = HuddleTab.values().size

            override fun createFragment(position: Int): Fragment {
                val tab = HuddleTab.values()[position]
                return PublicHuddlesInnerListFragment().apply {
                    arguments = PublicHuddlesInnerListFragment.getTabBundle(tab)
                }
            }
        }

        binding.huddlesPager.apply {
            isUserInputEnabled = false
            adapter = mHuddlesPagerAdapter
        }
        binding.btnMine.setOnClickListener {
            viewModel.setCurrentTab(HuddleTab.TAB_MINE)
            (it as MaterialButton).isChecked = true
        }
        binding.btnAdmin.setOnClickListener {
            viewModel.setCurrentTab(HuddleTab.TAB_ADMIN)
            (it as MaterialButton).isChecked = true
        }
        binding.btnJoined.setOnClickListener {
            viewModel.setCurrentTab(HuddleTab.TAB_JOINED)
            (it as MaterialButton).isChecked = true
        }
        binding.btnSuggested.setOnClickListener {
            viewModel.setCurrentTab(HuddleTab.TAB_SUGGESTED)
            (it as MaterialButton).isChecked = true
        }
        binding.createHuddleFab.setOnClickListener {
            checkUserInteractionAllowed()
        }
        viewModel.currentTab.value?.let {
            binding.huddlesPager.setCurrentItem(it.ordinal, false)
        }
    }

    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            Log.w("PHBF", "observe currentTab: $it")
            if (binding.huddlesPager.currentItem==it.ordinal) return@observe
            binding.huddlesPager.setCurrentItem(it.ordinal,false)
        }

        viewModel.showCreateHuddleFab.observe(viewLifecycleOwner) {
            binding.createHuddleFab.apply {
                if(it && !viewModel.isVisitor) show() else hide()
            }
        }
        viewModel.huddleEligibility.observe(viewLifecycleOwner) {it->
            it?:return@observe
            ensureHuddlePostingAllowed {
                if (it == true){
                    navigateToCreate()
                } else{
                    gotoCreateHuddlePurchaseAction(viewModel.user.citizenship)
                }
            }
        }

        viewModel.inSufficientBalance.observe(viewLifecycleOwner){
            showToast(getString(R.string.title_insufficient_points_balance_premium),Toast.LENGTH_SHORT)
        }
        viewModel.showSuccess.observe(viewLifecycleOwner){
            showToast(getString(R.string.huddle_creation_purchase_completed), Toast.LENGTH_SHORT)
            navigateToCreate()
        }
        viewModel.errorMessage.observe(viewLifecycleOwner){
            showToast(it,Toast.LENGTH_SHORT)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        Log.w("MNUF", "PHF: onCreateMenu")

       val menuData =  menuInflater.inflate(R.menu.menu_home_huddles,menu)
        menu.apply {
            findItem(R.id.action_more).isVisible = !viewModel.isVisitor
        }
        return menuData
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> {
                navigateToSearch()
            }
            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    private fun showMoreMenu(v: MenuItem) {
        val view = activity?.findViewById<View>(v.itemId)?: return
        val popup = PopupMenu(requireContext(),view)
        popup.menuInflater.inflate(R.menu.menu_home_huddles_more, popup.menu)
        popup.setForceShowIcon(true)
        popup.setOnMenuItemClickListener { menuItem ->
            when(menuItem.itemId) {
                R.id.action_create_huddle -> checkUserInteractionAllowed()
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    private fun checkUserInteractionAllowed() {
       viewModel.checkHuddleCreationLimit()
    }

    private fun gotoCreateHuddlePurchaseAction(citizenship: UserCitizenship) {
        dialogue = MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutConfirmHuddlePurchaseBinding>(layoutInflater, R.layout.layout_confirm_huddle_purchase, null, false)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)

            view.nickNameTitle.text = when(citizenship){
                UserCitizenship.RESIDENT ->{
                    getString(R.string.huddle_creation_limit_text_resident)
                }
                UserCitizenship.CITIZEN,UserCitizenship.GOLDEN ->{
                    getString(R.string.huddle_creation_limit_citizen)
                }
                else->{null}
            }
            view.actionConfirm.text = getString(R.string.huddle_creation_purchase_pay_create)
            view.actionCancel.text  = getString(R.string.huddle_creation_purchase_action_later)

            view.actionConfirm.setOnClickListener {
                viewModel.purchaseCreateHuddle()
                dismiss()
            }
            view.actionCancel.setOnClickListener {
                dismiss()

            }
        }
    }

    protected abstract fun navigateToSearch()
    protected abstract fun navigateToCreate()
}