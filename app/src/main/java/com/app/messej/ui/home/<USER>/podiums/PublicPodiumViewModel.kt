package com.app.messej.ui.home.publictab.podiums

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.podium.PodiumFriend
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.DataFormatHelper
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PublicPodiumViewModel(application: Application) : AndroidViewModel(application) {
    private val podiumRepository = PodiumRepository(application)
    private val profileRepo = ProfileRepository(application)


    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val _currentTab = MutableLiveData(PodiumTab.LIVE_PODIUM)
    val currentTab: LiveData<PodiumTab?> = _currentTab.distinctUntilChanged()

    protected val _dataLoading = MutableLiveData<Boolean>(true)
    val dataLoading: LiveData<Boolean> = _dataLoading

    private val _actionLoading = MutableLiveData<Boolean>(false)

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    val showCompactLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(_actionLoading.value==true||dataLoadingMore.value==true)
        }
        med.addSource(_actionLoading) { update() }
        med.addSource(dataLoadingMore) { update() }
        med
    }

    fun setCurrentTab(tab: PodiumTab, skipIfSet: Boolean = false) {
        Log.w("PHVM", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
    }

    val myPodiumsList = podiumRepository.getMyPodiumsListingPager().liveData.cachedIn(viewModelScope)

    private val _livePodiumCount = MutableLiveData<Int?>(null)
    val livePodiumCount: LiveData<String?> = _livePodiumCount.map {
        it?: return@map null
        DataFormatHelper.numberToK(it)
    }

    val livePodiumsList = podiumRepository.getLivePodiumsListingPager {
        _livePodiumCount.postValue(it)
    }.liveData.cachedIn(viewModelScope)

    val podiumInvitationDeclined = LiveEvent<Boolean>()
    val podiumRemoval = LiveEvent<Boolean>()
    fun declinePodiumInvitation(podiumId: String) {
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    when (podiumRepository.declinePodiumInvitation(podiumId)) {
                        is ResultOf.Success -> {
                            podiumInvitationDeclined.postValue(true)
                        }
                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }

                } catch (e: Exception) {
                    Log.d("PublicPodiumLVM", "declinePodiumInvitation: ${e.message}")
                }
            }
    }

    fun withDrawAsAdmin(id: Int,podiumId: String) {

            viewModelScope.launch(Dispatchers.IO) {
                try {
                    when (val result = podiumRepository.dismissAnAdmin(podiumId, id)) {
                        is ResultOf.Success -> {
                            podiumRemoval.postValue(true)
                        }

                        is ResultOf.APIError -> {
//                            podiumRemoval.postValue(true)
                        }
                        is ResultOf.Error -> {}
                    }

                } catch (e: Exception) {
                    Log.d("PodiumLVM", "dismissAsAdmin: erPodiumActionTyperor: ${e.message}")
                }

        }
    }

    val isAmbassadorOrMinister: Boolean
        get() = user.citizenship == UserCitizenship.AMBASSADOR || user.citizenship == UserCitizenship.MINISTER

    val iAmMinister:Boolean
        get() = user.citizenship == UserCitizenship.MINISTER


    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val _liveFriendsCount = MutableLiveData<Int?>(null)
    val liveFriendsCount: LiveData<String?> = _liveFriendsCount.map {
        it?: return@map null
        DataFormatHelper.numberToK(it)
    }
    val podiumLiveFriendsList = podiumRepository.getPodiumLiveFriendsPager{
            _liveFriendsCount.postValue(it)} .liveData.cachedIn(viewModelScope)

    val liveFriendsList: MediatorLiveData<PagingData<PodiumFriend>> by lazy {
        val med = MediatorLiveData<PagingData<PodiumFriend>>()
        fun update() {
            val data = podiumLiveFriendsList.value?.map { pc ->

                _nickNames.value?.findNickName(pc.userId!!)?.let {
                    Log.d("NICKKKKNAME","nick id: $it"+"  Org. name ${pc.name}")
                    pc.name = it
                }
                pc
            }
            med.postValue(data)
        }
        med.addSource(podiumLiveFriendsList) { update() }
        med.addSource(_nickNames) { update() }
        med
    }

    val onUnfollowedUser = LiveEvent<Boolean>()
    val onFollowedUser = LiveEvent<Boolean>()

     fun unFollowUser(id:Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (profileRepo.unFollowStar(id)) {
                is ResultOf.Success -> {
                    onUnfollowedUser.postValue(true)
                }

                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }
            }
        }
    }
    val podiumCloseReason = LiveEvent<String>()


    //close podium for empowered user and minister
    fun closePodium(podiumId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<APIResponse<String>> = podiumRepository.closePodium(podiumId)) {
                is ResultOf.Success -> {
                    podiumRemoval.postValue(true)
                }

                is ResultOf.APIError -> {
                    podiumCloseReason.postValue(result.error.message)
                }

                is ResultOf.Error -> {

                }
            }
        }
    }

    fun followUser(id: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            var user = profileRepo.getLocalOtherUserProfile(id).value
            if (user==null) {
                user = getUserProfile(id)?: return@launch
            }
            when (val result = profileRepo.followUser(user)) {
                is ResultOf.Success -> {
                    profileRepo.updateOtherUser(user.copy(isStar = true))
                    onFollowedUser.postValue(true)
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }

    private suspend fun getUserProfile(id: Int): OtherUser? {
        when (val result =profileRepo.getPublicUserDetails(id)) {
            is ResultOf.Success -> {
                return result.value
            }
            is ResultOf.APIError -> {
            }
            is ResultOf.Error -> {
            }
        }
        return null
    }



}