package com.app.messej.ui.chat

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.view.MenuProvider
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.FragmentPublicHuddlesChatAttachVideoBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.TextFormatUtils.enableTextFormatting

abstract class BaseVideoAttachFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentPublicHuddlesChatAttachVideoBinding

    abstract val viewModel: BaseChatViewModel

    abstract val destinationName: String?

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_public_huddles_chat_attach_video, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        binding.destination = destinationName
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(binding.customActionBar.toolbar)
            setHomeIcon(R.drawable.ic_close)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
//        return menuInflater.inflate(R.menu.menu_image_attach_preview,menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        return when (menuItem.itemId) {
            android.R.id.home -> {
                viewModel.clearChatMedia()
                return false
            }
            R.id.action_crop->{
                viewModel.cropAttachedMedia()
                return true
            }
            else -> false
        }
    }

    private fun setup() {
        binding.chatSendButton.setOnClickListener {
            releasePlayer()
            viewModel.prepareAndSendMessage()
        }
        if (viewModel.enableTextFormatting) {
            binding.chatInput.enableTextFormatting(requireActivity(),viewModel)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    private var player: ExoPlayer? = null

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    private fun observe() {
        viewModel.onMessageCreated.observe(viewLifecycleOwner) {
            findNavController().popBackStack()
        }
        viewModel.chatMedia.observe(viewLifecycleOwner) { media ->
            media?: return@observe
            if (media.mediaType != MediaType.VIDEO) return@observe
            setupPlayer(MediaItem.fromUri(media.path))
        }
        viewModel.videoIsEncoding.observe(viewLifecycleOwner) {
            if (it) {
                Log.d("ENCODE", "observe: FLAG_KEEP_SCREEN_ON enabled")
                activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                binding.playerView.apply {
                    hideController()
                    useController = false
                }
                player?.pause()
            } else {
                activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                binding.playerView.apply {
                    useController = true
                }
                Log.d("ENCODE", "observe: FLAG_KEEP_SCREEN_ON cleared")
            }
        }
        viewModel.chatMediaVideoProgress.observe(viewLifecycleOwner) { progress ->
            binding.encodeProgress.progress = progress
            if (progress == null || progress < 0) {
                binding.encodeProgress.text = resources.getString(R.string.chat_video_encoding)
            } else {
                binding.encodeProgress.text = resources.getString(R.string.chat_video_encoding_progress, progress)
            }
        }
    }

    private fun releasePlayer() {
        player?.apply {
            stop()
            release()
            player = null
        }
    }

    @androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
    private fun setupPlayer(med: MediaItem? = null) {
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build().apply {
                binding.playerView.player = this
                binding.playerView.findViewById<View>(androidx.media3.ui.R.id.exo_settings).isVisible = false
                // Prepare the player.
            }
        }
        player?.apply {
            clearMediaItems()
            med?.let {
                setMediaItem(it)
            }
            prepare()
//            play()
        }
    }
}