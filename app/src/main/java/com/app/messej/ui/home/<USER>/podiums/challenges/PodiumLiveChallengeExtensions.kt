package com.app.messej.ui.home.publictab.podiums.challenges

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.view.View
import android.view.ViewPropertyAnimator
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.isVisible
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

object PodiumLiveChallengeExtensions {

    suspend fun View.fadeOutIn(duration: Long = 400, updateUI: (outV: View, inV: View) -> Unit) {
        this.fadeOutIn(this,duration,updateUI)
    }
    suspend fun AppCompatTextView.transitionToText(message: String, duration: Long = 400) {
        this.fadeOutIn(duration) { outV, inV ->
            this.text = message
            this.isVisible = true
        }
    }

    suspend fun View.fadeOut(duration: Long = 400) {
        this.animate().apply {
            interpolator = AccelerateDecelerateInterpolator()
            setDuration(duration / 2)
            alpha(0f)
            start()
            awaitEnd()
        }
        this.isVisible = false
    }

    suspend fun View.fadeIn(duration: Long = 400) {
        this.isVisible = true
        this.animate().apply {
            interpolator = AccelerateDecelerateInterpolator()
            setDuration(duration / 2)
            alpha(1f)
            start()
            awaitEnd()
        }
    }

    suspend fun View.fadeOutIn(inView: View, duration: Long = 400, updateUI: suspend (outV: View, inV: View) -> Unit = { outV, inV ->
        outV.isVisible = false
        inV.isVisible = true
    }) {
        this.animate().apply {
            interpolator = AccelerateDecelerateInterpolator()
            setDuration(duration/2)
            alpha(0f)
            start()
            awaitEnd()
        }
        updateUI.invoke(this,inView)
        inView.animate().apply {
            interpolator = AccelerateDecelerateInterpolator()
            setDuration(duration/2)
            alpha(1f)
            start()
            awaitEnd()
        }
    }

    suspend fun View.fadeInOut(duration: Long = 400, updateUI: suspend (v: View) -> Unit = { _ -> }) {
        this.animate().apply {
            interpolator = AccelerateDecelerateInterpolator()
            setDuration(duration/2)
            alpha(1f)
            start()
            awaitEnd()
        }
        updateUI.invoke(this)
        this.animate().apply {
            interpolator = AccelerateDecelerateInterpolator()
            setDuration(duration/2)
            alpha(0f)
            start()
            awaitEnd()
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun ViewPropertyAnimator.awaitEnd() = suspendCancellableCoroutine<Unit> { cont ->
        // Add an invokeOnCancellation listener. If the coroutine is
        // cancelled, cancel the animation too that will notify
        // listener's onAnimationCancel() function
        cont.invokeOnCancellation { cancel() }

        setListener(object : AnimatorListener {
            private var endedSuccessfully = true

            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationCancel(animation: Animator) {
                // Animator has been cancelled, so flip the success flag
                endedSuccessfully = false
            }

            override fun onAnimationRepeat(animation: Animator) {
            }

            override fun onAnimationEnd(animation: Animator) {
                // Make sure we remove the listener so we don't keep
                // leak the coroutine continuation
                animation.removeListener(this)

                if (cont.isActive) {
                    // If the coroutine is still active...
                    if (endedSuccessfully) {
                        // ...and the Animator ended successfully, resume the coroutine
                        cont.resume(Unit)
                    } else {
                        // ...and the Animator was cancelled, cancel the coroutine too
                        cont.cancel()
                    }
                }
            }
        })
    }
}