package com.app.messej.ui.auth.common

import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.text.style.CharacterStyle
import android.text.style.StyleSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterSearchLocationBinding
import com.app.messej.databinding.ItemSearchLocationBinding
import com.app.messej.ui.utils.MapUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.google.android.libraries.places.api.Places
import com.google.android.libraries.places.api.model.AutocompletePrediction
import com.google.android.libraries.places.api.net.FindAutocompletePredictionsRequest
import com.google.android.libraries.places.api.net.PlacesClient


class LocationSearchFragment : Fragment() {

    private lateinit var binding: FragmentRegisterSearchLocationBinding

    private val viewModel: LocationSearchViewModel by viewModels()

    private var mAdapter: LocationSearchQuickAdapter? = null
    private lateinit var placesClient: PlacesClient

    companion object {
        const val LOCATION_SEARCH_REQUEST_KEY = "locationSearchKey"
        const val LOCATION_SEARCH_RESULT_KEY = "locationSearchResultKey"
        const val LOCATION_SEARCH_RESULT_CURRENT = "currentLocationKey"

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_search_location, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        MapUtils.initPlacesClient()
        placesClient = Places.createClient(this.requireContext())
    }

    private fun setup() {
        initAdapter()
        binding.textInputSearch.editText?.requestFocus()
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(binding.textInputSearch.editText, InputMethodManager.SHOW_IMPLICIT)

        binding.currentLocationLayout.setOnClickListener {
            findNavController().popBackStack()
            setFragmentResult(LOCATION_SEARCH_REQUEST_KEY, bundleOf(LOCATION_SEARCH_RESULT_KEY to LOCATION_SEARCH_RESULT_CURRENT))
        }

        binding.textInputSearch.setStartIconOnClickListener { findNavController().popBackStack() }
    }

    private fun observe() {
        viewModel.debouncedSearchKeyword.observe(viewLifecycleOwner) {
            val request = FindAutocompletePredictionsRequest.builder()
                .setQuery(it.orEmpty()) // Replace with your input
                .build()

            MapUtils.initPlacesClient()
            placesClient.findAutocompletePredictions(request)
                .addOnSuccessListener { response ->
                    viewModel.updateSearchResults(response.autocompletePredictions)
                }
                .addOnFailureListener { exception ->
                    Log.e("Places", "Autocomplete failed: ${exception.message}")
                }
        }

        viewModel.suggestions.observe(viewLifecycleOwner){
            mAdapter?.apply {
                Log.d("SUG", "observe: list go ${data.size} to ${it?.size}")
                if (data.isEmpty() || it.isEmpty()) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }
    }

    private fun initAdapter() {
//        if(mAdapter != null) {
//            return
//        }
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = LocationSearchQuickAdapter(mutableListOf())

        val layoutMan = LinearLayoutManager(context)

        binding.locationSearchList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }
        mAdapter!!.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true

            setOnItemClickListener{ adapter, view, position ->
                val location = (adapter as LocationSearchQuickAdapter).data[position].placeId
                findNavController().popBackStack()
                setFragmentResult(LOCATION_SEARCH_REQUEST_KEY, bundleOf(LOCATION_SEARCH_RESULT_KEY to location))
            }
            setDiffCallback(LocationSearchQuickAdapter.DiffCallback())
        }
    }

    data class LocationResult(
        val primaryText: String,
        val secondaryText: String
    ) {
        companion object {
            fun from(prediction: AutocompletePrediction): LocationResult {
                val boldStyle: CharacterStyle = StyleSpan(Typeface.BOLD)
                return LocationResult(
                    prediction.getPrimaryText(boldStyle).toString(),
                    prediction.getSecondaryText(boldStyle).toString()
                )
            }
        }
    }

    class LocationSearchQuickAdapter(data: MutableList<AutocompletePrediction>): BaseQuickAdapter<AutocompletePrediction, BaseDataBindingHolder<ItemSearchLocationBinding>>(R.layout.item_search_location, data) {
        override fun convert(holder: BaseDataBindingHolder<ItemSearchLocationBinding>, item: AutocompletePrediction) {
            holder.dataBinding?.apply {
                locationItem = LocationResult.from(item)
            }
        }
        class DiffCallback: DiffUtil.ItemCallback<AutocompletePrediction>() {
            override fun areItemsTheSame(oldItem: AutocompletePrediction,newItem: AutocompletePrediction): Boolean {
                return oldItem.placeId == newItem.placeId
            }
            override fun areContentsTheSame(oldItem: AutocompletePrediction, newItem: AutocompletePrediction): Boolean {
                return true
            }
        }
    }

}