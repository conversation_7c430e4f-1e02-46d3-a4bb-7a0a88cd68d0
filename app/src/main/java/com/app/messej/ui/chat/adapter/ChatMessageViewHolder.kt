package com.app.messej.ui.chat.adapter

import android.content.Context
import android.content.res.ColorStateList
import android.text.SpannableString
import android.text.style.BackgroundColorSpan
import android.util.Log
import android.view.HapticFeedbackConstants
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.annotation.ColorRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.media3.common.Player
import com.aitsuki.swipe.SwipeLayout
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.enums.ChatBubbleMediaState
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.utils.MediaResolution
import com.app.messej.databinding.ItemChatMessageMediaAudioBinding
import com.app.messej.databinding.ItemChatMessageMediaDocumentBinding
import com.app.messej.databinding.ItemChatMessageMediaImageBinding
import com.app.messej.databinding.ItemChatMessageMediaLocationBinding
import com.app.messej.databinding.ItemChatMessageMediaStickerBinding
import com.app.messej.databinding.ItemChatMessageMediaVideoBinding
import com.app.messej.databinding.ItemChatMessageReplyToBinding
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.LocaleUtil
import com.app.messej.ui.utils.TextFormatUtils.applyMarkdownFormatting
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.bumptech.glide.Glide
import com.google.android.material.slider.Slider


abstract class ChatMessageViewHolder(view: View, protected val userId: Int, private val allowReplySwipe: Boolean, private var mListener: ChatAdapter.ChatClickListener) : ChatAdapter.ChatViewHolder(view), ChatAdapter.ChatHighlightViewProvider {

    companion object {

        const val IMAGE_ASPECT_DEFAULT = 1.0f
        const val IMAGE_ASPECT_MIN = 0.8f
        const val IMAGE_ASPECT_MAX = 2.0f

        fun getChatHighlightColor(color: Int): Int {
            return when (color) {
                R.color.colorChatDefault -> R.color.colorChatDefaultHighlight
                R.color.colorChatOutgoing -> R.color.colorChatOutgoingHighlight
                R.color.colorChatAdmin -> R.color.colorChatAdminHighlight
                R.color.colorChatPremium -> R.color.colorChatPremiumHighlight
                else -> R.color.colorPrimary
            }
        }
        fun getChatHighlightDarkColor(color: Int): Int {
            return when (color) {
                R.color.colorChatDefault -> R.color.colorChatDefaultHighlightDark
                R.color.colorChatOutgoing -> R.color.colorChatOutgoingHighlightDark
                R.color.colorChatAdmin -> R.color.colorChatAdminHighlightDark
                R.color.colorChatPremium -> R.color.colorChatPremiumHighlightDark
                else -> R.color.colorPrimary
            }
        }

        fun formatReplyText(c: Context, reply: ReplyTo): SpannableString? {
            val messageSpan = reply.message?.applyMarkdownFormatting(c)
            return messageSpan
        }

    }

    protected var imageBinding: ItemChatMessageMediaImageBinding? = null
    protected var audioBinding: ItemChatMessageMediaAudioBinding? = null
    protected var videoBinding: ItemChatMessageMediaVideoBinding? = null
    protected var docBinding: ItemChatMessageMediaDocumentBinding? = null
    protected var locationBinding: ItemChatMessageMediaLocationBinding? = null
    protected var stickerBinding: ItemChatMessageMediaStickerBinding? = null

    protected var mediaMessageId: String? = null

    protected open var replyBinding: ItemChatMessageReplyToBinding? = null

    protected var swipeListener: SwipeLayout.Listener? = null

    @CallSuper
    override fun bind(item: ChatMessageUIModel) {
        mediaMessageId = null
    }

    @CallSuper
    override fun cleanup() {
        super.cleanup()
        Log.d("VPLAY", "sub cleanup - $mediaMessageId")
        mediaMessageId?.let {
            mListener.onViewHolderCleanup(it)
            videoBinding?.playerView?.player = null
            mediaMessageId = null
        }
    }

    protected fun setSwipeListener(layout: SwipeLayout, item: AbstractChatMessage, pos: Int) {
        layout.swipeFlags = if (allowReplySwipe && item.sendStatus==AbstractChatMessage.SendStatus.NONE) (SwipeLayout.LEFT or SwipeLayout.RIGHT) else 0
        swipeListener?.let {
            layout.removeListener(it)
            swipeListener = null
        }
        val listener = object : SwipeLayout.Listener {
            override fun onMenuOpened(menuView: View) {
                layout.closeMenu(true)
                mListener.onItemReply(item, pos)
                menuView.performHapticFeedback(HapticFeedbackConstants.CONTEXT_CLICK)
            }

            override fun onSwipeStateChanged(menuView: View, newState: Int) {
                super.onSwipeStateChanged(menuView, newState)
                //To handle the edge condition of the swipe layout( getting opened and then getting stuck)
                if(newState == SwipeLayout.STATE_IDLE && (layout.isEndMenuOpened() || layout.isStartMenuOpened())) {
                    layout.closeMenu(true)
                }
            }
        }
        layout.addListener(listener)
        swipeListener = listener
    }

    protected open fun formatAndHighlightText(c: Context, msg: ChatMessageUIModel.ChatMessageModel): SpannableString? {
        var messageSpan = msg.message.displayMessage?.applyMarkdownFormatting(c)
        val highlight = msg.highlightMatches
        if (highlight!=null && messageSpan!=null) {
            messageSpan = messageSpan.highlightOccurrences(highlight){BackgroundColorSpan(ContextCompat.getColor(c, R.color.shimmerColorOnPrimary))}
        }
        return messageSpan
    }

    protected open fun loadMedia(mediaHolder: ViewGroup, cm: ChatMessageUIModel.ChatMessageModel, @ColorRes bubbleColor: Int) {
        if (cm.message.hasSticker) {
            if (stickerBinding == null) {
                mediaHolder.removeAllViews()
                val ib = ItemChatMessageMediaStickerBinding.inflate(LayoutInflater.from(mediaHolder.context), mediaHolder, false)
                mediaHolder.addView(ib.root)
                mediaHolder.visibility = View.VISIBLE
                stickerBinding = ib
            }
            stickerBinding?.let {
                it.sticker = cm.message.media
            }
        } else if (cm.message.hasMedia) {
            mediaMessageId = cm.message.messageId
            when (cm.message.mediaMeta?.mediaType) {
                MediaType.IMAGE -> {
                    if (imageBinding == null) {
                        mediaHolder.removeAllViews()
                        val ib = ItemChatMessageMediaImageBinding.inflate(LayoutInflater.from(mediaHolder.context), mediaHolder, false)
                        mediaHolder.addView(ib.root)
                        mediaHolder.visibility = View.VISIBLE
                        imageBinding = ib
                    }
                    imageBinding?.let { loadMediaImage(it, cm, layoutPosition) }

                }
                MediaType.AUDIO -> {
                    if (audioBinding == null) {
                        mediaHolder.removeAllViews()
                        val ab = ItemChatMessageMediaAudioBinding.inflate(LayoutInflater.from(mediaHolder.context), mediaHolder, false)
                        mediaHolder.addView(ab.root)
                        mediaHolder.visibility = View.VISIBLE
                        audioBinding = ab
                    }
                    audioBinding?.let { loadMediaAudio(it, cm, bubbleColor) }
                }
                MediaType.VIDEO -> {
                    if (videoBinding == null) {
                        mediaHolder.removeAllViews()
                        val ib = ItemChatMessageMediaVideoBinding.inflate(LayoutInflater.from(mediaHolder.context), mediaHolder, false)
                        mediaHolder.addView(ib.root)
                        mediaHolder.visibility = View.VISIBLE
                        videoBinding = ib
                    }
                    videoBinding?.let { loadMediaVideo(it, cm) }
                }
                MediaType.DOCUMENT -> {
                    if (docBinding == null) {
                        mediaHolder.removeAllViews()
                        val ib = ItemChatMessageMediaDocumentBinding.inflate(LayoutInflater.from(mediaHolder.context), mediaHolder, false)
                        mediaHolder.addView(ib.root)
                        mediaHolder.visibility = View.VISIBLE
                        docBinding = ib
                    }
                    docBinding?.let { loadMediaDocument(it, cm, bubbleColor) }
                }
                else -> {}
            }
        } else if (cm.message.hasLocation) {
            if (locationBinding == null) {
                mediaHolder.removeAllViews()
                val ib = ItemChatMessageMediaLocationBinding.inflate(LayoutInflater.from(mediaHolder.context), mediaHolder, false)
                mediaHolder.addView(ib.root)
                mediaHolder.visibility = View.VISIBLE
                locationBinding = ib
            }
            locationBinding?.let { loadMediaLocation(it, cm, layoutPosition) }
        }
    }

    protected open fun loadMediaVideo(binding: ItemChatMessageMediaVideoBinding, msg: ChatMessageUIModel.ChatMessageModel) {
        val meta = msg.message.mediaMeta!!
        val ratio = meta.imageWidth / meta.imageHeight

        binding.chatVideoThumbnail.apply {
            (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = ratio.coerceIn(IMAGE_ASPECT_MIN, IMAGE_ASPECT_MAX).toString()
            Glide.with(context).load(meta.thumbnail).placeholder(R.drawable.im_chat_image_placeholder).error(R.drawable.im_chat_image_placeholder)
                .into(this)
        }
        binding.playerView.apply {
            (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = ratio.coerceIn(IMAGE_ASPECT_MIN, IMAGE_ASPECT_MAX).toString()
        }
        Log.w("VDOWNLOAD", "loadMediaVideo: transfer - ${msg.ongoingTransfer}")
        Log.d("VPLAY", "loadMediaVideo: meta - $meta")
        Log.d("VPLAY", "loadMediaVideo: playerInfo - ${msg.playerInfo}")
        binding.meta = meta

        binding.transfer = null

        binding.chatVideoThumbnail.apply {
            setOnClickListener {
                mListener.onItemClick(msg.message, layoutPosition)
            }
            setOnLongClickListener {
                mListener.onItemLongClick(msg.message, layoutPosition)
                true
            }
        }

        binding.playButton.setOnClickListener {
            mListener.onMediaVideoPlay(binding.playerView,msg.chat,layoutPosition)
            binding.mediaState = ChatBubbleMediaState.PLAYING
            binding.playButton.bringToFront()
            binding.playerView.player?.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(@Player.State state: Int) {
                    when (state) {
                        Player.STATE_READY -> {
                        }
                        Player.STATE_BUFFERING -> {
                        }
                        Player.STATE_IDLE -> {
                            //To handle player idle state, eg: When player get released
                            binding.mediaState = ChatBubbleMediaState.LOCAL
                        }
                        Player.STATE_ENDED -> {
                        }
                        else -> {
                        }
                    }
                }
            })
        }

        msg.chat.offlineMedia?.let { med ->

            when (val state: MediaUploadState = med.uploadState) {
                is MediaUploadState.None -> {
                    binding.mediaState = ChatBubbleMediaState.LOCAL
                }
                is MediaUploadState.Pending -> {
                    binding.mediaState = ChatBubbleMediaState.UPLOAD
                    binding.uploadButton.setOnClickListener { mListener.onMediaUpload(msg.chat, layoutPosition) }
                }
                is MediaUploadState.Uploading -> {
                    Log.d("ENCODE", "setting upload progress: ${state.progress}")
                    binding.mediaState = ChatBubbleMediaState.UPLOADING
                    binding.transfer = msg.ongoingTransfer
                }
            }
            return
        }

        binding.mediaState = if(msg.isTransferringMedia) {
            Log.d("VDOWNLOAD", "setProgress: ${msg.ongoingTransfer?.progress}")
            binding.transfer = msg.ongoingTransfer
            ChatBubbleMediaState.DOWNLOADING
        } else {
            binding.downloadButton.setOnClickListener {
                mListener.onMediaDownload(it, msg.chat, layoutPosition)
            }
            ChatBubbleMediaState.DOWNLOAD
        }

        // check if video is playing currently
        if (msg.playerInfo?.active==true) {
            binding.mediaState = ChatBubbleMediaState.PLAYING
        }
    }

    protected open fun loadMediaImage(binding: ItemChatMessageMediaImageBinding, msg: ChatMessageUIModel.ChatMessageModel, pos: Int) {
        val meta = msg.message.mediaMeta!!
        val ratio = meta.imageWidth / meta.imageHeight

        msg.chat.offlineMedia?.let { med ->
            binding.showLoading = false
            binding.loadProgress = -1
            binding.showDownload = false
            binding.showUpload = false
            binding.chatImage.apply {
                (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = ratio.coerceIn(IMAGE_ASPECT_MIN, IMAGE_ASPECT_MAX).toString()
                Glide.with(context).load(med.path).placeholder(R.drawable.im_chat_image_placeholder).error(R.drawable.im_chat_image_placeholder).into(this)
            }
            binding.chatImage.apply {
                setOnClickListener {
                    mListener.onMediaImageTap(it, msg.chat, pos)
                }
                setOnLongClickListener {
                    mListener.onItemLongClick(msg.message, pos)
                    true
                }
            }
            when (val state: MediaUploadState = med.uploadState) {
                is MediaUploadState.None -> {
                    binding.showUpload = false
                }
                is MediaUploadState.Pending -> {
                    binding.showUpload = true
                    binding.uploadButton.setOnClickListener { mListener.onMediaUpload(msg.chat, pos) }
                }
                is MediaUploadState.Uploading -> {
                    binding.showUpload = true
                    binding.showLoading = true
                    binding.loadProgress = -1
                }
            }
            return
        }
        binding.chatImage.apply {
            binding.showLoading = msg.isTransferringMedia
            binding.loadProgress = -1
            binding.showDownload = true
            (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = ratio.coerceIn(IMAGE_ASPECT_MIN, IMAGE_ASPECT_MAX).toString()
            Glide.with(context).load(meta.thumbnail).placeholder(R.drawable.im_chat_image_placeholder).error(R.drawable.im_chat_image_placeholder)
                .into(this)
        }
        binding.chatImage.apply {
            setOnClickListener { mListener.onMediaDownload(it, msg.chat, pos) }
            setOnLongClickListener {
                mListener.onItemLongClick(msg.message, pos)
                true
            }
        }
    }

    protected open fun loadMediaAudio(binding: ItemChatMessageMediaAudioBinding, msg: ChatMessageUIModel.ChatMessageModel,@ColorRes bubbleColor: Int) {
        var mediaDownloaded = false
        msg.chat.offlineMedia?.let {
            mediaDownloaded = true
        }
        binding.apply {
            downloading = !mediaDownloaded && msg.isTransferringMedia
            downloaded = mediaDownloaded
            this.info = msg.playerInfo
            this.meta = msg.message.mediaMeta
            Log.d("mediaMETA", "loadMediaAudio: ${msg.message.mediaMeta?.isPlayed}")

            downloadButton.setOnClickListener { mListener.onMediaDownload(it, msg.chat, layoutPosition) }
            audioPlayButton.setOnClickListener {
                if (!mediaDownloaded) return@setOnClickListener
                mListener.onMediaAudioPlay(it, msg.chat, layoutPosition)
            }

//            audioSpeedButton.backgroundTintList = highlight
            audioSpeedButton.setOnClickListener {
                msg.playerInfo?: return@setOnClickListener
                mListener.onMediaAudioToggleSpeed(it, msg.chat, layoutPosition)
            }

            mediaSlider.clearOnSliderTouchListeners()
            if(mediaDownloaded) {
                mediaSlider.addOnSliderTouchListener(object : Slider.OnSliderTouchListener {
                    override fun onStartTrackingTouch(slider: Slider) {}
                    override fun onStopTrackingTouch(slider: Slider) {
                        mListener.onMediaAudioSeek(slider, msg.chat, layoutPosition, slider.value)
                    }
                })
            }
        }
    }

    protected open fun loadMediaDocument(binding: ItemChatMessageMediaDocumentBinding, msg: ChatMessageUIModel.ChatMessageModel,@ColorRes bubbleColor: Int) {
        val mediaDownloaded = msg.chat.offlineMedia!=null
        val mediaDownloading = !mediaDownloaded && msg.isTransferringMedia
        binding.apply {
            downloading = mediaDownloading
            downloaded = mediaDownloaded
            this.meta = msg.message.mediaMeta

            val highlight = ContextCompat.getColor(root.context, getChatHighlightColor(bubbleColor))
            docLayout.setCardBackgroundColor(highlight)

            downloadButton.setOnClickListener { mListener.onMediaDownload(it, msg.chat, layoutPosition) }
            docLayout.setOnClickListener {
                if (mediaDownloading) {
                    // Do Nothing
                } else if (mediaDownloaded) {
                    mListener.onMediaDocumentTap(it, msg.chat, layoutPosition)
                } else {
                    mListener.onMediaDownload(it, msg.chat, layoutPosition)
                }
            }

            msg.chat.offlineMedia?.let { med ->

                when (val state: MediaUploadState = med.uploadState) {
                    is MediaUploadState.None -> {
                        binding.mediaState = ChatBubbleMediaState.LOCAL
                    }
                    is MediaUploadState.Pending -> {
                        binding.mediaState = ChatBubbleMediaState.UPLOAD
                    }
                    is MediaUploadState.Uploading -> {
                        Log.d("ENCODE", "setting upload progress: ${state.progress}")
                        binding.mediaState = ChatBubbleMediaState.UPLOADING
                        binding.transfer = msg.ongoingTransfer
                    }
                }
                return
            }

            binding.mediaState = if(msg.isTransferringMedia) {
                Log.d("VDOWNLOAD", "setProgress: ${msg.ongoingTransfer?.progress}")
                binding.transfer = msg.ongoingTransfer
                ChatBubbleMediaState.DOWNLOADING
            } else {
                binding.downloadButton.setOnClickListener {
                    mListener.onMediaDownload(it, msg.chat, layoutPosition)
                }
                ChatBubbleMediaState.DOWNLOAD
            }
        }
    }

    protected open fun loadMediaLocation(binding: ItemChatMessageMediaLocationBinding, msg: ChatMessageUIModel.ChatMessageModel, pos: Int) {
        msg.message.attachedLocation?.let { loc ->
            binding.chatLocation.apply {
                setOnClickListener {
                    mListener.onMediaLocationTap(it, msg.chat, pos)
                }
                setOnLongClickListener {
                    mListener.onItemLongClick(msg.message, pos)
                    true
                }
                this.post {
                    val ratio = 1.8f
                    (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = ratio.toString()
                    val calcHeight = width.toFloat()/ratio
                    val res = MediaResolution(width,calcHeight.toInt())
                    Log.w("LML", "loadMediaLocation: $res | density: ${context.resources.displayMetrics.density}" )
                    val url = LocaleUtil.getGoogleMapEmbedURL(loc.latitude,loc.longitude, res, context.resources.displayMetrics.density>=2f)
                    Log.w("LML", "loadMediaLocation: $url" )
                    Glide.with(context).load(url)
                        .placeholder(R.drawable.im_chat_location_placeholder)
                        .error(R.drawable.im_chat_location_placeholder)
                        .into(this)
                }
            }
        }
    }

    protected open fun loadReply(replyHolder: ViewGroup, cm: ChatMessageUIModel.ChatMessageModel,@ColorRes bubbleColor: Int) {
        replyHolder.visibility = if (cm.message.hasReply) View.VISIBLE else View.GONE
        cm.message.replyTo?.let { reply ->
            if (replyBinding == null) {
                replyHolder.removeAllViews()
                val ab = ItemChatMessageReplyToBinding.inflate(LayoutInflater.from(replyHolder.context), replyHolder, false)
                replyHolder.addView(ab.root)
                replyBinding = ab
            }
            replyBinding?.apply {
                cancellable = false
                replyTo = reply
                isOwnMessage = userId == reply.senderId
                prepare(reply,bubbleColor)
                replyLayout.isClickable = true
                replyLayout.setOnClickListener { mListener.onItemReplyClick(cm.message,layoutPosition) }
            }
        }
    }

    protected open fun setChatBubbleColor(chatBubble: View, cm: ChatMessageUIModel.ChatMessageModel): Int {
        val outgoing: Boolean = cm.message.belongsToUser(userId)
        var color = R.color.colorChatDefault
        if (outgoing) {
            color = R.color.colorChatOutgoing
            when (val msg = cm.message) {
                is BroadcastMessage -> {
                    color = R.color.colorChatDefault
                }
            }
        }
        else {
            when (val msg = cm.message) {
                is HuddleChatMessage -> {
                    color = if (listOf(UserRole.ADMIN, UserRole.MANAGER).contains(msg.senderDetails?.role)) {
                        R.color.colorChatAdmin
                    } else if (msg.senderDetails?.premium == true) {
                        R.color.colorChatPremium
                    } else {
                        R.color.colorChatDefault
                    }
                }
                is PrivateChatMessage -> {
                    color = R.color.colorChatDefault
                }
                is BroadcastMessage -> {
                    color = R.color.colorChatDefault
                }
            }
        }
        chatBubble.backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(chatBubble.context, color))
        return color
    }
}