package com.app.messej.ui.home.publictab.huddles.chat

import android.app.Application
import android.net.Uri
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.app.messej.ConnectivityObserver
import com.app.messej.NetworkConnectivityObserver
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AttachLocation
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.MediaPlayerInfo
import com.app.messej.data.model.MediaPlayerInfo.Companion.LIST_POS_PINNED
import com.app.messej.data.model.MediaPlayerInfo.Companion.LIST_POS_RECORDING
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.api.huddles.HuddleMentionableUsersResponse
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.api.poll.PollResponse
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.ChatTextColor.Companion.orDefault
import com.app.messej.data.model.enums.GroupChatStatus.ACTIVE
import com.app.messej.data.model.enums.GroupChatStatus.ADMIN_BLOCKED
import com.app.messej.data.model.enums.GroupChatStatus.INVITED
import com.app.messej.data.model.enums.GroupChatStatus.OPEN_TO_JOIN
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.LegalAffairPermissionSource
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.repository.LegalAffairsRepository
import com.app.messej.data.repository.PollsRepository
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DDMMYYYY_DASHED
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.ui.chat.ChatMessageUIModel
import com.app.messej.ui.chat.GroupChatViewModel
import com.app.messej.ui.legal.report.ReportUtils.canReport
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.time.ZoneId
import java.time.ZonedDateTime

open class HuddleChatViewModel(application: Application) : GroupChatViewModel(application) {

    private val connectionStatus = NetworkConnectivityObserver.connectionStatusFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    private val legalAffairsRepo = LegalAffairsRepository(application)
    private val flashRepo = FlashRepository(application)

    private var currentDateZonedTime = ZonedDateTime.now(ZoneId.systemDefault())
    private val currentDate = DateTimeUtils.format(currentDateZonedTime, FORMAT_DDMMYYYY_DASHED)

    fun setHuddleId(id: Int, expectedType: HuddleType? = null, navigateToMsgId: String?) {
        _dataLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            // load from local DB - set [_dataLoading] to false and load API anyway
            // if it fails, load from api
            loadHuddleInfo(id, expectedType)
            navigateToMsgId?.let { msgId ->
                cachedHuddle?.let { huddle ->
                    if (huddle.huddleStatus == ACTIVE && pendingCommentNavigation !=msgId) {
                        huddleRepo.getHuddleChatWithMedia(msgId)?.let {
                            onNavigateToPostComments.postValue(msgId)
                            pendingCommentNavigation=msgId
                        }
                    }
                }
            }
            _dataLoading.postValue(false)
            getPollInfo(id)
        }
    }

    val userEmpowerment = user.userEmpowerment?: CurrentUser.UserEmpowerment()

    val huddlePrivacy = huddle.map {
        it?.privacy
    }.distinctUntilChanged()

    private var currentlyTransferringMedia: OfflineMedia? = null

    val mediaTransfer: LiveData<MediaTransfer?> = MediaUploadListener.uploadProgressFlow.map {
        val id = currentlyTransferringMedia?.messageId?: return@map null
        it[id]?.let { progress ->
            return@map MediaTransfer(id).apply {
                this.progress = progress
            }
        }
        return@map null

    }.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val newPosts: MutableLiveData<MutableSet<String>> = MutableLiveData(mutableSetOf ())
    val newPostCounter: LiveData<Int> = newPosts.map { it.size }
    val newActivityReceived = LiveEvent<Boolean>()
    private val _isImageEditMode = MutableLiveData(false)
    val isImageEditMode: LiveData<Boolean?> = _isImageEditMode


    override val canDeleteSelectionForEveryone = _selectedChats.map {
        return@map if (huddle.value?.role == UserRole.MANAGER || userEmpowerment.canDeleteAnyHuddlePost) {
            Log.w("BCVM", "delete timeout: admin")
            true
        } else {
            val timeout = deleteTimeout.value
            Log.w("BCVM","delete timeout: $timeout")
            it.all { ch ->
                Log.w("BCVM","delete timeout: age:  ${DateTimeUtils.durationToNowFromPast(ch.parsedCreatedTime)?.seconds}")
                val hasTimedOut = if (timeout==null || timeout<=0) false
                else (DateTimeUtils.durationToNowFromPast(ch.parsedCreatedTime)?.seconds ?: 0) > timeout
                !hasTimedOut && ch.sender == user.id
            }
        }
    }

    override val canDeleteSelection by lazy { canDeleteSelectionForEveryone }


    override val showChats = huddleStatus.switchMap {status ->
            if (userEmpowerment.canEnterAnyHuddle && status != ADMIN_BLOCKED) MutableLiveData(true)
            else if (status == ADMIN_BLOCKED) MutableLiveData(false)
            else super.showChats
    }

    init {
        viewModelScope.launch {
            try {
                eventRepo.newPostFlow.filter { it.first == huddleID.value }.collect {
                    val set = newPosts.value?: mutableSetOf()
                    set.add(it.second)
                    newPosts.postValue(set)
                }
            } finally { }
        }


        viewModelScope.launch {
            try {
                eventRepo.pollPayloadFlow.filter { it.pollId == poll.value?.id }.collect {payload->
                    val huddleAnswers = payload.answers
                    val updatedAnswers = poll.value?.answers?.map { pollAnswer ->
                        huddleAnswers.find { it.id == pollAnswer.id }?.let { huddleAnswer ->
                            pollAnswer.copy(
                                answer = huddleAnswer.answer,
                                answerPercentage = huddleAnswer.answerPercentage,
                                timeCreated = huddleAnswer.timeCreated,
                                timeUpdated = huddleAnswer.timeUpdated
                            )
                        } ?: pollAnswer
                    }
                    if(!updatedAnswers.isNullOrEmpty()){
                        _poll.postValue(
                            _poll.value?.copy(
                                answers = updatedAnswers
                            )
                        )
                    }

                }
            } finally { }
        }

        viewModelScope.launch {
            try {
                eventRepo.newActivityFlow.filter { it.first == huddleID.value }.collect {
                    newActivityReceived.postValue(true)
                }
            } finally { }
        }


        viewModelScope.launch {
            try {
                eventRepo.removedFromHuddleFlow.filter { it.huddleId == huddleID.value }.collect {
                    //Handle removed from huddle case
                    setHuddleId(it.huddleId, expectedType = if(it.private) HuddleType.PRIVATE else HuddleType.PUBLIC)
                }
            } finally { }
        }

        viewModelScope.launch {
            try {
                eventRepo.huddleDeletedFlow.filter { it == huddleID.value }.collect {
                    //Handle huddle deleted case
                    onExitHuddle.postValue(true)
                }
        } finally { }
        }

        viewModelScope.launch {
            try {
                eventRepo.huddleUpdateFlow.filter { it.id == huddleID.value }.collect {

                }
            } finally { }
        }
    }

    val mentionableUsers = huddleID.switchMap {
        val premiumUserLiveData: MutableLiveData< List<HuddleMentionableUsersResponse.MentionableUser>> = MutableLiveData()
        it?.let { id ->
            viewModelScope.launch {
                when (val result: ResultOf<HuddleMentionableUsersResponse> = huddleRepo.getHuddleMentionableUsers(id)) {
                    is ResultOf.APIError -> {}
                    is ResultOf.Error -> {}
                    is ResultOf.Success -> {
                        premiumUserLiveData.postValue(result.value.members)
                    }
                }
            }
        }
        premiumUserLiveData
    }

    fun clearNewPosts() {
        newPosts.postValue(mutableSetOf())
    }

    fun clearImageEditMode(){
        _isImageEditMode.postValue(false)
    }

    val enablePostButton: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            val hasData = showSendAction.value == true || chatMedia.value != null
            med.postValue(hasData && enableChatInteraction.value == true && hasFieldsChanged() && connectionStatus.value == ConnectivityObserver.ConnectionStatus.AVAILABLE)
        }
        med.addSource(showSendAction) { update() }
        med.addSource(chatMedia) { update() }
        med.addSource(enableChatInteraction) { update() }
        med.addSource(chatTextInput) { update() }
        med.addSource(chatReplyTo) { update() }
        med.addSource(chatTextColor) { update() }
        med.addSource(connectionStatus) { update() }
        med
    }

    override val canReplyToSelection = _selectedChats.map {
        it.size==1 && huddlePrivacy.value?.canReply!=false
    }

    fun canReport(msg: HuddleChatMessage): Boolean {
        return !msg.reported && msg.sender != user.id && user.canReport(msg.senderDetails)
    }

    override val canReportSelection = _selectedChats.map {
        if (it.isNullOrEmpty()) return@map false
        val sender = (it[0] as? HuddleChatMessage)?.senderDetails
        it.size == 1 && !it[0].reported && it[0].sender != user.id && user.canReport(sender)
    }

    val canForwardSelection = _selectedChats.map { it.isNotEmpty() && it.size==1 && it.all { ch -> ch.messageType == AbstractChatMessage.MessageType.TEXT ||  ch.messageType == AbstractChatMessage.MessageType.MEDIA  } }

    val chatMediaAudioInfo = chatMediaAudio.map {
        it?: return@map null
        try {
            if (it is TempMedia.SavedMedia) {
                return@map if (it.offlineMedia!=null) {
                    MediaPlayerInfo.from(it.offlineMedia, it.meta, LIST_POS_RECORDING)
                } else {
                    MediaPlayerInfo.from(it, it.meta, LIST_POS_RECORDING)
                }
            } else {
                val meta = chatRepo.generateMediaMeta(it)
                return@map MediaPlayerInfo.from(it, meta, LIST_POS_RECORDING)
            }
        } catch (e: Exception) {
            return@map null
        }
    }

    fun canReplyToPost(msg: AbstractChatMessage): Boolean {
        return enableChatInteraction.value==true
                && huddlePrivacy.value?.canReply!=false
                && msg.sendStatus==AbstractChatMessage.SendStatus.NONE
                && !msg.reported
    }

    private var pendingCommentNavigation:String?=null

    val onNavigateToPostComments = LiveEvent<String>()

    override fun finishAudioRecording() {
        _audioRecordingLocked.value = true
        super.finishAudioRecording()
    }

    /*
    Set this value when launching a post edit action
     */
    val postToEdit: MutableLiveData<HuddleChatMessageWithMedia?> = MutableLiveData(null)

    val onPostEditReady = LiveEvent<Unit>()

    fun playPostAudio() {
        when(chatMediaAudio.value) {
            is TempMedia.SavedMedia -> postToEdit.value?.let { playMedia(it, LIST_POS_RECORDING) }
            is TempMedia -> playPauseRecording()
        }
    }
    fun huddleEditImage(){
        _isImageEditMode.postValue(true)
        super.editImage()
    }

    fun editPost(post: HuddleChatMessageWithMedia) {
        post.message.apply{
            mediaMeta?.let {
                Log.w("PEDIT", "editPost: ${TempMedia.SavedMedia.of(it, post.offlineMedia)}" )
                chatMedia.postValue(TempMedia.SavedMedia.of(it, post.offlineMedia))
            }
            replyTo?.let {
                chatReplyTo.postValue(it)
            }

            chatTextInput.postValue(UserInfoUtil.decodeMentions(displayMessage.orEmpty(),mentionedUsers?: listOf()))
            onSelectColor(post.message.chatTextColor.orDefault())
        }
        postToEdit.postValue(post)
        onPostEditReady.postValue(Unit)
    }

    val hasUnsavedChatMedia = chatMedia.map {
        it is TempMedia && it !is TempMedia.SavedMedia
    }

    val chatMediaAvailableOffline = chatMedia.map {
        Log.w("PEDIT", "isChatMediaAvailableOffline: ${it !is TempMedia.SavedMedia ||  it.offlineMedia!=null} | $it" )
        it !is TempMedia.SavedMedia ||  it.offlineMedia!=null
    }

    val chatMediaVideoPlaceholder = chatMediaVideo.map {
        if (it is TempMedia.SavedMedia && !it.hasOfflineMedia) it.meta.thumbnail else null
    }

    private fun clearEditPost() {
        postToEdit.postValue(null)
    }

    fun cancelPostSheet() {
        _messageSending.postValue(false)
        clearEditPost()
        cancelAudioRecording()
        clearChatTextAndMedia()
        clearReplyTo()
    }

    enum class MessagePostErrorType { NETWORK, OTHER }

    val onFailedToPostMessage = LiveEvent<MessagePostErrorType>()

    override suspend fun sendMessage(message: String, media: TempMedia?, replyTo: ReplyTo?, location: AttachLocation?, color: ChatTextColor?): AbstractChatMessage? {
        val huddle = huddle.value ?: return null
        if (media is TempMedia.SavedMedia) {
            clearChatMedia(false)
        }
        if (connectionStatus.value != ConnectivityObserver.ConnectionStatus.AVAILABLE) {
            onFailedToPostMessage.postValue(MessagePostErrorType.NETWORK)
            return null
        }
        val result = chatRepo.sendHuddlePost(huddle.id, huddle.huddleType, UserInfoUtil.encodeMentions(message, mentionableUsers.value?:listOf()), media, replyTo, color, postToEdit.value?.message, postVideoToFlash) { med ->
            currentlyTransferringMedia = med
        }
        currentlyTransferringMedia = null
        return when (result) {
            is ResultOf.Success -> {
                chatRepo.resetHuddleUnreadCount(huddle.id)
                if (postVideoToFlash) videoPostedToFlash.postValue(true)
                result.value
            }
            else -> {
                onFailedToPostMessage.postValue(MessagePostErrorType.OTHER)
                null
            }
        }
    }

    suspend fun getVideoStreamingURL(msg: AbstractChatMessageWithMedia, pos: Int): String? {
        msg.message.mediaMeta?.let { meta ->
            if (meta.mediaType == MediaType.VIDEO) {
                val result = chatRepo.getMediaDownloadURL(msg.message)
                if (result is ResultOf.Success) {
                    return result.value.signedUrl
                }
            }
        }
        return null
    }

    fun onStreamingVideo(message: AbstractChatMessageWithMedia,uri: String, pos: Int) {
        stopMediaPlayback()
        val info = MediaPlayerInfo.from(message.message,uri,pos).apply {
            state = MediaPlayerInfo.MediaState.PLAYING
        }
        _nowPlayingMedia = info
        nowPlayingMedia.postValue(info)
        pendingItemChange = pos
    }

    val onUserBlocked = LiveEvent<String>()
    val onUserFollow = LiveEvent<String>()

    fun blockUser(msg: HuddleChatMessage) {
        viewModelScope.launch(Dispatchers.IO){
            _actionLoading.postValue(true)
            val result = profileRepo.blockUser(msg.sender)
            if(result is ResultOf.Success) {
                onUserBlocked.postValue(result.value.orEmpty())
                huddleRepo.getHuddleInterventions(huddleID.value?:return@launch)
            }
            _actionLoading.postValue(false)
        }
    }

    fun unblockUser(msg: HuddleChatMessage) {
        viewModelScope.launch(Dispatchers.IO){
            _actionLoading.postValue(true)
            val result = profileRepo.unblockUser(msg.sender)
            if(result is ResultOf.Success) {
                onUserBlocked.postValue(result.value.orEmpty())
                huddleRepo.getHuddleInterventions(huddleID.value?:return@launch)
            }
            _actionLoading.postValue(false)
        }
    }

    fun followUser(id: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            var user = profileRepo.getLocalOtherUserProfile(id).value
            if (user==null) {
                user = getUserProfile(id)?: return@launch
            }
            when (val result = profileRepo.followUser(user)) {
                is ResultOf.Success -> {
                    profileRepo.updateOtherUser(user.copy(isStar = true))
                    onUserFollow.postValue(result.value.orEmpty())
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }

    private suspend fun getUserProfile(id: Int): OtherUser? {
        when (val result =profileRepo.getPublicUserDetails(id)) {
            is ResultOf.Success -> {
                return result.value
            }
            is ResultOf.APIError -> {
            }
            is ResultOf.Error -> {
            }
        }
        return null
    }

    val onFailedToSendAdminInvite = LiveEvent<Boolean>()

    fun appointAsAdmin(senderId: Int?) {
        fun sendAdminRequest(senderId: Int?) {
            viewModelScope.launch(Dispatchers.IO){
                _actionLoading.postValue(true)
                when(huddleRepo.sendOrCancelAdminInvite(huddleID.value?: return@launch,senderId!!)) {
                    is ResultOf.Success -> {
                        getFullHuddleInfo(huddleID.value?: return@launch)
                    }
                    is ResultOf.APIError -> {

                    }

                    is ResultOf.Error -> {

                    }
                }
                _actionLoading.postValue(false)
            }
        }
        if (!user.premium) {
            if (_huddleInfo.value?.canSendOrAcceptAdminInvite == true) {
                sendAdminRequest(senderId)
                onFailedToSendAdminInvite.postValue(false)
            } else onFailedToSendAdminInvite.postValue(true)
        } else sendAdminRequest(senderId)
    }

    fun cancelAdminInvite(senderId:Int) {
        viewModelScope.launch(Dispatchers.IO){
            _actionLoading.postValue(true)
            val result = huddleRepo.sendOrCancelAdminInvite(huddleID.value?: return@launch,senderId, cancel = true)
            if(result is ResultOf.Success) {
                //TODO show some toast
                getFullHuddleInfo(huddleID.value?: return@launch)
            }
            _actionLoading.postValue(false)
        }
    }

    fun dismissAdmin(senderId: Int?) {
        viewModelScope.launch(Dispatchers.IO){
            _actionLoading.postValue(true)
            val result = huddleRepo.dismissAdmin(huddleID.value?: return@launch,senderId!!)
            if(result is ResultOf.Success) {
                //TODO show some toast
                huddleRepo.getHuddleInterventions(huddleID.value?:return@launch)
            }
            _actionLoading.postValue(false)
        }
    }

    val onBlockedUser = LiveEvent<String>()
    val onUnblockedUser = LiveEvent<String>()

    fun blockUserFromHuddle(senderId: Int?, action: Participant.ParticipantsActionTypes) {
        viewModelScope.launch(Dispatchers.IO) {
            _actionLoading.postValue(true)
            val result = huddleRepo.blockOrUnblockHuddleUser(huddleID.value ?: return@launch, action = action, userList = arrayListOf(senderId!!))
            if (result is ResultOf.Success) {
                result.value.users.firstOrNull()?.memberName?.let { user ->
                    when (action) {
                        Participant.ParticipantsActionTypes.BLOCK_HUDDLE_PARTICIPANT -> onBlockedUser.postValue(user)
                        Participant.ParticipantsActionTypes.UNBLOCK_HUDDLE_PARTICIPANT -> onUnblockedUser.postValue(user)
                    }
                }
                huddleRepo.getHuddleInterventions(huddleID.value ?: return@launch)
            }
            _actionLoading.postValue(false)
        }
    }

    private val _pinnedPost = huddleID.switchMap {
        it?:return@switchMap null
        huddleRepo.getHuddlePinnedPostLiveData(it)
    }

    val pinnedPost : MediatorLiveData<ChatMessageUIModel?> by lazy {
        val med = MediatorLiveData<ChatMessageUIModel?>()
        fun pinnedPost(){
            if (showChats.value == false) med.postValue(null)
            else {
                if (_pinnedPost.value != null) {
                    val post = _pinnedPost.value!!
                    med.postValue(ChatMessageUIModel.ChatMessageModel(post).apply {
                        post.message.senderDetails?.countryCode?.let { cc ->
                            countryFlag = _countryList.value?.get(cc)
                        }
                        ongoingTransfer = _mediaTransfers.find { it.messageId == post.message.messageId }
                        playerInfo = if (_nowPlayingMedia?.messageId == post.message.messageId && _nowPlayingMedia?.listPosition== LIST_POS_PINNED) _nowPlayingMedia else null
                    })
                } else {
                    med.postValue(null)
                }
            }
        }
        med.addSource(_pinnedPost){ pinnedPost() }
        med.addSource(_countryList){ pinnedPost() }
        med.addSource(nowPlaying){ pinnedPost() }
        med.addSource(showChats) { pinnedPost() }
        med.addSource(onMediaTransfersChanged){ pinnedPost() }
        med
    }

    fun onPinnedPostClick(item: AbstractChatMessage) {
        val replyMsgId = item.messageId
        scrollToMessageNow.postValue(replyMsgId)
        viewModelScope.launch {
            delay(1000)
            highlightMessage.postValue(replyMsgId)
        }
    }

    val onPostPinned = LiveEvent<Boolean>()

    fun pinMessage(msg: HuddleChatMessage, pin: Boolean) {
        viewModelScope.launch(Dispatchers.IO){
            _actionLoading.postValue(true)
            val result = huddleRepo.pinUnpinHuddlePost(msg.huddleIdInt, pin, msg.messageId)
            if (result is ResultOf.Success){
                onPostPinned.postValue(pin)
            }
            _actionLoading.postValue(false)
        }
    }
    val onRestrictedFromHuddle = LiveEvent<String?>()
    private val searchTerm = MutableLiveData<String>("")
    fun banUser(participantsStatus: Participant.ParticipantStatus, isBanned:Boolean, userid:Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.banUnbanParticipant(huddleId = huddle.value?.id!!, userId = userid, status = participantsStatus, isBan = !isBanned)) {
                is ResultOf.Success -> {
                    searchTerm.postValue("")
                    onRestrictedFromHuddle.postValue(result.value)
                    huddleRepo.getHuddleInterventions(huddle.value?.id!!)
                }
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
            }
        }
    }
    private val _participantsAPIRequestActionError = MutableLiveData<String?>(null)
    fun removeUser(id: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.removeHuddleUser(huddleId = huddle.value?.id!!, userId = id)) {
                is ResultOf.Success -> {
                    searchTerm.postValue("")
                }
                is ResultOf.APIError -> {
                    _participantsAPIRequestActionError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    _participantsAPIRequestActionError.postValue("")
                }
            }
        }
    }


    private fun hasFieldsChanged(): Boolean {
        //New Post flow
        if (postToEdit.value == null) return true

        val message = chatTextInput.value ?: ""
        val reply = chatReplyTo.value
        val color = chatTextColor.value
        postToEdit.value.let {
            if (message != it?.message?.displayMessage) return true
            else if (chatMedia.value?.name != it.message.mediaMeta?.mediaName) return true
            else if (reply != it.message.replyTo) return true
            else if (color != it.message.chatTextColor) return true
        }
        return false
    }

    val onSharePostLinkGenerated = LiveEvent<String>()

    fun getSharePostLink(message: String) {
        val huddleId = huddle.value?.id?: return
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = huddleRepo.getHuddlePostLink(message, huddleId)) {
                is ResultOf.Success -> {
                    result.value.let {
                        onSharePostLinkGenerated.postValue(it)
                    }
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    private val _pollOptionOneSelected = MutableLiveData(false)
    val pollOptionOneSelected: LiveData<Boolean?> = _pollOptionOneSelected

    private val _pollOptionTwoSelected = MutableLiveData(false)
    val pollOptionTwoSelected: LiveData<Boolean?> = _pollOptionTwoSelected

    private val _pollOptionThreeSelected = MutableLiveData(false)
    val pollOptionThreeSelected: LiveData<Boolean?> = _pollOptionThreeSelected

    private val _pollOptionError = MutableLiveData<String?>(null)
    val pollOptionError: LiveData<String?> = _pollOptionError


    private val _pollCreated = MutableLiveData<Boolean?>(false)
    val pollCreated: LiveData<Boolean?> = _pollCreated

    private val _poll = MutableLiveData<Poll?>(null)
    val poll: LiveData<Poll?> = _poll


    private val _isPollHide = MutableLiveData(false)
    val isPollHide: LiveData<Boolean?> = _isPollHide

    private val _pollEnabled = MutableLiveData(false)
    val pollEnabled: LiveData<Boolean?> = _pollEnabled

    val  isEndPollSuccess=LiveEvent<Boolean>()
    protected val pollRepository: PollsRepository = PollsRepository(application)
    val  isPollVotedSuccess = LiveEvent<Boolean>()

    private val _isPollLoading=MutableLiveData<Boolean?>(null)

    fun  togglePollVisibility(isVisible:Boolean){
        viewModelScope.launch {
            _isPollHide.postValue(isVisible)
        }
    }

    val showPoll: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun show() {
            if (pollEnabled.value == true &&
                (huddleStatus.value == ACTIVE ||
                        (huddleStatus.value == INVITED && _huddleInfo.value?.requestToJoin==false)||
                        huddleStatus.value == OPEN_TO_JOIN)) {
                med.postValue(true)
            } else {
                med.postValue(false)
            }
        }
        med.addSource(huddleStatus) { show() }
        med.addSource(pollEnabled) { show() }
        med.addSource(_huddleInfo) { show() }
        med
    }

    val pollLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun active() {
            if (_isPollLoading.value==false && huddleStatus.value!=null) {
                med.postValue(false)
            } else {
                med.postValue(true)
            }
        }
        med.addSource(_isPollLoading) { active() }
        med.addSource(huddleStatus) { active() }
        med
    }

    val pollActive=huddleStatus.map {
        it?:return@map false
        it==ACTIVE
    }.distinctUntilChanged()

    fun setPollOptionOnSelected() {
        _pollOptionOneSelected.postValue(true)
        _pollOptionTwoSelected.postValue(false)
        _pollOptionThreeSelected.postValue(false)
        poll.value?.let {
            castVote(it.answers[0].id)
        }
    }

    fun setPollOptionTwoSelected() {
         castVote(huddle.value?.id!!)
        _pollOptionOneSelected.postValue(false)
        _pollOptionTwoSelected.postValue(true)
        _pollOptionThreeSelected.postValue(false)
        poll.value?.let {
            castVote(it.answers[1].id)
        }
    }

    fun setPollOptionThreeSelected() {
        _pollOptionOneSelected.postValue(false)
        _pollOptionTwoSelected.postValue(false)
        _pollOptionThreeSelected.postValue(true)
        poll.value?.let {
            castVote(it.answers[2].id)
        }
    }

    fun pollOptionOneUnSelected() {
        _pollOptionOneSelected.postValue(false)
    }

    fun pollOptionTwoUnSelected() {
       _pollOptionTwoSelected.postValue(false)
    }

    fun pollOptionThreeUnSelected() {
        _pollOptionThreeSelected.postValue(false)
    }

    val answerIndex=poll.map {poll->
        if(poll?.userAnswer!=null){
            poll.answers.indexOfFirst { it.id == poll.userAnswer.answerId }
        }else null
    }

    val isEditMode=answerIndex.map {
        it?:return@map true
        return@map false
    }
    fun setPollCreated() {
        _pollCreated.postValue(true)
        clearPollSelection()
    }

    private fun clearPollSelection(){
        _pollOptionOneSelected.postValue(false)
        _pollOptionTwoSelected.postValue(false)
        _pollOptionThreeSelected.postValue(false)
    }
    fun setPollOptions(index: Int) {
        when(index) {
            0 -> {
                _pollOptionOneSelected.postValue(true)
                _pollOptionTwoSelected.postValue(false)
                _pollOptionThreeSelected.postValue(false)
            }
            1 -> {
                _pollOptionOneSelected.postValue(false)
                _pollOptionTwoSelected.postValue(true)
                _pollOptionThreeSelected.postValue(false)
            }
            2 ->{
                _pollOptionOneSelected.postValue(false)
                _pollOptionTwoSelected.postValue(false)
                _pollOptionThreeSelected.postValue(true)
            }
        }
    }
    fun endPoll(pollId: Int, huddleId: Int){
        _isPollLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = pollRepository.updateHuddlePoll(pollId, huddleId)) {
                is ResultOf.Success -> {
                    isEndPollSuccess.postValue(true)
                    getPollInfo(huddleId)
                }
                is ResultOf.APIError -> {
                    isEndPollSuccess.postValue(false)
                }
                is ResultOf.Error -> {
                    isEndPollSuccess.postValue(false)
                }
            }
            _isPollLoading.postValue(false)
        }
    }

    fun refreshPoll(huddleId: Int) {
        getPollInfo(huddleId)
    }

    private fun castVote(answerId:Int){
         _isPollLoading.postValue(true)
        viewModelScope.launch {
            when (val result=pollRepository.setAnswer(answerId,poll.value?.id!!)) {
                is ResultOf.Success -> {
                    isPollVotedSuccess.postValue(true)
                    getPollInfo(huddle.value?.id!!)
                }
                else -> {
                    isPollVotedSuccess.postValue(false)
                    clearPollSelection()
                }
            }
            _isPollLoading.postValue(false)
        }

    }
    private fun getPollInfo(huddleId: Int) {
        _isPollLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<PollResponse> = pollRepository.getPollsList(huddleId, "active")) {
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    if(result.value.polls.isNotEmpty()) {
                        _poll.postValue(result.value.polls[0])
                        // TODO cannot set this directly
//                         setViewState(MultiStateView.ViewState.CONTENT)
                        _pollEnabled.postValue(true)
                    }else{
                        _poll.postValue(null)
                        // TODO cannot set this directly
//                        setViewState(MultiStateView.ViewState.EMPTY)
                        _pollEnabled.postValue(false)
                    }
                }
            }
            _isPollLoading.postValue(false)
        }
    }

    val showPostToFlashConfirmation = LiveEvent<Boolean>()
    val videoPostedToFlash = LiveEvent<Boolean>()

    private var postVideoToFlash = false
    fun checkAndSendMessage() {
        if (chatMedia.value?.mediaType == MediaType.VIDEO) {
            val isEdit = postToEdit.value != null
            val media = chatMedia.value
            if (isEdit && media is TempMedia.SavedMedia) {
                postVideoToFlash = false
                prepareAndSendMessage()
                return
            }
            media?.let {
                val uri = it.sourceUri?: Uri.fromFile(it.file)
                val videoDuration = MediaUtils.getDuration(uri)
                // Calling new api for checking the user can post in flash also
                getFlashVideoEligibilityDetails(duration = videoDuration)
//                if (MediaUtils.getDuration(uri) <= 90){
//                    showPostToFlashConfirmation.postValue(true)
//                    return
//                }
                return
            }
        }
        postVideoToFlash = false
        prepareAndSendMessage()
    }

    fun getFlashVideoEligibilityDetails(duration: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val result = flashRepo.getFlashEligibilityDetails(currentDate = currentDate)
            if (result is ResultOf.Success) {
                val allowedFlashDuration = result.value.enabledFlashDuration ?: 0
                if ((duration <= allowedFlashDuration) && result.value.flashEligibility == true) {
                    // If the current video is in allowed limit for the user,
                    // the bottom sheet for "post to flash" also will be visible
                    showPostToFlashConfirmation.postValue(true)
                    return@launch
                }
            }
            postVideoToFlash = false
            prepareAndSendMessage()
        }
    }

    fun postVideoToFlash(postToFlash: Boolean) {
        postVideoToFlash = postToFlash
    }

    val errorMessage = LiveEvent<String>()
    val showLoadingDialog = MutableLiveData(false)
    fun checkPermissionToUpdateHuddle(post: HuddleChatMessageWithMedia) {
        viewModelScope.launch (Dispatchers.IO) {
            showLoadingDialog.postValue(true)
            when(val result = legalAffairsRepo.checkPermission(
                id = post.message.messageId,
                parentId = post.message.huddleId,
                source = LegalAffairPermissionSource.HUDDLE)
            ) {
                is ResultOf.Success -> {
                    editPost(post)
                }
                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    errorMessage.postValue(result.exception.message)
                }
            }
            showLoadingDialog.postValue(false)
        }
    }

}