package com.app.messej.ui.auth.profile

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.ui.auth.common.BaseLocationSetFragment
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.gms.maps.GoogleMap

class RegisterLocationFragment : BaseLocationSetFragment() {

    override val viewModel: RegisterLocationViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
    }

    override fun onMapReady(googleMap: GoogleMap) {
        super.onMapReady(googleMap)
        setToCurrentLocation()
    }

    private fun observe() {
        viewModel.isLocationSelected.observe(viewLifecycleOwner) {
            binding.confirmLocationButton.text = if(it) resources.getText(R.string.register_confirm_location_button_text) else resources.getText(R.string.common_skip)
        }

        viewModel.confirmLocationLoading.observe(viewLifecycleOwner) {
            binding.confirmLocationButton.isEnabled = !it
        }

        viewModel.onLocationConfirmed.observe(viewLifecycleOwner){
            findNavController().navigateSafe(RegisterLocationFragmentDirections.actionRegisterLocationFragmentToNavigationRegisterSuperstar())
        }
    }
}


