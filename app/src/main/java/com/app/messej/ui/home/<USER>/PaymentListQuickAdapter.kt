package com.app.messej.ui.home.businesstab

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.api.PaymentMethodResponse
import com.app.messej.databinding.ItemPaymentMethodsBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class PaymentListQuickAdapter(paymentMethodList: MutableList<PaymentMethodResponse.PaymentMethod>, private val listener: ItemClickListener?) : BaseQuickAdapter<PaymentMethodResponse.PaymentMethod, BaseDataBindingHolder<ItemPaymentMethodsBinding>>(
    R.layout.item_payment_methods, paymentMethodList
) {
    var selectedItemPosition: Int = -1
    object PaymentMethodDiff : DiffUtil.ItemCallback<PaymentMethodResponse.PaymentMethod>(){
        override fun areItemsTheSame(oldItem: PaymentMethodResponse.PaymentMethod, newItem: PaymentMethodResponse.PaymentMethod): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: PaymentMethodResponse.PaymentMethod, newItem: PaymentMethodResponse.PaymentMethod): Boolean {
            return oldItem.id == newItem.id
        }
    }

    override fun convert(
        holder: BaseDataBindingHolder<ItemPaymentMethodsBinding>,
        item: PaymentMethodResponse.PaymentMethod
    ) {
        val isSelect = holder.absoluteAdapterPosition == selectedItemPosition
        holder.dataBinding?.apply {
            paymentMethod = item
            isSelected = isSelect
        }
        holder.itemView.setOnClickListener {
            val previousSelectedItemPosition = selectedItemPosition
            selectedItemPosition = holder.absoluteAdapterPosition
            notifyItemChanged(previousSelectedItemPosition)
            notifyItemChanged(selectedItemPosition)
            listener?.onItemClick(item, holder.absoluteAdapterPosition)
        }
    }

    interface ItemClickListener {
        fun onItemClick(item: PaymentMethodResponse.PaymentMethod, position: Int)
    }
}
