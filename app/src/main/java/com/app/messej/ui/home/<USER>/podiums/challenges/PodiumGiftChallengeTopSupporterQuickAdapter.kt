package com.app.messej.ui.home.publictab.podiums.challenges

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.databinding.ItemPodiumChallengeBoardGiftSupportersBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class PodiumGiftChallengeTopSupporterQuickAdapter(data: MutableList<PodiumChallenge.ChallengeUser>): BaseQuickAdapter<PodiumChallenge.ChallengeUser, BaseDataBindingHolder<ItemPodiumChallengeBoardGiftSupportersBinding>>(R.layout.item_podium_challenge_board_gift_supporters, data) {

    override fun convert(holder: BaseDataBindingHolder<ItemPodiumChallengeBoardGiftSupportersBinding>,
                         item: PodiumChallenge.ChallengeUser
    ) {
        holder.dataBinding?.apply {
            speaker = item
        }
    }

    class DiffCallback: DiffUtil.ItemCallback<PodiumChallenge.ChallengeUser>() {
        override fun areItemsTheSame(oldItem: PodiumChallenge.ChallengeUser, newItem: PodiumChallenge.ChallengeUser): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: PodiumChallenge.ChallengeUser,
                                        newItem: PodiumChallenge.ChallengeUser
        ): Boolean {
            return oldItem.score == newItem.score
        }
    }
}