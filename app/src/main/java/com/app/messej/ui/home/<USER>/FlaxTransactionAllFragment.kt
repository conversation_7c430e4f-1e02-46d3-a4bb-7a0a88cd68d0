package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.databinding.FragmentFlaxTransactionAllBinding
import com.app.messej.ui.home.businesstab.adapter.DealsTransactionsPagerAdapter
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView


class FlaxTransactionAllFragment : Fragment(), DealsTransactionsPagerAdapter.DealsTransactionListener {

    private var adapter: DealsTransactionsPagerAdapter?=null
    private val viewModel: BusinessDealsListViewModel by viewModels()
    private lateinit var binding: FragmentFlaxTransactionAllBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flax_transaction_all, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }
    override fun onStart() {
        super.onStart()
        viewModel.loadAllData()
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    private fun setUp() {
        emptyView()
        viewModel.setTransferType("")
        adapter = DealsTransactionsPagerAdapter(requireContext(), this,false)
        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = LinearLayoutManager.VERTICAL // Set the orientation as needed
        binding.rvFlaxList.layoutManager = layoutManager
        binding.rvFlaxList.adapter = adapter

        adapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    Log.d("ITEMCOUNTT","itemCount: $itemCount")
                    if (itemCount < 1){
                        MultiStateView.ViewState.EMPTY
                    }else {
                        MultiStateView.ViewState.CONTENT
                    }
                } else {
//                    if (itemCount < 1){
//                        MultiStateView.ViewState.EMPTY
//                    }else {
                    MultiStateView.ViewState.CONTENT
//                    }
                }
            }
        }
    }

    private fun observe() {
        viewModel.transferHistoryList.observe(viewLifecycleOwner){
            adapter?.submitData(viewLifecycleOwner.lifecycle, pagingData = it)
        }
    }

    private fun emptyView() {
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.eds_empty_image_transaction).setImageResource(R.drawable.bg_empty_flax_history)
            findViewById<AppCompatTextView>(R.id.eds_empty_message_transaction).text = resources.getString(R.string.no_transactions_made)
        }
    }
    private fun searchEmptyView() {
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.deals_empty_title).apply {
                text = resources.getString(R.string.no_sent_flax_history_found)
            }
        }
    }

    override fun onUserClick(item: DealsTransferHistory) {
        if (item.status == DealsTransferHistory.FlaxStatus.Sent) {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(item.receiverId ?: return))
        }
        else {
            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(item.senderId ?: return))
        }
    }


}