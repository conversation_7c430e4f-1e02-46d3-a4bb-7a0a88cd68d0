package com.app.messej.ui.home.businesstab.operations.tasks.information

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.BusinessTaskInformativeMessage
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.enums.BusinessTaskActivity
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.status.BusinessActivityStatus
import com.app.messej.databinding.FragmentTaskInformativeMessageDialogBinding
import com.app.messej.ui.home.businesstab.operations.tasks.BusinessOperationsTaskViewModel
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class TaskInformativeMessageDialogFragment : BottomSheetDialogFragment() {

    private val args: TaskInformativeMessageDialogFragmentArgs by navArgs()

    private val viewModel: TaskInformativeMessageDialogViewModel by viewModels()
    private val mViewModel: BusinessOperationsTaskViewModel by activityViewModels()

    private lateinit var binding: FragmentTaskInformativeMessageDialogBinding

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_task_informative_message_dialog, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = false
        binding.actionClose.setOnClickListener { dismiss() }
        observe()
    }

    private fun observe() {
        viewModel.businessOperation.observe(viewLifecycleOwner) {
            it?.let {
                val model = BusinessTaskInformativeMessage()
                when (args.activityName) {
//                    BusinessTaskActivity.LIKES -> {
//                       likesActions(it, model)
//                    }
                    BusinessTaskActivity.CUSTOMERS -> {
                        customerActions(it, model)
                    }
                    BusinessTaskActivity.HUDDLES -> {
                        huddleActions(it, model)
                    }
//                    BusinessTaskActivity.BROADCASTS -> {
//                        broadcastActions(it, model)
//                    }
//                    BusinessTaskActivity.SHARE -> {
//                        appShareActions(it, model)
//                    }
//                    BusinessTaskActivity.PARTICIPANTS -> {
//                        participantsActions(it, model)
//                    }
//                    BusinessTaskActivity.FOLLOWERS -> {
//                        followerActions(it, model)
//                    }

//                    BusinessTaskActivity.FANS -> {
//                        fansAction(it, model)
//                    }

                    else -> {}
                }
                viewModel.taskModel.postValue(model)
            }
        }
        mViewModel.dialogDismiss.observe(viewLifecycleOwner) {
            dismiss()
        }
    }

    private fun customerActions(data: BusinessActivityStatus, model: BusinessTaskInformativeMessage) {

        val validCitizenShips = setOf(UserCitizenship.MINISTER, UserCitizenship.OFFICER, UserCitizenship.AMBASSADOR, UserCitizenship.PRESIDENT)


        model.apply {
            achievedText = getString(R.string.business_task_activity_participants_achieved_message_one)
            achievedTextType =  if (viewModel.businessOperation.value?.citizenship in validCitizenShips) {
                resources.getQuantityString(R.plurals.business_task_activity_customer_text_officer_minister,data.customers?.currentFans!!)
            }else{
                resources.getQuantityString(R.plurals.business_task_activity_customer_text,data.customers?.currentFans!!)
            }

            achievedCount = data.customers.currentFans
            targetCount = data.customers.requiredFans
            targetTextType = if (viewModel.businessOperation.value?.citizenship in validCitizenShips) {
                resources.getQuantityString(R.plurals.business_task_activity_customer_text_officer_minister,data.customers.requiredFans!!)
            }else{
                resources.getQuantityString(R.plurals.business_task_activity_customer_text,data.customers.requiredFans!!)
            }
            targetText = getString(R.string.business_task_activity_text_one)
            percentage = data.customers.customersPercentage?.toInt()
            progressColor = data.percentageColor(data.customers.customersPercentage?.toInt())
            if (data.customers.customersPercentage?.toInt()!! >= 100) {
                isTaskCompleted = true
                statusText = getString(R.string.business_task_activity_success_message)
            } else {
                isTaskCompleted = false
                statusText = if (viewModel.businessOperation.value?.citizenship in validCitizenShips) {
                    getString(R.string.business_task_activity_customer_status_message_officer_minister, data.customers.requiredFans)
                }else{
                    getString(R.string.business_task_activity_customer_status_message, data.customers.requiredFans)
                }



            }
        }
    }

    private fun huddleActions(data: BusinessActivityStatus, model: BusinessTaskInformativeMessage) {
        model.apply {
            achievedText = getString(R.string.business_task_activity_likes_achieved_message_three)
            achievedTextType = resources.getQuantityString(R.plurals.business_task_activity_huddle_message,data.huddles?.currentHuddlesCount!!)
            achievedCount = data.huddles.currentHuddlesCount
            targetCount = data.huddles.requiredHuddleCount
            targetTextType = resources.getQuantityString(R.plurals.business_task_activity_huddle_message, data.huddles.requiredHuddleCount!!)
            targetText = getString(R.string.business_task_activity_text_one)
            percentage =data.huddles.huddlePercentage
            progressColor = data.percentageColor(data.huddles.huddlePercentage)
            if (data.huddles.huddlePercentage!! >= 100) {
                isTaskCompleted = true
                statusText = getString(R.string.business_task_activity_success_message)
            } else {
                isTaskCompleted = false
                statusText = getString(R.string.business_task_activity_huddle_status_message, data.huddles.requiredHuddleCount,data.huddles.requiredParticipantCount.toString())
            }
        }
    }

    private fun broadcastActions(data: BusinessOperation, model: BusinessTaskInformativeMessage) {
        model.apply {
            achievedText = getString(R.string.business_task_activity_likes_achieved_message_four)
            achievedTextType = resources.getQuantityString(R.plurals.business_task_activity_broadcast_message,data.totalBroadcasts!!)
            achievedCount = data.totalBroadcasts
            targetCount = data.requirementTargets?.broadcastTarget
            targetTextType = resources.getQuantityString(R.plurals.business_task_activity_broadcast_message,data.requirementTargets?.broadcastTarget!!)
            targetText = getString(R.string.business_task_activity_text_one)
            percentage =data.newBroadcastPercentage
            progressColor = data.percentageColor(data.newBroadcastPercentage)
            if (data.newBroadcastPercentage!! >= 100) {
                isTaskCompleted = true
                statusText = getString(R.string.business_task_activity_success_message)
            } else {
                isTaskCompleted = false
                statusText = getString(R.string.business_task_activity_broadcast_status_message, data.requirementTargets?.broadcastTarget)
            }
        }
    }

    private fun appShareActions(data: BusinessOperation, model: BusinessTaskInformativeMessage) {
        model.apply {
            achievedTextType =resources.getQuantityString(R.plurals.business_task_activity_app_share_message,data.appSharesCount!!)
            achievedText = getString(R.string.business_task_activity_likes_achieved_message_one)
            achievedCount = data.appSharesCount
            targetCount = data.requirementTargets?.appSharesTarget
            targetTextType = resources.getQuantityString(R.plurals.business_task_activity_app_share_message,data.requirementTargets?.appSharesTarget!!)
            targetText = getString(R.string.business_task_activity_text_one)
            percentage = data.newAppSharePercentage
            progressColor = data.sharesPercentColor
            if (data.newAppSharePercentage == 100) {
                isTaskCompleted = true
                statusText = getString(R.string.business_task_activity_success_message)
            } else {
                isTaskCompleted = false
                statusText = getString(R.string.business_task_activity_app_share_status_message,data.requirementTargets?.appSharesTarget)
            }
        }
    }

    private fun participantsActions(data: BusinessOperation, model: BusinessTaskInformativeMessage) {
        model.apply {
            achievedTextType = resources.getQuantityString(R.plurals.business_task_activity_participants_message,data.participantCount!!)
            achievedText = getString(R.string.business_task_activity_participants_achieved_message_one)
            achievedCount = data.participantCount
            targetCount = data.requirementTargets?.participantsTarget
            targetTextType = resources.getQuantityString(R.plurals.business_task_activity_participants_message,data.requirementTargets?.participantsTarget!!)
            targetText = getString(R.string.business_task_activity_text_one)
            percentage = data.newParticipantPercentage
            progressColor = data.percentageColor(data.newParticipantPercentage)
            if (data.newParticipantPercentage == 100) {
                isTaskCompleted = true
                statusText = getString(R.string.business_task_activity_success_message)
            } else {
                isTaskCompleted = false
                statusText = getString(R.string.business_task_activity_participants_status_message,  data.requirementTargets?.participantsTarget)
            }
        }
    }

    private fun followerActions(data: BusinessOperation, model: BusinessTaskInformativeMessage) {
        model.apply {
            achievedTextType = resources.getQuantityString(R.plurals.business_task_activity_followers_message,data.followers!!)
            achievedText = getString(R.string.business_task_activity_participants_achieved_message_one)
            achievedCount = data.followers
            targetCount = data.requirementTargets?.followersTarget
            targetTextType = resources.getQuantityString(R.plurals.business_task_activity_followers_message,data.requirementTargets?.followersTarget!!)
            targetText = getString(R.string.business_task_activity_text_one)
            percentage = data.newFollowersPercentage
            progressColor = data.followerPercentColor
            if (data.newFollowersPercentage == 100) {
                isTaskCompleted = true
                statusText = getString(R.string.business_task_activity_success_message)
            } else {
                isTaskCompleted = false
                statusText = getString(R.string.business_task_activity_followers_status_message, data.requirementTargets?.followersTarget)
            }
        }
    }


    private fun fansAction(data: BusinessOperation, model: BusinessTaskInformativeMessage) {
        model.apply {
            achievedTextType = resources.getQuantityString(R.plurals.business_task_activity_fans_message,data.fans!!)
            achievedText = getString(R.string.business_task_activity_participants_achieved_message_one)
            achievedCount = data.fans
            targetCount = data.requirementTargets?.fansTarget
            targetTextType = resources.getQuantityString(R.plurals.business_task_activity_fans_message,data.requirementTargets?.fansTarget!!)
            targetText = getString(R.string.business_task_activity_text_one)
            percentage = data.newFansPercentage
            progressColor = data.fansPercentColor
            if (data.newFansPercentage == 100) {
                isTaskCompleted = true
                statusText = getString(R.string.business_task_activity_success_message)
            } else {
                isTaskCompleted = false
                statusText = getString(R.string.business_task_activity_fans_status_message, data.requirementTargets.fansTarget)
            }
        }
    }
}