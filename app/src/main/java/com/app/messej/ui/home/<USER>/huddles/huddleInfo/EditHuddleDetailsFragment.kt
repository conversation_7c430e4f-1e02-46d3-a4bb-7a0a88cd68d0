package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.annotation.SuppressLint
import android.app.Activity
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AutoCompleteTextView
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentEditHuddleDetailsBinding
import com.app.messej.ui.auth.common.HuddleLanguageDropDownAdapter
import com.app.messej.ui.common.CategoryDropdownAdapter
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.ATTACH_SOURCE_RESULT_KEY
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_CAMERA
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment.Companion.SRC_GALLERY
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.PermissionsHelper.checkCameraPermission
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.launch

class EditHuddleDetailsFragment : Fragment() {

    private lateinit var binding: FragmentEditHuddleDetailsBinding

    private val args: EditHuddleDetailsFragmentArgs by navArgs()

    private val viewModel : HuddleEditViewModel by viewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_edit_huddle_details, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setup() {
        viewModel.setHuddleId(args.huddleId)
        binding.textInputName.editText?.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                viewModel.didEnterName.postValue(true)
            }
        }

        binding.saveHuddleButton.setOnClickListener {
            viewModel.updateHuddleDetails()
        }

        binding.huddleDp.setOnClickListener {
            val action = EditHuddleDetailsFragmentDirections.actionGlobalProfileImageAttachSourceFragment()
            findNavController().navigateSafe(action)
        }
    }

    private fun observe() {
        viewModel.editHuddleInfoStageValid.observe(viewLifecycleOwner) {
            binding.saveHuddleButton.isEnabled = it
        }
        viewModel.nameError.observe(viewLifecycleOwner) {
            binding.textInputName.error = when (it) {
                HuddleEditViewModel.Companion.NameError.NONE -> {
                    binding.textInputName.isErrorEnabled = false
                    null
                }
                HuddleEditViewModel.Companion.NameError.LT_MIN -> {
                    binding.textInputName.isErrorEnabled = false
                    null
                }
                HuddleEditViewModel.Companion.NameError.GT_MAX -> {
                    binding.textInputName.isErrorEnabled = true
                    resources.getString(R.string.register_create_profile_error_name_max)
                }
                null -> {
                    binding.textInputName.isErrorEnabled = false
                    null
                }
            }
        }

        viewModel.huddleUpdated.observe(viewLifecycleOwner){
            Toast.makeText(requireContext(), R.string.common_saved, Toast.LENGTH_SHORT).show()
            findNavController().previousBackStackEntry?.savedStateHandle?.set(HuddleInfoViewModel.HUDDLE_UPDATED, true)
            findNavController().popBackStack()
        }

        viewModel.onHuddleInfoError.observe(viewLifecycleOwner){
            val message = it?: resources.getString(R.string.default_unknown_error_message)
            Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
            findNavController().popBackStack()
        }

        setFragmentResultListener(ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when(bundle.getString(ATTACH_SOURCE_RESULT_KEY)) {
                SRC_GALLERY -> selectImageFromGallery()
                SRC_CAMERA -> takeImage()
            }
        }

        viewModel.huddleCategoryList.observe(viewLifecycleOwner) { cats ->
            if (cats != null) {
                val adapter = CategoryDropdownAdapter(requireContext(), cats)
                (binding.categoryDropdown.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.setCategoryId(item.categoryId)
                    }
                    viewModel.category.value?.let { cat ->
                        val index = cats.indexOfFirst { it.categoryId == cat }
                        if (index==-1) return@let
                        adapter.setSelectedPos(index)
                        setText(cats[index].name)
                    }
                }
            }
        }


        viewModel.huddleLanguageList.observe(viewLifecycleOwner){ languages ->
            if (languages != null) {
                val adapter = HuddleLanguageDropDownAdapter(requireContext(), languages)
                (binding.languageDropdown.editText as? AutoCompleteTextView)?.apply {
                    setAdapter(adapter)
                    setOnItemClickListener { _, _, position, _ ->
                        adapter.setSelectedPos(position)
                        val item = adapter.getItem(position)
                        setText(item.name)
                        viewModel.setLanguage(item)
                    }
                    viewModel.huddleLanguage.value?.let { lang ->
                        val index = languages.indexOfFirst { it.englishName == lang }
                        if (index==-1) return@let
                        adapter.setSelectedPos(index)
                        setText(languages[index].name)
                    }
                }
            }

        }

        viewModel.onTriggerCrop.observe(viewLifecycleOwner) {
            val options = UCrop.Options().apply {
                setCircleDimmedLayer(true)
                val color = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                setToolbarColor(color)
                setStatusBarColor(color)
                setToolbarTitle(resources.getString(R.string.common_crop))
            }
            val crop = UCrop.of(it.first, it.second)
                .withAspectRatio(1f,1f)
                .withOptions(options)
            imageCropResult.launch(crop.getIntent(requireContext()))
        }
    }

    private val imageCropResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let {
                val resultUri = UCrop.getOutput(it)?: return@let
                viewModel.addCroppedImage(resultUri)
            }
        } else {
            viewModel.onCropCancelled()
        }
    }

    override fun onStart() {
        super.onStart()
        val actionBar = binding.toolbar
        (activity as MainActivity).setupActionBar(actionBar)

        (activity as AppCompatActivity).onBackPressedDispatcher.addCallback(viewLifecycleOwner, object: OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed:")
                checkAndShowBackPressAlert()
            }
        })
        (activity as MainActivity).setupActionBar(binding.toolbar)
        binding.toolbar.apply {
            setNavigationOnClickListener {
                checkAndShowBackPressAlert()
            }
        }

    }


    private fun selectImageFromGallery() = selectImageFromGalleryResult.launch("image/*")

    private val takeImageResult =
        registerForActivityResult(ActivityResultContracts.TakePicture()) { isSuccess ->
            if (isSuccess) {
                viewModel.addCapturedImage()
            }
        }

    private val selectImageFromGalleryResult =
        registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
            uri?.let {
                viewModel.addImage(uri)
            }
        }

    private fun takeImage() {
        checkCameraPermission(binding.root) {
            lifecycleScope.launch {
                val uri = viewModel.getImageUriForCapture()
                takeImageResult.launch(uri)
            }
        }
    }

    private fun checkAndShowBackPressAlert() {
        if (viewModel.saveHuddleLoading.value == true) return
        if (viewModel.editHuddleInfoStageValid.value == true) {
            MaterialAlertDialogBuilder(requireContext()).setTitle(getText(R.string.create_huddle_unsaved_alert_title)).setMessage(getText(R.string.create_huddle_unsaved_alert_text))
                .setPositiveButton(getText(R.string.common_ok)) { dialog, _ ->
                    dialog.dismiss()
                    findNavController().popBackStack()
                }.setNegativeButton(getText(R.string.common_cancel)) { dialog, _ ->
                    dialog.dismiss()
                }.show()
        } else {
            findNavController().popBackStack()
        }
    }
}