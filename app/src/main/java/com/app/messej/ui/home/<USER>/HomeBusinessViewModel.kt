package com.app.messej.ui.home.businesstab

import android.app.Application
import android.net.Uri
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.app.messej.R
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import com.yalantis.ucrop.UCrop
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class HomeBusinessViewModel(application: Application) :AndroidViewModel(application) {

    private val profileRepo = ProfileRepository(getApplication())
    private val chatRepo = ChatRepository(getApplication())

    val profileDpClicked = LiveEvent<Boolean>()

    private var imageCaptureTempFile: File? = null

    val uploadDpCompleted = LiveEvent<Boolean?>()

    val dpUploading = MutableLiveData<Boolean>(false)
    val activityChange = MutableLiveData<Boolean>(false)

    fun addCapturedImage(){
        viewModelScope.launch {
            imageCaptureTempFile?.let {
                cropAttachedMedia()
            }
        }
    }

    suspend fun getImageUriForCapture(): Uri = withContext(Dispatchers.IO) {
        val file = chatRepo.createTempImageFile()
        imageCaptureTempFile = file
        chatRepo.getUriForFile(file)
    }

    fun addImage(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            imageCaptureTempFile = file
            cropAttachedMedia()
        }
    }

    fun onActivityUpdated(update:Boolean){
       activityChange.postValue(true)
    }

    fun addCroppedImage(uri: Uri) {
        viewModelScope.launch(Dispatchers.IO) {
            val file = chatRepo.storeImageUriToTempFile(uri)
            imageCaptureTempFile = null
            uploadProfileDp(file)
        }
    }
    fun onCropCancelled() {
        imageCaptureTempFile = null
    }

    val onTriggerCrop = LiveEvent<UCrop>()

    private fun cropAttachedMedia() {
        viewModelScope.launch(Dispatchers.IO) {
            val media = imageCaptureTempFile?: return@launch
            val src = chatRepo.getUriForFile(media)
            val dest = chatRepo.getUriForFile(chatRepo.createTempImageFile())

            val options = UCrop.Options().apply {
                setCircleDimmedLayer(true)
                val color = ContextCompat.getColor(getApplication(), R.color.colorPrimary)
                setToolbarColor(color)
                setStatusBarColor(color)
                setToolbarTitle(getApplication<Application>().applicationContext.getString(R.string.common_crop))
            }
            val crop = UCrop.of(src, dest)
                .withAspectRatio(1f,1f)
                .withOptions(options)

            onTriggerCrop.postValue(crop)
        }
    }
    private fun uploadProfileDp(image: File) {
        dpUploading.postValue(true)
        viewModelScope.launch(Dispatchers.IO){
            val compressed = profileRepo.compressImage(image)
            when(profileRepo.uploadProfileDp(
                file = compressed
            )){
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
                is ResultOf.Success -> {
                    uploadDpCompleted.postValue(true)

                }
            }
            dpUploading.postValue(false)
        }
    }

    private val _currentTab = MutableLiveData<Int?>(null)
    val currentTab: LiveData<Int?> = _currentTab.distinctUntilChanged()

    fun setCurrentTab(tab: Int, skipIfSet: Boolean = false) {
        Log.w("HBVM", "setCurrentTab:$tab, skip: $skipIfSet, current: ${_currentTab.value}")
        if (skipIfSet && currentTab.value != null) return
        _currentTab.value = tab
    }

}