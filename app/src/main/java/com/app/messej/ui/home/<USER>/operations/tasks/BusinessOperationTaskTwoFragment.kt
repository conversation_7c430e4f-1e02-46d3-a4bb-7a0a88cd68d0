import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.databinding.FragmentBussinessOperationTaskTwoBinding
import com.app.messej.ui.customviews.ViewExtensions.setBlurredBackground
import com.app.messej.ui.customviews.ViewExtensions.setBlurredImage
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.businesstab.HomeBusinessFragmentDirections
import com.app.messej.ui.home.businesstab.HomeBusinessViewModel
import com.app.messej.ui.home.businesstab.operations.tasks.BusinessOperationTaskTwoViewModel
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BusinessOperationTaskTwoFragment : Fragment() {

    private lateinit var binding: FragmentBussinessOperationTaskTwoBinding
    private val viewModel: BusinessOperationTaskTwoViewModel by viewModels()
    private val commonViewModel: CommonHomeViewModel by activityViewModels()
    private val mViewModel: HomeBusinessViewModel by activityViewModels()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_bussiness_operation_task_two, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
    }

    override fun onResume() {
        super.onResume()
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                delay(200)
                binding.layoutBusiness.setBlurredBackground(R.drawable.bg_business, requireContext())
            }
        }
    }

    private fun setUp() {
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                delay(200)
                binding.layoutBusiness.setBlurredBackground(R.drawable.bg_business, requireContext())
            }
        }
        binding.imgTaskTwoUnderLine.setBlurredImage(R.drawable.bg_task_one_underline, 10, requireContext())
        binding.shareButton.setOnClickListener {
            shareReferralLink()
//            commonViewModel.countAppShare()
        }
            binding.broadcastButton.setOnClickListener {
            if ((viewModel.businessOperation.value?.fans ?: 0) <= 0) {
                Toast.makeText(requireContext(), getString(R.string.business_broadcast_warning_message), Toast.LENGTH_SHORT).show()
            } else {
                val action = HomeBusinessFragmentDirections.actionGlobalNavigationChatBroadcast(BroadcastMode.ALL_FANS, getString(R.string.broadcast_msg_from_business_customers))
                (activity as MainActivity).navController.navigateSafe(action)
            }
        }
        binding.imageArrowCircle.setOnClickListener {
            commonViewModel.navigateToDearsList.postValue(true)
        }
        binding.actionLearnMore.setOnClickListener { commonViewModel.learnMoreClicked.postValue(true) }
    }

    private fun shareReferralLink() {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(
                Intent.EXTRA_TEXT, getString(R.string.title_operations_share_message, viewModel.user.value?.username, viewModel.user.value?.profile?.referralLink)
            )
            type = "text/plain"
        }
        val shareIntent = Intent.createChooser(sendIntent, null)
        startActivity(shareIntent)
    }
}