package com.app.messej.ui.home.publictab.huddles.huddleInfo

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.repository.HuddlesRepository
import kotlinx.coroutines.launch

class SellHuddlesListViewModel(application: Application) : AndroidViewModel(application) {


    val huddleRepo = HuddlesRepository(application)
    private val _isRemoved = MutableLiveData<Int?>(null)
    private val isRemoved: LiveData<Int?> = _isRemoved

//    val sellHuddlesList = huddleRepo.getSellHuddlesListMediatorPager().liveData.cachedIn(viewModelScope)


    private val isRemoveChecked: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            med.postValue(true)
        }
        med.addSource(isRemoved) { update() }
        med
    }
    val sellHuddlesList = isRemoveChecked.switchMap {
        huddleRepo.getSellHuddlesListMediatorPager().liveData.cachedIn(viewModelScope)
    }

    fun refreshHuddleList(huddleId: Int) {

        deleteFromDb(huddleId)
        Log.d("QQQQ",huddleId.toString())
        _isRemoved.postValue(huddleId)
    }

    fun deleteFromDb(huddleId:Int){
        viewModelScope.launch {
            huddleRepo.deleteBuyHuddle(huddleId)
        }
    }
}