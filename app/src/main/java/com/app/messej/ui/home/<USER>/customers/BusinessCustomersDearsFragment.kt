package com.app.messej.ui.home.businesstab.customers

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.databinding.FragmentBusinessCustomersDearsBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.businesstab.BusinessCustomersViewModel
import com.app.messej.ui.home.businesstab.HomeBusinessFragmentDirections
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView

class BusinessCustomersDearsFragment : Fragment() {

    private lateinit var binding: FragmentBusinessCustomersDearsBinding

    private val viewModel: BusinessCustomersViewModel by activityViewModels()

    private var mAdapter: BusinessCustomerListAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_customers_dears, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        setEmptyView()
        initAdapter()
    }

    private fun observe() {
        viewModel.customersDearsList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
                viewModel.setDearsCount(mAdapter?.itemCount)
            }
        }

        viewModel.onNavigateToPrivateMessage.observe(viewLifecycleOwner) {
            val action = HomeBusinessFragmentDirections.actionGlobalNavigationChatPrivate(it.first,it.second)
            (activity as MainActivity).navController.navigateSafe(action)
        }
        viewModel.onNavigateToProfile.observe(viewLifecycleOwner){
//            val userType = PublicUserProfileLoaderFragment.getUserType(it.second)
            val action = HomeBusinessFragmentDirections.actionGlobalPublicUserProfileFragment(it.first)
            (activity as MainActivity).navController.navigateSafe(action)
        }
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            image = R.drawable.im_eds_dears_list,
            message = R.string.broadcast_list_dears_empty_text,
        )
    }

    private fun initAdapter() {
        mAdapter = BusinessCustomerListAdapter(object: BusinessCustomerListAdapter.ActionListener {
            override fun onProfileClick(item: UserRelative) {
                viewModel.navigateToProfile(item)
            }

            override fun onPrivateMessageClick(item: UserRelative) {
                viewModel.navigateToPrivateMessage(item)
            }

        })

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 &&  loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT

            }
        }

        val layoutMan = LinearLayoutManager(context)
        binding.dearsList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }
    }

}