package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.databinding.FragmentSentFlaxHistoryBinding
import com.app.messej.ui.home.businesstab.adapter.DealsTransactionsPagerAdapter
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.kennyc.view.MultiStateView


class SentFlaxHistoryFragment : Fragment(),DealsTransactionsPagerAdapter.DealsTransactionListener, MenuProvider {
    private lateinit var binding:FragmentSentFlaxHistoryBinding
    private val viewModel: BusinessDealsListViewModel by viewModels()

    private var mAdapter: DealsTransactionsPagerAdapter? = null
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?, ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_sent_flax_history, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()

    }
    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.sent_flax_records)
        viewModel.loadAllData()
    }

    private fun setUp() {
        emptyView()
        viewModel.setTransferType("Sent")
        //  val adapter = BusinesDealsAdapter(true)
        mAdapter = DealsTransactionsPagerAdapter(requireContext(), this,true)

        val layoutManager = LinearLayoutManager(requireContext())
        layoutManager.orientation = LinearLayoutManager.VERTICAL // Set the orientation as needed
        binding.rvFlaxHistoryList.layoutManager = layoutManager
        binding.rvFlaxHistoryList.adapter = mAdapter

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1){
                        MultiStateView.ViewState.EMPTY
                    }else {
                        MultiStateView.ViewState.CONTENT
                    }
                } else {
                    if (itemCount < 1){
                        MultiStateView.ViewState.EMPTY
                    }else {
                        MultiStateView.ViewState.CONTENT
                    }
                }
            }
        }
    }


    private fun observe(){
        viewModel.transferHistoryList.observe(viewLifecycleOwner) { pagingData ->
            mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
        }
        viewModel.isSearchMode.observe(viewLifecycleOwner){
            if(it)emptyView()else searchEmptyView()
        }

    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {

    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            android.R.id.home -> {
                findNavController().popBackStack()
            }
        }
        return true
    }
    private fun emptyView() {
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatTextView>(R.id.deals_empty_title).apply {
                text = resources.getString(R.string.no_sent_flax_history_found)
            }
            findViewById<AppCompatImageView>(R.id.ic_empty_transaction).apply {
                visibility =View.VISIBLE
            }
        }
    }
    private fun searchEmptyView() {
        binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?.apply {
            findViewById<AppCompatImageView>(R.id.ic_empty_transaction).apply {
                visibility =View.GONE
            }
            findViewById<AppCompatTextView>(R.id.deals_empty_title).apply {
                text = resources.getString(R.string.no_sent_flax_history_found)
            }
        }
    }

    override fun onUserClick(item: DealsTransferHistory) {
        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicUserProfileFragment(item.receiverId?:return))
    }

}