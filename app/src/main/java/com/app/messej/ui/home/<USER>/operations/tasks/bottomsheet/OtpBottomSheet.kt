package com.app.messej.ui.home.businesstab.operations.tasks.bottomsheet

import android.annotation.SuppressLint
import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import com.app.messej.R
import com.app.messej.databinding.LayoutOperationsOtpBottomsheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class OtpBottomSheet : BottomSheetDialogFragment() {
    var binding: LayoutOperationsOtpBottomsheetBinding? =null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ):View?{
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_operations_otp_bottomsheet, container, false)
        binding?.lifecycleOwner = viewLifecycleOwner
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        //  binding?.bgBottomSheet?.setBluredBackground(R.drawable.bg_business)
        binding?.btnBottomSheetClose?.setOnClickListener {
            dismissNow()
        }
    }

    @SuppressLint("RestrictedApi")
    override fun setupDialog(dialog: Dialog, style: Int) {

        val modalBottomSheetBehavior = (dialog as BottomSheetDialog).behavior
        //modalBottomSheetBehavior.
       // super.setupDialog(dialog, style)

       //Toast.makeText(requireContext(),modalBottomSheetBehavior.,Toast.LENGTH_SHORT).show()

    }


    companion object {
        const val TAG = "ModalBottomSheet"
    }
}