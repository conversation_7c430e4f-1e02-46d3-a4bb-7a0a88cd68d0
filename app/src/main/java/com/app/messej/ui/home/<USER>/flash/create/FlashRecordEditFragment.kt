package com.app.messej.ui.home.publictab.flash.create

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.graphics.Canvas
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.annotation.ColorRes
import androidx.annotation.OptIn
import androidx.appcompat.widget.AppCompatImageView
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.createBitmap
import androidx.core.view.doOnLayout
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.R
import com.app.messej.data.model.EditableFlashMedia
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentFlashRecordEditBinding
import com.app.messej.databinding.LayoutVideoEncodeProgressCardFullWidthBinding
import com.app.messej.ui.home.publictab.flash.create.FlashRecordViewModel.TextOverlayColor
import com.app.messej.ui.utils.DataFormatHelper.dpToPx
import com.app.messej.ui.utils.FragmentExtensions.afterMeasured
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.RequestOptions
import com.google.android.material.button.MaterialButton
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlin.math.floor
import kotlin.math.roundToInt


class FlashRecordEditFragment : Fragment() {

    private lateinit var binding: FragmentFlashRecordEditBinding

    private val viewModel: FlashRecordViewModel by navGraphViewModels(R.id.nav_flash_record)

    companion object {
        const val SLIDER_MIN = 0f
        const val SLIDER_MAX = 100f
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_record_edit, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setup() {
        binding.closeButton.setOnClickListener { findNavController().popBackStack() }

        binding.composeToolbar.setContent {
            val specs = viewModel.textOverlaySpecs.observeAsState(null).value
            val isColorPickerVisible = viewModel.isColorPickerVisible.observeAsState(false).value

            FlashTextEditingToolbarComposable(
                specs = specs?: FlashRecordViewModel.TextOverlaySpecs(),
                isColorPickerVisible = isColorPickerVisible,

                onToolbarEvent = { event ->
                    when (event) {
                        is ToolbarEvent.BoldToggle -> viewModel.toggleBold()
                        is ToolbarEvent.ItalicToggle -> viewModel.toggleItalic()
                        is ToolbarEvent.UnderlineToggle -> viewModel.toggleUnderline()
                        is ToolbarEvent.ColorClick -> viewModel.toggleColorPicker()
                        is ToolbarEvent.ColorSelected -> {
                            viewModel.updateTextOverlayColor(event.color)
                            viewModel.toggleColorPicker()
                        }
                    }
                }
            )
        }
        binding.composeTextOverlay.setContent {
            val specs = viewModel.textOverlaySpecs.observeAsState(null).value
            val isEditingActive = viewModel.isTextEditingActive.observeAsState(false).value

            TextOverlayComposable(
                specs = specs?: FlashRecordViewModel.TextOverlaySpecs(),
                isEditingActive = isEditingActive,
                onTextChanged = { newText ->
                    viewModel.updateTextOverlayText(newText)
                },
                onOffsetChanged = { newOffset -> viewModel.updateTextOverlayOffset(newOffset) }
            )
        }

        binding.textOverlayAaButton.setOnClickListener {
            viewModel.toggleTextEditing()
        }

        binding.recordFooter.apply {
            doOnLayout {
                val rect = Rect(0,0,width,height)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    systemGestureExclusionRects = mutableListOf(rect)
                }
            }
        }
        binding.trimRangeSlider.apply {
            setCustomThumbDrawable(R.drawable.ic_video_trim_handle)
            values = listOf(SLIDER_MIN,SLIDER_MAX)
            stepSize = 0f
            this.addOnChangeListener { slider, value, fromUser ->
                showTrimOverlay()
                if (fromUser) {
                    Log.w("TRIM", "setTrimRange: value: $value | values: ${slider.values}")
                    viewModel.setTrimRange(slider.values, value==slider.values[0])
                    player?.apply {
                        if(isPlaying) pause()
                        seekTo((duration * value / 100).toLong())
                    }
                }
            }
            afterMeasured {
                binding.trimThumbs.apply {
                    layoutParams = (layoutParams as ViewGroup.MarginLayoutParams).apply {
                        setMargins(binding.trimRangeSlider.trackSidePadding, 0, binding.trimRangeSlider.trackSidePadding, 0)
                    }
                }
                binding.trimRangeStartBg.apply {
                    layoutParams = (layoutParams as ViewGroup.MarginLayoutParams).apply {
                        setMargins(binding.trimRangeSlider.trackSidePadding,0,0,0)
                    }
                }
                binding.trimRangeEndBg.apply {
                    layoutParams = (layoutParams as ViewGroup.MarginLayoutParams).apply {
                        setMargins(0,0,binding.trimRangeSlider.trackSidePadding,0)
                    }
                }
                showTrimOverlay()
            }
        }

        binding.trimPlaySlider.apply {
            setCustomThumbDrawable(R.drawable.ic_video_play_handle)
        }

        binding.muteButton.setOnClickListener {
            viewModel.toggleMute()
        }
        binding.downloadButton.setOnClickListener {
            viewModel.saveVideoToGallery()
        }
        binding.doneButton.setOnClickListener {
            releasePlayer()
            if(viewModel.hasOverlay) {
                commitOverlay()
            }
            findNavController().navigateSafe(FlashRecordEditFragmentDirections.actionFlashRecordEditFragmentToFlashRecordFinalizeFragment())
        }
    }

    private fun commitOverlay() {
        binding.composeTextOverlay.apply {
            measure(
                View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY), View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY)
            )
            layout(0, 0, measuredWidth, measuredHeight)
            try {
                val bitmap = createBitmap(width, height)
                val canvas = Canvas(bitmap)
                draw(canvas)
                viewModel.addTextOverlay(bitmap)
            } catch (e: Exception) {
                Firebase.crashlytics.recordException(e)
            }
        }
    }

    private fun Float.coerceToSliderLimits(): Float {
        return coerceIn(SLIDER_MIN, SLIDER_MAX)
    }

    private fun observe() {

        viewModel.textOverlaySpecs.observe(viewLifecycleOwner){
            it?: return@observe
        }

        viewModel.isTextEditingActive.observe(viewLifecycleOwner) { isActive ->
            binding.composeToolbar.isVisible = isActive
        }

        viewModel.flashRecording.observe(viewLifecycleOwner) { media ->
            media?: return@observe
            if(player != null) releasePlayer()
            setupPlayer(MediaItem.fromUri(media.path))
            loadThumbnails(media)
            binding.trimRangeSlider.setLabelFormatter {
                val ms = media.meta.durationMs*it/100
                DateTimeUtils.formatSeconds(ms/1000)
            }
            binding.playerHolder.apply {
                (layoutParams as ConstraintLayout.LayoutParams).dimensionRatio = if (media.isFromGallery) (9f/16).toString() else media.meta.resolution.aspectRatio.toString()
            }
        }
        viewModel.flashEdits.observe(viewLifecycleOwner) {
            it?: return@observe
            player?.volume = if(it.mute) 0f else 1f
            it.trim?.let { trim ->
                val dur = viewModel.flashRecording.value?.meta?.durationMs?.toFloat()?: return@let
                val trimPerc = listOf((trim.startMs/dur*100).coerceToSliderLimits(), (trim.endMs/dur*100).coerceToSliderLimits())
                if (trimPerc != binding.trimRangeSlider.values) {
                    Firebase.crashlytics.log("Update slider trim: $trimPerc | trim: $trim | dur: $dur")
                    binding.trimRangeSlider.values = trimPerc
                }
            }
            viewModel.flashRecording.value?.let { rec ->
                val msDur = (it.trim?.trimDurationMs?:rec.meta.durationMs)
                binding.txtStartDuration.text = DateTimeUtils.formatSeconds(msDur/1000)
            }
        }

        viewModel.videoSaveProgress.observe(viewLifecycleOwner) {
            if (it==null) {
                saveLoader?.dismiss()
                saveLoader = null
                saveLoaderBinding = null

            } else {
                if (saveLoader==null) {
                    saveLoader = MaterialDialog(requireContext()).show {
                        val view = DataBindingUtil.inflate<LayoutVideoEncodeProgressCardFullWidthBinding>(layoutInflater, R.layout.layout_video_encode_progress_card_full_width, null, false)
                        customView(null, view.root, dialogWrapContent = true)
                        cancelable(false)
                        saveLoaderBinding = view
                    }
                }
                saveLoaderBinding?.apply {
                    text = if (it.first==FlashRecordViewModel.VideoSaveStage.ENCODING) resources.getString(R.string.chat_video_encoding_progress,it.second)
                    else resources.getString(R.string.flash_save_progress_saving)
                    progress = it.second
                }

            }
        }
        viewModel.flashVideoProgress.observe(viewLifecycleOwner) { prog ->
            saveLoaderBinding?.let {
                it.progress = (prog?:0).coerceAtLeast(0)
            }
        }
        viewModel.onSavedToGallery.observe(viewLifecycleOwner) {
            showSnackbar(R.string.flash_saved_to_gallery_toast)
        }
    }

    private var saveLoader: MaterialDialog? = null
    private var saveLoaderBinding: LayoutVideoEncodeProgressCardFullWidthBinding? = null

    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }

    private var player: ExoPlayer? = null

    private fun releasePlayer() {
        player?.apply {
            stop()
            release()
            player = null
        }
    }

    private fun showTrimOverlay() {
        val slider = binding.trimRangeSlider
        val values = slider.values
        val max = 100f
        val start = values.getOrElse(0) { 0f }
        val end = values.getOrElse(1) { 100f }
        val rangeFactor = (end-start)/max
        val startFactor = start/max
        val endFactor = end/max

        val overlayOffset = resources.getDimensionPixelSize(R.dimen.video_trim_seekbar_overlay_thickness)
        binding.trimRangeBg.apply {
            isVisible = true
            layoutParams = layoutParams.apply {
                width = (slider.trackWidth*rangeFactor).roundToInt()
            }
            translationX = ViewUtils.localeAwareTranslation((slider.trackWidth*startFactor) + slider.trackSidePadding)
        }
        binding.trimRangeStartBg.apply {
            layoutParams = layoutParams.apply {
                width = (slider.trackWidth*startFactor).roundToInt() + overlayOffset
            }
        }
        binding.trimRangeEndBg.apply {
            layoutParams = layoutParams.apply {
                width = (slider.trackWidth*(1-endFactor)).roundToInt() + overlayOffset
            }
        }
    }

    private var animator: ValueAnimator? = null

    private fun cancelProgressPolling() {
        animator?.let {
            it.cancel()
            animator = null
        }
    }
    private fun startProgressPolling(millis: Long) {
        cancelProgressPolling()
        animator = ValueAnimator.ofFloat(0f, 1f).apply {
            duration = millis.coerceAtLeast(0)
            interpolator = LinearInterpolator()
            addUpdateListener { _ ->
                val current = player?.currentPosition?:return@addUpdateListener
                val dur = player?.duration?: return@addUpdateListener
                Firebase.crashlytics.log("Update play slider current: $current | dur: $dur | value: ${(current.toFloat()/dur*100).coerceToSliderLimits()}")
                binding.trimPlaySlider.value = (current.toFloat()/dur*100).coerceToSliderLimits()
                viewModel.flashEdits.value?.trim?.endMs?.let {  endMs ->
                    if (current >= endMs) {
                        if (player?.isPlaying==true) {
                            player?.pause()
                        } else cancelProgressPolling()
                    }
                }
            }
            start()
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer(med: MediaItem? = null) {
        if (player == null) {
            player = ExoPlayer.Builder(requireContext()).build().apply {
                binding.playerView.player = this
                binding.playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM

                val playButton = binding.playerView.findViewById<MaterialButton>(R.id.exo_play_pause_custom)
                playButton.setOnClickListener {
                    if (isPlaying) {
                        pause()
                    } else {
                        seekTo(viewModel.flashEdits.value?.trim?.startMs?:0)
                        play()
                    }
                }
                addListener(object : Player.Listener {
                    override fun onIsPlayingChanged(isPlaying: Boolean) {
                        Log.w("FREF", "onIsPlayingChanged: $isPlaying")
                        if (isPlaying) {
                            playButton.setIconResource(R.drawable.ic_media_stop_large)
                            val remainingMs = duration-currentPosition
                            startProgressPolling(remainingMs)
                        } else {
                            playButton.setIconResource(R.drawable.ic_media_play_large)
                            cancelProgressPolling()
                        }
                    }
                })
            }
        }
        player?.apply {
            clearMediaItems()
            med?.let {
                setMediaItem(it)
            }
            prepare()
//            play()
        }
    }

    private fun loadThumbnails(media: EditableFlashMedia) {
        try {
            binding.thumbsHolder.removeAllViews()
            val thumbCount = (resources.displayMetrics.widthPixels.toFloat()/64.dpToPx(requireContext())).roundToInt()
            val millis = media.meta.durationMs
            val frameStep = (millis.toFloat()/thumbCount)

            for(i in 0 until thumbCount) {
                val thumbView = layoutInflater.inflate(R.layout.layout_flash_video_trim_thumbnail,binding.thumbsHolder,false) as AppCompatImageView
                val frameTimeMicros = floor(frameStep*i).toLong()*1000
                val options = RequestOptions().frame(frameTimeMicros)
                binding.thumbsHolder.addView(thumbView)
                Glide.with(this).load(media.path).apply(options)
                    .transition(DrawableTransitionOptions.withCrossFade(300))
//                    .diskCacheStrategy(DiskCacheStrategy.NONE)
//                    .skipMemoryCache(true)
                    .into(thumbView)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

private val TextOverlayColor.colorRes: Int
    @ColorRes
    get()=when(this){
        TextOverlayColor.WHITE -> R.color.white
        TextOverlayColor.RED -> R.color.textColorBusinessRed
        TextOverlayColor.GREEN -> R.color.colorBusinessGreen
        TextOverlayColor.BLACK -> R.color.black
        TextOverlayColor.YELLOW -> R.color.textColorBusinessYellow
        else -> R.color.textColorOnPrimary
    }

@Preview
@Composable
fun TextOverlayComposablePreview(
){
    TextOverlayComposable(
        FlashRecordViewModel.TextOverlaySpecs(
            "Hello World testing here please check ",
            TextOverlayColor.RED,
            bold = true,
            italic = false,
            underline = false,
            offset = Offset(9F, 0F)
        ),
        isEditingActive = true,
        onTextChanged = {},
        onOffsetChanged = {}
    )
}

@Composable
fun TextOverlayComposable(
    specs: FlashRecordViewModel.TextOverlaySpecs,
    isEditingActive: Boolean,
    onTextChanged: (String) -> Unit,
    onOffsetChanged: (Offset) -> Unit
) {

    var textFieldValue by remember { mutableStateOf(TextFieldValue(text = specs.text)) }
    var currentOffset by remember { mutableStateOf(specs.offset) }

    LaunchedEffect(specs.text) {
        if (specs.text != textFieldValue.text) {

            val newText = specs.text
            val cursor = textFieldValue.selection.end.coerceIn(0, newText.length)
            textFieldValue = TextFieldValue(text = newText, selection = TextRange(cursor))
        }
    }

    val baseTextSize = 20.sp
    val baseLayoutWidth = 300.dpToPx(LocalContext.current)
    val baseTextPadding = 16.dp
    var textSize by remember { mutableStateOf(baseTextSize) }
    var textPadding by remember { mutableStateOf(baseTextPadding) }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .wrapContentHeight(align = Alignment.CenterVertically)
            .onSizeChanged{size ->
                val factor = size.width.toFloat()/baseLayoutWidth
                textPadding = baseTextPadding *factor
                Log.i("FREF","factor: $factor")
                textSize = baseTextSize*factor
            }

        , contentAlignment = Alignment.Center
    ) {
        val dynamicTextStyle = TextStyle(
            color = colorResource(specs.color.colorRes),
            fontSize = textSize,
            fontFamily = FontFamily(listOf(Font(R.font.nunito_regular))),
            textAlign = TextAlign.Center,
            fontWeight = if (specs.bold) FontWeight.Bold else FontWeight.Normal,
            fontStyle = if (specs.italic) FontStyle.Italic else FontStyle.Normal,
            textDecoration = if (specs.underline) TextDecoration.Underline else TextDecoration.None
        )

        if (isEditingActive) {

            BasicTextField(
                value = textFieldValue, onValueChange = { newValue ->
                    if(newValue.text.length <= 50) { // word limit
                        textFieldValue = newValue
                        onTextChanged(newValue.text)
                    }
                }, textStyle = dynamicTextStyle,
                modifier = Modifier
                    .offset { IntOffset(currentOffset.x.roundToInt(), currentOffset.y.roundToInt()) }
                    .pointerInput(Unit) {
                        detectDragGestures { change, dragAmount ->
                            change.consume()
                            currentOffset = currentOffset.plus(dragAmount)
                            onOffsetChanged(currentOffset)
                        }
                    }

                    .fillMaxWidth(0.9f)
                    .wrapContentHeight()
                    .padding(textPadding),
                decorationBox = { innerTextField ->
                    if (textFieldValue.text.isEmpty()) {
                        Text(
                            text = stringResource(R.string.flash_edit_text), style = dynamicTextStyle.copy(color = dynamicTextStyle.color.copy(alpha = 0.5f)),
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                    innerTextField()
                }
            )
        } else if (specs.text.isNotBlank()) {

            Text(
                text = specs.text,
                style = dynamicTextStyle,
                modifier = Modifier
                    .offset { IntOffset(currentOffset.x.roundToInt(), currentOffset.y.roundToInt()) }
                    .fillMaxWidth(0.9f)
                    .wrapContentHeight()
                    .padding(textPadding)
            )
        }
    }
}

@Preview
@Composable
fun FlashTextEditingToolbarComposablePreview(
){
    FlashTextEditingToolbarComposable(
        FlashRecordViewModel.TextOverlaySpecs(
            "Hello World testing here please check ",
            TextOverlayColor.RED,
            bold = true,
            italic = true,
            underline = false,
            offset = Offset(0F, 0F)
        ),
        isColorPickerVisible = true,
        onToolbarEvent = {}
    )
}

@Composable
fun FlashTextEditingToolbarComposable(
    specs: FlashRecordViewModel.TextOverlaySpecs,
    isColorPickerVisible: Boolean,
    onToolbarEvent: (ToolbarEvent) -> Unit,
) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Row(
            modifier = Modifier
                .wrapContentWidth()
                .background(colorResource(R.color.colorAlwaysDarkSurface).copy(alpha = 0.5f), RoundedCornerShape(8.dp))
                .padding(all = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                painterResource(id = R.drawable.ic_text_bold),
                contentDescription = "Bold",
                tint = if (specs.bold) Color.Yellow else Color.White,
                modifier = Modifier
                    .background(
                        color = if (specs.bold) Color.White.copy(alpha = 0.1f) else Color.Transparent, shape = RoundedCornerShape(6.dp)
                    )
                    .padding(horizontal = 4.dp, vertical = 4.dp)
                    .size(24.dp)
                    .clickable { onToolbarEvent(ToolbarEvent.BoldToggle) }
            )
            Icon(
                painterResource(id = R.drawable.ic_text_italic),
                contentDescription = "Italic",
                tint = if (specs.italic) Color.Yellow else Color.White,
                modifier = Modifier
                    .background(
                        color = if (specs.italic) Color.White.copy(alpha = 0.2f) else Color.Transparent, shape = RoundedCornerShape(6.dp)
                    )
                    .padding(horizontal = 4.dp, vertical = 4.dp)
                    .size(24.dp)
                    .clickable { onToolbarEvent(ToolbarEvent.ItalicToggle) }

            )

            Icon(
                painterResource(id = R.drawable.ic_text_underline),
                contentDescription = "Underline",
                tint = if (specs.underline) Color.Yellow else Color.White,
                modifier = Modifier
                    .background(
                        color = if (specs.underline) Color.White.copy(alpha = 0.2f) else Color.Transparent, shape = RoundedCornerShape(6.dp)
                    )
                    .padding(horizontal = 4.dp, vertical = 4.dp)
                    .size(24.dp)
                    .clickable { onToolbarEvent(ToolbarEvent.UnderlineToggle) })

            Icon(
                painterResource(id = R.drawable.ic_color_circle),
                contentDescription = "ColourCircle",
                tint = Color.Unspecified,
                modifier = Modifier
                    .background(
                        color = if (isColorPickerVisible) Color.White.copy(alpha = 0.2f) else Color.Transparent, shape = RoundedCornerShape(6.dp)
                    )
                    .padding(horizontal = 4.dp, vertical = 4.dp)
                    .size(24.dp)
                    .clickable { onToolbarEvent(ToolbarEvent.ColorClick) })
        }

        if (isColorPickerVisible) {
            Row(
                modifier = Modifier
                    .padding(4.dp)
                    .wrapContentWidth()
                    .background(colorResource(R.color.colorAlwaysDarkSurface).copy(alpha = 0.5f), RoundedCornerShape(50))
                    .padding(all= 8.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TextOverlayColor.entries.forEach { overlayColor ->
                    ColorSwatch(
                        color = colorResource(id = overlayColor.colorRes)
                    ) { onToolbarEvent(ToolbarEvent.ColorSelected(overlayColor)) }
                }
            }
        }
    }
}

@Composable
fun ColorSwatch(
    color: Color,
    onClick: () -> Unit,
) {
    Box(
        modifier = Modifier
            .size(32.dp)
            .clip(CircleShape)
            .background(color)
            .border(1.dp,colorResource(R.color.colorAlwaysDarkSurfaceSecondaryDarker), CircleShape)
            .clickable(onClick = onClick)
    )
}

sealed class ToolbarEvent {
    object BoldToggle : ToolbarEvent()
    object ItalicToggle : ToolbarEvent()
    object UnderlineToggle : ToolbarEvent()
    object ColorClick : ToolbarEvent()
    data class ColorSelected(val color: TextOverlayColor) : ToolbarEvent()
}
