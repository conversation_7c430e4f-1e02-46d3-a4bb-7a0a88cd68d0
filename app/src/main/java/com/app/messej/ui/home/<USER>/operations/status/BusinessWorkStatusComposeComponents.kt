package com.app.messej.ui.home.businesstab.operations.status

import android.content.Context
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.data.model.enums.BusinessWorkStatusType
import com.app.messej.data.model.status.AppRating
import com.app.messej.data.model.status.BusinessActivityStatus
import com.app.messej.ui.composeComponents.CustomLargeRoundButton
import com.app.messej.ui.composeComponents.CustomLinearProgressIndicatorWithProgress
import com.app.messej.ui.composeComponents.CustomSmallOutlinedButton
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.composeComponents.ShimmerDefaultCircleItem
import com.app.messej.ui.composeComponents.ShimmerDefaultItem
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.app.messej.ui.utils.EnumUtils.displayText
import java.util.Locale

@Composable
fun WorkStatusSingleItem(
    type: BusinessWorkStatusType,
    activityStatus: BusinessActivityStatus?,
    onInfoClick: () -> Unit,
    onUploadScreenshot: () -> Unit
) {
    val context = LocalContext.current
    val shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))
    val isCompleted = type.isTaskCompleted(activityStatus = activityStatus)
    val title = stringResource(id = type.setTitle())

    Column(
        modifier = Modifier
            .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
            .shadow(elevation = dimensionResource(id = R.dimen.element_spacing), shape = shape)
            .background(color = colorResource(id = R.color.buttonTabBackground), shape = shape)
            .padding(vertical = dimensionResource(id = R.dimen.element_spacing))
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = if (type == BusinessWorkStatusType.ETribe) title else title.uppercase(Locale.getDefault()),
                style = FlashatComposeTypography.captionBold,
                color = colorResource(id = R.color.colorPrimaryDarkest),
                modifier = Modifier
                    .background(
                        color = colorResource(id = R.color.colorPrimaryLight).copy(alpha = 0.55F),
                        shape = RoundedCornerShape(topEnd = dimensionResource(id = R.dimen.element_spacing))
                    )
                    .padding(vertical = dimensionResource(id = R.dimen.line_spacing), horizontal = dimensionResource(id = R.dimen.activity_margin))
            )
            Icon(
                modifier = Modifier
                    .clip(shape = CircleShape)
                    .padding(end = dimensionResource(id = R.dimen.activity_margin))
                    .clickable(enabled = !isCompleted) { onInfoClick() }
                    .size(size = 20.dp),
                painter = painterResource(id = if (isCompleted) R.drawable.ic_business_tick else R.drawable.ic_restore_info),
                tint = colorResource(id = if (isCompleted) R.color.colorPass else R.color.colorPrimary),
                contentDescription = null
            )
        }
        Row (
            modifier = Modifier
                .padding(all = dimensionResource(id = R.dimen.activity_margin))
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                modifier = Modifier.size(size = dimensionResource(id = R.dimen.extra_margin)),
                painter = painterResource(id = type.setIcon()),
                tint = Color.Unspecified,
                contentDescription = null
            )
            Column(
                modifier = Modifier
                    .padding(start = dimensionResource(id = R.dimen.activity_margin))
                    .fillMaxWidth()
            ) {
                Text(
                    text = type.setDescriptionText(
                        context = context,
                        activityStatus = activityStatus
                    ),
                    style = FlashatComposeTypography.captionBold,
                    color = colorResource(id = R.color.textColorSecondary)
                )
                when(type) {
                    BusinessWorkStatusType.Profile -> {
                        Text(
                            text = stringResource(id = R.string.common_percentage_value, "${activityStatus?.profileCompleteness?.currentPercentage ?: 0}"),
                            modifier = Modifier.align(alignment = Alignment.End),
                            style = FlashatComposeTypography.overLineSmaller,
                            color = colorResource(id = R.color.textColorSecondary)
                        )
                        CustomLinearProgressIndicatorWithProgress(
                            progress = (activityStatus?.profileCompleteness?.currentPercentage ?: 0).toFloat() / 100
                        )
                    }
                    BusinessWorkStatusType.Huddle -> {
                        Text(
                            text = stringResource(id = R.string.business_huddle_created_count, "${activityStatus?.huddles?.currentHuddlesCount ?: 0}"),
                            style = FlashatComposeTypography.captionBold,
                            modifier = Modifier
                                .padding(top = dimensionResource(id = R.dimen.element_spacing))
                                .clip(shape = RoundedCornerShape(size = 10.dp))
                                .background(color = colorResource(id = R.color.colorPodiumSpeakerAmbassador))
                                .padding(
                                    horizontal = dimensionResource(id = R.dimen.element_spacing), vertical = dimensionResource(id = R.dimen.line_spacing)
                                ),
                            color = colorResource(id = R.color.colorAlwaysLightPrimaryDarker)
                        )
                    }
                    BusinessWorkStatusType.RateFlashat -> {
                        if (!isCompleted) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = dimensionResource(id = R.dimen.element_spacing)),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                if (activityStatus?.appRating?.status == AppRating.RatingStatus.NEW) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_business_in_review),
                                        tint = colorResource(id = R.color.colorPrimary),
                                        contentDescription = null,
                                        modifier = Modifier.size(size = dimensionResource(id = R.dimen.activity_margin))
                                    )
                                    Text(
                                        modifier = Modifier.padding(start = dimensionResource(id = R.dimen.element_spacing)),
                                        text = stringResource(id = R.string.business_rate_review_sub_title),
                                        color = colorResource(id = R.color.colorPrimary),
                                        style = FlashatComposeTypography.overLineBold,
                                    )

                                    CustomHorizontalSpacer(space = dimensionResource(id = R.dimen.line_spacing))
                                    return@Row
                                }

                                CustomSmallOutlinedButton(
                                    text = stringResource(id = R.string.file_a_case_upload_file),
                                    contentColor = colorResource(id = R.color.textColorSecondary),
                                    iconColor = colorResource(id = R.color.textColorSecondary),
                                    trailingIcon = R.drawable.ic_caret_right,
                                    onClick = onUploadScreenshot
                                )
                            }
                        }
                    }
                    else -> {}
                }
            }
        }
    }
}

@StringRes
fun BusinessWorkStatusType.setTitle(): Int = when(this) {
        BusinessWorkStatusType.AccountStatus -> R.string.business_task_title_account_status
        BusinessWorkStatusType.Profile -> R.string.bottom_sheet_profile
        BusinessWorkStatusType.Huddle -> R.string.business_task_title_account_huddle
        BusinessWorkStatusType.ETribe -> R.string.business_task_title_account_e_tribe
        BusinessWorkStatusType.RateFlashat -> R.string.business_task_title_account_rate_flashat
        BusinessWorkStatusType.PerformanceRating -> R.string.business_task_title_account_performance_rating
        BusinessWorkStatusType.MinimumBalance -> R.string.business_task_title_account_minimum_balance
        BusinessWorkStatusType.Generosity -> R.string.business_task_title_account_generosity

}

@DrawableRes
fun BusinessWorkStatusType.setIcon() : Int = when(this) {
    BusinessWorkStatusType.AccountStatus -> R.drawable.ic_task_account_status
    BusinessWorkStatusType.Profile -> R.drawable.ic_task_status_profile
    BusinessWorkStatusType.Huddle -> R.drawable.ic_task_status_huddle
    BusinessWorkStatusType.ETribe -> R.drawable.ic_task_status_e_tribe
    BusinessWorkStatusType.RateFlashat -> R.drawable.ic_task_status_rate_flashat
    BusinessWorkStatusType.PerformanceRating -> R.drawable.ic_task_status_performance
    BusinessWorkStatusType.MinimumBalance -> R.drawable.ic_task_status_minimum_balance
    BusinessWorkStatusType.Generosity -> R.drawable.ic_task_status_generosity
}

fun BusinessWorkStatusType.isTaskCompleted(activityStatus: BusinessActivityStatus?) : Boolean {
    return when(this) {
        BusinessWorkStatusType.AccountStatus -> activityStatus?.accountComplete?.isSatisfied == true
        BusinessWorkStatusType.Profile -> activityStatus?.isProfileCompleted == true
        BusinessWorkStatusType.Huddle -> activityStatus?.huddles?.isSatisfied == true
        BusinessWorkStatusType.ETribe -> activityStatus?.eTribe?.isSatisfied == true
        BusinessWorkStatusType.RateFlashat -> activityStatus?.appRating?.isSatisfied == true
        BusinessWorkStatusType.PerformanceRating -> activityStatus?.userRating?.isSatisfied == true
        BusinessWorkStatusType.MinimumBalance -> activityStatus?.flaxBalance?.isSatisfied == true
        BusinessWorkStatusType.Generosity -> activityStatus?.generosity?.isSatisfied == true
    }
}

fun BusinessWorkStatusType.setDescriptionText(
    context: Context,
    activityStatus: BusinessActivityStatus?
) : AnnotatedString {
    return buildAnnotatedString {
        when(this@setDescriptionText) {
            BusinessWorkStatusType.AccountStatus -> {
                append(context.getString(R.string.business_task_account_status_level_text))
                activityStatus?.citizenship?.let {
                    withStyle(style = SpanStyle(fontWeight = FontWeight.Bold, color = Color(context.getColor(R.color.colorPrimary)))) {
                        append(" " + context.getString(it.displayText()).uppercase(Locale.getDefault()))
                    }
                    if (!activityStatus.citizenship.isFreeType) {
                        append("\n" + context.getString(R.string.business_task_account_status_premium_expire_date, activityStatus.accountComplete?.expiryDate ?: "" ))
                    }
                }
            }
            BusinessWorkStatusType.Profile -> {
                append(context.getString(R.string.business_task_profile_completion, "${activityStatus?.profileCompleteness?.currentPercentage ?: 0}"))
            }
            BusinessWorkStatusType.Huddle -> {
                append(context.getString(R.string.business_task_huddle_description, "${activityStatus?.huddles?.requiredHuddleCount ?: 0}"))
            }
            BusinessWorkStatusType.ETribe -> {
                val eTribe = activityStatus?.eTribe
                append(context.getString(R.string.business_task_e_tribe_description, eTribe?.userRatingNeeded.formatDecimalWithRemoveTrailingZeros(), "${eTribe?.activeDearsCount ?: 0}"))
            }
            BusinessWorkStatusType.RateFlashat -> {
                append(context.getString(R.string.business_task_rate_flashat_description))
            }
            BusinessWorkStatusType.PerformanceRating -> {
                append(context.getString(R.string.business_task_current_rating_description, activityStatus?.userRating?.currentUserRating.formatDecimalWithRemoveTrailingZeros()))
                append("\n" + context.getString(R.string.business_task_rating_description))
            }
            BusinessWorkStatusType.MinimumBalance -> {
                val flixDetails = activityStatus?.flaxBalance
                if (flixDetails?.isSocialSatisfied == true) {
                    append(context.getString(R.string.business_task_primary_flix_balance_description,flixDetails.currentFlaxBalance.formatDecimalWithRemoveTrailingZeros()))
                    append("\n" + context.getString(R.string.business_task_primary_coin_balance_description,flixDetails.currentCoinBalance.formatDecimalWithRemoveTrailingZeros()))
                    append("\n" + context.getString(R.string.business_task_social_support_balance_description,flixDetails.socialFlaxBalance.formatDecimalWithRemoveTrailingZeros()))
                    append("\n" + context.getString(R.string.business_task_total_primary_flix_description,flixDetails.effectiveFlixBalance.formatDecimalWithRemoveTrailingZeros()))
                } else {
                    append(context.getString(R.string.business_task_minimum_flix_balance_description, flixDetails?.currentFlaxBalance.formatDecimalWithRemoveTrailingZeros()))
                    append("\n" + context.getString(R.string.business_task_coin_balance_description, flixDetails?.currentCoinBalance.formatDecimalWithRemoveTrailingZeros()))
                    append("\n" + context.getString(R.string.business_task_total_flix_description, flixDetails?.effectiveFlixBalance.formatDecimalWithRemoveTrailingZeros()))
                }
            }
            BusinessWorkStatusType.Generosity -> {
                val generosity = activityStatus?.generosity
                append(context.getString(R.string.business_task_generosity_gained_coin_description, generosity?.totalGainedCoins.formatDecimalWithRemoveTrailingZeros()))
                append("\n" + context.getString(R.string.business_task_generosity_gave_away_coin_description, generosity?.totalGivenCoins.formatDecimalWithRemoveTrailingZeros()))
                append("\n" + context.getString(R.string.business_task_generosity_give_away_percentage_description, generosity?.givenCoinPercentage.formatDecimalWithRemoveTrailingZeros()))
                append("\n" + context.getString(R.string.business_task_generosity_gift_total_coins_description, generosity?.totalCoinObligation.formatDecimalWithRemoveTrailingZeros()))
            }
        }
    }
}

@Composable
fun BusinessWorkStatusOfferFlixView(
    modifier: Modifier = Modifier,
    onOfferFlixForSaleClick : () -> Unit,
    onEligibilityInfoClick: () -> Unit,
    activityStatus: BusinessActivityStatus?
) {
    val isEligible = activityStatus?.eligibility == true
    Column(
        modifier = modifier
            .shadow(elevation = dimensionResource(id = R.dimen.element_spacing))
            .background(color = colorResource(id = R.color.buttonTabBackground))
            .padding(all = dimensionResource(id = R.dimen.activity_margin))
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = if (isEligible) R.drawable.ic_fire_work else R.drawable.ic_close_circled),
                modifier = Modifier.size(size = dimensionResource(id = R.dimen.extra_margin)),
                tint = Color.Unspecified,
                contentDescription = null
            )
            Text(
                text = if(isEligible) stringResource(R.string.business_task_eligible_text)
                        else if(activityStatus?.eligibilityInDays?.isSatisfied == false)  stringResource(R.string.title_business_days,(activityStatus.eligibilityInDays.requiredDays?:0).toString())
                            else stringResource(R.string.business_task_not_eligible_text),
                modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.element_spacing)),
                style = FlashatComposeTypography.defaultType.h6,
                color = colorResource(id = R.color.textColorPrimary)
            )
            Icon(
                painter = painterResource(id = R.drawable.ic_gift_info),
                modifier = Modifier.clip(shape = CircleShape).clickable { onEligibilityInfoClick() },
                tint = colorResource(id = R.color.colorPrimary),
                contentDescription = null
            )
        }

        CustomVerticalSpacer(space = dimensionResource(id = R.dimen.activity_margin))
        CustomLargeRoundButton(
            isTextCaps = false,
            text = R.string.business_task_offer_your_flix_for_sale,
            isEnabled = isEligible,
            onClick = onOfferFlixForSaleClick
        )
    }
}

@Composable
fun WorkStatusShimmerItem(
    brush: Brush
) {
    Column(
        modifier = Modifier
            .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
            .background(color = colorResource(id = R.color.colorSurfaceSecondaryDark), shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .padding(vertical = dimensionResource(id = R.dimen.element_spacing))
            .fillMaxWidth()
    ) {
        ShimmerDefaultItem(brush = brush, shape = RoundedCornerShape(topEnd = dimensionResource(id = R.dimen.element_spacing)))
        Row (
            modifier = Modifier.padding(all = dimensionResource(id = R.dimen.activity_margin)).fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            ShimmerDefaultCircleItem(brush = brush, size = dimensionResource(id = R.dimen.double_margin))
            Column(
                modifier = Modifier
                    .padding(start = dimensionResource(id = R.dimen.activity_margin))
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing))
            ) {
                ShimmerDefaultItem(brush = brush, fraction = 0.75F)
                ShimmerDefaultItem(brush = brush, fraction = 0.55F)
            }
        }
    }
}
