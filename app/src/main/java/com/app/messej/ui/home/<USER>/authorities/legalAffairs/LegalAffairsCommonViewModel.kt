package com.app.messej.ui.home.publictab.authorities.legalAffairs

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.data.model.enums.LegalAffairsMainTab
import com.app.messej.data.model.enums.LegalAffairsViolationSubTab
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalAffairsRepository
import com.app.messej.data.utils.EnumUtil.except
import kotlinx.coroutines.flow.map

class LegalAffairsCommonViewModel(application: Application) : AndroidViewModel(application) {

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val tag = "LABVM"

    private val legalAffairsRepository = LegalAffairsRepository(application)
    val bannersList = accountRepo.userFlow.map {
        it?: return@map emptyList<LegalAffairsMainTab>()
        var list = LegalAffairsMainTab.entries.except(LegalAffairsMainTab.MyLegalRecords)
        if (it.citizenship.isBelowAmbassador || it.citizenship.isGolden) {
            list = list.except(LegalAffairsMainTab.Jury)
        }
        return@map list.toList()
    }

    private val _legalAffairsMainTab = MutableLiveData(LegalAffairsMainTab.MyLegalRecords)
    val legalAffairsMainTab : LiveData<LegalAffairsMainTab> = _legalAffairsMainTab.distinctUntilChanged()

    private val _currentSelectedLegalAffairsSubTab = MutableLiveData(LegalAffairTabs.Violations)
    val currentSelectedLegalAffairsSubTab : LiveData<LegalAffairTabs> = _currentSelectedLegalAffairsSubTab.distinctUntilChanged()

    private val _currentSelectedLegalAffairsViolationSubTab = MutableLiveData(LegalAffairsViolationSubTab.Open)
    val currentSelectedLegalAffairsViolationSubTab : LiveData<LegalAffairsViolationSubTab> = _currentSelectedLegalAffairsViolationSubTab.distinctUntilChanged()

    val myLegalRecordsCount = MutableLiveData<LegalRecordsResponse?>(null)

    private val _investigationBureauCount = MutableLiveData<LegalRecordsResponse?>(null)
    val investigationBureauCount : LiveData<LegalRecordsResponse?> = _investigationBureauCount

    init {
        Log.d(tag,"Init Block")
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(tag,"Viewmodel cleared")
    }

    fun setLegalAffairSubTab(tab : LegalAffairTabs) {
        if (_currentSelectedLegalAffairsSubTab.value == tab) return
        _currentSelectedLegalAffairsSubTab.value = tab
    }

    fun setLegalAffairsMainTab(tab: LegalAffairsMainTab) {
        _legalAffairsMainTab.value = tab
        setLegalAffairSubTab(
            tab = if (tab == LegalAffairsMainTab.InvestigationBureau) LegalAffairTabs.Bans
            else LegalAffairTabs.Violations
        )
    }

    fun setViolationSubTab(tab: LegalAffairsViolationSubTab) {
        _currentSelectedLegalAffairsViolationSubTab.value = tab
        Log.d(tag,"Violation Sub Tab Changed -> $tab")
    }

    val violationList = _currentSelectedLegalAffairsViolationSubTab.switchMap { status ->
        if (status == LegalAffairsViolationSubTab.Statistics) return@switchMap MutableLiveData()
        Log.d(tag, "Inside violation tab switch map")
        legalAffairsRepository.getViolationList(
            recordType = LegalAffairTabs.Violations.serializedName(),
            status = status.serializedName(),
            reportingType = null,
            countCallBack = {
                myLegalRecordsCount.postValue(
                    LegalRecordsResponse(
                        violationsCount = it?.violationsCount,
                        banCount = it?.banCount,
                        reportingCount = it?.reportingCount
                    )
                )
            },
        ).liveData.cachedIn(viewModelScope)
    }

    fun setInvestigationBureauCount(item: LegalRecordsResponse?) {
        _investigationBureauCount.postValue(
            LegalRecordsResponse(
                banCount = item?.banCount,
                reportsCount = item?.reportsCount,
                hiddenCount = item?.hiddenCount
            )
        )
    }
}

