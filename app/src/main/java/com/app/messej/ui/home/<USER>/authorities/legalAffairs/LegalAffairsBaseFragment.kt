package com.app.messej.ui.home.publictab.authorities.legalAffairs

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import com.app.messej.R
import com.app.messej.data.model.enums.LegalAffairTabs
import com.app.messej.databinding.FragmentBaseLegalAffairsBinding
import com.app.messej.ui.home.publictab.authorities.legalAffairs.ban.BanFragment
import com.app.messej.ui.home.publictab.authorities.legalAffairs.hidden.HiddenFragment
import com.app.messej.ui.home.publictab.authorities.legalAffairs.reportings.LegalAffairsReportingFragment
import com.app.messej.ui.home.publictab.authorities.legalAffairs.violations.ViolationsFragment

abstract class LegalAffairsBaseFragment : Fragment() {

    protected abstract var commonBinding: FragmentBaseLegalAffairsBinding
    protected abstract val viewModel : LegalAffairsCommonViewModel

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }

    fun setUp() {
        commonBinding.viewModel = <EMAIL>
        setupButtonClickListeners()
    }

    private fun setupButtonClickListeners() {
        commonBinding.apply {
            buttonViolations.cardView.setOnClickListener {
                viewModel?.setLegalAffairSubTab(tab = LegalAffairTabs.Violations)
            }
            buttonBan.cardView.setOnClickListener {
                viewModel?.setLegalAffairSubTab(tab = LegalAffairTabs.Bans)
            }
            buttonReporting.cardView.setOnClickListener {
                viewModel?.setLegalAffairSubTab(tab = LegalAffairTabs.Reporting)
            }
            buttonHidden.cardView.setOnClickListener {
                viewModel?.setLegalAffairSubTab(tab = LegalAffairTabs.Hidden)
            }
        }
    }

    private fun observe() {
        viewModel.currentSelectedLegalAffairsSubTab.observe(viewLifecycleOwner) { tab ->
            commonBinding.apply {
                buttonViolations.isSelected = tab == LegalAffairTabs.Violations
                buttonBan.isSelected = tab == LegalAffairTabs.Bans
                buttonReporting.isSelected = tab == LegalAffairTabs.Reporting
                buttonHidden.isSelected = tab == LegalAffairTabs.Hidden
                setupVerticalDottedLinesVisibility(currentTab = tab)
            }
            replaceFrameLayout(currentTab = tab)
        }

        setupCount()
    }

    abstract fun setupCount()
    abstract fun setupVerticalDottedLinesVisibility(currentTab: LegalAffairTabs)

    private fun replaceFrameLayout(currentTab : LegalAffairTabs) {
        val fragment = when(currentTab) {
            LegalAffairTabs.Violations -> { ViolationsFragment() }
            LegalAffairTabs.Bans -> { BanFragment() }
            LegalAffairTabs.Reporting -> { LegalAffairsReportingFragment() }
            LegalAffairTabs.Hidden -> { HiddenFragment() }
        }

        childFragmentManager
            .beginTransaction()
            .replace(R.id.legal_affairs_frame_layout, fragment)
            .commit()
    }
}