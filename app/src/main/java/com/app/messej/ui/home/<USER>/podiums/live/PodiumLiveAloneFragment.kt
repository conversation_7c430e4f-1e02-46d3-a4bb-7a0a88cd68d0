package com.app.messej.ui.home.publictab.podiums.live

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.appcompat.widget.PopupMenu
import androidx.compose.ui.platform.ComposeView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.PodiumEnforcementDialogType
import com.app.messej.data.model.enums.PodiumPauseGift
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentPodiumLiveAloneBinding
import com.app.messej.databinding.ItemPodiumAloneMainBinding
import com.app.messej.databinding.ItemPodiumLikesContainerBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.showPodiumLikeInfoDialog
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeIn
import com.app.messej.ui.home.publictab.podiums.challenges.PodiumLiveChallengeExtensions.fadeOut
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.CHALLENGE_RUNNING
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_ADMIN
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.DISABLED_BY_MANAGER
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.ChatDisableReason.UNKNOWN
import com.app.messej.ui.home.publictab.podiums.model.PodiumSpeakerUIModel.ActiveSpeakerUIModel
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.ifStillAttached
import com.app.messej.ui.utils.FragmentExtensions.setAsDestructiveMenuItem
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.SwipeGestureListener
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


class PodiumLiveAloneFragment : PodiumLiveAbstractFragment() {
    override val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

    val args: PodiumLiveAloneFragmentArgs by navArgs()

    override val podiumIdArg: String
        get() = args.podiumId

    override val enableScrollForTab: PodiumTab?
        get() = PodiumTab.fromInt(args.enableScrollForTab)

    override lateinit var binding: FragmentPodiumLiveAloneBinding

    override val liveBindingElements = object: LiveBindingElements {

        override val liveChat: RecyclerView
            get() = binding.liveChat

        override fun showLocalVideoSurface(show: Boolean) {
            binding.mainScreen.showVideo = show
        }

        override fun onPodiumLoading(loading: Boolean) {
            binding.podiumHeaderMultiStateView.viewState = if (loading) MultiStateView.ViewState.LOADING else MultiStateView.ViewState.CONTENT
        }

        override val likesContainer: ItemPodiumLikesContainerBinding
            get() = binding.likesContainer

        override val chatSend: MaterialButton
            get() = binding.chatSendButton

        override val actionPaidLike: MaterialButton
            get() = binding.actionLike

        override val actionShare: MaterialButton
            get() = binding.actionShare

        override val actionDecorHolderTop: LinearLayoutCompat
            get() = binding.actionDecorHolderTop

        override val liveCounter: ViewGroup
            get() = binding.liveCounter

        override val scrollerLayout: SmartRefreshLayout
            get() = binding.refreshLayout

        override val anthemOverlay: ComposeView?
            get() = binding.anthemOverlay

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_live_alone, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addObservers()
    }

    override fun onSecondResume() {
        viewModel.mainScreenSpeakerId.value?.let { mss ->
            binding.mainScreen.videoHolder.setupMainScreen(mss)
        }
    }

    private fun setup() {
        setEmptyView()
        binding.podiumDp.setOnClickListener {
            ensureAnthemNotPlaying {
                val action = PodiumLiveAloneFragmentDirections.actionPodiumLiveAloneFragmentToPodiumSpeakerActionsBottomSheetFragment(viewModel.mainScreenSpeakerId.value ?: return@ensureAnthemNotPlaying)
                findNavController().navigateSafe(action)
            }
        }
        binding.collapseActions = false
        binding.actionCollapse.setOnClickListener {
            ensureAnthemNotPlaying {
                binding.collapseActions = !(binding.collapseActions ?: false)
            }
        }

        binding.moreButton.setOnClickListener {
            ensureAnthemNotPlaying {
                showMoreMenu(it)
            }
        }

        binding.closeButton.setOnClickListener {
            onExit()
        }

        binding.actionVideoToggle.setOnClickListener {
            Log.d("CAM_ACTION", "main Screen  " + viewModel.iAmOnMainScreen())
            ensureAnthemNotPlaying {
                toggleCamera()
            }
        }

        binding.actionCameraFlip.setOnClickListener {
            ensureAnthemNotPlaying {
                toggleCameraFacing()
            }
        }

        binding.actionMuteToggle.setOnClickListener {
            ensureAnthemNotPlaying {
                viewModel.muteToggleSelf()
            }
        }

        binding.giftSendButton.setOnClickListener {
            val pod = viewModel.podium.value?: return@setOnClickListener
//            if(viewModel.user.citizenship==UserCitizenship.VISITOR) return@setOnClickListener

            ensureAnthemNotPlaying {
                if (viewModel.podium.value?.podiumGiftPaused == false) {
                    if (pod.managerId == viewModel.user.id) {
                        findNavController().navigateSafe(PodiumLiveAloneFragmentDirections.actionGlobalGiftFileFragment())
                    } else {
                        val action = PodiumLiveAloneFragmentDirections.actionGlobalGiftFragment(
                            pod.managerId,
//                    podiumId = pod.id,
                            giftContext = GiftContext.GIFT_PODIUM, managerId = pod.managerId, giftContextId = pod.id
                        )
                        findNavController().navigateSafe(action)
                    }
                }
            }
        }

        binding.mainScreen.root.setOnTouchListener(object: SwipeGestureListener(requireContext()) {
            override fun onSwipeLeft(): Boolean {
                // Handle swipe left action here, like switching to the next item or screen
                Log.d("PLAF", "Swipe Left Detected")
                ensureAnthemNotPlaying {
                    viewModel.podium.value?.let {
                        val action = NavGraphHomeDirections.actionGlobalNavAboutPodium(it.id, it.role?.isElevated!!)
                        findNavController().navigateSafe(action)
                    }
                }
                return true
            }

//            override fun onSwipeUp(): Boolean {
//                // Handle swipe up action here, like scrolling up or dismissing a view
//                Log.d("PLAF", "Swipe Up Detected")
//                showNextPodiums()
//                return true
//            }
        })
        binding.likeCounter.setOnClickListener {
            showPodiumLikeInfoDialog()
        }
    }

    override fun onExit() {
        ensureAnthemNotPlaying {
            if (viewModel.iAmManager.value == true) {
                confirmClose {
                    viewModel.close()
                }
            } else super.onExit()
        }
    }

    private fun addObservers() {

        viewModel.chatDisabled.observe(viewLifecycleOwner) {
            if (it!=null && viewModel.iAmManager.value!=true) {
                when(it){
                    DISABLED_BY_MANAGER -> {
                        binding.chatInput.editText?.hint = getString(R.string.podium_comments_disabled_manager)
                    }
                    DISABLED_BY_ADMIN -> {
                        binding.chatInput.editText?.hint = getString(R.string.podium_comments_disabled_admin)
                    }
                    UNKNOWN -> {
                        binding.chatInput.editText?.hint = getString(R.string.podium_comments_disabled)
                    }
                    CHALLENGE_RUNNING -> { /* Not Applicable */ }
                }
            } else {
                binding.chatInput.editText?.hint = getString(R.string.chat_input_hint)
            }
        }

        viewModel.mainScreenSpeaker.observe(viewLifecycleOwner) {
            Log.w("PLVM", "observe: mainScreenSpeaker $it")
            setMainScreenSpeaker(binding.mainScreen, it)
        }
        viewModel.mainScreenSpeakerId.observe(viewLifecycleOwner) {
            it?: return@observe
            binding.mainScreen.videoHolder.setupMainScreen(it)
        }
        viewModel.myVideoIsTurnedOn.observe(viewLifecycleOwner) {
            it?: return@observe
            binding.mainScreen.videoHolder.setupMainScreen(viewModel.user.id)
        }
        viewModel.isLikeLoading.observe(viewLifecycleOwner){
            Log.d("LIKE_ENABLED", "observe: $it")
        }
    }

    override fun onAboutToJoin() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.podium_alone_about_title)
            .setMessage(R.string.podium_alone_about_desc)
            .setPositiveButton(R.string.common_close) { dialog, which ->
                dialog.dismiss()
            }
            .show()
        lifecycleScope.launch {
            binding.welcomeMessage.fadeIn(500)
            delay(10000)
            binding.welcomeMessage.fadeOut(500)
        }
    }

    private fun setMainScreenSpeaker(binding: ItemPodiumAloneMainBinding, item: ActiveSpeakerUIModel?) {
        Log.w("AGORA", "setMainScreenSpeaker: $item")
        binding.apply {
            speaker = item
            isSelf = viewModel.user.id == item?.speaker?.id
        }
    }

    private lateinit var liveChatEmptyView: LayoutListStateEmptyBinding

    fun setEmptyView() {
        val emptyView: View = binding.liveChatMultiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        liveChatEmptyView = LayoutListStateEmptyBinding.bind(emptyView).apply {
            prepare(message = R.string.podium_comments_disabled)
        }
    }

    override fun showMoreMenu(v: View): PopupMenu {
        val popup = PopupMenu(requireContext(), v)
        popup.menuInflater.inflate(R.menu.menu_podium_live_alone_actions, popup.menu)
        popup.menu.apply {

            findItem(R.id.podium_edit).isVisible = viewModel.iAmManager.value == true

            findItem(R.id.action_pause_resume_likes).apply {
                isVisible = viewModel.iAmManager.value == true || viewModel.iAmElevated.value == true
                setTitle(
                    if (viewModel.podium.value?.likesDisabled == true) R.string.podium_resume_likes
                    else R.string.podium_pause_likes
                )
            }
            findItem(R.id.action_report_podium).apply {
                setAsDestructiveMenuItem(requireContext())
                isVisible = viewModel.iAmManager.value != true && viewModel.user.premium
            }
            findItem(R.id.action_pause_resume_gift).apply {
                isVisible = viewModel.iAmManager.value == true || viewModel.iAmElevated.value == true
                setTitle(
                    if (viewModel.podium.value?.podiumGiftPaused == true) R.string.podium_resume_gift
                    else R.string.podium_pause_gift
                )
            }
            Log.w("PLAF", "showMoreMenu: iAmManager: ${viewModel.iAmManager.value} | iAmElevated: ${viewModel.iAmElevated.value}", )

            findItem(R.id.action_pause_resume_comments).apply {
                isVisible = viewModel.iAmElevated.value == true
                setTitle(
                    if (viewModel.podium.value?.chatDisabled == true) R.string.podium_resume_comments
                    else R.string.podium_pause_comments
                )
            }

            findItem(R.id.action_admins).isVisible = viewModel.iAmManager.value == true
            findItem(R.id.action_restricted).isVisible = viewModel.iAmElevated.value == true

            val iAmAudience = viewModel.iAmManager.value != true

            findItem(R.id.action_share).isVisible = true
            findItem(R.id.action_send_flix).isVisible = iAmAudience && viewModel.user.citizenship!=UserCitizenship.VISITOR
            findItem(R.id.action_leave).isVisible = iAmAudience

            findItem(R.id.action_close).isVisible = viewModel.iAmManager.value == true || viewModel.user.userEmpowerment?.allowEndPodium == true || viewModel.iAmMinister == true

            findItem(R.id.action_hide_live_users).apply {
                isVisible = viewModel.iAmMinister || viewModel.iAmPresident
                title = if (viewModel.podiumHideLiveUsers.value==true) getString(R.string.podium_action_unhide_live_users)
                else getString(R.string.podium_hide_live_users)
            }
            findItem(R.id.action_anthem).isVisible = viewModel.iAmElevated.value == true
        }

        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.podium_about -> {
                    viewModel.podium.value?.let {
                        val action = PodiumLiveAloneFragmentDirections.actionGlobalNavAboutPodium(it.id, it.role?.isElevated!!)
                        findNavController().navigateSafe(action)
                    }
                }
                R.id.action_report_podium -> {
                    ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                        viewModel.podium.value?.let {
                            val action = NavGraphHomeDirections.actionGlobalReportFragment(ReportPackage.PodiumReport(podium = it).serialize())
                            findNavController().navigateSafe(action)
                        }
                    }
                }
                R.id.podium_edit -> {
                    val action = PodiumLiveAloneFragmentDirections.actionGlobalCreatePodiumFragment(podiumIdArg)
                    findNavController().navigateSafe(action)
                }
                R.id.action_pause_resume_likes ->confirmLikeDisable{
                    viewModel.disableLikes()
                }

                R.id.action_pause_resume_gift -> confirmGiftDisable {
                    if (viewModel.podium.value?.podiumGiftPaused == true)
                        viewModel.pauseGift(PodiumPauseGift.PODIUM_RESUME_GIFT) else viewModel.pauseGift(PodiumPauseGift.PODIUM_PAUSE_GIFT)
                }

                R.id.action_pause_resume_comments -> ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    confirmCommentsDisable {
                        viewModel.toggleComments()
                    }
                }
                R.id.action_admins -> {
                    val action = PodiumLiveAloneFragmentDirections.actionPodiumLiveAloneFragmentToPodiumAdminsBottomSheetFragment()
                    findNavController().navigateSafe(action)
                }

                R.id.action_restricted -> {
                    val action = PodiumLiveAloneFragmentDirections.actionPodiumLiveAloneFragmentToPodiumBlockedUsersBottomSheetFragment()
                    findNavController().navigateSafe(action)
                }
                R.id.action_share -> {
                    sharePodium()
                }
                R.id.action_send_flix -> {
                    val id = viewModel.podium.value?.managerId?: return@setOnMenuItemClickListener false
                    ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                        val action = PodiumLiveAloneFragmentDirections.actionGlobalFlaxTransfer(id)
                        findNavController().navigateSafe(action)
                    }
                }
                R.id.action_leave -> confirmLeave {
                    viewModel.leave()
                }
                R.id.action_close -> ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                    confirmClose { viewModel.close() }
                }
                R.id.action_hide_live_users ->confirmHideLiveUsers{
                    if (viewModel.podiumHideLiveUsers.value ==true) viewModel.hideUnHideLiveUsers(false)
                    else viewModel.hideUnHideLiveUsers(true)
                }
                R.id.action_anthem -> {
                    ensurePodiumCreateAllowed(dialogType = PodiumEnforcementDialogType.UserInteractions) {
                        if (viewModel.challengeActive.value == true) {
                            showFlashatDialog {
                                setMessage(R.string.podium_play_anthem_error_challenge)
                                setCloseButton(R.string.common_close) {
                                    true
                                }
                            }
//                    } else if (viewModel.user.coinBalance.toInt()<100) {
//                        showInsufficientBalanceAlert(requiredCoins = 100)
                        } else {
                            showFlashatDialog {
                                setTitle(R.string.podium_play_anthem)
                                setMessage(R.string.podium_play_anthem_confirm)
                                setConfirmButton(R.string.common_accept) {
                                    ifStillAttached {
                                        viewModel.playFlashatAnthem()
                                    }
                                    true
                                }
                                setCloseButton(R.string.common_cancel) {
                                    true
                                }
                            }
                        }
                    }
                }
                else -> return@setOnMenuItemClickListener false
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
        this.popup = popup
        return popup
    }

    override fun toLiveChatActions(userId: Int) {
        val action = PodiumLiveAloneFragmentDirections.actionPodiumLiveAloneFragmentToPodiumLiveChatActionsBottomSheetFragment(userId)
        findNavController().navigateSafe(action)
    }

    override fun toBuyCamera(buy: Boolean) {
        val action = PodiumLiveAloneFragmentDirections.actionPodiumLiveAloneFragmentToPodiumBuyCameraBottomSheetFragment(buy)
        findNavController().navigateSafe(action)
    }

    override fun toLiveUsersList() {
        val action = PodiumLiveAloneFragmentDirections.actionPodiumLiveAloneFragmentToPodiumLiveUsersListBottomSheetFragment()
        findNavController().navigateSafe(action)
    }

    override val isDisplayedOnDayNightSurface: Boolean = false

    override fun executeExtraStepsToShowAnthem(show: Boolean) {
        super.executeExtraStepsToShowAnthem(show)
        binding.collapseActions = show
    }

}