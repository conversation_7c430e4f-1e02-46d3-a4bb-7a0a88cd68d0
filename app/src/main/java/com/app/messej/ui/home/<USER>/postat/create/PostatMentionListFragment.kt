package com.app.messej.ui.home.publictab.postat.create

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.PostatMentionedUser
import com.app.messej.databinding.FragmentPostatMentionListBinding
import com.app.messej.databinding.ItemPostatMentionChipBinding


class PostatMentionListFragment : Fragment() {

    private lateinit var binding: FragmentPostatMentionListBinding

    private val viewModel: CreatePostatViewModel by navGraphViewModels(R.id.nav_create_postat)

    private var mentionUsersListAdapter: PostatMentionListAdapter? = null


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_mention_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observer()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.removeKeywordFromSearchBox()
    }

    private fun setup() {
        viewModel.initMentionsWorkingCopy()
        initAdapter()
        binding.closeButton.setOnClickListener {
            viewModel.initMentionsWorkingCopy()
            findNavController().popBackStack()
        }
        binding.doneButton.setOnClickListener {
            viewModel.commitMentions()
            findNavController().popBackStack()
        }
    }

    private fun observer() {
        viewModel.mentionableUsersList.observe(viewLifecycleOwner) {
            mentionUsersListAdapter?.submitData(lifecycle, it)
        }

        viewModel.nickNames.observe(viewLifecycleOwner) { user ->
            Log.i("nicknames", "observer: ")
        }

        viewModel.mentionedUserRemoved.observe(viewLifecycleOwner) { user ->

            val viewToRemove = binding.mentionedUsersLayout.findViewById<View>(user.id)

            if (viewToRemove != null) {
                binding.mentionedUsersFlow.referencedIds = binding.mentionedUsersFlow.referencedIds.filter { it != user.id }.toIntArray()
                binding.mentionedUsersLayout.removeView(viewToRemove)
                binding.mentionedUsersFlow.requestLayout()
            }
        }

        viewModel.mentionedUsersTemp.observe(viewLifecycleOwner) { userList ->
            userList.forEach {
                createMentionedUserChip(it)
            }
        }
    }

    private fun initAdapter() {
        mentionUsersListAdapter = PostatMentionListAdapter(object : PostatMentionListAdapter.UserActionListener {
            override fun onUserSelect(user: PostatMentionedUser) {
               if (createMentionedUserChip(user)) {
                   viewModel.addMentionedUser(user)
               }
            }

            override fun getNickName(user: PostatMentionedUser): String? {
                return viewModel.getNickName(user)
            }
        })

        binding.musicList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mentionUsersListAdapter
        }
    }

    private fun createMentionedUserChip(user: PostatMentionedUser) : Boolean{
        val userChipExists = (0 until binding.mentionedUsersLayout.childCount)
            .map { binding.mentionedUsersLayout.getChildAt(it) }
            .any { it.id == user.id }

        if (!userChipExists) {
            val inflater = LayoutInflater.from(requireContext())
            val chipBinding: ItemPostatMentionChipBinding = DataBindingUtil.inflate(
                inflater, R.layout.item_postat_mention_chip, binding.mentionedUsersLayout, false)
            chipBinding.mentionedUser = user
            chipBinding.viewModel = viewModel
            chipBinding.root.id = user.id

            binding.mentionedUsersLayout.addView(chipBinding.root)
            binding.mentionedUsersFlow.referencedIds += user.id
        }

        return !userChipExists
    }
}