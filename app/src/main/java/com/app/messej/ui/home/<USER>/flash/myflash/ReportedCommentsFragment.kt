package com.app.messej.ui.home.publictab.flash.myflash

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.FlashReportedComment
import com.app.messej.databinding.FragmentFlashReportedCommentsBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView

class ReportedCommentsFragment : Fragment() {

    private lateinit var binding: FragmentFlashReportedCommentsBinding

    private val viewModel : ReportedCommentsViewModel by viewModels()

    private var mAdapter: ReportedCommentsListAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_flash_reported_comments, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setEmptyView()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
    }

    private fun setup() {
        initAdapter()
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        emptyViewBinding.prepare(
            message = R.string.chat_eds_message_reported_comment
        )
    }

    private fun observe() {
        viewModel.reportedCommentsList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }
        viewModel.onDeleteComment.observe(viewLifecycleOwner) {
            mAdapter?.refresh()
        }
        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            if (it != null) {
                binding.multiStateView.viewState = it
            }
        }
    }

    fun initAdapter() {
        mAdapter = ReportedCommentsListAdapter(object : ReportedCommentsListAdapter.ReportActionListener {
            override fun onDeleteAction(item: FlashReportedComment, position: Int) {
                showDeleteAlertDialog(item)
            }

            override fun onViewComment(item: FlashReportedComment, position: Int) {
            }

            override fun onViewReporters(item: FlashReportedComment, position: Int) {
//                findNavController().navigateSafe(
//                    HuddleReportsFragmentDirections.actionReportedMessagesFragmentToReportedParticipantsFragment(
//                        item.huddleId,
//                        item.reportId,
//                        item.reportsCount,
//                        ReportType.COMMENT
//                    )
//                )
            }

        })
        mAdapter?.apply {
            stateRestorationPolicy = RecyclerView.Adapter.StateRestorationPolicy.PREVENT_WHEN_EMPTY
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                viewModel.setViewState(state)
            }
        }

        val layoutMan = LinearLayoutManager(context)

        binding.commentsList.apply {
            itemAnimator = null
            layoutManager = layoutMan
            setHasFixedSize(false)
            adapter = mAdapter
        }
    }

    private fun showDeleteAlertDialog(item: FlashReportedComment) {
        MaterialAlertDialogBuilder(requireContext(),R.style.ThemeOverlay_Flashat_MaterialAlertDialog)
            .setTitle(getString(R.string.huddle_reported_comment_delete_confirmation_title))
            .setMessage(getString(R.string.flash_reported_comment_delete_confirmation_message))
            .setCancelable(false)
            .setPositiveButton(getString(R.string.common_delete)) { _, _ ->
                viewModel.deleteReportedComment(item)
            }
            .setNegativeButton(getString(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}