package com.app.messej.ui.home.privatetab

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import com.app.messej.ui.home.privatetab.HomePrivateFragment.Companion.PrivateTab

class HomePrivateViewModel(application: Application) : AndroidViewModel(application) {

    private val _currentTab = MutableLiveData<PrivateTab?>(null)
    val currentTab: LiveData<PrivateTab?> = _currentTab.distinctUntilChanged()

    fun setCurrentTab(tab: PrivateTab, skipIfSet: Boolean = false) {
        Log.w("HPVM", "setCurrentTab:$tab, skip: $skipIfSet, current: ${_currentTab.value}", )
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
    }
}