package com.app.messej.ui.home.publictab.postat.create

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.PostatMentionedUser
import com.app.messej.databinding.ItemChooseMoreContributorsListBinding

class PostatMentionListAdapter(private val listener: UserActionListener) : PagingDataAdapter<PostatMentionedUser, PostatMentionListAdapter.UsersListViewHolder>(
    MentionUsersDiff
) {
    interface UserActionListener {
        fun getNickName(user: PostatMentionedUser): String?
        fun onUserSelect(user: PostatMentionedUser)
    }

    inner class UsersListViewHolder(private val binding: ItemChooseMoreContributorsListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: PostatMentionedUser, position: Int) = with(binding) {
            user = item.copy(
                name = listener.getNickName(item) ?: item.name
            )
            selected = false
            checkbox.visibility = View.INVISIBLE
            binding.memberLayout.setOnClickListener {
                listener.onUserSelect(item)
            }
        }
    }

    object MentionUsersDiff : DiffUtil.ItemCallback<PostatMentionedUser>() {
        override fun areItemsTheSame(oldItem: PostatMentionedUser, newItem: PostatMentionedUser) = oldItem.id == newItem.id
        override fun areContentsTheSame(oldItem: PostatMentionedUser, newItem: PostatMentionedUser) = oldItem == newItem
    }

    override fun onBindViewHolder(holder: UsersListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UsersListViewHolder {
        return UsersListViewHolder(
            ItemChooseMoreContributorsListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }
}