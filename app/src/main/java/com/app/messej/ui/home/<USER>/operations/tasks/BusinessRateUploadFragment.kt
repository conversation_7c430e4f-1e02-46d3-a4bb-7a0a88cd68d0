package com.app.messej.ui.home.businesstab.operations.tasks

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentBusinessRateUploadBinding
import com.app.messej.ui.home.businesstab.operations.status.BusinessWorkStatusBaseFragment
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class BusinessRateUploadFragment:BottomSheetDialogFragment() {
    lateinit var binding: FragmentBusinessRateUploadBinding
    val viewModel: BusinessRateViewModel by navGraphViewModels(R.id.navigation_rate)
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_rate_upload, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
        viewModel.isScreenShotUploaded.observe(viewLifecycleOwner) {
             findNavController().popBackStack(R.id.navigation_rate,false)
            setFragmentResult(BusinessWorkStatusBaseFragment.STATUS_CHANGE_REQUEST_KEY, bundleOf(BusinessWorkStatusBaseFragment.STATUS_CHANGE_RESULT_KEY to true))
            if (it) {
                Toast.makeText(requireContext(), getString(R.string.business_screenshot_add_success_message), Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), getString(R.string.business_screenshot_added_failed_message), Toast.LENGTH_SHORT).show()
            }
        }
        viewModel.uri.observe(viewLifecycleOwner){
            it?.let {
                binding.imgRatingUpload.setImageURI(it)
            }

        }
    }

    private fun setup() {
        binding.btnRatingLater.setOnClickListener{
            dialog?.dismiss()
        }
        binding.btnRatingClose.setOnClickListener {
            dialog?.dismiss()
        }
        binding.btnRatingSubmit.setOnClickListener {
            confirmAction(R.string.title_rate_flashat_app, getString(R.string.title_business_upload_rating), R.string.common_proceed, R.string.common_cancel) {
                viewModel.getImageUriForCapture()
            }
        }
        binding.btnRatingChange.setOnClickListener {
            val galleryIntent = Intent(Intent.ACTION_GET_CONTENT)
            galleryIntent.type = "image/*"
            selectImageFromGalleryResult.launch(galleryIntent)
        }
    }


    private val selectImageFromGalleryResult = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                viewModel.addImage(uri)
            }
        }
    }


}