package com.app.messej.ui.auth.register

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.FragmentRegisterMobileConfirmBottomSheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class RegisterMobileConfirmFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentRegisterMobileConfirmBottomSheetBinding

    private val args: RegisterMobileConfirmFragmentArgs by navArgs()

    companion object {
        const val MOBILE_CONFIRM_RESULT_CODE = "mobile_confirm"
        const val MOBILE_CONFIRM_RESULT_KEY = "mobile_confirm_result"

        const val MOBILE_CONFIRM_RESULT_SUCCESS = "mobile_confirmed"

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding =
            DataBindingUtil.inflate(inflater, R.layout.fragment_register_mobile_confirm_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun observe() {
    }

    private fun setup() {
        if(args.email.isNullOrEmpty()) {
            binding.phoneNumberBottomsheet.text = UserInfoUtil.combineCountryCodeAndMobileNumber(args.countryCode,args.phoneNumber)
            binding.bottomsheetCountryPicker.setCountryForPhoneCode(args.countryCode.toInt())
            binding.confirmMobileTitle.text = getString(R.string.register_confirm_mobile_number)
            binding.otpText.text=getString(R.string.register_confirm_otp_text)
        }else{
            binding.phoneNumberBottomsheet.text=args.email
            binding.bottomsheetCountryPicker.visibility=View.GONE
            binding.confirmMobileTitle.text = getString(R.string.register_confirm_email)
            binding.otpText.text=getString(R.string.register_confirm_otp_email)
        }
        binding.confirmButton.setOnClickListener {

            findNavController().popBackStack()
            setFragmentResult(
                MOBILE_CONFIRM_RESULT_CODE, bundleOf(
                    MOBILE_CONFIRM_RESULT_KEY to true
                )
            )
        }

        binding.cancelButton.setOnClickListener {
            findNavController().popBackStack()
        }

    }

}