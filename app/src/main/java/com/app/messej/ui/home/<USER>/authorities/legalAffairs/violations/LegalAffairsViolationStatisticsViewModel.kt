package com.app.messej.ui.home.publictab.authorities.legalAffairs.violations

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.LegalAffairStatisticsAppealedEnumItems
import com.app.messej.data.model.enums.LegalAffairsStatisticsDropdown
import com.app.messej.data.model.enums.LegalAffairsViolationSubTab
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalAffairsRepository

class LegalAffairsViolationStatisticsViewModel(application: Application) : AndroidViewModel(application)  {

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    private val legalAffairsRepository = LegalAffairsRepository(application)

    private val _selectedDropdownItem = MutableLiveData<LegalAffairsStatisticsDropdown>(LegalAffairsStatisticsDropdown.YourContent)
    val selectedDropdownItem : LiveData<LegalAffairsStatisticsDropdown> = _selectedDropdownItem.distinctUntilChanged()

    private val _isGraphVisible = MutableLiveData(false)
    val isGraphVisible : LiveData<Boolean> = _isGraphVisible.distinctUntilChanged()

    val statisticCount = MutableLiveData<LegalRecordsResponse>()

    private val _appealedGraphValues = MutableLiveData<List<AppealedItem>>()
    val appealedGraphValues : LiveData<List<AppealedItem>> = _appealedGraphValues.distinctUntilChanged()

    fun setDropdownItem(item: LegalAffairsStatisticsDropdown) {
        _selectedDropdownItem.value = item
        Log.d("LAVSV", "Selected Dropdown -> $item")
    }

    fun setGraphVisibility() {
        _isGraphVisible.value = !(_isGraphVisible.value ?: false)
    }

    val statisticsList = _selectedDropdownItem.switchMap {
        legalAffairsRepository.getViolationList(
            recordType = LegalAffairsViolationSubTab.Statistics.serializedName(),
            reportingType = it.value,
            countCallBack = { counts ->
                statisticCount.postValue(
                    LegalRecordsResponse(
                        guilty = counts?.guilty,
                        notGuilty = counts?.notGuilty,
                        appeal = counts?.appeal,
                        fine = counts?.fine,
                        advocates = counts?.advocates,
                        jury = counts?.jury,
                        underInvestigation = counts?.underInvestigation,
                        reportedCount = counts?.reportedCount
                    )
                )
                setAppealedCountGraphValues(counts)
            }
        ).liveData.cachedIn(viewModelScope)
    }

    private fun setAppealedCountGraphValues(item: LegalRecordsResponse?) {
        _appealedGraphValues.postValue(
            if ((item?.reportedCount ?: 0) == 0) null
            else listOf(
                AppealedItem(type = LegalAffairStatisticsAppealedEnumItems.PayableFines, count = item?.fine),
                AppealedItem(type = LegalAffairStatisticsAppealedEnumItems.AppointingAdvocates, count = item?.advocates),
                AppealedItem(type = LegalAffairStatisticsAppealedEnumItems.InJury, count = item?.jury),
                AppealedItem(type = LegalAffairStatisticsAppealedEnumItems.ConfirmedGuilty, count = item?.guilty),
                AppealedItem(type = LegalAffairStatisticsAppealedEnumItems.FoundNotGuilty, count = item?.notGuilty),
            )
        )
    }
}

data class AppealedItem(
    val type:LegalAffairStatisticsAppealedEnumItems,
    val count: Int?
)