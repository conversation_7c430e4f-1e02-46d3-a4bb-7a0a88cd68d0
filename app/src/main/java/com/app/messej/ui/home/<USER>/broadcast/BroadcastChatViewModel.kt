package com.app.messej.ui.home.publictab.broadcast

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessage.MessageType
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AttachLocation
import com.app.messej.data.model.ChatMessageSearchResult
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.entity.BroadcastChatMessageWithMedia
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.enums.BroadcastAction
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.repository.BroadcastRepository
import com.app.messej.data.socket.repository.BroadcastEventRepository
import com.app.messej.ui.chat.BaseChatViewModel
import com.app.messej.ui.chat.ChatMessageUIModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BroadcastChatViewModel(application: Application): BaseChatViewModel(application)  {

    private val broadcastRepo = BroadcastRepository(getApplication())
    private val eventRepo = BroadcastEventRepository

    private val socketRepo = BroadcastEventRepository

    private val _broadcastMode = MutableLiveData<BroadcastMode?>()
    val broadcastMode: LiveData<BroadcastMode?> = _broadcastMode

    fun setBroadcastMode(mode: BroadcastMode) {
        if (_broadcastMode.value==mode) return
        _broadcastMode.postValue(mode)
    }

    private var alreadyPrefilled = false

    fun prefillChatInput(text: String) {
        if (!alreadyPrefilled && chatTextInput.value.isNullOrBlank()) {
            alreadyPrefilled = true
            chatTextInput.value = text
        }
    }

    override val showChats = MutableLiveData<Boolean>(true)

    override val _chatList: LiveData<PagingData<ChatMessageUIModel>> = _broadcastMode.switchMap {
        it ?: return@switchMap null
        return@switchMap broadcastRepo.getOutgoingBroadcastPager(it).liveData.map { pagingData: PagingData<BroadcastChatMessageWithMedia> ->
            val pgData = pagingData.map { msg ->
                ChatMessageUIModel.ChatMessageModel(msg, false)
            }
            checkIfNeedsSending(pgData)
            pgData.setShowName().insertDateSeparators()
        }.cachedIn(viewModelScope)
    }

    override fun likeItem(item: AbstractChatMessage) {
        viewModelScope.launch {
            eventRepo.likeBroadcastMessage(item as BroadcastMessage)
        }
    }

    private suspend fun performMessageUpload(msg: BroadcastChatMessageWithMedia) = chatRepo.sendBroadcastMessage(msg)

    override suspend fun sendMessage(message: String, media: TempMedia?, replyTo: ReplyTo?, location: AttachLocation?, color: ChatTextColor?): AbstractChatMessage? {
        val mode = _broadcastMode.value ?: return null
        val msg = chatRepo.createBroadcastMessage(mode, message, media, location, color)
        if (msg.message.messageType.canSendInstantly()) {
            // Trigger a send immediately as there is no media to send
            chatRepo.sendBroadcastMessage(msg)
        }
        return msg.message
    }

    override fun onTriggerUpload(msg: AbstractChatMessageWithMedia) {
        viewModelScope.launch(Dispatchers.IO) {
            performMessageUpload(msg as BroadcastChatMessageWithMedia)
        }
    }

    override val typingListener = object: TypingListener {
        override fun onTyping(typing: Boolean) {
            // No need to send event
        }
    }

    override fun getSenderForReply(msg: AbstractChatMessage): SenderDetails? {
        return null
    }

    val canForwardSelection = _selectedChats.map { it.isNotEmpty() && it.all { ch -> ch.messageType == MessageType.TEXT } }
    val actionIsStar = _selectedChats.map {
        it.all { chat -> !(chat as BroadcastMessage).starred }
    }

    val onStarAction = LiveEvent<BroadcastAction>()

    fun toggleStar() {
        val star = actionIsStar.value?:return
        val list = _selectedChatsList.map{ it as BroadcastMessage }.filter {
            it.starred != star
        }
        if (list.isEmpty()) {
            exitSelectionMode()
            return
        }
        viewModelScope.launch {
            eventRepo.onStarAction.take(1).collect {
                onStarAction.postValue(if (it.starred) BroadcastAction.STAR else BroadcastAction.UNSTAR)
            }
        }
        eventRepo.starBroadcastMessage(list,star)
        exitSelectionMode()
    }

    override val deleteTimeout = accountRepo.getAccountDetailsFlow().map {
        it?.broadcastDeleteTimeoutInSeconds
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = null
    )

    val onMessageForwarded = LiveEvent<List<BroadcastMode>>()
    val onCannotForward = LiveEvent<Boolean>()

    fun confirmForward(modes: List<BroadcastMode>) {
        _selectedChatsList.forEach {
            val message = it as BroadcastMessage
            viewModelScope.launch(Dispatchers.IO) {
                if(!chatRepo.checkIfCanForwardBroadcast(message)) {
                    onCannotForward.postValue(true)
                    return@launch
                }
                modes.forEach { mode ->
                    val msg =  chatRepo.cloneBroadcastMessage(mode, message)
                    performMessageUpload(msg)
                }
                withContext(Dispatchers.Main) {
                    exitSelectionMode()
                }
                onMessageForwarded.postValue(modes)
            }
        }
    }

    fun deleteSelection(forEveryone: Boolean) {
        val list = _selectedChatsList.map{ it as BroadcastMessage }
        if (list.isEmpty()) {
            exitSelectionMode()
            return
        }
        eventRepo.deleteBroadcastMessage(list,forEveryone)
        exitSelectionMode()
    }

    // search
    val searchText = MutableLiveData<String>("")

    fun clearSearch() {
        searchText.postValue("")
    }

    init {
        viewModelScope.launch {
            searchText.asFlow().debounce(1000L).collect { text ->
                if (text.isNullOrBlank() || text.length<2) {
                    debouncedSearchText.postValue(null)
                } else {
                    debouncedSearchText.postValue(text)
                }
            }
        }
    }

    override suspend fun provideSearchResults(term: String): List<ChatMessageSearchResult> {
        val mode = _broadcastMode.value ?: return emptyList()
        return broadcastRepo.getOutgoingBroadcastSearchPositions(mode, term)
    }
}