package com.app.messej.ui.home.publictab.huddles.create

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.huddles.AddParticipantsResponse.Members
import com.app.messej.databinding.ItemAddParticipantsListBinding

class AddParticipantsListAdapter(private val mListener: ItemListener) : PagingDataAdapter<AddParticipantsListAdapter.AddParticipantsUIModel, AddParticipantsListAdapter.ParticipantsListViewHolder>(
    StarsDiff
) {


    data class AddParticipantsUIModel(
        val members: Members,
        var selected: Boolean = false
    )

    interface ItemListener {
        fun onItemClick(item: Members, position: Int)
        fun onItemChecked(item: Members, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        ParticipantsListViewHolder(
            ItemAddParticipantsListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )

    override fun onBindViewHolder(holder: ParticipantsListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }

    inner class ParticipantsListViewHolder(private val binding: ItemAddParticipantsListBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: AddParticipantsUIModel, position: Int) = with(binding) {
            Log.d("MembersTag", "bind: ${item.members.name}")
            user=item.members
            selected = item.selected
            memberLayout.setOnClickListener {
                mListener.onItemClick(item.members, position)
            }
//            superstarDp.setOnClickListener {
//                mListener.onItemClick(item.members, position)
//            }
//            superstarName.setOnClickListener {
//                mListener.onItemClick(item.members, position)
//            }
//            checkbox.setOnClickListener {
//                mListener.onItemClick(item.members, position)
//            }
        }
    }

    object StarsDiff : DiffUtil.ItemCallback<AddParticipantsUIModel>() {
        override fun areItemsTheSame(oldItem: AddParticipantsUIModel, newItem: AddParticipantsUIModel) =
            oldItem.members.memberId == newItem.members.memberId

        override fun areContentsTheSame(oldItem: AddParticipantsUIModel, newItem: AddParticipantsUIModel) =
            oldItem == newItem
    }
}