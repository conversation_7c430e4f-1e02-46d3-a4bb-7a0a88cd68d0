package com.app.messej.ui.home.publictab.authorities.legalAffairs

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.OptIn
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.databinding.ItemCasePreviewMediaBinding

class CaseDetailsPreviewViewPagerAdapter(private val listener: PlayerActionListener, private var mediaList: MutableList<PostatMedia>) :
    RecyclerView.Adapter<CaseDetailsPreviewViewPagerAdapter.MediaPagerViewHolder>() {

    interface PlayerActionListener {
        fun registerForFuturePlayback(obj: FuturePlaybackObject)
        fun detachFuturePlayback(pos: Int)
    }

    companion object {
        const val TAG = "CDBS"
    }

    data class FuturePlaybackObject(
        val pos: Int,
        val media: PostatMedia,
        val onPlay: (Player) -> Unit,
        val onStop: () -> Unit
    )

    inner class MediaPagerViewHolder(val binding: ItemCasePreviewMediaBinding) : RecyclerView.ViewHolder(binding.root) {

        private var media: PostatMedia? = null

        fun setData(med: PostatMedia) {
            media = med
            binding.media = med
        }

        @OptIn(UnstableApi::class)
        fun bindPlayer(): FuturePlaybackObject? = with(binding) {
            <EMAIL>?.let { med ->
                when (med.mediaType) {
                    MediaType.IMAGE -> {
                        return@with null
                    }

                    MediaType.VIDEO -> {
                        Log.w(TAG, "preparePlayerForSetup: $med")
                        val fpo = FuturePlaybackObject(bindingAdapterPosition, med, { player ->
                                Log.w(TAG, "binding player for video: ${med.s3Key}")
                                player.apply { binding.playerView.player = this }
                            }, { cleanup() })
                        listener.registerForFuturePlayback(fpo)
                        return@with fpo
                    }
                    else -> {
                        return@with null
                    }
                }
            }
        }

        fun cleanup() = with(binding) {
            playerView.player = null
            listener.detachFuturePlayback(bindingAdapterPosition)
        }
    }
    override fun getItemCount(): Int = mediaList.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MediaPagerViewHolder {

        val binding = ItemCasePreviewMediaBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MediaPagerViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MediaPagerViewHolder, position: Int) {
        holder.setData(mediaList[position])
    }

    override fun onViewDetachedFromWindow(holder: MediaPagerViewHolder) {
        super.onViewDetachedFromWindow(holder)
        Log.w(TAG, "onViewDetachedFromWindow: $holder")
        holder.cleanup()
    }

    override fun onViewAttachedToWindow(holder: MediaPagerViewHolder) {
        super.onViewAttachedToWindow(holder)
        Log.w(TAG, "onViewAttachedToWindow: $holder")
        holder.bindPlayer()
    }

}