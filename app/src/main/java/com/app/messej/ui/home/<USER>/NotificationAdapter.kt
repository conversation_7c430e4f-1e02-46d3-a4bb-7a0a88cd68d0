package com.app.messej.ui.home.notification

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.Notification
import com.app.messej.data.model.entity.Notification.NotificationAction.BIRTHDAYS
import com.app.messej.data.model.entity.Notification.NotificationAction.FLIX_PURCHASED
import com.app.messej.data.model.entity.Notification.NotificationAction.HUDDLE_DELETED
import com.app.messej.data.model.entity.Notification.NotificationAction.INVITE_PARTICIPANT
import com.app.messej.data.model.entity.Notification.NotificationAction.NEW_SUBSCRIPTION
import com.app.messej.data.model.entity.Notification.NotificationAction.PRESIDENT
import com.app.messej.data.model.entity.Notification.NotificationAction.RENEW_SUBSCRIPTION
import com.app.messej.data.model.entity.Notification.NotificationAction.RENEW_SUBSCRIPTION_REMINDER
import com.app.messej.data.model.entity.Notification.NotificationAction.SUBSCRIPTION_FAILURE
import com.app.messej.data.model.entity.Notification.NotificationAction.SUBSCRIPTION_RENEW_FAILURE
import com.app.messej.databinding.ItemNotificationBinding
import com.app.messej.ui.utils.DateFormatHelper
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import jp.wasabeef.transformers.glide.CropCircleTransformation

class NotificationAdapter(private val listener: ActionListener) : PagingDataAdapter<Notification, NotificationAdapter.NotificationViewHolder>(DearsDiff) {
    interface ActionListener {
        fun getPositiveAction(item: Notification): String?
        fun getNegativeAction(item: Notification): String?
        fun getActionStatus(item: Notification): String?
        fun showMoreAction(item: Notification): Boolean

        fun onPositiveActionClick(item: Notification)
        fun onNegativeActionClick(item: Notification)
        fun onMoreActionClick(item: Notification, view: View)
        fun onItemClick(item: Notification, position: Int)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = NotificationViewHolder(
        ItemNotificationBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it, position) }
    }

    inner class NotificationViewHolder(private val binding: ItemNotificationBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: Notification, position: Int) = with(binding) {
            model = item
            setNotificationIcon(item, binding)
            if(item.action == BIRTHDAYS) binding.birthday=true else binding.birthday=false
            if(item.action == PRESIDENT) binding.president=true else binding.president=false
            notificationTime.text = DateFormatHelper.humanizeMessageTime(item.parsedCreatedTime,root.context)

            positiveAction = listener.getPositiveAction(item)
            negativeAction = listener.getNegativeAction(item)
            statusChip = listener.getActionStatus(item)
            actionMore.visibility = if(listener.showMoreAction(item)) View.VISIBLE else View.GONE
            isRead = item.isRead
            actionPositive.setOnClickListener { listener.onPositiveActionClick(item) }
            actionNegative.setOnClickListener { listener.onNegativeActionClick(item) }
            actionMore.setOnClickListener { listener.onMoreActionClick(item,it) }

            root.setOnClickListener { listener.onItemClick(item, position) }
        }
    }

    fun setNotificationIcon(item: Notification, binding: ItemNotificationBinding) {
        if(item.action == PRESIDENT) {
          if(item.assosiateData?.thumbnailUrl==null){
              binding.imgNotification.setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.im_user_placeholder_opaque))
          } else {
              Glide.with(binding.root.context).load(item.assosiateData.thumbnailUrl).apply(RequestOptions.bitmapTransform(CropCircleTransformation())).into(binding.imgNotification)
          }
        }else if(item.action == FLIX_PURCHASED){
            if(item.assosiateData?.profileUrl.isNullOrEmpty()){
                binding.imgNotification.setImageDrawable(ContextCompat.getDrawable(binding.root.context, R.drawable.im_user_placeholder_opaque))
            } else {
                Glide.with(binding.root.context).load(item.assosiateData?.profileUrl).apply(RequestOptions.bitmapTransform(CropCircleTransformation())).into(binding.imgNotification)
            }
        } else {
            if (item.iconPath == null) {
                val icon = when (item.action) {
                    SUBSCRIPTION_RENEW_FAILURE, SUBSCRIPTION_FAILURE -> R.drawable.ic_notification_subscription_failed
                    RENEW_SUBSCRIPTION_REMINDER, RENEW_SUBSCRIPTION -> R.drawable.ic_notification_reminder
                    NEW_SUBSCRIPTION -> R.drawable.ic_notification_new_subscription
                    HUDDLE_DELETED -> R.drawable.ic_notification_huddle_deleted
                    INVITE_PARTICIPANT -> R.drawable.im_user_placeholder_opaque
                    BIRTHDAYS -> R.drawable.im_user_placeholder_opaque
                    else -> R.drawable.ic_notification_administrator
                }
                binding.imgNotification.setImageDrawable(ContextCompat.getDrawable(binding.root.context, icon))
            } else {
                Glide.with(binding.root.context).load(item.iconPath).apply(RequestOptions.bitmapTransform(CropCircleTransformation())).into(binding.imgNotification)
            }
        }

    }

    object DearsDiff : DiffUtil.ItemCallback<Notification>() {
        override fun areItemsTheSame(oldItem: Notification, newItem: Notification) = oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: Notification, newItem: Notification) = oldItem == newItem
    }
}
