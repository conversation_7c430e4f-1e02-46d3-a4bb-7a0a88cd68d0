package com.app.messej.ui.home.publictab.broadcast

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.databinding.ItemUserWithActionStatsBinding

class PublicBroadcastListAdapter(private val listener: ItemListener) : PagingDataAdapter<UserRelative,PublicBroadcastListAdapter.BroadCastListViewHolder>(UserRelativeDiff) {

    interface ItemListener {
        fun onItemClick(user:UserRelative)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) = BroadCastListViewHolder(
        ItemUserWithActionStatsBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
    )

    override fun onBindViewHolder(holder: BroadCastListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    inner class BroadCastListViewHolder(private val binding: ItemUserWithActionStatsBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: UserRelative) = with(binding) {
            user=item
            action = null
            clickable = true
            if (item.relativeType == FollowerType.LIKER && item.broadcastLikersPrivacy == true) removedFromBroadcastIcon.visibility = View.VISIBLE else View.GONE
            root.setOnClickListener {
                listener.onItemClick(item)
            }
        }
    }

    object UserRelativeDiff : DiffUtil.ItemCallback<UserRelative>() {
        override fun areItemsTheSame(oldItem: UserRelative, newItem: UserRelative) =
            oldItem.id == newItem.id

        override fun areContentsTheSame(oldItem: UserRelative, newItem: UserRelative) =
            oldItem == newItem
    }
}
