package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.databinding.FragmentPodiumBlockedUsersBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.kennyc.view.MultiStateView

class PodiumBlockedUsersFragment : Fragment() {

    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

//    private var mBlockedUsersListAdapter: BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumBlockedUserBinding>>? = null
        private var mBlockedUsersListAdapter: PodiumBlockedUsersListAdapter? = null

    private lateinit var binding: FragmentPodiumBlockedUsersBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_blocked_users, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setEmptyView()
        setup()
        observe()
    }

    override fun onResume() {
        super.onResume()
        mBlockedUsersListAdapter?.refresh()
    }

    fun setup(){
        initAdapter()
    }

    fun observe(){

        viewModel.blockedUsersList.observe(viewLifecycleOwner) {
            it?: return@observe
            mBlockedUsersListAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

//        viewModel.blockedUsersListLoading.observe(viewLifecycleOwner) {
//            binding.multiStateView.viewState = if (it) MultiStateView.ViewState.LOADING
//            else if (mBlockedUsersListAdapter?.data?.size == 0) {
//                MultiStateView.ViewState.EMPTY
//            } else {
//                MultiStateView.ViewState.CONTENT
//            }
//        }

        viewModel.onUserBlockToggled.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            mBlockedUsersListAdapter?.refresh()
        }
    }

    private fun initAdapter() {

        mBlockedUsersListAdapter = PodiumBlockedUsersListAdapter(object : PodiumBlockedUsersListAdapter.PodiumActionListener {
            override fun onActionButtonClicked(view: View, speaker: PodiumSpeaker) {
                confirmAction(
                    message = R.string.podium_action_unblock_user_confirm_message
                ) {
                    viewModel.toggleUserBlock(speaker.id,BlockUnblockAction.UNBLOCK)
                }
            }

            override fun AppCompatTextView.setupStatChip(item: PodiumSpeaker) {
                isVisible = false
                if (viewModel.podiumKind.value == PodiumKind.THEATER) {
                    val stat = if (item.blockedFromPodium==true) getString(R.string.podium_theater_block_status_theater)
                    else if (item.blockedFromStage==true && item.blockedFromAudience==true) getString(R.string.podium_theater_block_status_stage_audience)
                    else if(item.blockedFromStage==true) getString(R.string.podium_theater_block_status_stage)
                    else if (item.blockedFromAudience==true) getString(R.string.podium_theater_block_status_audience)
                    else null

                    text = stat
                    isVisible = !stat.isNullOrEmpty()
                }
            }
        }, resources.getString(R.string.common_unblock))

        binding.blockedUsers.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(true)
            adapter = mBlockedUsersListAdapter
        }

        mBlockedUsersListAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1){
                        MultiStateView.ViewState.EMPTY
                    }else {
                        MultiStateView.ViewState.CONTENT
                    }
                } else {
                    MultiStateView.ViewState.CONTENT
                }
            }
        }

    }

    private var emptyViewBinding: LayoutListStateEmptyBinding? = null

    private fun setEmptyView() {
        try {
            val empty = DataBindingUtil.inflate<LayoutListStateEmptyBinding>(layoutInflater, R.layout.layout_list_state_empty, binding.multiStateView, false)
            binding.multiStateView.setViewForState(empty.root, MultiStateView.ViewState.EMPTY, false)
            emptyViewBinding = empty
            emptyViewBinding?.apply {
                edsEmptyImage.setImageResource(R.drawable.im_eds_podium)
                edsEmptyMessage.text = resources.getString(R.string.podium_blocked_user_eds_message)
            }
            Log.w("BCDFLSL", "setEmptyView: empty view binding has been set!!")
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            Log.e("BCDFLSL", "setEmptyView: ")
        }
    }

}