package com.app.messej.ui.home.publictab.podiums.live

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.livedata.observeAsState
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import com.app.messej.R
import com.app.messej.databinding.FragmentMaidanPlayerStatsBottomSheetBinding
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

class MaidanPlayerStatsBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentMaidanPlayerStatsBottomSheetBinding
    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)
    private val args: MaidanPlayerStatsBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(
            inflater,
            R.layout.fragment_maidan_player_stats_bottom_sheet,
            container,
            false
        )
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        viewModel.getMaidanStats(
            userId = args.userId,
            competitorId = args.competitorId.takeIf { it != -1 })
    }

    private fun observe() {


        binding.maidanPlayerStatsComposeView.setContent {
            val maidanSummaryState = viewModel.maidanStats.observeAsState()
            binding.canSwitch = maidanSummaryState.value?.competitorStats == null
            Log.d("TEST","maidanSummaryState from VM: ${viewModel.maidanStats.value}")
            Log.d("TEST","maidanSummaryState from VM observeAsState: $maidanSummaryState")
            val maidanSummary = maidanSummaryState.value
            Log.d("TEST","maidanSummary: $maidanSummary")

            val speakerState = viewModel.allSpeakers.observeAsState()
            Log.d("TEST","speakerState from VM: ${viewModel.speakers.value}")
            Log.d("TEST","speakerState from VM observeAsState: $speakerState")
            val speaker = speakerState.value
            Log.d("TEST","speaker: $speaker")

            MaidanPlayerSummaryBottomSheet(speaker, maidanSummary)
        }
    }
}