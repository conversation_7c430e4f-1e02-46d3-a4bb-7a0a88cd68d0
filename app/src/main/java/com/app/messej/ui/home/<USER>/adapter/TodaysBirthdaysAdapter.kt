package com.app.messej.ui.home.gift.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.data.model.api.profile.BirthdayUser
import com.app.messej.databinding.ItemBirthdayUserBinding


class TodaysBirthdaysAdapter(private val items: List<BirthdayUser>?, val listener: ActionItemListener) : RecyclerView.Adapter<TodaysBirthdaysAdapter.ItemViewHolder>() {
    interface ActionItemListener {
        fun onItemClick(item: BirthdayUser)
        fun onUserClick(item: BirthdayUser)
    }

    inner class ItemViewHolder(private val binding: ItemBirthdayUserBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: BirthdayUser) {
            binding.birthday = item
            binding.btnSendGift.setOnClickListener {
                listener.onItemClick(item)
            }
            binding.imgBirthday.setOnClickListener {
                listener.onUserClick(item)
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {


        val binding = ItemBirthdayUserBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return ItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        items?.let {
            holder.bind(it.get(position))
        }
    }

    override fun getItemCount(): Int = items?.size ?: 0
}