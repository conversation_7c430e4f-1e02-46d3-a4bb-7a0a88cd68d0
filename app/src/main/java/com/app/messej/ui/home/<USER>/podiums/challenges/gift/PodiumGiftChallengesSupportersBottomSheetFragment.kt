package com.app.messej.ui.home.publictab.podiums.challenges.gift

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.databinding.FragmentPodiumGiftChallengeSupportesBottomSheetBinding
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.ViewUtils

class PodiumGiftChallengesSupportersBottomSheetFragment  : ExpandableListBottomSheetDialogFragment(){
    private val args : PodiumGiftChallengesSupportersBottomSheetFragmentArgs by navArgs()

    private lateinit var binding: FragmentPodiumGiftChallengeSupportesBottomSheetBinding

    private var mAdapter: PodiumGiftChallengeSupportersAdapter? = null

    private val viewModel: PodiumGiftChallengeSupportersViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_gift_challenge_supportes_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
        observe()
        viewModel._podiumChallengeData.value = Pair(args.podiumId, args.challengeId)
    }

    fun setup() {
        initAdapter()
    }

    fun observe() {
        viewModel.giftChallengeSupportersList.observe(viewLifecycleOwner){pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }
        viewModel.podiumChallengeData.observe(viewLifecycleOwner) {
            Log.d("PGC", "observe: ")
        }
    }

    private fun initAdapter() {
        mAdapter = PodiumGiftChallengeSupportersAdapter()
        val layoutMan = LinearLayoutManager(context)

        binding.challengeSupporters.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                viewModel.challengeSupportersListLoading.postValue(loadState.append is LoadState.Loading)
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }

}