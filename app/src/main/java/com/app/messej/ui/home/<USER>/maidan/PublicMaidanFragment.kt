package com.app.messej.ui.home.publictab.maidan

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.annotation.CallSuper
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.MaidanTab
import com.app.messej.databinding.FragmentPodiumMaidanBinding
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe


class PublicMaidanFragment : Fragment(), MenuProvider {

    private lateinit var binding: FragmentPodiumMaidanBinding

    private lateinit var mPodiumPagerAdapter: FragmentStateAdapter

    private val viewModel: PublicMaidanViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_maidan, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setupPager()
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.appbar.toolbar, customBackButton = false)
        binding.appbar.toolBarTitle.text = getString(R.string.podium_challenge_maidan)
        bindFlaxRateToolbarChip(binding.appbar.flaxRateChip)
    }

    @CallSuper
    private fun setup() {

        binding.podiumPager.apply {
            isUserInputEnabled = false
            adapter = mPodiumPagerAdapter
        }
        binding.btnChallenge.setOnClickListener {
            viewModel.setCurrentTab(MaidanTab.CHALLENGE)
        }
        binding.btnWatch.setOnClickListener{
            viewModel.setCurrentTab(MaidanTab.WATCH)
        }

        binding.btnAdd.setOnClickListener {
            viewModel.checkStatus()
        }

        binding.btnMaidanStats.setOnClickListener {
            viewModel.setCurrentTab(MaidanTab.STATS)
        }

        viewModel.currentTab.value?.let {
            binding.podiumPager.setCurrentItem(it.ordinal, false)
        }

        binding.switchMaidanOpt.setOnCheckedChangeListener { switch, isChecked ->
            if (switch.isPressed) {
                confirmAction(
                    message = if (isChecked) R.string.podium_maidan_invitation_prompt else R.string.podium_maidan_invitation_disable_prompt,
                    positiveTitle = if (isChecked) R.string.podium_maidan_opt_in else R.string.podium_maidan_opt_out,
                    onCancel = {
                        switch.isChecked = !isChecked
                    }
                ) {
                    viewModel.toggleOptIn(isChecked)
                }
            }
        }
    }
    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            if (binding.podiumPager.currentItem==it.ordinal) return@observe
            binding.podiumPager.setCurrentItem(it.ordinal,false)
        }
        viewModel.maidanMeta.observe(viewLifecycleOwner) {
            activity?.invalidateOptionsMenu()
        }

        viewModel.onOptInOut.observe(viewLifecycleOwner) {
            showToast(if (it) R.string.podium_maidan_opt_in_success else R.string.podium_maidan_opt_out_success)
        }

        viewModel.hasCoinsToStartChallenge.observe(viewLifecycleOwner) {
            //Remove balance checking
            viewModel.setCurrentTab(MaidanTab.CREATE)
//            if (it) {
//                viewModel.setCurrentTab(MaidanTab.CREATE)
//            } else {
//                confirmAction(
//                    message = getString(R.string.podium_maidan_minimum_coins, 100.toString()),
//                    positiveTitle = R.string.title_buy_coins,
//                    negativeTitle = R.string.common_close,
//                ) {
//                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = true))
//                }
//            }
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        Log.w("MNUF", "PPBF: onCreateMenu")
        return menuInflater.inflate(R.menu.menu_home_maidan, menu)
    }

    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        val checkable = menu.findItem(R.id.action_notify)
        checkable.setChecked(viewModel.maidanMeta.value?.notifyStartChallenge==true)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_notify -> {
                val notify = !menuItem.isChecked
                confirmAction(
                    message = if (notify) R.string.podium_maidan_notify_prompt else R.string.podium_maidan_notify_disable_prompt,
                    positiveTitle = if (notify) R.string.podium_maidan_notify else R.string.common_confirm
                ) {
                    viewModel.toggleNotify(notify)
                    menuItem.setChecked(notify)
                }
                return true
            }
            R.id.action_about -> {
                findNavController().navigateSafe(
                    PublicMaidanFragmentDirections.actionGlobalPolicyFragment(
                        DocumentType.PODIUM_ABOUT_MAIDAN_CHALLENGE_DETAILS,
                        isButtonVisible = false
                    )
                )
            }
            else -> return false
        }
        return true
    }

    private fun setupPager(){
        mPodiumPagerAdapter = object: FragmentStateAdapter(this) {
            override fun getItemCount(): Int = MaidanTab.entries.size

            override fun createFragment(position: Int): Fragment {
                //                return PodiumInnerListFragment().apply {
//                    arguments = PodiumInnerListFragment.getTabBundle(tab)
//                }

                return when (val tab = MaidanTab.entries[position]) {
                    MaidanTab.CHALLENGE,MaidanTab.WATCH  -> {
                        PodiumMaidanInnerListFragment().apply {
                            arguments = PodiumMaidanInnerListFragment.getTabBundle(tab)
                        }
                    }
                    MaidanTab.CREATE -> CreateMaidanFragment()
                    MaidanTab.STATS -> PodiumMaidanStatsFragment()
                }
            }
        }
    }
}