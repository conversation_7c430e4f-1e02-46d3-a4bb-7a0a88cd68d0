package com.app.messej.ui.home.publictab.podiums.challenges.gift

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.api.podium.challenges.PodiumGiftSupportersResponse
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn

class PodiumGiftChallengeSupportersViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)
    private val profileRepo = ProfileRepository(application)

    val challengeSupportersListLoading = MutableLiveData(false)

    val _podiumChallengeData = MutableLiveData<Pair<String?, String?>>()
    val podiumChallengeData: LiveData<Pair<String?,String?>> = _podiumChallengeData

    val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )
    private val nickNamesLiveData = nickNames.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    private val _giftChallengeSupportersList = podiumChallengeData.switchMap {
        it.first ?: return@switchMap null
        it.second ?: return@switchMap null
        it.first?.let { podiumId ->
            podiumRepository.getPodiumGiftChallengeSupportersListingPager(podiumId, it.second).liveData.cachedIn(viewModelScope)
        }
    }

    val giftChallengeSupportersList: MediatorLiveData<PagingData<PodiumGiftSupportersResponse.ChallengeSupporter>> by lazy {
        val med = MediatorLiveData<PagingData<PodiumGiftSupportersResponse.ChallengeSupporter>>()
        fun update() {
            val data = _giftChallengeSupportersList.value?.map { supporter ->
                supporter.copy(
                    name = nickNames.nickNameOrName(supporter.id, supporter.name)
                )
            }
            med.postValue(data)
        }
        med.addSource(nickNamesLiveData) { update() }
        med.addSource(_giftChallengeSupportersList) { update() }
        med
    }
}