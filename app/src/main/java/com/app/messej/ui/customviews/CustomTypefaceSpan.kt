package com.app.messej.ui.customviews

import android.graphics.Paint
import android.graphics.Typeface
import android.text.TextPaint
import android.text.style.MetricAffectingSpan

class CustomTypefaceSpan(family: String?, typeface: Typeface?) : MetricAffectingSpan() {
    private val typeface: Typeface

    init {
        requireNotNull(typeface) { "typeface is null" }
        this.typeface = typeface
    }

    override fun updateDrawState(tp: TextPaint) {
        applyCustomTypeface(tp)
    }

    override fun updateMeasureState(tp: TextPaint) {
        applyCustomTypeface(tp)
    }

    private fun applyCustomTypeface(paint: Paint) {
        val oldTypeface = paint.typeface
        val oldStyle = oldTypeface?.style ?: 0
        val fakeStyle = oldStyle and typeface.style.inv()
        if (fakeStyle and Typeface.BOLD != 0) {
            paint.isFakeBoldText = true
        }
        if (fakeStyle and Typeface.ITALIC != 0) {
            paint.textSkewX = -0.25f
        }
        paint.typeface = typeface
    }
}