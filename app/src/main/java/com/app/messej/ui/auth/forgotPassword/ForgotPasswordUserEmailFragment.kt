package com.app.messej.ui.auth.forgotPassword

import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.core.widget.addTextChangedListener
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.LayoutForgotPasswordEmailBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class ForgotPasswordUserEmailFragment :Fragment(){

    private var recoveryType: OTPRequestMode?=null
    private lateinit var binding: LayoutForgotPasswordEmailBinding
    private val viewModel: ForgotPasswordViewModel by viewModels({ requireParentFragment() })
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.layout_forgot_password_email,container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }
    private fun setup() {
        recoveryType=if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getSerializable(RECOVERY_TYPE,OTPRequestMode::class.java)
        }
        else{
            arguments?.getSerializable(RECOVERY_TYPE) as OTPRequestMode
        }
        binding.forgotPasswordEmailAddress.editText?.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (!hasFocus && !this.text.isNullOrEmpty()) {
                    viewModel.didEnterEmail.postValue(true)
                }else{
                    viewModel.didEnterEmail.postValue(false)
                }
                addTextChangedListener {
                    text ->
                        viewModel.didEnterPhoneNumber.value
                        if(UserInfoUtil.isEmailValid(text.toString())||text.toString().isEmpty()){
                            viewModel.setEmailValid(true)
                        }else{
                            viewModel.setEmailValid(false)
                        }

                }
            }
        }

        binding.root.rootView.viewTreeObserver.addOnGlobalLayoutListener {
            val r = Rect()
            binding.root.rootView.getWindowVisibleDisplayFrame(r)
            val screenHeight: Int = binding.root.rootView.height
            val keypadHeight: Int = screenHeight - r.bottom
            if (keypadHeight < screenHeight * 0.15) {
                binding.forgotPasswordEmailAddress.editText!!.clearFocus()
            }
        }
        binding.forgotPasswordEmailAddress.editText?.setOnEditorActionListener {
                view, actioId, event ->
            binding.forgotPasswordEmailAddress.editText!!.clearFocus()
            return@setOnEditorActionListener false
        }
        binding.forgotPasswordNextButton.apply {
            setOnClickListener {
                if(binding.forgotPasswordEmailAddress.editText?.text.toString().isNotEmpty()) {
                    binding.forgotPasswordEmailError.text = ""
                    viewModel.verifyEmailAccount(email =binding.forgotPasswordEmailAddress.editText?.text.toString() , OTPRequestMode = OTPRequestMode.RESET_EMAIL)
                }
            }
        }
    }
    private fun observe() {

        viewModel.showEmailInvalidError.observe(viewLifecycleOwner) {
            if (!binding.forgotPasswordEmailAddress.editText?.text.isNullOrEmpty()) {
               binding.forgotPasswordEmailError.text = if (it) {
                    binding.forgotPasswordNextButton.isEnabled = false
                    binding.forgotPasswordEmailError.text
                    resources.getString(R.string.forgot_password_error_email)
                } else {
                    ""; null
                }
            }
        }
        viewModel.isEmailNextButtonVisible.observe(viewLifecycleOwner){
            if (!binding.forgotPasswordEmailAddress.editText?.text.isNullOrEmpty()) {
                binding.forgotPasswordNextButton.isEnabled = it == false
            }
        }

       viewModel.isEmailVerified.observe(viewLifecycleOwner){
           if(it == true){
              val email=binding.forgotPasswordEmailAddress.editText?.text.toString()
               if(email.isNotEmpty()) {
                   findNavController().navigateSafe(ForgotPasswordFragmentDirections.actionGlobalAuthOTPFragment(OTPRequestMode.RESET_EMAIL, "", "", binding.forgotPasswordEmailAddress.editText?.text.toString()))
               }
           }
       }

        viewModel.verifyEmailError.observe(viewLifecycleOwner){
            binding.forgotPasswordNextButton.isEnabled = false
            binding.forgotPasswordEmailError.text=it
        }

        viewModel.unSelectedPage.observe(viewLifecycleOwner){
            if(it==0){ binding.forgotPasswordEmailAddress.editText?.setText("")
                binding.forgotPasswordEmailError.text = ""
                binding.forgotPasswordNextButton.isEnabled=false
            }
        }
    }
    companion object {
        private const val RECOVERY_TYPE = "recoveryType"
        fun newInstance( type: OTPRequestMode) = ForgotPasswordUserEmailFragment().apply {
            arguments = bundleOf(
                RECOVERY_TYPE to type)
        }
    }
}