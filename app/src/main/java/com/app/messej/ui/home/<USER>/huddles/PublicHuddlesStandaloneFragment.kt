package com.app.messej.ui.home.publictab.huddles

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.HuddleTab
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.databinding.FragmentHomePublicHuddlesBinding
import com.app.messej.databinding.LayoutPublicHuddlesBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureHuddlePostingAllowed
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.SubscriptionStatusViewModel
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.home.publictab.HomePublicFragmentDirections
import com.app.messej.ui.utils.FragmentExtensions.adjustForNotifications
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindGiftRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.bindLegalAffairsPayFineButton
import com.app.messej.ui.utils.FragmentExtensions.bindMaidanToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.setBadgeNumber
import com.app.messej.ui.utils.FragmentExtensions.setupPayFineIcon
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.badge.BadgeDrawable
import com.google.android.material.badge.BadgeUtils

class PublicHuddlesStandaloneFragment : PublicHuddlesBaseFragment() {

    override lateinit var binding: LayoutPublicHuddlesBinding

    private lateinit var outerBinding: FragmentHomePublicHuddlesBinding

    private val homeViewModel: CommonHomeViewModel by activityViewModels()
    private val subscriptionStatusViewModel: SubscriptionStatusViewModel by activityViewModels()


    private val args: PublicHuddlesStandaloneFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        outerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_home_public_huddles, container, false)
        outerBinding.lifecycleOwner = viewLifecycleOwner
        binding = outerBinding.layout
        binding.viewModel = viewModel
        return outerBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val tab = HuddleTab.values().getOrElse(args.subtab) {HuddleTab.TAB_JOINED }
        viewModel.setCurrentTab(tab,true)
        observe()
        outerBinding.interactionBanner.upgradeTitle.setOnClickListener{
            upgradeToPremium()
        }

        setupPromoBoard(outerBinding.promoBar)
        setupPayFineIcon(composeView = outerBinding.payFine)
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(outerBinding.appbar.toolbar, customBackButton = false)
        bindGiftRateToolbarChip(outerBinding.appbar.giftChip)
        bindFlaxRateToolbarChip(outerBinding.appbar.flaxRateChip)
        bindMaidanToolbarChip(outerBinding.appbar.maidanChip)
        bindLegalAffairsPayFineButton(outerBinding.appbar.payFineChip)
    }

    private var notificationBadge: BadgeDrawable? = null

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_home_notifications,menu)
        super.onCreateMenu(menu, menuInflater)
    }

    @androidx.annotation.OptIn(com.google.android.material.badge.ExperimentalBadgeUtils::class)
    override fun onPrepareMenu(menu: Menu) {
        super.onPrepareMenu(menu)
        notificationBadge = BadgeDrawable.create(requireContext())
        setBadgeNumber(notificationBadge,homeViewModel.unreadNotifications.value)
        notificationBadge?.apply {
            adjustForNotifications(requireContext())
            BadgeUtils.attachBadgeDrawable(this, outerBinding.appbar.toolbar, R.id.action_notifications)
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_notifications -> findNavController().navigateSafe(HomePublicFragmentDirections.actionGlobalNotificationFragment())
            else -> return super.onMenuItemSelected(menuItem)
        }
        return true
    }

    fun observe() {
        homeViewModel.unreadNotifications.observe(viewLifecycleOwner) {
            setBadgeNumber(notificationBadge,it)
        }

        homeViewModel.didDismissUpgradeBannerToday.observe(viewLifecycleOwner) {
            outerBinding.showUpgradeBanner = !it
        }

        outerBinding.upgradeBanner.upgradeBannerLayout.setOnClickListener {
            upgradeToPremium()
        }

        outerBinding.upgradeBanner.dismissUpgradeBannerBtn.setOnClickListener {
            homeViewModel.onDismissUpgradeBanner()
        }
        homeViewModel.accountDetails.observe(viewLifecycleOwner){
            outerBinding.citizenship = it?.citizenship
            outerBinding.isPremium = it?.isPremium
            outerBinding.daysLeft = it?.remainingDaysForResident
        }

        subscriptionStatusViewModel.subscriptionLoaded.observe(viewLifecycleOwner) {
            it?.let { clickable ->
                outerBinding.upgradeBanner.clickable = clickable
                outerBinding.interactionBanner.clickable = clickable
            }
        }
    }

    private fun upgradeToPremium() {

        subscriptionStatusViewModel.isActive.value?.let { isActive ->
            when (isActive) {
                UserSubscriptionStatus.ACTIVE, UserSubscriptionStatus.EXPIRED -> {
                    findNavController().navigateSafe(
                        PublicHuddlesStandaloneFragmentDirections.actionGlobalAlreadySubscribedFragment(false)
                    )
                }

                else -> {
                    findNavController().navigateSafe(PublicHuddlesStandaloneFragmentDirections.actionGlobalUpgradePremiumFragment())
                }
            }
        }
    }

    override fun navigateToSearch() {
        if (viewModel.currentTab.value==HuddleTab.TAB_SUGGESTED) {
            findNavController().navigateSafe(PublicHuddlesStandaloneFragmentDirections.actionPublicHuddlesStandaloneFragmentToPublicHuddleSuggestionSearchFragment())
        } else {
            viewModel.currentTab.value?.let {tab->
                findNavController().navigateSafe(PublicHuddlesStandaloneFragmentDirections.actionPublicHuddlesStandaloneFragmentToPublicHuddleSearchFragment(tab))
            }
        }
    }

    override fun navigateToCreate() {
        ensureHuddlePostingAllowed {
            findNavController().navigateSafe(PublicHuddlesStandaloneFragmentDirections.actionGlobalCreateHuddleFragment(HuddleType.PUBLIC))
        }
    }


}