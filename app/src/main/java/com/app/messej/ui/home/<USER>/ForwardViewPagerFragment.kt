package com.app.messej.ui.home.forward

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.api.forward.Forward
import com.app.messej.data.model.api.forward.ForwardRequest
import com.app.messej.data.model.enums.ForwardType
import com.app.messej.databinding.FragmentForwardBinding
import com.app.messej.databinding.LayoutEmptyPollsBinding
import com.app.messej.ui.home.forward.adapter.ForwardAdapter
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.kennyc.view.MultiStateView

class ForwardViewPagerFragment : Fragment() {

    private lateinit var binding: FragmentForwardBinding
    private val forwardViewModel: ForwardViewModel by viewModels()
    private val commonForwardViewModel: ForwardCommonViewModel by navGraphViewModels(R.id.navigation_forward)
    private var mAdapter: ForwardAdapter? = null

    companion object {
        private const val TAB_TYPE = "tabType"
        fun setParams(tab: Int) = Bundle().apply {
            putInt(TAB_TYPE, tab)

        }

        fun getTab(bundle: Bundle?) = bundle?.getInt(TAB_TYPE)

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_forward, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = forwardViewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    private fun initAdapter() {
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = ForwardAdapter(object : ForwardAdapter.ActionListener {
            override fun onItemClick(forward: Forward, position: Int) {
                onAdapterClick(forward, position)
            }

        })
        val layoutMan = LinearLayoutManager(context)
        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) MultiStateView.ViewState.EMPTY else MultiStateView.ViewState.CONTENT
                } else MultiStateView.ViewState.CONTENT
                forwardViewModel.setLoading(loadState.append is LoadState.Loading || !loadState.prepend.endOfPaginationReached || !loadState.prepend.endOfPaginationReached)
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }

        binding.forwardList.layoutManager = layoutMan
        binding.forwardList.adapter = mAdapter
    }

    private fun setup() {
        forwardViewModel.setTab(getTab(arguments))
        commonForwardViewModel.enableSend.observe(viewLifecycleOwner) {
            forwardViewModel.setOptionSelected(it)
        }
        initAdapter()
        binding.fabForward.setOnClickListener {
            commonForwardViewModel.shareMessage()
        }
    }

    private fun observe() {
        forwardViewModel.forwardList.observe(viewLifecycleOwner) {
            mAdapter?.submitData(lifecycle, it)
        }

        forwardViewModel.tab.observe(viewLifecycleOwner) {
            val emptyView: View? = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY)
            val emptyViewBinding = LayoutEmptyPollsBinding.bind(emptyView!!)
            when (it) {
                ForwardType.MESSAGES -> emptyViewBinding.pollsEmptyTitle.text = getString(R.string.title_forward_empty_chat)
                ForwardType.GROUPS,ForwardType.HUDDLES -> emptyViewBinding.pollsEmptyTitle.text = getString(R.string.title_forward_empty_huddle)
                else -> {}
            }
        }
    }

    private fun onAdapterClick(forward: Forward, position: Int) {
        if (commonForwardViewModel.forwardRequestList.size <= (commonForwardViewModel.messageLimit - 1)) {
            forward.isSelected = forward.isSelected == false
            if (forward.isSelected == true) {
                commonForwardViewModel.addForwardList(ForwardRequest(forward = forward, forwardViewModel.tab.value))
            } else {
                commonForwardViewModel.removeForwardList(ForwardRequest(forward = forward, forwardViewModel.tab.value))
            }
            mAdapter?.notifyItemChanged(position)
        } else {
            if (commonForwardViewModel.forwardRequestList.size == commonForwardViewModel.messageLimit) {
                if (forward.isSelected == true) {
                    forward.isSelected = false
                    commonForwardViewModel.removeForwardList(ForwardRequest(forward = forward, forwardViewModel.tab.value))
                    mAdapter?.notifyItemChanged(position)
                } else {
                    commonForwardViewModel.setLimitExceeds()
                }
            } else {
                commonForwardViewModel.setLimitExceeds()
            }
        }
    }
}
