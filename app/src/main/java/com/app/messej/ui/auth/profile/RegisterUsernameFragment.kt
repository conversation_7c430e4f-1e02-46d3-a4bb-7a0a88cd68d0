package com.app.messej.ui.auth.profile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.widget.doOnTextChanged
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResultListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterCreateUsernameBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class RegisterUsernameFragment : Fragment() {

    private lateinit var binding: FragmentRegisterCreateUsernameBinding

    private val viewModel: RegisterUsernameViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_create_username, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
        setup()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(binding.customActionBar.toolbar)
            showHomeButton(false)
        }
    }

    private fun setup() {
        binding.registerCheckUsernameButton.setOnClickListener {
            viewModel.checkUsernameAvailability()
        }

        binding.createUsernameNextButton.setOnClickListener {
            val action = RegisterUsernameFragmentDirections.actionCreateUsernameFragmentToRegisterUsernameConfirmFragment(viewModel.username.value!!)
            findNavController().navigateSafe(action)
        }
        binding.editTextUsername.doOnTextChanged { text, start, before, count ->
            binding.textInputUsername.isErrorEnabled = false
            binding.textInputUsername.error = null
            binding.textInputUsername.helperText = null
            binding.textInputUsername.isEndIconVisible = false
        }
    }

    private fun observe() {
        viewModel.usernameValid.observe(viewLifecycleOwner) { it ->
            if (!it) {
                binding.textInputUsername.helperText = null
                binding.textInputUsername.isEndIconVisible = false

            }
        }

        viewModel.onCheckUsernameComplete.observe(viewLifecycleOwner) {
            it?.let {
                binding.textInputUsername.helperText = it
                binding.textInputUsername.isEndIconVisible = true
            }
        }

        viewModel.setUsernameError.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.checkUsernameError.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                binding.textInputUsername.isErrorEnabled = true
                binding.textInputUsername.error = it
            } else {
                binding.textInputUsername.isErrorEnabled = false
                binding.textInputUsername.error = null
            }
        }

        //To listen username confirm click from RegisterUsernameConfirmFragment
        setFragmentResultListener(RegisterUsernameConfirmFragment.USERNAME_CONFIRM_REQUEST_KEY) { _, bundle ->
            val result = bundle.getBoolean(RegisterUsernameConfirmFragment.USERNAME_CONFIRM_RESULT_KEY)
            if (result) viewModel.setUsername()
        }

        viewModel.onSetUsernameComplete.observe(viewLifecycleOwner) {
            if (it) {
                findNavController().navigateSafe(RegisterUsernameFragmentDirections.actionCreateUsernameFragmentToNavigationRegisterSuperstar())
            }
        }

    }

}