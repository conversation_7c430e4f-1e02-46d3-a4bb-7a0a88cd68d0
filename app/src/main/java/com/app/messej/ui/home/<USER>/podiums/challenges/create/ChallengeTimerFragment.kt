package com.app.messej.ui.home.publictab.podiums.challenges.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.navGraphViewModels
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.databinding.FragmentChallengeTimerBinding
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe


class ChallengeTimerFragment : Fragment() {

    private lateinit var binding: FragmentChallengeTimerBinding
    private val viewModel: PodiumCreateChallengeViewModel by navGraphViewModels(R.id.nav_challenge_setup)

    companion object {
        private const val MIN_TIMER_VALUE = 0
        private const val MAX_HOUR_VALUE = 4
        private const val MAX_MINUTES_SECONDS_VALUE = 59
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_challenge_timer, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observer()
        setUp()
    }

    private fun observer() {
        viewModel.onChallengeTimerSet.observe(viewLifecycleOwner) {
            when(viewModel.challengeType.value){
                ChallengeType.LIKES -> {
                    val options = NavOptions.Builder()
                        .setPopUpTo(R.id.challengeTimerFragment, inclusive = true)
                        .build()
                    val action = ChallengeTimerFragmentDirections.actionChallengeTimerFragmentToPodiumChallengePrizeContributorFragment()
                    findNavController().navigateSafe(action, options)
                }
                ChallengeType.GIFTS -> {
                    viewModel.startPodiumChallenge()
                }
                else -> {}
            }
        }

        viewModel.startChallengeAPIFinished.observe(viewLifecycleOwner) {
            if(it) {
                findNavController().popBackStack(R.id.nav_challenge_setup, true)
            }
        }

        viewModel.participantsCountNotMet.observe(viewLifecycleOwner) {
            Toast.makeText(
                requireContext(), resources.getString(
                    R.string.podium_challenge_participants_count_not_met,
                    it.first.toString(),
                    it.last.toString()
                ), Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun setUp() {
        binding.apply {
            seconds.minValue = MIN_TIMER_VALUE
            seconds.maxValue = MAX_MINUTES_SECONDS_VALUE
            seconds.setFormatter { value ->
                value.toString()
            }

            hours.minValue = MIN_TIMER_VALUE
            hours.maxValue = MAX_HOUR_VALUE
            hours.setFormatter { value ->
                value.toString()
            }

            minutes.minValue = MIN_TIMER_VALUE
            minutes.maxValue = MAX_MINUTES_SECONDS_VALUE
            minutes.setFormatter { value ->
                value.toString()
            }
        }

        binding.actionNext.setOnClickListener {
            viewModel.setChallengeTimer(viewModel.getSelectedTimerSeconds())
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.podium_challenge_timer_title)
    }
}