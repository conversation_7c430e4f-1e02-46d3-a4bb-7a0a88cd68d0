package com.app.messej.ui.auth.common

import android.app.Application
import android.location.Address
import android.location.Geocoder
import android.os.Build
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.app.messej.data.model.api.profile.RegisterLocationRequest
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ProfileRepository
import com.google.android.gms.maps.model.LatLng
import com.hadilq.liveevent.LiveEvent
import java.util.Locale

abstract class BaseLocationSetViewModel(application: Application) : AndroidViewModel(application) {

    private var geocoder: Geocoder? = null
    protected val accountRepo = AccountRepository(application)
    protected val profileRepo = ProfileRepository(application)

    init {
        geocoder = Geocoder(application, Locale.getDefault())
    }


    protected val _selectedLocation = MutableLiveData<RegisterLocationRequest?>(null)
    val selectedLocation: LiveData<RegisterLocationRequest?> = _selectedLocation

    val isLocationSelected = _selectedLocation.map {
        return@map it!=null && it.locationSkipped==false
    }

    val canConfirmLocation: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun combineSources() {
            val loc = _selectedLocation.value
            val loading = _confirmLocationLoading.value?:false
            med.postValue(loc!=null && !loc.isAlreadySaved && !loading)
        }
        med.addSource(_selectedLocation) { combineSources() }
        med.addSource(_confirmLocationLoading) { combineSources() }
        med
    }

    var locationPermanentlyDenied = false

    fun selectLocation(latLng: LatLng){
        setAddressFromGeoCoder(latLng.latitude, latLng.longitude) { loc ->
            onSelectLocation(loc)
            _selectedLocation.postValue(loc)
        }
    }

    fun clearLocation(){
        _selectedLocation.value = null
    }

    protected val _confirmLocationLoading = MutableLiveData(false)
    val confirmLocationLoading: LiveData<Boolean> = _confirmLocationLoading

    protected val _setLocationError = MutableLiveData<String?>(null)
    val setLocationError: LiveData<String?> = _setLocationError

    val onLocationConfirmed = LiveEvent<Boolean>()

    abstract fun confirmLocation()

    protected open fun onSelectLocation(loc: RegisterLocationRequest) {}

    protected fun setAddressFromGeoCoder(lat: Double, lng: Double, callback: ((RegisterLocationRequest) -> Unit)? = null){
        geocoder?.getAddress(lat, lng){address: Address? ->
            if (address != null) {
                val registerLocationRequest = RegisterLocationRequest()
                registerLocationRequest.country = address.countryName
                registerLocationRequest.countryCode=address.countryCode
                registerLocationRequest.lat = address.latitude.toString()
                registerLocationRequest.long = address.longitude.toString()
                if (!address.locality.isNullOrBlank()){
                    if (!address.subLocality.isNullOrBlank()){
                        registerLocationRequest.name = "${address.subLocality}, ${address.locality}"
                    } else{
                        registerLocationRequest.name = address.locality
                    }
                } else if (!address.subLocality.isNullOrBlank()){
                    registerLocationRequest.name = address.subLocality
                }
                registerLocationRequest.countryCode=address.countryCode
                Log.d("BLSF", "setAddressFromGeoCoder: setting location: $registerLocationRequest")
                callback?.invoke(registerLocationRequest)
            }
        }
    }

    @Suppress("DEPRECATION")
    private fun Geocoder.getAddress(
        latitude: Double,
        longitude: Double,
        address: (Address?) -> Unit
    ) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                getFromLocation(latitude, longitude, 1) { address(it.firstOrNull()) }
            } else {
                address(getFromLocation(latitude, longitude, 1)?.firstOrNull())
            }
        } catch(e: Exception) {
            address(null)
        }
    }
}