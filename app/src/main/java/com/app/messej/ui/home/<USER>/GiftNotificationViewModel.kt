package com.app.messej.ui.home.gift

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.gift.GiftNotificationResponse
import com.app.messej.data.model.api.gift.GiftReplyRequest
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.repository.GiftRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class GiftNotificationViewModel(application: Application):AndroidViewModel(application) {

    private val giftRepository=GiftRepository(application)
    private val profileRepository=ProfileRepository(application)

    private val _gift= MutableLiveData<GiftNotificationResponse?>(null)
    val gift: LiveData<GiftNotificationResponse?> = _gift

    private val _senderID = MutableLiveData<Int?>(null)
    val senderID: LiveData<Int?> = _senderID
    val onDialogueTimeOver = LiveEvent<Boolean>()
    val onGiftReplySend = LiveEvent<Boolean>()
    private val onGiftReplySendError = LiveEvent<String>()
    private val _otherUser = MutableLiveData<OtherUser?>(null)
    private val otherUser: LiveData<OtherUser?> = _otherUser
    private val nickNames: LiveData<List<NickName>> = profileRepository.getNickNamesLiveData()

    private val _isFromSocket = MutableLiveData<Boolean>(false)
    val isFromSocket: LiveData<Boolean> = _isFromSocket

    fun setArgs(giftId: Int, senderId: Int, fromSocket: Boolean) {
        _senderID.postValue(senderId)
        _isFromSocket.postValue(fromSocket)
        getGiftDetails(giftId,fromSocket)
        getUserDetails(senderId)
    }

    private fun getGiftDetails(giftId: Int, fromSocket: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            when(val result = giftRepository.getGift(giftId)){
                is ResultOf.Success -> {
                    if(fromSocket) {
                        handleDelay(result.value.animationTime)
                    }
                    _gift.postValue(result.value)
                }
                is ResultOf.APIError -> {
                    onGiftReplySendError.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    onGiftReplySendError.postValue(result.exception.message)
                }

            }
        }

    }

    private fun getUserDetails(senderId: Int?) {
        viewModelScope.launch(Dispatchers.IO) {
                when(val result = profileRepository.getPublicUserDetails(senderId!!)) {
                    is ResultOf.Success -> {
                        _otherUser.postValue(result.value)
                    }
                    is ResultOf.APIError -> {

                    }
                    is ResultOf.Error -> {
                        onGiftReplySendError.postValue(result.exception.message)
                    }
                }
            }
    }

    val nameOrNickname: MediatorLiveData<String?> by lazy {
        val med = MediatorLiveData<String?>()
        fun update() {
            _otherUser.value?.also {
                med.postValue(nickNames.value.nickNameOrName(it))
            } ?: {
                med.postValue(null)
            }
        }
        med.addSource(nickNames) { update() }
        med.addSource(_otherUser) { update() }
        med
    }

    private fun handleDelay(animationTime: Int?) {
        viewModelScope.launch {
            val delayTime = animationTime?.toLong() ?: 0
            delay(delayTime.times(1000))
            onDialogueTimeOver.postValue(true)
        }

    }

    fun sendReplyGift() {
       viewModelScope.launch {
           when(val result=giftRepository.sendGiftReply(_gift.value?.id?:0, GiftReplyRequest(_senderID.value))){
               is ResultOf.APIError -> {
                   onGiftReplySendError.postValue(result.error.message)
               }
               is ResultOf.Error -> {

               }
               is ResultOf.Success -> {
                   onGiftReplySend.postValue(true)
               }
           }
       }
    }


}