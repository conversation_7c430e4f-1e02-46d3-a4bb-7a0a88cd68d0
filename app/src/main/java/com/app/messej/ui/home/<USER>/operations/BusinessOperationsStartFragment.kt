package com.app.messej.ui.home.businesstab.operations

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.databinding.FragmentBusinessOperationsBinding
import com.app.messej.ui.customviews.ViewExtensions.setBlurredBackground
import com.app.messej.ui.customviews.ViewExtensions.setBlurredImage
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class BusinessOperationsStartFragment : Fragment() {

    private lateinit var binding: FragmentBusinessOperationsBinding
    private val viewModel: BusinessOperationsStartViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_operations, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        /** Update UI from a  background thread*/
        lifecycleScope.launch {
            withContext(Dispatchers.Main) {
                binding.layoutOperations.setBlurredBackground(R.drawable.bg_business, requireContext())
            }
        }
        binding.imgBusinessStartWorkingUnderLine.setBlurredImage(R.drawable.bg_business_operations_start_working, 10, requireContext())
        binding.btnOperationsStatementStartNow.setOnClickListener {
            isPremiumMember()
        }
    }

    private fun isPremiumMember() {
        if (viewModel.isPremiumMember.value == true) {
            viewModel.setStartClicked(true)
            findNavController().navigateSafe(BusinessOperationsStartFragmentDirections.actionBusinessOperationsFragmentStartToBusinessTaskOneFragment())
        } else {
            findNavController().navigateSafe(BusinessOperationsStartFragmentDirections.actionBusinessOperationsFragmentStartToBusinessOperationsPremiumFragment2())
        }
    }

    private fun observe() {

        viewModel.taskOneData.observe(viewLifecycleOwner) {
            it?.let {
                if (it.membership == "Premium") {
                    viewModel.isPremiumMember.postValue(true)
                } else {
                    viewModel.isPremiumMember.postValue(false)
                }
                viewModel.setBusinessStartShown()
            }
        }
        viewModel.isStartPageClicked.observe(viewLifecycleOwner) {
            if (it) {
                isPremiumMember()
            }
        }
    }


}