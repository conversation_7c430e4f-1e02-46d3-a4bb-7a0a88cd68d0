package com.app.messej.ui.home.gift.bottomSheet

import android.graphics.PorterDuff
import android.graphics.Typeface
import android.os.Bundle
import android.text.style.StyleSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentLevelUpgradationBottomSheetBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class LevelUpgradationBottomSheetFragment : BottomSheetDialogFragment() {
    private lateinit var binding: FragmentLevelUpgradationBottomSheetBinding
    private val viewModel: LevelUpgradationBottomSheetViewModel by viewModels()
    private val args: LevelUpgradationBottomSheetFragmentArgs by navArgs()


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        binding = FragmentLevelUpgradationBottomSheetBinding.inflate(inflater, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }
    private fun setUp() {
        viewModel.setArgs(args.userId)
        binding.btnLevelUpgradationClose.setOnClickListener {
            findNavController().popBackStack(R.id.levelUpgradationBottomSheetFragment, true)
        }
        binding.sendGift.setOnClickListener {
            findNavController().navigateSafe(LevelUpgradationBottomSheetFragmentDirections.actionUserLevelUpgradeFragmentToGiftListFragment(args.userId, giftContext = GiftContext.GIFT_USER_LEVEL, userLevelCongrats = true))
        }

    }

    private fun observe() {
        viewModel.profile.observe(viewLifecycleOwner) { profile ->
            Log.w("PRFL", "observe: ${profile?.citizenship}")
            if(profile?.citizenship==null) return@observe

            val userCitizenship =  profile.citizenship
            val formattedCitizenship = if(userCitizenship==UserCitizenship.PRESIDENT)getString(R.string.user_citizenship_president)else userCitizenship.toString()
            val citizenshipType= getString(R.string.level_upgradation_other_title,formattedCitizenship)

            binding.textIsNow.text = citizenshipType.highlightOccurrences(formattedCitizenship) {
                StyleSpan(Typeface.BOLD)
            }
            binding.imageSendGift.setColorFilter(ContextCompat.getColor(requireContext(),if(profile.citizenship==UserCitizenship.PRESIDENT)R.color.colorPrimaryColorDarkest1 else R.color.colorPrimary), PorterDuff.Mode.SRC_IN)

            when (profile.citizenship) {
                UserCitizenship.CITIZEN -> {
                    binding.userType = context?.getString(R.string.user_citizenship_citizen)
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_citizen)
                    binding.userLevelBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_citizen)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.headerTextColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorSecondaryLight)
                    binding.isResident=false
                    binding.isPresident =false
                    binding.userIconTextTint = ContextCompat.getColor(requireContext(), R.color.colorIdCardUserStrength)
                    binding.sendGiftBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_level_upgrade_send_gift_others)
                    binding.sendGift.visibility =View.VISIBLE
                }
                UserCitizenship.OFFICER -> {
                    binding.userType = context?.getString(R.string.user_citizenship_officer)
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_officer)
                    binding.userLevelBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_officer)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.headerTextColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorSecondaryLight)
                    binding.isResident=false
                    binding.isPresident =false
                    binding.userIconTextTint = ContextCompat.getColor(requireContext(), R.color.colorIdCardUserStrength)
                    binding.sendGiftBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_level_upgrade_send_gift_others)
                    binding.sendGift.visibility =View.VISIBLE
                }

                UserCitizenship.AMBASSADOR -> {
                    binding.userType = context?.getString(R.string.user_citizenship_ambassador)
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_ambassador)
                    binding.userLevelBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_ambassador)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_citizen_ambassador)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.colorHuddleTagCitizen)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                    binding.headerTextColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorSecondaryLight)
                    binding.isResident=false
                    binding.isPresident =false
                    binding.userIconTextTint = ContextCompat.getColor(requireContext(), R.color.colorIdCardUserStrength)
                    binding.sendGiftBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_level_upgrade_send_gift_others)
                    binding.sendGift.visibility =View.VISIBLE
                }

                UserCitizenship.MINISTER -> {
                    binding.userType = context?.getString(R.string.user_citizenship_minister)
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_leader)
                    binding.userLevelBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_leader)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_leader)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.headerTextColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.isResident=false
                    binding.isPresident =false
                    binding.userIconTextTint = ContextCompat.getColor(requireContext(), R.color.colorIdCardUserStrength)
                    binding.sendGiftBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_level_upgrade_send_gift_others)
                    binding.sendGift.visibility =View.VISIBLE
                }
                UserCitizenship.PRESIDENT -> {
                    binding.apply {
                        userType = context?.getString(R.string.user_citizenship_president)
                        usertypeColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_president)
                        userLevelBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_user_level_president)
                        textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_president)
                        idColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        textColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        binding.headerTextColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        labelTextColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        userIconTint = ContextCompat.getColor(requireContext(), R.color.colorAlwaysLightPrimary)
                        iconTintColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        isResident = false
                        userIconTextTint = ContextCompat.getColor(requireContext(), R.color.colorIdCardPresidentBackground)
                        isPresident = true
                        layoutHeader.setPadding(48, 32, 60, 32)
                        textCitizenship.setPadding(0, 0, 16, 0)
                        sendGiftBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_level_upgrade_send_gift_president)
                        binding.sendGift.visibility =View.VISIBLE
                    }
                }
                UserCitizenship.GOLDEN -> {
                    binding.apply {
                        userType = context?.getString(R.string.user_citizenship_golden)
                        usertypeColor = ContextCompat.getColor(requireContext(), R.color.white)
                        idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_golden)
                        userLevelBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_user_level_golden)
                        textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_golden)
                        idColor = ContextCompat.getColor(requireContext(), R.color.white)
                        textColor = ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden)
                        binding.headerTextColor = ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden)
                        labelTextColor = ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden)
                        userIconTint = ContextCompat.getColor(requireContext(), R.color.colorFlashatGolden)
                        iconTintColor = ContextCompat.getColor(requireContext(), R.color.colorPrimaryColorDarkest1)
                        isResident = false
                        userIconTextTint = ContextCompat.getColor(requireContext(), R.color.colorIdCardPresidentBackground)
                        isPresident = true
                        layoutHeader.setPadding(48, 32, 60, 32)
                        textCitizenship.setPadding(0, 0, 16, 0)
                        sendGiftBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_upgrade_golden)
                        binding.sendGift.visibility =View.GONE
                    }
                }



                UserCitizenship.RESIDENT -> {
                    binding.userType = context?.getString(R.string.user_citizenship_resident)
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_resident)
                    binding.userLevelBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_resident)
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_resident_latest)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorOnPrimary)
                    binding.isResident=true
                    binding.isPresident =false
                    binding.userIconTextTint = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.sendGift.visibility =View.GONE
                }
                UserCitizenship.VISITOR -> {
                    binding.userType = context?.getString(R.string.user_citizenship_visitor)
                    binding.usertypeColor = ContextCompat.getColor(requireContext(), R.color.textColorAlwaysLightPrimary)
                    binding.idCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_visitor)
                    binding.userLevelBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_idcard_visitor  )
                    binding.textIdCardDrawBackground = ContextCompat.getDrawable(requireContext(), R.drawable.bg_label_idcard_visitor)
                    binding.idColor = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.textColor = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.labelTextColor = binding.textColor
                    binding.userIconTint = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.iconTintColor = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.isResident=true
                    binding.isPresident =false
                    binding.userIconTextTint = ContextCompat.getColor(requireContext(), R.color.textColorOnSecondary)
                    binding.sendGift.visibility =View.GONE
                }
            }

            viewModel.nickNameOrName.observe(viewLifecycleOwner) {
                Log.d(Constants.FLASHAT_TAG, "usernameOrNickName: $it")
            }
            viewModel._countryList.observe(viewLifecycleOwner){
                Log.d("CountryFlag", "countryFlag: ${profile.countryCode}")
                viewModel.getFlag(it,profile.countryCode)
                binding.idCardFlag.setImageResource(viewModel.countryFlag ?: 0)
            }
        }
    }
    override fun getTheme(): Int {
        return R.style.Widget_Flashat_Tribe_BottomSheet
    }

}