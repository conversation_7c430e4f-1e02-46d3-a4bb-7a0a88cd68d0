package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.NotificationAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.Notification
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.DateTimeUtils
import retrofit2.HttpException
import java.io.IOException
import java.time.LocalDate
import java.util.TimeZone

@OptIn(ExperimentalPagingApi::class)
class NotificationListRemoteMediator(private val database: FlashatDatabase, private val networkService: NotificationAPIService) : RemoteMediator<Int, Notification>() {
    private val dao = database.getNotificationDao()

    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = "${EntityDescriptions.TABLE_NOTIFICATIONS}-main"

    override suspend fun initialize(): InitializeAction {
        Log.d("URRM", "load: Initialize")
        return super.initialize()
    }

    override suspend fun load(loadType: LoadType, state: PagingState<Int, Notification>): MediatorResult {
        return try {
            val page = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND ->
                    return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    Log.d("URRM", "load: APPEND")
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPageInt
                }
            }

            val response = networkService.getNotifications(page = page, timeZone = TimeZone.getDefault().id, date = DateTimeUtils.format(
                LocalDate.now(), DateTimeUtils.FORMAT_DATE_YYYYMMDD_SLASHED
            ))
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                }
                val nextPage= if(result.nextPage==true) (page+1).toString() else null
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey,nextPage)
                )

                dao.insertNotifications(result.notifications)
            }
            MediatorResult.Success(endOfPaginationReached =(result.nextPage == false) )
        } catch (e: IOException) {
            MediatorResult.Error(e)
        } catch (e: HttpException) {
            MediatorResult.Error(e)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}