package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName

data class SocialUserStatus(
    @SerializedName(value = "citizenship") val citizenship: UserCitizenship?,
    @SerializedName(value = "coin_balance") val coinBalance: Double?,
    @SerializedName(value = "flashat_age") val flashatAge: Int?,
    @SerializedName(value = "flix_balance") val flixBalance: Double?,
    @SerializedName(value = "user_rating") val userRating: Double?
) {
    companion object {
        val testSocialUserStatus = SocialUserStatus(
            citizenship = UserCitizenship.AMBASSADOR,
            coinBalance = 96406.85,
            flashatAge = 345,
            flixBalance = 300016.00,
            userRating = 100.00
        )
    }
}