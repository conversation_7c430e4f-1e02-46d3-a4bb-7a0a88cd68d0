package com.app.messej.data.model.api.socialAffairs

import android.util.Log
import com.app.messej.data.model.enums.SocialCaseStatus
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import okhttp3.ResponseBody

data class SocialVoteErrorResponse (
    @SerializedName("message") val message: String,
    @SerializedName("result") val result: SocialError?
) {
    companion object {
        fun parseError(response: ResponseBody?): SocialVoteErrorResponse {
            response?: return SocialVoteErrorResponse("", null)
            return try {
                val type = object : TypeToken<SocialVoteErrorResponse>() {}.type
                val str = response.string()
                Log.d("SVER", "Parsing: $str")
                Gson().fromJson(str, type)
            } catch (e: Exception) {
                Log.e("SVER", "Error parsing error", e)
                SocialVoteErrorResponse("", null)
            }
        }
    }

    enum class SocialVoteErrorReason {
        @SerializedName("RATING_REQUIRED") RATING_REQUIRED,
        @SerializedName("AGE_REQUIRED") AGE_REQUIRED,
        @SerializedName("MSA_VOTE") MSA_VOTE,
        @SerializedName("ACTIVE_CASE") ACTIVE_CASE
    }

    data class SocialError(
        @SerializedName("reason") val reason: SocialVoteErrorReason?,
        @SerializedName("rating") val rating: Int?,
        @SerializedName("required_rating") val requiredRating: Int?,
        @SerializedName("age") val flashatAge: Int?,
        @SerializedName("required_age") val requiredAge: Int?,

        @SerializedName("id") val activeCaseId: Int?,
        @SerializedName(value = "status") val activeCaseStatus: SocialCaseStatus?,
    )
}