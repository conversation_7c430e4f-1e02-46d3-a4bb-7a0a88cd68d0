package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PodiumEntry {
    @SerializedName("GENERAL") GENERAL,
    @SerializedName("MEN_ONLY") MEN_ONLY,
    @SerializedName("WOMEN_ONLY") WOMEN_ONLY;

    fun canAccept(gender: Gender?): <PERSON><PERSON><PERSON> {
        return when (this) {
            GENERAL -> true
            MEN_ONLY -> gender == Gender.MALE
            WOMEN_ONLY -> gender == Gender.FEMALE
        }
    }
}