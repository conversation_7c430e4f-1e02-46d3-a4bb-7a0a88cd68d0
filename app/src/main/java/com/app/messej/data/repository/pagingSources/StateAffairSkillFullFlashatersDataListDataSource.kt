package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.StateAffairsAPIService
import com.app.messej.ui.home.publictab.authorities.stateAffairs.UserStateAffair

private const val STARTING_KEY = 1
class StateAffairSkillFullFlashatersDataListDataSource(private val api: StateAffairsAPIService) : PagingSource<Int, UserStateAffair>() {
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, UserStateAffair> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getStateAffairsSkillFullFlashaters(page = currentPage, limit = 50)
            val responseData = mutableListOf<UserStateAffair>()
            val result = response.body()?.result
            val data = result?.stateAffairUserData ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (!response.body()?.result!!.hasNext!!) null else currentPage.plus(1)

            LoadResult.Page(
                data = data, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("StateAffairSkillfullFlashtersDataResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, UserStateAffair>): Int? {
        return null
    }
}