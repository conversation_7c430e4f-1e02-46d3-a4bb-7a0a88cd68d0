package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class ETribeContactOptions {
    @SerializedName("popup") Popup,
    @SerializedName("notification") Notification,
    @SerializedName("private_message") PrivateMessage,
    @SerializedName("email") Email;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)?.value ?: ""
    }

}