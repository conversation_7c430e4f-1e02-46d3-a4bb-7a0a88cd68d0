package com.app.messej.data.model.socket

import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

data class PodiumCameraTimeExpired(
    @SerializedName("podium_id"     ) var podiumId     : String,
    @SerializedName("user_id"       ) var userId       : Int,
    @SerializedName("camera_expiry"       ) var cameraExpiry      : String,
): SocketEventPayload(){
    val parsedExpiryTime: LocalDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(cameraExpiry)?.run { DateTimeUtils.getLocalDateTime(this) }
}