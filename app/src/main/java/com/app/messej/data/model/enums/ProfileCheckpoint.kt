package com.app.messej.data.model.enums

import com.google.gson.TypeAdapter
import com.google.gson.annotations.JsonAdapter
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter
import java.io.IOException

@JsonAdapter(ProfileCheckpoint.ProfileCheckpointAdapter::class)
enum class ProfileCheckpoint(val code: Int) {
    PROFILE_CHECKPOINT_NONE(0),
    PROFILE_CHECKPOINT_PASSWORD_SETUP(1),
    PROFILE_CHECKPOINT_PROFILE_SETUP(2),
    PROFILE_CHECKPOINT_USERNAME_SETUP(3),
//    PROFILE_CHECKPOINT_LOCATION_SETUP(4),
    PROFILE_CHECKPOINT_SUPERSTAR_SETUP(5),
    PROFILE_CHECKPOINT_LOCK_SCREEN(6);

    override fun toString(): String {
//        return javaClass
//            .getField(name)
//            .getAnnotation(SerializedName::class.java)
//            ?.value ?: ""
        return code.toString()
    }

    companion object {
        infix fun from(value: Int): ProfileCheckpoint? = ProfileCheckpoint.values().firstOrNull { it.code == value }
    }

    class ProfileCheckpointAdapter: TypeAdapter<ProfileCheckpoint>() {
        @Throws(IOException::class)
        override fun write(out: JsonWriter, value: ProfileCheckpoint?) {
            if (value == null) {
                out.nullValue()
            } else {
                out.value(value.code)
            }
        }

        @Throws(IOException::class)
        override fun read(reader: JsonReader): ProfileCheckpoint? {
            return if (reader.peek() === JsonToken.NULL) {
                reader.nextNull()
                null
            } else {
                from(reader.nextInt())

            }
        }
    }
}