package com.app.messej.data.repository.mediators

import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class PodiumListingRemoteMediator(
    private val tab: PodiumTab,
    private val database: FlashatDatabase,
    private val networkService: PodiumAPIService,
)  : RemoteMediator<Int, Podium>() {

    private val remoteKeyDao = database.getRemotePagingDao()
    private val dao = database.getPodiumDao()
    private val tableKey = "${EntityDescriptions.TABLE_PODIUMS}-main-${tab.name}"
    override suspend fun load(loadType: LoadType, state: PagingState<Int, Podium>): MediatorResult {

        return try {
            val loadKey = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {

                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }

                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPageInt
                }
            }
            val response = networkService.getPodiumList(tab = tab.serializedName() ,page = loadKey)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }
            database.withTransaction {

                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    dao.deleteAll()
                }

                val nextPage = if (result.hasNextPage) result.currentPage.plus(1) else result.currentPage

                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, nextPage.toString())
                )
                result.podiums.apply {
                    dao.insert(this)
                }
            }
            val ended = !result.hasNextPage || result.podiums.isEmpty() || result.currentPage>=result.totalPages
            MediatorResult.Success(endOfPaginationReached = ended)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}
