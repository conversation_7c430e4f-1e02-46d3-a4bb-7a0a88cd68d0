package com.app.messej.data.model

import com.app.messej.data.model.entity.OfflineMedia

abstract class AbstractChatMessageWithMedia {

    abstract val message: AbstractChatMessage

    abstract var offlineMedia: OfflineMedia?

    val needsMediaUpload: Boolean
        get() {
            return offlineMedia?.uploadState is MediaUploadState.Pending
        }

    val mediaIsUploading: Boolean
        get() {
            return offlineMedia?.uploadState is MediaUploadState.Uploading
        }

    val hasOfflineMedia: <PERSON>ole<PERSON>
        get() = offlineMedia!=null
}