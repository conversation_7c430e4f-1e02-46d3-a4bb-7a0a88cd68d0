package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.PollAnswerRequest
import com.app.messej.data.model.api.poll.CreatePollRequest
import com.app.messej.data.model.api.poll.CreatePollResponse
import com.app.messej.data.model.api.poll.InvitePollRequest
import com.app.messej.data.model.api.poll.PollDeleteRequest
import com.app.messej.data.model.api.poll.PollParticipantResponse
import com.app.messej.data.model.api.poll.PollResponse
import com.app.messej.data.model.entity.Poll
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface PollsAPIService {

    @GET("/huddles/polls/{pollId}/participants")
    @Headers("Accept: application/json")
    suspend fun getPollParticipants(
        @Path("pollId") pollId: Int,
        @Query("limit") limit: Int = 50,
        @Query("page") page: Int,
    ): Response<APIResponse<PollParticipantResponse>>

    @GET("/huddles/polls")
    suspend fun getPollList(
        @Query("limit") limit: Int = 50,
        @Query("page") page: Int,
        @Query("huddle_id") huddleId: Int,
        @Query("status") status: String,
    ): Response<APIResponse<PollResponse>>


    @POST("huddles/polls/answers")
    @Headers("Accept: application/json")
    suspend fun setAnswer(@Body request: PollAnswerRequest): Response<APIResponse<Unit>>

    @PUT("huddles/polls")
    @Headers("Accept: application/json")
    suspend fun deletePoll(@Body pollDeleteRequest: PollDeleteRequest): Response<APIResponse<PollResponse>>

    /**Create POll**/
    @POST("/huddles/polls")
    @Headers("Accept: application/json")
    suspend fun postCreatePoll(@Body req: CreatePollRequest): Response<APIResponse<CreatePollResponse>>

    /**Edit POll**/
    @PUT("/huddles/polls")
    @Headers("Accept: application/json")
    suspend fun editCreatePoll(@Body req: CreatePollRequest): Response<APIResponse<CreatePollResponse>>

    /**Get Polls by ID**/
    @GET("/huddles/polls/{pollId}")
    @Headers("Accept: application/json")
    suspend fun getPollsById(@Path("pollId") pollId: Int): Response<APIResponse<Poll>>

    /**Poll Invite**/
    @POST("/huddles/polls/{pollId}/inviteparticipants")
    @Headers("Accept: application/json")
    suspend fun pollInviteByMember(@Path("pollId") pollId: Int, @Body req: InvitePollRequest): Response<APIResponse<Unit>>
}