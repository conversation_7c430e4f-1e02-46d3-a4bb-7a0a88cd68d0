package com.app.messej.data.model.api.podium

import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.ChallengeContributionType

sealed class PodiumChallengeSetupEvent(open val challengeId: String) {

    data class FacilitatorSelected(override val challengeId: String, val userId: Int, val name: String): PodiumChallengeSetupEvent(challengeId)
    data class FacilitatorConfirmed(override val challengeId: String, val userId: Int, val name: String): PodiumChallengeSetupEvent(challengeId)
    data class FacilitatorDeclined(override val challengeId: String, val userId: Int, val name: String): PodiumChallengeSetupEvent(challengeId)
    data class ContributorRequested(override val challengeId: String, val contributor: PodiumChallenge.ChallengeUser, val contributorType: ChallengeContributionType): PodiumChallengeSetupEvent(challengeId)
    data class ContributorConfirmed(override val challengeId: String, val contributor: PodiumChallenge.ChallengeUser, val contributorType: ChallengeContributionType): PodiumChallengeSetupEvent(challengeId)
    data class ContributorDeclined(override val challengeId: String, val contributor: PodiumChallenge.ChallengeUser, val contributorType: ChallengeContributionType): PodiumChallengeSetupEvent(challengeId)
    data class ContributorTimedOut(override val challengeId: String, val contributorIds: List<Int>): PodiumChallengeSetupEvent(challengeId)
    data class ContributorLowBalance(override val challengeId: String, val userId: Int, val name: String, val contributorType: ChallengeContributionType): PodiumChallengeSetupEvent(challengeId)

    data class DurationSet(override val challengeId: String, val duration: Long): PodiumChallengeSetupEvent(challengeId)

}