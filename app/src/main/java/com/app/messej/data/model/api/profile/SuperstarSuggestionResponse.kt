package com.app.messej.data.model.api.profile

import com.app.messej.data.model.User
import com.google.gson.annotations.SerializedName

data class SuperstarSuggestionResponse(
    @SerializedName("no_country_offset" ) var noCountryOffset   : Int             = 0,
    @SerializedName("current_page"      ) var currentPage       : Int             = 0,
    @SerializedName("next_page"         ) var nextPage          : Boolean         = false,
    @SerializedName("total"             ) var total             : Int             = 0,
    @SerializedName("users"             ) var users             : ArrayList<User> = arrayListOf(),
    @SerializedName("search_type"       ) var searchType        : String?         = null
)
