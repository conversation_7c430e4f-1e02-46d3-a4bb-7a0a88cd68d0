package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.BusinessAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase
import retrofit2.HttpException
import java.io.IOException

@OptIn(ExperimentalPagingApi::class)
class TransferHistoryRemoteMediator(
    private val query: String,
    private val database: FlashatDatabase,
    private val networkService: BusinessAPIService,private val transferFilter:String
) : RemoteMediator<Int, DealsTransferHistory>() {
    private val dao = database.getTransferHistoryDao()
    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = "${EntityDescriptions.TABLE_TRANSFER_HISTORY}-main"

    override suspend fun initialize(): InitializeAction {
        Log.d("MEDIATOR","Enter IInitialize")
        return super.initialize()
    }

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, DealsTransferHistory>
    ): MediatorResult {

        Log.d("MEDIATOR","Enter load")
        return try {
            val page = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    Log.d("PHRM", "load: APPEND")
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }

                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPageInt
                }
            }

            val response = networkService.getTransferHistory("", page = page, 50,transferFilter)
            val result = if (response.isSuccessful && response.code() == 200) {

                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    /*dao.deleteTransferHistory()*/
                }
                val nextPage= if(result.nextPage==true) (page+1).toString() else null
                // Update RemoteKey for this query.
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, nextPage)
                )

                dao.insertAll(result.transfers)
            }

            MediatorResult.Success(endOfPaginationReached = result.nextPage == null)
        } catch (e: IOException) {
            MediatorResult.Error(e)
        } catch (e: HttpException) {
            MediatorResult.Error(e)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}