package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.model.entity.UserRelative
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max
private const val STARTING_KEY = 1
class BlockedBroadcastDataSource (val api: ProfileAPIService) : PagingSource<Int, UserRelative>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, UserRelative> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage =  params.key ?: STARTING_KEY
                val response = currentPage.let { api.getBlockedBroadCastList(it) }
                val responseData = mutableListOf<UserRelative>()
                val data = response.body()?.result?.hiddenUsers ?: emptyList()
                responseData.addAll(data)
                val nextKey = if (response.body()?.result?.hiddenUsers?.isEmpty()!!) null else currentPage.plus(1)

                LoadResult.Page(
                    data = response.body()?.result!!.hiddenUsers, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, UserRelative>): Int? {
        val anchorPosition = state.anchorPosition ?: return null
        val article = state.closestItemToPosition(anchorPosition) ?: return null
        return ensureValidKey(key = article.id - (state.config.pageSize / 2))
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}