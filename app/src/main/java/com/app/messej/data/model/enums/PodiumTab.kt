package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PodiumTab {
    @SerializedName("live-podiums") LIVE_PODIUM,
    @SerializedName("my-podiums") MY_PODIUM,
    @SerializedName("live-friends") LIVE_FRIENDS;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    companion object {
        fun fromInt(value: Int): PodiumTab? {
            return entries.find { it.ordinal == value }
        }
    }
}