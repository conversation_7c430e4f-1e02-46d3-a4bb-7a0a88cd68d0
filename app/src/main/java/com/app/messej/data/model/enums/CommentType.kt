package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class CommentType {
    @SerializedName("postat") POSTAT,
    @SerializedName("flash") FLASH;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    companion object {
        fun default(): CommentType = POSTAT

        fun CommentType?.orDefault(): CommentType {
            return this ?: default()
        }

        fun fromString(value: String): CommentType {
            return when (value.lowercase()) {
                "postat" -> POSTAT
                "flash" -> FLASH
                else -> default()
            }
        }
    }
}
