package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.FlashTab
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FlashFeedDataSource(private val api: FlashAPIService, private val tab: <PERSON>Tab): PagingSource<FlashFeedDataSource.PagingParams, FlashVideo>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    data class PagingParams(
        val privateOffset: Int?,
        val publicOffset: Int?,
        val viewedOffset: Int?,
        val huddleOffset: Int?
    ) {
        val validOrNull: PagingParams?
            get() = if ((privateOffset?:-1) < 0 && (publicOffset?:-1) < 0 && (viewedOffset?:-1) < 0) null else this
    }

    override suspend fun load(params: LoadParams<PagingParams>): LoadResult<PagingParams, FlashVideo> {
        val parts = params.key
        return try {
            withContext(Dispatchers.IO) {
                val response = api.getFlashFeed(
                    tab = tab.serializedName(),
                    privateOffset = parts?.privateOffset,
                    publicOffset = parts?.publicOffset,
                    viewedOffset = parts?.viewedOffset,
                    huddleOffset = parts?.huddleOffset
                )
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = PagingParams(data.privateOffset, data.publicOffset, data.viewedOffset, data.huddleOffset)

                data.items.forEach { it.sanitize() }

                LoadResult.Page(
                    data = data.items, prevKey = null, nextKey = nextKey.validOrNull
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override val keyReuseSupported: Boolean
        get() = true

    override fun getRefreshKey(state: PagingState<PagingParams, FlashVideo>): PagingParams? {
        return null
    }
}