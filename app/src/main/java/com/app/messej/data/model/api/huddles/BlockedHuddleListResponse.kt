package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.BlockedHuddle
import com.google.gson.annotations.SerializedName

data class BlockedHuddleListResponse(@SerializedName("current_page") val currentPage: Int? = null,
                                     @SerializedName("huddles") val huddles: List<BlockedHuddle> = listOf(),
                                     @SerializedName("next_page") val nextPage: Int? = null,
                                     @SerializedName("total") val total: Int? = null)