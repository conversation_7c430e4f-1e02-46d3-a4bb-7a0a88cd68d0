package com.app.messej.data.model.api.legal

import com.google.gson.annotations.SerializedName

data class LegalAffairsPendingFineResponse(
    @SerializedName("fines") val reportingList: List<LegalAffairsFine>? = null,
    @SerializedName("next_page") val nextPage: Boolean? = null,
    @SerializedName("total_amount") val totalFineAmount: Double? = null,
) {
    data class LegalAffairsFine(
        @SerializedName("report_id") val reportId: Int? = null,
        @SerializedName("category") val category: String? = null,
        @SerializedName("reason") val reason: String? = null,
        @SerializedName("amount") val amount: Double? = null,
    )
}

