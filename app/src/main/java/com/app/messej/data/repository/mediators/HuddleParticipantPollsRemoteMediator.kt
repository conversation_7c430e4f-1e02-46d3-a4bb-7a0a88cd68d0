package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.PollsAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.PollParticipant
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class HuddleParticipantPollsRemoteMediator(
    private val pollId: Int,
    private val database: FlashatDatabase,
    private val networkService: PollsAPIService,
) : RemoteMediator<Int, PollParticipant>() {
    val dao = database.getPollsDao()

    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = "${EntityDescriptions.TABLE_POLLS_PARTICIPANT}-$pollId"
    init {

        Log.d("Data","test")
    }

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, PollParticipant>
    ): MediatorResult {

        return try {
            val page: String? = when (loadType) {
                LoadType.REFRESH -> null
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    Log.d("PHRM", "load: APPEND")
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPage
                }
            }
            Log.d("PHRM", "load: loading page $page")
            Log.d("ParticipantResponse","page is $page poll id $pollId")
            val response = networkService.getPollParticipants(page = page?.toInt()?:1, pollId = pollId, limit = 50)
            Log.d("ParticipantResponse",response.toString() )
            val result = if (response.isSuccessful && response.code() == 200) {
                Log.d("ParticipantResponse", response.body()?.result.toString() )
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                Log.d("ParticipantResponse",error.message)
                throw Exception(error.message)

            }

            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    dao.deleteAllPollParticipant(pollId)
                }
                // Update RemoteKey for this query.
                val nextPage = result.currentPage?.toInt()?.plus(1)
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, nextPage.toString())
                )
                result.pollParticipants.apply {
                  this.map {
                        it.pollID=pollId
                    }
                    dao.insertParticipant(this)
                }
            }

            MediatorResult.Success(endOfPaginationReached = result.nextPage == null)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}