package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.PollsAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.DateTimeUtils
import java.io.IOException
import java.time.ZoneId
import java.time.ZonedDateTime


@OptIn(ExperimentalPagingApi::class)
class PollsListingRemoteMediator(
    private val database: FlashatDatabase,
    private val networkService: PollsAPIService,
    private val  huddleId:Int,
    private val status: String
) : RemoteMediator<Int, Poll>() {
    private val remoteKeyDao = database.getRemotePagingDao()
    private val dao = database.getPollsDao()
    private val tableKey = "${EntityDescriptions.TABLE_POLLS_LIST}-main"

    override suspend fun load(loadType: LoadType, state: PagingState<Int, Poll>): MediatorResult {

        return try {
            val loadKey = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND -> return MediatorResult.Success(
                    endOfPaginationReached = true
                )

                LoadType.APPEND -> {

                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }

                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPageInt
                }
            }
            val response = networkService.getPollList(page = loadKey, limit = 10, huddleId = huddleId, status = status)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }
            var currentDate = ZonedDateTime.now(ZoneId.systemDefault())
            val date = DateTimeUtils.format(currentDate)
            database.withTransaction {

                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    when (status) {
                        "scheduled" -> dao.deleteSchedulePolls(huddleId,date)
                        "past_polls" -> dao.deletePastPolls(huddleId,date)
                        else -> dao.deleteAllPolls(huddleId)
                    }
                   // dao.deleteAllPolls(huddleId)  
                }
                // Update RemoteKey for this query.
                val nextPage = result.page.toInt().plus(1)
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, nextPage.toString())
                )
                result.polls.apply {
                    dao.insertPolls(this)
                }
            }
            MediatorResult.Success(endOfPaginationReached = result.nextPage ==false)
        } catch (e: IOException) {
            Log.i("POLL_RESPONSE", ": ${e.message}")
            MediatorResult.Error(e)
        }

    }
}
