package com.app.messej.data.room.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.app.messej.data.model.entity.PostReply
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class PostReplyDao {


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertPostReplies(replies: List<PostReply>): List<Long>

    @Update
    abstract suspend fun updatePostReply(reply: PostReply): Int



    @Query("UPDATE ${EntityDescriptions.TABLE_POSTAT_REPLIES} SET ${PostReply.COLUMN_TOTAL_LIKE_COUNT} = ${PostReply.COLUMN_TOTAL_LIKE_COUNT} + :increment WHERE ${PostReply.COLUMN_REPLY_ID} = :replyId AND ${PostReply.COLUMN_TYPE} = :type")
    abstract suspend fun updateReplyLikeCount(replyId: String, type: CommentType, increment: Int): Int



    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POSTAT_REPLIES} WHERE ${PostReply.COLUMN_PARENT_COMMENT_ID} = :parentCommentId AND ${PostReply.COLUMN_TYPE} = :type ORDER BY ${PostReply.COLUMN_CREATED} ASC LIMIT :pageSize OFFSET :offset")
    abstract suspend fun getPostReplies(parentCommentId: String, type: CommentType, pageSize: Int, offset: Int): List<PostReply>


    @Query("DELETE FROM ${EntityDescriptions.TABLE_POSTAT_REPLIES} WHERE ${PostReply.COLUMN_PARENT_COMMENT_ID} = :parentCommentId AND ${PostReply.COLUMN_TYPE} = :type")
    abstract suspend fun deletePostReplies(parentCommentId: String, type: CommentType): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_POSTAT_REPLIES} WHERE ${PostReply.COLUMN_REPLY_ID} = :replyCommentId AND ${PostReply.COLUMN_TYPE} = :type")
    abstract suspend fun deleteSingleCommentReply(replyCommentId: String,type: CommentType): Int
}
