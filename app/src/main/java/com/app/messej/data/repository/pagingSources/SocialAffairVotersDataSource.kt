package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.SocialAffairAPIService
import com.app.messej.data.model.api.socialAffairs.VotersResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SocialAffairVotersDataSource(private val apiService: SocialAffairAPIService, private val caseId: Int) : PagingSource<Int, VotersResponse.Voter>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, VotersResponse.Voter>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, VotersResponse.Voter> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = apiService.getVotersList(id = caseId, page = currentPage)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (data.haveNextPage == true) currentPage + 1  else null
                LoadResult.Page(
                    data = data.honoursList ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}