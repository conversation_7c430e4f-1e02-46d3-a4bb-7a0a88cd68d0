package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.model.Star
import com.app.messej.data.model.enums.SearchType
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1
private var userType: UserType = UserType.PREMIUM
private var searchType: SearchType = SearchType.EXACT_MATCH

class StarsSearchSuggestionDataSource(private val api: ProfileAPIService, private val accountRepo: AccountRepository, private val searchKeyWord: String?): PagingSource<Int, Star>() {

    override suspend fun load(params: LoadParams<Int>): LoadR<PERSON>ult<Int, Star> {
        return try {
                withContext(Dispatchers.IO) {
                    val currentPage = params.key ?: STARTING_KEY
                    val response = api.getStarsSearchSuggestion(
                        userType,
                        currentPage,
                        searchKeyWord!!,
                        searchType
                    )
                    val result = response.body()?.result ?: throw Exception("Result is null")
                    val responseData = mutableListOf<Star>()
                    val data = result.users
                    responseData.addAll(data)
//                    val nextKey = if (result.users.isEmpty()) null else currentPage.plus(1)
                    val nextKey = if (result.nextPage == true) currentPage.plus(1)
                    else if (searchType != result.searchType && result.searchType != null){
                        searchType = result.searchType!!
                        1
                    } else if (userType == UserType.PREMIUM){
                        userType = UserType.FREE
                        searchType = SearchType.EXACT_MATCH
                        1
                    } else{
                        userType = UserType.PREMIUM
                        searchType = SearchType.EXACT_MATCH
                        null
                    }

                    LoadResult.Page(
                        data = response.body()?.result!!.users,
                        prevKey = if (currentPage == 1) null else currentPage.minus(1),
                        nextKey = nextKey
                    )
                }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override val keyReuseSupported: Boolean = true

    override fun getRefreshKey(state: PagingState<Int, Star>): Int? {
        val anchorPosition = state.anchorPosition ?: return null
        val article = state.closestItemToPosition(anchorPosition) ?: return null
        return ensureValidKey(key = article.id - (state.config.pageSize / 2) )
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}