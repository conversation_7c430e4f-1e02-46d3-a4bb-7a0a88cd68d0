package com.app.messej.data.model.notification

import com.google.gson.annotations.SerializedName

data class FlashCommentNotification(
        @SerializedName("video_url"         ) val videoUrl          : String,
        @SerializedName("receiver_id"       ) val receiverId        : Int,
        @SerializedName("message"           ) val message           : String,
        @SerializedName("html_text"         ) val messageHtml       : String,
        @SerializedName("notification_id"   ) val notificationId    : Int = 0,
        @SerializedName("sender_id"         ) val senderId          : Int,
        @SerializedName("flash_id"          ) val flashId           : String,
        @SerializedName("comment_id"        ) val commentId              : String?=null
)