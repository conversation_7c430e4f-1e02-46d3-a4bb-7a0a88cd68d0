package com.app.messej.data.model.api

import android.util.Log
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import okhttp3.ResponseBody

data class PodiumJoinErrorResponse (
    @SerializedName("message") val message: String,
    @SerializedName("result") val result: PodiumJoinError?
) {
    companion object {
        fun parseError(response: ResponseBody?): PodiumJoinErrorResponse {
            response?: return PodiumJoinErrorResponse("",null)
            return try {
                val type = object : TypeToken<PodiumJoinErrorResponse>() {}.type
                val str = response.string()
                Log.d("PLVM", "Parsing: $str")
                Gson().fromJson(str, type)
            } catch (e: Exception) {
                Log.e("PLVM", "Error parsing error", e)
                PodiumJoinErrorResponse("",null)
            }
        }
    }

    enum class PodiumJoinErrorReason {
        @SerializedName("LIVE_IN_OTHER_PODIUM") LIVE_IN_OTHER_PODIUM,
        @SerializedName("INSUFFICIENT_BALANCE") INSUFFICIENT_BALANCE,
        @SerializedName("COMPETITOR_ALREADY_JOINED") COMPETITOR_ALREADY_JOINED,
        OTHER
    }

    data class PodiumJoinError(
        @SerializedName("podium_id") val podiumId: String?,
        @SerializedName("podium_name") val podiumName: String?,
        @SerializedName("reason") val reason: PodiumJoinErrorReason?,
        @SerializedName("allowed_to_leave_live_podium") val canLeave: Boolean?
    )
}