package com.app.messej.data.model.api.podium.challenges

import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime
import kotlin.math.max

data class PodiumConFourInviteResponse(
    @SerializedName("time_invited_participants") val invitedParticipantsTime: String,
) {
    private val parsedInvitedParticipantsTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(invitedParticipantsTime)

    val conFourParticipantRequestedTimeRemaining: Long
        get() {
            val dur = (DateTimeUtils.durationToNowFromPast(parsedInvitedParticipantsTime))?.seconds?: 30L
            return max(0,30-dur)
        }
}