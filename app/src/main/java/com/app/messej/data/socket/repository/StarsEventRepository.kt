package com.app.messej.data.socket.repository

import com.app.messej.data.model.entity.UserStar
import com.app.messej.data.model.socket.SuperStarHideEvent
import com.app.messej.data.model.socket.SuperStarHidePayload
import com.app.messej.data.model.socket.SuperStarMutePayload
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object StarsEventRepository: BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {
    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when(event){
            ChatSocketEvent.RX_TX_MUTE_UNMUTE_SUPERSTAR -> onSuperstarMute(data)
            ChatSocketEvent.RX_TX_HIDE_UNHIDE_SUPERSTAR -> onSuperstarHide(data)
            else -> return false
        }
        return true
    }

    private fun onSuperstarHide(data: JSONObject)  = runBlocking{
        val evt = Gson().fromJson<SuperStarHideEvent>(data.toString())
        var star = db.getUserDao().getUserStar(evt.broadcasterId)
        star?: return@runBlocking
        star = star.copy(
            hidden = evt.hide
        )
        star.isSuperStar = true
        withContext(Dispatchers.IO) {
            db.getUserDao().insert(star)
        }
    }

    private fun onSuperstarMute(data: JSONObject) = runBlocking{
        val evt = Gson().fromJson<SuperStarHideEvent>(data.toString())
        var star = db.getUserDao().getUserStar(evt.broadcasterId)
        star?: return@runBlocking
        star = star.copy(
            muted = evt.muted
        )
        star.isSuperStar = true
        withContext(Dispatchers.IO) {
            db.getUserDao().insert(star)
        }
    }

    fun muteUnmuteSuperstar(user: UserStar): Boolean {
        return ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_MUTE_UNMUTE_SUPERSTAR, SuperStarMutePayload(listOf(user.id)))
    }

    fun showHideSuperstar(user: UserStar): Boolean {
        return ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_HIDE_UNHIDE_SUPERSTAR, SuperStarHidePayload(user.id))
    }

}