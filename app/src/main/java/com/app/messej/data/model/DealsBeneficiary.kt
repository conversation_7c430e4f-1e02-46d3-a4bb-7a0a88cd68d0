package com.app.messej.data.model


import com.google.gson.annotations.SerializedName

data class DealsBeneficiary(
    @SerializedName("citizenship")
    val citizenship: String,
    @SerializedName("country")
    val country: String,
    @SerializedName("created_on")
    val createdOn: String,
    @SerializedName("disable_broadcast_notification")
    val disableBroadcastNotification: <PERSON><PERSON><PERSON>,
    @SerializedName("email")
    val email: String,
    @SerializedName("id")
    val id: Int,
    @SerializedName("inactive_checkpoint")
    val inactiveCheckpoint: Int,
    @SerializedName("ip")
    val ip: String,
    @SerializedName("is_leader")
    val isLeader: <PERSON><PERSON><PERSON>,
    @SerializedName("membership")
    val membership: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("payment_date")
    val paymentDate: String,
    @SerializedName("phone")
    val phone: String,
    @SerializedName("pp")
    val pp: Double,
    @SerializedName("profile_image")
    val profileImage: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("superstar")
    val superstar: String,
    @SerializedName("username")
    val username: String,
    @SerializedName("verified")
    val verified: Boolean
)