package com.app.messej.data.model.socket

import com.app.messej.data.model.AbstractHuddle
import com.google.gson.annotations.SerializedName

data class HuddleUpdateEvent(
    @SerializedName("id"           ) val id          : Int,
    @SerializedName("user_status"  ) val userStatus  : AbstractHuddle.HuddleUserStatus,
    @SerializedName("admin_status" ) val adminStatus : AbstractHuddle.HuddleAdminStatus?  = null,
    @SerializedName("private"      ) val private     : Boolean? = null,
    @SerializedName("action"       ) val action     : AbstractHuddle.HuddleAction? = null
)
