package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.api.podium.PodiumFriend


private const val STARTING_KEY = 1
class PodiumLiveFriendsDataSource(private val api: PodiumAPIService,private val countCallback: (Int) -> Unit = {}): PagingSource<Int, PodiumFriend>() {


    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PodiumFriend> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getPodiumLiveFriends(page = currentPage, limit = 50)
            val responseData = mutableListOf<PodiumFriend>()
            val result = response.body()?.result
            val data = result?.data ?: emptyList()
            result?.liveFriendsCount?.let { countCallback.invoke(it) }

            responseData.addAll(data)
            val nextKey = if (!(response.body()?.result!!.hasNext == true)) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.data, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("PodiumLiveFriendsResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, PodiumFriend>): Int? {
        return null
    }


}