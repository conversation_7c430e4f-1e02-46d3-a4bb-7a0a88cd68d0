package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractComment
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.PostReply.Companion.COLUMN_COMMENT_ID
import com.app.messej.data.model.entity.PostReply.Companion.COLUMN_PARENT_COMMENT_ID
import com.app.messej.data.model.entity.PostReply.Companion.COLUMN_POSTAT_ID
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = EntityDescriptions.TABLE_POSTAT_REPLIES,
    indices = [
        Index(COLUMN_COMMENT_ID, unique = false),
        Index(COLUMN_PARENT_COMMENT_ID, unique = false),
        Index(COLUMN_POSTAT_ID, unique = false)
    ]
)
@TypeConverters(
    SenderDetails.Converter::class,
    MentionedUser.SingleConverter::class
)
data class PostReply(
    @SerializedName("id",alternate = ["content_id"])
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = COLUMN_REPLY_ID)
    override val commentId: String,

    @SerializedName("comment_id")
    @ColumnInfo(name = COLUMN_COMMENT_ID)
    override val messageId: String,

    @SerializedName("time_created", alternate = ["created"])
    @ColumnInfo(name = COLUMN_CREATED)
    override val created: String? = null,

    @SerializedName("message")
    @ColumnInfo(name = COLUMN_MESSAGE)
    override val comment: String? = null,

    @SerializedName("sender")
    @ColumnInfo(name = COLUMN_SENDER_ID)
    override val senderId: Int,

    @SerializedName("sender_details")
    @ColumnInfo(name = COLUMN_SENDER_DETAILS)
    override val senderDetails: SenderDetails? = null,

    @SerializedName("is_delete")
    @ColumnInfo(name = COLUMN_IS_DELETE)
    val isDelete: Boolean = false,

    @SerializedName("mentioned_user_details")
    @ColumnInfo(name = COLUMN_MENTIONED_USER_DETAILS)
    val mentionedUserDetails: SenderDetails? = null,

    @SerializedName("mentioned_users")
    @ColumnInfo(name = COLUMN_MENTIONED_USER)
    val mentionedUser: MentionedUser? = null,

    @SerializedName("mentioned_user_id")
    @ColumnInfo(name = COLUMN_MENTIONED_USER_ID)
    val mentionedUserId: Int? = null,

    @SerializedName("time_updated")
    @ColumnInfo(name = COLUMN_TIME_UPDATED)
    val timeUpdated: String? = null,

    @SerializedName("total_like_count")
    @ColumnInfo(name = COLUMN_TOTAL_LIKE_COUNT)
    val totalLikeCount: Int = 0,

    @SerializedName("user_id")
    @ColumnInfo(name = COLUMN_USER_ID)
    val userId: Int? = null,

    // Local-only fields for database relationships
    @ColumnInfo(name = COLUMN_PARENT_COMMENT_ID)
    val parentCommentId: String? = null,

    @ColumnInfo(name = COLUMN_POSTAT_ID)
    val basePostId: String = "",

    // Override required fields from AbstractComment that are not in the API response
    @ColumnInfo(name = COLUMN_HUDDLE_ID)
    override val huddleId: Int = 0,

    @ColumnInfo(name = COLUMN_MEDIA)
    override val media: String? = null,

    @SerializedName("type")
    @ColumnInfo(name = COLUMN_TYPE, defaultValue = "POSTAT")
    val type: CommentType = CommentType.POSTAT

) : AbstractComment() {

    companion object {
        const val COLUMN_REPLY_ID = "replyId"
        const val COLUMN_COMMENT_ID = "commentId"
        const val COLUMN_MESSAGE = "message"
        const val COLUMN_CREATED = "created"
        const val COLUMN_SENDER_ID = "senderId"
        const val COLUMN_SENDER_DETAILS = "senderDetails"
        const val COLUMN_IS_DELETE = "isDelete"
        const val COLUMN_MENTIONED_USER_DETAILS = "mentionedUserDetails"
        const val COLUMN_MENTIONED_USER = "mentionedUser"
        const val COLUMN_MENTIONED_USER_ID = "mentionedUserId"
        const val COLUMN_TIME_UPDATED = "timeUpdated"
        const val COLUMN_TOTAL_LIKE_COUNT = "totalLikeCount"
        const val COLUMN_USER_ID = "userId"
        const val COLUMN_PARENT_COMMENT_ID = "parentCommentId"
        const val COLUMN_POSTAT_ID = "postatId"
        const val COLUMN_HUDDLE_ID = "huddleId"
        const val COLUMN_MEDIA = "media"
        const val COLUMN_TYPE = "type"
    }

    fun sanitize() {
        senderDetails?.id = senderId
        // Note: MentionedUser.id is immutable (val), so we can't modify it like SenderDetails
        // The mentionedUserId should already match mentionedUserDetails?.id from the API response
    }
}
