package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.PublicHuddle
import com.google.gson.annotations.SerializedName

data class HuddleListResponse(
    @SerializedName("current_page"      ) val currentPage       : Int?               = null,
    @SerializedName("huddles"           ) val huddles           : List<PublicHuddle> = listOf(),
    @SerializedName("next_page"         ) val nextPage          : Int?               = null,
    @SerializedName("total"             ) val total             : Int?               = null,
    @SerializedName("deleted_huddles_id") val deletedHuddleIds  : List<Int>          = listOf(),
    @SerializedName("max_allowed"       ) val max_allowed       : Int?               = null
)