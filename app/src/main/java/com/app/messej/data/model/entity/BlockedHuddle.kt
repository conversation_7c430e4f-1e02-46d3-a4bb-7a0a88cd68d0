package com.app.messej.data.model.entity


import com.google.gson.annotations.SerializedName

data class BlockedHuddle(
    @SerializedName("about") val about: String? = null,
    @SerializedName("group_photo") val thumbnail: String? = null,
    @SerializedName("id") val id: Int,
    @SerializedName("name") val name: String,
    @SerializedName("participant_count") val participantCount: Int? = null,
)

