package com.app.messej.data.model.status


import com.google.gson.annotations.SerializedName

data class ProfileCompleteness(
    @SerializedName("current_percentage") val currentPercentage: Int? = 0,
    @SerializedName("is_satisfied") val isSatisfied: Boolean? = false,
    @SerializedName("mobile_verified") val mobileVerified: Boolean? = false,
    @SerializedName("required_percentage") val requiredPercentage: Int? = 0,
)