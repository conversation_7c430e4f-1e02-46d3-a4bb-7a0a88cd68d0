package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.policy.PolicyResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Query

interface DocumentAPIService {
    @GET("/user/legal/documents")
    @Headers("Accept: application/json")
    suspend fun getLegalDocument(@Query("policy_type") documentType: String): Response<APIResponse<PolicyResponse>>
}
