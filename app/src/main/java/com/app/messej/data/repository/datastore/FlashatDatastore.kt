package com.app.messej.data.repository.datastore

import android.content.Context
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.core.handlers.ReplaceFileCorruptionHandler
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.android.billingclient.api.ProductDetails
import com.app.messej.MainApplication
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.OAuthTokens
import com.app.messej.data.model.api.AppLocalSettings
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.api.auth.UnreadItemsResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.api.settings.PrivacySettingsResponse
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.time.LocalDateTime

class FlashatDatastore {

    companion object {
        private const val DATASTORE_ACCOUNT: String = "messejDataStore_account"
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(
            name = DATASTORE_ACCOUNT,
            corruptionHandler = ReplaceFileCorruptionHandler {
                emptyPreferences()
            }
        )

        private val KEY_TOKENS = stringPreferencesKey("dpm_access_token")
        private val KEY_PROFILE = stringPreferencesKey("dpm_user_profile")
        private val KEY_BIOMETRIC_ENABLED = stringPreferencesKey("fls_biometric_enabled")
        private val KEY_COOKIE_SHOWED_ONBOARDING = stringPreferencesKey("fls_shown_onboarding")
        private val KEY_COOKIE_SHOWED_BUSINESS_START = stringPreferencesKey("fls_shown_business_start")
        private val KEY_NOTIFICATION_COUNT = stringPreferencesKey("fls_notification_count")
        private val KEY_INCOMPLETE_PROFILE_DIALOG_SHOULD_SHOW = stringPreferencesKey("didShowProfileCompletionDialog")
        private val KEY_INCOMPLETE_PROFILE_DIALOG_LAST_SHOWN = stringPreferencesKey("lastShowProfileCompletionDialog")
        private val KEY_UPGRADE_BANNER_LAST_DISMISSED = stringPreferencesKey("lastDismissOfUpgradeBanner")
        private val KEY_ACCOUNT_VERIFIED_STATUS = stringPreferencesKey("accountVerifiedStatus")
        private val KEY_SETTINGS_DATASTORE = stringPreferencesKey("settings_datastore")
        private val KEY_ACCOUNT_DETAILS = stringPreferencesKey("account_details")
        private val KEY_PRIVACY_SETTINGS = stringPreferencesKey("privacy_settings")
        private val KEY_UNREAD_COUNTS = stringPreferencesKey("unread_counts")
        private val KEY_POLL_HIDE = stringPreferencesKey("poll_hide")
        private val KEY_FLASH_COOKIE = stringPreferencesKey("flash_cookie")
        private val KEY_POSTAT_COOKIE = stringPreferencesKey("postat_cookie")
        private val KEY_BIRTHDAY_DATE = stringPreferencesKey("birthday_date")
        private val KEY_CITIZENSHIP_PRIORITY = intPreferencesKey("citizenship_priority")
        private val KEY_LOCATION_SYNC_DATE = stringPreferencesKey("location_sync_date")
        private val KEY_ONE_TIME_OFFER_DETAILS = stringPreferencesKey("one_time_offer_details")
        private val KEY_IN_APP_PURCHASE_TYPE = stringPreferencesKey("in_app_purchase_type")
        private val KEY_CURRENT_PRESIDENT_ID = stringPreferencesKey("president_id")
        private val KEY_PAID_LIKE_SKIP_CONFIRM = stringPreferencesKey("flash_paid_like_action_checkbox")
        private val KEY_POSTAT_PAID_LIKE_ACTION_CHECKBOX = stringPreferencesKey("postat_paid_like_action_checkbox")

        private val KEY_FLASH_POST_PAID_LIKE_ACTION_CHECKBOX = stringPreferencesKey("flash_post_paid_like_action_checkbox")
        private val KEY_PODIUM_SWIPE_ACTION_CHECK_BOX = booleanPreferencesKey(name = "podium_swipe_action_checkbox")


        private val _onTokenUpdated = MutableSharedFlow<OAuthTokens>(replay = 0)

    }

    val onTokenUpdated: SharedFlow<OAuthTokens> = _onTokenUpdated
    private val mContext = MainApplication.applicationContext()
    private val gson = Gson()

    suspend fun updateUser(user: CurrentUser) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_PROFILE] = Gson().toJson(user)
        }
    }

    suspend fun updateTokens(tokens: OAuthTokens) = withContext(Dispatchers.IO)  {
        mContext.dataStore.edit {
            it[KEY_TOKENS] = Gson().toJson(tokens)
        }
        _onTokenUpdated.emit(tokens)
    }

    suspend fun saveUserAndTokens(user: CurrentUser, tokens: OAuthTokens) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_PROFILE] = Gson().toJson(user)
            it[KEY_TOKENS] = Gson().toJson(tokens)
        }
    }

    suspend fun clearData() {
        mContext.dataStore.edit {
            it.clear()
        }
    }

    suspend fun userAccount(): CurrentUser? = withContext(Dispatchers.IO) {
        val json = mContext.dataStore.data.first()[KEY_PROFILE]
        if(json.isNullOrEmpty()) {
            return@withContext null
        }
        return@withContext Gson().fromJson(json, CurrentUser::class.java)
    }

    fun userAccountFLow(): Flow<CurrentUser?> {
        return mContext.dataStore.data.map {
            val json = it[KEY_PROFILE]
            if(json.isNullOrEmpty()) {
                return@map null
            }
            return@map Gson().fromJson(json, CurrentUser::class.java)
        }
    }

    suspend fun tokens(): OAuthTokens? = withContext(Dispatchers.IO)  {
        try {

            val json = mContext.dataStore.data.first()[KEY_TOKENS]
            if (json.isNullOrEmpty()) {
                return@withContext null
            }
            return@withContext Gson().fromJson(json, OAuthTokens::class.java)
        } catch (e: Exception) {
            clearData()
            return@withContext null
        }
    }


    fun onNewTokenFlow(): Flow<OAuthTokens?> {
        return mContext.dataStore.data.map {
            val json = it[KEY_TOKENS]
            if(json.isNullOrEmpty()) {
                return@map null
            }
            return@map Gson().fromJson(json, OAuthTokens::class.java)
        }
    }

    suspend fun setOnboardingShown(shown: Boolean) {
        mContext.dataStore.edit {
            it[KEY_COOKIE_SHOWED_ONBOARDING] = shown.toString()
        }
    }

    suspend fun setOnPollHide(hide: Boolean) {
        mContext.dataStore.edit {
            it[KEY_POLL_HIDE] = hide.toString()
        }
    }

    suspend fun onPollHideShown(): Boolean {
        val data = mContext.dataStore.data.first()[KEY_POLL_HIDE]
        return data.toBoolean()
    }

    suspend fun onBoardingShown(): Boolean {
        val data = mContext.dataStore.data.first()[KEY_COOKIE_SHOWED_ONBOARDING]
        return data.toBoolean()
    }

    suspend fun setBusinessStartShown(isShown: Boolean) {
        mContext.dataStore.edit {
            it[KEY_COOKIE_SHOWED_BUSINESS_START] = isShown.toString()
        }
    }

    suspend fun isBusinessStartShown(): Boolean {
        val data = mContext.dataStore.data.first()[KEY_COOKIE_SHOWED_BUSINESS_START]
        return data.toBoolean()
    }


    suspend fun setNotificationCount(count:Int) = withContext(Dispatchers.IO)  {
        mContext.dataStore.edit {
            it[KEY_NOTIFICATION_COUNT] = count.toString()
        }
    }

    fun getNotificationCountFlow(): Flow<Int?> {
        return mContext.dataStore.data.map { it[KEY_NOTIFICATION_COUNT]?.toInt() }
    }

    suspend fun setDontShowProfileCompletionDialog() {
        mContext.dataStore.edit {
            it[KEY_INCOMPLETE_PROFILE_DIALOG_SHOULD_SHOW] = true.toString()
        }
    }

    fun getCanShowProfileCompletionDialog(): Flow<Boolean> {
        return mContext.dataStore.data.map { it[KEY_INCOMPLETE_PROFILE_DIALOG_SHOULD_SHOW]?.toBoolean()!=true }
    }

    suspend fun setProfileCompletionDialogShown() {
        mContext.dataStore.edit {
            it[KEY_INCOMPLETE_PROFILE_DIALOG_LAST_SHOWN] = LocalDateTime.now().toString()
        }
    }

    fun getProfileCompletionDialogLastShown(): Flow<LocalDateTime?> {
        return mContext.dataStore.data.map {
            val time = it[KEY_INCOMPLETE_PROFILE_DIALOG_LAST_SHOWN]
            time?: return@map null
            LocalDateTime.parse(time)
        }
    }

    suspend fun setDismissedUpgradeBanner() {
        mContext.dataStore.edit {
            it[KEY_UPGRADE_BANNER_LAST_DISMISSED] = LocalDateTime.now().toString()
        }
    }

    fun getLastDismissedUpgradeBanner(): Flow<LocalDateTime?> {
        return mContext.dataStore.data.map {
            val time = it[KEY_UPGRADE_BANNER_LAST_DISMISSED]
            time?: return@map null
            LocalDateTime.parse(time)
        }
    }

    suspend fun setAccountVerifiedStatus(status:String) {
        mContext.dataStore.edit {
            it[KEY_ACCOUNT_VERIFIED_STATUS] = status
        }
    }

    fun getAccountVerifiedStatus(): Flow<String?> {
        return mContext.dataStore.data.map { it[KEY_ACCOUNT_VERIFIED_STATUS] }
    }

    suspend fun saveSettings(settings: AppLocalSettings) {
        val json = gson.toJson(settings)
        mContext.dataStore.edit { preferences ->
            preferences[KEY_SETTINGS_DATASTORE] = json
        }
    }

    fun settingsFlow(): Flow<AppLocalSettings> {
        return mContext.dataStore.data.map {
            val json = it[KEY_SETTINGS_DATASTORE]
            if(json.isNullOrEmpty()) {
                return@map AppLocalSettings()
            }
            return@map gson.fromJson(json, AppLocalSettings::class.java)
        }
    }

    suspend fun saveAccountDetails(accountDetails: AccountDetailsResponse) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_ACCOUNT_DETAILS] = Gson().toJson(accountDetails)
        }
    }

    fun getAccountDetailsAsFlow() : Flow<AccountDetailsResponse?> {
        return mContext.dataStore.data.map {
            val json = it[KEY_ACCOUNT_DETAILS]
            if(json.isNullOrEmpty()) {
                return@map null
            }
            return@map gson.fromJson(json, AccountDetailsResponse::class.java)
        }
    }

    suspend fun savePrivacySettings(privacySettings: PrivacySettingsResponse) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_PRIVACY_SETTINGS] = Gson().toJson(privacySettings)
        }
    }

    fun getPrivacySettingsFlow(): Flow<PrivacySettingsResponse?> {
        return mContext.dataStore.data.map {
            val json = it[KEY_PRIVACY_SETTINGS]
            Log.d("LIMIT", "getPrivacySettingsFlow: stored limit json - $json")
            if(json.isNullOrEmpty()) {
                return@map null
            }
            val parsed = Gson().fromJson(json, PrivacySettingsResponse::class.java)
            Log.d("LIMIT", "getPrivacySettingsFlow: stored limit - ${parsed.huddleVoiceMessageLengthInSeconds}")
            return@map parsed
        }
    }

    suspend fun saveUnreadCounts(counts: UnreadItemsResponse) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_UNREAD_COUNTS] = Gson().toJson(counts)
        }
    }

    fun getUnreadCountsFlow(): Flow<UnreadItemsResponse?> {
        return mContext.dataStore.data.map {
            val json = it[KEY_UNREAD_COUNTS]
            if (json.isNullOrEmpty()) {
                return@map null
            }
            return@map Gson().fromJson(json, UnreadItemsResponse::class.java)
        }
    }

    suspend fun updateUnreadCounts(updateBlock: (UnreadItemsResponse) -> UnreadItemsResponse) {
        withContext(Dispatchers.IO) {
            val preferences = mContext.dataStore.data.first()
            val currentJson = preferences[KEY_UNREAD_COUNTS]
            val currentCounts = if (!currentJson.isNullOrEmpty()) {
                Gson().fromJson(currentJson, UnreadItemsResponse::class.java)
            } else {
                UnreadItemsResponse()
            }
            val updatedCounts = updateBlock(currentCounts)
            mContext.dataStore.edit { prefs ->
                prefs[KEY_UNREAD_COUNTS] = Gson().toJson(updatedCounts)
            }
        }
    }

    suspend fun saveFlashCookie(cookie: VideoPlaybackCookie) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_FLASH_COOKIE] = Gson().toJson(cookie)
        }
    }

    fun getFlashCookieFlow(): Flow<VideoPlaybackCookie?> {
        return mContext.dataStore.data.map {
            val json = it[KEY_FLASH_COOKIE]
            if (json.isNullOrEmpty()) {
                return@map null
            }
            return@map Gson().fromJson<VideoPlaybackCookie?>(json, VideoPlaybackCookie::class.java)
        }
    }

    suspend fun saveBirthdayDate(date: String) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_BIRTHDAY_DATE] = date
        }
    }
    suspend fun savePostatCookie(cookie: VideoPlaybackCookie) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_POSTAT_COOKIE] = Gson().toJson(cookie)
        }
    }

    fun getLastBirthdayDate(): Flow<String?> {
        return mContext.dataStore.data.map {
            it[KEY_BIRTHDAY_DATE]
        }
    }

    fun getPostatCookieFlow(): Flow<VideoPlaybackCookie?> {
        return mContext.dataStore.data.map {
            val json = it[KEY_POSTAT_COOKIE]
            if (json.isNullOrEmpty()) {
                return@map null
            }
            return@map Gson().fromJson<VideoPlaybackCookie?>(json, VideoPlaybackCookie::class.java)
        }
    }


    suspend fun saveCitizenshipPriority(priority: Int) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_CITIZENSHIP_PRIORITY] = priority
        }
    }

    fun getCitizenshipPriorityFlow(): Flow<Int?> {
        return mContext.dataStore.data.map {
            it[KEY_CITIZENSHIP_PRIORITY]
        }
    }

    suspend fun syncLocationDate(date: String) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_LOCATION_SYNC_DATE] = date
        }
    }

    fun getLastLocationDate(): Flow<String?> {
        return mContext.dataStore.data.map {
            it[KEY_LOCATION_SYNC_DATE]
        }
    }
    suspend fun saveOneTimeOfferDetails(cookie: ProductDetails.OneTimePurchaseOfferDetails) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_ONE_TIME_OFFER_DETAILS] = Gson().toJson(cookie)
        }
    }
    fun getOneTimeOfferDetailsFlow(): Flow<ProductDetails.OneTimePurchaseOfferDetails?> {
        return mContext.dataStore.data.map {
            val json = it[KEY_ONE_TIME_OFFER_DETAILS]
            if (json.isNullOrEmpty()) {
                return@map null
            }
            return@map Gson().fromJson<ProductDetails.OneTimePurchaseOfferDetails?>(json, ProductDetails.OneTimePurchaseOfferDetails::class.java)
        }
    }
    suspend fun clearOnetimeOfferKey() {
        mContext.dataStore.edit { preferences ->
            preferences.remove(KEY_ONE_TIME_OFFER_DETAILS) // Removes the specific key
        }
    }
    suspend fun saveCurrentPresidentId(presidentId: String) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_CURRENT_PRESIDENT_ID] = presidentId
        }
    }
    fun getCurrentPresidentId(): Flow<String?> {
        return mContext.dataStore.data.map {
            it[KEY_CURRENT_PRESIDENT_ID]
        }
    }

    suspend fun setPaidLikeSkipConfirm(action: Boolean) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_PAID_LIKE_SKIP_CONFIRM] = action.toString()
        }
    }
    suspend fun getPaidLikeSkipConfirm(): Boolean {
        val data = mContext.dataStore.data.first()[KEY_PAID_LIKE_SKIP_CONFIRM]
        return data.toBoolean()
    }

    suspend fun getFlashPostPaidLikeCheckBoxAction(): Boolean {
        val data = mContext.dataStore.data.first()[KEY_FLASH_POST_PAID_LIKE_ACTION_CHECKBOX]
        return data.toBoolean()
    }

    suspend fun saveFlashPostPaidLikeCheckBoxAction(action: Boolean) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_FLASH_POST_PAID_LIKE_ACTION_CHECKBOX] = action.toString()
        }
    }


    suspend fun savePostatPaidLikeCheckBoxAction(action: Boolean) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_POSTAT_PAID_LIKE_ACTION_CHECKBOX] = action.toString()
        }
    }
    suspend fun getPostatPaidLikeCheckBoxAction(): Boolean {
        val data = mContext.dataStore.data.first()[KEY_POSTAT_PAID_LIKE_ACTION_CHECKBOX]
        return data.toBoolean()
    }

    suspend fun savePodiumSwipeCheckBoxAction(isHidden: Boolean) = withContext(Dispatchers.IO) {
        mContext.dataStore.edit {
            it[KEY_PODIUM_SWIPE_ACTION_CHECK_BOX] = isHidden
        }
    }

    fun getPodiumSwipeCheckBoxAction() : Flow<Boolean> {
        return mContext.dataStore.data.map {
            it[KEY_PODIUM_SWIPE_ACTION_CHECK_BOX] ?: false
        }
    }
}