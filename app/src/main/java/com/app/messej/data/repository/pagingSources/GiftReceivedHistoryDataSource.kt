package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.GiftAPIService
import com.app.messej.data.model.api.gift.GiftItem

private const val STARTING_KEY = 1
class GiftReceivedHistoryDataSource(private val api: GiftAPIService,private val giftType: String) : PagingSource<Int, GiftItem>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, GiftItem> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getGiftReceivedHistory(page = currentPage, limit = 50,giftType)
            val responseData = mutableListOf<GiftItem>()
            val data = response.body()?.result?.giftDetails ?: emptyList()

            data.forEach {
                responseData.addAll(it.data.orEmpty())
            }
            val nextKey = if (!response.body()?.result!!.nextPage) null else currentPage.plus(1)

            LoadResult.Page(
                data = responseData, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("TransactionResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, GiftItem>): Int? {
        return null
    }
}