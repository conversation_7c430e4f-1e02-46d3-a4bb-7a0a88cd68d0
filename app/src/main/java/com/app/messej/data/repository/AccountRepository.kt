package com.app.messej.data.repository

import android.content.Context
import android.util.Log
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.external.ExternalServiceGenerator
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.OAuthTokens
import com.app.messej.data.model.api.IpApiResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.api.profile.CurrentUserAndAccountDetails.Companion.copyCommonFieldsFrom
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

/**
 * Repository that takes care of session and tokens
 */
class AccountRepository(c: Context) {

    private val datastore = FlashatDatastore()

    init {
        restoreState()
    }

    private data class UserSession(
        var user: CurrentUser,
        var tokens: OAuthTokens
    )

    companion object {

        @Volatile
        private var didRestore = false

        @Volatile
        private var session: UserSession? = null

    }

    val loggedIn: Boolean
        get() {
            return session != null
        }

    val user: CurrentUser
        get() = session?.user?: throw Exception("Trying to access user when not logged in")

    val tokens: OAuthTokens?
        get() = session?.tokens

    val accessToken: String
        get() {
            return session?.tokens?.accessToken ?: throw Exception("Trying to get access token when not logged in")
        }

    val isPremiumUser: Boolean?
        get() = session?.user?.profile?.premium

    val isResident:Boolean?
        get()= session?.user?.citizenship?.equals(UserCitizenship.RESIDENT)

    val isVisitor: Boolean?
        get()= session?.user?.citizenship?.equals(UserCitizenship.VISITOR)

    val isPresident: Boolean?
        get()= session?.user?.citizenship?.equals(UserCitizenship.PRESIDENT)


    val tokenUpdatedFLow: SharedFlow<OAuthTokens?> = datastore.onTokenUpdated
    val loggedInFlow: Flow<Boolean> = datastore.onNewTokenFlow().map { it!=null }.distinctUntilChanged()
    val userFlow: Flow<CurrentUser?> = datastore.userAccountFLow()
    val userProfileFlow: Flow<CurrentUser.Profile?> = userFlow.map { it?.profile }

    suspend fun setAccount(user: CurrentUser, accessToken: String, refreshToken: String, persist: Boolean = true) = withContext(Dispatchers.Main) {
        val tokens = OAuthTokens(accessToken, refreshToken)
        session = UserSession(user,tokens)
        if (persist) {
            datastore.saveUserAndTokens(user, tokens)
        }
        APIServiceGenerator.invalidateAuthApiService()
    }

    suspend fun updateUser(user: CurrentUser) {
        session!!.user = user
        datastore.updateUser(user)
        Log.d("ACCREP", "Updated User : $user")
    }

    suspend fun saveAccountDetails(account: AccountDetailsResponse){
        updateUser(user.copyCommonFieldsFrom(account))
        datastore.saveAccountDetails(account)
    }

    fun getAccountDetailsFlow() = datastore.getAccountDetailsAsFlow()

    fun getEnforcementsFlow() = datastore.getAccountDetailsAsFlow().map {
        it?.enforcements
    }

    val citizenShipFlow = datastore.userAccountFLow().mapNotNull { it?.citizenship }

    suspend fun updatePremiumStatus(isPremium:Boolean) {
        val user = session!!.user.copy(
            profile = user.profile.copy(
                premium = isPremium
            )
        )
        updateUser(user)
    }

    suspend fun updateUserName(username: String) {
        val user = session!!.user.copy(username = username)
        session!!.user = user
        datastore.updateUser(user)
    }

    suspend fun updateUserEmail(email: String) {
        val user = session!!.user.copy(email = email)
        session!!.user = user
        datastore.updateUser(user)
    }

    suspend fun updateUserMobile(username: String) {
        val user = session!!.user.copy(username = username)
        session!!.user = user
        datastore.updateUser(user)
    }


    fun updateTokens(new: OAuthTokens) {
        session?.let {
            it.tokens = new
            runBlocking {
                datastore.updateTokens(new)
            }
        }
    }

    suspend fun clearAccount() {
        if(loggedIn) {
            session = null
            datastore.clearData()
            datastore.setProfileCompletionDialogShown()
            datastore.setOnboardingShown(true)
        }
    }

    private fun restoreState() {
        if (didRestore) return
        didRestore = true
        runBlocking {
            val access = datastore.tokens()
            val user = datastore.userAccount()

            if (access != null) {
                if (user==null) {
                    Log.e("ACCREP", "restoreState: user has become null but token exists")
                    Firebase.crashlytics.recordException(Exception("user has become null but token exists"))
                    clearAccount()
                    return@runBlocking
                }
                session = UserSession(user,access)
            }
        }
    }

   suspend fun getCountryCode() : IpApiResponse? {
       val response = ExternalServiceGenerator.createIPApiService().getIpApiData()
       return if (response.isSuccessful) {
           val ipApiResponse = response.body()!!
           ipApiResponse
       } else {
           null
       }
    }
}