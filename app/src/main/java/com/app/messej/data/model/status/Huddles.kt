package com.app.messej.data.model.status


import com.google.gson.annotations.SerializedName

data class Huddles(
    @SerializedName("current_huddles_count") val currentHuddlesCount: Int? = 0,
    @SerializedName("current_participant_count") val currentParticipantCount: Int? = 0,
    @SerializedName("huddle_percentage") val huddlePercentage: Int? = 0,
    @SerializedName("is_satisfied") val isSatisfied: Boolean? = false,
    @SerializedName("required_huddle_count") val requiredHuddleCount: Int? = 0,
    @SerializedName("required_participant_count") val requiredParticipantCount: Int? = 0,
)