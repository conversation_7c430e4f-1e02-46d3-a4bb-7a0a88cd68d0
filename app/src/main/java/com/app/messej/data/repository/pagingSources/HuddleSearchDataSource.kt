package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.repository.pagingSources.HuddleSearchDataSource.Companion.STARTING_KEY
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

class HuddleSearchDataSource(private val api: ChatAPIService, private val type: HuddleType, private val searchKeyWord: String?, private val tab: HuddleInvolvement?): PagingSource<Int, PublicHuddle>() {

    companion object {
        private const val STARTING_KEY = 1
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PublicHuddle> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = if(type == HuddleType.PRIVATE) {
                    api.huddleSearch(
                        page = currentPage,
                        type = type,
                        searchKeyWord!!
                    )
                } else {
                    api.publicHuddleSearch(
                        page = currentPage,
                        type = type,
                        tab = tab?.serializedName()?:"",
                        searchKeyWord!!
                    )
                }
                val responseData = mutableListOf<PublicHuddle>()
                val data = response.body()?.result?.huddles ?: emptyList()
                responseData.addAll(data)
                val nextKey = if (response.body()?.result!!.huddles.isEmpty()) null else currentPage.plus(1)

                LoadResult.Page(
                    data = response.body()?.result!!.huddles,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, PublicHuddle>): Int? {
        val anchorPosition = state.anchorPosition ?: return null
        val article = state.closestItemToPosition(anchorPosition) ?: return null
        return ensureValidKey(key = article.id - (state.config.pageSize / 2) )
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}