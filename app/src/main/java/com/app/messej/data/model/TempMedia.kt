package com.app.messej.data.model

import android.content.ContentResolver
import android.net.Uri
import androidx.media3.common.MediaItem
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.enums.AttachDocumentType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.MediaResolution
import com.app.messej.data.utils.MediaUtils
import java.io.File

/**
 * used to represent a media file in local storage
 * @param name: the filename with extension
 * @param path: the absolutePath of the file in storage
 */
open class TempMedia (
    override var name: String,
    override var path: String,
    override val mediaType: MediaType,
    val sourceUri: Uri? = null,
    val sourceFileName: String? = null,
    val mediaSize: Long = 0
): AbstractMedia() {
    companion object {
        fun of(file: File, type: MediaType, srcUri: Uri? = null) = TempMedia(file.name, file.absolutePath, type, srcUri)
        fun of(uri: Uri, type: MediaType, contentResolver: ContentResolver? = null): TempMedia {
            val meta = contentResolver?.run {
                MediaUtils.getFileNameAndSizeFromUri(uri, this)
            }
            return TempMedia(uri.toString(), uri.toString(), type, uri,meta?.first,meta?.second?:0)
        }
    }

    var videoEditInfo: VideoEditInfo? = null

    val mediaItem: MediaItem
        get() {
            val inputMediaItem = MediaItem.Builder()
            if (sourceUri!=null) {
                inputMediaItem.setUri(sourceUri)
            } else {
                inputMediaItem.setUri(path)
            }
            videoEditInfo?.trim?.let {
                inputMediaItem.setClippingConfiguration(it.clippingConfig)
            }
            return inputMediaItem.build()
        }

    val nameExtension: String?
        get() {
            sourceFileName?: return null
            return sourceFileName.split(".").last()
        }

    val nameDocumentType: AttachDocumentType
        get() = AttachDocumentType.forExtension(nameExtension)

    val mediaSizeHumanized: String
        get() = MediaUtils.humanizeBytes(mediaSize)

    fun setFromFile(file: File) {
        name = file.name
        path = file.absolutePath
    }

    data class SavedMedia(
        override var name: String,
        override var path: String,
        override val mediaType: MediaType,
        val meta: MediaMeta,
        val offlineMedia: OfflineMedia?
    ) : TempMedia(name, path, mediaType) {
        companion object {
            fun of(meta: MediaMeta, offlineMedia: OfflineMedia?) = SavedMedia(meta.mediaName ?: "", offlineMedia?.path?:meta.thumbnail?:"", meta.mediaType, meta, offlineMedia)
        }

        override val resolution: MediaResolution
            get() = meta.resolution

        val hasOfflineMedia: Boolean
            get() = offlineMedia!=null
    }

    data class StickerMedia(
        val meta: MediaMeta,
    ): TempMedia(meta.mediaName.orEmpty(),meta.thumbnail.orEmpty(),meta.mediaType)
}