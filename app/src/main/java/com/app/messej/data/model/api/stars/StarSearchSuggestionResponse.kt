package com.app.messej.data.model.api.stars

import com.app.messej.data.model.Star
import com.app.messej.data.model.enums.SearchType
import com.google.gson.annotations.SerializedName

data class StarSearchSuggestionResponse(
    @SerializedName("users"          ) var users         : ArrayList<Star> = arrayListOf(),
    @SerializedName("total"          ) var total         : Int?             = null,
    @SerializedName("next_page"      ) var nextPage      : Boolean?         = null,
    @SerializedName("current_page"   ) var currentPage   : Int?             = null,
    @SerializedName("search_type"    ) var searchType    : SearchType?          = null,
    @SerializedName("premium_offset" ) var premiumOffset : Int?             = null,
    @SerializedName("free_offset"    ) var freeOffset    : Int?             = null,
    @SerializedName("user_type"      ) var userType      : String?          = null
)
