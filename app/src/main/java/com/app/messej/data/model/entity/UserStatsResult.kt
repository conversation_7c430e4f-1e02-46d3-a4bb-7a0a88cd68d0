package com.app.messej.data.model.entity

import com.google.gson.annotations.SerializedName

data class UserStatsResult(
    @SerializedName("dears") val dears: Int = 0,
    @SerializedName("fans") val fans: Int = 0,
    @SerializedName("active_fans") val activeFans: Int = 0,
    @SerializedName("tribe_score") val tribeScore: Int = 0,
    @SerializedName("citizens") val citizens: Int = 0,
    @SerializedName("officers") val officers: Int = 0,
    @SerializedName("ambassadors") val ambassadors: Int = 0,
    @SerializedName("ministers") val ministers: Int = 0,
    @SerializedName("maidan_wins") val maidanWins: Int = 0,
)