package com.app.messej.data.model.api.business


import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

data class PayoutHistory(
    @SerializedName("flax_rate") val flaxRate: Double? = 0.0,
    @SerializedName("id") val id: Int? = 0,
    @SerializedName("membership") val membership: String? = "",
    @SerializedName("name") val name: String? = "",
    @SerializedName("processing_fee") val processingFee: Double? = 0.0,
    @SerializedName("profile_image") val profileImage: String? = "",
    @SerializedName("receivable") val receivable: Double? = 0.0,
    @SerializedName("requested_date") val requestedDate: String? = "",
    @SerializedName("requested_points_for_review") val requestedPointsForReview: Double? = 0.0,
    @SerializedName("status") val status: String? = "",
    @SerializedName("status_id") val statusId: Int? = 0,
    @SerializedName("time_created") val timeCreated: String? = "",
    @SerializedName("time_updated") val timeUpdated: String? = "",
    @SerializedName("transfer_fee") val transferFee: Double? = 0.0,
    @SerializedName("user_id") val userId: Int? = 0,
    @SerializedName("verified") val verified: Boolean? = false,
    var isExpanded: Boolean = false,
){
    val parsedRequestedDate:LocalDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(requestedDate)?.run { DateTimeUtils.getLocalDateTime(this) }
}