package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.ActivityMeta
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.HuddleReportedMessage.Companion.COLUMN_HUDDLE_ID
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

@Entity(
    tableName = EntityDescriptions.TABLE_HUDDLE_REPORTED_MESSAGES,
    indices = [
        Index(COLUMN_HUDDLE_ID, unique = false)
    ]
)
@TypeConverters(
    SenderDetails.Converter::class,
    ActivityMeta.Converter::class,
    ReplyTo.Converter::class,
    MediaMeta.Converter::class
)
data class HuddleReportedMessage (

    @SerializedName("message_id"        ) @ColumnInfo(name = COLUMN_MESSAGE_ID         ) @PrimaryKey(autoGenerate = false) override val messageId        : String,
    @SerializedName("room_id"           ) @ColumnInfo(name = "room_id"                 ) override val roomId           : String?    = null,
    @SerializedName("message"           ) @ColumnInfo(name = "message"                 ) override var rawMessage          : String?    = null,

    @SerializedName("time_created"      ) @ColumnInfo(name = COLUMN_MESSAGE_CREATED    ) override val createdTime      : String,
    @SerializedName("message_sent"      ) @ColumnInfo(name = "message_sent"            ) override val sentTime         : String?    = null,
    @SerializedName("time_updated"      ) @ColumnInfo(name = "time_updated"            )          var updatedTime      : String?    = null,

    @SerializedName("media"             ) @ColumnInfo(name = COLUMN_MEDIA              ) override var media            : String?    = null,
    @SerializedName("media_meta"        ) @ColumnInfo(name = "media_meta"              ) override var mediaMeta        : MediaMeta? = null,
    @SerializedName("message_type"      ) @ColumnInfo(name = "message_type"            ) override var internalMessageType     : MessageType?    = null,

    @SerializedName("sender_id"         ) @ColumnInfo(name = COLUMN_SENDER             ) override var sender           : Int,

    @SerializedName("report_id"         ) @ColumnInfo(name = "report_id"               )          var reportId         : Int,
    @SerializedName("reports_count"     ) @ColumnInfo(name = "reports_count"           )          var reportsCount     : Int        = 0,

    @SerializedName("deletedUser"       ) @ColumnInfo(name = "deletedUser"             )          val userDeleted      : Boolean    = false,
    @SerializedName("sender_membership" ) @ColumnInfo(name = "sender_membership"       )          val senderMembership : UserType?    = null,
    @SerializedName("sender_name"       ) @ColumnInfo(name = "sender_name"             )          val senderName       : String     = "",
    @SerializedName("sender_role"       ) @ColumnInfo(name = "sender_role"             )          val senderRole       : UserRole?     = null,
    @SerializedName("sender_username"   ) @ColumnInfo(name = "sender_username"         )          val senderUsername   : String?    = null,
    @SerializedName("thumbnail"         ) @ColumnInfo(name = "thumbnail"               )          val thumbnail        : String?    = null,

    @SerializedName("status"            ) @ColumnInfo(name = "status"                  )          var reportStatus     : ReportStatus?    = null,

    @SerializedName("huddle_id"         ) @ColumnInfo(name = COLUMN_HUDDLE_ID          )          var huddleId         : Int,
    @SerializedName("verified"          ) @ColumnInfo(name = "verified"                )          var verified         : Boolean = false,
    @SerializedName("color"          ) @ColumnInfo(name = "color") override val chatTextColor         : ChatTextColor?,
    @SerializedName("forward_id"    ) @ColumnInfo(name = "forward_id")                override  val forwardId   : String?  = null

): AbstractChatMessage() {

    companion object {
        const val COLUMN_MESSAGE_ID = "message_id"
        const val COLUMN_HUDDLE_ID = "huddle_id"
        const val COLUMN_MESSAGE_CREATED = "created"
        const val COLUMN_MEDIA = "media"
        const val COLUMN_SENDER = "sender_id"
    }

    val senderDetails: SenderDetails
        get() = SenderDetails(
            id = sender,
            _username = "",
            name = senderName,
            deletedAccount = userDeleted,
            premium = senderMembership==UserType.PREMIUM,
            role = null,
            thumbnail = thumbnail
        )

    override val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(createdTime)

    override val formattedCreatedTime: String?
        get() = DateTimeUtils.format(parsedCreatedTime, DateTimeUtils.FORMAT_READABLE_DATE_TIME)

    val parsedUpdatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(updatedTime)

    //unused values
    @Ignore override val deliveredTime: String? = null

    @Ignore override val isActivity    : Boolean = false
    @Ignore override val activityMeta  : ActivityMeta? = null

    @Ignore override val read          : String? = null
    @Ignore override val receiver      : Int? = null
    @Ignore override val replyTo       : ReplyTo? = null

    @Ignore override val liked    : Boolean = false
    @Ignore override val reported    : Boolean = false

    @Ignore override val deleted: Boolean = reportStatus != ReportStatus.PENDING

    enum class ReportStatus {
        @SerializedName("pending") PENDING,
        @SerializedName("admin_deleted") ADMIN_DELETED,
        @SerializedName("admin_deleted_user_blocked") ADMIN_DELETED_USER_BLOCKED,
        @SerializedName("manager_deleted") MANAGER_DELETED,
        @SerializedName("manager_deleted_user_blocked") MANAGER_DELETED_USER_BLOCKED,
        @SerializedName("user_deleted") USER_DELETED
    }
}