package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.ReportedByResponse
import com.app.messej.data.model.enums.ReportToManagerType
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1

class ReportedParticipantsDataSource(private val api: ChatAPIService,
                                     private val accountRepo: AccountRepository, private val huddleId: Int, private val reportId: Int, private val reportType: ReportToManagerType): PagingSource<Int, ReportedByResponse.ReportedParticipant>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ReportedByResponse.ReportedParticipant> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response =
                    if (reportType == ReportToManagerType.MESSAGE) api.getHuddleReportedParticipants(huddleId, reportId, page = currentPage)
                    else api.getHuddleCommentReportedParticipants(huddleId, reportId, page = currentPage)

                val responseData = mutableListOf<ReportedByResponse.ReportedParticipant>()
                val data = response.body()?.result?.reports ?: emptyList()

                responseData.addAll(data)
                val nextKey = if (!response.body()?.result!!.nextPage) null else currentPage.plus(1)

                LoadResult.Page(
                    data = response.body()?.result!!.reports,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, ReportedByResponse.ReportedParticipant>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}