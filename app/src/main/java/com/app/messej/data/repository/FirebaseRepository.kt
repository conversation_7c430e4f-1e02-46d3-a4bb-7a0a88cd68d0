package com.app.messej.data.repository

import com.google.android.gms.tasks.Tasks
import com.google.firebase.installations.FirebaseInstallations

class FirebaseRepository {

    companion object {
        private var firebaseID: String? = null
    }

    fun getFirebaseInstallationID() : String {
        firebaseID?.let { return it }
        val idTask = FirebaseInstallations.getInstance().id
        val id = Tasks.await(idTask)
        firebaseID = id
        return id
    }
}