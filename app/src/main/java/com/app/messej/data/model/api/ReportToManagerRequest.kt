package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName

sealed class ReportToManagerRequest {

    abstract var categoryId: Int?
    abstract var comment: String?

    data class MessageReportRequest(
        @Transient val huddleId: Int,
        @Transient val messageId: String,
        @SerializedName("category_id") override var categoryId: Int? = null,
        @SerializedName("comment") override var comment: String? = null
    ) : ReportToManagerRequest()

    data class MessageReportCancelRequest(
        @Transient val huddleId: Int,
        @Transient val messageId: String,
    ) : ReportToManagerRequest() {
        @Transient
        override var categoryId: Int? = null
        @Transient
        override var comment: String? = null
    }

    data class CommentReportRequest(
        @Transient val huddleId: Int,
        @Transient val messageId: String,
        @SerializedName("commentId") var commentId: String,
        @SerializedName("category_id") override var categoryId: Int? = null,
        @SerializedName("comment") override var comment: String? = null,
    ) : ReportToManagerRequest()

    data class CommentReportCancelRequest(
        @Transient val huddleId: Int,
        @Transient val messageId: String,
        @SerializedName("commentId") var commentId: String,
        @SerializedName("action") val action: String = "cancel"
    ) : ReportToManagerRequest() {
        @Transient
        override var categoryId: Int? = null
        @Transient
        override var comment: String? = null
    }
}