package com.app.messej.data.repository

import android.content.Context
import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.room.withTransaction
import com.amazonaws.mobile.config.AWSConfiguration
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.AbstractChatMessage.SendStatus
import com.app.messej.data.model.Category
import com.app.messej.data.model.EditableFlashMedia
import com.app.messej.data.model.FlashCommentLikeRequest
import com.app.messej.data.model.FlashCommentPayload
import com.app.messej.data.model.FlashLikeRequest
import com.app.messej.data.model.FlashReplyCommentPayload
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.ShareTo
import com.app.messej.data.model.api.FlashEligibility
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.api.flash.CreateFlashRequest
import com.app.messej.data.model.api.flash.UserFunctionalityBlockRequest
import com.app.messej.data.model.api.huddles.FlashSearchResponse
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.FlashVideo.FlashType
import com.app.messej.data.model.entity.FlashVideoWithMedia
import com.app.messej.data.model.entity.LocalFlashMedia
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.model.entity.PostReply
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.model.enums.FlashTab
import com.app.messej.data.model.enums.Functionality
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.repository.mediators.FlashCommentRemoteMediator
import com.app.messej.data.repository.mediators.MyFlashListRemoteMediator
import com.app.messej.data.repository.pagingSources.FlashAccountSearchDataSource
import com.app.messej.data.repository.pagingSources.FlashArchivedDataSource
import com.app.messej.data.repository.pagingSources.FlashFeedDataSource
import com.app.messej.data.repository.pagingSources.FlashMyFeedDataSource
import com.app.messej.data.repository.pagingSources.FlashReportedCommentsDataSource
import com.app.messej.data.repository.pagingSources.FlashSearchDataSource
import com.app.messej.data.repository.pagingSources.SingleFlashDataSource
import com.app.messej.data.repository.pagingSources.UserFlashDataSource
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.MediaUtils.uri
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import com.github.f4b6a3.uuid.UuidCreator
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class FlashRepository(private var mContext: Context): BaseMediaUploadRepository(mContext)  {

    private val datastore = FlashatDatastore()

    fun getFlashFeedPager(tab: FlashTab) : Pager<FlashFeedDataSource.PagingParams, FlashVideo> {
        return Pager(
            config = PagingConfig(pageSize = 25, enablePlaceholders = false),
            pagingSourceFactory = { FlashFeedDataSource(APIServiceGenerator.createService(FlashAPIService::class.java),tab) }
        )
    }

    fun getMyFlashFeedPager() : Pager<Int, FlashVideo> {
        return Pager(
            config = PagingConfig(pageSize = 25, enablePlaceholders = false),
            pagingSourceFactory = { FlashMyFeedDataSource(APIServiceGenerator.createService(FlashAPIService::class.java)) }
        )
    }

    fun getSingleFlashPager(id: String) : Pager<Int, FlashVideo> {
        return Pager(
            config = PagingConfig(pageSize = 25, enablePlaceholders = false),
            pagingSourceFactory = { SingleFlashDataSource(APIServiceGenerator.createService(FlashAPIService::class.java),id) }
        )
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getMyFlashPager() : Pager<Int, FlashVideoWithMedia> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = MyFlashListRemoteMediator(accountRepo.user.id, db, APIServiceGenerator.createService(FlashAPIService::class.java)),
            pagingSourceFactory = { db.getFlashDao().getFlashVideoWithMediaPager(FlashType.MINE) }
        )
    }

    fun getUserFlashPager(id: Int) = Pager(
        config = PagingConfig(pageSize = 50, enablePlaceholders = false),
        pagingSourceFactory = { UserFlashDataSource(APIServiceGenerator.createService(FlashAPIService::class.java),id) }
    )

    fun getArchivedFlashPager() : Pager<Int, FlashVideo> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = { FlashArchivedDataSource(APIServiceGenerator.createService(FlashAPIService::class.java)) }
        )
    }

    fun getSavedFlashPager() = Pager(
        config = PagingConfig(pageSize = 25, enablePlaceholders = false),
        pagingSourceFactory = { db.getFlashDao().getFlashVideoPager(FlashType.SAVED) }
    )

    fun getDraftFlashPager() = Pager(
        config = PagingConfig(pageSize = 25, enablePlaceholders = false),
        pagingSourceFactory = { db.getFlashDao().getFlashVideoPager(FlashType.DRAFT) }
    )

    suspend fun getLocalFlash(id: String): FlashVideoWithMedia? {
        return db.getFlashDao().getLocalFlashVideoWithMedia(id)
    }

    suspend fun saveFlash(flash: FlashVideo): Boolean {
        if (accountRepo.user.id == flash.userId) return false
        db.getFlashDao().insert(
            flash.copy(
                flashType = FlashType.SAVED
            )
        )
        return true
    }

    suspend fun getFlashDetail(id: String) : ResultOf<FlashVideo> {
        return try {
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).getFlashDetail(id)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getHuddleCategories: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getFlashCategories() : ResultOf<List<Category>> {
        return try {
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).getFlashCategories()
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getHuddleCategories: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun processVideo(med: EditableFlashMedia, listener: VideoEncoderUtil.MediaProcessingListener): File {
        return VideoEncoderUtil.encodeVideoMedia3(med, listener)
    }

    suspend fun createFlash(media: File, caption: String, category: Category?, shareTo: ShareTo, draft: Boolean=false,isCommentDisabled: Boolean): FlashVideoWithMedia {
        val uuid = UuidCreator.getTimeBased().toString()

        val localMedia = createFlashMedia(media,uuid) { name -> APIUtil.getFlashMediaUploadKey(name,accountRepo.user.id) }

        var dbFlash = FlashVideo(
            id = uuid,
            caption = caption,
            mediaMeta = localMedia.meta,

            userId = accountRepo.user.id,
            senderDetails = SenderDetails.from(accountRepo.user),
            shareTo = shareTo,
            videoUrl = localMedia.meta.s3Key,
            thumbnailUrl = "",

            category = category?.name,
            categoryId = category?.categoryId,

            timeCreated = DateTimeUtils.getZonedDateTimeNowAsString(),

            flashType = if (draft) FlashType.DRAFT else FlashType.MINE,
            commentDisabled = isCommentDisabled
        )
        dbFlash.sendStatus = if(draft) SendStatus.NONE else SendStatus.PENDING

        Log.w("FLASH", "createChatMessage: inserting message: $dbFlash")
        db.withTransaction {
            db.getFlashDao().insert(dbFlash)
            db.getFlashDao().insert(localMedia)
        }
        return db.getFlashDao().getLocalFlashVideoWithMedia(uuid)!!
    }

    suspend fun updateDraft(flash: FlashVideo, queueSend: Boolean = false) {
        db.getFlashDao().update(flash.apply {
            if (queueSend) {
                flashType = FlashType.MINE
                sendStatus = SendStatus.PENDING
            }
        })
    }

    private suspend fun createFlashMedia(file: File, uuid: String, s3KeyProvider: (String) -> String): LocalFlashMedia {
        val file = MediaUtils.storeFlashVideoFile(mContext, file.path, uuid)
        val s3Key = s3KeyProvider(file.name)

        val meta = MediaMeta(
            mediaType = MediaType.VIDEO,
            mediaName = file.name,
            mimeType = MediaUtils.getMIME(file),
            s3Key = s3Key,
        ).apply {
            formattedSize = MediaUtils.getFileSize(file)
            seconds = MediaUtils.getDuration(file)
            val res = MediaUtils.getVideoResolution(file.uri)
            mediaWidth = res.width.toString()
            mediaHeight = res.height.toString()
        }

        val media =  LocalFlashMedia(
            flashId = uuid,
            key = s3Key,
            path = file.absolutePath,
            meta = meta
        ).apply {
            uploadState = MediaUploadState.Pending
        }
        Firebase.crashlytics.log("stored flash (${media.flashId}) file to ${media.path}")
        return media
    }

    suspend fun sendFlash(msg: FlashVideoWithMedia): ResultOf<Unit> {
        if (msg.mediaIsUploading) return ResultOf.getError("Media is Already uploading")
        if(msg.needsMediaUpload) {
            val result = uploadMedia(msg)
            if(result !is ResultOf.Success) return result
        }
        val flash = db.withTransaction {
            val fv = db.getFlashDao().getLocalFlashVideo(msg.flash.id)
            return@withTransaction if (fv?.sendStatus == SendStatus.PENDING) {
                fv.sendStatus = SendStatus.SENDING
                db.getFlashDao().update(fv)
                fv
            } else null
        } ?: return ResultOf.getError("Chat is already sending. exiting...")
        return try {
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).createFlash(CreateFlashRequest.from(flash))
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                db.withTransaction {
                    db.getFlashDao().apply {
                        deleteFlashVideo(flash.id)
                        deleteFlashMedia(flash.id)
                        insert(result.value.apply {
                            flashType = FlashType.MINE
                        })
                    }
                }
            } else {
                db.withTransaction {
                    flash.sendStatus = SendStatus.PENDING
                    db.getFlashDao().update(flash)
                }
            }
            result.convertTo { ResultOf.Success(Unit) }
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getHuddleCategories: ", e)
            db.withTransaction {
                flash.sendStatus = SendStatus.PENDING
                db.getFlashDao().update(flash)
            }
            ResultOf.Error(Exception(e))
        }
    }

    private suspend fun uploadMedia(msg: FlashVideoWithMedia): ResultOf<Unit> = withContext(Dispatchers.IO) {
        if (!msg.needsMediaUpload) return@withContext ResultOf.getError("No media to upload")
        val media = msg.media?:return@withContext ResultOf.getError("No media to upload")
        if (!getMediaUploadLock(media.flashId)) return@withContext ResultOf.getError("Media is already being uploaded")

        val result = prepareAndUploadMultipartMedia(media)
        if (result is ResultOf.Success) {
            media.uploadState = MediaUploadState.None
            db.getFlashDao().update(media)
        } else {
            media.uploadState = MediaUploadState.Pending
            db.getFlashDao().update(media)
        }
        result
    }

    private suspend fun getMediaUploadLock(flashId: String): Boolean {
        return db.withTransaction {
            val media = db.getFlashDao().getLocalFlashMedia(flashId)
            Log.d("FLASH", "getMediaUploadLock: current status is: ${media?.uploadState}")
            if (media?.uploadState == MediaUploadState.Pending) {
                media.uploadState = MediaUploadState.Uploading(0)
                db.getFlashDao().update(media)
                return@withTransaction true
            }
            return@withTransaction false
        }
    }

    override suspend fun getVideoSecrets(): ResultOf<UploadCredentialsResponse> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java, true).getVideoUploadCredentials()
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    override fun getAwsConfig() = AWSConfiguration(mContext, R.raw.awsconfiguration,"Flash")

    private suspend fun prepareAndUploadMultipartMedia(media: LocalFlashMedia): ResultOf<Unit> {
        try {
            MediaUploadListener.post(media.flashId, 0)
            performMultipartMediaUpload(media.s3UploadMedia).collect {
                when (val res = it) {
                    is MultipartMediaUploadResult.Progress -> {
//                        withContext(Dispatchers.IO) {
//                            db.withTransaction {
//                                val copy = media.copy(uploadState = UploadState.Uploading(res.percent))
//                                db.getChatMessageDao().updateMedia(copy)
//                            }
//                        }
                        Log.d("ENCODE", "posting ${res.percent} to MUL")
                        MediaUploadListener.post(media.flashId, res.percent)
                    }
                    is MultipartMediaUploadResult.Complete -> {
                        // Nothing to do. Channel will close
                    }

                    is MultipartMediaUploadResult.Error -> throw res.error
                }
            }
            MediaUploadListener.clear(media.flashId)
        } catch (e: Exception) {
            MediaUploadListener.clear(media.flashId)
            Log.e("ENCODE", "prepareAndUploadMultipartMedia:",e)
            return ResultOf.getError(e)
        }

        Log.d("ENCODE", "prepareAndUploadMultipartMedia: end of uploading Method")
        return ResultOf.Success(Unit)
    }

    suspend fun refreshPlaybackCookie() : ResultOf<VideoPlaybackCookie> {
        return try {
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).getVideoPlaybackCookies()
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                datastore.saveFlashCookie(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getHuddleCategories: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun playbackCookieFlow() = datastore.getFlashCookieFlow()

    suspend fun deleteComment(id: String): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).deleteComment(id)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPendingFlash(): FlashVideoWithMedia? {
        return db.withTransaction {
            return@withTransaction db.getFlashDao().getFlashByStatus(SendStatus.PENDING)
        }
    }

    fun getFlashSearchPager(keyword: String): Pager<Int, FlashVideo> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = { FlashSearchDataSource(APIServiceGenerator.createService(FlashAPIService::class.java), keyword) }
        )
    }

    fun getFlashAccountsSearchPager(keyword: String): Pager<Int, FlashSearchResponse.FlashUser> {
        return Pager(
            config = PagingConfig(pageSize = 25, enablePlaceholders = false),
            pagingSourceFactory = { FlashAccountSearchDataSource(APIServiceGenerator.createService(FlashAPIService::class.java), keyword) }
        )
    }
    suspend fun writeComment(flashId: String, body: FlashCommentPayload) : ResultOf<Unit> {
        return try {
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).writeFlashComment(flashId, body)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun writeReplyComment(commentId: String, body: FlashReplyCommentPayload): ResultOf<Unit> {
        return try {
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).writeFlashReplyComment(commentId, body)
            val result = APIUtil.handleResponse(response)

            // If successful, refresh the replies for this comment to prevent duplicates
            if (result is ResultOf.Success && body.flashId != null) {
                // Clear existing replies for this comment before fetching fresh ones
                db.getPostReplyDao().deletePostReplies(commentId, CommentType.FLASH)
                // Fetch fresh replies from server using the flashId from the payload
                getFlashReplies(flashId = body.flashId, parentCommentId = commentId, page = 1)
            }

            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun commentToggle(id: String, enable: Boolean): ResultOf<String> {
        return try {
            val resp = if (enable) {
                Log.i("BFPF", "calling enable")
                APIServiceGenerator.createService(FlashAPIService::class.java).commentEnable(id)
            } else {
                Log.i("BFPF", "calling disable")
                APIServiceGenerator.createService(FlashAPIService::class.java).commentDisable(id)
            }
            val result = APIUtil.handleResponseWithoutResult(resp)
            if (result is ResultOf.Success) {
                updateFlashCommentToggle(id, enable)
            }
            Log.w("BFPF", "commentToggle: $result")
            result

        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    private suspend fun updateFlashCommentToggle(messageId: String, enabled: Boolean) {
        db.getFlashDao().apply {
            val flash = getLocalFlashVideo(messageId) ?: return@apply
            db.withTransaction {
                update(
                    flash.copy(
                        commentDisabled = !enabled
                    )
                )
            }
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getFlashComments(flashId: String, commentCountCallback: (Int) -> Unit) : Pager<Int, PostCommentWithReplies> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = FlashCommentRemoteMediator(
                flashId = flashId,
                database = db,
                networkService = APIServiceGenerator.createService(FlashAPIService::class.java),
                commentCountCallback = commentCountCallback
            ),
            pagingSourceFactory = { db.getPostCommentDao().getBaseCommentsWithRepliesPaging(flashId, CommentType.FLASH) }
        )
    }

    // Manual pagination for Flash replies - fetches from API and stores in reply table
    suspend fun getFlashReplies(flashId: String, parentCommentId: String, page: Int, pageSize: Int = 20): ResultOf<List<PostReply>> {
        return try {

            // Fetch from network API
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).getFlashReplyComments(
                id = parentCommentId,
                page = page
            )

            if (response.isSuccessful && response.code() == 200) {
                val result = response.body()?.result ?: return ResultOf.Error(Exception("No data received from server"))
                val apiReplies = result.data

                // Convert PostatComment (API response) to BasePostatReply (database entity)
                val replyEntities = apiReplies.map { basePostatReply ->
                    basePostatReply.copy(
                        basePostId = flashId,
                        parentCommentId = parentCommentId,
                        type = CommentType.FLASH
                    )
                }


                // Sanitize and store in reply table
                replyEntities.forEach { it.sanitize() }
                db.getPostReplyDao().insertPostReplies(replyEntities)

                ResultOf.Success(replyEntities)
            } else {
                ResultOf.Error(Exception("Failed to fetch Flash replies"))
            }
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    fun getReportedCommentsPager() = Pager(
        config = PagingConfig(pageSize = 50, enablePlaceholders = false),
        pagingSourceFactory = { FlashReportedCommentsDataSource(APIServiceGenerator.createService(FlashAPIService::class.java)) }
    )

    suspend fun deleteFlashComment(id: String): ResultOf<Boolean> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).deleteFlashComment(id)
            return when(val result = APIUtil.handleResponseWithoutResult(resp)) {
                is ResultOf.Success -> {
                    ResultOf.Success(true)
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }

        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteFlashCommentReply(id: String): ResultOf<Boolean> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).deleteFlashCommentReply(id)
            return when(val result = APIUtil.handleResponseWithoutResult(resp)) {
                is ResultOf.Success -> {
                    ResultOf.Success(true)
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }

        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }



    suspend fun likeFlash(flashId: String) : ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).likeFlash(flashId)
            val result = APIUtil.handleResponseWithoutResult(resp)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun unlikeFlash(flashId: String) : ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).unlikeFlash(flashId)
            val result = APIUtil.handleResponseWithoutResult(resp)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteFlash(flash: FlashVideo): ResultOf<String> {
        return try {
            val result = if (flash.flashType == FlashType.DRAFT) {
                ResultOf.Success("Success")
            } else {
                val resp = APIServiceGenerator.createService(FlashAPIService::class.java).deleteFlash(flash.id)
                APIUtil.handleResponseWithoutResult(resp)
            }
            if (result is ResultOf.Success) {
                db.withTransaction {
                    db.getFlashDao().apply {
                        deleteFlashMedia(flash.id)
                        deleteFlashVideo(flash.id)
                    }
                }
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun flashBlock(userId: Int) : ResultOf<String> {
        return try {
            val request = UserFunctionalityBlockRequest(Functionality.FLASH, BlockUnblockAction.BLOCK)
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).blockuserFunctionality(userId, request)
            val result = APIUtil.handleResponseWithoutResult(resp)
            result
    }catch (e: Exception) {
        ResultOf.Error(Exception(e))

        }
    }

    suspend fun deleteFlashVideos(flashList: List<String>) : ResultOf<String> {
        return try {
            db.withTransaction {
                db.getFlashDao().apply {
                    deleteMultipleFlashVideo(flashList)
                    deleteMultipleFlashMedia(flashList)
                }
            }
            ResultOf.Success("Success")
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }


    suspend fun archiveFlash(flashId: String) : ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).archiveFlash(flashId)
            val result = APIUtil.handleResponseWithoutResult(resp)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun unarchiveFlash(flashId: String) : ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).unarchiveFlash(flashId)
            val result = APIUtil.handleResponseWithoutResult(resp)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun resetSendActionsAndMediaUploads() = withContext(Dispatchers.IO) {
        db.getFlashDao().apply {
            resetSendStatus()
            resetUploadingMedia()
        }
    }

    suspend fun getFlashEligibilityDetails(currentDate: String): ResultOf<FlashEligibility> {
        return try {
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java, true).getFlashEligibilityDetails(currentDate = currentDate)
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun sendFlashCommentLike(request: FlashCommentLikeRequest): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).postFlashCommentLike(request)
            val result = APIUtil.handleResponseWithoutResult(response)

            // Update local database on success
            if (result is ResultOf.Success) {
                updateLocalFlashCommentLikeCount(request)
            }

            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    private suspend fun updateLocalFlashCommentLikeCount(request: FlashCommentLikeRequest) = withContext(Dispatchers.IO) {
        db.withTransaction {
            val increment = 1

            if (request.replyId != null) {
                db.getPostReplyDao().updateReplyLikeCount(request.replyId, CommentType.FLASH, increment)
            } else if (request.commentId != null) {
                db.getPostCommentDao().updateCommentLikeCount(request.commentId, CommentType.FLASH, increment)
            }
        }
    }

     suspend fun deleteFlashCommentFromDB(commentId: String) = withContext(Dispatchers.IO) {
        db.withTransaction {
            db.getPostCommentDao().deletePostCommentFromDB(commentId, CommentType.FLASH)
        }
    }

    suspend fun deleteFlashCommentReplyFromDB(commentId: String) = withContext(Dispatchers.IO) {
        db.withTransaction {
            db.getPostReplyDao().deleteSingleCommentReply(commentId, CommentType.FLASH)
        }
    }

    //flash  like
    suspend fun sendPaidLike(postId: String): ResultOf<String> {
        return try {
            val request = FlashLikeRequest(postId)
            val response = APIServiceGenerator.createService(FlashAPIService::class.java).postFlashLike(request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "postFlashLike: ", e)
            ResultOf.Error(Exception(e))
        }
    }

}