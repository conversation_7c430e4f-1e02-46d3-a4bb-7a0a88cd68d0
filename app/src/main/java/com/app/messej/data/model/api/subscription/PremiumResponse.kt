package com.app.messej.data.model.api.subscription


import com.google.gson.annotations.SerializedName

data class PremiumResponse(
    @SerializedName("reason") val reason: String? = "",
    @SerializedName("send_notification") val sendNotification: Boolean? = false,
    @SerializedName("success") val success: Boolean? = false,
    @SerializedName("order_id") val orderID: String? = "",
    @SerializedName("purchased_flax") val purchasedFlax: String? = "",
)