package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.enums.SocialCaseStatus
import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName

data class AskedQuestionsResponse(
    @SerializedName(value = "has_next") val haveNextPage: Boolean? = null,
    @SerializedName(value = "answerer_id") val answererUserId: Int? = null,
    @SerializedName(value = "total") val totalQuestions: Int? = null,
    @SerializedName(value = "eligible_to_question") val eligibleForAskQuestion: Boolean? = false,
    @SerializedName(value = "case_status") val caseStatus: SocialCaseStatus? = null,
    @SerializedName(value = "data") val questionAndAnswers: List<QuestionAndAnswer>? = null
) {
    data class QuestionAndAnswer(
        @SerializedName(value = "id") val id : Int?,
        @SerializedName(value = "question") val question : String?,
        @SerializedName(value = "answer") val answer : String?,
        @SerializedName(value = "questioner_id") val questionerUserId : Int?,
        var answererUserId : Int?,
        var caseStatus : SocialCaseStatus?,
    ) {

        val isAnswered: Boolean
            get() = !answer.isNullOrEmpty()

        fun isPresidentQuestion(presidentUserId: Int?): Boolean =
            presidentUserId == questionerUserId

        val isCaseClosed: Boolean
            get() = caseStatus?.isClosed == true

        val isCasePendingApproval: Boolean
            get() = caseStatus?.isPendingApproval == true

        fun isAwaitingResponse(currentUserId: Int): Boolean =
            !isAnswered && answererUserId != currentUserId && !isCaseClosed

        fun canAnswer(currentUserId: Int, presidentUserId: Int?): Boolean =
            if(isCasePendingApproval) {
                isPresidentQuestion(presidentUserId) && !isAnswered && answererUserId == currentUserId
            } else {
                !isAnswered && answererUserId == currentUserId && !isCaseClosed
            }

        fun canDeleteOrEditQuestion(currentUserId: Int): Boolean =
            !isAnswered && currentUserId == questionerUserId && !isCaseClosed

        fun canDeleteOrEditAnswer(currentUserId: Int, presidentUserId: Int?): Boolean =
            if (isCasePendingApproval) {
                isPresidentQuestion(presidentUserId) && isAnswered && currentUserId == answererUserId
            } else {
                isAnswered && currentUserId == answererUserId && !isCaseClosed
            }

        fun canAnswerAndEditAnswer(currentUserId: Int, presidentUserId: Int?): Boolean =
            canAnswer(currentUserId, presidentUserId) || canDeleteOrEditAnswer(currentUserId, presidentUserId) && !isCaseClosed

        fun isDropdownArrowVisible(currentUserId: Int, presidentUserId: Int?): Boolean =
            (isAwaitingResponse(currentUserId) || canAnswer(currentUserId, presidentUserId) ||
            canDeleteOrEditQuestion(currentUserId) || canDeleteOrEditAnswer(currentUserId, presidentUserId)) || isAnswered
    }

    companion object {
        val questionTestData = QuestionAndAnswer(
            id = 1,
            question = "How you going to we plan to utilize the collected COiNS for various purpo?",
            answer = null,
            questionerUserId = 11,
            answererUserId = 11,
            caseStatus = SocialCaseStatus.NEW
        )
        val questionTestDataWithAnswer = questionTestData.copy(answer = "Answer of the case")
        val testQuestionCurrentUser = CurrentUser(id = 11, profile = CurrentUser.Profile(), username = "Abijith", citizenship = UserCitizenship.MINISTER)
    }
}