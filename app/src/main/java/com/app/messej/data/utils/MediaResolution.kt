package com.app.messej.data.utils

import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

data class MediaResolution(
    val width: Int,
    val height: Int,
) {
    override fun toString(): String {
        return "$width x $height"
    }

    val longEdge: Int
        get() = max(width, height)

    val shortEdge: Int
        get() = min(width, height)

    fun fitInside(max: Int): MediaResolution {
        if (longEdge<=max) return this
        val factor: Float = max.toFloat()/longEdge
        return MediaResolution((width * factor).roundToInt(), (height * factor).roundToInt())
    }

    fun fillInsideAndCrop(res: MediaResolution): MediaResolution {
        val heightFactor = res.height.toFloat()/height
        val widthFactor = res.width.toFloat()/width
        val factor = max(heightFactor,widthFactor)
        if (factor>=1) {
            return if (heightFactor<widthFactor) MediaResolution(width,(width*res.height/res.width))
            else MediaResolution((height*res.width/res.height),height)
        }
        return MediaResolution((width * factor).roundToInt().coerceAtMost(res.width), (height * factor).roundToInt().coerceAtMost(res.height))
    }

    val aspectRatio = if (height==0) 1f else width.toFloat()/height
}