package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class HuddleActionType {
    // User Side Actions
    @SerializedName("accept") ACCEPT_HUDDLE_INVITE,
    @SerializedName("decline") DECLINE_HUDDLE_INVITE,
    @SerializedName("block") BLOCK_HUDDLE_INVITE,

    @SerializedName("accepted") ACCEPT_ADMIN_REQUEST,
    @SerializedName("declined") DECLINE_ADMIN_REQUEST,
    @SerializedName("ignored") IGNORE_ADMIN_REQUEST,

    //Admin Side actions
    @SerializedName("cancel") CANCEL_HUDDLE_INVITE,
    @SerializedName("cancelled") CANCELLED_HUDDLE_REQUEST,
    @SerializedName("unblock") UNBLOCK_HUDDLE_INVITE,
    @SerializedName("admin_blocked") BLOCK_HUDDLE_REQUEST;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}