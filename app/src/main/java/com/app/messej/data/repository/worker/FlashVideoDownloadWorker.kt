package com.app.messej.data.repository.worker

import android.app.Notification
import android.app.NotificationManager
import android.content.Context
import android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ForegroundInfo
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkContinuation
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.app.messej.MainApplication
import com.app.messej.R
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.repository.BaseMediaUploadRepository
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.utils.MediaUtils
import com.app.messej.service.FlashatFirebaseMessagingService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext


class FlashVideoDownloadWorker(appContext: Context, workerParams: WorkerParameters) : CoroutineWorker(appContext, workerParams) {

    private val notificationManager = appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    private val flashRepo = FlashRepository(appContext)

    companion object {
        private const val WORK_TAG = "flash_download_service"
        const val Progress = "Progress"
        const val NOTIFICATION_ID = 1456
        private const val JOB_GROUP_NAME = "flash_download_job"

        private const val WORK_DATA_FILE_NAME = "fileName"
        private const val WORK_DATA_S3_KEY = "fileS3Key"

        private fun Data.toMediaMeta() = MediaMeta(
            mediaType = MediaType.VIDEO,
            mediaName = getString(WORK_DATA_FILE_NAME),
            s3Key = getString(WORK_DATA_S3_KEY).orEmpty()
        )

        suspend fun startDownload(flashVideo: FlashVideo) = withContext(Dispatchers.IO) {
            val workManager = WorkManager.getInstance(MainApplication.applicationContext())
            val data = Data.Builder()
            data.putString(WORK_DATA_FILE_NAME, flashVideo.mediaMeta.mediaName)
            data.putString(WORK_DATA_S3_KEY, flashVideo.mediaMeta.s3Key)

            val workRequest = OneTimeWorkRequestBuilder<FlashVideoDownloadWorker>()
                .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                .setInputData(data.build())
                .addTag(WORK_TAG)
                .build()
            val work: WorkContinuation = workManager.beginUniqueWork(JOB_GROUP_NAME, ExistingWorkPolicy.APPEND_OR_REPLACE, workRequest)
            work.enqueue()
        }
    }

    override suspend fun doWork(): Result {
        try {
            val meta = inputData.toMediaMeta()
            Log.d("FVDW", "Starting download Worker for ${meta.mediaName}")
            flashRepo.downloadMultipartMedia(meta).collect {
                when (val res = it) {
                    is BaseMediaUploadRepository.VideoDownloadResult.Progress -> {
                        notificationManager.notify(NOTIFICATION_ID,getNotification(res.percent))
                        Log.d("FVDW", "progress VM: ${res.percent}")
                    }

                    is BaseMediaUploadRepository.VideoDownloadResult.Complete -> {
                        MediaUtils.saveVideoToGallery(applicationContext, res.file)
                    }

                    is BaseMediaUploadRepository.VideoDownloadResult.Error -> throw res.error
                }
            }
            setProgress(workDataOf(Progress to 100))
            Log.d("FVDW", "Progress: 100%")
            notificationManager.cancel(NOTIFICATION_ID)
            return Result.success()
        } catch (throwable: Throwable) {
            // clean up and log
            Log.d("FVDW", "Download job cancelled")
            return Result.failure()
        }
    }

    override suspend fun getForegroundInfo() = createForegroundInfo()

    // Create a Notification channel if necessary
    val channel = FlashatFirebaseMessagingService.createChannel(notificationManager, "DOWNLOAD", "Flash downloads", NotificationManager.IMPORTANCE_LOW)

    private fun createForegroundInfo(): ForegroundInfo {
        Log.d("FVDW", "createForegroundInfo")
        val notification = getNotification()

        return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            ForegroundInfo(NOTIFICATION_ID, notification)
        } else ForegroundInfo(NOTIFICATION_ID, notification, FOREGROUND_SERVICE_TYPE_DATA_SYNC)
    }

    private fun getNotification(progress: Int = 0): Notification {
        return NotificationCompat.Builder(applicationContext, channel.id)
                .setContentTitle(applicationContext.getString(R.string.flash_downloading_title))
                .setSilent(true)
                .setSmallIcon(R.drawable.ic_notification_small)
                .setOngoing(true)
            .setProgress(100,progress,progress==0)
            .build()
    }
}