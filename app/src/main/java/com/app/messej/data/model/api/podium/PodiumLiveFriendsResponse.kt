package com.app.messej.data.model.api.podium

import com.google.gson.annotations.SerializedName

data class PodiumLiveFriendsResponse(
    @SerializedName("data") val data: ArrayList<PodiumFriend> = arrayListOf(),
    @SerializedName("has_next") val hasNext: Boolean?=null,
    @SerializedName("live_friends_count") val liveFriendsCount: Int?=null,
    @SerializedName("page") val page: Int?=null,
    @SerializedName("per_page") val perPage: Int?=null
)