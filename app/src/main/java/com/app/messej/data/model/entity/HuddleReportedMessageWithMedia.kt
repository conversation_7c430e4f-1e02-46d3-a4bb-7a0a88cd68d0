package com.app.messej.data.model.entity

import androidx.room.Embedded
import androidx.room.Relation
import com.app.messej.data.model.AbstractChatMessageWithMedia

data class HuddleReportedMessageWithMedia (
    @Embedded
    override val message: HuddleReportedMessage,

    @Relation(
        parentColumn = HuddleReportedMessage.COLUMN_MESSAGE_ID,
        entityColumn = OfflineMedia.COLUMN_MESSAGE_ID
    )
    override var offlineMedia: OfflineMedia? = null,

    @Relation(
        parentColumn = HuddleReportedMessage.COLUMN_SENDER,
        entityColumn = NickName.COLUMN_USER_ID
    )
    val nickName: NickName? = null
): AbstractChatMessageWithMedia() {

    val senderNickNameOrName: String
        get() {
            nickName?.nickName?.let {
                if(it.isNotEmpty()) {
                    return it
                }
            }
            return message.senderDetails?.name ?: ""
        }
}