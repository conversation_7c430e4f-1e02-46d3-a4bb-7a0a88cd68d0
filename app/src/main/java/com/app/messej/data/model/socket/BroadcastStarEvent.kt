package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.BroadcastMode
import com.google.gson.annotations.SerializedName

data class BroadcastStarEvent(
    @SerializedName("broadcaster") val broadcaster: Int,
    @SerializedName("broadcast_type") val broadcastType: BroadcastMode,
    @SerializedName("id") val messageId: String,
    @SerializedName("starred") val starred: <PERSON><PERSON><PERSON>
)