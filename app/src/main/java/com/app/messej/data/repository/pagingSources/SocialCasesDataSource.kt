package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.SocialAffairAPIService
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.api.socialAffairs.SocialCasesListResponse
import com.app.messej.data.model.enums.SocialActiveCaseMainTab
import com.app.messej.data.model.enums.SocialActiveTab
import com.app.messej.data.model.enums.SocialCaseFilter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SocialCasesDataSource(
    private val apiService: SocialAffairAPIService,
    private val mainTab: SocialActiveCaseMainTab? = null,
    private val tab: SocialActiveTab? = null,
    private val subTab: SocialCaseFilter? = null,
    private val countCallBack: (SocialCasesListResponse?) -> Unit
) : PagingSource<Int, SocialCaseInfo>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, SocialCaseInfo>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, SocialCaseInfo> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = apiService.getSocialCases(page = currentPage, mainTab = mainTab?.serializedName(), tab = tab?.serializedName(), subTab = subTab?.serializedName())
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val dataWithoutCases = data.copy(cases = null)
                countCallBack(dataWithoutCases)
                val nextKey = if (data.hasNext == true) currentPage + 1 else null
                LoadResult.Page(
                    data = data.cases ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}