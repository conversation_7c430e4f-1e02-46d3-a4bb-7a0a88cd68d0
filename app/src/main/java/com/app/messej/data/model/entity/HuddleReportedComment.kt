package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractComment
import com.app.messej.data.model.ActivityMeta
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.HuddleReportedComment.Companion.COLUMN_HUDDLE_ID
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = EntityDescriptions.TABLE_HUDDLE_REPORTED_COMMENTS,
    indices = [
        Index(COLUMN_HUDDLE_ID, unique = false)
    ]
)
@TypeConverters(
    SenderDetails.Converter::class,
    ActivityMeta.Converter::class,
    ReplyTo.Converter::class,
    MediaMeta.Converter::class
)
data class HuddleReportedComment (

    @SerializedName("comment_id")           @ColumnInfo(name = COLUMN_COMMENT_ID) @PrimaryKey(autoGenerate = false) override val commentId: String,
    @SerializedName("huddle_id")            @ColumnInfo(name = COLUMN_HUDDLE_ID)                               override val huddleId: Int,
    @SerializedName("post_id")              @ColumnInfo(name = "post_id")                                      override val messageId: String,
    @SerializedName("time_created")         @ColumnInfo(name = COLUMN_MESSAGE_CREATED)                         override val created: String,
    @SerializedName("comment")              @ColumnInfo(name = "comment")                                      override val comment: String?,
    @SerializedName("media")                @ColumnInfo(name = "media")                                        override val media: String?,
    @SerializedName("sender_name")          @ColumnInfo(name = "sender_name")                                  val senderName: String="",

    @SerializedName("report_id")            @ColumnInfo(name = "report_id")                                    val reportId: Int,

    @SerializedName("reports_count")        @ColumnInfo(name = "reports_count")                                val reportsCount: Int=0,
    @SerializedName("status")               @ColumnInfo(name = "status")                                       val reportStatus: ReportStatus? = null,
    @SerializedName("time_updated")         @ColumnInfo(name = "time_updated")                                 var updatedTime      : String?    = null,

    @SerializedName("deletedUser")          @ColumnInfo(name = "deletedUser")                                  val userDeleted: Boolean=false,
    @SerializedName("sender_membership")    @ColumnInfo(name = "sender_membership")                            val senderMembership: UserType? = null,
//    @SerializedName("sender_username")      @ColumnInfo(name = "sender_username")                              val senderUsername: String? = null,
    @SerializedName("sender_id")            @ColumnInfo(name = COLUMN_SENDER)                         override val senderId: Int,
    @SerializedName("thumbnail")            @ColumnInfo(name = "thumbnail")                                    val thumbnail: String? = null,
//    @SerializedName("verified")             @ColumnInfo(name = "verified")                                     var verified: Boolean = false,
): AbstractComment() {

    companion object {
        const val COLUMN_COMMENT_ID = "message_id"
        const val COLUMN_HUDDLE_ID = "huddle_id"
        const val COLUMN_MESSAGE_CREATED = "created"
        const val COLUMN_SENDER = "sender_id"
    }

    override val senderDetails: SenderDetails
        get() = SenderDetails(
            id = senderId,
            _username = "",
            name = senderName,
            deletedAccount = userDeleted,
            premium = senderMembership== UserType.PREMIUM,
            role = null,
            thumbnail = thumbnail
        )

    val deleted: Boolean
        get() = reportStatus != ReportStatus.PENDING

    val hasText: Boolean
        get() = !comment.isNullOrBlank()

    enum class ReportStatus {
        @SerializedName("pending") PENDING,
        @SerializedName("admin_deleted") ADMIN_DELETED,
        @SerializedName("admin_deleted_user_blocked") ADMIN_DELETED_USER_BLOCKED,
        @SerializedName("manager_deleted") MANAGER_DELETED,
        @SerializedName("manager_deleted_user_blocked") MANAGER_DELETED_USER_BLOCKED,
        @SerializedName("user_deleted") USER_DELETED
    }
}
