package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

@Entity(
    tableName = EntityDescriptions.TABLE_PRIVATE_CHATS,
    indices = [
        Index(PrivateChat.COLUMN_CHAT_TYPE, unique = false),
        Index(PrivateChat.COLUMN_UNREAD_COUNT, unique = false),
        Index(PrivateChat.COLUMN_UPDATED, unique = false),
        Index(PrivateChat.COLUMN_PRIVATE_MESSAGE_TAB, unique = false),
    ]
)
@TypeConverters(
    PrivateChatMessage.Converter::class,
    SenderDetails.Converter::class
)
/**
 * Represents a private message
 * @property updated zoned datetime of last activity. example: 2023-01-04T08:16:02Z
 */
data class PrivateChat(
    @SerializedName("id"              ) @ColumnInfo(name = COLUMN_ID           ) @PrimaryKey(autoGenerate = false) val id: String,

    @SerializedName("last_message"    ) @ColumnInfo(name = COLUMN_LAST_MESSAGE )         var lastMessageInternal     : PrivateChatMessage?      = null,
    @SerializedName("last_message_id" ) @ColumnInfo(name = "last_message_id"   )         val lastMessageId    : String?           = null,

    @SerializedName("sender"          ) @ColumnInfo(name = "sender"            )         val sender           : Int,
    @SerializedName("receiver"        ) @ColumnInfo(name = "receiver"          )         val receiver         : Int,

    @SerializedName("receiver_data"   ) @ColumnInfo(name = "receiver_data"     )         val receiverDetails  : SenderDetails,
    @SerializedName("room_id"         ) @ColumnInfo(name = "room_id"           )         val roomId           : String,
    @SerializedName("room_name"       ) @ColumnInfo(name = "room_name"         )         val roomName         : String?           = null,
    @SerializedName("status"          ) @ColumnInfo(name = "status"            )         val status           : String?           = null,
    @SerializedName("type"            ) @ColumnInfo(name = COLUMN_CHAT_TYPE    )         val type             : ChatType          = ChatType.PRIVATE,
    @SerializedName("unread_count"    ) @ColumnInfo(name = COLUMN_UNREAD_COUNT )         val unreadCount      : Int               = 0,

    @SerializedName("updated"         ) @ColumnInfo(name = COLUMN_UPDATED      )         var updated          : String?           = null,

    @SerializedName("is_ignored"      ) @ColumnInfo(name = "is_ignored"        )         var isIgnored        : Boolean?          = false,

    @SerializedName("followed_by_each") @ColumnInfo(name = "followed_by_each"  )         var followedByMe   : Boolean?          = null,

    @ColumnInfo(name = COLUMN_PRIVATE_MESSAGE_TAB) var privateMessageTab: PrivateMessageTabType? = null

) {

    companion object {
        const val COLUMN_ID = "id"
        const val COLUMN_UPDATED = "updated"
        const val COLUMN_UNREAD_COUNT = "unread_count"
        const val COLUMN_LAST_MESSAGE = "last_message"
        const val COLUMN_CHAT_TYPE = "type"
        const val COLUMN_PRIVATE_MESSAGE_TAB = "private_message_tab"
    }

    enum class ChatType {
        @SerializedName(value = "request", alternate = ["REQUEST","Request"]) REQUEST,
        @SerializedName(value = "private", alternate = ["PRIVATE","Private"]) PRIVATE;

        override fun toString(): String {
            return javaClass
                .getField(name)
                .getAnnotation(SerializedName::class.java)
                ?.value ?: ""
        }
    }

    enum class PrivateMessageTabType {
        BUDDIES,
        INTRUDER;
    }

    var parsedUpdated: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(updated)
        set(value) {
            value?:return
            updated = DateTimeUtils.formatISOInstant(value)
        }

    var lastMessage: PrivateChatMessage?
        get() = lastMessageInternal
        set(value) {
            lastMessageInternal = value
            value?:return
            parsedUpdated = value.parsedCreatedTime
        }

    val hasLastMessage: Boolean
        get() = lastMessageInternal!=null

    val hasUnread: Boolean
        get() = unreadCount > 0

    val lastMessageIsOutgoing: Boolean
        get() = sender == lastMessage?.sender

    val isAdminMessage: Boolean
        get() = receiverDetails.id == 1
}
