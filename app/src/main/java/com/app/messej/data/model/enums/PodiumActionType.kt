package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PodiumActionType {
    // User Side Actions
    @SerializedName("confirm") CONFIRM_PODIUM_ADMIN_INVITE,
    @SerializedName("decline") DECLINE_PODIUM_ADMIN_INVITE;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}