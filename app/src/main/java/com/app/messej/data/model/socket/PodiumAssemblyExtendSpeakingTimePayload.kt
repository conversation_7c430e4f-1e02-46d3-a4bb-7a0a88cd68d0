package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.AssemblySpeakingStatus
import com.google.gson.annotations.SerializedName

data class PodiumAssemblyExtendSpeakingTimePayload(
    @SerializedName("podium_id") val podiumId: String?,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("speaking_time_limit") val speakingTimeLimit: Int? = null,
    @SerializedName("speaking_start_time") val speakingStartTime: Double? = null,
    @SerializedName("speaking_order") val speakingOrder: Int? = null,
    @SerializedName("speaking_status") val speakingStatus: AssemblySpeakingStatus? = null,
) : SocketEventPayload()
