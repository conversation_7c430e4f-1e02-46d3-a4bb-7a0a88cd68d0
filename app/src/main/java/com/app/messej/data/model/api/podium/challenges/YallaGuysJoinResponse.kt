package com.app.messej.data.model.api.podium.challenges

import com.google.gson.annotations.SerializedName

data class YallaGuysJoinResponse(
    @SerializedName("paused") val paused: <PERSON><PERSON><PERSON>,
    @SerializedName("status") val status: Ya<PERSON>JoinStatus
) {
    enum class YallaJoinStatus {
        @SerializedName("JOINED") JOINED,
        @SerializedName("QUEUED") QUEUED,
        @SerializedName("WAITING") WAITING,
        @SerializedName("STARTED") STARTED,
    }
}
