package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.api.socialAffairs.SocialAffairUser.Companion.dummySocialUser
import com.google.gson.annotations.SerializedName

data class HonoursResponse(
    @SerializedName("has_next") val hasNext: Boolean? = null,
    @SerializedName("data") val honoursList: List<Honour>? = null
) {

    data class Honour(
        @SerializedName("user_details") val userDetail : SocialAffairUser?,
        @SerializedName("vote_count") val voteCount : Int?,
        @SerializedName("total_donated") val totalDonated : Double?
    )

    companion object {
        val testSingleHonourDetail = Honour(
            userDetail = dummySocialUser,
            voteCount = 10,
            totalDonated = 6.0
        )
    }
}