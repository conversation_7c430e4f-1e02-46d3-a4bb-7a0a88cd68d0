package com.app.messej.data.model.api.huddles

import com.google.gson.annotations.SerializedName

data class UpdateHuddleRequest(
    @SerializedName("name") val name: String?,
    @SerializedName("about") val about: String?,
    @SerializedName("category_id") val categoryId: Int?,
    @SerializedName("request_to_join") val requestToJoin: Boolean?,
    @SerializedName("isPrivate") val isPrivate: Boolean?,
    @SerializedName("participant_share") val participantShare: Boolean?,
    @SerializedName("remove_group_photo") val isRemove: Boolean?,
    @SerializedName("language") val huddleLanguage:String?,
    @SerializedName("use_user_profile_photo") val useUserProfilePhoto:Boolean
)
