package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.BusinessAPIService
import com.app.messej.data.model.BuyFlaxRates
import com.app.messej.data.model.enums.FlixPurchaseTypesTab
import com.app.messej.data.model.enums.PurchaseItem

private const val STARTING_KEY = 1

class DealsBuyFlaxPurchaseHistoryDataSource(private val api: BusinessAPIService, private val purchaseItem: PurchaseItem,private val flixTabType: FlixPurchaseTypesTab?) : PagingSource<Int, BuyFlaxRates>() {


    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, BuyFlaxRates> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = when (purchaseItem) {
                PurchaseItem.BUY_COIN -> flixTabType?.let { flixType ->
                    api.getCoinPurchaseHistory(page = currentPage, limit = 50, flixType)
                }
                else -> flixTabType?.let { flixType ->
                    api.getFlixPurchaseHistory(page = currentPage, limit = 50, flixType)
                }
            }

            val responseData = mutableListOf<BuyFlaxRates>()
            val result = response?.body()?.result
            val data = result?.purchases ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (!response?.body()?.result!!.nextPage) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.purchases, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("TransactionResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, BuyFlaxRates>): Int? {
        return null
    }
}