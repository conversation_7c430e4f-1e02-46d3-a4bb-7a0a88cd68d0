package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.TypeConverters
import androidx.room.Update
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.entity.PublicHuddleInterventions
import com.app.messej.data.model.entity.PublicHuddleWithInterventions
import com.app.messej.data.model.entity.Sticker
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.room.EntityDescriptions
import kotlinx.coroutines.flow.Flow

@Dao
@TypeConverters(
    HuddleChatMessage.Converter::class,
    SenderDetails.Converter::class
)
abstract class HuddleDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertHuddle(huddle: PublicHuddle): Long

    @Update
    abstract suspend fun updateHuddle(huddle: PublicHuddle): Int

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertHuddleInterventions(intervention: PublicHuddleInterventions): Long

    @Update
    abstract suspend fun updateHuddleInterventions(intervention: PublicHuddleInterventions): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract suspend fun deleteHuddle(id: Int): Int

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertHuddles(huddleList: List<PublicHuddle>): List<Long>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_HUDDLE_TYPE} = :type AND ${PublicHuddle.COLUMN_HUDDLE_INVOLVEMENT} = :involvement")
    abstract suspend fun deleteAllHuddles(type: HuddleType, involvement: HuddleInvolvement)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_HUDDLE_TYPE} = :type")
    abstract suspend fun deleteAllHuddles(type: HuddleType)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_HUDDLE_TYPE} = :type AND ${PublicHuddle.COLUMN_HUDDLE_INVOLVEMENT} = :showType ORDER BY ${PublicHuddle.COLUMN_TRIBE} DESC,${PublicHuddle.COLUMN_PINNED} DESC, ${PublicHuddle.COLUMN_ACTIVITY} DESC")
    abstract fun huddlesPagingSource(type: HuddleType, showType: HuddleInvolvement): PagingSource<Int, PublicHuddle>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_HUDDLE_TYPE} = :type ORDER BY ${PublicHuddle.COLUMN_PINNED} DESC, ${PublicHuddle.COLUMN_ACTIVITY} DESC")
    abstract fun huddlesPagingSource(type: HuddleType): PagingSource<Int, PublicHuddle>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract fun getHuddleFlow(id: Int): Flow<PublicHuddle?>

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLES} SET ${PublicHuddle.COLUMN_NAME} = :newName WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract suspend fun updateHuddleName(id: Int, newName: String)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract fun getHuddleWithInterventionsFlow(id: Int): Flow<PublicHuddleWithInterventions?>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract fun getHuddle(id: Int): PublicHuddle?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract fun getHuddleWithInterventions(id: Int): PublicHuddleWithInterventions?

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLES} SET ${PublicHuddle.COLUMN_LAST_READ} = :messageId WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract fun setHuddleLastReadMessage(id: Int, messageId: String)

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLES} SET ${PublicHuddle.COLUMN_ACTIVITY} = :act WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract fun setHuddleActivity(id: Int, act: String)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_PINNED} = 1 AND ${PublicHuddle.COLUMN_HUDDLE_TYPE} = :type AND ${PublicHuddle.COLUMN_HUDDLE_INVOLVEMENT} = :showType")
    abstract fun getPinnedHuddles(type: HuddleType, showType: HuddleInvolvement): List<PublicHuddle>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_PINNED} = 1 AND ${PublicHuddle.COLUMN_HUDDLE_TYPE} = :type")
    abstract fun getPinnedHuddles(type: HuddleType): List<PublicHuddle>

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLES} SET ${PublicHuddle.COLUMN_PINNED} = :pin WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract fun setHuddlePinned(id: Int, pin: Boolean)

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLES} SET ${PublicHuddle.COLUMN_MUTED} = :mute WHERE ${PublicHuddle.COLUMN_ID} = :id")
    abstract fun setHuddleMuted(id: Int, mute: Boolean)

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLES} SET ${PublicHuddle.COLUMN_ONLINE} = :count WHERE id = :id")
    abstract fun updateOnlineCount(id: Int, count: Int)

    @Query("SELECT count(*) FROM ${EntityDescriptions.TABLE_HUDDLES} WHERE ${PublicHuddle.COLUMN_UNREAD_COUNT} > 0 AND ${PublicHuddle.COLUMN_HUDDLE_TYPE} = :type")
    abstract fun countUnreadHuddles(type: HuddleType): LiveData<Int>

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLES} SET ${PublicHuddle.COLUMN_UNREAD_COUNT} = :count WHERE id = :id")
    abstract fun updateUnreadCount(id: Int, count: Int)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertStickers(stickerList: List<Sticker>)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_STICKERS} ORDER BY ${Sticker.COLUMN_EMOJI_ID}")
    abstract fun getStickers(): PagingSource<Int, Sticker>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_STICKERS}")
    abstract suspend fun deleteAllStickers()

}