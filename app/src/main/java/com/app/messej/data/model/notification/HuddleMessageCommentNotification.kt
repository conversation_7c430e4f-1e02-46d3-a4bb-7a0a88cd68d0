package com.app.messej.data.model.notification

import com.app.messej.data.model.AbstractHuddle
import com.google.gson.annotations.SerializedName

data class HuddleMessageCommentNotification(
    @SerializedName("huddle_id"         ) var huddleId         : Int,
    @SerializedName("thumbnail"         ) var thumbnail        : String? = null,
    @SerializedName("user_status"       ) var status           : AbstractHuddle.HuddleStatus,
    @SerializedName("private"           ) var private          : <PERSON><PERSON><PERSON>,
    @SerializedName("message_id"        ) var messageId        : String,
    @SerializedName("notification_id"   ) var notificationId   : Int    = 0,
    @SerializedName("message"           ) var message          : String = "",
    @SerializedName("comment_id"        ) var commentId        : String,
    @SerializedName("sender_id"         ) var senderId         : Int,
    @SerializedName("name"              ) var name             : String,
)