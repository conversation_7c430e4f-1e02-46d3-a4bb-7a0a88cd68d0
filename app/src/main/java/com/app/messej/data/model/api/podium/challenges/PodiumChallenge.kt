package com.app.messej.data.model.api.podium.challenges

import android.content.res.Resources
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.databinding.BindingAdapter
import androidx.room.Ignore
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.Duration
import java.time.ZonedDateTime
import kotlin.math.max

data class PodiumChallenge(
    @SerializedName("contributor_type"       ) val contributorType     : ChallengeContributionType? = null,
    @SerializedName("contributors"           ) val contributors        : List<ChallengeUser> = arrayListOf(),
    @SerializedName("waiting_participants"   ) val waitingParticipants : List<ChallengeUser>? = arrayListOf(),
    @SerializedName("duration"               ) val duration            : Long?                    = null,
    @SerializedName("facilitator"            ) val facilitator         : ChallengeUser?,
    @SerializedName("game_type"              ) val challengeType       : ChallengeType,
    @SerializedName("id"                     ) val challengeId         : String,
    @SerializedName("podium_id"              ) val podiumId            : String,
    @SerializedName("podium_live_session_id" ) val podiumLiveSessionId : String?                 = null,
    @SerializedName("prize"                  ) val prize               : Double?                    = null,
    @SerializedName("screen"                 ) val screen              : ChallengeScreen?        = null,
    @SerializedName("status"                 ) val _status             : ChallengeStatus?,
    @SerializedName("time_start"             ) val startTime           : String?                 = null,
    @SerializedName("time_end"               ) val endTime             : String?                 = null,
    @SerializedName("end_timestamp_utc"      ) var endTimeUTC          : Long?                 = null,
    @SerializedName("top_supporters"         ) val topSupporters       : List<ChallengeUser>? = null,
    @SerializedName("participants"           ) val _participantScores   : List<PodiumChallengeScore>? = null,

    @SerializedName("time_contributor_requested" ) var contributorRequestedTime  : String?   = null,
    @SerializedName("time_invited_participants"  ) val invitedParticipantsTime   : String?   = null,
    // denotes the id for which an accept/decline/request event was triggered
    @Ignore @SerializedName("contributor_id"     ) val contributorId             : Int?   = null,
    @Ignore @SerializedName("podium_name"        ) val podiumName                : String?   = null,

    //For ConFour
    @SerializedName("con4_data"                  ) var conFourData               : ConFourData?   = null,

    //For Penalty
    @SerializedName("penalty_data"               ) var penaltyData               : PenaltyData?   = null,
    // For Box Challenge
    @SerializedName("box_data"                   ) var boxData                   : BoxData?   = null,
    //For Knowledge Race
    @SerializedName("knowledge_data"             ) var knowledgeRaceData         : KnowledgeRaceData?   = null,

    //For Maidan
    @SerializedName("challenge_fee"              ) val challengeFee             : Double?                    = null,
    @SerializedName(value = "competitor_fee", alternate = ["contributor_fee"]    ) val competitorFee            : Double?                    = null,
    @SerializedName("challenge_exit_fee"         ) val challengeExitFee         : Double?                    = null,
    @SerializedName("parent_podium_id"           ) val parentPodiumId           : String? = null,  // Only used in the invite socket event
    @SerializedName("is_yalla"                   ) val isYalla                  : Boolean? = null
    ) {

    companion object {

        private const val CONTRIBUTOR_REQUEST_TIMEOUT_SECONDS = 30L

        private const val MIN_PARTICIPANTS_GIFT = 2
        private const val MAX_PARTICIPANTS_GIFT = 9

        private const val MIN_PARTICIPANTS_LIKE = 2
        private const val MAX_PARTICIPANTS_LIKE = 8

        private const val MIN_PARTICIPANTS_FLAG = 2
        private const val MAX_PARTICIPANTS_FLAG = 9

        private const val MIN_PARTICIPANTS_CONFOUR = 2
        private const val MAX_PARTICIPANTS_CONFOUR = 2

        private const val MIN_PARTICIPANTS_PENALTY = 2
        private const val MAX_PARTICIPANTS_PENALTY = 2

        private const val MIN_PARTICIPANTS_MAIDAN = 2
        private const val MAX_PARTICIPANTS_MAIDAN = 2

        private const val MIN_PARTICIPANTS_BOXES = 2
        private const val MAX_PARTICIPANTS_BOXES = 2

        private const val MIN_PARTICIPANTS_KNOWLEDGE = 2
        private const val MAX_PARTICIPANTS_KNOWLEDGE = 6

        const val MAX_ADMINS = 5

        fun minParticipants(ct: ChallengeType?): Int = when(ct) {
            null -> 0
            ChallengeType.LIKES -> MIN_PARTICIPANTS_LIKE
            ChallengeType.GIFTS -> MIN_PARTICIPANTS_GIFT
            ChallengeType.FLAGS -> MIN_PARTICIPANTS_FLAG
            ChallengeType.CONFOUR -> MIN_PARTICIPANTS_CONFOUR
            ChallengeType.PENALTY -> MIN_PARTICIPANTS_PENALTY
            ChallengeType.MAIDAN -> MIN_PARTICIPANTS_MAIDAN
            ChallengeType.BOXES -> MIN_PARTICIPANTS_BOXES
            ChallengeType.KNOWLEDGE -> MIN_PARTICIPANTS_KNOWLEDGE
        }

        fun maxParticipants(ct: ChallengeType?): Int = when(ct) {
            null -> 0
            ChallengeType.LIKES -> MAX_PARTICIPANTS_LIKE
            ChallengeType.GIFTS -> MAX_PARTICIPANTS_GIFT
            ChallengeType.FLAGS -> MAX_PARTICIPANTS_FLAG
            ChallengeType.CONFOUR -> MAX_PARTICIPANTS_CONFOUR
            ChallengeType.PENALTY -> MAX_PARTICIPANTS_PENALTY
            ChallengeType.MAIDAN -> MAX_PARTICIPANTS_MAIDAN
            ChallengeType.BOXES -> MAX_PARTICIPANTS_BOXES
            ChallengeType.KNOWLEDGE -> MAX_PARTICIPANTS_KNOWLEDGE
        }
    }

    val participantScores: List<PodiumChallengeScore>
        get() = _participantScores.orEmpty()

    val parsedStartTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(startTime)

    val parsedEndTime: ZonedDateTime?
        get() {
            val seconds = endTimeUTC?:DateTimeUtils.parseZonedDateTimeWithoutZ(endTime)?.toEpochSecond()
            val timeInMilliSeconds = seconds?.times(1000L)
            return DateTimeUtils.parseMillisToDateTime(timeInMilliSeconds?:return null)
        }

    val status: ChallengeStatus
        get() = if (_status==ChallengeStatus.LIVE && gameOver) ChallengeStatus.GAME_OVER else _status?: ChallengeStatus.SETUP

    val hasStarted: Boolean
        get() {
            return (_status == ChallengeStatus.LIVE && parsedStartTime?.isBefore(ZonedDateTime.now()) == true) || _status == ChallengeStatus.GAME_OVER
        }

    val gameOver: Boolean
        get() = when(_status) {
            ChallengeStatus.SETUP -> false
            ChallengeStatus.LIVE -> parsedEndTime?.isBefore(ZonedDateTime.now())==true
            else -> true
        }


    val running: Boolean
        get() = hasStarted && !gameOver

    val countDownRemaining: Duration?
        get() = DateTimeUtils.durationFromNowToFuture(parsedStartTime)

    val liveTimeRemaining: Duration?
        get() = DateTimeUtils.durationFromNowToFuture(parsedEndTime)

    val liveTimeElapsed: Duration?
        get() = if(running) DateTimeUtils.durationToNowFromPast(parsedStartTime)
    else null

    val challengeStep : ChallengeStep
        get() {
            return if (hasStarted || gameOver) {
                ChallengeStep.STARTED
            } else if (contributorType == ChallengeContributionType.FREE) {
                ChallengeStep.PARTICIPANTS
            } else if (contributors.isNotEmpty()) {
                if (allContributorsAccepted) ChallengeStep.PARTICIPANTS
                else ChallengeStep.CONTRIBUTOR
            } else if (duration != null) {
                ChallengeStep.TIMER
            } else ChallengeStep.FACILITATOR
        }

    val allContributorsAccepted: Boolean
        get() = contributors.all { it.requestAccepted==true } && contributors.isNotEmpty()

    val anyContributorAccepted: Boolean
        get() = contributors.any { it.requestAccepted == true } && contributors.isNotEmpty()

    private val parsedContributorRequestedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(contributorRequestedTime)

    val contributorRequestTimeOut: ZonedDateTime?
        get() = parsedContributorRequestedTime?.plusSeconds(CONTRIBUTOR_REQUEST_TIMEOUT_SECONDS)

    val contributorRequestedTimeRemaining: Duration
        get() {
            return (DateTimeUtils.durationFromNowToFuture(contributorRequestTimeOut))?: Duration.ZERO
        }

    val hasContributorRequestTimedOut: Boolean?
        get() = contributorRequestTimeOut?.isBefore(ZonedDateTime.now())

    val allWaitingParticipantsAccepted: Boolean
        get() = waitingParticipants.orEmpty().isNotEmpty() && waitingParticipants.orEmpty().all { it.requestAccepted==true }

    private val parsedInvitedParticipantsTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(invitedParticipantsTime)

    val invitedParticipantsTimeOut : ZonedDateTime?
        get() = parsedInvitedParticipantsTime?.plusSeconds(CONTRIBUTOR_REQUEST_TIMEOUT_SECONDS)

    val participantsInvitedTimeRemaining: Long
        get() {
            val dur = (DateTimeUtils.durationToNowFromPast(parsedInvitedParticipantsTime))?.seconds?: 30L
            return max(0,30-dur)
        }

    val minParticipantsCountReached: Boolean
        get() {
            val participants = participantScores ?: return false
            return participants.size >= minParticipants(challengeType)
        }


    fun formattedChallengeDuration(res: Resources): String {
        val dur = duration?: return ""
        return DateTimeUtils.formatDurationToHours(dur,DateTimeUtils.getResourceProvider(res))
    }

    enum class ChallengeStatus {
        @SerializedName("SETUP") SETUP,
        @SerializedName("LIVE") LIVE,
        @SerializedName("GAME_OVER") GAME_OVER,
        @SerializedName("END") ENDED,
    }
    enum class ChallengeUserStatus {
        @SerializedName("Accepted") ACCEPTED,
        @SerializedName("Requested") REQUESTED,
        @SerializedName("Rejected") REJECTED
    }

    enum class ChallengeStep {
        INITIATED, FACILITATOR, TIMER, CONTRIBUTOR, PARTICIPANTS, STARTED;

        fun comesBefore(step: ChallengeStep?): Boolean {
            step?: return false
            return this.ordinal<step.ordinal
        }
        fun comesAfter(step: ChallengeStep?): Boolean {
            step?: return false
            return this.ordinal>step.ordinal
        }

        companion object {
            @JvmStatic
            @BindingAdapter("currentStep","setImageFor")
            fun setDotByComparingCurrentStep(view: AppCompatImageView, current: ChallengeStep?, compareTo: ChallengeStep) { // This methods should not have any return type, = declaration would make it return that object declaration.
                if(current!=null) {
                    var res = if (current == compareTo) R.drawable.ic_challenge_timeline_circle_current
                    else if (current.comesAfter(compareTo)) R.drawable.ic_challenge_timeline_circle
                    else R.drawable.ic_challenge_timeline_circle_not_started

                    view.setImageResource(res)
                }
            }

            @JvmStatic
            @BindingAdapter("currentStep","setLineFor")
            fun setDotByComparingCurrentStep(view: View, current: ChallengeStep?, compareTo: ChallengeStep) { // This methods should not have any return type, = declaration would make it return that object declaration.
                if (current!= null) {
                    if (current.comesBefore(compareTo) || current == compareTo) {
                        view.setBackgroundResource(R.drawable.bg_podium_challenge_dotted_line_vertical)
                    } else {
                        view.setBackgroundResource(R.color.colorPrimary)
                    }
                }
            }
        }
    }

    enum class ChallengeScreen {
        @SerializedName("0") SELECT_FACILITATOR,
        @SerializedName("1") SET_TIMER,
        @SerializedName("2") SELECT_CONTRIBUTION,
        @SerializedName("3") INVITE_PARTICIPANTS
    }

    enum class ChallengeScreenStatus {
        WAITING, NORMAL
    }

    val challengeInvitationStatus : ChallengeScreenStatus
        get() {
            if (screen == ChallengeScreen.INVITE_PARTICIPANTS && participantsInvitedTimeRemaining > 0 && (challengeType == ChallengeType.CONFOUR || challengeType == ChallengeType.PENALTY))
                return ChallengeScreenStatus.WAITING
            return ChallengeScreenStatus.NORMAL
        }

    data class ChallengeUser (
        @SerializedName("id"                ) override val id              : Int,
        @SerializedName("name"              ) override var name            : String,
        @SerializedName("thumbnail"         ) override val thumbnail       : String?,
        @SerializedName("username"          ) override val username        : String,
        @SerializedName("verified"          ) override val verified        : Boolean,
        @SerializedName("citizenship"       ) override val citizenship     : UserCitizenship?,
        @SerializedName("membership"        ) override val membership      : UserType,
        @SerializedName("appointed_by"      ) val          appointedBy     : Int,
        @SerializedName("coins_spent"       ) val          coinsSpent      : Double?               = null,
        @SerializedName("country_code"      ) override val countryCode     : String?            = null,
        @SerializedName("profile_url"       ) val          profileUrl      : String?            = null,
        @SerializedName("request_accepted"  ) var          requestAccepted : Boolean?           = null,
        @SerializedName("self_requested"    ) val          selfRequested   : String?            = null,
        @SerializedName("time_request_sent" ) val          timeRequestSent : String?            = null,
        @SerializedName("time_invited"      ) val          timeInvited     : String?            = null,
        @SerializedName("time_responded"    ) val          timeResponded   : String?            = null,
        @SerializedName("score"             ) val          score           : String?            = null,

        @SerializedName("contributor_level" ) val          contributorLevel: String?           = null,
    ): AbstractUser() {

        //only used for Maidan
        var supporterRank: Int? = null

        enum class InvitationStatus {
            @SerializedName("Invited")PENDING,
            @SerializedName("Accepted")ACCEPTED,
            @SerializedName("Rejected")REJECTED,
            @SerializedName("Expired")EXPIRED;

            override fun toString(): String {
                return javaClass
                    .getField(name)
                    .getAnnotation(SerializedName::class.java)
                    ?.value ?: ""
            }
        }
        val parsedRequestedTime: ZonedDateTime?
            get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeRequestSent)

        val parsedInvitedTime : ZonedDateTime?
            get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeInvited)

        val isExpired : Boolean
            get() = timeRemaining<=0L

        val inviteExpired : Boolean
            get() = invitedTimeRemaining<=0L

        val timeRemaining : Long
            get() {
                val dur = (DateTimeUtils.durationToNowFromPast(parsedRequestedTime))?.seconds?: 30L
                return max(0,30-dur)
            }

        val invitedTimeRemaining : Long
            get() {
                val dur = (DateTimeUtils.durationToNowFromPast(parsedInvitedTime))?.seconds?: 30L
                return max(0, 30-dur)
            }

        val invitationStatus : InvitationStatus
            get() {
                return if (!inviteExpired && requestAccepted != true) InvitationStatus.PENDING
                else if (requestAccepted == true) InvitationStatus.ACCEPTED
                else if(requestAccepted == false) InvitationStatus.REJECTED
                else if (isExpired) InvitationStatus.EXPIRED
                else InvitationStatus.PENDING
            }

        val rejectedOrExpired : Boolean
            get() = invitationStatus == InvitationStatus.REJECTED || invitationStatus == InvitationStatus.EXPIRED
    }
}
