package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class LegalAffairTabs {
    @SerializedName("violations") Violations,
    @SerializedName("ban") Bans,
    @SerializedName("reportings") Reporting,
    @SerializedName("hidden") Hidden;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)?.value ?: ""
    }
}