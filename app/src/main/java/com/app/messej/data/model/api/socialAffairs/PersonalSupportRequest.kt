package com.app.messej.data.model.api.socialAffairs

 import com.google.gson.annotations.SerializedName

data class PersonalSupportRequest(
    @SerializedName("status") val status: String? = null,
    @SerializedName("amount_requested") var amountRequested: String? = null,
    @SerializedName("case_title") val caseTitle: String? = null,
    @SerializedName("case_description") val caseDetails: String? = null,
    @SerializedName("proof_files") var proofFiles: List<String>? = null
) {
    companion object {
        val personalSupportBasicData = PersonalSupportRequest()
    }
}