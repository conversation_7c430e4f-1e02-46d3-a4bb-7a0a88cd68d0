package com.app.messej.data.model.api.podium

import com.google.gson.annotations.SerializedName

data class PodiumRecordsResponse(
    @SerializedName("current_page" ) val currentPage : Int,
    @SerializedName("has_next"     ) val hasNext     : <PERSON><PERSON><PERSON>,
    @SerializedName("per_page"     ) val perPage     : Int?               = null,
    @SerializedName("records"      ) val records     : List<PodiumRecord>,
    @SerializedName("total_items"  ) val totalItems  : Int,
    @SerializedName("total_pages"  ) val totalPages  : Int
)
