package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName


data class IpApiResponse(
    @SerializedName("ip") var ip: String,
    @SerializedName("country_name") val country: String,
    @SerializedName("city") val city: String,
    @SerializedName("region") val region: String,
    @SerializedName("latitude") val latitude: Double,
    @SerializedName("longitude") val longitude: Double,
    @SerializedName("country_code")val countryCode: String?=null
)
