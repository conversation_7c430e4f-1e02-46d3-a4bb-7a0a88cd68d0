package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.entity.UserStar
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.room.EntityDescriptions
import kotlinx.coroutines.flow.Flow

@Dao
abstract class UserDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertAll(starList: List<UserStar>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(star: UserStar): Long

    @Update
    abstract suspend fun update(star: UserStar): Int

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_STARS_LIST} ORDER BY ${UserStar.COLUMN_IS_SUPERSTAR} DESC, ${UserStar.COLUMN_LAST_BROADCAST_TIME} DESC")
    abstract fun starsPagingSource(): PagingSource<Int, UserStar>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_STARS_LIST} WHERE ${UserStar.COLUMN_USER_ID} = :id")
    abstract fun getUserStar(id: Int): UserStar?

    @Query("DELETE FROM ${EntityDescriptions.TABLE_STARS_LIST} WHERE id = :starId")
    abstract suspend fun deleteStar(starId: Int): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_STARS_LIST}")
    abstract suspend fun deleteAllStars()

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_STARS_LIST} WHERE ${UserStar.COLUMN_USER_ID} = :id")
    abstract fun getUserStarLiveData(id: Int): LiveData<UserStar?>

    @Query("UPDATE ${EntityDescriptions.TABLE_STARS_LIST} SET ${UserStar.COLUMN_UNREAD_MESSAGE_COUNT} = :count  WHERE ${UserStar.COLUMN_USER_ID} = :id")
    abstract fun setUserStarUnreadMessageCount(id: Int, count: Int): Int

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertUserRelative(list: List<UserRelative>): List<Long>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_LIST} WHERE relative_type = :type")
    abstract fun userRelativePagingSource(type: FollowerType): PagingSource<Int, UserRelative>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_LIST} WHERE id = :id")
    abstract fun getRelativeUser(id: Int): UserRelative?

    @Query("DELETE FROM ${EntityDescriptions.TABLE_BROADCAST_LIST} WHERE relative_type = :type")
    abstract fun deleteAllUserRelatives(type: FollowerType)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertAllNickNames(starList: List<NickName>)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_USER_NICK_NAMES}")
    abstract fun getNickNamesLiveData(): LiveData<List<NickName>>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_USER_NICK_NAMES}")
    abstract fun getNickNamesFlow(): Flow<List<NickName>>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_USER_NICK_NAMES} WHERE ${NickName.COLUMN_USER_ID} = :id")
    abstract fun getNickNameLiveData(id: Int): LiveData<NickName?>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_USER_NICK_NAMES} WHERE ${NickName.COLUMN_USER_ID} = :id")
    abstract fun getNickName(id: Int): NickName?

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_USER_NICK_NAMES} WHERE ${NickName.COLUMN_USER_ID} in (:ids)")
    abstract fun getNickNames(ids: List<Int>): List<NickName>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_USER_NICK_NAMES} WHERE ${NickName.COLUMN_USER_ID} = :id")
    abstract suspend fun deleteNickName(id: Int): Int

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertOtherUser(user: OtherUser)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_OTHER_USER} WHERE ${OtherUser.COLUMN_USER_ID} = :id")
    abstract fun getOtherUser(id: Int): OtherUser?

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_OTHER_USER} WHERE ${OtherUser.COLUMN_USER_ID} = :id")
    abstract fun getOtherUserLiveData(id: Int): LiveData<OtherUser?>
}