package com.app.messej.data.model.api.podium.challenges

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DDMMYYYY_SLASHED
import com.app.messej.data.utils.DateTimeUtils.FORMAT_ISO_DATE_TIME
import com.app.messej.data.utils.DateTimeUtils.format
import com.app.messej.data.utils.DateTimeUtils.parseDateTime
import com.google.gson.annotations.SerializedName

data class PodiumMaidanSupporter(

    @SerializedName("id")                                   override val id: Int,
    @SerializedName("name")                                 override val name: String,
    @SerializedName("username")                             override val username: String,
    @SerializedName("thumbnail")                            override val thumbnail: String?,
    @SerializedName("membership")                           override val membership: UserType,
    @SerializedName("citizenship")                          override val citizenship: UserCitizenship?,
    @SerializedName("verified")                             override val verified: <PERSON><PERSON><PERSON>,
    @SerializedName("created_time")                         val createdTime: String?,
    @SerializedName("gender")                               val gender: String?,
    @SerializedName("join_hidden")                          val joinHidden: Boolean?,
    @SerializedName("profile_url")                          val profileUrl: String?,
    @SerializedName("camera_violation")                     val cameraViolation: Boolean?,
    @SerializedName("gain_loss")                            val gainLoss: Boolean?,
    @SerializedName("enable_camera")                        val enableCamera: Boolean?,
    @SerializedName("country_code")                         override val countryCode: String?,
    @SerializedName("gain_loss_amount")                     val gainLossAmount: Double?,
    @SerializedName("camera_expiry")                        val cameraExpiry: String?,
    @SerializedName("likes")                                val likes: Int?,
    @SerializedName("user_rating")                          val userRating: Double?,
    @SerializedName("score")                                val score:Int?,
    @SerializedName("all_count")                            val allCount: Int?,
    @SerializedName("loses_count")                          val losesCount: Int?,
    @SerializedName("won_count")                            val wonCount: Int?,
    @SerializedName("is_followed")                          val isFollowed: Boolean?,
    @SerializedName("charges")                              val charges:Double?,

    var supporterRank: Int? 
) : AbstractUser(){

    val formattedCreatedTime: String
        get() = format(parseDateTime(createdTime, FORMAT_ISO_DATE_TIME), FORMAT_DDMMYYYY_SLASHED)
}
