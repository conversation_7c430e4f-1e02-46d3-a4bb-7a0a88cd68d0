package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.FlashReportedComment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FlashReportedCommentsDataSource(private val api: FlashAPIService): PagingSource<Int, FlashReportedComment>() {

    companion object;
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FlashReportedComment> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: null
                val response = api.getReportedComments(page = currentPage)
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
//                val nextKey = if (data.items.isEmpty() || data >= data.totalPages) null else currentPage.plus(1)
                val nextKey = null
                LoadResult.Page(
                    data = data.items, prevKey = currentPage?.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, FlashReportedComment>): Int? {
        return null
    }
}