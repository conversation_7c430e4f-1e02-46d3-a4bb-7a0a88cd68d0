package com.app.messej.data.model.api.podium.challenges

import com.app.messej.data.model.enums.ChallengeType
import com.google.gson.annotations.SerializedName

data class PodiumCreateChallengeRequest(
    @SerializedName("challenge_type") val challengeType: ChallengeType,
    @SerializedName("facilitator_id") val facilitatorId: Int? = null,
    @SerializedName("is_yalla") val isYalla: Boolean? = null,
    @SerializedName("participate") val participate: Boolean? = null,
    @SerializedName("prize") val prize: Int? = null,
)