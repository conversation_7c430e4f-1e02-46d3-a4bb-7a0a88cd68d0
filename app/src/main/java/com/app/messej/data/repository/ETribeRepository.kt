package com.app.messej.data.repository

import android.content.Context
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.ETribeAPIService
import com.app.messej.data.model.api.ContactETribeRequest
import com.app.messej.data.model.api.EditEtribeRequest
import com.app.messej.data.model.api.eTribe.ETribeResponse
import com.app.messej.data.model.api.eTribe.ETribeSuperStarMessageResponse
import com.app.messej.data.model.enums.ETribeTabs
import com.app.messej.data.repository.pagingSources.ETribeListingDataSource
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf

/**
 * Repository to handle E- Tribe
 */
class ETribeRepository(private var mContext: Context) {

    fun getETribeList(
        tab: ETribeTabs,
        tribeDetailCallBack: (ETribeResponse?) -> Unit
    ): Pager<Int, ETribeResponse.ETribeMembers> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                ETribeListingDataSource(
                    apiService = APIServiceGenerator.createService(ETribeAPIService ::class.java),
                    tab = tab,
                    tribeDetailCallBack = tribeDetailCallBack
                )
            }
        )
    }

    suspend fun editTribeName(tribeId: Int?, tribeName: String?): ResultOf<String> {
        return try {
            val req = EditEtribeRequest(tribeId = tribeId, tribeName = tribeName)
            val resp = APIServiceGenerator.createService(ETribeAPIService::class.java).editTribeName(req = req)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun contactETribe(req: ContactETribeRequest) : ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(ETribeAPIService::class.java).notifyUsers(req = req)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getSuperStarMessageDetails() : ResultOf<ETribeSuperStarMessageResponse> {
        return try {
            val response = APIServiceGenerator.createService(ETribeAPIService::class.java).getSuperStarMessages()
            APIUtil.handleResponse(response)
        } catch (error : Exception) {
            ResultOf.Error(Exception(error))
        }
    }
}