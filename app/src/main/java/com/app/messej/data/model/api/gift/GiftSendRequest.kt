package com.app.messej.data.model.api.gift


import com.google.gson.annotations.SerializedName

data class GiftSendRequest(
    @SerializedName("gift_id")
    val giftId: Int,
    @SerializedName("receiver_id")
    val receiverId: Int? = 0,
    @SerializedName("podium_id")
    val podiumId: String? = null,
    @SerializedName("challenge_id")
    val challengeId: String? = null,
    @SerializedName("challenge_end_timestamp_utc")
    val challengeEndTimeStampUTC: Long? = null,
    @SerializedName("sending_source")
    val sendingSource: String,
    @SerializedName("sending_source_id")
    val sendingSourceId: String? = null,
    @SerializedName("manager_id")
    val managerId: Int? = 0,

    @SerializedName("device_type")
    val deviceType:String = "android"
)