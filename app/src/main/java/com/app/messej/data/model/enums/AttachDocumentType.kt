package com.app.messej.data.model.enums

enum class AttachDocumentType {
    DOC, PPT, IMAGE, AUDIO, VIDEO, FONT, PDF, TEXT, CSV, OTHER;

    companion object {
        fun forExtension(ext: String?): AttachDocumentType {
            return when (ext) {
                "doc", "docx", "odt" -> DOC
                "ppt", "pptx", "ods" -> PPT
                "jpg", "jpeg", "png", "tiff", "heif", "dng", "bmp", "webp", -> IMAGE
                "mp3", "wav", "aac", "ogg", "flac", "m4a" -> AUDIO
                "mp4", "avi", "mov", "mkv", "mpeg", "wmv", "webm" -> VIDEO
                "ttf", "woff", "woff2", "otf" -> FONT
                "pdf" -> PDF
                "txt", "rtf" -> TEXT
                "csv" -> CSV
                else -> <PERSON><PERSON><PERSON>
            }
        }
    }
}