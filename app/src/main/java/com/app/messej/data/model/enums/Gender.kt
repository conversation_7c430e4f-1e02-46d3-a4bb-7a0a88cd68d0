package com.app.messej.data.model.enums

import com.google.gson.TypeAdapter
import com.google.gson.annotations.JsonAdapter
import com.google.gson.annotations.SerializedName
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter
import java.io.IOException

@JsonAdapter(Gender.GenderAdapter::class)
enum class Gender(val code: String) {
    @SerializedName("Male") MALE("Male"),
    @SerializedName("Female") FEMALE("Female"),
    @SerializedName("Prefer Not to Say") NOT_SAID("Prefer Not to Say");

    override fun toString(): String {
        return code
    }

    companion object {
        infix fun from(value: String): Gender? = entries.firstOrNull { it.code.lowercase() == value.lowercase() }
    }

    class GenderAdapter: TypeAdapter<Gender>() {
        @Throws(IOException::class)
        override fun write(out: JsonWriter, value: Gender?) {
            if (value == null) {
                out.nullValue()
            } else {
                out.value(value.code)
            }
        }

        @Throws(IOException::class)
        override fun read(reader: JsonReader): Gender? {
            return if (reader.peek() === JsonToken.NULL) {
                reader.nextNull()
                null
            } else {
                from(reader.nextString().lowercase())
            }
        }
    }
}