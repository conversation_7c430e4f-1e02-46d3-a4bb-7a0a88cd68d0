package com.app.messej.data.model.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.room.EntityDescriptions.TABLE_REMOTE_PAGING_KEY

@Entity(tableName = TABLE_REMOTE_PAGING_KEY)
data class RemotePagingKey(
    @PrimaryKey(autoGenerate = false) val query: String,
    val nextPage: String?
    ) {
    val nextPageInt: Int
        get() = nextPage?.toInt()?:1
}
