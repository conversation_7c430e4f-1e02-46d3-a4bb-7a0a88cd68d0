package com.app.messej.data.model.api.postat


import com.google.gson.annotations.SerializedName

data class PostatCommentResponse(
    @SerializedName("items", alternate = ["comments"])
    val postatComments: List<PostComment>,
    @SerializedName("pageState")
    val pageState: String?,
    @SerializedName("total")
    val totalCount: Int,
    @SerializedName("has_next")
    val hasNext: Boolean? = null,
    @SerializedName("is_comment_enabled")
    val isCommentEnabled: Boolean? = null
)
