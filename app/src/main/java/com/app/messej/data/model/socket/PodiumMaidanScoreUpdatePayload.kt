package com.app.messej.data.model.socket

import com.app.messej.data.model.SenderDetails
import com.google.gson.annotations.SerializedName

data class PodiumMaidanScoreUpdatePayload(
    @SerializedName("podium_id"      ) val podiumId: String,
    @SerializedName("event_type") val eventType: MaidanContributionType,
    @SerializedName("hidden") val hidden: <PERSON><PERSON><PERSON>,
    @SerializedName("coins") val coins: Int, // coins given now
    @SerializedName("total_coins") val totalCoins: Int, // total coins given for current type
    @SerializedName("gift") val gift: SentGiftPayload?,
    @SerializedName("sender") val sender: SenderDetails,
    @SerializedName("coin_balance") val coinBalance: Double, // coin balance of the user
    @SerializedName("coins_given") val coinsGiven: Int, // total coins given across all types
    @SerializedName("time_created") val timeCreated: String,
    @SerializedName("participants") val participants: List<MaidanParticipantScore>
): SocketEventPayload() {
    enum class MaidanContributionType {
        @SerializedName("like") LIKE,
        @SerializedName("gift") GIFT
    }

    data class MaidanParticipantScore(
        @SerializedName("id") val userId: Int,
        @SerializedName("score") val score: Double,
    )
}
