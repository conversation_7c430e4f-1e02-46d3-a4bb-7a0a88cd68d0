package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ETribeAPIService
import com.app.messej.data.model.api.eTribe.ETribeResponse
import com.app.messej.data.model.enums.ETribeTabs
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ETribeListingDataSource(
    private val apiService: ETribeAPIService,
    private val tab: ETribeTabs,
    private val tribeDetailCallBack: (ETribeResponse?) -> Unit
) : PagingSource<Int, ETribeResponse.ETribeMembers>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, ETribeResponse.ETribeMembers>): Int? {
        return state.anchorPosition
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ETribeResponse.ETribeMembers> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = apiService.getETribe(page = currentPage, tab = tab.serializedName())
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val dataWithoutList = data.copy(members = null)
                tribeDetailCallBack(dataWithoutList)
                val nextKey = if (data.nextPage == null) null else currentPage + 1
                LoadResult.Page(
                    data = data.members ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}