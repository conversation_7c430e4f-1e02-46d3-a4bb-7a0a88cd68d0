package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class LegalAffairsMainTab {
    @SerializedName("investigation_bureau") InvestigationBureau,
    @SerializedName("advocates") AdvocatesUnion,
    @SerializedName("jury") Jury,
    MyLegalRecords;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)?.value ?: ""
    }
}