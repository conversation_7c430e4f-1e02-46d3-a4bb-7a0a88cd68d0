package com.app.messej.data.model.api.forward


import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.socket.AbstractChatMessagePayload
import com.google.gson.annotations.SerializedName

data class ForwardHuddleRequest(
    @SerializedName("forward_id")
    val forwardId: String? = "",
    @SerializedName("huddle_id")
    val huddleId: List<Int?>? = listOf(),
    @SerializedName("has_mention")
    val hasMention: Boolean? = false,
    override val message: String?,
    override val color: ChatTextColor?,
): AbstractChatMessagePayload()