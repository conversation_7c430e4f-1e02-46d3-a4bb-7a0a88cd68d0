package com.app.messej.data.room.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class RemotePagingDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertOrReplace(remoteKey: RemotePagingKey)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_REMOTE_PAGING_KEY} WHERE query = :query")
    abstract suspend fun remoteKeyByQuery(query: String): RemotePagingKey?

    @Query("DELETE FROM ${EntityDescriptions.TABLE_REMOTE_PAGING_KEY} WHERE query = :query")
    abstract suspend fun deleteByQuery(query: String)
}