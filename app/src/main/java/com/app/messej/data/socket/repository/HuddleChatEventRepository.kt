package com.app.messej.data.socket.repository

import android.util.Log
import androidx.room.withTransaction
import com.app.messej.MainApplication
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.AbstractHuddle.HuddleAdminStatus
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.Remover
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleReportedMessage
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.model.socket.ChatDeleteEvent
import com.app.messej.data.model.socket.ChatDeletePayload
import com.app.messej.data.model.socket.ChatReadAllPayload
import com.app.messej.data.model.socket.ChatTypingPayload
import com.app.messej.data.model.socket.GroupChatReadPayload
import com.app.messej.data.model.socket.HuddleAdminStatusUpdateEvent
import com.app.messej.data.model.socket.HuddleBlockedEvent
import com.app.messej.data.model.socket.HuddleChatDeliveredAllEvent
import com.app.messej.data.model.socket.HuddleChatEnterPayload
import com.app.messej.data.model.socket.HuddleChatMessagePayload
import com.app.messej.data.model.socket.HuddleDeleteEvent
import com.app.messej.data.model.socket.HuddleLikeEvent
import com.app.messej.data.model.socket.HuddleLikePayload
import com.app.messej.data.model.socket.HuddleMemberRemovedEvent
import com.app.messej.data.model.socket.HuddleOnlineCount
import com.app.messej.data.model.socket.HuddlePollPayload
import com.app.messej.data.model.socket.HuddleTypingInfo
import com.app.messej.data.model.socket.HuddleUpdateEvent
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.JsonUtil.fromJson
import com.app.messej.ui.utils.LocaleUtil
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object HuddleChatEventRepository: BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {

    private val accountRepo = AccountRepository(MainApplication.applicationInstance())
    private val huddleRepo = HuddlesRepository(MainApplication.applicationInstance())

    private val dao = db.getHuddleDao()

    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when(event) {
            ChatSocketEvent.RX_TX_PUBLIC_HUDDLE_CHAT_MESSAGE -> onNewHuddleChatMessage(data)
            ChatSocketEvent.RX_TX_PRIVATE_GROUP_CHAT_MESSAGE -> onNewGroupChatMessage(data, HuddleType.PRIVATE)
            ChatSocketEvent.RX_TX_HUDDLE_CHAT_TYPING -> onHuddleTyping(data)
            ChatSocketEvent.RX_HUDDLE_CHAT_USER_COUNT -> onUserCountUpdate(data)
            ChatSocketEvent.RX_TX_HUDDLE_CHAT_LIKE -> onChatLike(data)
            ChatSocketEvent.RX_TX_HUDDLE_CHAT_DELETE -> onDeleteHuddleChatMessage(data)
            ChatSocketEvent.RX_TX_HUDDLE_CHAT_GOT_BLOCKED -> onBlockedFromHuddleChat(data)
            ChatSocketEvent.RX_HUDDLE_UPDATE -> onHuddleUpdate(data)
            ChatSocketEvent.RX_HUDDLE_DELETE -> onHuddleDelete(data)
            ChatSocketEvent.RX_HUDDLE_MEMBER_REMOVED -> onRemovedFromHuddle(data)
            ChatSocketEvent.RX_HUDDLE_ADMIN_STATUS_UPDATE -> onHuddleAdminStatusUpdate(data)
            ChatSocketEvent.RX_HUDDLE_CHAT_DELIVERED_ALL -> onHuddleChatDeliveredAll(data)
            ChatSocketEvent.RX_HUDDLE_POLL_DELIVERED_ALL -> onHuddlePollDeliveredAll(data)
            else -> return false
        }
        return true
    }



    private val _pollPayloadFlow: MutableSharedFlow<HuddlePollPayload> = MutableSharedFlow()
    val pollPayloadFlow: SharedFlow<HuddlePollPayload> = _pollPayloadFlow

    private fun onHuddlePollDeliveredAll(data: JSONObject) = runBlocking {
        val pollPayload = Gson().fromJson<HuddlePollPayload>(data.toString())

        _pollPayloadFlow.emit(pollPayload)
    }


    private val _newPostFlow: MutableSharedFlow<Pair<Int,String>> = MutableSharedFlow()
    val newPostFlow: SharedFlow<Pair<Int,String>> = _newPostFlow

    private val _newActivityFlow: MutableSharedFlow<Pair<Int,String>> = MutableSharedFlow()
    val newActivityFlow: SharedFlow<Pair<Int,String>> = _newActivityFlow

    private fun onNewHuddleChatMessage(data: JSONObject) = runBlocking {
        val msg = Gson().fromJson<HuddleChatMessage>(data.toString())
        db.withTransaction {
            val huddle = db.getHuddleDao().getHuddle(msg.huddleIdInt) ?: return@withTransaction
            //check if blocked from receiving chats
            if (!huddle.canReceiveNewChats) return@withTransaction

            val unread = if (data.has("unread_count")) data.getInt("unread_count") else null
            unread?.let {
                if (msg.sender == AccountRepository(MainApplication.applicationContext()).user.id) return@let
                huddle.unreadCount = it
            }

            huddle.lastMessage = msg
            Log.d("HCER", "onNewHuddleChatMessage: updating huddle $huddle")
            dao.updateHuddle(huddle)
            val existing = db.getChatMessageDao().getHuddleChatMessage(msg.messageId)
            if (existing != null) {
                db.getChatMessageDao().updateChat(existing.copy(
                    rawMessage = msg.rawMessage,
                    media = msg.media,
                    edited = msg.edited,
                    mediaMeta = msg.mediaMeta,
                    hasMention = msg.hasMention,
                    mentionedUsers = msg.mentionedUsers
                ).apply { sanitize() })
                return@withTransaction
            }
            if (msg.edited == true) return@withTransaction

            if(accountRepo.user.id!=msg.sender && !msg.isActivity && msg.messageType != AbstractChatMessage.MessageType.POLL) {
                _newPostFlow.emit(Pair(msg.huddleIdInt, msg.messageId))
            } else if(msg.isActivity) {
                _newActivityFlow.emit(Pair(msg.huddleIdInt, msg.messageId))
            }
        }
    }

    private val _groupChatMessageReceived = MutableSharedFlow<HuddleChatMessage>(replay = 0)
    val groupChatMessageReceived: SharedFlow<HuddleChatMessage> = _groupChatMessageReceived

    private fun onNewGroupChatMessage(data: JSONObject, type: HuddleType) = runBlocking {
        val tempChatId = if (data.has("private_chat_id")) data.getString("private_chat_id") else null
        var msg = Gson().fromJson<HuddleChatMessage>(data.toString())
        val huddle = db.getHuddleDao().getHuddle(msg.huddleIdInt)?: return@runBlocking
        //check if blocked from receiving chats
        if (!huddle.canReceiveNewChats) return@runBlocking

        val existing = db.getChatMessageDao().getHuddleChatMessageWithMedia(msg.messageId)
        if(existing!=null && existing.message.isActivity) {
            return@runBlocking
        }
        sendGroupMessageDeliveryStatus(msg.roomId, msg.messageId)
        if (msg.sender == user.id) _groupChatMessageReceived.emit(msg)
        msg = msg.copy(
            huddleType = type,
            huddleId = HuddleChatMessage.prefixHuddleId(msg.huddleId)
        )
        msg.sanitize()
        ChatRepository(MainApplication.applicationInstance()).insertNewChatMessage(msg, tempChatId)
        val unread = if (data.has("unread_count")) data.getInt("unread_count") else null
        unread?.let {
            if(msg.sender==AccountRepository(MainApplication.applicationContext()).user.id) return@let
            dao.updateUnreadCount(msg.huddleIdInt,unread)
        }

    }

    private fun onBlockedFromHuddleChat(data: JSONObject) = runBlocking {
        var msg = Gson().fromJson<HuddleBlockedEvent>(data.toString())
        val accountRepo = AccountRepository(MainApplication.applicationContext())
        if(!accountRepo.loggedIn) return@runBlocking
        val chats = db.getChatMessageDao().getHuddleChatMessagesOfMember(HuddleChatMessage.prefixHuddleId(msg.huddleId), msg.memberId)
        if(chats.isNotEmpty()) {
            chats.forEach {
                    it.senderDetails?.blockedByHuddleAdmin = msg.status == AbstractHuddle.HuddleUserStatus.BLOCKED_BY_ADMIN
            }
            db.getChatMessageDao().updateChats(chats)
        }
        if(msg.memberId!=accountRepo.user.id) return@runBlocking
        val huddle = db.getHuddleDao().getHuddle(msg.huddleId)?: return@runBlocking
        db.getHuddleDao().updateHuddle(huddle.copy(
            userStatus = msg.status
        ))
    }

    private val _huddleTyping = MutableStateFlow<Map<Int,HuddleTypingInfo>>(mapOf())
    val huddleTyping: StateFlow<Map<Int, HuddleTypingInfo>> = _huddleTyping

    private fun onHuddleTyping(data: JSONObject) {
        val info = Gson().fromJson<HuddleTypingInfo>(data.toString())
        val map = _huddleTyping.value.toMutableMap()
        val accountRepo = AccountRepository(MainApplication.applicationContext())
        val userId = if (accountRepo.loggedIn) accountRepo.user.id else 0
        if(info.typing && info.senderId!=userId) map[info.huddleId] = info else map.remove(info.huddleId)
        _huddleTyping.value = map
    }

    private val _onlineCountFlow: MutableStateFlow<Map<Int,Int>> = MutableStateFlow(mapOf())
    val onlineCountFlow: StateFlow<Map<Int,Int>> = _onlineCountFlow

    private fun onUserCountUpdate(data: JSONObject) = runBlocking {
        val msg = Gson().fromJson<HuddleOnlineCount>(data.toString())
        val map = _onlineCountFlow.value.toMutableMap()
        map[msg.huddleId] = msg.online
        _onlineCountFlow.value = map
//        withContext(Dispatchers.IO) {
//            db.withTransaction {
//                dao.updateOnlineCount(msg.huddleId,msg.online)
//            }
//        }
    }

    private fun onChatLike(data: JSONObject) = runBlocking {
        val msg = Gson().fromJson<HuddleLikeEvent>(data.toString())
        withContext(Dispatchers.IO) {
            db.withTransaction {
                db.getChatMessageDao().apply {
                    val huddle = getHuddleChatMessage(msg.messageId)?: return@withTransaction
                    updateChat(huddle.copy(
                        totalLikes = msg.totalLikes
                    ))
                }
            }
        }
    }

    private val _huddleUpdateFlow: MutableSharedFlow<HuddleUpdateEvent> = MutableSharedFlow()
    val huddleUpdateFlow: SharedFlow<HuddleUpdateEvent> = _huddleUpdateFlow

    private fun onHuddleUpdate(data: JSONObject) = runBlocking {
        val event = Gson().fromJson<HuddleUpdateEvent>(data.toString())
        val accountRepo = AccountRepository(MainApplication.applicationContext())
        if(!accountRepo.loggedIn) return@runBlocking
        withContext(Dispatchers.IO){
            db.withTransaction {
                val huddle = db.getHuddleDao().getHuddle(event.id)
                huddle?.let {
                    if(user.id != huddle.managerId) {
                        db.getHuddleDao().updateHuddle(
                            huddle.copy(
                                userStatus = event.userStatus, adminStatus = event.adminStatus
                            )
                        )
                    }
                }
            }
        }
        _huddleUpdateFlow.emit(event)
    }

    //shared flow for is huddle deleted with huddle id
    private val _huddleDeletedFlow: MutableSharedFlow<Int> = MutableSharedFlow()
    val huddleDeletedFlow: SharedFlow<Int> = _huddleDeletedFlow

    private fun onHuddleDelete(data: JSONObject) = runBlocking {
        val event = Gson().fromJson<HuddleDeleteEvent>(data.toString())
        val accountRepo = AccountRepository(MainApplication.applicationContext())
        if(!accountRepo.loggedIn) return@runBlocking
        withContext(Dispatchers.IO){
            db.withTransaction {
                db.getHuddleDao().deleteHuddle(id = event.huddleId)
            }
            _huddleDeletedFlow.emit(event.huddleId)
        }
    }

    private val _removedFromHuddleFlow: MutableSharedFlow<HuddleMemberRemovedEvent> = MutableSharedFlow()
    val removedFromHuddleFlow: SharedFlow<HuddleMemberRemovedEvent> = _removedFromHuddleFlow
    private fun onRemovedFromHuddle(data: JSONObject) = runBlocking {
        val event = Gson().fromJson<HuddleMemberRemovedEvent>(data.toString())
        val accountRepo = AccountRepository(MainApplication.applicationContext())
        if(!accountRepo.loggedIn) return@runBlocking
        if (!event.memberId.contains(accountRepo.user.id)) return@runBlocking
        Log.w("HCER", "onRemovedFromHuddle: deleting huddle as member ${event.memberId} removed. I am ${accountRepo.user.id}" )
        withContext(Dispatchers.IO){
            db.withTransaction {
                db.getHuddleDao().deleteHuddle(id = event.huddleId)
                _removedFromHuddleFlow.emit(event)
            }
        }
    }


    val user: CurrentUser get() = accountRepo.user

    private fun onDeleteHuddleChatMessage(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<ChatDeleteEvent>(data.toString())
        withContext(Dispatchers.IO) {
            if(evt.deleteForAll) {
                var msg = db.getChatMessageDao().getHuddleChatMessage(evt.messageId)
                msg ?: return@withContext
                if (msg.huddleType==HuddleType.PRIVATE) {
                    val message = if (evt.remover != null && user.id == evt.remover.id.toInt()) evt.remover.message else evt.message
                    val remover = if (evt.remover != null) Remover(id = evt.remover.id, message = evt.remover.message) else Remover(id = evt.userId.toString(), message = evt.message)
                    msg = msg.copy(
                        rawMessage = message, deleted = true, remover = if(msg.remover != null) msg.remover else remover
                    )
                    val replies = db.getChatMessageDao().getHuddleChatMessagesThatReplyToMessage(evt.messageId)

                    db.withTransaction {
                        replies?.forEach { reply ->
                            reply.replyTo?.let {
                                if (it.messageId == evt.messageId) {
                                    it.message = evt.message
                                    it.deleted = true
                                }
                            }
                            db.getChatMessageDao().updateChat(reply)
                        }
                        db.getChatMessageDao().updateChat(msg)
                    }
                } else {
                    db.getChatMessageDao().deleteHuddleChatMessage(evt.messageId)
                }
            } else {
                db.getChatMessageDao().deleteHuddleChatMessage(evt.messageId)
            }
        }
    }

    private fun onHuddleAdminStatusUpdate(data: JSONObject) = runBlocking {
        val event = Gson().fromJson<HuddleAdminStatusUpdateEvent>(data.toString())
        withContext(Dispatchers.IO) {
            if (user.id == event.memberId) {
                db.withTransaction {
                    val huddle = db.getHuddleDao().getHuddle(event.id)
                    huddle?.let {
                        huddleRepo.saveHuddle(huddle.copy(
                            userStatus = event.userStatus, adminStatus = event.adminStatus, role = event.role
                        ).apply {
                            detectInvolvement()
                        })
                    }
                }
            } else {
                db.getHuddleDao().getHuddleWithInterventions(event.id)?.interventions?.let {int ->
                    val newIntervention = when (event.adminStatus) {
                        HuddleAdminStatus.INVITED -> int.copy(invitedToBeAdmin = int.invitedToBeAdmin.plus(event.memberId))
                        else  -> int.copy(invitedToBeAdmin = int.invitedToBeAdmin.minus(event.memberId))
                    }
                    when (event.adminStatus) {
                        HuddleAdminStatus.ACCEPTED -> {
                            val chats = db.getChatMessageDao().getHuddleChatMessagesOfMember(HuddleChatMessage.prefixHuddleId(event.id), event.memberId)
                            chats.forEach {
                                it.senderDetails?.role = UserRole.ADMIN
                            }
                            db.getChatMessageDao().updateChats(chats)
                        }
                        null -> {
                            val chats = db.getChatMessageDao().getHuddleChatMessagesOfMember(HuddleChatMessage.prefixHuddleId(event.id), event.memberId)
                            chats.forEach {
                                it.senderDetails?.role = UserRole.USER
                            }
                            db.getChatMessageDao().updateChats(chats)
                        }

                        else -> {}
                    }
                    db.getHuddleDao().updateHuddleInterventions(newIntervention)
                }
            }
        }
    }

    private fun onHuddleChatDeliveredAll(data: JSONObject) = runBlocking {
        val event = Gson().fromJson<HuddleChatDeliveredAllEvent>(data.toString())
        db.withTransaction {
            db.getChatMessageDao().updateHuddleDeliveredAllMessages(
                deliveredTime = DateTimeUtils.getZonedDateTimeNowAsString(),
                roomId = event.roomId,
                createdTime = event.created
            )
        }
    }

    fun sendHuddleMessage(msg: HuddleChatMessagePayload, channel: HuddleType): Boolean {
        val event = when(channel) {
            HuddleType.PUBLIC -> ChatSocketEvent.RX_TX_PUBLIC_HUDDLE_CHAT_MESSAGE
            HuddleType.PRIVATE -> ChatSocketEvent.RX_TX_PRIVATE_GROUP_CHAT_MESSAGE
        }
        return ChatSocketRepository.sendEvent(event, msg)
    }

    fun likeChatMessage(msg: AbstractChatMessage): Boolean {
        return ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_HUDDLE_CHAT_LIKE, HuddleLikePayload(msg.roomId!!, msg.messageId, !msg.liked))
    }

    fun setReadAll(userId: Int, messageId: String) {
        ChatSocketRepository.sendEvent(ChatSocketEvent.TX_HUDDLE_CHAT_READ_ALL, ChatReadAllPayload(userId, messageId, messageId))
    }

    fun setRead(roomId: String, messageId: String) {
        ChatSocketRepository.sendEvent(ChatSocketEvent.TX_HUDDLE_CHAT_READ, GroupChatReadPayload(roomId, messageId))
    }

    fun sendGroupMessageDeliveryStatus(roomId: String, messageId: String){
        ChatSocketRepository.sendEvent(ChatSocketEvent.TX_HUDDLE_DELIVERY_STATUS, GroupChatReadPayload(roomId, messageId))
    }

    fun deleteHuddleChatMessage(messages: List<HuddleChatMessage>, deleteForEveryone: Boolean) {
        val payload = ChatDeletePayload(
            messages = messages.map {
                ChatDeletePayload.MessagesToDelete(
                    messageId = it.messageId,
                    roomId = it.roomId
                )
            },
            deleteForEveryone = deleteForEveryone,
            language = LocaleUtil.getAppLocale().apiCode
        )
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_HUDDLE_CHAT_DELETE, payload)
    }

    fun deleteHuddleReportedMessage(messages: List<HuddleReportedMessage>) : Boolean {
        val payload = ChatDeletePayload(
            messages = messages.map {
                ChatDeletePayload.MessagesToDelete(
                    messageId = it.messageId,
                    roomId = it.roomId!!
                )
            },
            deleteForEveryone = true,
            language = LocaleUtil.getAppLocale().apiCode
        )
        return ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_HUDDLE_CHAT_DELETE, payload)
    }

    fun announceChatEnter(huddleId: Int) {
        ChatSocketRepository.sendEvent(ChatSocketEvent.TX_HUDDLE_CHAT_ENTER, HuddleChatEnterPayload(huddleId))
    }

    fun announceChatExit(huddleId: Int) {
        ChatSocketRepository.sendEvent(ChatSocketEvent.TX_HUDDLE_CHAT_ENTER, HuddleChatEnterPayload(huddleId, false))
    }

    fun announceChatTyping(huddleId: Int, typing: Boolean) {
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_HUDDLE_CHAT_TYPING, ChatTypingPayload(huddleId, typing))
    }
}