package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.PenaltyData
import com.app.messej.data.model.api.podium.challenges.PenaltyKickTarget
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.google.gson.annotations.SerializedName

data class PodiumChallengePenaltyKickResultPayload(
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("result") val result: Result,
    @SerializedName("penalty_data") val penaltyData: PenaltyData,
    @SerializedName("switch_round") val switchRound: Boolean = false,
    @SerializedName("game_over") val gameOver: Boolean = false,

) : SocketEventPayload() {

    data class Result(
        @SerializedName("status") val status: PodiumChallengeScore.PenaltyKickResult,
        @SerializedName("kicker_target") val _kickerTarget: Int?,
        @SerializedName("keeper_target") val _keeperTarget: Int?,
    ) {
        val kickerTarget: PenaltyKickTarget?
            get() = PenaltyKickTarget.from(_kickerTarget)

        val keeperTarget: PenaltyKickTarget?
            get() = PenaltyKickTarget.from(_keeperTarget)
    }
}