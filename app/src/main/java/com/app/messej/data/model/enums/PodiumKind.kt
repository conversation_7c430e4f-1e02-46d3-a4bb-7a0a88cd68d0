package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PodiumKind(val maxSpeakers: Int, val mainScreens: Int) {
    @SerializedName("THEATER") THEATER(18,2),
    @SerializedName("LECTURE") LECTURE(9,1),
    @SerializedName("INTERVIEW") INTERVIEW(2,2),
    @SerializedName("ALONE") ALONE(1,1),
    @SerializedName("ASSEMBLY") ASSEMBLY(12,0),
    @SerializedName("MAIDAN") MAIDAN(2,2);


    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    val hasMainScreens: Boolean
        get() = mainScreens > 0

    companion object {
        fun PodiumKind?.orDefault(): PodiumKind {
            return this?: LECTURE
        }
    }
}