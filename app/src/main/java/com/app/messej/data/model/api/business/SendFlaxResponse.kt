package com.app.messej.data.model.api.business


import com.google.gson.annotations.SerializedName

data class SendFlaxResponse(
    @SerializedName("receiving_flax")
    val receivingFlax: Double? = null,
    @SerializedName("sending_fees")
    val sendingFees: Double? = null,
    @SerializedName("sending_flax")
    val sendingFlax: Double? = null,
    @SerializedName("user_rating")
    val userRating: Double? = null,
    @SerializedName("receiver_name")
    val receiverName: String? = null

)