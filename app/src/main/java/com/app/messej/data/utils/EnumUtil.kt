package com.app.messej.data.utils

import kotlin.enums.EnumEntries

object EnumUtil {
    inline fun <reified T : Enum<*>> enumValueOrNull(name: String): T? = T::class.java.enumConstants?.firstOrNull { it.name == name }

    inline fun <reified T: Enum<T>> T.next(): T {
        val values = enumValues<T>()
        val nextOrdinal = (ordinal + 1) % values.size
        return values[nextOrdinal]
    }

    inline fun <reified T : Enum<T>> EnumEntries<T>.except(vararg src: T): Array<T> {
        return this.filter { !src.contains(it) }.toTypedArray()
    }

    inline fun <reified T : Enum<T>> Array<T>.except(vararg src: T): Array<T> {
        return this.filter { !src.contains(it) }.toTypedArray()
    }

    inline fun <reified T : Enum<T>> EnumEntries<T>.just(vararg src: T): Array<T> {
        return this.filter { src.contains(it) }.toTypedArray()
    }

    inline fun <reified T : Enum<T>> Array<T>.just(vararg src: T): Array<T> {
        return this.filter { src.contains(it) }.toTypedArray()
    }
}