package com.app.messej.data.model.api.poll

import com.google.gson.annotations.SerializedName

data class CreatePollRequest(
    @SerializedName("answers") val answers: List<String>? = null,
    @SerializedName("end_date") val endDate: String? = "",
    @SerializedName("huddle_id") var huddleId: Int? = null,
    @SerializedName("poll_id") var pollId: Int? = null,
    @SerializedName("question") val question: String? = null,
    @SerializedName("start_date") val startDate: String? = "",
    @SerializedName("tz") val tz: String? = null,
    @SerializedName("start_now") val startNow:Boolean?=null
)