package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.app.messej.data.model.api.huddles.HuddleForSale
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class SellHuddleListDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertAll(huddleForSale: List<HuddleForSale>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(huddleForSale: HuddleForSale): Long

    @Query("DELETE FROM ${EntityDescriptions.TABLE_SELL_HUDDLE_LIST}")
    abstract suspend fun deleteSellHuddleList()

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_SELL_HUDDLE_LIST} ORDER BY ${HuddleForSale.COLUMN_CREATED_TIME} DESC")
    abstract fun sellHuddleListPagingSource(): PagingSource<Int, HuddleForSale>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_SELL_HUDDLE_LIST} WHERE ${HuddleForSale.HUDDLE_ID} = :huddleId ")
    abstract fun getSelectedHuddle(huddleId:Int): LiveData<HuddleForSale?>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_SELL_HUDDLE_LIST} WHERE ${HuddleForSale.HUDDLE_ID} = :huddleId")
    abstract suspend fun deleteBuyHuddleFromDB(huddleId: Int): Int
}