package com.app.messej.data.model

import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

data class FlashReportedComment (
    @SerializedName("id"                  ) val commentId: String,
    @SerializedName("flash_id"            ) val flashId: String,

    @SerializedName("comment"             ) val comment: String,
    @SerializedName("flash"               ) val flash: FlashVideoMinimal,

    @SerializedName("report_counts"       ) val reportsCount: Int=0,
    @SerializedName("is_delete"           ) var isDeleted: Boolean = false,

    @SerializedName("time_created"        ) val created: String,
    @SerializedName("time_updated"        ) var updatedTime      : String?    = null,

    @SerializedName("sender"             ) val senderId: Int,
    @SerializedName("sender_details"      ) val senderDetails: SenderDetails,
) {
    data class FlashVideoMinimal(
        @SerializedName("id"                ) val id              : String,
        @SerializedName("caption"           ) val caption         : String?       = null,

        @SerializedName("video_url"         ) val videoUrl        : String,
        @SerializedName("thumbnail"         ) val thumbnailUrl    : String,
    )

    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(created)
    val formattedCreatedDateTime: String
        get() = DateTimeUtils.format(parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")
}
