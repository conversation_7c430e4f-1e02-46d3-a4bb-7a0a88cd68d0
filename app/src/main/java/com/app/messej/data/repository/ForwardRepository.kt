package com.app.messej.data.repository

import android.app.Application
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.ForwardAPIService
import com.app.messej.data.model.api.forward.Forward
import com.app.messej.data.model.api.forward.ForwardHuddleRequest
import com.app.messej.data.model.enums.ForwardType
import com.app.messej.data.repository.pagingSources.ForwardDataSource
import com.app.messej.data.repository.pagingSources.ForwardSearchDataSource
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf

class ForwardRepository(private val context: Application) {

    fun getForwardListing(forwardType: ForwardType): Pager<Int, Forward> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = { ForwardDataSource(APIServiceGenerator.createService(ForwardAPIService::class.java), forwardType) }
        )
    }

    fun getForwardSearchList(forwardType: ForwardType,searchTerm:String): Pager<Int, Forward> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = { ForwardSearchDataSource(APIServiceGenerator.createService(ForwardAPIService::class.java), forwardType,searchTerm) }
        )
    }

    suspend fun sendHuddleForwardList(request : ForwardHuddleRequest) : ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(ForwardAPIService::class.java).sendHuddleForward(request)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }
}