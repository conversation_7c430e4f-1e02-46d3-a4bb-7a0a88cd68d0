package com.app.messej.data.socket.repository

import android.util.Log
import com.app.messej.MainApplication
import com.app.messej.data.model.api.podium.PodiumChallengeSetupEvent
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.entity.YallaGuysChallenge
import com.app.messej.data.model.socket.BoxChallengeLineDrawPayload
import com.app.messej.data.model.socket.BoxChallengeLineDrawnPayload
import com.app.messej.data.model.socket.ChallengeClosePayload
import com.app.messej.data.model.socket.ChallengeContributorTimeOutPayload
import com.app.messej.data.model.socket.ChallengeGameOverPayload
import com.app.messej.data.model.socket.ChallengeScoreUpdatePayload
import com.app.messej.data.model.socket.ChallengeSyncPayload
import com.app.messej.data.model.socket.ConFourDropTimedOutPayload
import com.app.messej.data.model.socket.ConFourTokenDropPayload
import com.app.messej.data.model.socket.ConFourTokenDroppedPayload
import com.app.messej.data.model.socket.KnowledgeRaceAnswerPayload
import com.app.messej.data.model.socket.KnowledgeRaceUpdateEvent
import com.app.messej.data.model.socket.PodiumChallengeInsufficientCoinEvent
import com.app.messej.data.model.socket.PodiumChallengeInvitePayload
import com.app.messej.data.model.socket.PodiumChallengePenaltyKickResultPayload
import com.app.messej.data.model.socket.PodiumChallengePenaltyReadyPayload
import com.app.messej.data.model.socket.PodiumChallengePenaltyStartPayload
import com.app.messej.data.model.socket.PodiumChallengePenaltyTargetPayload
import com.app.messej.data.model.socket.PodiumMaidanScoreUpdatePayload
import com.app.messej.data.model.socket.PodiumSpeakerInviteResponsePayload
import com.app.messej.data.model.socket.PodiumUserJoinedPayload
import com.app.messej.data.model.socket.YallaChallengeRemovePayload
import com.app.messej.data.model.socket.YallaCompetitorRequestPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.socket.PodiumSocketEvent
import com.app.messej.data.socket.PodiumSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.runBlocking
import org.json.JSONObject

object PodiumChallengeEventRepository : BaseEventRepository<PodiumSocketEvent>(PodiumSocketRepository) {

    private val accountRepo = AccountRepository(MainApplication.applicationInstance())

    override fun handleEvent(event: PodiumSocketEvent, data: JSONObject): Boolean {
        Log.w("SOCKETs", "handleEvent: $event")
        when (event) {
            PodiumSocketEvent.RX_CHALLENGE_FACILITATOR_APPOINT -> onFacilitatorAppoint(data)
            PodiumSocketEvent.RX_CHALLENGE_FACILITATOR_CONFIRMED -> onFacilitatorConfirmed(data)
            PodiumSocketEvent.RX_CHALLENGE_FACILITATOR_DECLINED -> onFacilitatorDeclined(data)
            PodiumSocketEvent.RX_CHALLENGE_CONTRIBUTOR_APPOINT -> onContributorRequest(data)
            PodiumSocketEvent.RX_CHALLENGE_CONTRIBUTOR_CONFIRMED -> onContributorConfirmed(data)
            PodiumSocketEvent.RX_CHALLENGE_CONTRIBUTOR_DECLINED -> onContributorDeclined(data)
            PodiumSocketEvent.RX_CHALLENGE_CONTRIBUTOR_TIMED_OUT -> onContributorTimedOut(data)
            PodiumSocketEvent.RX_CHALLENGE_CONTRIBUTOR_INSUFFICIENT_BALANCE -> onContributorLowBalance(data)
            PodiumSocketEvent.RX_CHALLENGE_TIMER_SET -> onChallengeTimeSet(data)
            PodiumSocketEvent.RX_CHALLENGE_START -> onChallengeStart(data)
            PodiumSocketEvent.RX_CHALLENGE_CLOSE -> onChallengeClose(data)
            PodiumSocketEvent.RX_CHALLENGE_CANCEL -> onChallengeClose(data)
            PodiumSocketEvent.RX_TX_CHALLENGE_SCORE_UPDATE -> onChallengeScoreUpdate(data)
            PodiumSocketEvent.RX_CHALLENGE_SYNC -> onChallengeSync(data)
            PodiumSocketEvent.RX_CHALLENGE_SPEAKER_INVITE -> onChallengeSpeakerInvite(data)
            PodiumSocketEvent.RX_CHALLENGE_SPEAKER_INVITE_RESPONSE -> onChallengeSpeakerInviteResponse(data)
            PodiumSocketEvent.RX_TX_CONFOUR_TOKEN_DROPPED -> onConFourTokenDropped(data)
            PodiumSocketEvent.RX_TX_CONFOUR_DROP_TIMED_OUT -> onConFourDropTimedOut(data)
            PodiumSocketEvent.RX_CHALLENGE_GAME_OVER -> onChallengeGameOver(data)
            PodiumSocketEvent.RX_PODIUM_USER_JOINED -> onUserJoined(data)

            PodiumSocketEvent.RX_TX_PENALTY_PLAYER_READY -> onPenaltyPlayerReady(data)
            PodiumSocketEvent.RX_PENALTY_START_TURN -> onPenaltyStartTurn(data)
            PodiumSocketEvent.RX_TX_PENALTY_SELECT_TARGET -> onPenaltySelectTarget(data)
            PodiumSocketEvent.RX_TX_PENALTY_KICK_RESULT -> onPenaltyKickResult(data)

            PodiumSocketEvent.RX_MAIDAN_SCORE_UPDATE -> onMaidanScoreUpdate(data)
            PodiumSocketEvent.RX_MAIDAN_CONTRIBUTOR_UPDATE -> onMaidanContributorUpdate(data)

            PodiumSocketEvent.RX_YALLA_UPDATE -> onYallaUpdate(data)
            PodiumSocketEvent.RX_YALLA_REMOVE -> onYallaRemove(data)
            PodiumSocketEvent.RX_YALLA_COMPETITOR_REQUEST -> onYallaCompetitorRequest(data)
            PodiumSocketEvent.RX_YALLA_NOTIFICATION -> onYallaNotification(data)

            PodiumSocketEvent.RX_TX_BOX_LINE_DRAWN -> onBoxChallengeLineDrawn(data)
            PodiumSocketEvent.RX_TX_BOX_LINE_TIMED_OUT -> onBoxLineTimedOut(data)
            PodiumSocketEvent.RX_KNOWLEDGE_RACE_UPDATE -> onKnowledgeRaceUpdate(data)

            else -> return false
        }
        return true
    }

    private val _challengeFacilitatorRequestFlow:MutableSharedFlow<PodiumChallenge> = MutableSharedFlow()
    val challengeFacilitatorRequestFlow:SharedFlow<PodiumChallenge> = _challengeFacilitatorRequestFlow

    private val _challengeContributorRequestFlow:MutableSharedFlow<PodiumChallenge> = MutableSharedFlow()
    val challengeContributorRequestFlow:SharedFlow<PodiumChallenge> = _challengeContributorRequestFlow

    private val _challengeSetupEventFlow:MutableSharedFlow<PodiumChallengeSetupEvent> = MutableSharedFlow()
    val challengeSetupEventFlow:SharedFlow<PodiumChallengeSetupEvent> = _challengeSetupEventFlow

    private val _challengeUpdateFlow:MutableSharedFlow<PodiumChallenge> = MutableSharedFlow()
    val challengeUpdateFlow:SharedFlow<PodiumChallenge> = _challengeUpdateFlow

    private val _challengeCloseFlow:MutableSharedFlow<ChallengeClosePayload> = MutableSharedFlow()
    val challengeCloseFlow:SharedFlow<ChallengeClosePayload> = _challengeCloseFlow

    private fun onFacilitatorAppoint(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
            val facilitator  = result.facilitator?: return@runBlocking
            delay(5000)
            if (facilitator.id == facilitator.appointedBy) {
                _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.FacilitatorConfirmed(result.challengeId, facilitator.id, facilitator.name))
            } else {
                _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.FacilitatorSelected(result.challengeId, facilitator.id, facilitator.name))
            }
            _challengeFacilitatorRequestFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "showFacilitatorConfirmAction: ", e)
        }
    }

    private fun onFacilitatorConfirmed(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
            val facilitator  = result.facilitator?: return@runBlocking
            _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.FacilitatorConfirmed(result.challengeId, facilitator.id, facilitator.name))
        } catch (e: Exception) {
            Log.e("PER", "facilitatorConfirmed: ", e)
        }
    }

    private fun onFacilitatorDeclined(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
            val facilitator  = result.facilitator?: return@runBlocking
            _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.FacilitatorDeclined(result.challengeId, facilitator.id, facilitator.name))
        } catch (e: Exception) {
            Log.e("PER", "facilitatorDeclined: ", e)
        }
    }

    private fun onContributorRequest(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
            result.contributorId?.let { cid ->
                val contributorType = result.contributorType?: return@runBlocking
                val contributor = result.contributors.find { it.id == cid }?: return@runBlocking
                _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.ContributorRequested(result.challengeId, contributor, contributorType))
            }
            _challengeContributorRequestFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "showContributorConfirmAction: ", e)
        }
    }

    private fun onContributorConfirmed(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
            result.contributorId?.let { cid ->
                val contributorType = result.contributorType?: return@runBlocking
                val contributor = result.contributors.find { it.id == cid }?: return@runBlocking
                _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.ContributorConfirmed(result.challengeId, contributor, contributorType))
            }
        } catch (e: Exception) {
            Log.e("PER", "onContributorConfirmed: ", e)
        }
    }

    private fun onContributorDeclined(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
            result.contributorId?.let { cid ->
                val contributorType = result.contributorType?: return@runBlocking
                val contributor = result.contributors.find { it.id == cid }?: return@runBlocking
                _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.ContributorDeclined(result.challengeId, contributor, contributorType))
            }
        } catch (e: Exception) {
            Log.e("PER", "onContributorDeclined: ", e)
        }
    }

    private fun onContributorTimedOut(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<ChallengeContributorTimeOutPayload>(data.toString())
            _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.ContributorTimedOut(result.challengeId, result.contributors))
        } catch (e: Exception) {
            Log.e("PER", "onContributorDeclined: ", e)
        }
    }

    private fun onContributorLowBalance(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallengeInsufficientCoinEvent>(data.toString())
            _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.ContributorLowBalance(result.challengeId, result.id, result.name, result.contributorType))
        } catch (e: Exception) {
            Log.e("PER", "onContributorLowBalance: ", e)
        }
    }

    private fun onChallengeTimeSet(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
            _challengeSetupEventFlow.emit(PodiumChallengeSetupEvent.DurationSet(result.challengeId, result.duration?:0L))
        } catch (e: Exception) {
            Log.e("PER", "challengeTimeSet: ", e)
        }
    }

    private fun onChallengeStart(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "challengeTimeSet: ", e)
        }
    }

    private fun onChallengeClose(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<ChallengeClosePayload>(data.toString())
            _challengeCloseFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "challengeTimeSet: ", e)
        }
    }

    private val _challengeScoreUpdateFlow:MutableSharedFlow<ChallengeScoreUpdatePayload> = MutableSharedFlow()
    val challengeScoreUpdateFlow:SharedFlow<ChallengeScoreUpdatePayload> = _challengeScoreUpdateFlow
    private fun onChallengeScoreUpdate(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<ChallengeScoreUpdatePayload>(data.toString())
            _challengeScoreUpdateFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "challengeScoreUpdate: ", e)
        }
    }

    fun updateChallengeScore(challenge: PodiumChallenge, receiver: Int): Boolean {
        return PodiumSocketRepository.sendEvent(PodiumSocketEvent.RX_TX_CHALLENGE_SCORE_UPDATE, ChallengeScoreUpdatePayload.from(challenge, accountRepo.user.id, receiver, 1.0))
    }

    private val _challengeSyncFlow:MutableSharedFlow<ChallengeSyncPayload> = MutableSharedFlow()
    val challengeSyncFlow:SharedFlow<ChallengeSyncPayload> = _challengeSyncFlow
    private fun onChallengeSync(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<ChallengeSyncPayload>(data.toString())
            _challengeSyncFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "challengeScoreUpdate: ", e)
        }
    }

    private val _challengeSpeakerInviteFlow:MutableSharedFlow<PodiumChallengeInvitePayload> = MutableSharedFlow()
    val challengeSpeakerInviteFlow:SharedFlow<PodiumChallengeInvitePayload> = _challengeSpeakerInviteFlow

    private fun onChallengeSpeakerInvite(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallengeInvitePayload>(data.toString())
            _challengeSpeakerInviteFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onChallengeSpeakerInvite: ", e)
        }
    }

    private val _challengeSpeakerInviteResponseFlow:MutableSharedFlow<PodiumSpeakerInviteResponsePayload> = MutableSharedFlow()
    val challengeSpeakerInviteResponseFlow:SharedFlow<PodiumSpeakerInviteResponsePayload> = _challengeSpeakerInviteResponseFlow

    private fun onChallengeSpeakerInviteResponse(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumSpeakerInviteResponsePayload>(data.toString())
            _challengeSpeakerInviteResponseFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onChallengeSpeakerInviteResponse: ", e)
        }
    }

    // ConFour

    private val _conFourTokenDroppedFlow:MutableSharedFlow<ConFourTokenDroppedPayload> = MutableSharedFlow()
    val conFourTokenDroppedFlow:SharedFlow<ConFourTokenDroppedPayload> = _conFourTokenDroppedFlow
    private fun onConFourTokenDropped(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<ConFourTokenDroppedPayload>(data.toString())
            _conFourTokenDroppedFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onConFourTokenDroppedResponse: ", e)
        }
    }

    private fun onConFourDropTimedOut(data: JSONObject)  = runBlocking {
        try {
            val result = Gson().fromJson<ConFourTokenDroppedPayload>(data.toString())
            _conFourTokenDroppedFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onConFourDropTimeoutResponse: ", e)
        }
    }

    fun informConFourTimeout(payload: ConFourDropTimedOutPayload): Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.RX_TX_CONFOUR_DROP_TIMED_OUT, payload
        )
    }

    fun dropConFourToken(payload: ConFourTokenDropPayload): Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.RX_TX_CONFOUR_TOKEN_DROPPED, payload
        )
    }

    private val _challengeGameOverFlow: MutableSharedFlow<ChallengeGameOverPayload> = MutableSharedFlow()
    val challengeGameOverFlow: SharedFlow<ChallengeGameOverPayload> = _challengeGameOverFlow
    private fun onChallengeGameOver(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<ChallengeGameOverPayload>(data.toString())
            _challengeGameOverFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onConFourDropTimeoutResponse: ", e)
        }
    }

    private val _userJoinedFlow: MutableSharedFlow<PodiumUserJoinedPayload> = MutableSharedFlow()
    val userJoinedFlow: SharedFlow<PodiumUserJoinedPayload> = _userJoinedFlow
    private fun onUserJoined(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumUserJoinedPayload>(data.toString())
            _userJoinedFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onConFourDropTimeoutResponse: ", e)
        }
    }

    // Penalty

    fun setPenaltyPlayerReady(payload: PodiumChallengePenaltyReadyPayload): Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.RX_TX_PENALTY_PLAYER_READY, payload
        )
    }

    private val _playerReadyFlow: MutableSharedFlow<PodiumChallengePenaltyReadyPayload> = MutableSharedFlow()
    val playerReadyFlow: SharedFlow<PodiumChallengePenaltyReadyPayload> = _playerReadyFlow

    private fun onPenaltyPlayerReady(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallengePenaltyReadyPayload>(data.toString())
            _playerReadyFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onPenaltyPlayerReady: ", e)
        }
    }

    private val _penaltyStartTurnFlow: MutableSharedFlow<PodiumChallengePenaltyStartPayload> = MutableSharedFlow()
    val penaltyStartTurnFlow: SharedFlow<PodiumChallengePenaltyStartPayload> = _penaltyStartTurnFlow

    private fun onPenaltyStartTurn(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallengePenaltyStartPayload>(data.toString())
            _penaltyStartTurnFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onPenaltyStartTurn: ", e)
        }
    }

    fun setPenaltyTarget(payload: PodiumChallengePenaltyTargetPayload): Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.RX_TX_PENALTY_SELECT_TARGET, payload
        )
    }

    private val _penaltySelectTargetFlow: MutableSharedFlow<PodiumChallengePenaltyTargetPayload> = MutableSharedFlow()
    val penaltySelectTargetFlow: SharedFlow<PodiumChallengePenaltyTargetPayload> = _penaltySelectTargetFlow

    private fun onPenaltySelectTarget(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallengePenaltyTargetPayload>(data.toString())
            _penaltySelectTargetFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onPenaltySelectTarget: ", e)
        }
    }

    private val _penaltyKickResultFlow: MutableSharedFlow<PodiumChallengePenaltyKickResultPayload> = MutableSharedFlow()
    val penaltyKickResultFlow: SharedFlow<PodiumChallengePenaltyKickResultPayload> = _penaltyKickResultFlow

    private fun onPenaltyKickResult(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallengePenaltyKickResultPayload>(data.toString())
            _penaltyKickResultFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onPenaltySelectTarget: ", e)
        }
    }

    private val _maidanScoreUpdateFLow: MutableSharedFlow<PodiumMaidanScoreUpdatePayload> = MutableSharedFlow()
    val maidanScoreUpdateFLow: SharedFlow<PodiumMaidanScoreUpdatePayload> = _maidanScoreUpdateFLow

    private fun onMaidanScoreUpdate(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumMaidanScoreUpdatePayload>(data.toString())
            _maidanScoreUpdateFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onPodiumLike: ", e)
        }
    }

    private fun onMaidanContributorUpdate(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            _challengeUpdateFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onMaidanContributorUpdate: ", e)
        }
    }

    private val yallaDao = db.getYallaGuysDao()

    private fun onYallaUpdate(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<YallaGuysChallenge>(data.toString())
            val existing = yallaDao.getChallenge(result.challengeId)
            if (existing != null) {
                yallaDao.update(result)
            } else {
                yallaDao.insert(result)
            }
        } catch (e: Exception) {
            Log.e("PER", "onYallaUpdate: ", e)
        }
    }

    private fun onYallaRemove(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<YallaChallengeRemovePayload>(data.toString())
            yallaDao.delete(result.challengeId)
        } catch (e: Exception) {
            Log.e("PER", "onYallaRemove: ", e)
        }
    }

    private val _yallaCompetitorRequestFlow: MutableSharedFlow<YallaCompetitorRequestPayload> = MutableSharedFlow()
    val yallaCompetitorRequestFlow: SharedFlow<YallaCompetitorRequestPayload> = _yallaCompetitorRequestFlow

    private fun onYallaCompetitorRequest(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<YallaCompetitorRequestPayload>(data.toString())
            _yallaCompetitorRequestFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onYallaCompetitorRequest: ", e)
        }
    }

    private val _yallaNotificationFlow: MutableSharedFlow<String> = MutableSharedFlow()
    val yallaNotificationFlow: SharedFlow<String> = _yallaNotificationFlow

    private fun onYallaNotification(data: JSONObject) = runBlocking {
        try {
            val podiumId = data.getString("podium_id")
            _yallaNotificationFlow.emit(podiumId)
        } catch (e: Exception) {
            Log.e("PER", "onYallaNotification: ", e)
        }
    }

    // Box Challenge
    private val _boxLineDrawnFlow:MutableSharedFlow<BoxChallengeLineDrawnPayload> = MutableSharedFlow()
    val boxLineDrawnFlow:SharedFlow<BoxChallengeLineDrawnPayload> = _boxLineDrawnFlow
    private fun onBoxChallengeLineDrawn(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<BoxChallengeLineDrawnPayload>(data.toString())
            _boxLineDrawnFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onConFourTokenDroppedResponse: ", e)
        }
    }

    private fun onBoxLineTimedOut(data: JSONObject)  = runBlocking {
        try {
            val result = Gson().fromJson<BoxChallengeLineDrawnPayload>(data.toString())
            _boxLineDrawnFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onConFourDropTimeoutResponse: ", e)
        }
    }

    fun informBoxLineTimeout(payload: ConFourDropTimedOutPayload): Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.RX_TX_BOX_LINE_TIMED_OUT, payload
        )
    }

    fun drawBoxLine(payload: BoxChallengeLineDrawPayload): Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.RX_TX_BOX_LINE_DRAWN, payload
        )
    }

    private val _knowledgeRaceUpdateFlow:MutableSharedFlow<KnowledgeRaceUpdateEvent> = MutableSharedFlow()
    val knowledgeRaceUpdateFlow:SharedFlow<KnowledgeRaceUpdateEvent> = _knowledgeRaceUpdateFlow

    private fun onKnowledgeRaceUpdate(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<KnowledgeRaceUpdateEvent>(data.toString())
            _knowledgeRaceUpdateFlow.emit(result)
        } catch (e: Exception) {
            Log.e("PER", "onKnowledgeRaceUpdate: ", e)
        }
    }

    fun answerKnowledgeRaceQuestion(payload: KnowledgeRaceAnswerPayload): Boolean {
        return PodiumSocketRepository.sendEvent(
            PodiumSocketEvent.TX_KNOWLEDGE_RACE_ANSWER, payload
        )
    }
}