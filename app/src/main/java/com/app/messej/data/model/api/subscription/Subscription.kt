package com.app.messej.data.model.api.subscription


import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_ISO_DATE_TIME
import com.google.gson.annotations.SerializedName
import java.time.LocalDate
import java.time.LocalDateTime

data class Subscription(
    @SerializedName("expiration_date")
    val expirationDate: String? = "",
    @SerializedName("limit")
    val limit: Int? = 0,
    @SerializedName("status")
    val status: String? = "",
    @SerializedName("subscription_details")
    val subscriptionDetails: List<SubscriptionDetails?>? = listOf()
) {
    data class SubscriptionDetails(
        @SerializedName("currency")
        val currency: String? = "",
        @SerializedName("payment_amount")
        val paymentAmount: Double? = 0.0,
        @SerializedName("payment_date")
        val paymentDate: String? = "",
        @SerializedName("payment_method")
        val paymentMethod: String? = ""
    )

   val subscriptionExpiration: LocalDateTime?
        get() = DateTimeUtils.parseDateTime(expirationDate, FORMAT_ISO_DATE_TIME)

    val daysLeft: Int?
        get()= LocalDate.now().until(subscriptionExpiration?.toLocalDate()).days
}