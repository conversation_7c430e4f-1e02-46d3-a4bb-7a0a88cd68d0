package com.app.messej.data.model.socket

import com.app.messej.data.model.entity.PrivateChatRoomInfo
import com.google.gson.annotations.SerializedName

data class ChatRoomUpdateEvent(

    @SerializedName("receiver"         ) val receiver       : Int,
    @SerializedName("room_id"          ) val roomId         : String,
    @SerializedName("chat_room_info"   ) var chatRoomInfo   : PrivateChatRoomInfo.ChatRoom,
    @SerializedName("followed_by_each" ) var followedByMe   : Boolean?      = null

) {
}
