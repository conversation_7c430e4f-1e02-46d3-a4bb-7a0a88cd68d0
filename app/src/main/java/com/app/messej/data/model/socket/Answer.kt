package com.app.messej.data.model.socket


import com.google.gson.annotations.SerializedName

data class Answer(
    @SerializedName("answer")
    val answer: String,
    @SerializedName("answer_percentage")
    val answerPercentage: Double,
    @SerializedName("id")
    val id: Int,
    @SerializedName("no_of_votes")
    val noOfVotes: Int,
    @SerializedName("poll_id")
    val pollId: Int,
    @SerializedName("time_created")
    val timeCreated: String,
    @SerializedName("time_updated")
    val timeUpdated: String
)