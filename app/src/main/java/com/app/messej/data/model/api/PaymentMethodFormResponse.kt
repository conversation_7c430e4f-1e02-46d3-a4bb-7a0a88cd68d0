package com.app.messej.data.model.api


import com.app.messej.data.model.enums.InputType
import com.google.gson.annotations.SerializedName

data class PaymentMethodFormResponse(
    @SerializedName("country_id") val countryId: Int,
    @SerializedName("country_name") val countryName: String,
    @SerializedName("inputs") val inputs: List<Input>,
    @SerializedName("language") val language: String,
    @SerializedName("payment_method_id") val paymentMethodId: Int,
) {
    data class Input(
        @SerializedName("id") val id: Int,
        @SerializedName("input_infotext") val inputInfotext: String,
        @SerializedName("input_label") var inputLabel: String,
        @SerializedName("input_placeholder") var inputPlaceholder: String,
        @SerializedName("input_type") val inputType: InputType,
        @SerializedName("listing_order") val listingOrder: Int,
        @SerializedName("options") val options: List<Option>,
        @SerializedName("input_data") var inputData: String? = null,
    ) {
        data class Option(
            @SerializedName("input_type_value") val inputTypeValue: String,
            @SerializedName("listing_order") val listingOrder: Int,
        )
    }
}