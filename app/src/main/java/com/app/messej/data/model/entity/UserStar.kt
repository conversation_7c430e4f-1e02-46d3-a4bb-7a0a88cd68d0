package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.app.messej.data.model.AbstractUserWithStats
import com.app.messej.data.model.entity.UserStar.Companion.COLUMN_IS_SUPERSTAR
import com.app.messej.data.model.entity.UserStar.Companion.COLUMN_LAST_BROADCAST_TIME
import com.app.messej.data.model.entity.UserStar.Companion.COLUMN_MEMBERSHIP
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = EntityDescriptions.TABLE_STARS_LIST,
    indices = [
        Index(COLUMN_LAST_BROADCAST_TIME, unique = false),
        Index(COLUMN_IS_SUPERSTAR, unique = false),
        Index(COLUMN_MEMBERSHIP, unique = false)
    ])
data class UserStar(
    @SerializedName("id"                    ) @ColumnInfo(name = COLUMN_USER_ID         ) @PrimaryKey(autoGenerate = false) override val id: Int,
    @SerializedName("name"                  ) @ColumnInfo(name = "name"                 ) override var name                : String = "",
    @SerializedName("thumbnail"             ) @ColumnInfo(name = "thumbnail"            ) override val thumbnail           : String?,
    @SerializedName("username"              ) @ColumnInfo(name = "username"             ) override val username            : String?,

    @SerializedName("stars"                 ) @ColumnInfo(name = "stars"                ) override val stars               : Int = 0,
    @SerializedName("likers"                ) @ColumnInfo(name = "likers"               ) override val likers              : Int = 0,
    @SerializedName("dears"                 ) @ColumnInfo(name = "dears"                ) override val dears               : Int = 0,
    @SerializedName("fans"                  ) @ColumnInfo(name = "fans"                 ) override val fans                : Int = 0,

    @SerializedName("membership"            ) @ColumnInfo(name = COLUMN_MEMBERSHIP      ) override val membership          : UserType  = UserType.FREE,
    @SerializedName("verified"              ) @ColumnInfo(name = "verified"             ) override val verified            : Boolean,

    @SerializedName("superstar_id"          ) @ColumnInfo(name = "superstar_id"         ) val superstarId         : Int?     = null,
    @SerializedName("last_broadcast_time"   ) @ColumnInfo(name = COLUMN_LAST_BROADCAST_TIME  ) val lastBroadcastTime   : String? = null,
    @SerializedName("unread_messages_count" ) @ColumnInfo(name = COLUMN_UNREAD_MESSAGE_COUNT ) val unreadMessagesCount : Int = 0,

    @SerializedName("muted"                 ) @ColumnInfo(name = "muted"                ) val muted               : Boolean  = false,
    @SerializedName("pinned"                ) @ColumnInfo(name = "pinned"               ) val pinned              : Boolean  = false,
    @SerializedName("reported"              ) @ColumnInfo(name = "reported"             ) val reported            : Boolean  = false,
    @SerializedName("archived"              ) @ColumnInfo(name = "archived"             ) val archived            : Boolean  = false,
    @SerializedName("archived_pinned"       ) @ColumnInfo(name = "archived_pinned"      ) val archivedPinned      : String?  = null,
    @SerializedName("hidden"                ) @ColumnInfo(name = "hidden"               ) val hidden              : Boolean  = false,
    @SerializedName("citizenship"           ) @ColumnInfo(name = "citizenship"        ) override val citizenship         : UserCitizenship?=null

): AbstractUserWithStats() {

    @ColumnInfo(name = COLUMN_IS_SUPERSTAR)
    @Transient
    var isSuperStar: Boolean = false

    companion object{
        const val COLUMN_USER_ID = "id"
        const val COLUMN_LAST_BROADCAST_TIME = "last_broadcast_time"
        const val COLUMN_IS_SUPERSTAR = "isSuperStar"
        const val COLUMN_MEMBERSHIP = "membership"
        const val COLUMN_UNREAD_MESSAGE_COUNT = "unread_messages_count"

        fun AbstractUserWithStats.asStar(): UserStar {
            return UserStar(
                id = id,
                name = name,
                thumbnail = thumbnail,
                username = username,
                stars = stars,
                likers = likers,
                dears = dears,
                fans = fans,
                membership = membership,
                verified = verified,
                citizenship = citizenship
            )
        }
    }
}
