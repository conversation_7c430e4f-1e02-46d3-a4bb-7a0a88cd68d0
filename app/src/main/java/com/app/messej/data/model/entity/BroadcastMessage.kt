package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.ActivityMeta
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.socket.BroadcastMessagePayload
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = EntityDescriptions.TABLE_BROADCAST_MESSAGE,
    indices = [
        Index(BroadcastMessage.COLUMN_BROADCASTER, unique = false),
        Index(BroadcastMessage.COLUMN_BROADCAST_MODE, unique = false),
        Index(BroadcastMessage.COLUMN_MESSAGE_CREATED, unique = false),
        Index(BroadcastMessage.COLUMN_STARRED, unique = false),
        Index(BroadcastMessage.COLUMN_MESSAGE, unique = false)
    ]
)
@TypeConverters(
    ActivityMeta.Converter::class,
    ReplyTo.Converter::class,
    MediaMeta.Converter::class
)
data class BroadcastMessage (
    @SerializedName("id") @ColumnInfo(name = COLUMN_MESSAGE_ID) @PrimaryKey(autoGenerate = false) override val messageId : String,

    @SerializedName("broadcast_id"   ) @ColumnInfo(name = COLUMN_BROADCAST_ID       )          val broadcastId   : String,
    @SerializedName("message"        ) @ColumnInfo(name = COLUMN_MESSAGE            ) override var rawMessage       : String?  = null,

    @SerializedName("created"        ) @ColumnInfo(name = COLUMN_MESSAGE_CREATED    ) override val createdTime   : String,
    @SerializedName("delivered"      ) @ColumnInfo(name = "delivered"               ) override val deliveredTime : String? = null,

    @SerializedName("deleted"        ) @ColumnInfo(name = "deleted"                 ) override val deleted       : Boolean  = false,

    @SerializedName("media"          ) @ColumnInfo(name = "media"                   ) override var media         : String?  = null,
    @SerializedName("media_meta"     ) @ColumnInfo(name = "media_meta"              ) override var mediaMeta     : MediaMeta?  = null,
    @SerializedName("message_type"   ) @ColumnInfo(name = "message_type"            ) override var internalMessageType  : MessageType? = null,

    @SerializedName("broadcaster"    ) @ColumnInfo(name = COLUMN_BROADCASTER        )          val broadcaster   : Int,
    @SerializedName("subscriber"     ) @ColumnInfo(name = "subscriber"              )          val subscriber    : Int,
    @SerializedName("broadcast_type" ) @ColumnInfo(name = COLUMN_BROADCAST_MODE     )          val broadcastMode : BroadcastMode,


    @SerializedName("liked"          ) @ColumnInfo(name = "liked"                   ) override val liked         : Boolean  = false,
    @SerializedName("starred"        ) @ColumnInfo(name = COLUMN_STARRED            )          val starred       : Boolean  = false,
    @SerializedName("total_likes"    ) @ColumnInfo(name = "total_likes"             )          val totalLikes    : Int      = 0,
    @SerializedName("color"          ) @ColumnInfo(name = "color") override val chatTextColor         : ChatTextColor?,
    @SerializedName("forward_id"    ) @ColumnInfo(name = "forward_id")                override  val forwardId   : String?  = null


    ): AbstractChatMessage() {

    val socketpayload: BroadcastMessagePayload
        get() {
            val payload = BroadcastMessagePayload(broadcastId, broadcastMode, rawMessage, chatTextColor)
            mediaMeta?.let { med ->
                payload.setMedia(med)
            }
            return payload
        }

    companion object {
        const val COLUMN_MESSAGE_ID = "id"
        const val COLUMN_BROADCAST_ID = "broadcast_id"
        const val COLUMN_BROADCAST_MODE = "broadcast_type"
        const val COLUMN_BROADCASTER = "broadcaster"
        const val COLUMN_STARRED = "starred"
        const val COLUMN_MESSAGE_CREATED = "created"
        const val COLUMN_MESSAGE = "message"
    }

    @Ignore override val roomId        : String ? = null

    @Ignore override val sentTime      : String? = null

    @Ignore override val isActivity    : Boolean = false
    @Ignore override val activityMeta  : ActivityMeta? = null

    @Ignore override val read          : String? = null
    @Ignore override val receiver      : Int = subscriber
    @Ignore override val replyTo       : ReplyTo? = null
    @Ignore override val sender        : Int = broadcaster

    @Ignore override val reported    : Boolean = false
}