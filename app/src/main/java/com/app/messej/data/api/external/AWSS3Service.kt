package com.app.messej.data.api.external

import com.app.messej.data.Constants.HTTP_HEADER_CONTENT_TYPE
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PUT
import retrofit2.http.Streaming
import retrofit2.http.Url

interface AWSS3Service {

    @PUT
    fun uploadMedia(@Url url:String, @Body file: RequestBody, @Header(HTTP_HEADER_CONTENT_TYPE) type: String): Call<ResponseBody>

    @GET
    @Streaming
    fun downloadMedia(@Url url: String): Call<ResponseBody>
}