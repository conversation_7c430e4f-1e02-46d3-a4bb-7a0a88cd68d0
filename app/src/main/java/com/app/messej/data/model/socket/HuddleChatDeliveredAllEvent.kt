package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class HuddleChatDeliveredAllEvent(
    @SerializedName("user_id"    ) val userId    : Int,
    @SerializedName("room_id"    ) val roomId    : String,
    @SerializedName("message_id" ) val messageId : String,
    @SerializedName("chat_type"  ) val chatType  : String? = null,
    @SerializedName("created"    ) val created   : String,
    @SerializedName("delivered"  ) val delivered : String? = null,
    @SerializedName("sender"     ) val sender    : Int?    = null
) : SocketEventPayload()
