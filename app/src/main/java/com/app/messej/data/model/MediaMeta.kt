package com.app.messej.data.model

import androidx.room.TypeConverter
import com.app.messej.data.model.enums.AttachDocumentType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.MediaResolution
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class MediaMeta(
    @SerializedName("media_type"   ) var mediaType   : MediaType,
    @SerializedName("media_name"   ) val mediaName   : String? = null,

    @SerializedName("mime_type"    ) val mimeType    : String? = null,
    @SerializedName("s3_key"       ) val s3Key       : String,

    //only for images/video
    @SerializedName("thumbnail"    ) var thumbnail   : String? = null,
    @SerializedName("media_height" ) var mediaHeight : String? = null,
    @SerializedName("media_width"  ) var mediaWidth  : String? = null,

    //only for audio/video
    @SerializedName("media_duration") var mediaDuration  : String? = null,

    // only for video
    @SerializedName("media_size"   ) var formattedSize  : String? = null,

    // only for document
    @SerializedName("display_name" ) var documentDisplayName  : String? = null,

    // only for stickers
    @SerializedName("unicode"   ) var unicode      : String? = null,

    @SerializedName("hasPlayed") var hasPlayed   : Boolean?=null
) {
    val imageWidth: Float
        get() = mediaWidth?.toFloatOrNull()?:0f

    val imageHeight: Float
        get() = mediaHeight?.toFloatOrNull()?:0f

    val mediaExtension: String
        get() = mediaName?.split(".")?.lastOrNull()?:""

    val mediaDocumentType: AttachDocumentType
        get() = AttachDocumentType.forExtension(mediaExtension)

    var seconds: Long
        get() {
            val dur = DateTimeUtils.parseToDuration(mediaDuration?:"0:0")?.seconds?:0
            return dur.coerceAtLeast(1)
        }
        set(value) {
            mediaDuration = DateTimeUtils.formatSeconds(value)
        }

    val resolution: MediaResolution
        get() = MediaResolution(imageWidth.toInt(),imageHeight.toInt())

    class Converter {
        @TypeConverter
        fun decode(data: String?): MediaMeta? {
            data?: return null
            val type: Type = object : TypeToken<MediaMeta?>() {}.type
            return Gson().fromJson<MediaMeta>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: MediaMeta?): String? {
            return Gson().toJson(someObjects)
        }
    }
    val isPlayed: Boolean
        get() = hasPlayed ?: false
}