package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.PostatAPIServices
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.room.FlashatDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@OptIn(ExperimentalPagingApi::class)
class PostatCommentRemoteMediator(
    private val postatId: String,
    private val database: FlashatDatabase,
    private val networkService: PostatAPIServices,
    private val commentCountCallback: (Int) -> Unit
) : RemoteMediator<Int, PostCommentWithReplies>() {

    private val dao = database.getPostCommentDao()
    private val remoteKeyDao = database.getRemotePagingDao()
    private val tableKey = "postat_comments_$postatId"

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, PostCommentWithReplies>
    ): MediatorResult {
        return try {
            // For parent comments only, we use cursor-based pagination
            val pageState = when (loadType) {
                LoadType.REFRESH -> null
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    // For cursor-based pagination, we need to get the last cursor from RemoteKey
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }

                    // If nextPage is null, we've reached the end
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(endOfPaginationReached = true)
                    }

                    remoteKey.nextPage
                }
            }

            // Fetch parent comments only
            val response = networkService.getComments(postId = postatId, pageState = pageState)

            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                throw Exception(error.message)
            }

            val comments = result.postatComments
            commentCountCallback.invoke(result.totalCount)

            withContext(Dispatchers.IO) {
                database.withTransaction {
                    if (loadType == LoadType.REFRESH) {
                        // Clear parent comments for this postat with out touching replies
                        dao.deletePostComments(postatId, type = CommentType.POSTAT)
                        // Also clear the remote key
                        remoteKeyDao.deleteByQuery(tableKey)
                    }

                    // Add postatId to comments (parentCommentId remains null for parent comments)
                    val commentItems = comments.map { comment ->
                        comment.copy(
                            postatId = postatId,
                            type = CommentType.POSTAT
                        )
                    }

                    commentItems.forEach { it.sanitize() }
                    dao.insertPostComments(commentItems)

                    // Store the next page state for future APPEND operations
                    remoteKeyDao.insertOrReplace(
                        RemotePagingKey(tableKey, result.pageState)
                    )
                }
            }

            MediatorResult.Success(endOfPaginationReached = comments.isEmpty() || result.pageState == null)
        } catch (e: Exception) {
            Log.e("PostatCommentMediator", "Error loading comments", e)
            MediatorResult.Error(e)
        }
    }
}
