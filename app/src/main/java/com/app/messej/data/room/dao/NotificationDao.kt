package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.TypeConverters
import androidx.room.Update
import com.app.messej.data.model.entity.Notification
import com.app.messej.data.model.entity.NotificationAssociate
import com.app.messej.data.room.EntityDescriptions

@Dao
@TypeConverters(
    NotificationAssociate.Converter::class,
)
abstract class NotificationDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertNotifications(notificationList: List<Notification>): List<Long>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_NOTIFICATIONS} ORDER BY ${Notification.COLUMN_CREATED_TIME} DESC")
    abstract fun notificationListPagingSource(): PagingSource<Int, Notification>

    @Update(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun updateNotification(notificationList: Notification)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_NOTIFICATIONS} WHERE id = :itemId")
    abstract suspend fun deleteNotification(itemId: Int): Int

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_NOTIFICATIONS}")
    abstract fun getNotifications(): LiveData<List<Notification>>

}