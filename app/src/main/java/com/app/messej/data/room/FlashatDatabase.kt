package com.app.messej.data.room

import android.content.Context
import android.os.Environment
import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.app.messej.data.model.api.huddles.HuddleForSale
import com.app.messej.data.model.api.postat.PostComment
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.entity.BusinessPayoutHistory
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.model.entity.BusinessTaskOne
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleReportedComment
import com.app.messej.data.model.entity.HuddleReportedMessage
import com.app.messej.data.model.entity.LocalFlashMedia
import com.app.messej.data.model.entity.LocalPostatMedia
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.Notification
import com.app.messej.data.model.entity.OfflineGiftVideo
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.entity.PollParticipant
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.entity.PostReply
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.entity.PrivateChatRoomInfo
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.entity.PublicHuddleInterventions
import com.app.messej.data.model.entity.RecentSearch
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.entity.Sticker
import com.app.messej.data.model.entity.SubscriptionPurchaseRequest
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.entity.UserStar
import com.app.messej.data.model.entity.YallaGuysChallenge
import com.app.messej.data.model.status.BusinessActivityStatus
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.room.FlashatDatabase.Companion.DB_VERSION
import com.app.messej.data.room.dao.BusinessDao
import com.app.messej.data.room.dao.ChatMessageDao
import com.app.messej.data.room.dao.FeedPostatDao
import com.app.messej.data.room.dao.FlashDao
import com.app.messej.data.room.dao.GiftVideoDao
import com.app.messej.data.room.dao.HuddleDao
import com.app.messej.data.room.dao.MyPostatDao
import com.app.messej.data.room.dao.NotificationDao
import com.app.messej.data.room.dao.PodiumDao
import com.app.messej.data.room.dao.PollsDao
import com.app.messej.data.room.dao.PostCommentDao
import com.app.messej.data.room.dao.PostReplyDao
import com.app.messej.data.room.dao.PrivateChatDao
import com.app.messej.data.room.dao.RecentSearchDao
import com.app.messej.data.room.dao.RemotePagingDao
import com.app.messej.data.room.dao.SellHuddleListDao
import com.app.messej.data.room.dao.SubscriptionDao
import com.app.messej.data.room.dao.TransferHistoryDao
import com.app.messej.data.room.dao.UserDao
import com.app.messej.data.room.dao.YallaGuysDao
import com.app.messej.data.room.typeconverters.CommonConverters
import com.app.messej.data.utils.DateTimeUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.time.LocalDateTime

@Database(
    entities = [
        RemotePagingKey::class,

        PublicHuddle::class,
        PublicHuddleInterventions::class,
        PrivateChat::class,
        PrivateChatRoomInfo::class,
        HuddleChatMessage::class,
        PrivateChatMessage::class,
        BroadcastMessage::class,
        HuddleReportedMessage::class,
        HuddleReportedComment::class,
        OfflineMedia::class,
        Sticker::class,

        FlashVideo::class,
        LocalFlashMedia::class,

        UserStar::class,
        UserRelative::class,
        NickName::class,
        RecentSearch::class,
        PollParticipant::class,

        BusinessStatement::class,
        BusinessOperation::class,
        BusinessTaskOne::class,
        BusinessPayoutHistory::class,
        Notification::class,
        SubscriptionPurchaseRequest::class,
        OtherUser::class,
        PostCommentItem::class,
        PostComment::class,
        PostReply::class,
        DealsTransferHistory::class,
        Poll::class,
        Podium ::class,
        HuddleForSale::class,
        OfflineGiftVideo::class,

        Postat::class,
        Postat.FeedPostat::class,
        LocalPostatMedia::class,
        BusinessActivityStatus::class,

        YallaGuysChallenge::class,
    ],
    exportSchema = true,
    version = DB_VERSION,
    autoMigrations = [
        AutoMigration (from = 38, to = 39),
        AutoMigration (from = 39, to = 40),
        AutoMigration (from = 40, to = 42),
        AutoMigration (from = 42, to = 43),
        AutoMigration (from = 43, to = 44),
        AutoMigration (from = 44, to = 45),
        AutoMigration (from = 45, to = 46),
        AutoMigration (from = 46, to = 47),
        AutoMigration (from = 47, to = 48),
        AutoMigration (from = 48, to = 49),
        AutoMigration (from = 49, to = 50),
        AutoMigration (from = 50, to = 51),
        AutoMigration (from = 51, to = 52),
        AutoMigration (from = 52, to = 53),
        AutoMigration (from = 53, to = 54),
        AutoMigration (from = 54, to = 55),
        AutoMigration (from = 55, to = 56),
        AutoMigration (from = 56, to = 57),
        AutoMigration (from = 57, to = 58),
        AutoMigration (from = 58, to = 59),
        AutoMigration (from = 59, to = 60, spec = DatabaseMigrations.DeleteUserEmpowerment::class),
        AutoMigration (from = 60, to = 61),
        AutoMigration (from = 61, to = 62),
        AutoMigration (from = 62, to = 63),
        AutoMigration (from = 63, to = 64),
        AutoMigration (from = 64, to = 65),
        AutoMigration (from = 65, to = 66),
        AutoMigration (from = 66, to = 67),
        AutoMigration (from = 67, to = 68, spec = DatabaseMigrations.DeleteTabColumn::class),
        AutoMigration (from = 68, to = 69),
        AutoMigration (from = 69, to = 70),
        AutoMigration (from = 70, to = 71),
        AutoMigration (from = 71, to = 72, spec = DatabaseMigrations.DeletePodiumTypeEnumColumn::class),
        AutoMigration (from = 72, to = 73),
        AutoMigration (from = 73, to = 74),
        AutoMigration (from = 74, to = 75),
        AutoMigration (from = 75, to = 76),
        AutoMigration (from = 76, to = 77),
        AutoMigration (from = 77, to = 78),
        AutoMigration (from = 78, to = 79),
        AutoMigration (from = 79, to = 80),
        AutoMigration (from = 80, to = 81),
        AutoMigration (from = 81, to = 82),
        AutoMigration (from = 82, to = 83),
        AutoMigration (from = 83, to = 84),
        AutoMigration (from = 84, to = 85),
        AutoMigration (from = 85, to = 86),
        AutoMigration (from = 86, to = 87),
        AutoMigration (from = 87, to = 88),
        AutoMigration (from = 88, to = 89, spec = DatabaseMigrations.DeleteYallaParticipantColumn::class),
        AutoMigration (from = 89, to = 90),
        AutoMigration (from = 90, to = 91),
        AutoMigration (from = 91, to = 92),
        AutoMigration (from = 92, to = 93),
        AutoMigration (from = 93, to = 94),
        AutoMigration (from = 94, to = 95),
        AutoMigration (from = 95, to = 96),
        AutoMigration (from = 96, to = 97),
        AutoMigration (from = 97, to = 98),
        AutoMigration (from = 98, to = 99),
        AutoMigration (from = 99, to = 100),
    ]
)

@TypeConverters(
    CommonConverters::class,
)
abstract class FlashatDatabase : RoomDatabase() {

    companion object {

        const val DB_VERSION = 100

        @Volatile
        private var INSTANCE: FlashatDatabase? = null

        fun getInstance(context: Context): FlashatDatabase {
            val tempInstance = INSTANCE
            if (tempInstance != null) {
                return tempInstance
            }

            synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    FlashatDatabase::class.java,
                    EntityDescriptions.DB_NAME
                )
                    .addMigrations(*DatabaseMigrations.MIGRATIONS)
                    .fallbackToDestructiveMigration()
                    .build()

                INSTANCE = instance
                return instance
            }
        }

        fun export(c: Context) = getInstance(c).exportDatabase(c)

    }

    fun clearTables() {
        GlobalScope.launch(Dispatchers.IO) {
            <EMAIL>()
        }
    }

    fun exportDatabase(c: Context): String {
//        val sd = File(c.getExternalFilesDir(null)!!.absolutePath + "/backups")
        val sd = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).absolutePath + "/Flashat Backups");
        var success = true
        if (!sd.exists()) {
            success = sd.mkdirs()
        }
        if (success && sd.canWrite()) {
            // Get the Room database storage path using SupportSQLiteOpenHelper
            val currentDBPath = openHelper.writableDatabase.path.orEmpty()
            val backupDBPath = "db-backup-${AccountRepository(c).user.id}-${DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.FORMAT_DATE_TIME_FILE_TS)}.sqlite"      //you can modify the file type you need to export
            val currentDB = File(currentDBPath)
            val backupDB = File(sd, backupDBPath)
            if (currentDB.exists()) {
                try {
                    val src = FileInputStream(currentDB).channel
                    val dst = FileOutputStream(backupDB).channel
                    dst.transferFrom(src, 0, src.size())
                    src.close()
                    dst.close()
                    return backupDB.absolutePath
                } catch (e: IOException) {
                    e.printStackTrace()
                    return e.message?:"Error saving DB"
                }
            }
        }
        return "Error saving DB"
    }


    abstract fun getHuddleDao(): HuddleDao
    abstract fun getPrivateChatDao(): PrivateChatDao
    abstract fun getChatMessageDao(): ChatMessageDao
    abstract fun getFlashDao(): FlashDao
    abstract fun getRemotePagingDao(): RemotePagingDao

    abstract fun getUserDao(): UserDao
    abstract fun getRecentSearchDao(): RecentSearchDao

    abstract fun getBusinessDao(): BusinessDao
    abstract fun getNotificationDao(): NotificationDao

    abstract fun getTransferHistoryDao(): TransferHistoryDao

    abstract fun getPollsDao(): PollsDao

    abstract fun getPodiumDao(): PodiumDao

    abstract fun getYallaGuysDao(): YallaGuysDao

    abstract fun getSubscriptionDao(): SubscriptionDao

    abstract fun getSellHuddleListDao():SellHuddleListDao

    abstract fun getGiftVideoDao():GiftVideoDao

    abstract fun getMyPostatDao(): MyPostatDao

    abstract fun getFeedPostatDao(): FeedPostatDao

    abstract fun getPostCommentDao(): PostCommentDao

    abstract fun getPostReplyDao(): PostReplyDao
}