package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.enums.UserBadge
import com.app.messej.ui.home.publictab.huddles.create.AddParticipantsViewModel.ParticipantsTab
import com.google.gson.annotations.SerializedName

data class AddParticipantsResponse(

    @SerializedName("current_page"  ) var currentPage  : Int?               = null,
    @SerializedName("members"       ) var members      : ArrayList<Members> = arrayListOf(),
    @SerializedName("next_page"     ) var nextPage     : Int?            = null,
    @SerializedName("total_members" ) var totalMembers : Int?               = null
){
    data class Members (
        @SerializedName("is_premium"   ) var isPremium    : Boolean,
        @SerializedName("member_id"    ) var memberId     : Int,
        @SerializedName("name"         ) var name         : String?  = null,
        @SerializedName("relationship" ) var relationship : ParticipantsTab?  = null,
        @SerializedName("thumbnail"    ) var thumbnail    : String?  = null
    ){
        val badge: UserBadge
            get() {
                return if(isPremium) UserBadge.PREMIUM else UserBadge.NONE
            }
    }
}
