package com.app.messej.data.model.api.podium


import com.google.gson.annotations.SerializedName

data class PodiumUsersListResponse(
    @SerializedName("current_page" ) val currentPage : Int,
    @SerializedName("has_next"     ) val hasNext     : <PERSON><PERSON><PERSON>,
    @SerializedName("per_page"     ) val perPage     : Int,
    @SerializedName("total_items"  ) val totalItems  : Int,
    @SerializedName("total_pages"  ) val totalPages  : Int,
    @SerializedName("users"        ) val users: List<PodiumSpeaker>,
)