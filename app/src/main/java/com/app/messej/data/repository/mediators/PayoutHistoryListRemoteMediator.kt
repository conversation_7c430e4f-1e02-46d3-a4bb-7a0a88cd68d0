package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.BusinessAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.BusinessPayoutHistory
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class PayoutHistoryListRemoteMediator (
    private val database: FlashatDatabase,
    private val networkService: BusinessAPIService
) : RemoteMediator<Int, BusinessPayoutHistory>() {
    private val dao = database.getBusinessDao()
    override suspend fun initialize(): InitializeAction {
        return super.initialize()
    }
    override suspend fun load(loadType: LoadType, state: PagingState<Int, BusinessPayoutHistory>): MediatorResult {
        return try {
            val page = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    val page = if (state.pages.isEmpty()) null else state.pages.size + 1
                    // You must explicitly check if the last item is null when
                    // appending, since passing null to networkService is only
                    // valid for initial load. If lastItem is null it means no
                    // items were loaded after the initial REFRESH and there are
                    // no more items to load.
                    page ?: return MediatorResult.Success(
                        endOfPaginationReached = true
                    )
                    page
                }
            }
            Log.d("PHRM", "load: loading page $page")
            // Suspending network load via Retrofit. This doesn't need to be
            // wrapped in a withContext(Dispatcher.IO) { ... } block since
            // Retrofit's Coroutine CallAdapter dispatches on a worker
            // thread.
            val response = networkService.getPayoutHistoryList(page = page)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    response.errorBody()?.let { ErrorResponse.parseError(response = it) }!!
                throw Exception(error.message)
            }
            database.withTransaction {
//                if (loadType == LoadType.REFRESH) {
//                    userDao.deleteByQuery(query)
//                }
                // Insert new users into database, which invalidates the
                // current PagingData, allowing Paging to present the updates
                // in the DB.
                dao.insertPayoutHistory(result)
            }
            MediatorResult.Success(endOfPaginationReached = false)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}