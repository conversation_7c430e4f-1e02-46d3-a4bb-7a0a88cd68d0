package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.LegalAffairsAPIService
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class LegalAffairsListDataSource(
    private val apiService: LegalAffairsAPIService,
    private val recordType: String?,
    private val status: String? = null,
    private val reportingType: String? = null,
    private val countCallBack: ((LegalRecordsResponse?) -> Unit)? = null
) : PagingSource<Int, LegalRecordsResponse.ReportCase>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, LegalRecordsResponse.ReportCase>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, LegalRecordsResponse.ReportCase> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = apiService.getLegalAffairsList(page = currentPage, recordType = recordType, status = status, reportingType = reportingType)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val dataWithoutReportingList = data.copy(reportingList = null)
                countCallBack?.invoke(dataWithoutReportingList)
                val nextKey = if (data.nextPage != true) null else currentPage.inc()
                LoadResult.Page(
                    data = data.reportingList ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

}