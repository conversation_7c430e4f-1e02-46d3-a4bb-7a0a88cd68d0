package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.BroadcastMode
import com.google.gson.annotations.SerializedName

data class BroadcastReadPayload(
    @SerializedName("id"             ) val messageId : String,
    @SerializedName("broadcast_id"   ) val broadcastId   : String,
    @SerializedName("created"        ) val createdTime   : String,
    @SerializedName("subscriber"     ) val subscriber    : Int,
    @SerializedName("broadcaster"    ) val broadcaster   : Int,
    @SerializedName("broadcast_type" ) val broadcastMode : BroadcastMode
): SocketEventPayload()
