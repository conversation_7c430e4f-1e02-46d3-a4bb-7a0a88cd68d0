package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.SuggestedHuddle
import com.app.messej.data.repository.pagingSources.HuddleSuggestionDataSource.Companion.STARTING_KEY
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

class HuddleSuggestionDataSource(private val api: ChatAPIService, private val searchKeyWord: String?) : PagingSource<Int, SuggestedHuddle>() {

    companion object {
        private const val STARTING_KEY = 1
    }
    private var freeOffset = 0
    private var premiumOffset = 0
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, SuggestedHuddle> {
        return try {
            withContext(Dispatchers.IO) {

                val currentPage = params.key ?: STARTING_KEY

                val response = api.getHuddleSuggestion(
                    keyword = searchKeyWord!!, freeOffset = freeOffset, premiumOffset = premiumOffset
                )
                val responseData = mutableListOf<SuggestedHuddle>()
                val result = response.body()?.result
                val data = result?.huddles ?: emptyList()
                freeOffset = result?.freeOffset!!
                premiumOffset = result.premiumOffset!!
                responseData.addAll(data)
                val nextKey = if (result.huddles.isEmpty()) {
                    null
                } else currentPage.plus(1)

                LoadResult.Page(
                    data = result.huddles, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, SuggestedHuddle>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}
