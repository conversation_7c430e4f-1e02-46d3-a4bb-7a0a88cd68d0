package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.HuddleForSale

private const val STARTING_KEY = 1
class SellHuddlesListDataSource(private val api: ChatAPIService) : PagingSource<Int, HuddleForSale>() {


    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, HuddleForSale> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getSellHuddleList(page = currentPage, limit = 50)
            val responseData = mutableListOf<HuddleForSale>()
            val result = response.body()?.result
            val data = result?.huddleForSale ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (!response.body()?.result!!.nextPage!!) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.huddleForSale, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("SellHuddleListResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, HuddleForSale>): Int? {
        return null
    }
}