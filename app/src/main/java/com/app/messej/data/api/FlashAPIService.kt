package com.app.messej.data.api

import com.app.messej.data.model.Category
import com.app.messej.data.model.FlashCommentLikeRequest
import com.app.messej.data.model.FlashCommentPayload
import com.app.messej.data.model.FlashLikeRequest
import com.app.messej.data.model.FlashReplyCommentPayload
import com.app.messej.data.model.PostCommentsRepliesResponse
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.FlashEligibility
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.api.flash.CreateFlashRequest
import com.app.messej.data.model.api.flash.FlashReportedCommentsResponse
import com.app.messej.data.model.api.flash.UserFlashListResponse
import com.app.messej.data.model.api.flash.UserFunctionalityBlockRequest
import com.app.messej.data.model.api.huddles.FlashFeedResponse
import com.app.messej.data.model.api.huddles.FlashSearchResponse
import com.app.messej.data.model.api.postat.PostatCommentResponse
import com.app.messej.data.model.entity.FlashVideo
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface FlashAPIService {

    @GET("flash/feeds")
    @Headers("Accept: application/json")
    suspend fun getFlashFeed(
        @Query("tab") tab: String,
        @Query("page") page: Int? = null,
        @Query("private_offset") privateOffset: Int? = null,
        @Query("public_offset") publicOffset: Int? = null,
        @Query("viewed_offset") viewedOffset: Int? = null,
        @Query("huddle_offset") huddleOffset: Int? = null
    ) : Response<APIResponse<FlashFeedResponse>>

    @GET("flash/category/all")
    @Headers("Accept: application/json")
    suspend fun getFlashCategories() : Response<APIResponse<List<Category>>>

    @POST("flash/create")
    @Headers("Accept: application/json")
    suspend fun createFlash(@Body request: CreateFlashRequest) : Response<APIResponse<FlashVideo>>

    @GET("flash/user/{id}/mylist")
    @Headers("Accept: application/json")
    suspend fun getUserFlashList(@Path("id") id: Int, @Query("page") page: Int? = null) : Response<APIResponse<UserFlashListResponse>>

    @GET("/flash/video_secrets")
    @Headers("Accept: application/json")
    suspend fun getVideoUploadCredentials(): Response<APIResponse<UploadCredentialsResponse>>

    @GET("/flash/signed-cookies")
    @Headers("Accept: application/json")
    suspend fun getVideoPlaybackCookies(): Response<APIResponse<VideoPlaybackCookie>>

    @GET("/flash/archives")
    @Headers("Accept: application/json")
    suspend fun getFlashArchive(@Query("page") page: Int? = null) : Response<APIResponse<UserFlashListResponse>>


    @GET("/flash/search?type=flash")
    @Headers("Accept: application/json")
    suspend fun flashSearch(@Query("keyword") keyword: String? = null, @Query("offset") offset: Int = 0, @Query("page") page: Int? = null) : Response<APIResponse<FlashSearchResponse>>

    @GET("/flash/search?type=user")
    @Headers("Accept: application/json")
    suspend fun flashAccountsSearch(@Query("keyword") keyword: String? = null, @Query("offset") offset: Int = 0, @Query("page") page: Int? = null, @Query("limit") limit: Int = 25) : Response<APIResponse<FlashSearchResponse>>
    @POST("/flash/{id}/comment")
    @Headers("Accept: application/json")
    suspend fun writeFlashComment(@Path("id") id: String, @Body request: FlashCommentPayload): Response<APIResponse<Unit>>

    @POST("/flash/comment/{id}/reply")
    @Headers("Accept: application/json")
    suspend fun writeFlashReplyComment(@Path("id") id: String, @Body request: FlashReplyCommentPayload): Response<APIResponse<Unit>>

    @POST("/flash/{id}/comment/enable")
    @Headers("Accept: application/json")
    suspend fun commentEnable(@Path("id") id: String): Response<APIResponse<Unit>>

    @POST("/flash/{id}/comment/disable")
    @Headers("Accept: application/json")
    suspend fun commentDisable(@Path("id") id: String): Response<APIResponse<Unit>>

    @GET("/flash/{id}/comment")
    @Headers("Accept: application/json")
    suspend fun getFlashComments(@Path("id") id: String, @Query("page") page: Int? = null) : Response<APIResponse<PostatCommentResponse>>

    @GET("/flash/comment/{id}/reply")
    @Headers("Accept: application/json")
    suspend fun getFlashReplyComments(@Path("id") id: String, @Query("page") page: Int? = null) : Response<APIResponse<PostCommentsRepliesResponse>>

    @POST("/flash/{id}/like")
    @Headers("Accept: application/json")
    suspend fun likeFlash(@Path("id") id: String): Response<APIResponse<Unit>>

    @DELETE("/flash/{id}/like")
    @Headers("Accept: application/json")
    suspend fun unlikeFlash(@Path("id") id: String): Response<APIResponse<Unit>>

    @GET("/flash/comment/report")
    @Headers("Accept: application/json")
    suspend fun getReportedComments(@Query("page") page: Int? = null) : Response<APIResponse<FlashReportedCommentsResponse>>

    @DELETE("/flash/comment/{id}")
    @Headers("Accept: application/json")
    suspend fun deleteComment(@Path("id") id: String) : Response<APIResponse<Unit>>

    @POST("/user/func-block/{userId}")
    @Headers("Accept: application/json")
    suspend fun blockuserFunctionality(@Path("userId") id: Int, @Body request: UserFunctionalityBlockRequest): Response<APIResponse<Unit>>

    @DELETE("/flash/comment/{id}")
    @Headers("Accept: application/json")
    suspend fun deleteFlashComment(@Path("id") messageId: String): Response<APIResponse<Unit>>

    @DELETE("/flash/comment/reply/{id}")
    @Headers("Accept: application/json")
    suspend fun deleteFlashCommentReply(@Path("id") messageId: String): Response<APIResponse<Unit>>

    @POST("/flash/{id}/archive")
    @Headers("Accept: application/json")
    suspend fun archiveFlash(@Path("id") flashId: String): Response<APIResponse<Unit>>

    @DELETE("/flash/{id}/archive")
    @Headers("Accept: application/json")
    suspend fun unarchiveFlash(@Path("id") flashId: String): Response<APIResponse<Unit>>

    @DELETE("/flash/{id}")
    @Headers("Accept: application/json")
    suspend fun deleteFlash(@Path("id") flashId: String): Response<APIResponse<Unit>>

    @GET("/flash/{id}")
    @Headers("Accept: application/json")
    suspend fun getFlashDetail(@Path("id") messageId: String): Response<APIResponse<FlashVideo>>

    @GET("/flash/eligibility")
    @Headers("Accept: application/json")
    suspend fun getFlashEligibilityDetails(
        @Query("current_date") currentDate: String
    ): Response<APIResponse<FlashEligibility>>

    @POST("flash/comment/like")
    @Headers("Accept: application/json")
    suspend fun postFlashCommentLike(@Body request: FlashCommentLikeRequest): Response<APIResponse<Unit>>


    @POST("flash/post/like")
    @Headers("Accept: application/json")
    suspend fun postFlashLike(@Body request: FlashLikeRequest): Response<APIResponse<Unit>>

}
