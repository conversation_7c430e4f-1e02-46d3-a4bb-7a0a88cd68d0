package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName
import java.util.Locale

data class Participant (

    @SerializedName("member_id"    ) override val id          : Int,
    @SerializedName("name"         ) override var name        : String,
    @SerializedName("thumbnail"    ) override var thumbnail   : String?  = null,
    @SerializedName("membership"   ) override var membership  : UserType,
    @SerializedName("verified"     ) override var verified    : Boolean = false,
    @SerializedName("admin_status" ) var adminStatus          : AdminStatus?  = null,
    @SerializedName("is_admin"     ) var isAdmin              : Boolean? = null,
    @SerializedName("is_manager"   ) var isManager            : Boolean? = null,
    @SerializedName("relationship" ) var relationship         : String?  = null,
    @SerializedName("role"         ) var role                 : UserRole?  = null,
    @SerializedName("status"       ) var status               : ParticipantStatus,
    @SerializedName("user_blocked" ) var userBlocked          : Boolean? = null,
    @SerializedName("can_comment"  ) var canComment           : Boolean? = null,
    @SerializedName("can_post"     ) var canPost          : Boolean? = null,
    @SerializedName("can_reply"    ) var canReply         : Boolean? = null,
    @SerializedName("citizenship"  ) var userCitizenship      :UserCitizenship?=null
    ): AbstractUser() {

    override val username: String = ""

    override val citizenship: UserCitizenship
        get() = UserCitizenship.default()

    val statusText: String?
        get() {
            return if (status == ParticipantStatus.ADMIN_BLOCKED) return ParticipantStatus.ADMIN_BLOCKED.value
            else if (adminStatus == AdminStatus.INVITED)  return AdminStatus.INVITED.value
            else null
        }

    val participantRelation: String?
        get() = relationship?.uppercase(Locale.ROOT)

    enum class ParticipantStatus(val value: String){
        @SerializedName("user_accepted") USER_ACCEPTED("user_accepted"),
        @SerializedName("admin_blocked") ADMIN_BLOCKED("Blocked"),
        @SerializedName("admin_accepted") ADMIN_ACCEPTED("admin_accepted"),
        @SerializedName("comment_ban") COMMENT_BAN("comment_ban"),
        @SerializedName("post_ban") POST_BAN("post_ban"),
        @SerializedName("reply_ban") REPLY_BAN("reply_ban");
    }

    enum class AdminStatus(val value: String){
        @SerializedName("invited") INVITED("Admin Invited"),
        @SerializedName("cancelled") CANCELLED("cancelled")
    }

    enum class ParticipantsActionTypes {
        @SerializedName("block") BLOCK_HUDDLE_PARTICIPANT,
        @SerializedName("unblock") UNBLOCK_HUDDLE_PARTICIPANT;

        override fun toString(): String {
            return javaClass
                .getField(name)
                .getAnnotation(SerializedName::class.java)
                ?.value ?: ""
        }
    }
}