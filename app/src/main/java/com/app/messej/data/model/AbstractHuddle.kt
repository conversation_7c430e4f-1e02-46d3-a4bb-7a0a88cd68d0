package com.app.messej.data.model

import com.app.messej.data.model.enums.GroupChatStatus
import com.google.gson.annotations.SerializedName

abstract class AbstractHuddle {
    abstract val id           : Int
    abstract val name         : String
    abstract val about        : String?
    abstract val thumbnail    : String?
    abstract val groupPhoto   : String?
    abstract val category     : String?

    abstract val totalMembers : Int
    abstract val isPrivate    : Boolean
    abstract val requestToJoin : Boolean
    abstract val managerPremium:Boolean?
    abstract val status: HuddleStatus
    abstract val userStatus   : HuddleUserStatus


    val isOpenForAll: Boolean
        get() = !requestToJoin

    val huddleStatus: GroupChatStatus
        get() {
            return if(status == HuddleStatus.ADMIN_BLOCKED) GroupChatStatus.ADMIN_BLOCKED
            else {
                when(userStatus) {
                    HuddleUserStatus.JOIN_REQUEST_DECLINED, HuddleUserStatus.NOT_JOINED, HuddleUserStatus.INVITE_DECLINED -> if(requestToJoin) GroupChatStatus.REQUEST_TO_JOIN else GroupChatStatus.OPEN_TO_JOIN
                    HuddleUserStatus.JOIN_REQUEST_SENT -> GroupChatStatus.JOIN_REQUESTED
                    HuddleUserStatus.INVITE_RECEIVED -> GroupChatStatus.INVITED
                    HuddleUserStatus.RESTRICTED -> GroupChatStatus.RESTRICTED
                    HuddleUserStatus.BLOCKED_BY_ADMIN, HuddleUserStatus.JOIN_REQUEST_BLOCKED -> GroupChatStatus.BLOCKED
                    HuddleUserStatus.INVITE_ACCEPTED, HuddleUserStatus.JOIN_REQUEST_ACCEPTED -> GroupChatStatus.ACTIVE
                    HuddleUserStatus.INVITE_BLOCKED -> GroupChatStatus.BLOCKED
                    else -> GroupChatStatus.UNKNOWN
                }
            }
        }

    val canReadChats: Boolean
        get() {
            return isOpenForAll || huddleStatus == GroupChatStatus.ACTIVE
        }

    val canSendChats: Boolean
        get() {
            return huddleStatus == GroupChatStatus.ACTIVE
        }

    val canReceiveNewChats: Boolean
        get() {
            return huddleStatus!= GroupChatStatus.BLOCKED && huddleStatus!= GroupChatStatus.ADMIN_BLOCKED
        }

    val isParticipant: Boolean
        get() {
            return userStatus == HuddleUserStatus.INVITE_ACCEPTED || userStatus == HuddleUserStatus.JOIN_REQUEST_ACCEPTED
        }

    enum class HuddleStatus {
        @SerializedName("active") ACTIVE,
        @SerializedName("inactive") INACTIVE,
        @SerializedName("admin_blocked") ADMIN_BLOCKED;
    }

    enum class HuddleAdminStatus {
        @SerializedName("invited") INVITED,
        @SerializedName("accepted") ACCEPTED,
        @SerializedName("declined") DECLINED,
        @SerializedName("ignored") IGNORED,
        @SerializedName("cancelled") CANCELLED;
    }

    enum class HuddleUserStatus {
        @SerializedName("invited") INVITE_RECEIVED,
        @SerializedName("user_declined") INVITE_DECLINED,
        @SerializedName("user_accepted") INVITE_ACCEPTED,
        @SerializedName("user_blocked") INVITE_BLOCKED,
        //
        @SerializedName("requested") JOIN_REQUEST_SENT,
        @SerializedName("request_blocked") JOIN_REQUEST_BLOCKED,
        @SerializedName("admin_declined") JOIN_REQUEST_DECLINED,
        @SerializedName("admin_accepted") JOIN_REQUEST_ACCEPTED,
        //
        @SerializedName("admin_blocked" ) BLOCKED_BY_ADMIN,
        @SerializedName("huddleRestricted") RESTRICTED,
        //
        @SerializedName("not_joined") NOT_JOINED,
        @SerializedName("cancelled") CANCELLED
    }

    enum class HuddleAction {
        @SerializedName("cancel") CANCEL
    }
}