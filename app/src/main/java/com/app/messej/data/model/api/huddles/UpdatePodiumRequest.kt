package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.api.huddles.CreatePodiumRequest.PodiumTypeEntry
import com.app.messej.data.model.enums.PodiumKind
import com.google.gson.annotations.SerializedName

data class UpdatePodiumRequest(
    @SerializedName("about") val about: String?=null,
    @SerializedName("category_id") val categoryId: Int?=null,
    @SerializedName("id") val id: String,
    @SerializedName("name") val name: String?=null,
    @SerializedName("type") val type: PodiumTypeEntry,
    @SerializedName("temp_id") val tempId:String?=null,
    @SerializedName("live") val live: Boolean,
    @SerializedName("use_manager_profile_pic") val useProfileDp: Boolean? = false,
    @SerializedName("kind") val kind: PodiumKind,
    @SerializedName("audience_fee") val audienceFee: Int? = null,
    @SerializedName("stage_fee") val stageFee: Int? = null,
    @SerializedName("required_user_rating") val requiredUserRating: String? = null,
    @SerializedName("required_rating_to_comment") val requiredRatingToComment: String? = null,
    @SerializedName("required_rating_to_speak") val requiredRatingToSpeak: String? = null,
    @SerializedName("joining_fee") val joiningFee: String? = null,
)
