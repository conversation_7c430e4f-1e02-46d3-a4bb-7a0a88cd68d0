package com.app.messej.data.model.api.auth

import com.google.gson.annotations.SerializedName

data class UnreadItemsResponse(
    @SerializedName("unread_stars_messages"         ) var unreadStarMessages: Int = 0,
    @SerializedName("unread_private_chat_requests"  ) var unreadPrivateChatRequests: Int = 0,
    @SerializedName("unread_private_chats"          ) var unreadPrivateChats: Int = 0,
    @SerializedName("unread_private_huddles"        ) var unreadPrivateHuddles: Int = 0,
    @SerializedName("my_huddles_count"              ) var myHuddlesCount: Int = 0,
    @SerializedName("my_admin_huddles_count"        ) var adminHuddlesCount: Int = 0,
    @SerializedName("my_joined_huddles_count"       ) var joinedHuddlesCount: Int = 0,
    @SerializedName("maidan_coins"                  ) var maidanCoins: Float? = 0f,
    @SerializedName("maidan_count"                  ) var maidanCount: Int? = 0,
    @SerializedName("my_live_maidan"                ) var myLiveMaidan: String? = null,
    @SerializedName("has_pending_fines"             ) var hasPendingFines: Boolean? = false,
    )
