package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@OptIn(ExperimentalPagingApi::class)
class FlashCommentRemoteMediator(
    private val flashId: String,
    private val database: FlashatDatabase,
    private val networkService: FlashAPIService,
    private val commentCountCallback: (Int) -> Unit
) : RemoteMediator<Int, PostCommentWithReplies>() {

    private val dao = database.getPostCommentDao()
    private val remoteKeyDao = database.getRemotePagingDao()
    private val tableKey =  "${EntityDescriptions.TABLE_POSTAT_COMMENTS}-$flashId"

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, PostCommentWithReplies>
    ): MediatorResult {
        return try {
            // For parent comments only, we use page-based pagination
            val page = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }
                    
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(endOfPaginationReached = true)
                    }
                    
                    remoteKey.nextPageInt
                }
            }

            // Fetch Flash comments using FlashAPIService
            val response = networkService.getFlashComments(id = flashId, page = page)

            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                throw Exception(error.message)
            }

            val comments = result.postatComments
            commentCountCallback.invoke(result.totalCount)

            withContext(Dispatchers.IO) {
                database.withTransaction {
                    if (loadType == LoadType.REFRESH) {
                        // Clear Flash comments for this flash
                        dao.deletePostComments(flashId, CommentType.FLASH)
                        remoteKeyDao.deleteByQuery(tableKey)
                    }

                    // Convert PostCommentItem to BasePostComment with Flash type
                    val basePostComments = comments.map { comment ->
                        comment.copy(
                            postatId = flashId,
                            type = CommentType.FLASH
                        )
                    }

                    // Sanitize sender details
                    basePostComments.forEach { it.sanitize() }
                    
                    // Insert Flash comments
                    dao.insertPostComments(basePostComments)
                    
                    
                    val hasNext = result.hasNext ?: false
                    remoteKeyDao.insertOrReplace(
                        RemotePagingKey(tableKey, if (hasNext) (page + 1).toString() else null)
                    )
                }
            }

            MediatorResult.Success(endOfPaginationReached = result.hasNext == false)
        } catch (e: Exception) {
            Log.e("FlashCommentMediator", "Error loading Flash comments", e)
            MediatorResult.Error(e)
        }
    }
}
