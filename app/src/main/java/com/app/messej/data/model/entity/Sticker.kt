package com.app.messej.data.model.entity


import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Entity(
    tableName = EntityDescriptions.TABLE_HUDDLE_STICKERS
)
@Parcelize
data class Sticker(
    @SerializedName("category_name" )  @ColumnInfo(name = "category_name"       )  val categoryName: String? = "",
    @SerializedName("id"            )  @ColumnInfo(name = COLUMN_EMOJI_ID              )  @PrimaryKey(autoGenerate = false) val id: Int? = 0,
    @SerializedName("media_url"     )  @ColumnInfo(name = "media_url"           )  val mediaUrl: String? = "",
    @SerializedName("s3_key"        )  @ColumnInfo(name = "s3_key"              )  val s3Key: String? = "",
    @SerializedName("time_created"  )  @ColumnInfo(name = "time_created"        )  val timeCreated: String? = "",
    @SerializedName("time_updated"  )  @ColumnInfo(name = "time_updated"        )  val timeUpdated: String? = "",
    @SerializedName("unicode"       )  @ColumnInfo(name = "unicode"             )  val unicode: String? = ""
): Parcelable {
    companion object{
        const val COLUMN_EMOJI_ID = "id"
    }

    val mediaMeta: MediaMeta
        get() = MediaMeta(
            s3Key = s3Key.orEmpty(),
            mediaType = MediaType.IMAGE,
            mediaName = categoryName,
            mimeType = "image/jpeg",
            unicode = unicode,
            thumbnail = mediaUrl
        )
}