package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.PrivateMessagesSuggestionResponse
import com.app.messej.data.model.enums.PrivateMessageSuggestionType
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PrivateMessageSuggestionDataSource(private val api: ChatAPIService,
                                         private val accountRepo: AccountRepository, private val suggestionType: PrivateMessageSuggestionType, private val userType: String = ""): PagingSource<Pair<Int, PrivateMessageSuggestionType>, PrivateMessagesSuggestionResponse.User>() {

    override suspend fun load(params: LoadParams<Pair<Int, PrivateMessageSuggestionType>>): LoadResult<Pair<Int,PrivateMessageSuggestionType>, PrivateMessagesSuggestionResponse.User> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: Pair(1, PrivateMessageSuggestionType.TWO_WAY_FOLLOWED)
                val response = api.newMessageSuggestions(
                    suggestionType = currentPage.second, page = currentPage.first
                )
                val responseData = mutableListOf<PrivateMessagesSuggestionResponse.User>()
                Log.i("PMSDS", "load: $responseData")
                val result = response.body()?.result ?: throw Exception("Result is null")
                val data = result.suggestions

                if (result.suggestionType > PrivateMessageSuggestionType.DEARS) {
                    data.forEach {
                        it.isSuggestion = true
                    }
                }

                if (currentPage.first == result.page) responseData.addAll(data)

                val nextKey =if(currentPage.first != result.page) null
                else if (currentPage.second == result.suggestionType){
                    Pair(currentPage.first.plus(1), currentPage.second)
                } else {
                    Pair(1, result.suggestionType)
                }

                LoadResult.Page(
                    data = response.body()?.result!!.suggestions,
                    nextKey = nextKey,
                    prevKey = null
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Pair<Int, PrivateMessageSuggestionType>, PrivateMessagesSuggestionResponse.User>): Pair<Int, PrivateMessageSuggestionType>? {
        return null
    }
}