package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.entity.PublicHuddle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1

class HuddleRequestInvitesDataSource(private val api: ChatAPIService) : PagingSource<Int, PublicHuddle>(){

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PublicHuddle> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getHuddleRequestAndInvites(
                   count = false
                )
                val responseData = mutableListOf<PublicHuddle>()
                val result = response.body()?.result
                val data = result?.huddleParticipant ?: emptyList()
                responseData.addAll(data)
                val nextKey = if (result?.nextPage == false) null else currentPage.plus(1)
                LoadResult.Page(
                    data = data,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }


    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
    override fun getRefreshKey(state: PagingState<Int, PublicHuddle>): Int? {
     return null
    }
}
