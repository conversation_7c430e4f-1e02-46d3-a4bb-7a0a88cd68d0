package com.app.messej.data.repository

import android.content.Context
import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.room.withTransaction
import com.amazonaws.mobile.config.AWSConfiguration
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.api.external.ExternalServiceGenerator
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessage.SendStatus
import com.app.messej.data.model.AbstractChatMessageWithMedia
import com.app.messej.data.model.AbstractMedia
import com.app.messej.data.model.AttachLocation
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.api.ReportCategoryResponse
import com.app.messej.data.model.api.ReportToManagerRequest
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.huddles.ImageURLResponse
import com.app.messej.data.model.entity.BroadcastChatMessageWithMedia
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.entity.HuddleReportedMessage
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.entity.PrivateChatMessageWithMedia
import com.app.messej.data.model.entity.Sticker
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.socket.HuddleChatMessagePayload
import com.app.messej.data.repository.mediators.StickerRemoteMediator
import com.app.messej.data.repository.pagingSources.BlockedPrivateMessageDataSource
import com.app.messej.data.repository.pagingSources.StickerDataSource
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.socket.repository.BroadcastEventRepository
import com.app.messej.data.socket.repository.HuddleChatEventRepository
import com.app.messej.data.socket.repository.PrivateChatEventRepository
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.MediaUtils.asUri
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.data.utils.VideoEncoderUtil
import com.github.f4b6a3.uuid.UuidCreator
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Repository to handle media storage
 */
class ChatRepository(private var mContext: Context): BaseMediaUploadRepository(mContext) {

    companion object {
        private const val TAG = "CHATREPO"
    }

    suspend fun saveOfflineMedia(messageId: String, meta: MediaMeta, file: File): OfflineMedia = withContext(Dispatchers.IO) {
        val media = OfflineMedia(
            messageId = messageId, key = meta.s3Key, path = file.absolutePath, mediaType = meta.mediaType, name = meta.mediaName?:file.name
        )
        db.getChatMessageDao().insertMedia(media)
        return@withContext media
    }

    suspend fun getMediaDownloadURL(msg: AbstractChatMessage): ResultOf<ImageURLResponse> = withContext(Dispatchers.IO) {
        val service = APIServiceGenerator.createService(ChatAPIService::class.java)
        val response = when (msg) {
            is HuddleChatMessage, is PrivateChatMessage, is HuddleReportedMessage -> service.getMediaSignedUrl(msg.messageId, msg.roomId)
            is BroadcastMessage -> service.getBroadcastMediaSignedUrl(msg.messageId, msg.broadcastMode, msg.broadcaster)
            else -> service.getMediaSignedUrl(msg.messageId, msg.roomId)
        }
        return@withContext APIUtil.handleResponse(response)
    }

    suspend fun downloadChatMedia(msg: AbstractChatMessage): ResultOf<OfflineMedia> = withContext(Dispatchers.IO) {
        msg.mediaMeta?.let { meta ->
            try {
                if(meta.mediaType==MediaType.VIDEO) {
                    return@withContext ResultOf.getError("Wrong method called for downloading Video")
                } else {
                    when (val result = getMediaDownloadURL(msg)) {
                        is ResultOf.Success -> {
                            val fileResponse = ExternalServiceGenerator.createAwsS3Service().downloadMedia(result.value.signedUrl).execute()
                            fileResponse.body()?.use {
                                val file = MediaUtils.saveDownloadedFile(mContext, it, meta.mediaName!!, meta.mediaType)
                                val media = saveOfflineMedia(msg.messageId,meta,file)
                                return@withContext ResultOf.Success(media)
                            } ?: throw Exception("Failed to download media")
                        }

                        is ResultOf.APIError -> return@withContext result
                        else -> throw Exception()
                    }
                }
            } catch (e: Exception) {
                return@withContext ResultOf.getError(e)
            }
        }
        return@withContext ResultOf.getError("Unsupported media type")
    }

    private fun getChatMessage(huddleId:Int,huddleType: HuddleType, message: String, replyTo: ReplyTo?, location: AttachLocation?,color: ChatTextColor?): HuddleChatMessage {
        val uuid = UuidCreator.getTimeBased().toString()
        var dbMessage = HuddleChatMessage(
            messageId = uuid,
            roomId = HuddleChatMessage.prefixHuddleId(huddleId),
            rawMessage = message,
            createdTime = DateTimeUtils.getZonedDateTimeNowAsString(),
            huddleId = HuddleChatMessage.prefixHuddleId(huddleId),
            huddleType = huddleType,
            sender = accountRepo.user.id,
            senderDetails = SenderDetails.from(accountRepo.user),
            replyTo = replyTo,
            chatTextColor = color
        )
        location?.let { loc ->
            dbMessage = dbMessage.copy(
                rawMessage = loc.encode(), internalMessageType = AbstractChatMessage.MessageType.LOCATION
            )
        }
        return dbMessage
    }

    suspend fun getPrivateChatMessage(id:String)= db.getChatMessageDao().getPrivateChatMessage(id)

    suspend fun getPrivateGroupMessage(id:String)= db.getChatMessageDao().getHuddleChatMessage(id)

    suspend fun getPrivateHuddleMessage(id:String)= db.getChatMessageDao().getHuddleChatMessage(id)


    suspend fun insertHuddlePostMessage(message: HuddleChatMessage, media: OfflineMedia?): HuddleChatMessageWithMedia = withContext(Dispatchers.IO) {
        db.withTransaction {
            media?.let {
                db.getChatMessageDao().insertMedia(it)
            }
            insertNewChatMessage(message)
            db.getHuddleDao().setHuddleLastReadMessage(message.huddleIdInt, message.messageId)
        }
        return@withContext db.getChatMessageDao().getHuddleChatMessageWithMedia(message.messageId)!!
    }

    /**
     * Creates a chat message entry.
     * @param huddle: The huddle object
     * @param message: the text/caption
     * @param med: Attach media if any exists
     * @param replyTo: the reply message if applicable
     * @param status: the send status of the message. This is mostly left default except for Huddle posts - where this method is called after the message has been sent.
     */
    suspend fun createChatMessage(
        huddleId: Int,
        huddleType: HuddleType,
        message: String,
        med: TempMedia? = null,
        replyTo: ReplyTo? = null,
        location: AttachLocation? = null,
        status: SendStatus = SendStatus.PENDING,
        color: ChatTextColor? = null,
    ): HuddleChatMessageWithMedia {
        val dbMessage = getChatMessage(huddleId, huddleType, message, replyTo, location, color)
        med?.let {
            val meta = createChatMedia(it, dbMessage.messageId) { name -> APIUtil.getHuddleChatMediaUploadKey(huddleId, name) }
            dbMessage.mediaMeta = meta
            dbMessage.media = meta.s3Key
        }
        dbMessage.sendStatus = status
        db.withTransaction {
            insertNewChatMessage(dbMessage)
            db.getHuddleDao().setHuddleLastReadMessage(dbMessage.huddleIdInt, dbMessage.messageId)
        }
        return db.getChatMessageDao().getHuddleChatMessageWithMedia(dbMessage.messageId)!!
    }

    suspend fun createChatMessageForward(
        huddleId: Int,
        huddleType: HuddleType,
        message: AbstractChatMessage,
        status: SendStatus = SendStatus.PENDING,
    ): HuddleChatMessageWithMedia {
        val dbMessage = getChatMessage(huddleId, huddleType, message.rawMessage.orEmpty(), message.replyTo, null, message.chatTextColor).apply {
            mediaMeta = message.mediaMeta
            media = message.media
            sendStatus = status
            forwardId = message.messageId
        }

        db.withTransaction {
            insertNewChatMessage(dbMessage)
            db.getHuddleDao().setHuddleLastReadMessage(dbMessage.huddleIdInt, dbMessage.messageId)
        }
        return db.getChatMessageDao().getHuddleChatMessageWithMedia(dbMessage.messageId)!!
    }

    private fun getChatMessage(roomId:String,receiver:Int,  message: String, replyTo: ReplyTo?, location: AttachLocation?,color: ChatTextColor?,forwardId:String?=null): PrivateChatMessage {

        val uuid = UuidCreator.getTimeBased().toString()
        var dbMessage = PrivateChatMessage(
            roomId = roomId,
            messageId = uuid,
            rawMessage = message,
            createdTime = DateTimeUtils.getZonedDateTimeNowAsString(),
            sender = accountRepo.user.id,
            receiver = receiver,
            userId = receiver,
            replyTo = replyTo,
            chatTextColor = color
        )
        location?.let { loc ->
            Log.w(TAG, "createChatMessage: setting message as location: ${loc.encode()}")
            dbMessage = dbMessage.copy(
                rawMessage = loc.encode(),
                internalMessageType = AbstractChatMessage.MessageType.LOCATION
            )
        }

        return dbMessage

    }

    suspend fun createChatMessage(
        chat: PrivateChat,
        message: String,
        med: TempMedia? = null,
        replyTo: ReplyTo? = null,
        location: AttachLocation? = null,
        color: ChatTextColor? = null,
    ): PrivateChatMessageWithMedia {

        val dbMessage = getChatMessage(chat.roomId, chat.receiver, message, replyTo, location, color)

        dbMessage.sendStatus = SendStatus.PENDING
        med?.let {
            val meta = createChatMedia(it, dbMessage.messageId) { name -> APIUtil.getPrivateChatMediaUploadKey(chat.id, name) }
            dbMessage.mediaMeta = meta
            dbMessage.media = meta.mediaName
        }
        Log.w(TAG, "createChatMessage: inserting message: ${dbMessage}")
        db.withTransaction {
            insertNewChatMessage(dbMessage)
        }
        return db.getChatMessageDao().getPrivateChatMessageWithMedia(dbMessage.messageId)!!
    }

    suspend fun createChatMessageForward(
        chat: PrivateChat,
        message: AbstractChatMessage,
        status: SendStatus = SendStatus.PENDING,
    ): PrivateChatMessageWithMedia {
        val dbMessage = getChatMessage(chat.roomId, chat.receiver, message.rawMessage.orEmpty(), message.replyTo, null, message.chatTextColor).apply {
            mediaMeta = message.mediaMeta
            media = message.media
            sendStatus = status
            forwardId = message.messageId
        }

        db.withTransaction {
            insertNewChatMessage(dbMessage)
        }
        return db.getChatMessageDao().getPrivateChatMessageWithMedia(dbMessage.messageId)!!
    }

    suspend fun createBroadcastMessage(mode: BroadcastMode, message: String, med: TempMedia?, location: AttachLocation?, color:ChatTextColor?): BroadcastChatMessageWithMedia {
        val uuid = UuidCreator.getTimeBased().toString()
        val userId = accountRepo.user.id
        var dbMessage = BroadcastMessage(
            broadcastMode = mode,
            messageId = uuid,
            broadcastId = uuid,
            rawMessage = message,
            createdTime = DateTimeUtils.getZonedDateTimeNowAsString(),
            broadcaster = userId,
            subscriber = userId,
            chatTextColor = color
        )
        location?.let { loc ->
            dbMessage = dbMessage.copy(
                rawMessage = loc.encode(),
                internalMessageType = AbstractChatMessage.MessageType.LOCATION
            )
        }
        dbMessage.sendStatus = SendStatus.PENDING
        med?.let {
            val meta = createChatMedia(med,uuid) { name -> APIUtil.getBroadcastMediaUploadKey(userId, name) }
            dbMessage.mediaMeta = meta
            dbMessage.media = meta.mediaName
        }
        db.withTransaction {
            insertNewChatMessage(dbMessage)
        }
        return db.getChatMessageDao().getBroadcastMessageWithMedia(uuid)!!
    }

    suspend fun checkIfCanForwardBroadcast(message: BroadcastMessage): Boolean = withContext(Dispatchers.IO) {
        val existing = db.getChatMessageDao().getBroadcastMessageWithMedia(message.messageId)?: return@withContext false
        //Media has not been downloaded
        return@withContext !(existing.message.hasMedia && existing.offlineMedia==null)
    }

    suspend fun cloneBroadcastMessage(mode: BroadcastMode, message: BroadcastMessage): BroadcastChatMessageWithMedia = withContext(Dispatchers.IO) {
        val uuid = UuidCreator.getTimeBased().toString()
        val userId = accountRepo.user.id
        val dbMessage = message.copy(
            broadcastMode = mode,
            messageId = uuid,
            broadcastId = uuid,
            broadcaster = userId,
        )
        dbMessage.sendStatus = SendStatus.PENDING

//        val existing = db.getChatMessageDao().getBroadcastMessageWithMedia(message.messageId)
//        if (existing!= null && existing.message.hasMedia && existing.offlineMedia==null) {
//            //Media has not been downloaded
//            throw Exception("Media not downloaded")
//        }
//        val meta = cloneChatMedia(existing?.offlineMedia,uuid) { name -> APIUtil.getBroadcastMediaUploadKey(userId, name) }
//        meta?.let {
//            dbMessage.mediaMeta = it
//            dbMessage.media = it.mediaName
//        }
        db.withTransaction {
            insertNewChatMessage(dbMessage)
        }
        return@withContext db.getChatMessageDao().getBroadcastMessageWithMedia(uuid)!!
    }

    private suspend fun getChatMedia(med: TempMedia, uuid: String, s3KeyProvider: (String) -> String): OfflineMedia {
        val file = when (med.mediaType) {
            MediaType.IMAGE -> MediaUtils.storeImageFile(mContext, med.path, uuid)
            MediaType.AUDIO -> MediaUtils.storeAudioFile(mContext, med.path, uuid)
            MediaType.VIDEO -> MediaUtils.storeVideoFile(mContext, med.path, uuid)
            MediaType.DOCUMENT -> MediaUtils.storeDocumentFile(mContext,med.sourceUri!!)
        }
        val s3Key = s3KeyProvider(file.name)
        return OfflineMedia(
            messageId = uuid, key = s3Key, path = file.absolutePath, mediaType = med.mediaType, name = file.name
        )
    }

    private suspend fun createHuddlePostMedia(huddleId: Int, med: TempMedia): OfflineMedia {
        val uuid = UuidCreator.getTimeBased().toString()
        return getChatMedia(med,uuid) { name -> APIUtil.getHuddleChatMediaUploadKey(huddleId, name) }
    }

    fun forwardHuddle(payLoad: HuddleChatMessagePayload, huddleType: HuddleType)= HuddleChatEventRepository.sendHuddleMessage(payLoad, huddleType)

    private suspend fun createChatMedia(med: TempMedia, uuid: String, s3KeyProvider: (String) -> String): MediaMeta {
        val media = getChatMedia(med, uuid, s3KeyProvider)
        Firebase.crashlytics.log("stored media (${med.mediaType}) file to ${media.path}")
        val meta = generateMediaMeta(media,med.sourceFileName)
        media.uploadState = MediaUploadState.Pending
        db.getChatMessageDao().insertMedia(media)
        return meta
    }

    suspend fun insertNewChatMessage(msg: HuddleChatMessage, oldId: String? = null) = withContext(Dispatchers.IO) {
        if (!accountRepo.loggedIn) return@withContext
        db.withTransaction {
            db.getHuddleDao().apply {
                oldId?.let {
                    db.getChatMessageDao().deleteHuddleChatMessage(it,false)
                    db.getChatMessageDao().updateMediaMessageId(oldId,msg.messageId)

                    //TOD might cause problems when sending late
                    setHuddleLastReadMessage(msg.huddleIdInt,msg.messageId)
                }
                if (msg.sender == accountRepo.user.id) {
                    setHuddleLastReadMessage(msg.huddleIdInt,msg.messageId)
                }
                Log.d("LM", "insertNewChat: setting lastMessage for ${msg.huddleIdInt}")
                db.getChatMessageDao().insertChat(msg)
                val huddle = getHuddle(msg.huddleIdInt)?: return@apply
                huddle.lastMessage = msg
                updateHuddle(huddle)
            }
        }
    }

    suspend fun insertNewChatMessage(msg: PrivateChatMessage, oldId: String? = null) = withContext(Dispatchers.IO) {
        db.withTransaction {
            db.getPrivateChatDao().apply {
                oldId?.let {
                    db.getChatMessageDao().deletePrivateChatMessage(it)
                    db.getChatMessageDao().updateMediaMessageId(oldId,msg.messageId)
                }
                Log.d("LM", "insertNewChat: setting lastMessage for ${msg.roomId}")
                db.getChatMessageDao().insertChat(msg)
                val chat = getPrivateChat(msg.roomId)?: return@withTransaction
                chat.lastMessage = msg
                update(chat)
                setPrivateChatActivity(msg.roomId,msg.createdTime)
            }
        }
    }

    suspend fun insertNewChatMessage(msg: BroadcastMessage, oldId: String? = null) = withContext(Dispatchers.IO) {
        db.withTransaction {
            oldId?.let {
                db.getChatMessageDao().deleteBroadcastMessage(it)
                db.getChatMessageDao().updateMediaMessageId(oldId,msg.messageId)
            }
            db.getChatMessageDao().insertBroadcast(msg)
        }
    }

    fun generateMediaMeta(media: AbstractMedia, srcFileName: String? = null): MediaMeta {
        val meta = MediaMeta(
            mediaType = media.mediaType,
            mediaName = media.name,
            mimeType = MediaUtils.getMIME(media.file),
            s3Key = if(media is OfflineMedia) media.key else "NA",
            documentDisplayName = if (media.mediaType==MediaType.DOCUMENT) srcFileName else null
        )
        when(media.mediaType) {
            MediaType.IMAGE -> {
                val res = MediaUtils.getImageResolution(media.file)
                meta.mediaWidth = res.width.toString()
                meta.mediaHeight = res.height.toString()
            }
            MediaType.VIDEO -> {
                media.path.asUri().let {
                    val res = MediaUtils.getVideoResolution(it)
                    meta.mediaWidth = res.width.toString()
                    meta.mediaHeight = res.height.toString()
                }
                meta.formattedSize = MediaUtils.getFileSize(media.file)
                meta.seconds = MediaUtils.getDuration(media.file)
            }
            MediaType.AUDIO -> {
                meta.seconds = MediaUtils.getDuration(media.file)
            }

            MediaType.DOCUMENT -> {
                meta.formattedSize = MediaUtils.getFileSize(media.file)
            }
        }
        return meta
    }

    suspend fun resetHuddleUnreadCount(id: Int) = db.getHuddleDao().updateUnreadCount(id, 0)

    suspend fun sendGroupChatMessage(msg: HuddleChatMessageWithMedia): ResultOf<Unit> {
        if (msg.mediaIsUploading) return ResultOf.getError("Media is Already uploading")
        Log.w("CMW", "sendGroupChatMessage: needsMediaUpload: ${msg.needsMediaUpload}")
        Log.w("CMW", "sendGroupChatMessage: media: ${msg.offlineMedia}")
        Log.w("CMW", "sendGroupChatMessage: message: ${msg.message}")
        if(msg.needsMediaUpload) {
            val result = uploadMedia(msg)
            Log.w("CMW", "sendGroupChatMessage: uploadMedia result: $result")
            if(result !is ResultOf.Success) {
                return result
            }
        }
        val chat = db.withTransaction {
            val hm = db.getChatMessageDao().getHuddleChatMessage(msg.message.messageId)
            return@withTransaction if (hm?.sendStatus == SendStatus.PENDING) {
                hm.sendStatus = SendStatus.SENDING
                db.getChatMessageDao().updateChat(hm)
                hm
            } else null
        } ?: return ResultOf.getError("Chat is already sending. exiting...")
        val sent = HuddleChatEventRepository.sendHuddleMessage(chat.socketpayload, chat.huddleType)
        return if(sent) ResultOf.Success(Unit) else {
            db.getChatMessageDao().updateHuddleChatStatus(chat.messageId,SendStatus.PENDING)
            ResultOf.getError("Count not send socket event")
        }
    }

    suspend fun sendHuddlePost(
        huddleId: Int,
        huddleType: HuddleType,
        text: String,
        tempMed: TempMedia? = null,
        replyTo: ReplyTo? = null,
        color: ChatTextColor? = null,
        postToEdit: HuddleChatMessage? = null,
        postVideoToFlash: Boolean = false,
        onGenerateMedia: (OfflineMedia) -> Unit,
    ): ResultOf<HuddleChatMessage> {
        return try {
            val isEdit = postToEdit != null
            var media: OfflineMedia? = null
            Log.w("HPVD", "med: $tempMed")
            if (tempMed != null) {
                when (tempMed) {
                    is TempMedia.SavedMedia, is TempMedia.StickerMedia -> {}
                    else -> {
                        Log.w("HPVD", "creating media")
                        media = createHuddlePostMedia(huddleId, tempMed)
                        onGenerateMedia.invoke(media)
                        val result = when (media.mediaType) {
                            MediaType.IMAGE, MediaType.AUDIO -> prepareAndUploadMedia(media)
                            MediaType.VIDEO, MediaType.DOCUMENT -> prepareAndUploadMultipartMedia(media)
                        }
                        if (result !is ResultOf.Success) MediaUtils.moveFiles(media.file, tempMed.file)
                        when (result) {
                            is ResultOf.Success -> {}
                            is ResultOf.Error -> return ResultOf.Error(result.exception)
                            is ResultOf.APIError -> return ResultOf.APIError(result.error, result.code)
                        }
                    }
                }
            }

            Log.w("HPVD", "media: $media")
            val message = if (postToEdit != null) {
                val meta = if (media != null) generateMediaMeta(media, tempMed?.sourceFileName)
                else if (tempMed is TempMedia.SavedMedia) postToEdit.mediaMeta
                else if (tempMed is TempMedia.StickerMedia) tempMed.meta
                else null

                val mediaString = when (tempMed) {
                    is TempMedia.StickerMedia -> meta?.thumbnail
                    is TempMedia.SavedMedia -> postToEdit.media
                    else -> meta?.s3Key
                }

                postToEdit.copy(
                    rawMessage = text, replyTo = replyTo, mediaMeta = meta, media = mediaString, chatTextColor = color
                )
            } else {
                val dbMessage = getChatMessage(huddleId, huddleType, text, replyTo, color = color, location = null)
                media?.let {
                    val meta = generateMediaMeta(it, tempMed?.sourceFileName)
                    dbMessage.mediaMeta = meta
                    dbMessage.media = meta.s3Key
                }

                if (tempMed is TempMedia.StickerMedia) {
                    dbMessage.mediaMeta = tempMed.meta
                    dbMessage.media = tempMed.meta.thumbnail
                    dbMessage.internalMessageType = AbstractChatMessage.MessageType.STICKER
                }
                dbMessage
            }
            Log.w("HPVD", "check mentions in $text: ${UserInfoUtil.hasMentions(text)}")
            message.hasMention = UserInfoUtil.hasMentions(text)
            val service = APIServiceGenerator.createService(ChatAPIService::class.java)
            val socketPayload = message.socketpayload.apply {
                this.publishToFlash = if (media?.mediaType == MediaType.VIDEO) postVideoToFlash else null
            }
            val resp = if (isEdit) service.editHuddlePost(huddleId, socketPayload)
            else service.sendHuddlePost(huddleId, socketPayload)
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                val dbMessage = result.value
                media?.messageId = dbMessage.messageId
                dbMessage.huddleType = HuddleType.PUBLIC
                dbMessage.huddleId = huddleId.toString()
                dbMessage.prefixHuddleId()
                dbMessage.roomId = dbMessage.huddleId
                dbMessage.replyTo = replyTo
                insertHuddlePostMessage(dbMessage, media)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun sendPrivateChatMessage(msg: PrivateChatMessageWithMedia): ResultOf<Unit> {
        if (msg.mediaIsUploading) return ResultOf.getError("Media is Already uploading")
        if(msg.needsMediaUpload) {
            val result = uploadMedia(msg)
            if(result !is ResultOf.Success) return result
        }
        val chat = db.withTransaction {
            val hm = db.getChatMessageDao().getPrivateChatMessage(msg.message.messageId)
            return@withTransaction if (hm?.sendStatus == SendStatus.PENDING) {
                hm.sendStatus = SendStatus.SENDING
                db.getChatMessageDao().updateChat(hm)
                hm
            } else null
        } ?: return ResultOf.getError("Chat is already sending. exiting...")
        val sent = PrivateChatEventRepository.sendChatMessage(chat.socketpayload)
        return if(sent) ResultOf.Success(Unit) else {
            db.getChatMessageDao().updatePrivateChatStatus(chat.messageId,SendStatus.PENDING)
            ResultOf.getError("Count not send socket event")
        }
    }

    suspend fun sendBroadcastMessage(msg: BroadcastChatMessageWithMedia): ResultOf<Unit> {
        if (msg.mediaIsUploading) return ResultOf.getError("Media is Already uploading")
        if(msg.needsMediaUpload) {
            val result = uploadMedia(msg)
            if(result !is ResultOf.Success) return result
        }
        val chat = db.withTransaction {
            val hm = db.getChatMessageDao().getBroadcastMessage(msg.message.messageId)
            return@withTransaction if (hm?.sendStatus == SendStatus.PENDING) {
                hm.sendStatus = SendStatus.SENDING
                db.getChatMessageDao().updateBroadcast(hm)
                hm
            } else null
        } ?: return ResultOf.getError("Chat is already sending. exiting...")
        val sent = BroadcastEventRepository.sendBroadcastMessage(chat)
        return if(sent) ResultOf.Success(Unit) else {
            db.getChatMessageDao().updateBroadcastChatStatus(chat.messageId,SendStatus.PENDING)
            ResultOf.getError("Count not send socket event")
        }
    }

    private suspend fun getMediaUploadLock(messageId: String): Boolean {
        return db.withTransaction {
            val media = db.getChatMessageDao().getMedia(messageId)
            Log.d(TAG, "getMediaUploadLock: current status is: ${media?.uploadState}")
            if (media?.uploadState == MediaUploadState.Pending) {
                media.uploadState = MediaUploadState.Uploading(0)
                db.getChatMessageDao().updateMedia(media)
                return@withTransaction true
            }
            return@withTransaction false
        }
    }

    suspend fun resetSendActionsAndMediaUploads() = withContext(Dispatchers.IO) {
        db.getChatMessageDao().apply {
            resetSendStatus()
            resetUploadingMedia()
        }
    }
    suspend fun resetSendActions() = db.getChatMessageDao().resetSendStatus()

    private suspend fun uploadMedia(msg: AbstractChatMessageWithMedia): ResultOf<Unit> = withContext(Dispatchers.IO) {
        if (!msg.needsMediaUpload) return@withContext ResultOf.getError("No media to upload")
        val media = msg.offlineMedia ?: return@withContext ResultOf.getError("Offline Media is missing")
        if (!getMediaUploadLock(media.messageId)) return@withContext ResultOf.getError("Media is already being uploaded")

        val result = when (media.mediaType) {
            MediaType.IMAGE, MediaType.AUDIO -> prepareAndUploadMedia(media)
            MediaType.VIDEO, MediaType.DOCUMENT -> prepareAndUploadMultipartMedia(media)
        }
        if (result is ResultOf.Success) {
            media.uploadState = MediaUploadState.None
            db.getChatMessageDao().updateMedia(media)
        } else {
            media.uploadState = MediaUploadState.Pending
            db.getChatMessageDao().updateMedia(media)
        }
        result
    }

    suspend fun prepareAndUploadMedia(media: OfflineMedia): ResultOf<Unit> {
        try {
            val uri = MediaUtils.getUriForFile(mContext,media.file)
            val type = mContext.contentResolver.getType(uri)?:""

            val response = APIServiceGenerator.createService(ChatAPIService::class.java)
                .getMediaUploadURL(media.key,type)
            return when (val result = APIUtil.handleResponse(response)) {
                is ResultOf.Success -> {
                    performMediaUpload(media.s3UploadMedia,result.value)
                    ResultOf.Success(Unit)
                }
                is ResultOf.APIError -> return result
                else -> throw Exception()
            }
        } catch (e: Exception) {
            return ResultOf.getError(e)
        }
    }

    override suspend fun getVideoSecrets(): ResultOf<UploadCredentialsResponse> {
        return try {
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java, true).getVideoUploadCredentials()
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    override fun getAwsConfig() = AWSConfiguration(mContext)

    suspend fun processVideo(med: TempMedia, listener: VideoEncoderUtil.MediaProcessingListener): TempMedia {
        if(med.mediaType != MediaType.VIDEO) throw Exception("Media is not a video file")
        val file = VideoEncoderUtil.encodeVideoMedia3(med, listener)
        return TempMedia.of(file,MediaType.VIDEO,med.sourceUri)
    }

    private suspend fun prepareAndUploadMultipartMedia(media: OfflineMedia): ResultOf<Unit> {
        try {
            MediaUploadListener.post(media.messageId,0)
            performMultipartMediaUpload(media.s3UploadMedia).collect {
                when (val res = it) {
                    is MultipartMediaUploadResult.Progress -> {
//                        withContext(Dispatchers.IO) {
//                            db.withTransaction {
//                                val copy = media.copy(uploadState = UploadState.Uploading(res.percent))
//                                db.getChatMessageDao().updateMedia(copy)
//                            }
//                        }
                        Log.d("ENCODE", "posting ${res.percent} to MUL")
                        MediaUploadListener.post(media.messageId,res.percent)
                    }
                    is MultipartMediaUploadResult.Complete -> {
                        // Nothing to do. Channel will close
                    }

                    is MultipartMediaUploadResult.Error -> throw res.error
                }
            }
            MediaUploadListener.clear(media.messageId)
        } catch (e: Exception) {
            MediaUploadListener.clear(media.messageId)
            return ResultOf.getError(e)
        }

        Log.d("ENCODE", "prepareAndUploadVideo: end of uploading Method")
        return ResultOf.Success(Unit)
    }

    fun getBlockedPrivateMessageListPager(): Pager<Int,UserRelative> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = { BlockedPrivateMessageDataSource(APIServiceGenerator.createService(ChatAPIService::class.java)) })
    }


    suspend fun getMessageReportCategories(): ResultOf<ReportCategoryResponse> {
        return try {
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).getMessageReportCategories()
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    private suspend fun updateReportStatus(messageId: String, msg: String, reported: Boolean) {
        db.getChatMessageDao().apply {
            val message = getHuddleChatMessage(messageId) ?: return@apply
            val replies = getHuddleChatMessagesThatReplyToMessage(messageId)

            db.withTransaction {
                updateChat(
                    message.copy(
                        rawMessage = msg, reported = reported
                    )
                )
                replies?.forEach { reply ->
                    if (reply.replyTo?.messageId == messageId) {
                        updateChat(
                            reply.copy(
                                replyTo = reply.replyTo?.copy(
                                    message = msg,
                                    reported = reported,
                                )
                            )
                        )
                    }
                }
            }
        }
    }

    suspend fun reportMessage(req: ReportToManagerRequest.MessageReportRequest): ResultOf<Boolean> {
        return try {
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).reportMessage(req.huddleId, req.messageId, req)
            return when(val result = APIUtil.handleResponse(resp)) {
                is ResultOf.Success -> {
                    updateReportStatus(req.messageId, result.value.message, result.value.reported)
                    ResultOf.Success(true)
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }

        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun reportComment(req: ReportToManagerRequest.CommentReportRequest): ResultOf<Boolean> {
        return try {
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).reportComment(req.huddleId, req.messageId, req.commentId, req)
            return when(val result = APIUtil.handleResponse(resp)) {
                is ResultOf.Success -> {
                    db.getChatMessageDao().apply {
                        val postCommentItem = getHuddlePostSingleComment(req.commentId)?:return@apply
                        updateHuddlePostComment(postCommentItem.copy(
                            comment = result.value.comment,
                            isReported = result.value.isReported
                        ))
                    }
                    ResultOf.Success(true)
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }

        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun reportMessageCancel(req: ReportToManagerRequest.MessageReportCancelRequest): ResultOf<Boolean> {
        return try {
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).cancelMessageReport(req.huddleId, req.messageId)
            return when(val result = APIUtil.handleResponse(resp)) {
                is ResultOf.Success -> {
                    updateReportStatus(req.messageId, result.value.message, result.value.reported)
                    ResultOf.Success(true)
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }

        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun cancelReportComment(req: ReportToManagerRequest.CommentReportCancelRequest): ResultOf<Boolean> {
        return try {
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).cancelCommentReport(req.huddleId, req.messageId, req.commentId, req.action)
            return when(val result = APIUtil.handleResponse(resp)) {
                is ResultOf.Success -> {
                    db.getChatMessageDao().apply {
                        val postCommentItem = getHuddlePostSingleComment(req.commentId)?:return@apply
                        updateHuddlePostComment(postCommentItem.copy(
                            comment = result.value.comment,
                            isReported = result.value.isReported
                        ))
                    }
                    ResultOf.Success(true)
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }

        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    fun stickersPager(): Pager<Int, Sticker> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = StickerRemoteMediator(db, APIServiceGenerator.createService(ChatAPIService::class.java)),
            pagingSourceFactory = { db.getHuddleDao().getStickers() }
        )
    }

    fun getStickerSearchPager(keyword: String): Pager<Int, Sticker> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = { StickerDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), keyword) }
        )
    }

    suspend fun clearPrivateChatMessages(id: String): ResultOf<String> = withContext(Dispatchers.IO) {
        return@withContext try {
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).deletePrivateChatMessages(id)
            val result = APIUtil.handleResponseWithoutResult(resp)
            if(result is ResultOf.Success) {
                db.getChatMessageDao().deleteAllPrivateChatMessages(id)
                db.getPrivateChatDao().getPrivateChat(id)?.let { chat ->
                    db.getPrivateChatDao().insert(chat.copy(lastMessageId = null, lastMessageInternal = null).apply {
                        privateMessageTab = chat.privateMessageTab
                    })
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }
}