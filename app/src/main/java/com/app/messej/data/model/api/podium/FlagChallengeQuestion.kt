package com.app.messej.data.model.api.podium


import com.google.gson.annotations.SerializedName

data class FlagChallengeQuestion(
    @SerializedName("countries") val options: List<FlagChallengeOption> = listOf(),
    @SerializedName("country_code") val countryCode: String
){
    data class FlagChallengeOption(
        @SerializedName("id") val answerId: Int,
        @SerializedName("country_name") val countryName: String,
        @SerializedName("is_answer") val isAnswer: Boolean = false,
    )
}