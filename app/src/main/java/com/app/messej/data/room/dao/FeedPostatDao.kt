package com.app.messej.data.room.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.entity.Postat.FeedPostat
import com.app.messej.data.model.enums.PostatTab
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class FeedPostatDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(postat: FeedPostat): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(postat: List<FeedPostat>): List<Long>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POSTAT_FEED} WHERE ${Postat.COLUMN_TAB} = :tab ORDER BY ${FlashVideo.COLUMN_CREATED_TIME} DESC")
    abstract fun getFeedPostatPager(tab: PostatTab): PagingSource<Int, FeedPostat>

    @Update
    abstract suspend fun update(postat: FeedPostat): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_POSTAT_FEED} WHERE ${Postat.COLUMN_ID} = :id")
    abstract suspend fun deletePostat(id: String)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_POSTAT_FEED} WHERE ${Postat.COLUMN_TAB} = :tab")
    abstract suspend fun deleteAllByTab(tab: PostatTab)

}