package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.MentionedUserListResponse
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.api.postat.CreatePostatRequest
import com.app.messej.data.model.api.postat.PostatAudioListResponse
import com.app.messej.data.model.api.postat.PostatListResponse
import com.app.messej.data.model.api.postat.PostatRuleLimitResponse
import com.app.messej.data.model.api.postat.PostatShareResponse
import com.app.messej.data.model.entity.Postat
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface PostatAPIService {
    @GET("/postat/library/audio")
    @Headers("Accept: application/json")
    suspend fun getPostatAudios(@Query("keyword") searchKeyword: String? = null, @Query("page") page: Int? = null): Response<APIResponse<PostatAudioListResponse>>

    @GET("postat/users")
    @Headers("Accept: application/json")
    suspend fun getMentionUsers(@Query("page") page: Int, @Query("keyword") searchKeyword: String? = null): Response<APIResponse<MentionedUserListResponse>>

    @GET("/postat/video_secrets")
    @Headers("Accept: application/json")
    suspend fun getPostatUploadCredentials(): Response<APIResponse<UploadCredentialsResponse>>

    @GET("postat/signed_cookies")
    @Headers("Accept: application/json")
    suspend fun getAudioPlaybackCookies(): Response<APIResponse<VideoPlaybackCookie>>

    @GET("postat")
    suspend fun getMyPostat(@Query("page") page: Int): Response<APIResponse<PostatListResponse>>

    @POST("postat")
    @Headers("Accept: application/json")
    suspend fun createPostat(@Body request: CreatePostatRequest): Response<APIResponse<Postat>>

    @PUT("postat/{msgId}")
    @Headers("Accept: application/json")
    suspend fun updatePostat(@Path("msgId") msgId: String, @Body request: CreatePostatRequest): Response<APIResponse<Postat>>

    @GET("postat")
    suspend fun getFeedPostat(@Query("page") page: Int? = null, @Query("tab") tab: String): Response<APIResponse<PostatListResponse>>


    @GET("postat")
    suspend fun getUserFeedPostat(@Query("userId") userId: Int, @Query("page") page: Int? = null): Response<APIResponse<PostatListResponse>>


    @DELETE("postat/{msgId}")
    suspend fun deletePostatbyId(@Path("msgId") msgId: String): Response<APIResponse<Unit>>

    @PUT("postat/{msgId}")
    suspend fun updatePostatField(
        @Path("msgId") msgId: String,
        @Query("quick") quick: Boolean,
        @Body requestBody: Map<String, Boolean>
    ): Response<APIResponse<Unit>>

    @GET("postat/share/generate-code")
    suspend fun getShareCode(@Query("postatId") postAtId: String): Response<APIResponse<PostatShareResponse>>

    @GET("/postat/limit/{id}")
    @Headers("Accept: application/json")
    suspend fun getPostatCitizenshipRuleData(@Path("id") userId: Int): Response<APIResponse<PostatRuleLimitResponse>>

}