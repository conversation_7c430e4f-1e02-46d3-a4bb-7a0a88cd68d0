package com.app.messej.data.model.api

import com.app.messej.data.model.enums.BiometricLockTimeout
import com.app.messej.data.model.enums.NightModeSetting
import java.time.ZonedDateTime

data class AppLocalSettings(
    var appLanguage                     : String?       = null,
//    var isDeviceLanEnabled              : Boolean?      = null,
    var isBioMetricEnabled              : Boolean = false,
    var biometricLockTimeout            : BiometricLockTimeout?       = null,
    var disableAllNotificationState     : Boolean?      = null,
    var appNightMode: NightModeSetting = NightModeSetting.FORCE_DARK
) {
    private var lastActiveTime: String? = null

    var appLastActiveTime: ZonedDateTime?
        set(value) {
            lastActiveTime = value?.toString()
        }
        get() {
            return if(lastActiveTime!=null) ZonedDateTime.parse(lastActiveTime)
            else null
        }
}
