package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.PenaltyData
import com.google.gson.annotations.SerializedName

data class PodiumChallengePenaltyStartPayload(
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("penalty_data") val penaltyData: PenaltyData
) : SocketEventPayload()