package com.app.messej.data.model.api.business

import com.app.messej.data.Constants.PLATFORM_OS
import com.google.gson.annotations.SerializedName

data class BusinessVerifyRequest(
    @SerializedName("phone") val phone: String?=null,
    @SerializedName("country_code") val countryCode: String?=null,
    @SerializedName("device") val device: String = PLATFORM_OS,
    @SerializedName("referral_code") val referralCode: String? = null,
    @SerializedName("resend") val resend: Boolean = true,
    @SerializedName("identity") val identity: String?=null,
    @SerializedName("email") val email: String?=null,
    @SerializedName("paypal_id") val paypalId: String?=null,
    @SerializedName("about") val about: String?=null
)
