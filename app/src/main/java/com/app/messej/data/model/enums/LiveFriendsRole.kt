package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class LiveFriendsRole {

    @SerializedName("dear") DEAR,
    @SerializedName("following")FOLLOWING,
    @SerializedName("fan")FAN,
    @SerializedName("friends")FRIENDS,
    @SerializedName("superstar")SUPERSTAR;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}