package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class LegalAffairsViolationSubTab {
    @SerializedName("open") Open,
    @SerializedName("closed") Closed,
    @SerializedName("fines") Fines,
    @SerializedName("statistics") Statistics;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)?.value ?: ""
    }
}