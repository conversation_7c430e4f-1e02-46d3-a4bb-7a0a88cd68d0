package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.HuddleChatMessage
import com.google.gson.annotations.SerializedName

data class HuddleMyPostsListResponse(
    @SerializedName("current_page"      ) val currentPage       : Int,
    @SerializedName("messages"          ) val messages          : List<HuddleChatMessage> = listOf(),
    @SerializedName("post_count"        ) val postCount         : Int,
    @SerializedName("reply_count"       ) val replyCount        : Int,
    @SerializedName("migration_status"  ) val migrationStatus   : String?               = null,
)