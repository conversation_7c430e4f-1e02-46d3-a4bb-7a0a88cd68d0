package com.app.messej.data.model.api.profile


import com.google.gson.annotations.SerializedName

data class VerifyAccountResponse(
    @SerializedName("min_dears_for_verified_status")
    val minDearsForVerifiedStatus: Int = 0,
    @SerializedName("no_of_dears")
    val dears: Int = 0,
    @SerializedName("premium")
    val premium: Boolean = false,
    @SerializedName("profile_completion_percentage")
    val profileCompletionPercentage: Int = 0,
    @SerializedName("verification_status")
    val verificationStatus: String? = null
){
    enum class VerificationStatus {
        @SerializedName("Declined")DECLINED,
        @SerializedName("Verified") VERIFIED,
        @SerializedName("Processing") PROCESSING;

        override fun toString(): String {
            return javaClass.getField(name).getAnnotation(SerializedName::class.java)?.value ?: ""
        }
    }
}