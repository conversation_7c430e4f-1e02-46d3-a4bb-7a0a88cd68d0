package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class PodiumDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(podium: Podium)
    @Update
    abstract suspend fun update(podium: Podium)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(podiumList: List<Podium>)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_PODIUMS}")
    abstract suspend fun deleteAll()

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PODIUMS}")
    abstract fun podiumsPagingSource(): PagingSource<Int, Podium>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PODIUMS} WHERE ${Podium.COLUMN_ID} = :id")
    abstract fun podiumLiveData(id: String): LiveData<Podium?>
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PODIUMS} WHERE ${Podium.COLUMN_ID} = :id")
    abstract fun getPodium(id: String): Podium?

    @Query("DELETE FROM ${EntityDescriptions.TABLE_PODIUMS} WHERE ${Podium.COLUMN_ID} = :id")
    abstract fun delete(id: String)

}