package com.app.messej.data.socket.repository

import android.util.Log
import com.app.messej.MainApplication
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.promo.PromoHistoryResponse
import com.app.messej.data.model.socket.PromoAnnouncement
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.runBlocking
import org.json.JSONObject

object PromoBoardEventRepository: BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {

    private val accountRepo = AccountRepository(MainApplication.applicationInstance())
    override fun handleEvent(event: ChatSocketEvent, data: J<PERSON>NObject): Boolean {
        when (event) {
            ChatSocketEvent.RX_ANNOUNCEMENT -> onMaidanAnnouncement(data)
            else -> return false
        }
        return true
    }

    val user: CurrentUser get() = accountRepo.user

    private val _announcementFLow: MutableSharedFlow<PromoAnnouncement> = MutableSharedFlow()
    val announcementFLow: SharedFlow<PromoAnnouncement> = _announcementFLow

    private val _announcementClearFLow: MutableSharedFlow<PromoAnnouncement.AnnouncementType> = MutableSharedFlow()
    val announcementClearFLow: SharedFlow<PromoAnnouncement.AnnouncementType> = _announcementClearFLow

    private fun onMaidanAnnouncement(data: JSONObject) = runBlocking {
        try {
            Log.d("ANNC", "onMaidanAnnouncement: $data")
            val type = PromoAnnouncement.AnnouncementType.entries.find {
                data.getString("type") == it.serializedName()
            }?: return@runBlocking

            // Special handling for birthday announcements
            if (type == PromoAnnouncement.AnnouncementType.BIRTHDAYS_TODAY) {
                val birthdayAnnouncement = Gson().fromJson<PromoHistoryResponse.Birthdays>(
                    data.getJSONObject("content").toString()
                )

                // Check if this is a "clear birthdays" signal (both president and ministers are null)
                if (birthdayAnnouncement.president == null && birthdayAnnouncement.ministers == null) {
                    // Emit a special "clear" announcement
                    _announcementClearFLow.emit(PromoAnnouncement.AnnouncementType.BIRTHDAYS_TODAY)
                    Log.d("ANNC", "Emitting clear birthday announcement signal")
                } else {
                    // Emit president birthday if present
                    birthdayAnnouncement.president?.let { president ->
                        val presidentAnnouncement = PromoAnnouncement.Birthday(
                            userType = PromoAnnouncement.Birthday.BirthdayType.PRESIDENT,
                            user = president
                        )
                        _announcementFLow.emit(presidentAnnouncement)
                    }

                    // Emit each minister birthday separately
                    birthdayAnnouncement.ministers?.forEach { minister ->
                        val ministerAnnouncement = PromoAnnouncement.Birthday(
                            userType = PromoAnnouncement.Birthday.BirthdayType.MINISTER,
                            user = minister
                        )
                        _announcementFLow.emit(ministerAnnouncement)
                    }
                }

                return@runBlocking
            }

            // Regular handling for other announcement types
            val content = when(type) {
                PromoAnnouncement.AnnouncementType.ADMIN_ANNOUNCEMENT -> Gson().fromJson<PromoAnnouncement.AdminMessage>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.TOP_LIKED_PODIUMS -> Gson().fromJson<PromoAnnouncement.TopLikedPodium>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.TOP_USER_PODIUMS -> Gson().fromJson<PromoAnnouncement.TopUserPodium>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.USER_UPGRADE -> Gson().fromJson<PromoAnnouncement.UserUpgrade>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.MAIDAN_RESULT -> Gson().fromJson<PromoAnnouncement.MaidanResult>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.CASE_REPORTED -> Gson().fromJson<PromoAnnouncement.CaseReported>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.STRONGEST_TRIBE -> Gson().fromJson<PromoAnnouncement.StrongestTribe>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.MOST_GENEROUS_USER -> Gson().fromJson<PromoAnnouncement.MostGenerousUser>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.MOST_GENEROUS_PODIUM -> Gson().fromJson<PromoAnnouncement.MostGenerousPodium>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.ADMIN_ANNOUNCEMENT_SCHEDULED -> Gson().fromJson<PromoAnnouncement.AdminAnnouncementScheduled>(data.getJSONObject("content").toString())
                PromoAnnouncement.AnnouncementType.BIRTHDAYS_TODAY -> return@runBlocking
            }
            Log.d("ANNC", "onMaidanAnnouncement: $content")
            _announcementFLow.emit(content)
        } catch (e: Exception) {
            Log.e("PER", "onPodiumLike: ", e)
        }
    }
}