package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class AcceptDecline {
    @SerializedName("accept") ACCEPT,
    @SerializedName("insufficient_balance") INSUFFICIENT_COIN,
    @SerializedName("decline") DECLINE;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}