package com.app.messej.data.model.api.postat

import androidx.room.TypeConverter
import com.app.messej.data.model.AbstractUriMedia
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.entity.LocalPostatMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.MediaResolution
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class PostatMedia (
    @SerializedName("media_type"           ) override val mediaType          : MediaType,
    @SerializedName("media_name"           ) val mediaName          : String? = null,

    @SerializedName("mime_type"            ) override val mimeType           : String? = null,
    @SerializedName("s3_key"               ) val s3Key              : String? = null,


    @SerializedName("thumbnail"            ) override val thumbnail          : String? = null,
    @SerializedName("media_height"         ) val mediaHeight        : String? = null,
    @SerializedName("media_width"          ) val mediaWidth         : String? = null,


    @SerializedName("media_duration"       ) val mediaDuration      : String? = null,


    @SerializedName("media_size"           ) val mediaSize          : String? = null,


    @SerializedName("display_name"         ) val displayName        : String? = null,

    @SerializedName("media"                ) val media              : String? = null,

    /**
     * Provides CloudFront url in case of Postat Feed (needs Cookies)
     * Provides S3 Signed url in Case Details API
     */
    @SerializedName(value = "media_cloudfront_url", alternate = ["media_url"]) val mediaUrl : String? = null,
): AbstractUriMedia() {

    val imageWidth: Float
        get() = mediaWidth?.toFloatOrNull()?:0f

    val imageHeight: Float
        get() = mediaHeight?.toFloatOrNull()?:0f

    val mediaExtension: String
        get() = mediaName?.split(".")?.lastOrNull()?:""

    val seconds: Long
        get() {
            val dur = DateTimeUtils.parseToDuration(mediaDuration?:"0:0")?.seconds?:0
            return dur.coerceAtLeast(1)
        }

    val resolution: MediaResolution
        get() = MediaResolution(imageWidth.toInt(),imageHeight.toInt())

    val asMediaMeta: MediaMeta
        get() = MediaMeta(
            mediaType = mediaType,
            mediaName = mediaName,
            mimeType = mimeType,
            s3Key = s3Key?:"",
            thumbnail = thumbnail,
            mediaHeight = mediaHeight,
            mediaWidth = mediaWidth,
            mediaDuration = mediaDuration,
            formattedSize = mediaSize,
        )

    class Converter {
        @TypeConverter
        fun decode(data: String?): List<PostatMedia>? {
            data?: return null
            val type: Type = object : TypeToken<List<PostatMedia>?>() {}.type
            return Gson().fromJson(data, type)
        }
        @TypeConverter
        fun encode(someObjects: List<PostatMedia>?): String? {
            return Gson().toJson(someObjects)
        }
    }

    companion object {
        fun from(localPostatMedia: LocalPostatMedia): PostatMedia {
            return PostatMedia(
                mediaSize = localPostatMedia.meta.formattedSize,
                mediaName = localPostatMedia.meta.mediaName,
                displayName = localPostatMedia.meta.documentDisplayName,
                s3Key = localPostatMedia.key,
                mediaHeight = localPostatMedia.meta.mediaHeight,
                mediaType = localPostatMedia.meta.mediaType,
                mimeType = localPostatMedia.meta.mimeType,
                mediaWidth = localPostatMedia.meta.mediaWidth,
                mediaDuration = localPostatMedia.meta.mediaDuration,
                thumbnail = localPostatMedia.path,
                media = localPostatMedia.key
            )
        }
    }
}
