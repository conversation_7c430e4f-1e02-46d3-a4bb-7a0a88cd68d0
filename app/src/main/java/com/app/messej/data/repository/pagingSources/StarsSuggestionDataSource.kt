package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.model.Star
import com.app.messej.data.model.enums.SearchType
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1

class StarsSuggestionDataSource(private val api: ProfileAPIService,
                                private val accountRepo: AccountRepository, private val searchKeyWord: String?): PagingSource<Int, Star>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Star> {
        return try {
            if (searchKeyWord.isNullOrEmpty()) {
                withContext(Dispatchers.IO) {
                    val currentPage = params.key ?: STARTING_KEY
                    val response = api.getStarsSuggestion(
                        UserType.PREMIUM,
                        currentPage
                    )
                    val responseData = mutableListOf<Star>()
                    val data = response.body()?.result?.stars ?: emptyList()
                    responseData.addAll(data)
                    val nextKey = if (responseData.isEmpty()) null else currentPage.plus(1)

                    LoadResult.Page(
                        data = responseData,
                        prevKey = if (currentPage == 1) null else currentPage.minus(1),
                        nextKey = nextKey
                    )
                }
            } else {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getStarsSearchSuggestion(
                    UserType.FREE,
                    currentPage,
                    searchKeyWord,
                    SearchType.EXACT_MATCH
                )
                val responseData = mutableListOf<Star>()
                val data = response.body()?.result?.users ?: emptyList()
                responseData.addAll(data)
                val nextKey = if (response.body()?.result!!.users.isEmpty()) null else currentPage.plus(1)

                LoadResult.Page(
                    data = response.body()?.result!!.users,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, Star>): Int? {
        val anchorPosition = state.anchorPosition ?: return null
        val article = state.closestItemToPosition(anchorPosition) ?: return null
        return ensureValidKey(key = article.id - (state.config.pageSize / 2) )
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}