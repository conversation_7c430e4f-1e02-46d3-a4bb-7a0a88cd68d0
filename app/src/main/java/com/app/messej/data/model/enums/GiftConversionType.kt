package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class GiftConversionType() {
    @SerializedName("p2f") COIN_TO_FLAX,
    @SerializedName("f2p") FLAX_TO_COIN,
    @SerializedName("Purchased") COIN_PURCHASE,
    @SerializedName("Rewarded") COIN_REWARDED;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}