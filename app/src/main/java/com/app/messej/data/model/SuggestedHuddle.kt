package com.app.messej.data.model

import com.google.gson.annotations.SerializedName

data class SuggestedHuddle(
    @SerializedName("id"              ) override val id            : Int     = 0,
    @SerializedName("name"            ) override val name          : String  = "",
    @SerializedName("about"           ) override val about         : String  = "",
    @SerializedName("thumbnail"       ) override val thumbnail     : String?  = null,
    @SerializedName("group_photo"     ) override val groupPhoto    : String?  = null,
    @SerializedName("manager_premium_status") override val managerPremium : Boolean?,
    @SerializedName("category"        ) override val category      : String?  = null,
    @SerializedName("category_id"     ) var categoryId    : Int?     = null,
    @SerializedName("total_members"   ) override val totalMembers  : Int     = 0,
    @SerializedName("private"         ) override val isPrivate       : Boolean = false,
    @SerializedName("request_to_join" ) override val requestToJoin : <PERSON><PERSON><PERSON>,
    @SerializedName("status"          ) override val status    : HuddleStatus = HuddleStatus.ACTIVE,
    @SerializedName("user_status"     ) override val userStatus    : HuddleUserStatus,
): AbstractHuddle()
