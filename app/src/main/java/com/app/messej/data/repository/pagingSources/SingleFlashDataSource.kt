package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.entity.FlashVideo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SingleFlashDataSource(private val api: FlashAPIService,private val id: String): PagingSource<Int, FlashVideo>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FlashVideo> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = null
                val response = api.getFlashDetail(id)
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))

                LoadResult.Page(
                    data = listOf(data), prevKey = null , nextKey = null
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, FlashVideo>): Int? {
        return null
    }
}