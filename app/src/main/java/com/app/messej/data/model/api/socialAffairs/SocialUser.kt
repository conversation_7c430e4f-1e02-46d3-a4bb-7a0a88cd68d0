package com.app.messej.data.model.api.socialAffairs

import androidx.annotation.DrawableRes
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class SocialAffairUser(
    @SerializedName(value = "id") override val id: Int,
    @SerializedName(value = "citizenship") override val citizenship: UserCitizenship? = null,
    @SerializedName(value = "premium") val premium: Boolean? = null,
    @SerializedName(value = "name") override val name: String,
    @SerializedName(value = "thumbnail_url") override val thumbnail: String? = null,
    @SerializedName(value = "country_code_iso") override val countryCode: String? = null,

    @SerializedName(value = "generosity") val generosity: String? = null,
    @SerializedName(value = "user_rating") val userRating: Double? = null,
    @DrawableRes val countryFlag: Int? = null
) : AbstractUser() {

    override val membership: UserType
        get() = if(premium == true) UserType.PREMIUM else UserType.FREE

    override val verified: Boolean
        get() = false

    override val username: String
        get() = ""

    companion object {
        val dummySocialUser = SocialAffairUser(
            id = 2,
            premium = true,
            name = "Abijith",
            thumbnail = null,
            countryFlag = com.hbb20.R.drawable.flag_india,
            generosity = "15",
            userRating = 50.00
        )
    }
}
