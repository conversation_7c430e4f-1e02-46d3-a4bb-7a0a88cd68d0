package com.app.messej.data.socket

import android.util.Log
import com.app.messej.MainApplication
import com.app.messej.data.model.socket.SocketEventPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.utils.BaseMultiListener
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.runBlocking
import org.json.JSONObject

abstract class AbstractSocketRepository<T: SocketEvent>(protected val service: AbstractSocketService): BaseMultiListener<SocketEventHandler<T>>(), SocketListener {

    protected open val logTag = "AbstractSocketRepo"

    init {
        service.registerListener(this)
    }

    override fun onConnect() {
        removeAllListeners()
        addListeners()
        Log.d(logTag, "SocketRepository: socket connected")
        runBlocking { _connectionStateFlow.emit(true) }
    }

    override fun onConnectionError() {
        _connectionStateFlow.value = false
    }

    override fun onDisconnect() {
        Log.d(logTag, "SocketRepository: socket disconnected")
        _connectionStateFlow.value = false
    }

    override fun onEvent(eventName: String, event: JSONObject) {
        TODO("Not yet implemented")
    }

    private val _connectionStateFlow = MutableStateFlow(false)
    val connectionStateFlow: StateFlow<Boolean> = _connectionStateFlow

    abstract fun addListeners()

    val connected: Boolean
        get() = service.isConnected

    fun start() {
        val repo = AccountRepository(MainApplication.applicationInstance())
        if (!repo.loggedIn) return
        if (service.isConnected) return
        Log.d(logTag, "SocketRepository: starting socket using token: ${repo.accessToken}")
        service.start(repo.accessToken)
    }

    fun stop() {
        Log.d(logTag, "SocketRepository: stopping socket")
        service.disconnect()
    }

    fun restart() {
        stop()
        start()
    }

    protected fun handleMessage(type: T, data: JSONObject) {
        if (!AccountRepository(MainApplication.applicationContext()).loggedIn) return
//        Log.d(logTag, "handleMessage: $listeners")
        for (handler in listeners) {
            if (handler.onEvent(type,data)) return
        }
    }

    fun sendEvent(event: T, payload: SocketEventPayload): Boolean {
        if (!service.isConnected) {
            start()
            return false
        }
        service.emitEvent(event.key,payload)
        return true
    }
}