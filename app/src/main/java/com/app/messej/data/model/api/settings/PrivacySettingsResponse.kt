package com.app.messej.data.model.api.settings


import com.google.gson.annotations.SerializedName

data class PrivacySettingsResponse(
    @SerializedName("about_privacy") val aboutPrivacy: AboutPrivacy? = AboutPrivacy(),
    @SerializedName("blocked_huddles") val blockedHuddles: Int? = 0,
    @SerializedName("enabled_documents") val enabledDocuments: List<EnabledDocument?>? = listOf(),
    @SerializedName("huddle_participants_limit_for_free") val huddleParticipantsLimitForFree: Int? = null,
    @SerializedName("last_seen_privacy") val lastSeenPrivacies: LastSeenPrivacies? = LastSeenPrivacies(),
    @SerializedName("otom_voice_message_length") val otomVoiceMessageLength: Float? = null,
    @SerializedName("huddle_voice_message_length") val huddleVoiceMessageLength: Float? = null,
    @SerializedName("policy_list") val policyList: List<Any?>? = listOf(),
    @SerializedName("profile_complete_percentage") val profileCompletePercentage: Int? = 0,
    @SerializedName("profile_incomplete_alert") val profileIncompleteAlert: Boolean? = false,
    @SerializedName("profile_photo_privacy") val profilePhotoPrivacy: ProfilePhotoPrivacy? = ProfilePhotoPrivacy(),
    @SerializedName("total_blocked_chats") val totalBlockedChats: Int? = 0,
    @SerializedName("total_blocked_users") val totalBlockedUsers: Int? = 0,
    @SerializedName("total_hidden_users") val totalHiddenUsers: Int? = 0,
    @SerializedName("unseen_notification_count") val unseenNotificationCount: Int? = 0,
    @SerializedName("receive_challenge_invitation") val receiveChallengeInvitation: Boolean? = false
){
    val huddleVoiceMessageLengthInSeconds: Int?
        get() {
            return if (huddleVoiceMessageLength!=null && huddleVoiceMessageLength>=0) huddleVoiceMessageLength.toInt()
            else null
        }
    val otomVoiceMessageLengthInSeconds: Int?
        get() {
            return if (otomVoiceMessageLength!=null && otomVoiceMessageLength>=0) otomVoiceMessageLength.toInt()
            else null
        }
}