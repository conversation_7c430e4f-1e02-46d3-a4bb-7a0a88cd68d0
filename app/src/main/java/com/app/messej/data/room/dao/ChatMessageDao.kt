package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.TypeConverters
import androidx.room.Update
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.AbstractChatMessage.SendStatus
import com.app.messej.data.model.ChatMessageSearchResult
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.entity.BroadcastChatMessageWithMedia
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.entity.HuddleReportedComment
import com.app.messej.data.model.entity.HuddleReportedMessage
import com.app.messej.data.model.entity.HuddleReportedMessageWithMedia
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.entity.PrivateChatMessageWithMedia
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.room.EntityDescriptions

@Dao
@TypeConverters(
    HuddleChatMessage.Converter::class
)
abstract class ChatMessageDao {

    // GroupChats

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertHuddleChatMessages(huddleChat: List<HuddleChatMessage>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertChat(huddleChat: HuddleChatMessage): Long

    @Update
    abstract suspend fun updateChat(huddleChat: HuddleChatMessage): Int

    @Update
    abstract suspend fun updateChats(huddleChat: List<HuddleChatMessage>): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_MESSAGE_ID} = :id")
    protected abstract suspend fun deleteHuddleChatMessage(id: String)

    suspend fun deleteHuddleChatMessage(id: String, cascade: Boolean = true) {
        deleteHuddleChatMessage(id)
        if (cascade) {
            deleteMedia(id)
        }
    }

    @Transaction
    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :huddleId")
    abstract suspend fun deleteAllHuddleChatMessages(huddleId: String): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_MESSAGE_ID} in (:idList)")
    abstract suspend fun deleteHuddleChatMessages(idList: List<String>)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_PINNED} = 1")
    abstract suspend fun deletePinnedHuddleChatMessage()

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract suspend fun getHuddleChatMessage(id: String): HuddleChatMessage?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :huddleId AND ${HuddleChatMessage.COLUMN_SENDER} = :memberId")
    abstract suspend fun getHuddleChatMessagesOfMember(huddleId: String, memberId: Int): List<HuddleChatMessage>

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} SET star_type = :star WHERE ${HuddleChatMessage.COLUMN_SENDER} = :senderId")
    abstract suspend fun updateHuddleMessageStarRelation(senderId: Int, star: String? = HuddleChatMessage.STAR_TYPE_STAR)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_REPLY_MESSAGE_ID} = :id")
    abstract suspend fun getHuddleChatMessagesThatReplyToMessage(id: String): List<HuddleChatMessage>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract suspend fun getHuddleChatMessageWithMedia(id: String): HuddleChatMessageWithMedia?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract fun getHuddleChatMessageWithMediaLiveData(id: String): LiveData<HuddleChatMessageWithMedia?>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :id AND ${HuddleChatMessage.COLUMN_PINNED} = 1 ORDER BY ${HuddleChatMessage.COLUMN_PINNED} DESC, ${HuddleChatMessage.COLUMN_MESSAGE_ID} DESC LIMIT 1")
    abstract suspend fun getHuddlePinnedPost(id: String): HuddleChatMessageWithMedia?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :id AND ${HuddleChatMessage.COLUMN_PINNED} = 1 ORDER BY ${HuddleChatMessage.COLUMN_PINNED} DESC, ${HuddleChatMessage.COLUMN_MESSAGE_ID} DESC LIMIT 1")
    abstract fun getHuddlePinnedPostLiveData(id: String): LiveData<HuddleChatMessageWithMedia?>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :id ORDER BY ${HuddleChatMessage.COLUMN_MESSAGE_CREATED} DESC, ${HuddleChatMessage.COLUMN_MESSAGE_ID} DESC")
    abstract fun groupChatsPagingSource(id: String): PagingSource<Int, HuddleChatMessageWithMedia>
    @Transaction
    @Query("SELECT COUNT(*) FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :id AND (${HuddleChatMessage.COLUMN_PINNED} = 1 OR ${HuddleChatMessage.COLUMN_IS_ACTIVITY} = 1) LIMIT 1")
    abstract suspend fun getHiddenHuddleChatMessage(id: String): Int

    @Transaction
    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :id AND (${HuddleChatMessage.COLUMN_PINNED} = 1 OR ${HuddleChatMessage.COLUMN_IS_ACTIVITY} = 1)")
    abstract suspend fun cleanupHuddleChatMessages(id: String): Int

    @Query("SELECT COUNT(*) FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :huddleId AND ${HuddleChatMessage.COLUMN_MESSAGE_CREATED} > :time")
    abstract suspend fun countChatsAfter(time: String, huddleId: String): Int

    @Query("SELECT COUNT(*) FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_ID} = :huddleId AND ${HuddleChatMessage.COLUMN_MESSAGE_CREATED} > :time AND ${HuddleChatMessage.COLUMN_SENDER} = :sender")
    abstract suspend fun countChatsAfterExceptForSender(time: String, huddleId: String, sender: Int): Int

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} SET ${AbstractChatMessage.COLUMN_SEND_STATUS} = :status WHERE ${HuddleChatMessage.COLUMN_MESSAGE_ID} = :messageId")
    abstract suspend fun updateHuddleChatStatus(messageId: String, status: SendStatus)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${AbstractChatMessage.COLUMN_SEND_STATUS}= :status AND ${HuddleChatMessage.COLUMN_HUDDLE_TYPE} = :type ORDER BY ${HuddleChatMessage.COLUMN_MESSAGE_CREATED} ASC LIMIT 1")
    abstract suspend fun getHuddleChatsByStatus(status: SendStatus, type: HuddleType): HuddleChatMessageWithMedia?

    suspend fun getPendingHuddleChats() = getHuddleChatsByStatus(SendStatus.PENDING, HuddleType.PRIVATE)

    // Reported Messages
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertHuddleReportedMessages(reportedMessages: List<HuddleReportedMessage>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertHuddleReportedMessage(reportedMessage: HuddleReportedMessage): Long

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_REPORTED_MESSAGES} WHERE ${HuddleReportedMessage.COLUMN_MESSAGE_ID} = :id")
    abstract suspend fun deleteHuddleReportedMessage(id: String)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_REPORTED_MESSAGES} WHERE ${HuddleReportedMessage.COLUMN_HUDDLE_ID} = :huddleId")
    abstract suspend fun deleteAllHuddleReportedMessages(huddleId: Int)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_REPORTED_MESSAGES} WHERE ${HuddleReportedMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract suspend fun getHuddleReportedMessage(id: String): HuddleReportedMessage?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_REPORTED_MESSAGES} WHERE ${HuddleReportedMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract suspend fun getHuddleReportedMessageWithMedia(id: String): HuddleReportedMessageWithMedia?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_REPORTED_MESSAGES} WHERE ${HuddleReportedMessage.COLUMN_HUDDLE_ID} = :id ORDER BY ${HuddleReportedMessage.COLUMN_MESSAGE_CREATED} DESC, ${HuddleReportedMessage.COLUMN_MESSAGE_ID} DESC")
    abstract fun huddleReportedMessagePagingSource(id: Int): PagingSource<Int, HuddleReportedMessageWithMedia>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} WHERE ${HuddleChatMessage.COLUMN_HUDDLE_TYPE} = :type AND (${HuddleChatMessage.COLUMN_SENDER} = :id OR ${HuddleChatMessage.COLUMN_REPLY_MESSAGE_SENDER} = :id) ORDER BY ${HuddleChatMessage.COLUMN_MESSAGE_CREATED} DESC, ${HuddleChatMessage.COLUMN_MESSAGE_ID} DESC")
    abstract fun myPostsPagingSource(id: Int, type: HuddleType): PagingSource<Int, HuddleChatMessageWithMedia>

    // Private Chats

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertPrivateChatMessages(huddleChat: List<PrivateChatMessage>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertChat(privateChat: PrivateChatMessage): Long

    @Update
    abstract suspend fun updateChat(privateChat: PrivateChatMessage): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${PrivateChatMessage.COLUMN_MESSAGE_ID} = :id")
    abstract suspend fun deletePrivateChatMessage(id: String)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${PrivateChatMessage.COLUMN_ROOM_ID} = :roomId")
    abstract suspend fun deleteAllPrivateChatMessages(roomId: String)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${PrivateChatMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract suspend fun getPrivateChatMessage(id: String): PrivateChatMessage?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${PrivateChatMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract suspend fun getPrivateChatMessageWithMedia(id: String): PrivateChatMessageWithMedia?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${PrivateChatMessage.COLUMN_ROOM_ID} = :id ORDER BY ${HuddleChatMessage.COLUMN_MESSAGE_CREATED} DESC, ${HuddleChatMessage.COLUMN_MESSAGE_ID} DESC")
    abstract fun privateChatsPagingSource(id: String): PagingSource<Int, PrivateChatMessageWithMedia>

    @Query("SELECT COUNT(*) FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${PrivateChatMessage.COLUMN_ROOM_ID} = :roomId AND ${PrivateChatMessage.COLUMN_MESSAGE_CREATED} > :time")
    abstract suspend fun countPrivateChatsAfter(time: String, roomId: String): Int

    @Query("UPDATE ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} SET ${AbstractChatMessage.COLUMN_SEND_STATUS} = :status WHERE ${PrivateChatMessage.COLUMN_MESSAGE_ID} = :messageId")
    abstract suspend fun updatePrivateChatStatus(messageId: String, status: SendStatus)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${AbstractChatMessage.COLUMN_SEND_STATUS} = :status ORDER BY ${PrivateChatMessage.COLUMN_MESSAGE_CREATED} ASC LIMIT 1")
    abstract suspend fun getPrivateChatsByStatus(status: SendStatus): PrivateChatMessageWithMedia?

    suspend fun getPendingPrivateChats() = getPrivateChatsByStatus(SendStatus.PENDING)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${PrivateChatMessage.COLUMN_ROOM_ID} = :id ORDER BY ${HuddleChatMessage.COLUMN_MESSAGE_CREATED} DESC, ${HuddleChatMessage.COLUMN_MESSAGE_ID} DESC LIMIT 1 OFFSET :pos")
    abstract suspend fun getPrivateMessageAfterPosition(id: String, pos: Int): PrivateChatMessage?

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} WHERE ${PrivateChatMessage.COLUMN_ROOM_ID} = :id AND ${PrivateChatMessage.COLUMN_MESSAGE_READ} IS NULL ORDER BY ${HuddleChatMessage.COLUMN_MESSAGE_CREATED} ASC, ${HuddleChatMessage.COLUMN_MESSAGE_ID} ASC LIMIT 1")
    abstract suspend fun getOldestUnreadMessage(id: String): PrivateChatMessage?

    // Broadcasts

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertBroadcastMessages(broadcast: List<BroadcastMessage>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertBroadcast(broadcast: BroadcastMessage): Long

    @Update
    abstract suspend fun updateBroadcast(broadcast: BroadcastMessage): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} WHERE ${BroadcastMessage.COLUMN_MESSAGE_ID} = :id")
    abstract suspend fun deleteBroadcastMessage(id: String)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} WHERE ${BroadcastMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract suspend fun getBroadcastMessage(id: String): BroadcastMessage?

    @Query("UPDATE ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} SET ${AbstractChatMessage.COLUMN_SEND_STATUS} = :status WHERE ${BroadcastMessage.COLUMN_MESSAGE_ID} = :messageId")
    abstract suspend fun updateBroadcastChatStatus(messageId: String, status: SendStatus)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} WHERE ${AbstractChatMessage.COLUMN_SEND_STATUS} = :status ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} ASC LIMIT 1")
    abstract suspend fun getBroadcastsByStatus(status: SendStatus): BroadcastChatMessageWithMedia?

    suspend fun getPendingBroadcasts() = getBroadcastsByStatus(SendStatus.PENDING)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} WHERE ${BroadcastMessage.COLUMN_BROADCAST_ID} = :id LIMIT 1")
    abstract suspend fun getBroadcastMessageByBroadcastId(id: String): BroadcastMessage?

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC LIMIT :count")
    abstract suspend fun getLastBroadcastMessagesByCount(count: Int): List<BroadcastMessage>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC LIMIT 1")
    abstract suspend fun getLastBroadcastMessages(broadcaster: Int): BroadcastMessage?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} WHERE ${BroadcastMessage.COLUMN_MESSAGE_ID} = :id LIMIT 1")
    abstract suspend fun getBroadcastMessageWithMedia(id: String): BroadcastChatMessageWithMedia?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE}" +
                   " WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster" +
                   " AND ${BroadcastMessage.COLUMN_BROADCAST_MODE} = :mode" +
                   " ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC")
    abstract fun outgoingBroadcastsPagingSource(broadcaster: Int, mode: BroadcastMode): PagingSource<Int, BroadcastChatMessageWithMedia>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} " +
                   " WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster" +
                   " AND ${BroadcastMessage.COLUMN_BROADCAST_MODE} = :mode" +
                   " AND ${BroadcastMessage.COLUMN_STARRED} = 1" +
                   " ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC")
    abstract fun outgoingStarredBroadcastsPagingSource(broadcaster: Int, mode: BroadcastMode): PagingSource<Int, BroadcastChatMessageWithMedia>

    @Transaction
    @Query("SELECT " +
                   "(SELECT COUNT(*) FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE}" +
                        " WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster" +
                        " AND ${BroadcastMessage.COLUMN_BROADCAST_MODE} = :mode" +
                        " AND ${BroadcastMessage.COLUMN_MESSAGE_CREATED} > t.${BroadcastMessage.COLUMN_MESSAGE_CREATED}) as position," +
                   " ${BroadcastMessage.COLUMN_MESSAGE_ID} as messageId FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} t" +
                   " WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster" +
                   " AND ${BroadcastMessage.COLUMN_BROADCAST_MODE} = :mode" +
                   " AND ${BroadcastMessage.COLUMN_MESSAGE} LIKE '%' || :search || '%'" +
                   " ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC")
    abstract suspend fun outgoingBroadcastsSearchResult(broadcaster: Int, mode: BroadcastMode, search: String): List<ChatMessageSearchResult>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE}" +
                   " WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster" +
                   " ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC")
    abstract fun incomingBroadcastsPagingSource(broadcaster: Int): PagingSource<Int, BroadcastChatMessageWithMedia>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE}" +
                   " WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster" +
                   " AND ${BroadcastMessage.COLUMN_STARRED} = 1" +
                   " ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC")
    abstract fun incomingStarredBroadcastsPagingSource(broadcaster: Int): PagingSource<Int, BroadcastChatMessageWithMedia>

    @Transaction
    @Query("SELECT " +
                   "(SELECT COUNT(*) FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE}" +
                       " WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster" +
                       " AND ${BroadcastMessage.COLUMN_MESSAGE_CREATED} > t.${BroadcastMessage.COLUMN_MESSAGE_CREATED}) as position," +
                   " ${BroadcastMessage.COLUMN_MESSAGE_ID} as messageId FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} t" +
                   " WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster" +
                   " AND ${BroadcastMessage.COLUMN_MESSAGE} LIKE '%' || :search || '%'" +
                   " ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC")
    abstract suspend fun incomingBroadcastsSearchResult(broadcaster: Int, search: String): List<ChatMessageSearchResult>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} WHERE ${BroadcastMessage.COLUMN_BROADCASTER} = :broadcaster ORDER BY ${BroadcastMessage.COLUMN_MESSAGE_CREATED} DESC LIMIT 1")
    abstract fun getLatestBroadcast(broadcaster: Int): BroadcastChatMessageWithMedia?

    // Shared

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertMedia(media: OfflineMedia): Long

    @Update
    abstract suspend fun updateMedia(media: OfflineMedia): Int

    @Delete
    abstract suspend fun deleteMedia(vararg media: OfflineMedia): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_OFFLINE_MEDIA} WHERE ${OfflineMedia.COLUMN_MESSAGE_ID} = :messageId")
    abstract suspend fun deleteMedia(messageId: String): Int

    @Query("SELECT med.* FROM ${EntityDescriptions.TABLE_OFFLINE_MEDIA} as med " +
                   " LEFT JOIN ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} as hm ON med.${OfflineMedia.COLUMN_MESSAGE_ID} = hm.${HuddleChatMessage.COLUMN_MESSAGE_ID}" +
                   " LEFT JOIN ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} as pm ON med.${OfflineMedia.COLUMN_MESSAGE_ID} = pm.${PrivateChatMessage.COLUMN_MESSAGE_ID}" +
                   " LEFT JOIN ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} as bm ON med.${OfflineMedia.COLUMN_MESSAGE_ID} = bm.${BroadcastMessage.COLUMN_MESSAGE_ID}" +
                   " LEFT JOIN ${EntityDescriptions.TABLE_HUDDLE_REPORTED_MESSAGES} as rm ON med.${OfflineMedia.COLUMN_MESSAGE_ID} = rm.${HuddleReportedMessage.COLUMN_MESSAGE_ID}" +
                   " WHERE hm.${HuddleChatMessage.COLUMN_MESSAGE_ID} IS NULL" +
                   " AND pm.${PrivateChatMessage.COLUMN_MESSAGE_ID} IS NULL" +
                   " AND bm.${BroadcastMessage.COLUMN_MESSAGE_ID} IS NULL" +
                   " AND rm.${HuddleReportedMessage.COLUMN_MESSAGE_ID} IS NULL")
    abstract suspend fun getOrphanedMedia(): List<OfflineMedia>

    @Query("UPDATE ${EntityDescriptions.TABLE_OFFLINE_MEDIA} SET ${OfflineMedia.COLUMN_UPLOAD_STATE}=:set WHERE ${OfflineMedia.COLUMN_UPLOAD_STATE} = :current")
    abstract suspend fun resetMediaUploadState(current: String?, set: String?)

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} SET ${AbstractChatMessage.COLUMN_SEND_STATUS}=:set WHERE ${AbstractChatMessage.COLUMN_SEND_STATUS} = :current")
    abstract suspend fun resetHuddleChatSendStatus(current: String?, set: String?)

    @Query("UPDATE ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} SET ${AbstractChatMessage.COLUMN_SEND_STATUS}=:set WHERE ${AbstractChatMessage.COLUMN_SEND_STATUS} = :current")
    abstract suspend fun resetPrivateChatSendStatus(current: String?, set: String?)

    @Query("UPDATE ${EntityDescriptions.TABLE_BROADCAST_MESSAGE} SET ${AbstractChatMessage.COLUMN_SEND_STATUS}=:set WHERE ${AbstractChatMessage.COLUMN_SEND_STATUS} = :current")
    abstract suspend fun resetBroadcastChatSendStatus(current: String?, set: String?)

    @Transaction
    open suspend fun resetSendStatus() {
        resetHuddleChatSendStatus(SendStatus.SENDING.name, SendStatus.PENDING.name)
        resetPrivateChatSendStatus(SendStatus.SENDING.name, SendStatus.PENDING.name)
        resetBroadcastChatSendStatus(SendStatus.SENDING.name, SendStatus.PENDING.name)
    }

    @Transaction
    open suspend fun resetUploadingMedia() {
        val converter = MediaUploadState.Converter()
        resetMediaUploadState(converter.encodeUploadState(MediaUploadState.Uploading(0)), converter.encodeUploadState(MediaUploadState.Pending))
    }

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_OFFLINE_MEDIA} WHERE ${OfflineMedia.COLUMN_MESSAGE_ID} = :id")
    abstract suspend fun getMedia(id: String): OfflineMedia?

    @Query("UPDATE ${EntityDescriptions.TABLE_OFFLINE_MEDIA} SET ${OfflineMedia.COLUMN_MESSAGE_ID} = :newId WHERE ${OfflineMedia.COLUMN_MESSAGE_ID} = :oldId")
    abstract suspend fun updateMediaMessageId(oldId: String, newId: String)

    @Query("UPDATE ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} " +
                   "SET ${PrivateChatMessage.COLUMN_MESSAGE_READ}=:readTime " +
                   "WHERE ${PrivateChatMessage.COLUMN_ROOM_ID} = :roomId AND :createdTime <= ${PrivateChatMessage.COLUMN_MESSAGE_CREATED} AND blocked = :isBlock")
    abstract suspend fun updateReadAllMessages(readTime: String, roomId: String, createdTime: String, isBlock: Boolean = false)

    @Query("UPDATE ${EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES} " +
                   "SET ${PrivateChatMessage.COLUMN_MESSAGE_DELIVERED}=:deliveredTime " +
                   "WHERE ${PrivateChatMessage.COLUMN_ROOM_ID} = :roomId AND :createdTime >= ${PrivateChatMessage.COLUMN_MESSAGE_CREATED} ")
    abstract suspend fun updateDeliveredAllMessages(deliveredTime: String, roomId: String, createdTime: String)

    @Query("UPDATE ${EntityDescriptions.TABLE_HUDDLE_MESSAGES} " +
                   "SET ${PrivateChatMessage.COLUMN_MESSAGE_DELIVERED}=:deliveredTime " +
                   "WHERE ${PrivateChatMessage.COLUMN_ROOM_ID} = :roomId AND :createdTime >= ${PrivateChatMessage.COLUMN_MESSAGE_CREATED} ")
    abstract suspend fun updateHuddleDeliveredAllMessages(deliveredTime: String, roomId: String, createdTime: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertHuddlePostComments(huddleChat: List<PostCommentItem>): List<Long>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_POST_COMMENTS} WHERE ${PostCommentItem.COLUMN_MESSAGE_ID} = :id")
    abstract suspend fun deleteHuddlePostComments(id: String)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_POST_COMMENTS} WHERE ${PostCommentItem.COLUMN_HUDDLE_ID} = :id")
    abstract suspend fun deleteHuddleComments(id: Int)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_POST_COMMENTS} WHERE ${PostCommentItem.COLUMN_MESSAGE_ID} = :id")
    abstract fun getHuddlePostComments(id: String): PagingSource<Int, PostCommentItem>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_POST_COMMENTS} WHERE ${PostCommentItem.COLUMN_COMMENT_ID} = :commentId")
    abstract suspend fun deletePostSingleComment(commentId: String)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_POST_COMMENTS} WHERE ${PostCommentItem.COLUMN_COMMENT_ID} = :commentId")
    abstract suspend fun getHuddlePostSingleComment(commentId: String): PostCommentItem?

    @Update
    abstract suspend fun updateHuddlePostComment(postCommentItem: PostCommentItem): Int
    // Reported comments
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertHuddleReportedComments(reportedComments: List<HuddleReportedComment>): List<Long>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_HUDDLE_REPORTED_COMMENTS} WHERE ${HuddleReportedComment.COLUMN_HUDDLE_ID} = :huddleId")
    abstract suspend fun deleteAllHuddleReportedComments(huddleId: Int)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_HUDDLE_REPORTED_COMMENTS} WHERE ${HuddleReportedComment.COLUMN_HUDDLE_ID} = :id ORDER BY ${HuddleReportedComment.COLUMN_MESSAGE_CREATED} DESC, ${HuddleReportedComment.COLUMN_COMMENT_ID} DESC")
    abstract fun huddleReportedCommentPagingSource(id: Int): PagingSource<Int, HuddleReportedComment>

}