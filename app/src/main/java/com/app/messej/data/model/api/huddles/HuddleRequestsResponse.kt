package com.app.messej.data.model.api.huddles

import com.google.gson.annotations.SerializedName

data class HuddleRequestsResponse(
    @SerializedName("current_page" ) val currentPage : Int?                = null,
    @SerializedName("next_page"    ) val nextPage    : Boolean,
    @SerializedName("requests"     ) val requests    : ArrayList<HuddleRequest> = arrayListOf(),
    @SerializedName("total"        ) val total       : Int?                = null
){
    data class HuddleRequest (

        @SerializedName("is_premium"       ) val isPremium       : Boolean? = null,
        @SerializedName("member_id"        ) val memberId        : Int,
        @SerializedName("member_name"      ) var memberName      : String?  = null,
        @SerializedName("member_thumbnail" ) val memberThumbnail : String?  = null,
        @SerializedName("member_username"  ) val memberUsername  : String?  = null,
        @SerializedName("request_id"       ) val requestId       : Int?     = null,
        @SerializedName("status"           ) val status          : Status?  = null,
        @SerializedName("verified"         ) val verified        : Boolean? = null

    )

    enum class Status() {
        @SerializedName("Requested") REQUESTED,
        @SerializedName("Invited") INVITED,
        @SerializedName("Declined") DECLINED,
        @SerializedName("Accepted") ACCEPTED,
        @SerializedName("Blocked") BLOCKED,
        @SerializedName("Pending") PENDING
    }
}
