package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.BoxData
import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.enums.ConFourGameStatus
import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoardModel
import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoardModel.LineDirection
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard
import com.google.gson.annotations.SerializedName

data class BoxChallengeLineDrawnPayload(
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("podium_id") val podiumId: String?,
//    @SerializedName("user_id") val userId: Int,
//    @SerializedName("opponent_user_id") val opponentUserId: Int,

    @SerializedName("participant_token_number") val participantTokenNumber: Int? = null,
    @SerializedName("row") val row: Int? = null,
    @SerializedName("column") val column: Int? = null,
    @SerializedName("status") val status: ConFourGameStatus?,

    @SerializedName("turn_start_time") val turnStartTime: Double,
    @SerializedName("current_player") val currentPlayerUserId: Int,
    @SerializedName("box"                ) private var _board                      : List<List<Int>>? = null,
    @SerializedName("hlines"             ) private var _hLines                     : List<List<Int>>? = null,
    @SerializedName("vlines"             ) private var _vLines                     : List<List<Int>>? = null,

    // Only for Box Challenge
    @SerializedName("direction") val lineDirection: LineDirection? = null,
    ) : SocketEventPayload() {

    val lastDrop: ConnectFourBoard.DropPoint?
        get() {
            row ?: return null
            column ?: return null
            return ConnectFourBoard.DropPoint(row, column)
        }

    val lastLine: BoxChallengeBoardModel.Line?
        get() {
            row ?: return null
            column ?: return null
            lineDirection?: return null
            return when(lineDirection) {
                LineDirection.VERTICAL -> BoxChallengeBoardModel.Line.Vertical(row, column)
                LineDirection.HORIZONTAL -> BoxChallengeBoardModel.Line.Horizontal(row, column)
            }
        }

    val lastDropPlayer: ChallengePlayer?
        get() = participantTokenNumber?.let { ChallengePlayer.fromToken(it) }

    val boxData: BoxData
        get() = BoxData(currentPlayerUserId,turnStartTime,_board,_hLines,_vLines)
}
