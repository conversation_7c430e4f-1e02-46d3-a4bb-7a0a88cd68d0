package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.forward.ForwardHuddleRequest
import com.app.messej.data.model.api.forward.ForwardListResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Query

interface ForwardAPIService {
    @GET("/chat/chats/forward")
    @Headers("Accept: application/json")
    suspend fun getForwardPrivateMessageList(
        @Query("type") keyword: String?="PRIVATE",
        @Query("page_count") limit: Int? = 50,
        @Query("pageState") pageState: String? = null,
        @Query("page") page: Int,
        @Query("intruders") intruders: Int = 0
    ): Response<APIResponse<ForwardListResponse>>


    @GET("/chat/chats/forward")
    @Headers("Accept: application/json")
    suspend fun getForwardPrivateMessageSearchList(
        @Query("type") type: String?="PRIVATE",
        @Query("keyword") keyword: String,
        @Query("page_count") limit: Int? = 50,
        @Query("pageState") pageState: String? = null,
        @Query("page") page: Int,
        @Query("intruders") intruders: Int = 0
    ): Response<APIResponse<ForwardListResponse>>


    @GET("/huddles/forwardslist")
    @Headers("Accept: application/json")
    suspend fun getForwardList(
        @Query("type") type: String,
        @Query("page_count") limit: Int? = 50,
        @Query("page") page: Int,
    ): Response<APIResponse<ForwardListResponse>>

    @GET("/huddles/forwardslist")
    @Headers("Accept: application/json")
    suspend fun getForwardListSearch(
        @Query("type") type: String,
        @Query("keyword") keyword: String,
        @Query("page_count") limit: Int? = 50,
        @Query("page") page: Int,
    ): Response<APIResponse<ForwardListResponse>>

    @POST("/huddles/forward")
    @Headers("Accept: application/json")
    suspend fun sendHuddleForward(
        @Body request: ForwardHuddleRequest
    ): Response<APIResponse<Unit>>


}