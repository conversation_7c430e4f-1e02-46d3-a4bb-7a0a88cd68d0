package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.api.podium.challenges.PodiumGiftSupportersResponse.ChallengeSupporter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PodiumGiftChallengeSupportersListDataSource(private val api: PodiumAPIService, private val podiumId: String, val challengeId: String? = null): PagingSource<Int, ChallengeSupporter>() {
    companion object {
        private const val STARTING_KEY = 1
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, ChallengeSupporter> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getGiftChallengeSupporters(podiumId, challengeId, page = currentPage, perPage = 50)
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (data.hasNext) null else currentPage.inc()

                LoadResult.Page(
                    data = data.supporters, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, ChallengeSupporter>): Int? {
        return null
    }
}