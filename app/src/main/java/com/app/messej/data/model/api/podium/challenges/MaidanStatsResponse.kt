package com.app.messej.data.model.api.podium.challenges

import com.google.gson.annotations.SerializedName

data class MaidanStatsResponse(
    @SerializedName("my_stats")                         val myStats: MyStats,
    @SerializedName("competitior_stats")                val competitorStats: MaidanStatsResponseItem,
    @SerializedName("challenge_history")                val challengeHistory: MaidanStatsResponseItem,
)

data class MyStats(
    @SerializedName("wins")                             val wins: TimePeriod,
    @SerializedName("loses")                            val loses: TimePeriod,
    @SerializedName("played")                           val played: TimePeriod,
)

data class MaidanStatsResponseItem(
    @SerializedName("per_page")                         val perPage: Int? = null,
    @SerializedName("data")                             val data: List<PodiumMaidanSupporter> = listOf(),
    @SerializedName("has_next")                         val hasNext: Boolean? = null,
    @SerializedName("total_items")                      val totalItems: Int? = null,
    @SerializedName("current_page")                     val currentPage: Int? = null,
)

data class TimePeriod(
    @SerializedName("ever")                             val ever: Int? = null,
    @SerializedName("month")                            val month: Int? = null,
    @SerializedName("year")                             val year: Int? = null,
)



