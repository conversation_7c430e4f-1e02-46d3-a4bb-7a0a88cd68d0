package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.profile.UserBirthdayResponse
import com.app.messej.data.model.api.promo.PromoHistoryResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST

interface AppAPIService {

    @POST("/user/homeapp/operations/appsharecount")
    @Headers("Accept: application/json")
    suspend  fun countAppShare(): Response<APIResponse<Unit>>

    @GET("/user/user-daily-sync")
    @Headers("Accept: application/json")
    suspend fun getBirthdays(): Response<APIResponse<UserBirthdayResponse>>

    @GET("/user/announcements")
    @Headers("Accept: application/json")
    suspend fun getAnnouncements(): Response<APIResponse<PromoHistoryResponse>>
}
