package com.app.messej.data.model.socket

import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.enums.UserRole
import com.google.gson.annotations.SerializedName

data class HuddleAdminStatusUpdateEvent(
    @SerializedName("huddle_id") var id: Int = 0,
    @SerializedName("member_id") var memberId: Int = 0,
    @SerializedName("admin_status") var adminStatus: AbstractHuddle.HuddleAdminStatus? = null,
    @SerializedName("status") var userStatus: AbstractHuddle.HuddleUserStatus,
    @SerializedName("role") var role: UserRole?,
    )
