package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PushNotificationType {
    @SerializedName("forceful_username_change"      ) FORCED_USERNAME_CHANGE,
    @SerializedName("forceful_dob_change"           ) FORCED_DOB_CHANGE,
    @SerializedName("forceful_gender_change"        ) FORCED_GENDER_CHANGE,
    @SerializedName("suggest_new_username"          ) NEW_USERNAME_SUGGESTION,
    @SerializedName("user_profile_edited"           ) USER_PROFILE_EDITED,
//    @SerializedName("renew_subscription_reminder"   ) SUBSCRIPTION_RENEWAL_REMINDER,
    @SerializedName("new_fan"                       ) NEW_FAN,
    @SerializedName("new_dear"                      ) NEW_DEAR,
   @SerializedName("new_follower"                   ) NEW_FOLLOWER,

    @SerializedName("broadcast"                     ) NEW_BROADCAST,
    @SerializedName("otom"                          ) NEW_PRIVATE_MESSAGE,
    @SerializedName("huddle"                        ) NEW_HUDDLE_MESSAGE,
    @SerializedName("comment"                       ) NEW_HUDDLE_COMMENT,
    @SerializedName("delete_huddle"                 ) HUDDLE_DELETED,
    @SerializedName("silent_notification"           ) UNREAD_COUNT_UPDATE,

    @SerializedName("accept_join_request"           ) JOIN_REQUEST_ACCEPTED,
    @SerializedName("admin_invite"                  ) ADMIN_INVITE_RECEIVED,
    @SerializedName("remove_participant"            ) PARTICIPANT_REMOVED,
    @SerializedName("invite_participant"            ) PARTICIPANT_INVITED,
    @SerializedName("join_request_notification"     ) JOIN_REQUEST_RECEIVED,
    @SerializedName("deals"                         ) DEALS,
    @SerializedName("comment_created"               ) FLASH_COMMENT_RECEIVED,
    @SerializedName("flash_comment_reply"           ) FLASH_COMMENT_REPLY_RECEIVED,
    @SerializedName("comment_reported"              ) FLASH_COMMENT_REPORTED,
    @SerializedName("poll_invite"                   ) POLL_INVITE,
    @SerializedName("notify_manager"                ) POLL_NOTIFY_MANAGER,
    @SerializedName("podium"                        ) PODIUM,
    @SerializedName("invited_to_podium"             ) PODIUM_INVITE,
    @SerializedName("reported_podium_winner"             ) REPORTED_PODIUM_WINNER,
    @SerializedName("gifts"                         ) GIFT_NOTIFY_MANAGER,
    @SerializedName("invited_to_contribute_podium_challenge"    ) CHALLENGE_CONTRIBUTION_INVITE,
    @SerializedName("invited_to_support_maidan"    ) MAIDAN_SUPPORTER_INVITE,
    @SerializedName("others", alternate = ["other"]             ) OTHERS,
    @SerializedName("birthdays"                                 ) BIRTHDAY_NOTIFICATION,
    @SerializedName("postat_comment"                ) POSTAT_COMMENT,
    @SerializedName("postat_mention"        ) POSTAT_MENTION,
    @SerializedName("president") PRESIDENT
}