package com.app.messej.data.model.api.postat

import com.google.gson.annotations.SerializedName

data class MusicFile(
    @SerializedName("song_name"     ) var songName     : String,
    @SerializedName("composer_name" ) var composerName : String?           = null,
//    @SerializedName("singers_name"  ) var singersName  : ArrayList<String> = arrayListOf(),
    @SerializedName("movie_name"    ) var movieName    : String?           = null,
    @SerializedName("description"   ) var description  : String?           = null,
    @SerializedName("last_modified" ) var lastModified : String?           = null,
    @SerializedName("media_meta"    ) var mediaMeta    : MediaMeta
) {
    data class MediaMeta (
        @SerializedName("media_url" ) var mediaUrl : String,
        @SerializedName("music_id"  ) var musicId  : String
    )
}
