package com.app.messej.data.utils

import com.app.messej.data.Constants
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object AuthUtil {

    fun encryptAES(stringToEncrypt: String): String {
        val initVector = Constants.ENC256_KEY_IV.toByteArray(charset("UTF8"))
        val ivSpec = IvParameterSpec(initVector)
        val pass = Constants.ENC256_KEY.toByteArray(charset("UTF8"))
        val key = SecretKeySpec(pass, "AES")
        val data = stringToEncrypt.toByteArray(charset("UTF8"))
        val cipher = Cipher.getInstance("AES/CBC/PKCS7PADDING")

        cipher.init(Cipher.ENCRYPT_MODE, key, ivSpec)
        val cipherText = cipher.doFinal(data)
        return Base64.getEncoder().encodeToString(cipherText)
    }

    fun decryptAES(stringToDecrypt: String): String {
        val initVector = Constants.ENC256_KEY_IV.toByteArray(charset("UTF8"))
        val ivSpec = IvParameterSpec(initVector)
        val pass = Constants.ENC256_KEY.toByteArray(charset("UTF8"))
        val key = SecretKeySpec(pass, "AES")
        val data = stringToDecrypt.toByteArray(charset("UTF8"))
        val cipher = Cipher.getInstance("AES/CBC/PKCS7PADDING")

        cipher.init(Cipher.DECRYPT_MODE, key, ivSpec)
        val plainText = cipher.doFinal(Base64.getDecoder().decode(data))
        return String(plainText)

    }

    fun encodeToBase64(input: String): String {
        val inputData = input.toByteArray(Charsets.UTF_8)
        return Base64.getEncoder().encodeToString(inputData)
    }
}