package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.Constants
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

private const val STARTING_KEY = 1
class HuddleInnerSearchDataSource(private val api: ChatAPIService,
                                  private val searchKeyWord: String,
                                  private val huddleId: Int?,
                                  val accountRepo: AccountRepository
): PagingSource<Int, HuddleChatMessageWithMedia>() {
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, HuddleChatMessageWithMedia> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.huddleInnerSearch(
                    currentPage,searchKeyWord,huddleId!!
                )

                val result = if (response.isSuccessful && response.code() == 200) {
                    response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                } else {
                    val error: ErrorResponse =
                        ErrorResponse.parseError(response = response.errorBody()!!)
                    throw Exception(error.message)
                }
                fun sanitizeChatMessage(msg: HuddleChatMessage) {

                    msg.huddleType = HuddleType.PUBLIC
                    //For delete message of manager removed messages
                    if (msg.deleted && msg.remover != null){
                        if (msg.remover.id?.toInt() == accountRepo.user.id) msg.displayMessage = msg.remover.message
                    }
                    msg.sanitize()
                }

                val messages = result.messages
                messages.forEach {
                    sanitizeChatMessage(it)
                }
                val nextKey = if (result.nextPage== false) null else currentPage.plus(1)

                LoadResult.Page(
                    data = result.messages.map { HuddleChatMessageWithMedia(message = it) }.toMutableList(),
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )

            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, HuddleChatMessageWithMedia>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
//    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)

}