package com.app.messej.data.model.notification

import com.google.gson.annotations.SerializedName

data class BirthdayNotification(
    @SerializedName("associated_obj_id"     ) val associatedObjId: Any,
    @SerializedName("category"              ) val category: String,
    @SerializedName("message"               ) val message: String,
    @SerializedName("notification_id"       ) val notificationId: Int,
    @SerializedName("notification_type"     ) val notificationType: String,
    @SerializedName("other_data"            ) val otherData: OtherData,
    @SerializedName("reason"                ) val reason: Any,
    @SerializedName("receiver_id"           ) val receiverId: Int,
    @SerializedName("sender_id"             ) val senderId: Int,
    @SerializedName("thumbnail"             ) val thumbnail: String,
)

data class OtherData(
    @SerializedName("date_of_birth"              ) val dateOfBirth: String,
    @SerializedName("name"                       ) val name: String,
    @SerializedName("profile_photo"              ) val profilePhoto: Any,
    @SerializedName("user_id"                    ) val userId: Int,
    @SerializedName("animation_url_android"      ) val animationUrlAndroid: String?,
)