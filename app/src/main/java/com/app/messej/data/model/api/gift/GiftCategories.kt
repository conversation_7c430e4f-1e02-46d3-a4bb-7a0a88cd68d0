package com.app.messej.data.model.api.gift

import com.google.gson.annotations.SerializedName

data class GiftCategories(
   @SerializedName("category_name") val categoryName: String,
   @SerializedName("category_id") val categoryId: Int,
   @SerializedName("balance") val balance: Double,
   @SerializedName("data") val data: List<GiftItem>? = listOf()
){
   val balanceFormatted: String
      get() = balance.toString()
}
