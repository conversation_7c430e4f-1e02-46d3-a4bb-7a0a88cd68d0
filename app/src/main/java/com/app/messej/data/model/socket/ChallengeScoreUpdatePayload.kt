package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.ChallengeType
import com.google.gson.annotations.SerializedName

data class ChallengeScoreUpdatePayload(
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("game_type") val challengeType: ChallengeType,
    @SerializedName("podium_id") val podiumId: String?,
    @SerializedName("end_timestamp_utc") val endTimeStampUtc: Long,

    @SerializedName("sender_id") val senderId: Int,
    @SerializedName("recipient_id") val recipientId: Int,
    @SerializedName("increment_by") val incrementBy: Double?,
    ) : SocketEventPayload() {
        companion object {
            fun from(challenge: PodiumChallenge, sender: Int, receiver: Int, increment: Double) = ChallengeScoreUpdatePayload(
                challengeId = challenge.challengeId,
                challengeType = challenge.challengeType,
                podiumId = challenge.podiumId,
                endTimeStampUtc = challenge.parsedEndTime?.toEpochSecond()?:0L,

                senderId = sender,
                recipientId = receiver,
                incrementBy = increment
            )
        }
    }
