package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class FlaxTransferPayload(
    @SerializedName("flax")
    val flax:Double,
    @SerializedName("flax_before_flaxrate")
    val flaxBeforeFlaxRate : Double,
    @SerializedName("transfer_animation")
    val transferAnimation :String,
    @SerializedName("user_id")
    val userId: Int,
    @SerializedName("user_name")
    val userName : String
)
