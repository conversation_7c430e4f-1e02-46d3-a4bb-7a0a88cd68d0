package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.ChatTextColor
import com.google.gson.annotations.SerializedName

data class BroadcastMessagePayload(
    @SerializedName("broadcastId") val broadcastId: String,
    @SerializedName("broadcastType") val broadcastType: BroadcastMode,
    @SerializedName("message") override val message: String?,
    @SerializedName("color") override val color: ChatTextColor?
): AbstractChatMessagePayload()