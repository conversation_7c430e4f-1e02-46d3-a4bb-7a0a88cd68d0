package com.app.messej.data.model.api.eTribe

import android.content.Context
import androidx.annotation.DrawableRes
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.DateFormatHelper
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

data class ETribeResponse(
    @SerializedName("next_page") val nextPage: Int? = null,
    @SerializedName("members") val members: List<ETribeMembers>? = null,
    @SerializedName("superstar_tribe_details") val superStarTribeDetail: SuperStarTribeDetail? = null,
    @SerializedName("tribe_name") val tribeName: String? = null,
    @SerializedName("tribe_image") val tribeImage: String? = null,
    @SerializedName("total_members") val totalMembers: Int? = null,
    @SerializedName("total_active_members") val totalActiveMembers: Int? = null,
    @SerializedName("total_inactive_members") val totalInactiveMembers: Int? = null,
    @SerializedName("tribe_id") val tribeId: Int? = null,
    @SerializedName("tribe_status") val tribeStatus: String? = null,
) {
    companion object {
        const val ADMIN_DELETED = "admin_deleted"
        const val INACTIVE = "inactive"
    }

    data class SuperStarTribeDetail(
        @SerializedName("superstar_name") val superStarName: String? = null,
        @SerializedName("superstar_tribe_name") val tribeName: String? = null,
        @SerializedName("superstar_tribe_id") val superStarTribeId: Int? = null,
        @SerializedName("superstar_tribe_image") val superStarTribeImage: String? = null,
        @SerializedName("superstar_tribe_status") val superStarTribeStatus: String? = null,
    )

    data class ETribeMembers(
        @SerializedName("member_id") override val id: Int,
        @SerializedName("citizenship") override val citizenship: UserCitizenship? = null,
        @SerializedName("membership") override val membership: UserType,
        @SerializedName("username") override val username: String,
        @SerializedName("verified") override val verified: Boolean,
        @SerializedName("name") override val name: String,
        @SerializedName("thumbnail") override val thumbnail: String? = null,
        @SerializedName("country_code_iso") override val countryCode: String? = null,

        @SerializedName("last_seen") val lastSeen: String? = null,
        @SerializedName("online") val isOnline: Boolean? = null,
        @SerializedName("admin_status") val adminStatus: String? = null,
        @SerializedName("contributor_level") val contributorLevel: String? = null,
        @SerializedName("is_admin") val isAdmin: Boolean? = null,
        @SerializedName("is_manager") val isManager: Boolean? = null,
        @SerializedName("member_tribe_details") val memberTribeDetails: MemberTribeDetails? = null,
        @SerializedName("member_tribe_exists") val memberTribeExists: Boolean? = null,
        @SerializedName("role") val role: String? = null,
        @SerializedName("status") val status: String? = null,
        @SerializedName("user_blocked") val userBlocked: Boolean? = null,
        @SerializedName("user_rating") val userRating: Double? = null,
        @DrawableRes val countryFlag: Int? = null
    ) : AbstractUser() {

        private val lastSeenDateTime: ZonedDateTime?
            get() = DateTimeUtils.parseZonedDateTime(lastSeen)

        fun lastSeenHumanized(c: Context): String {
            lastSeen?: return ""
            return DateFormatHelper.humanizeMessageTime(lastSeenDateTime, c)
        }
    }

    data class MemberTribeDetails(
        @SerializedName("active_users") val activeUsers: Int? = null,
        @SerializedName("inactive_users") val inactiveUsers: Int? = null,
        @SerializedName("thumbnail_url") val thumbnailUrl: String? = null,
        @SerializedName("total_users") val totalUsers: Int? = null
    )

}


