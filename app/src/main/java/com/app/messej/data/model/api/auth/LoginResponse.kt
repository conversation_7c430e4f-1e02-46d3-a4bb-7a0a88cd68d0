package com.app.messej.data.model.api.auth

import com.app.messej.data.model.CurrentUser
import com.google.gson.annotations.SerializedName

data class LoginResponse(
    @SerializedName("access_token"  ) var accessToken  : String,
    @SerializedName("refresh_token" ) var refreshToken : String,
    @SerializedName("user"          ) var user         : CurrentUser,

    @SerializedName("unread_private_chats"          ) var unreadPrivateChats: Int,
    @SerializedName("unread_private_huddles"        ) var unreadPrivateHuddles: Int,
    @SerializedName("my_huddles_count"              ) var myHuddlesCount: Int,
    @SerializedName("my_admin_huddles_count"        ) var adminHuddlesCount: Int,
    @SerializedName("my_joined_huddles_count"       ) var joinedHuddlesCount: Int,
    @SerializedName("unread_private_chat_requests"  ) var unreadPrivateChatRequests: Int,
    @SerializedName("unread_stars_messages"         ) var unreadStarMessages: Int,

    @SerializedName("google_user_info"         ) var googleUserInfo        : GoogleUserInfo? = null,
    @SerializedName("is_new_user_account"      ) var isNewUserAccount      : Boolean?        = false,
    @SerializedName("is_new_user_profile"      ) var isNewUserProfile      : Boolean?        = false,
    @SerializedName("is_new_social_connection" ) var isNewSocialConnection : Boolean?        = false

) {
    data class GoogleUserInfo (

//        @SerializedName("iss"            ) var iss           : String?  = null,
//        @SerializedName("azp"            ) var azp           : String?  = null,
//        @SerializedName("aud"            ) var aud           : String?  = null,
//        @SerializedName("sub"            ) var sub           : String?  = null,
//        @SerializedName("hd"             ) var hd            : String?  = null,
        @SerializedName("email"          ) var email         : String?  = null,
        @SerializedName("email_verified" ) var emailVerified : Boolean? = null,
//        @SerializedName("at_hash"        ) var atHash        : String?  = null,
//        @SerializedName("nonce"          ) var nonce         : String?  = null,
        @SerializedName("name"           ) var name          : String?  = null,
//        @SerializedName("picture"        ) var picture       : String?  = null,
//        @SerializedName("given_name"     ) var givenName     : String?  = null,
//        @SerializedName("family_name"    ) var familyName    : String?  = null,
//        @SerializedName("iat"            ) var iat           : Int?     = null,
//        @SerializedName("exp"            ) var exp           : Int?     = null

    )
}
