package com.app.messej.data.repository.mediators

import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChat.ChatType
import com.app.messej.data.model.entity.PrivateChat.PrivateMessageTabType
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class PrivateChatsRemoteMediator(
    private val query: String,
    private val type: ChatType,
    private val database: FlashatDatabase,
    private val networkService: ChatAPIService,
    private val messageTabType: PrivateMessageTabType?
    ) : RemoteMediator<Int, PrivateChat>() {
    val dao = database.getPrivateChatDao()

    override suspend fun initialize(): InitializeAction {
        return super.initialize()
    }

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, PrivateChat>
    ): MediatorResult {
        return try {
            val pageState: String? = when (loadType) {
                LoadType.REFRESH -> null
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    return MediatorResult.Success(
                        endOfPaginationReached = true
                    )
                }
            }

            val intruder = when(messageTabType) {
                PrivateMessageTabType.BUDDIES -> 0
                PrivateMessageTabType.INTRUDER -> 1
                null -> null
            }

            // Suspending network load via Retrofit. This doesn't need to be
            // wrapped in a withContext(Dispatcher.IO) { ... } block since
            // Retrofit's Coroutine CallAdapter dispatches on a worker
            // thread.
            val response = networkService.getPrivateChats(type = type,state = pageState, intruder)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }
            val chats = result.chats
            val adminChat = result.adminRoom
            chats.forEach {
                it.privateMessageTab = messageTabType
            }
            val deleted = result.deletedThreadList

            database.withTransaction {
                deleted.forEach {
                    dao.delete(it)
                }

                dao.insert(chats)
                adminChat?.let {
                    dao.insert(
                        adminChat.copy(
                        receiverDetails = it.receiverDetails.copy(premium = true),
                        privateMessageTab = PrivateMessageTabType.BUDDIES)
                    )
                }
            }

            MediatorResult.Success(endOfPaginationReached = result.pageState == null)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}