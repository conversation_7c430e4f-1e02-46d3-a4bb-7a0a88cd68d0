package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class AdvocatesUnionFilter {
    @SerializedName("defended") Defended,
    @SerializedName("in_jury") InJury,
    @SerializedName("no_advocates") NoAdvocates;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)?.value ?: ""
    }
}