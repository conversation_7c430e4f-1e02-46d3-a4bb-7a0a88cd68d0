package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.ChatTextColor
import com.google.gson.annotations.SerializedName

data class HuddleChatMessagePayload(
    @SerializedName("message_id") val messageId: String,
    @SerializedName("huddle_id") val huddleId: Int,
    @SerializedName("message") override val message: String?,
    @SerializedName("has_mention") val hasMention: Boolean,
    @SerializedName("color") override val color: ChatTextColor?,
    @SerializedName("forward_id") val forwardID:String?=null,
    @SerializedName("publish_to_flash") var publishToFlash:Boolean?=null
): AbstractChatMessagePayload()
