package com.app.messej.data.api

import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.CountryFlagResponse
import com.app.messej.data.model.api.auth.FCMTokenRequest
import com.app.messej.data.model.api.auth.LogoutRequest
import com.app.messej.data.model.api.auth.VerifyOTPRequest
import com.app.messej.data.model.api.profile.EditProfileRequest
import com.app.messej.data.model.api.profile.RegisterLocationRequest
import com.app.messej.data.model.api.profile.RegisterPasswordRequest
import com.app.messej.data.model.api.profile.SetProfileRequest
import com.app.messej.data.model.api.profile.SetSuperstarRequest
import com.app.messej.data.model.api.profile.SuperstarSuggestionResponse
import com.app.messej.data.model.api.settings.PodiumPrivacyResponse
import com.app.messej.data.model.api.settings.PrivacyMessage
import com.app.messej.data.model.api.settings.PrivacySettingsResponse
import com.app.messej.data.model.api.settings.PrivacyUpdateRequest
import com.app.messej.data.model.api.subscription.FlixSubscriptionDetails
import com.app.messej.data.model.api.subscription.PremiumResponse
import com.app.messej.data.model.api.subscription.Subscription
import com.app.messej.data.model.entity.SubscriptionPurchaseRequest
import com.app.messej.data.model.enums.SearchType
import com.app.messej.data.model.enums.UserType
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.Query

interface AuthAPIService {
    @POST("/user/password")
    suspend fun setPassword(@Body request: RegisterPasswordRequest): Response<APIResponse<CurrentUser>>

    @POST("/user/password/reset")
    suspend fun resetPassword(@Body request: RegisterPasswordRequest): Response<APIResponse<CurrentUser>>

    @POST("/user/profile")
    @Headers("Accept: application/json")
    suspend fun createProfile(@Body request: SetProfileRequest): Response<APIResponse<CurrentUser>>

    @POST("/user/location")
    suspend fun setLocation(@Body request: RegisterLocationRequest): Response<APIResponse<CurrentUser>>

    @GET("/user/superstars")
    @Headers("Accept: application/json")
    suspend fun getSuperstarSuggestions(
        @Query("keyword") keyword: String? = null,
        @Query("page") page: Int = 1,
        @Query("country") country: Boolean = true,
        @Query("country_filter[]") countryFilter: List<String>? = null,
        @Query("gender_filter[]") genderFilter: List<String>? = null,
        @Query("user_type") userType: UserType = UserType.FREE,
        @Query("search_type") searchType: SearchType? = SearchType.CONTAINS
    ): Response<APIResponse<SuperstarSuggestionResponse>>

    @POST("/user/superstars")
    @Headers("Accept: application/json")
    suspend fun setSuperstar(@Body request: SetSuperstarRequest): Response<APIResponse<CurrentUser>>

    @PUT("/user/profile")
    @Headers("Accept: application/json")
    suspend fun updateProfile(@Body request: EditProfileRequest): Response<APIResponse<CurrentUser>>


    @POST("/user/account")
    @Headers("Content-Type: application/json")
    suspend fun deleteUser(@Body req: VerifyOTPRequest): Response<APIResponse<Unit>>

    @POST("/user/subscriptions/purchase")
    @Headers("Accept: application/json")
    suspend fun updateSubscription(@Body request: SubscriptionPurchaseRequest): Response<APIResponse<PremiumResponse>>

    @GET("/user/subscriptions")
    @Headers("Accept: application/json")
    suspend fun getSubscriptionDetails(@Query("userId") userId: Int): Response<APIResponse<Subscription>>

    @POST("/user/device")
    @Headers("Accept: application/json")
    suspend fun registerFCMToken(@Body request: FCMTokenRequest): Response<APIResponse<Unit>>

    @POST("/user/device/delete")
    @Headers("Accept: application/json")
    suspend fun deregisterFCMToken(@Body request: FCMTokenRequest): Response<APIResponse<Unit>>

    @POST("/user/logout")
    @Headers("Accept: application/json")
    suspend fun logout(@Body request: LogoutRequest): Response<APIResponse<Boolean>>

    @GET("user/checkpoint")
    @Headers("Accept: application/json")
    suspend  fun getPrivacySettingDetails():Response<APIResponse<PrivacySettingsResponse>>

    @POST("user/privacy/seen")
    @Headers("Accept: application/json")
    suspend fun setLastSeenPrivacySettings(@Body req:PrivacyUpdateRequest): Response<APIResponse<PrivacySettingsResponse>>

    //set online status privacy
    @POST("chat/privacy/hide-status")
    @Headers("Accept: application/json")
    suspend fun setOnlineStatusPrivacySettings(@Body req:PrivacyUpdateRequest): Response<APIResponse<PrivacySettingsResponse>>

    @PUT("/user/privacy/photo")
    @Headers("Accept: application/json")
    suspend fun setProfilePicPrivacySettings(@Body req:PrivacyUpdateRequest): Response<APIResponse<PrivacySettingsResponse>>

    @PUT("/user/privacy/about")
    @Headers("Accept: application/json")
    suspend fun setAboutPrivacySettings(@Body req:PrivacyUpdateRequest): Response<APIResponse<PrivacySettingsResponse>>

    @GET("/chat/chats/privacy")
    @Headers("Accept: application/json")
    suspend  fun getPrivacyMessageSettingDetails(): Response<APIResponse<PrivacyMessage>>

    @POST("/chat/chats/privacy")
    @Headers("Accept: application/json")
    suspend  fun setMessagePrivacy(@Body privacy: PrivacyMessage): Response<APIResponse<Unit>>

    @Multipart
    @POST("/user/photo")
    suspend fun uploadProfileDp(@Part file: MultipartBody.Part?):Response<APIResponse<CurrentUser>>

    @GET("/user/privacy/displaycountryflag")
    @Headers("Accept: application/json")
    suspend  fun getCountryFlagDetails(): Response<APIResponse<CountryFlagResponse>>

    @PUT("/user/privacy/displaycountryflag")
    @Headers("Accept: application/json")
    suspend  fun setCountryFlagDetails(@Body request: CountryFlagResponse): Response<APIResponse<CountryFlagResponse>>

    @GET("/user/privacy/podium-privacy")
    @Headers("Accept: application/json")
    suspend  fun getPodiumPrivacyDetails() : Response<APIResponse<PodiumPrivacyResponse>>


    @PUT("/user/privacy/podium-privacy")
    @Headers("Accept: application/json")
    suspend  fun setPodiumPrivacy(@Body podiumPrivacy: PodiumPrivacyResponse): Response<APIResponse<Unit>>

    @PUT("/user/language")
    @Headers("Accept: application/json")
    suspend  fun setLanguage(): Response<APIResponse<Unit>>

    @GET("/user/subscriptions/purchase-with-flax")
    @Headers("Accept: application/json")
    suspend fun getFlixSubscriptionDetails(): Response<APIResponse<FlixSubscriptionDetails>>

    @POST("/user/subscriptions/purchase-with-flax")
    @Headers("Accept: application/json")
    suspend  fun setSubscriptionByFlix(@Body request: FlixSubscriptionDetails): Response<APIResponse<FlixSubscriptionDetails>>

    @POST("/user/subscriptions/toggle-for-flix-subscription-renewal")
    @Headers("Accept: application/json")
    suspend fun setSubscriptionToggle(@Body request: FlixSubscriptionDetails): Response<APIResponse<String>>

}
