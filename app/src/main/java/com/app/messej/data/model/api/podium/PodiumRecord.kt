package com.app.messej.data.model.api.podium

import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.DataFormatHelper
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

data class PodiumRecord(
    @SerializedName("duration"       ) val duration      : String,
    @SerializedName("ended"          ) val ended         : String,
    @SerializedName("id"             ) val id            : String,
    @SerializedName("likes"          ) val likes         : Int,
    @SerializedName("podium_id"      ) val podiumId      : String,
    @SerializedName("started"        ) val started       : String,
    @SerializedName("started_by"     ) val startedBy     : PodiumSpeaker,
    @SerializedName("total_speakers" ) val totalSpeakers : Int,
    @SerializedName("total_users"    ) val totalUsers    : Int
) {

    val likesFormatted: String
        get() = likes.numberToKWithFractions()

    val totalSpeakersFormatted: String
        get() = DataFormatHelper.numberToK(totalSpeakers)

    val totalUsersFormatted: String
        get() = DataFormatHelper.numberToK(totalUsers)

    private val parsedStartedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(started)

    private val parsedEndedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(ended)

    val startTimeFormatted: String
        get() = DateTimeUtils.format(parsedStartedTime, "hh:mm a")
    val endTimeFormatted: String
        get() = DateTimeUtils.format(parsedEndedTime, "hh:mm a")

    val startDateFormatted: String
        get() = DateTimeUtils.format(parsedStartedTime, "dd/MM/yyyy")


}
