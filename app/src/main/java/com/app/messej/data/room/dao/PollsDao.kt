package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.TypeConverters
import com.app.messej.data.model.api.poll.Answer
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.entity.PollParticipant
import com.app.messej.data.room.EntityDescriptions


@Dao
@TypeConverters(
    Answer.Converter::class
)
abstract class PollsDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertPolls(pollList: List<Poll>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertPoll(pollList: Poll)
    @Query("DELETE FROM ${EntityDescriptions.TABLE_POLLS_LIST} WHERE ${Poll.HUDDLE_ID} =:huddleId")
    abstract suspend fun deleteAllPolls(huddleId: Int)
    @Query("DELETE FROM ${EntityDescriptions.TABLE_POLLS_LIST} WHERE ${Poll.HUDDLE_ID} =:huddleId AND ${Poll.HUDDLE_START_DATE_ISO} > :currentDate")
    abstract suspend fun deleteSchedulePolls(huddleId: Int, currentDate: String)
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POLLS_LIST} WHERE ${Poll.HUDDLE_ID} =:huddleId AND ${Poll.HUDDLE_END_DATE_ISO} < :currentDate")
    abstract fun deletePastPolls(huddleId: Int, currentDate: String):PagingSource<Int, Poll>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POLLS_LIST} WHERE ${Poll.HUDDLE_ID} =:huddleId AND ${Poll.HUDDLE_START_DATE_ISO} > :currentDate  ORDER BY ${Poll.HUDDLE_START_DATE_ISO} DESC")
    abstract fun getScheduledPolls(huddleId: Int, currentDate: String):PagingSource<Int, Poll>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POLLS_LIST} WHERE ${Poll.HUDDLE_ID} =:huddleId AND ${Poll.HUDDLE_END_DATE_ISO} < :currentDate ORDER BY ${Poll.HUDDLE_END_DATE_ISO} DESC")
    abstract fun getPastPolls(huddleId: Int, currentDate: String):PagingSource<Int, Poll>
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POLLS_LIST} WHERE ${Poll.HUDDLE_ID} = :huddleId ")

    abstract fun getPagingPolls(huddleId:Int):PagingSource<Int, Poll>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POLLS_LIST} WHERE ${Poll.HUDDLE_ID} = :huddleId ")
    abstract fun getPolls(huddleId:Int):LiveData<Poll?>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POLLS_LIST} WHERE ${Poll.COLUMN_ID} = :pollID ")
    abstract fun getSelectedPoll(pollID:Int):LiveData<Poll?>
    @Query("DELETE FROM ${EntityDescriptions.TABLE_POLLS_PARTICIPANT} WHERE ${PollParticipant.POLL_ID} =:pollID")
    abstract fun deleteAllPollParticipant(pollID:Int)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertParticipant(pollList: List<PollParticipant>)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POLLS_PARTICIPANT} WHERE ${PollParticipant.POLL_ID} =:pollID ")
    abstract fun getParticipants(pollID:Int):PagingSource<Int, PollParticipant>

}