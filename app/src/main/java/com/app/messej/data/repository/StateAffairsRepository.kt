package com.app.messej.data.repository

import android.app.Application
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.StateAffairsAPIService
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.repository.pagingSources.StateAffairCitizenshipUserDataListDataSource
import com.app.messej.data.repository.pagingSources.StateAffairGenerousFlashatersDataListDataSource
import com.app.messej.data.repository.pagingSources.StateAffairPopularFlashatersDataListDataSource
import com.app.messej.data.repository.pagingSources.StateAffairSkillFullFlashatersDataListDataSource
import com.app.messej.data.repository.pagingSources.StateAffairTribeListDataSource
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.authorities.stateAffairs.StateAffairsTotalUsers
import com.app.messej.ui.home.publictab.authorities.stateAffairs.UserStateAffair

class StateAffairsRepository(val context: Application){

    fun getStateAffairsTribesPager(): Pager<Int, UserStateAffair> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { StateAffairTribeListDataSource(APIServiceGenerator.createService(StateAffairsAPIService::class.java))})
    }

    fun getStateAffairsCitizenshipUserPager(citizenship: UserCitizenship,countCallback: (Int) -> Unit = {}): Pager<Int, UserStateAffair> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { StateAffairCitizenshipUserDataListDataSource(APIServiceGenerator.createService(StateAffairsAPIService::class.java), citizenship = citizenship, countCallback = countCallback)})
    }
    fun getStateAffairsSkillFullFlashtersPager(): Pager<Int, UserStateAffair> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { StateAffairSkillFullFlashatersDataListDataSource(APIServiceGenerator.createService(StateAffairsAPIService::class.java))})


    }

    fun getStateAffairsPopularFlashtersPager(): Pager<Int, UserStateAffair> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { StateAffairPopularFlashatersDataListDataSource(APIServiceGenerator.createService(StateAffairsAPIService::class.java)) })

    }

    fun getStateAffairsGenerousFlashtersPager(): Pager<Int, UserStateAffair> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { StateAffairGenerousFlashatersDataListDataSource(APIServiceGenerator.createService(StateAffairsAPIService::class.java))})
    }



    suspend fun getStateStateStatistics(): ResultOf<StateAffairsTotalUsers> {
        return try {
            val resp = APIServiceGenerator.createService(StateAffairsAPIService::class.java).getStateAffairsStateStatistics()
             APIUtil.handleResponse(resp)
        }catch (e:Exception){
            ResultOf.Error(Exception(e))
        }

    }
}