package com.app.messej.data.model.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(tableName = EntityDescriptions.TABLE_BUSINESS_OPERATIONS_PAYOUT_HISTORY)
data class BusinessPayoutHistory(
    @SerializedName("forwarded_to") var forwardedTo: Int? = null,
    @SerializedName("forwarded_to_id") var forwardedToId: Int? = null,
    @PrimaryKey @SerializedName("id") var id: Int,
    @SerializedName("membership") var membership: String? = null,
    @SerializedName("name") var name: String? = null,
    @SerializedName("profile_image") var profileImage: String? = null,
    @SerializedName("requested_date") var requestedDate: String? = null,
    @SerializedName("requested_points_for_review") var requestedPointsForReview: Double? = null,
    @SerializedName("status") var status: String? = null,
    @SerializedName("status_id") var statusId: Int? = null,
    @SerializedName("time_created") var timeCreated: String? = null,
    @SerializedName("time_updated") var timeUpdated: String? = null,
    @SerializedName("user_id") var userId: Int? = null,
    @SerializedName("verified") var verified: Boolean? = null,
){
}