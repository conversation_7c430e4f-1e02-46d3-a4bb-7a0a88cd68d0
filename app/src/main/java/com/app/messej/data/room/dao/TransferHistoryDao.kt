package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class TransferHistoryDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertAll(transferHistoryList: List<DealsTransferHistory>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(transferHistory: DealsTransferHistory): Long

    @Query("DELETE FROM ${EntityDescriptions.TABLE_TRANSFER_HISTORY}")
    abstract suspend fun deleteTransferHistory()

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_TRANSFER_HISTORY} ORDER BY ${DealsTransferHistory.COLUMN_CREATED_TIME} DESC")
    abstract fun transferHistoryListPagingSource(): PagingSource<Int, DealsTransferHistory>


    @Query("SELECT * FROM ${EntityDescriptions.TABLE_TRANSFER_HISTORY} WHERE ${DealsTransferHistory.COLUMN_FILTER_STATUS}=:filterStatus ORDER BY ${DealsTransferHistory.COLUMN_CREATED_TIME} DESC"  )
    abstract fun transferHistoryListFilterPagingSource(filterStatus:String): PagingSource<Int, DealsTransferHistory>


    @Query("SELECT COUNT(*) FROM ${EntityDescriptions.TABLE_TRANSFER_HISTORY}")
    abstract fun getCount(): LiveData<Int>
}