package com.app.messej.data.model.status


import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName

data class AccountComplete(
    @SerializedName("citizenship") val citizenship: UserCitizenship? = null,
    @SerializedName("is_satisfied") val isSatisfied: Boolean? = false,
    @SerializedName("subscription_expiry_date") val expiryDate: String? = null
)