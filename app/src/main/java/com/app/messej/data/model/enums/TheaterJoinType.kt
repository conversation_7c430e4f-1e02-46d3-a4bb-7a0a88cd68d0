package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class TheaterJoinType {
    @SerializedName("add_to_main_screen") MAIN_SCREEN,
    @SerializedName("add_to_stage") STAGE,
    @SerializedName("add_to_audience") AUDIENCE;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}