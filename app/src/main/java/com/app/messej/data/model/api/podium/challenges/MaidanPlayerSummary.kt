package com.app.messej.data.model.api.podium.challenges

import com.google.gson.annotations.SerializedName

data class MaidanPlayerSummary(
    @SerializedName("player_stats") val playerStats: PlayerStats,
    @SerializedName("player_vs_competitor_stats") val competitorStats: PlayerStats?
)

data class PlayerStats(
    @SerializedName("gained") val gained: Double,
    @SerializedName("lost") val lost: Int,
    @SerializedName("played") val played: Int,
    @SerializedName("win_rate") val winRate: Double,
    @SerializedName("won") val won: Int
){
    var userId: Int? = null
}