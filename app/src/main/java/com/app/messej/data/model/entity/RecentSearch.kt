package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.model.enums.RecentSearchType
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = EntityDescriptions.TABLE_RECENT_SEARCHES
)
data class RecentSearch(
    @SerializedName("id")               @ColumnInfo(name = "id")        @PrimaryKey(autoGenerate = true) var id: Int? = null,
    @SerializedName("keyword")          @ColumnInfo(name = "keyword") var keyword: String,
    @SerializedName("time_created")     @ColumnInfo(name = "timeCreated") var timeCreated: String? = null,
    @SerializedName("screen_type")      @ColumnInfo(name = SCREEN_TYPE) var screenType: RecentSearchType? = null
) {
    companion object {
        const val COLUMN_ID = "id"
        const val COLUMN_KEYWORD = "keyword"
        const val COLUMN_TIME_CREATED = "timeCreated"
        const val SCREEN_TYPE = "screenType"
    }
}
