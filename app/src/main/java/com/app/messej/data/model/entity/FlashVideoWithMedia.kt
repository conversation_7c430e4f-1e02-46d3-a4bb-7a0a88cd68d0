package com.app.messej.data.model.entity

import androidx.room.Embedded
import androidx.room.Relation
import com.app.messej.data.model.MediaUploadState

data class FlashVideoWithMedia(
    @Embedded
    val flash: FlashVideo,
    @Relation(
        parentColumn = FlashVideo.COLUMN_ID,
        entityColumn = LocalFlashMedia.COLUMN_FLASH_ID
    )
    var media: LocalFlashMedia?
) {
    val needsMediaUpload: Boolean
        get() {
            return media?.uploadState is MediaUploadState.Pending
        }

    val mediaIsUploading: <PERSON>olean
        get() {
            return media?.uploadState is MediaUploadState.Uploading
        }
}