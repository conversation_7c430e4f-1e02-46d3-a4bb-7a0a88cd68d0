package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.PrivateChatMessageWithMedia
import com.app.messej.data.room.FlashatDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@OptIn(ExperimentalPagingApi::class)
class PrivateChatMessageRemoteMediator(
    private val roomId: String,
    private val database: FlashatDatabase,
    private val networkService: ChatAPIService
) : RemoteMediator<Int, PrivateChatMessageWithMedia>() {
    val dao = database.getChatMessageDao()

//    override suspend fun initialize(): InitializeAction {
//        return InitializeAction.SKIP_INITIAL_REFRESH
//    }

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, PrivateChatMessageWithMedia>
    ): MediatorResult {
        return try {
            var previous: String? = null
            var recent: String? = null
            when (loadType) {
                LoadType.REFRESH -> {
                    Log.d("PCMRM", "load: trying to REFRESH")
                }
                LoadType.PREPEND -> {
                    Log.d("PCMRM", "load: trying to PREPEND")
                    return MediatorResult.Success(endOfPaginationReached = true)
                }
                LoadType.APPEND -> {
                    Log.d("PCMRM", "load: trying to APPEND")
                    val lastItem = state.lastItemOrNull() ?: return MediatorResult.Success(endOfPaginationReached = true)
                    previous = lastItem.message.messageId
                }
            }

           val response = networkService.getPrivateChatMessages(roomId = roomId, previous = previous)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }


            val messages = result.messages

            withContext(Dispatchers.IO) {

                database.getPrivateChatDao().getPrivateChat(roomId)?.let { chat ->
                    messages.forEach { msg ->
                        msg.replyTo?.let { reply ->
                            if (reply.senderId == chat.receiver) {
                                reply.senderName = chat.receiverDetails.name
                            }
                        }
                    }
                }
                messages.forEach { msg ->
                    msg.sanitize()
                }

                database.withTransaction {
                    dao.insertPrivateChatMessages(messages)
                }
            }

            val isRepeating = result.messages.findLast { it.messageId == previous }!= null

            MediatorResult.Success(endOfPaginationReached = result.messages.isEmpty() || isRepeating)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}