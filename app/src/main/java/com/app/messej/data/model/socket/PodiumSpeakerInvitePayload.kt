package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.TheaterJoinType
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

data class PodiumSpeakerInvitePayload(
    @SerializedName("invited_by") val invitedBy: Int,
    @SerializedName("invitee_id") val inviteeId: Int,
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("time_invited") val invitedTime: String?,
    @SerializedName("action") val joinType: TheaterJoinType?,
    @SerializedName("invited_for_free") val invitedForFree: Boolean?,
    @SerializedName("fees") val inviteFee: Int?,
): SocketEventPayload() {
    private val parsedInvitedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(invitedTime)
}