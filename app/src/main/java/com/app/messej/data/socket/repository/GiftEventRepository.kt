package com.app.messej.data.socket.repository

import com.app.messej.MainApplication
import com.app.messej.data.model.socket.GiftHuddlePayload
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object GiftEventRepository:BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {
    private val accountRepo = AccountRepository(MainApplication.applicationInstance())
    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when (event) {
            ChatSocketEvent.RX_GIFT_RECEIVE -> {
                onNewGift(data)
            }
            else -> return false
        }
        return true
    }

    private val _giftPayLoad: MutableSharedFlow<SentGiftPayload> = MutableSharedFlow()
    val giftPayLoad: SharedFlow<SentGiftPayload> = _giftPayLoad


    private fun onNewGift(data: JSONObject) = runBlocking {
        val info = Gson().fromJson<SentGiftPayload>(data.toString())
        withContext(Dispatchers.IO) {
            val accountDetails = accountRepo.getAccountDetailsFlow().firstOrNull()
            if (accountDetails?.id == info.receiverId) {
                accountDetails?.totalGiftPoints = info.coins?.toDouble()
                accountDetails?.let { details ->
                    accountRepo.saveAccountDetails(details)
                }
            }
        }
        _giftPayLoad.emit(info.copy(
            receiverId = accountRepo.user.id,
            receiverName = accountRepo.user.name
        ))
    }
    fun sendHuddleGift(payload: GiftHuddlePayload):Boolean{
        return ChatSocketRepository.sendEvent(ChatSocketEvent.RX_HUDDLE_GIFT, payload = payload)
    }
}