package com.app.messej.data.repository

import android.app.Application
import android.util.Log
import androidx.core.app.NotificationManagerCompat
import com.app.messej.MainApplication
import com.app.messej.data.Constants
import com.app.messej.data.agora.AgoraEngineService
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.AuthAPIService
import com.app.messej.data.api.NoAuthAPIService
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.auth.FCMTokenRequest
import com.app.messej.data.model.api.auth.GoogleLoginRequest
import com.app.messej.data.model.api.auth.LoginRequest
import com.app.messej.data.model.api.auth.LoginResponse
import com.app.messej.data.model.api.auth.LogoutRequest
import com.app.messej.data.model.api.auth.ResetEmailRequest
import com.app.messej.data.model.api.auth.UnreadItemsResponse
import com.app.messej.data.model.api.auth.VerifyEmailRequest
import com.app.messej.data.model.api.auth.VerifyOTPRequest
import com.app.messej.data.model.api.auth.VerifyOTPResponse
import com.app.messej.data.model.api.auth.VerifyPhoneRequest
import com.app.messej.data.model.api.auth.VerifyRequest
import com.app.messej.data.model.api.auth.VerifyResponse
import com.app.messej.data.model.enums.OTPRequestMode
import com.app.messej.data.model.enums.ProfileCheckpoint
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.repository.worker.PodiumLiveWorker
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.socket.PodiumSocketRepository
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.AuthUtil
import com.app.messej.data.utils.DeviceInfoUtil
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.ResultOf.Companion.asException
import com.app.messej.data.utils.UserInfoUtil
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext


/**
 * Repository to handle auth operations (login/signup etc)
 */

private const val HEADER_AUTHORIZATION_PREFIX = "Bearer"

class   AuthenticationRepository(val context: Application) {

    private var accountRepo: AccountRepository = AccountRepository(context)
    private var firebaseRepo: FirebaseRepository = FirebaseRepository()
    private val db = FlashatDatabase.getInstance(context)



    val user: CurrentUser get() = accountRepo.user

    suspend fun signInWithMobileNumber(mobile: String, countryCode: String, password: String): ResultOf<LoginResponse> {
        val req = LoginRequest(
            phone = UserInfoUtil.sanitizePhoneNumber(mobile), countryCode = UserInfoUtil.addPlusToCountryCode(countryCode), password = password
        )
        return performSignIn(req)
    }

    suspend fun signInWithUsername(username: String, password: String): ResultOf<LoginResponse> {
        val req = LoginRequest(email = username, password = password)
        return performSignIn(req)
    }



    suspend fun signInWithGoogle(cred: GoogleIdTokenCredential): ResultOf<LoginResponse> {
        return try {
            val req = GoogleLoginRequest(cred.idToken)
            req.copyDeviceInfoFrom(DeviceInfoUtil.getAllDeviceInfo(firebaseRepo))

            val response = APIServiceGenerator.createService(NoAuthAPIService::class.java, false).loginGoogle(req)
            val result = APIUtil.handleResponse(response)
            processLoginResult(result)
            result
        } catch (e: Exception) {
            Log.e("TAG", "performSignIn: ", e)
            Firebase.crashlytics.recordException(e)
            ResultOf.Error(Exception(e))
        }
    }

    private suspend fun performSignIn(req: LoginRequest): ResultOf<LoginResponse> {
        return try {
            req.copyDeviceInfoFrom(DeviceInfoUtil.getAllDeviceInfo(firebaseRepo))

            val response = APIServiceGenerator.createService(NoAuthAPIService::class.java, false).login(req)
            val result = APIUtil.handleResponse(response)
            processLoginResult(result)
            result
        } catch (e: Exception) {
            Log.e("TAG", "performSignIn: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private suspend fun processLoginResult(result: ResultOf<LoginResponse>) {
        if (result is ResultOf.Success) {
            val lr = result.value
            accountRepo.setAccount(lr.user, lr.accessToken, lr.refreshToken)
            val ds = FlashatDatastore()
            ds.saveUnreadCounts(
                UnreadItemsResponse(
                    unreadPrivateChats = lr.unreadPrivateChats,
                    unreadPrivateHuddles = lr.unreadPrivateHuddles,
                    myHuddlesCount = lr.myHuddlesCount,
                    adminHuddlesCount = lr.adminHuddlesCount,
                    joinedHuddlesCount = lr.joinedHuddlesCount,
                    unreadPrivateChatRequests = lr.unreadPrivateChatRequests,
                    unreadStarMessages = lr.unreadPrivateChats,
                )
            )
            Log.d("TEST","save unread counts from Login")
            if (lr.user.profileCheckpoint==ProfileCheckpoint.PROFILE_CHECKPOINT_NONE) {
                GlobalScope.launch(Dispatchers.IO) {
                    registerFCMToken()
                }
            }
        }
    }

    suspend fun logoutUser(noApi: Boolean = false): ResultOf<Boolean> = withContext(Dispatchers.IO) {
        Firebase.crashlytics.log("logout called")
        NotificationManagerCompat.from(MainApplication.applicationContext()).cancelAll()
        return@withContext try {
            if (!accountRepo.loggedIn) {
                return@withContext ResultOf.Success(true)
            }
            if (noApi) {
                clearLoginSessionAndData(true)
                return@withContext ResultOf.Success(true)
            }
            val req = LogoutRequest(accessToken = accountRepo.tokens?.refreshToken?:"")
            req.copyDeviceInfoFrom(DeviceInfoUtil.getAllDeviceInfo(FirebaseRepository()))
            val response = APIServiceGenerator.logoutService(AuthAPIService::class.java).logout(request = req)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) clearLoginSessionAndData()
            else if(result is ResultOf.APIError && result.code==401) clearLoginSessionAndData()
            if (result is ResultOf.APIError) {
                Firebase.crashlytics.recordException(Exception("logout failed: ${result.code}: ${result.error.message}"))
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "performSignIn: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun clearLoginSessionAndData(noApi: Boolean = false): ResultOf<String> = withContext(Dispatchers.IO) {
        return@withContext try {
            if (!noApi) {
            try {
                val token = FirebaseMessaging.getInstance().token.await()
                APIServiceGenerator.createService(AuthAPIService::class.java).deregisterFCMToken(FCMTokenRequest(token))
            } catch (e: Exception) {
                Log.e(Constants.FLASHAT_TAG, "Error deregistering FCM token", e)
                Firebase.crashlytics.recordException(e)
            }
                }
            Firebase.crashlytics.log("stopping socket")
            ChatSocketRepository.stop()
            PodiumSocketRepository.stop()
            PodiumLiveWorker.endAll()
            AgoraEngineService.activeSession?.leave()
            Firebase.crashlytics.log("clearing tables")
            db.clearAllTables()
            Firebase.crashlytics.log("clearing tables again")
            db.clearTables()
            Firebase.crashlytics.log("clearing account")
            accountRepo.clearAccount()
            NotificationManagerCompat.from(MainApplication.applicationContext()).cancelAll()
            Firebase.crashlytics.log("logging out")
            ResultOf.Success("")
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "performSignIn: ", e)
            Firebase.crashlytics.recordException(e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun checkPhoneAvailability(phoneNumber: String, countryCode: String, isEdit: Boolean = false): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(NoAuthAPIService::class.java, false)
                .checkPhoneAvailability(UserInfoUtil.sanitizePhoneNumber(phoneNumber), UserInfoUtil.addPlusToCountryCode(countryCode), isEdit)
            if (response.isSuccessful && response.code() == 200) {

                val result = response.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)

            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun checkEmailAvailability(email: String?, isEdit: Boolean = false): ResultOf<String>? {
        return try {
            val response = APIServiceGenerator.createService(NoAuthAPIService::class.java, false).checkEmailAvailability(email!!, isEdit)
            if (response.isSuccessful && response.code() == 200) {
                val result = response.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)

            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getMobileOTP(requestMode: OTPRequestMode, phoneNumber: String, countryCode: String, resend: Boolean, referralCode: String? = null): ResultOf<VerifyResponse> {
        return try {
            val response = when (requestMode) {
                OTPRequestMode.REGISTER_MOBILE -> {
                    val phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(phoneNumber)
                    APIServiceGenerator.createService(NoAuthAPIService::class.java, false).verifyPhone(
                        VerifyRequest(
                            countryCode = UserInfoUtil.addPlusToCountryCode(countryCode),
                            phone = phoneNumberFormatted,
                            resend = resend,
                            referralCode = referralCode,
                            identity = AuthUtil.encryptAES("${UserInfoUtil.addPlusToCountryCode(countryCode)} $phoneNumberFormatted")
                        )
                    )
                }

                OTPRequestMode.RESET_MOBILE -> {
                    val phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(phoneNumber)
                    APIServiceGenerator.createService(NoAuthAPIService::class.java, false).resetPasswordSendOTP(
                        ResetEmailRequest(
                            countryCode = UserInfoUtil.addPlusToCountryCode(countryCode), phone = phoneNumberFormatted, resend = resend
                        )
                    )
                }

                OTPRequestMode.VERIFY_MOBILE -> {
                    val phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(phoneNumber)
                    APIServiceGenerator.createService(ProfileAPIService::class.java).verifyPhone(
                        VerifyPhoneRequest(
                            countryCode = UserInfoUtil.addPlusToCountryCode(countryCode), phone = phoneNumberFormatted, resend = resend
                        )
                    )
                }

                else -> {
                    throw Exception("Wrong method called")
                }
            }
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getEmailOTP(requestMode: OTPRequestMode, email: String, resend: Boolean, referralCode: String? = null): ResultOf<VerifyResponse> {
        return try {
            val response = when (requestMode) {

                OTPRequestMode.REGISTER_EMAIL -> {
                    APIServiceGenerator.createService(NoAuthAPIService::class.java, false).verifyEmail(
                        VerifyRequest(
                            email = email, referralCode = referralCode, resend = resend, identity = AuthUtil.encryptAES(email)
                        )
                    )
                }

                OTPRequestMode.RESET_EMAIL -> APIServiceGenerator.createService(NoAuthAPIService::class.java, false).resetPasswordSendOTP(
                    ResetEmailRequest(
                        email = email, resend = resend
                    )
                )

                OTPRequestMode.VERIFY_EMAIL -> APIServiceGenerator.createService(ProfileAPIService::class.java).verifyEmail(
                    VerifyEmailRequest(
                        email = email, resend = resend
                    )
                )

                else -> throw Exception("Wrong method called")
            }
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.APIError && result.code != 400) {
                Firebase.crashlytics.recordException(result.asException())
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun verifyMobileOTP(requestMode: OTPRequestMode, phoneNumber: String, countryCode: String, otp: String): ResultOf<VerifyOTPResponse> {
        return try {
            val phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(phoneNumber)
            val req = VerifyOTPRequest(
                countryCode = UserInfoUtil.addPlusToCountryCode(countryCode), phone = phoneNumberFormatted, otp = otp.toInt()
            )
            val response = when (requestMode) {
                OTPRequestMode.REGISTER_MOBILE -> APIServiceGenerator.createService(NoAuthAPIService::class.java, false).verifyOTP(req)
                OTPRequestMode.RESET_MOBILE -> APIServiceGenerator.createService(NoAuthAPIService::class.java, false).verifyPasswordResetOTP(req)
                OTPRequestMode.VERIFY_MOBILE -> APIServiceGenerator.createService(ProfileAPIService::class.java, true).verifyMobileOTP(req)
                else -> throw Exception("Wrong method called")
            }
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                when (requestMode) {
                    OTPRequestMode.REGISTER_MOBILE -> {
                        accountRepo.setAccount(result.value.user, result.value.accessToken, result.value.refreshToken)
                    }
                    OTPRequestMode.RESET_MOBILE -> {
                        accountRepo.setAccount(result.value.user, result.value.accessToken, result.value.refreshToken,false)
                    }
                    else -> {}
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun verifyEmailOTP(requestMode: OTPRequestMode, email: String, otp: String): ResultOf<VerifyOTPResponse> {
        return try {
            val req = VerifyOTPRequest(
                email = email, otp = otp.toInt()
            )
            val response = when (requestMode) {
                OTPRequestMode.REGISTER_EMAIL -> APIServiceGenerator.createService(NoAuthAPIService::class.java, false).verifyEmailOTP(req)
                OTPRequestMode.RESET_EMAIL -> APIServiceGenerator.createService(NoAuthAPIService::class.java, false).verifyPasswordResetOTP(VerifyOTPRequest(email = email, otp = otp.toInt()))
                OTPRequestMode.VERIFY_EMAIL -> APIServiceGenerator.createService(ProfileAPIService::class.java, true).verifyEmailOTP(req)
                else -> throw Exception("Wrong method called")
            }
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                when (requestMode) {
                    OTPRequestMode.REGISTER_EMAIL -> {
                        accountRepo.setAccount(result.value.user, result.value.accessToken, result.value.refreshToken)
                    }

                    OTPRequestMode.RESET_EMAIL -> {
                        accountRepo.setAccount(result.value.user, result.value.accessToken, result.value.refreshToken,false)
                    }

                    else -> {}
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun registerFCMToken(token: String) {
        if (!AccountRepository(context).loggedIn) return
        val response = APIServiceGenerator.createService(AuthAPIService::class.java).registerFCMToken(FCMTokenRequest(token))
        when (val result = APIUtil.handleResponseWithoutResult(response)) {
            is ResultOf.Error -> Firebase.crashlytics.recordException(result.exception)
            is ResultOf.APIError -> Firebase.crashlytics.recordException(result.asException())
            else -> {}
        }
    }

    suspend fun registerFCMToken() {
        if (!AccountRepository(context).loggedIn) return
        try {
            val token = FirebaseMessaging.getInstance().token.await()
            registerFCMToken(token)
        } catch (e: Exception) {
            Log.e("AR", "Fetching FCM registration token failed", e)
        }
    }


}