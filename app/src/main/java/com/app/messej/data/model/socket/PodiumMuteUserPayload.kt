package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class PodiumMuteUserPayload(
//    @SerializedName("time_added" ) var timeAdded : String?  = null,
    @SerializedName("podium_id"  ) var podiumId  : String,
    @SerializedName("id", alternate = ["user_id"]) var userId    : Int,
    @SerializedName("mute", alternate = ["muted"]) var muted     : <PERSON><PERSON>an,
    @SerializedName("muted_by"  ) var mutedBy  : Int? = null,
    ): SocketEventPayload()
