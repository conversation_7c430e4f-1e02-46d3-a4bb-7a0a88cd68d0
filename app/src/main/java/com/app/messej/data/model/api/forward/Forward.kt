package com.app.messej.data.model.api.forward

import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.PrivateChat
import com.google.gson.annotations.SerializedName

data class Forward(
    @SerializedName("about") val about: String? = "",
    @SerializedName("deleted_account") val deletedAccount: Boolean? = false,
    @SerializedName("id") val id: String = "",
    @SerializedName("is_premium") val isPremium: Boolean? = false,
    @SerializedName("name") val name: String? = "",
    @SerializedName("profile_url") val profileUrl: String? = "",
    @SerializedName("room_id") val roomId: String? = "",
    @SerializedName("thumbnail_url") val thumbnailUrl: String? = "",
    @SerializedName("time_updated") val timeUpdated: String? = "",
    @SerializedName("user_id") val userId: Int? = 0,
    @SerializedName("username") val username: String? = "",
    @SerializedName("verified") val verified: Boolean? = false,
    @SerializedName("is_tribe") val isTribe: Boolean? = false,
    var isSelected: Boolean? = false,
) {
    fun asPrivateChat(senderId: Int): PrivateChat? {
        return PrivateChat(
            id = id,
            receiver = userId ?: return null,
            receiverDetails = SenderDetails(id = userId, _username = username.orEmpty()),
            roomId = roomId ?: return null,
            sender = senderId
        )
    }
}


