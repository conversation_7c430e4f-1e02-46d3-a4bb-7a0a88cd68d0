package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.data.repository.PostatRepository

class PostatMediaPickerDataSource(private val postatRepo: PostatRepository): PagingSource<Int, PostatDeviceMedia>() {
    companion object {
        private const val STARTING_KEY = 0
    }

    override fun getRefreshKey(state: PagingState<Int, PostatDeviceMedia>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PostatDeviceMedia> {
        val position = params.key ?: STARTING_KEY
        return try {
            val media = postatRepo.loadMedia(position, params.loadSize)
            LoadResult.Page(
                data = media,
                prevKey = if (position == STARTING_KEY) null else position,
                nextKey = if (media.isEmpty()) null else position + params.loadSize
            )
        } catch (exception: Exception) {
            LoadResult.Error(exception)
        }
    }

}