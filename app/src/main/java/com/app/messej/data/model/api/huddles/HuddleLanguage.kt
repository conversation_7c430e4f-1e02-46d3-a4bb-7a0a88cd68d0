package com.app.messej.data.model.api.huddles


import com.google.gson.annotations.SerializedName

data class HuddleLanguage(
//    @SerializedName("id")
//    val id: Int? = 0,
    @SerializedName("language")
    val language: String? = "",
    @SerializedName("name")
    val name: String? = "",
    @SerializedName("time_created")
    val timeCreated: String? = "",
    @SerializedName("time_updated")
    val timeUpdated: String? = "",
    @SerializedName("translated_text")
    val englishName: String? = ""
)