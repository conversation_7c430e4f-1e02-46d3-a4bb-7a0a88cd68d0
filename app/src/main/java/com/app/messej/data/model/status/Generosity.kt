package com.app.messej.data.model.status


import com.google.gson.annotations.SerializedName

data class Generosity(
    @SerializedName("total_given_coins") val totalGivenCoins: Double? = 0.0,
    @SerializedName("is_satisfied") val isSatisfied: Boolean? = false,
    @SerializedName("total_gained_coins") val totalGainedCoins: Double? = 0.0,
    @SerializedName("given_coins_percentage") val givenCoinPercentage: Double? = 0.0,
    @SerializedName("total_coin_obligation") val totalCoinObligation: Double? = 0.0
)