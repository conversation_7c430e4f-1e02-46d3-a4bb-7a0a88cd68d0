package com.app.messej.data.model.socket

import com.app.messej.data.model.UserIdAndName
import com.google.gson.annotations.SerializedName

data class MaidanSupportRequestPayload(
    @SerializedName("challenge_id"      ) val challengeId           : String,
    @SerializedName("podium_id"         ) val podiumId              : String,
    @SerializedName("invited_by"        ) val invitedBy             : UserIdAndName,
    ) : SocketEventPayload()

