package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.HuddlePrivacy
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

@Entity(tableName = EntityDescriptions.TABLE_HUDDLES)
@TypeConverters(
    SenderDetails.Converter::class,
    HuddleChatMessage.Converter::class,
    HuddlePrivacy.Converter::class
)
/**
 * Represents a public huddle
 * @property activity zoned datetime of last activity. example: 2023-01-04T08:16:02Z
 */
data class  PublicHuddle(
    @SerializedName("id"                        ) @ColumnInfo(name = COLUMN_ID                  ) @PrimaryKey(autoGenerate = false) override val id: Int,
    @SerializedName("name",alternate= ["huddle_name"]) @ColumnInfo(name = "name"                ) override val name                  : String,
    @SerializedName("about"                     ) @ColumnInfo(name = "about"                    ) override val about                 : String? = "",
    @SerializedName("thumbnail"                 ) @ColumnInfo(name = "thumbnail"                ) override val thumbnail             : String?,
    @SerializedName("group_photo"               ) @ColumnInfo(name = "group_photo"              ) override val groupPhoto            : String?,
    @SerializedName("manager_premium_status"    ) @ColumnInfo(name = "manager_premium_status"   ) override var managerPremium        : Boolean?,

    @SerializedName("category"                  ) @ColumnInfo(name = "category"                 ) override val category              : String?,
    @SerializedName("created_by"                ) @ColumnInfo(name = "created_by"               )          val createdBy             : String?,
    @SerializedName("private"                   ) @ColumnInfo(name = "private"                  ) override val isPrivate               : Boolean,

    @SerializedName("status"                    ) @ColumnInfo(name = "status"                   ) override val status                : HuddleStatus,
    @SerializedName("admin_status"              ) @ColumnInfo(name = "admin_status"             )          val adminStatus           : HuddleAdminStatus?,
    @SerializedName("user_status"               ) @ColumnInfo(name = "user_status"              ) override var userStatus            : HuddleUserStatus,

    @SerializedName("total_members"             ) @ColumnInfo(name = "total_members"            ) override val totalMembers          : Int,

    //dates
    @SerializedName("activity"                  ) @ColumnInfo(name = COLUMN_ACTIVITY            )          var activity              : String?,
    @SerializedName("time_created"              ) @ColumnInfo(name = "time_created"             )          val timeCreated           : String?,
    @SerializedName("time_updated"              ) @ColumnInfo(name = "time_updated"             )          val timeUpdated           : String?,

    @SerializedName("online_participants"       ) @ColumnInfo(name = COLUMN_ONLINE              )          val onlineParticipants    : Int,
    @SerializedName("unread_count"              ) @ColumnInfo(name = COLUMN_UNREAD_COUNT        )          var unreadCount           : Int,
    @SerializedName("tribe"                     ) @ColumnInfo(name = COLUMN_TRIBE, defaultValue = "0" )    val isTribe:Boolean=false,
    @SerializedName("updated_by"                ) @ColumnInfo(name = "updated_by"               )          val updatedBy             : Int,

    @SerializedName("participant_share"         ) @ColumnInfo(name = "participant_share"        )          val participantShare      : Boolean,

    @SerializedName("invite_link"               ) @ColumnInfo(name = "invite_link"              )          val inviteLink            : String?,
    @SerializedName("manager_id"                ) @ColumnInfo(name = "manager_id"               )          val managerId             : Int,

    @SerializedName("request_to_join"           ) @ColumnInfo(name = "request_to_join"          ) override val requestToJoin         : Boolean,
    @SerializedName("requested_invited_count"   ) @ColumnInfo(name = "requested_invited_count"  )          val requestsAndInvites : Int,

    @SerializedName("muted"                     ) @ColumnInfo(name = COLUMN_MUTED               )          val muted                 : Boolean,
    @SerializedName("pinned"                    ) @ColumnInfo(name = COLUMN_PINNED              )          val pinned                : Boolean,
    @SerializedName("role"                      ) @ColumnInfo(name = "role"                     )          var role                  : UserRole?,

    @SerializedName("sender_details"            ) @ColumnInfo(name = "sender_details"           )          var senderDetails         : SenderDetails?,

    @SerializedName("last_message"              ) @ColumnInfo(name = COLUMN_LAST_MESSAGE        )          var lastMessageInternal           : HuddleChatMessage?,
    @SerializedName("last_read_message"         ) @ColumnInfo(name = COLUMN_LAST_READ           )          val lastReadMessage       : String?,
    @SerializedName("privacy"                   ) @ColumnInfo(name = "privacy"                  )          var privacy               : HuddlePrivacy?,

    @SerializedName("empowered_user_blocked"    ) @ColumnInfo(name = "empowered_user_blocked", defaultValue = "0") var empoweredUserBlocked  : Boolean = false,

    @ColumnInfo(name = COLUMN_HUDDLE_TYPE) var huddleType: HuddleType = HuddleType.PUBLIC,

    @ColumnInfo(name = COLUMN_HUDDLE_INVOLVEMENT, defaultValue = "NONE") var involvement: HuddleInvolvement? = HuddleInvolvement.NONE

): AbstractHuddle() {

    companion object {
        const val COLUMN_ID = "id"
        const val COLUMN_ACTIVITY = "activity"
        const val COLUMN_PINNED = "pinned"
        const val COLUMN_MUTED = "muted"
        const val COLUMN_NAME = "name"
        const val COLUMN_HUDDLE_TYPE = "huddle_type"
        const val COLUMN_LAST_MESSAGE = "last_message"
        const val COLUMN_LAST_READ = "last_read_message"
        const val COLUMN_UNREAD_COUNT = "unread_count"
        const val COLUMN_ONLINE = "online_participants"
        const val COLUMN_HUDDLE_INVOLVEMENT = "huddle_show_type"
        const val COLUMN_TRIBE="tribe"
    }

    init {
        lastMessageInternal?.let {
            if (it.senderDetails==null) {
                it.senderDetails = senderDetails
            }
        }
    }

    fun detectInvolvement() {
        involvement = when (role) {
            UserRole.MANAGER -> HuddleInvolvement.MANAGER
            UserRole.ADMIN -> HuddleInvolvement.ADMIN
            UserRole.USER -> HuddleInvolvement.PARTICIPANT
            else -> HuddleInvolvement.NONE
        }
    }

    var parsedActivity: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(activity)
        set(value) {
            value?:return
            activity = DateTimeUtils.formatISOInstant(value)
        }

    var lastMessage: HuddleChatMessage?
        get() = lastMessageInternal
        set(value) {
            lastMessageInternal = value
            value?:return
            senderDetails = value.senderDetails
            parsedActivity = if (!value.isActivity) value.parsedCreatedTime else parsedActivity
        }
    val isTribeEdit:Boolean
        get()=  timeCreated?.isNotEmpty() == true && timeUpdated?.isNotEmpty() == true && LocalDateTime.parse(timeCreated, DateTimeFormatter.ISO_LOCAL_DATE_TIME).compareTo(LocalDateTime.parse(timeUpdated, DateTimeFormatter.ISO_LOCAL_DATE_TIME)) == 0

    val hasLastMessage: Boolean
        get() = lastMessageInternal!=null

    val hasUnread: Boolean
        get() = unreadCount>0
}
