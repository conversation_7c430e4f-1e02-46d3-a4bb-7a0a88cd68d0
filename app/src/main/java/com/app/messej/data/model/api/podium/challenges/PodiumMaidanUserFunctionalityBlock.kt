package com.app.messej.data.model.api.podium.challenges

import com.google.gson.annotations.SerializedName

data class PodiumMaidanUserFunctionalityBlock(

	@SerializedName("flash_block")								val flashBlock: Boolean? = null,
	@SerializedName("podium_block")								val podiumBlock: Any? = null,
	@SerializedName("podium_speaking_block")					val podiumSpeakingBlock: Any? = null,
	@SerializedName("podium_write_comments_block")				val podiumWriteCommentsBlock: Any? = null,
	@SerializedName("user_id")									val userId: Int? = null,
	@SerializedName("time_created")								val timeCreated: String? = null,
	@SerializedName("huddle_posts_block")						val huddlePostsBlock: Any? = null,
	@SerializedName("id")										val id: Int? = null,
	@SerializedName("postat_posts_block")						val postatPostsBlock: Any? = null,
	@SerializedName("time_updated")								val timeUpdated: String? = null
)
