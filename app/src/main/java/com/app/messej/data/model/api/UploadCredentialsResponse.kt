package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName

data class UploadCredentialsResponse(
    @SerializedName("access_key" ) var accessKey : String,
    @SerializedName("secret_key" ) var secretKey : String,
    @SerializedName("session_token" ) var sessionToken : String,

    /**
     * Only passed at the moment for reports API credentials. All others user locally hardcoded paths.
     */
    @SerializedName("folder_path" ) var folderPath : String?,
)