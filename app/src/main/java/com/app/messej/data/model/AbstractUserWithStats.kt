package com.app.messej.data.model

import com.app.messej.ui.utils.DataFormatHelper

abstract class AbstractUserWithStats: AbstractUser() {

    abstract val dears        : Int
    abstract val fans         : Int
    abstract val likers       : Int
    abstract val stars        : Int

    val starsFormatted: String
        get() = DataFormatHelper.numberToK(stars)

    val dearsFormatted: String
        get() = DataFormatHelper.numberToK(dears)

    val fansFormatted: String
        get() = DataFormatHelper.numberToK(fans)

    val likersFormatted: String
        get() = DataFormatHelper.numberToK(likers)
}
