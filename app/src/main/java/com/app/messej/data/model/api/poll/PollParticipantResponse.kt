package com.app.messej.data.model.api.poll


import com.app.messej.data.model.entity.PollParticipant
import com.google.gson.annotations.SerializedName

data class PollParticipantResponse(
    @SerializedName("current_page") val currentPage: String? = "",
    @SerializedName("next_page") val nextPage: Boolean? = false,
    @SerializedName("participants") val pollParticipants: List<PollParticipant> = listOf(),
    @SerializedName("total") val total: Int? = 0,
)