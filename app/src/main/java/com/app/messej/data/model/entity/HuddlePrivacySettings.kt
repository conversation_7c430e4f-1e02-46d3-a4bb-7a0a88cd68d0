package com.app.messej.data.model.entity

import com.google.gson.annotations.SerializedName

data class HuddlePrivacySettings(
    @SerializedName("comment_settings") val commentSettings: PrivacySetting? = PrivacySetting(),
    @SerializedName("huddle_id") val huddleId: Int? = 0,
    @SerializedName("post_settings") val postSettings: PrivacySetting? = PrivacySetting(),
    @SerializedName("reply_settings") val replySettings: PrivacySetting? = PrivacySetting(),
    @SerializedName("time_created") val timeCreated: String? = null,
    @SerializedName("time_updated") val timeUpdated: String? =null,
)
data class PrivacySetting(
    @SerializedName("privacy") val privacy: HuddlePrivacyStatus? = null
)

enum class HuddlePrivacyStatus {
    @SerializedName("Anyone") ANYONE,
    @SerializedName("Dears") DEARS,
    @SerializedName("Citizens") CITIZENS,
    @SerializedName("Noone") NO_ONE
}




