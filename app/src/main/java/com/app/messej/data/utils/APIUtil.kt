package com.app.messej.data.utils

import com.app.messej.BuildConfig
import com.app.messej.data.Constants
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.ErrorResponse
import retrofit2.Response
import java.net.HttpURLConnection

object APIUtil {

    fun <T> handleResponse(response: Response<APIResponse<T>>): ResultOf<T> {
        return if (response.isSuccessful && response.code() == 200) {
            val result = response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            ResultOf.Success(result)

        } else {
            val error: ErrorResponse =
                ErrorResponse.parseError(response = response.errorBody()!!)
            ResultOf.APIError(error, code = response.code())
        }
    }

    fun <T> handleResponseWithMessage(response: Response<APIResponse<T>>): ResultOf<APIResponse<T>> {
        return if (response.isSuccessful && response.code() == 200) {
            val result = response.body() ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            ResultOf.Success(result)

        } else {
            val error: ErrorResponse =
                ErrorResponse.parseError(response = response.errorBody()!!)
//            Firebase.crashlytics.log("Error when calling API: ${response.raw().request.url}")
//            Firebase.crashlytics.recordException(Exception(error.message))
            ResultOf.APIError(error, code = response.code())
        }
    }

    fun <T> handleResponseWithoutResult(response: Response<APIResponse<T>>): ResultOf<String> {
        return if (response.isSuccessful && response.code() == 200) {
            val result = response.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            ResultOf.Success(result)

        } else {
            val error: ErrorResponse =
                ErrorResponse.parseError(response = response.errorBody()!!)
            ResultOf.APIError(error, code = response.code())
        }
    }

    fun handleDeleteResponse(response: Response<APIResponse<Unit>>): ResultOf<Unit> {
        return if (response.isSuccessful && response.code() == 204) {
            ResultOf.Success(Unit)
        } else {
            val error =
                ErrorResponse.parseError(response.errorBody()!!)
            ResultOf.APIError(error, code = response.code())
        }
    }

    /*
    * These keys need to be hardcoded as they need to be created even when offline
    * */

    fun getHuddleChatMediaUploadKey(huddleId: Int, fileName: String): String {
        return "${BuildConfig.MEDIA_UPLOAD_ENVIRONMENT}/HUDDLE-$huddleId/$fileName"
    }

    fun getPrivateChatMediaUploadKey(roomId: String, fileName: String): String {
        return "${BuildConfig.MEDIA_UPLOAD_ENVIRONMENT}/${roomId.replace('#','-')}/$fileName"
    }

    fun getBroadcastMediaUploadKey(userId: Int, fileName: String): String {
        return "${BuildConfig.MEDIA_UPLOAD_ENVIRONMENT}/broadcast/${userId}/$fileName"
    }

    fun getFlashMediaUploadKey(fileName: String, userId: Int): String {
        return "${BuildConfig.MEDIA_UPLOAD_ENVIRONMENT}/${userId}/$fileName"
    }

    fun getPostatMediaUploadKey(fileName: String, userId: Int): String {
        return "${BuildConfig.MEDIA_UPLOAD_ENVIRONMENT}/postat/${userId}/$fileName"
    }

    fun getReportProofUploadKey(fileName: String): String {
        return "${BuildConfig.MEDIA_UPLOAD_ENVIRONMENT}/report_proof_files/tmp/$fileName"
    }

    fun getSocialProofUploadKey(fileName: String): String {
        return "${BuildConfig.MEDIA_UPLOAD_ENVIRONMENT}/social_proof_files/tmp/$fileName"
    }

    fun canShowAPIErrorMessage(code: Int) =
        code == HttpURLConnection.HTTP_BAD_REQUEST
                || code == HttpURLConnection.HTTP_FORBIDDEN
                || code == HttpURLConnection.HTTP_NOT_FOUND
                || code == HttpURLConnection.HTTP_INTERNAL_ERROR
}