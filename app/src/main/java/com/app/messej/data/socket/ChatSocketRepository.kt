package com.app.messej.data.socket

import android.util.Log
import com.app.messej.data.socket.repository.BroadcastEventRepository
import com.app.messej.data.socket.repository.ChallengeEventRepository
import com.app.messej.data.socket.repository.FlaxEventRepository
import com.app.messej.data.socket.repository.GiftEventRepository
import com.app.messej.data.socket.repository.HuddleChatEventRepository
import com.app.messej.data.socket.repository.NotificationEventRepository
import com.app.messej.data.socket.repository.PresidentEventRepository
import com.app.messej.data.socket.repository.PrivateChatEventRepository
import com.app.messej.data.socket.repository.ProfileEventRepository
import com.app.messej.data.socket.repository.PromoBoardEventRepository
import com.app.messej.data.socket.repository.StarsEventRepository
import org.json.JSONObject

object ChatSocketRepository: AbstractSocketRepository<ChatSocketEvent>(ChatSocketService()) {

    private const val KEY_EVENT_NAME = "eventName"
    private const val KEY_DATA = "data"

    override fun onEvent(eventName: String, event: JSONObject) {
        val eventType = ChatSocketEvent.from(event.getString(KEY_EVENT_NAME))?:return
        Log.d(logTag, "onEvent: $eventType")
        val data = event.getJSONObject(KEY_DATA)
        handleMessage(eventType,data)
    }

    override fun addListeners() {
        registerListener(HuddleChatEventRepository)
        registerListener(PrivateChatEventRepository)
        registerListener(BroadcastEventRepository)
        registerListener(ProfileEventRepository)
        registerListener(NotificationEventRepository)
        registerListener(StarsEventRepository)
        registerListener(GiftEventRepository)
        registerListener(FlaxEventRepository)
        registerListener(ChallengeEventRepository)
        registerListener(PresidentEventRepository)
        registerListener(PromoBoardEventRepository)
    }
}