package com.app.messej.data.model.api.business


import com.google.gson.annotations.SerializedName

data class BusinessTargets(
    @SerializedName("app_shares_target") var appSharesTarget: Int? = 0,
    @SerializedName("broadcast_target") var broadcastTarget: Int? = 0,
    @SerializedName("customers_target") var customersTarget: Int? = 0,
    @SerializedName("followers_target") var followersTarget: Int? = 0,
    @SerializedName("huddles_target") var huddlesTarget: Int? = 0,
    @SerializedName("likes_target") var likesTarget: Int? = 0,
    @SerializedName("participants_target") var participantsTarget: Int? = 0,
    var fansTarget: Int? = 0,

    )