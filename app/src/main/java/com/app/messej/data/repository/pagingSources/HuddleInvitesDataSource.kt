package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.HuddleInvitationsResponse
import com.app.messej.data.room.dao.UserDao
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1

class HuddleInvitesDataSource(private val api: ChatAPIService,
                              private val dao: UserDao, private val huddleId: Int, private val totalCountCallback:(Int) -> Unit): PagingSource<Int, HuddleInvitationsResponse.HuddleInvitation>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, HuddleInvitationsResponse.HuddleInvitation> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getHuddleInvites(
                    huddleId, page = currentPage
                )
                val result = response.body()?.result
                val data = result?.invitations ?: emptyList()

                val nicknames = if (data.isNotEmpty()) dao.getNickNames(data.map { it.memberId }) else emptyList()

                data.forEach { member ->
                    val nn = nicknames.find { it.userId == member.memberId }
                    nn?.nickName?.let {
                        member.memberName = it
                    }
                }

                totalCountCallback.invoke(result?.total?: 0)
                val nextKey = if (result?.nextPage == null) null else currentPage.plus(1)
                LoadResult.Page(
                    data = data,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, HuddleInvitationsResponse.HuddleInvitation>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}