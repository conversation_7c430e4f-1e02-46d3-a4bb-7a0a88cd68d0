package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class ChallengeContributorTimeOutPayload(
    @SerializedName("challenge_id"      ) val challengeId           : String,
    @SerializedName("podium_id"         ) val podiumId              : String,
    @SerializedName("contributorIds"         ) val contributors              : List<Int>,
    ) : SocketEventPayload()
