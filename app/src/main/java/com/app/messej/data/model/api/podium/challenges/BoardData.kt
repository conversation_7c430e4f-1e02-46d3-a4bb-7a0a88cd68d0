package com.app.messej.data.model.api.podium.challenges

import com.app.messej.data.utils.DateTimeUtils
import java.time.ZonedDateTime

abstract class BoardData {
    abstract var currentPlayerUserId              : Int?
    abstract val turnStartTime              : Double?

    protected abstract val turnDurationSeconds: Long

    val parsedTurnStartTime: ZonedDateTime?
        get() = turnStartTime?.let { DateTimeUtils.parseMillisToDateTime((it*1000).toLong()) }

    val turnEndTime: ZonedDateTime?
        get() = parsedTurnStartTime?.plusSeconds(turnDurationSeconds)
}