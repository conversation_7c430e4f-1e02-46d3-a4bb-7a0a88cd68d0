package com.app.messej.data.repository.worker

import android.app.Application
import android.app.Notification
import android.app.NotificationManager
import android.content.Context
import android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_CAMERA
import android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
import android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.work.CoroutineWorker
import androidx.work.ForegroundInfo
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.app.messej.MainApplication
import com.app.messej.R
import com.app.messej.data.agora.AgoraEngineService
import com.app.messej.data.agora.PodiumEventService
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.entity.Podium.PodiumUserRole
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.socket.PodiumSocketRepository
import com.app.messej.data.socket.repository.PodiumChallengeEventRepository
import com.app.messej.data.socket.repository.PodiumEventRepository
import com.app.messej.service.FlashatFirebaseMessagingService
import com.app.messej.ui.utils.AsyncExtensions.collectAsync
import com.app.messej.ui.utils.DeepLinkUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resumeWithException

class PodiumLiveWorker(appContext: Context, workerParams: WorkerParameters):
    CoroutineWorker(appContext, workerParams) {

    private val notificationManager = appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    companion object {
        private const val WORK_TAG = "podium_live_worker"

        const val NOTIFICATION_ID = 1556

        private const val INPUT_ID = "input_podium_id"
        private const val INPUT_NAME = "input_podium_name"
        private const val INPUT_ROLE = "input_podium_role"
        private const val INPUT_INVITED = "input_podium_invited"

        private const val timeOutSeconds = 15

        suspend fun endAllAndStart(id: String, name: String, role: PodiumUserRole, invited: Boolean) = withContext(Dispatchers.IO) {
            endAll()
            val workManager = WorkManager.getInstance(MainApplication.applicationContext())
            Log.d("PLW", "nope. Starting PodiumLiveWorker")
            val workRequest = OneTimeWorkRequestBuilder<PodiumLiveWorker>().setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST).addTag(WORK_TAG)
                .setInputData(workDataOf(
                    INPUT_ID to id,
                    INPUT_NAME to name,
                    INPUT_ROLE to role.ordinal,
                    INPUT_INVITED to invited
                )).build()
            workManager.enqueue(workRequest)
        }

        suspend fun endAll() = withContext(Dispatchers.IO) {
            val workManager = WorkManager.getInstance(MainApplication.applicationContext())
            workManager.cancelAllWorkByTag(WORK_TAG)
            val notificationManager = MainApplication.applicationContext().getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(NOTIFICATION_ID)
        }
    }

    private var podiumName: String = ""
    private var podiumId: String = ""
    private var podiumRole: PodiumUserRole = PodiumUserRole.AUDIENCE
    private var isInvited: Boolean = false

    private var session: AgoraEngineService.AgoraSession? = null

    override suspend fun doWork(): Result = coroutineScope {
        return@coroutineScope try {
            podiumId = inputData.getString(INPUT_ID) ?: return@coroutineScope Result.failure()
            podiumName = inputData.getString(INPUT_NAME) ?: return@coroutineScope Result.failure()
            podiumRole = PodiumUserRole.entries[inputData.getInt(INPUT_ROLE, PodiumUserRole.AUDIENCE.ordinal)]
            session = AgoraEngineService.activeSession
            Log.d("PLW", "Starting Live Worker")

            val jobs = arrayListOf(
                launch(Dispatchers.Default) {
                    keepNotificationAlive()
                }, launch(Dispatchers.IO) {
                    keepSocketAlive()
                }, launch(Dispatchers.IO) {
                    startConnectionChecker()
                }, launch(Dispatchers.IO) {
                    startAttendanceTimer()
                }
            )
            Log.d("PLW", "Starting Live Work")
            startLive()
            // Indicate whether the work finished successfully with the Result
            Log.d("PLW", "Live Ended")
            jobs.forEach {
                it.cancel()
            }
            Result.success()
        } catch (throwable: Throwable) {
            // clean up and log
            Log.d("PLW", "Live job cancelled")
//            session?.leave()
            Result.failure()
        }
        finally {
            Log.d("PLW", "Cancelling Notification")
            val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(NOTIFICATION_ID)
        }
    }

    private suspend fun keepNotificationAlive() {
        try {
            while (true) {
                notificationManager.notify(NOTIFICATION_ID,getNotification())
                Log.d("PLW", "Setting foreground")
                delay(5000)
            }
        } catch (e: Exception) {
            Log.e("PLW", "Foreground coroutine cancelled")
            Log.e("PLW",e.message.orEmpty())
        }
    }

    private val podiumEventRepo = PodiumEventRepository
    private val challengeEventRepo = PodiumChallengeEventRepository
    private val accountRepo = AccountRepository(applicationContext)
    private val podiumRepository = PodiumRepository(applicationContext as Application)

    private suspend fun keepSocketAlive() = coroutineScope {
        Log.w("PLW", "Launch : connectionStateFlow")
        var wasConnected = true
        collectAsync(PodiumSocketRepository.connectionStateFlow) { connected ->
            Log.d("PLW", "socket is connected: $connected")
            if (connected && !wasConnected) {
                podiumRepository.joinPodium(podiumId)
            }
            wasConnected = connected
        }

        Log.w("PLW", "Launch : closeFlow")
        collectAsync(podiumEventRepo.closeFlow.filter { it.first == podiumId }) { pl ->
            try {
                Log.w("PLW", "Event : closeFlow")
                session?.leave()
                endAll()
            } finally {
            }
        }

        Log.w("PLW", "Launch : blockedFlow")
        collectAsync(podiumEventRepo.blockedFlow.filter { it.podiumId == podiumId }) { pl ->
            if (pl.userId == accountRepo.user.id) {
                podiumEventRepo.leavePodium(pl.podiumId, accountRepo.user.id)
                session?.leave()
            }
        }

        Log.w("PLW", "Launch : syncFlow")
        collectAsync(podiumEventRepo.syncFlow.filter { podiumId == it.podiumId }) {
            PodiumEventService.onSyncEvent()
            podiumRole = if (podiumRole == PodiumUserRole.MANAGER) PodiumUserRole.MANAGER
            else if (it.adminList.contains(accountRepo.user.id)) PodiumUserRole.ADMIN
            else if (isInvited) PodiumUserRole.INVITEE
            else PodiumUserRole.AUDIENCE
        }

        collectAsync(podiumEventRepo.newAdminFLow.filter { it.podiumId == podiumId }) { pl ->
            if (pl.userId == accountRepo.user.id) {
                Log.w("PLW", "Event : newAdminFLow")
                podiumRole = PodiumUserRole.ADMIN
            }
        }

        collectAsync(podiumEventRepo.dismissAdminFLow.filter { it.podiumId == podiumId }) { pl ->
            if (pl.userId == accountRepo.user.id) {
                Log.w("PLW", "Event : dismissAdminFLow")
                podiumRole = if (isInvited) PodiumUserRole.INVITEE else PodiumUserRole.AUDIENCE
            }
        }

        collectAsync(challengeEventRepo.challengeSyncFlow.filter { it.podiumId == podiumId }) { challenge ->
            if (challenge.challengeType == ChallengeType.FLAGS && challenge.status == PodiumChallenge.ChallengeStatus.LIVE) {
                session?.muteAudio(true)
            }
        }

//        collectAsync(podiumEventRepo.syncFlow.filter { it.podiumId == podiumId }) { pod ->
//            pod.speakerList.find { it.id == accountRepo.user.id }?.let { sp ->
//                if (sp.muted) {
//                    session?.muteAudio(true)
//                }
//            }
//        }
    }

    private suspend fun startConnectionChecker() = coroutineScope {
        PodiumEventService.onSyncEvent()

        Log.w("PLW", "Launch : startConnectionChecker")
        launch(Dispatchers.Default) {
            try {
                while (true) {
                    delay(timeOutSeconds*1000L)
                    val durationToNow = PodiumEventService.timeSinceLastSync
                    Log.w("PLW", "startConnectionChecker : time since last sync: ${durationToNow?.seconds}")
                    if (durationToNow==null || durationToNow.seconds>timeOutSeconds) {
                        val result = podiumRepository.joinPodium(podiumId)
                        if (result is PodiumRepository.JoinResultOf.APIError) {
                            Log.w("PLW", "startConnectionChecker : ending worker as join failed")
                            PodiumEventService.onJoinError(result)
//                            endAll()
                        }
                    }

//                    Log.w("startConnectionChecker", "agora is connected? ${agoraSession?.connected()} | last sync was ${durationToNow?.seconds}s ago")
//                    if (agoraSession?.connected()==false) {
//                        agoraSession?.leave()
//                        agoraSession = null
//                        joinChannel()
//                    }
                }
            } catch (e: Exception) {
                Log.e("startConnectionChecker", "startConnectionChecker: failed", e)
            } finally {
                Log.w("startConnectionChecker", "startConnectionChecker cancelled")
            }
        }
    }

//    private var agoraSession: AgoraEngineService.AgoraSession? = null

//    private var attendanceTimerJob: Job? = null
//    private fun stopAttendanceTimer() {
//        attendanceTimerJob?.apply {
//            cancel()
//            attendanceTimerJob = null
//        }
//    }

    private suspend fun startAttendanceTimer() = coroutineScope {
//        stopAttendanceTimer()
//        attendanceTimerJob = launch {
        Log.w("PLW", "Launch : startAttendanceTimer")
        launch {
            try {
                while (true) {
                    delay(25000)
                    Log.w("PLW", "startAttendanceTimer : sending attendance $podiumRole")
                    podiumEventRepo.sendUserAttendance(podiumId, accountRepo.user.id, podiumRole)
                }
            } catch (e: Exception) {
//                stopAttendanceTimer()
                Log.e("PLW", "startAttendanceTimer: failed", e)
            } finally {
                Log.w("PLW", "attendance timer cancelled")
            }
        }
    }

    private suspend fun startLive() {
        suspendCancellableCoroutine { cont ->
            session?.also { session ->
//                agoraSession = session
                session.registerListener(object : AgoraEngineService.ChannelEventListener {
                    override fun onJoinedChannel(elapsed: Int) {
                        Log.d("PLW", "onJoinedChannel")
                    }

                    override fun onLeave() {
                        Log.d("PLW", "onLeave")
                        cont.resumeWith(kotlin.Result.success(Unit))
                    }

                })
            } ?: run {
                cont.resumeWithException(Exception("No active session found"))
            }
            cont.invokeOnCancellation {
                Log.w("PLW", "Cancelling Session")
            }
        }
    }

    override suspend fun getForegroundInfo() = createForegroundInfo()

    private fun createForegroundInfo(): ForegroundInfo {
//        Log.d("PLW", "createForegroundInfo")

        val notification = getNotification()

        return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            ForegroundInfo(NOTIFICATION_ID, notification)
        } else if(Build.VERSION.SDK_INT < Build.VERSION_CODES.R) ForegroundInfo(NOTIFICATION_ID, notification, FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK)
        else ForegroundInfo(NOTIFICATION_ID, notification, FOREGROUND_SERVICE_TYPE_MICROPHONE or FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK or FOREGROUND_SERVICE_TYPE_CAMERA)
    }

    private fun getNotification(): Notification {
        // Create a Notification channel if necessary
        val channel = FlashatFirebaseMessagingService.createChannel(notificationManager,"POD_LIVE","Ongoing Podiums",NotificationManager.IMPORTANCE_HIGH)

        val title = podiumName
        val message = applicationContext.getString(R.string.podium_notification_message)
        val deepLink = DeepLinkUtil.toPodiumLive(applicationContext, podiumId)

        return NotificationCompat.Builder(applicationContext, channel.id)
            .setContentTitle(title)
            .setContentText(message)
            .setContentIntent(deepLink)
            .setSilent(true)
            .setSmallIcon(R.drawable.ic_notification_small)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
}