package com.app.messej.data.model

import android.util.Log
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.enums.AudioSpeed
import com.app.messej.data.utils.DateTimeUtils

data class MediaPlayerInfo (
    val messageId: String?,
    val mediaPath: String,
    val meta: MediaMeta,
    var listPosition: Int
): BaseObservable() {

    val duration: Long = meta.seconds

    @get:Bindable
    var state: MediaState = MediaState.NONE
        set(value) {
            field = value
            notifyPropertyChanged(BR.state)
            progress = if(value==MediaState.NONE) 0F else progress
            active = (value != MediaState.NONE)
        }

    @get:Bindable
    var active: Boolean = false
        private set(value) {
            field = value
            notifyPropertyChanged(BR.active)
        }

    @get:Bindable
    var progress: Float = 0F
        set(value) {
            if(field==value) return
            Log.d("TIMER", "timer: $value")
            field = value.coerceAtMost(duration.toFloat())
            notifyPropertyChanged(BR.progress)
            progressTimer = if (state == MediaState.NONE) DateTimeUtils.formatSeconds(duration) else DateTimeUtils.formatSeconds(value)
        }

    @get:Bindable
    var progressTimer: String = DateTimeUtils.formatSeconds(0)
        private set(value) {
            field = value
            notifyPropertyChanged(BR.progressTimer)
        }

    @get:Bindable
    var audioSpeed: AudioSpeed = AudioSpeed.SPEED_1X
        set(value) {
            field = value
            notifyPropertyChanged(BR.audioSpeed)
        }

    enum class MediaState {
        NONE, PLAYING, PAUSED
    }

    companion object {
        const val LIST_POS_RECORDING = -1
        const val LIST_POS_PINNED = -2

        fun from(message: AbstractChatMessageWithMedia, pos: Int) = from(message.message,message.offlineMedia!!.path,pos)

        fun from(message: AbstractChatMessage, mediaUrl: String, pos: Int): MediaPlayerInfo {
            return MediaPlayerInfo(
                messageId = message.messageId,
                mediaPath = mediaUrl,
                meta = message.mediaMeta!!,
                listPosition = pos
            )
        }

        fun from(med: OfflineMedia, meta: MediaMeta, pos: Int): MediaPlayerInfo {
            return MediaPlayerInfo(
                messageId = null,
                mediaPath = med.path,
                meta = meta,
                listPosition = pos
            )
        }

        fun from(med: TempMedia, meta: MediaMeta, pos: Int): MediaPlayerInfo {
            return MediaPlayerInfo(
                messageId = null,
                mediaPath = med.path,
                meta = meta,
                listPosition = pos
            )
        }
    }
}