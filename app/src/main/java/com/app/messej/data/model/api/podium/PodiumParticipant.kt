package com.app.messej.data.model.api.podium

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.UserRatingProvider
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class PodiumParticipant(
    @SerializedName("id"         ) override val id          : Int,
    @SerializedName("name"       ) override val name        : String,
    @SerializedName("username"   ) override val username    : String?,
    @SerializedName("thumbnail"  ) override val thumbnail   : String?,
    @SerializedName("membership" ) override val membership  : UserType,
    @SerializedName("citizenship") override val citizenship : UserCitizenship?,
    @SerializedName("gender"     )          val gender      : Gender? = null,
    @SerializedName("verified"   ) override val verified    : <PERSON><PERSON><PERSON>,
    @SerializedName("country_code")override val countryCode : String?  = null,
    @SerializedName("role"       )          var role        : Podium.PodiumUserRole? = null,
    @SerializedName("is_followed")          var isFollowed  : Boolean? = null,
    @SerializedName("participant_gift_paused") var participantGiftPaused : Boolean? = null,
    @SerializedName("allow_join_speak_podiums_for_free") val haveEmpowermentToSpeak : Boolean? = null,

    @SerializedName("report_pay_fine"   ) val reportPayFine   : Boolean? = false,
    @SerializedName("user_likes") val userLikes: Int? = null,
    @SerializedName("user_rating") override val userRating: Double? = null,
): AbstractUser(), UserRatingProvider {

    companion object {
        fun from(user: AbstractUser): PodiumParticipant {
            return PodiumParticipant(
                id = user.id,
                name = user.name,
                username = user.username,
                thumbnail = user.thumbnail,
                membership = user.membership,
                citizenship = user.citizenship?:UserCitizenship.CITIZEN,
                verified = user.verified,
            )
        }
    }

    var countryFlag: Int? = null

    val likesFormatted: String
        get() {
            return if (userLikes==null) "0/0"
            else "$userLikes/${userLikes*0.8f}"
        }

    override val userRatingPercent: String
        get() = super.userRatingPercent
}
