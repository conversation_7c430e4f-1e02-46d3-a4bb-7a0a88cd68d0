package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class UserBlockEvent(
    @SerializedName("blocked")
    val blocked: Boolean = false,
    @SerializedName("blocked_by")
    val blockedBy: Int = 0,
    @SerializedName("blocked_user_id")
    val blockedUserId: Int = 0,
//    @SerializedName("profile")
//    val profile: OtherUserResponse? = null,
    @SerializedName("totalBlockedChats")
    val totalBlockedChats: Int? = null
)