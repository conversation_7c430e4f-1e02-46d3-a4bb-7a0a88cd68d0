package com.app.messej.data.model.notification
import com.google.gson.annotations.SerializedName


data class PodiumNotification (
    @SerializedName("private"           ) var private          : String,
    @SerializedName("receiver_id"       ) var receiverId       : Int,
    @SerializedName("html_text"         ) var htmlText         : String,
    @SerializedName("podium_name"       ) var podiumName       : String,
    @SerializedName("notification_id"   ) var notificationId   : Int = 0,
    @SerializedName("message"           ) var message          : String,
    @SerializedName("category"          ) var category         : String,
    @SerializedName("thumbnail_url"     ) var thumbnailUrl     : String,
    @SerializedName("podium_id"         ) var podiumId         : String,
    @SerializedName("sender_id"         ) var senderId         : Int
)
