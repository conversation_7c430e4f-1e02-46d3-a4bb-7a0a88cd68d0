package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.entity.FlashVideo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class UserFlashDataSource(private val api: FlashAPIService,val userId: Int): PagingSource<Int, FlashVideo>() {

    companion object {
        private const val STARTING_KEY = 1
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FlashVideo> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getUserFlashList(id = userId, page = currentPage)
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (data.flashList.isEmpty() || data.currentPage >= data.totalPages) null else currentPage.plus(1)
                data.flashList.forEach { it.sanitize() }
                LoadResult.Page(
                    data = data.flashList, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, FlashVideo>): Int? {
        return null
    }
}