package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName

data class UserDetailResponse(
    @SerializedName("address") val address: String,
    @SerializedName("city") val city: String,
    @SerializedName("country_code") val countryCode: String,
    @SerializedName("country_code_iso") val countryCodeIso: String,
    @SerializedName("fname") val firstName: String,
    @SerializedName("id") val id: Int,
    @SerializedName("id_image") val idImage: String,
    @SerializedName("id_number") val idNumber: String,
    @SerializedName("lname") val lastName: String,
    @SerializedName("persist") val persist: <PERSON>olean,
    @SerializedName("phone_no") val phoneNo: String,
    @SerializedName("phone_verified") val phoneVerified: Boolean,
    @SerializedName("pincode") val pincode: String,
    @SerializedName("time_created") val timeCreated: String,
    @SerializedName("time_updated") val timeUpdated: String,
    @SerializedName("user_id") val userId: Int,
)