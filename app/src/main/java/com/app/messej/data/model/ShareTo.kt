package com.app.messej.data.model

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class ShareTo (
    @SerializedName("dears"  ) var dears  : Boolean = false,
    @SerializedName("fans"   ) var fans   : Boolean = false,
    @SerializedName("likers" ) var likers : Boolean = false,
    @SerializedName("public" ) var public : Boolean = false
) {
    class Converter {
        @TypeConverter
        fun decode(data: String?): ShareTo? {
            data?: return null
            val type: Type = object : TypeToken<ShareTo?>() {}.type
            return Gson().fromJson<ShareTo>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: ShareTo?): String? {
            return Gson().toJson(someObjects)
        }
    }
}