package com.app.messej.data.model

import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.MediaResolution
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.MediaUtils.asUri
import java.io.File

/**
 * used to represent a media file in local storage
 * @param name: the filename with extension
 * @param path: the absolutePath of the file in storage
 */
abstract class AbstractMedia {

    abstract var name: String
    abstract var path: String
    abstract val mediaType: MediaType

    open val resolution: MediaResolution?
        get() {
            return when(mediaType) {
                MediaType.IMAGE -> MediaUtils.getImageResolution(file)
                MediaType.VIDEO -> path.asUri()?.let { MediaUtils.getVideoResolution(it) }
                MediaType.AUDIO -> null
                MediaType.DOCUMENT -> null
            }
        }

    val file: File
        get() = File(path)
}