package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class SocialActiveCaseMainTab {
    @SerializedName("donate_cases") Donate,
    @SerializedName("new_cases") NewCases;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}

enum class SocialActiveTab {
    @SerializedName("all") All,
    @SerializedName("personal") Personal,
    @SerializedName("upgrade") Upgrade,
    @SerializedName("social") Social;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}

enum class SocialCaseFilter {
    @SerializedName("all") All,
    @SerializedName("declined") Declined,
    @SerializedName("unmet_voting") UnmetVoting,
    @SerializedName("pending") Pending,
    @SerializedName("closed") Closed;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}

enum class SocialCaseStatus {
    //Vote Cases
    @SerializedName("NEW") NEW,
    @SerializedName("UNMET_VOTING") UNMET_VOTING,
    @SerializedName("DECLINED") DECLINED,
    @SerializedName("ARCHIVED") ARCHIVED,
    @SerializedName("DELETED") DELETED,

    //Not Listing, Only draft
    @SerializedName("DRAFT") DRAFT,

    //Donate Cases
    @SerializedName("ACTIVE") ACTIVE,
    @SerializedName("CLOSED") CLOSED,
    @SerializedName("PENDING_APPROVAL") PENDING_APPROVAL;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    val isNewCase: Boolean
        get() = this == NEW

    val isActive: Boolean
        get() = this == ACTIVE

    val isArchived: Boolean
        get() = this == ARCHIVED

    val isInDrafts: Boolean
        get() = this == DRAFT

    val isPendingApproval: Boolean
        get() = this == PENDING_APPROVAL

    val isClosed: Boolean
        get() = this == CLOSED

}

enum class SocialActionButtonType {
    ActiveButton,
    DisabledButton,
    ExpiredButton
}

enum class SocialRequestType {
    @SerializedName("upgrade") Upgrade,
    @SerializedName("personal") Personal;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}


enum class SocialVoteAction {
    @SerializedName("support") Support,
    @SerializedName("oppose") Oppose;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}

enum class MySocialSupportActionMenuItem {
    @SerializedName("edit") Edit,
    @SerializedName("archive") Archive,
    @SerializedName("unarchive") UnArchive,
    @SerializedName("delete") Delete,
    @SerializedName("draft") Draft;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}

enum class SocialQuestionSubmitType {
    AddNewQuestion,
    EditQuestion,
    AddAnswer,
    EditAnswer;

    val isQuestion: Boolean
        get() = this == AddNewQuestion || this == EditQuestion

}