package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumTab
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PodiumsLiveListDataSource(private val api: PodiumAPIService,private val countCallback: (Int) -> Unit = {}): PagingSource<Int, Podium>() {
    companion object {
        private const val STARTING_KEY = 1
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Podium> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getPodiumList(tab = PodiumTab.LIVE_PODIUM.serializedName(), page = currentPage)
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                countCallback.invoke(data.totalItems)
                val nextKey = if (!data.hasNextPage) null else currentPage.inc()

                LoadResult.Page(
                    data = data.podiums, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, Podium>): Int? {
        return null
    }
}