package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PostatAPIService
import com.app.messej.data.model.PostatMentionedUser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext


class PostatMentionListDataSource(private val api: PostatAPIService, val searchTerm: String? = null) : PagingSource<Int, PostatMentionedUser>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PostatMentionedUser> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getMentionUsers(page = currentPage, searchKeyword = searchTerm)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (!data.nextPage) null else currentPage.inc()
                LoadResult.Page(
                    data = data.userDetails, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, PostatMentionedUser>): Int? {
        return null
    }
}