package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.ChatTextColor
import com.google.gson.annotations.SerializedName

data class PrivateChatMessagePayload(
    @SerializedName("private_chat_id") val messageId: String,
    @SerializedName("room_id") val roomId: String,
    @SerializedName("receiver") val receiver: Int,
    @SerializedName("message") override val message: String?,
    @SerializedName("color") override val color: ChatTextColor?,
    @SerializedName("forward_id") val forwardID:String?=null
): AbstractChatMessagePayload()