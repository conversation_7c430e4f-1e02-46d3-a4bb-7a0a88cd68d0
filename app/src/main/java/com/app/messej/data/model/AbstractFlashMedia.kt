package com.app.messej.data.model

import androidx.media3.common.MediaItem
import java.io.File

abstract class AbstractFlashMedia {
    abstract val path: String

    open val mediaItem: MediaItem
        get() {
            val inputMediaItem = MediaItem.Builder()
            inputMediaItem.setUri(file.absolutePath)
            return inputMediaItem.build()
        }

    val file: File
        get() = File(path)
}