package com.app.messej.data.model.enums

import com.google.gson.TypeAdapter
import com.google.gson.annotations.JsonAdapter
import com.google.gson.annotations.SerializedName
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter
import java.io.IOException

// @SerializedName(value = "firstName", alternate = {"firstname", "Firstname"})

@JsonAdapter(UserType.UserTypeAdapter::class)
enum class UserType(val value: String) {
    @SerializedName(value = "premium", alternate = ["Premium"]) PREMIUM("premium"),
    @SerializedName(value = "free", alternate = ["Free"]) FREE("free");

    override fun toString() = value

    companion object {
        infix fun from(value: String): UserType? = values().firstOrNull { it.value == value }
    }

    class UserTypeAdapter: TypeAdapter<UserType>() {
        @Throws(IOException::class)
        override fun write(out: JsonWriter, value: UserType?) {
            if (value == null) out.nullValue() else out.value(value.value)
        }

        @Throws(IOException::class)
        override fun read(reader: JsonReader): UserType? {
            return if (reader.peek() === JsonToken.NULL) {
                reader.nextNull()
                null
            } else {
                from(reader.nextString().lowercase())
            }
        }
    }
}