package com.app.messej.data.socket


import android.util.Log
import com.app.messej.data.model.socket.SocketEventPayload
import com.app.messej.data.utils.BaseMultiListener
import io.socket.client.Ack
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import io.socket.engineio.client.transports.WebSocket
import java.net.URI
import java.net.URISyntaxException
import java.util.Collections.singletonMap

abstract class AbstractSocketService(
    protected val socketUrl: String,
    protected val path: String? = null,
    protected val authKey: String = "token",
    protected val authPrefix: String = "Bearer",
) : BaseMultiListener<SocketListener>() {

    private val TAG = AbstractSocketService::class.java.simpleName

    private fun addAuthPrefix(token: String): String = if(token.startsWith(authPrefix)) token else "$authPrefix $token"

    private var socket: Socket? = null

    val isConnected: Boolean
        get() = socket?.connected()?:false

    private val connectListener: Emitter.Listener = Emitter.Listener { args ->
        Log.d(TAG, "onConnect ...")
        Log.d("CMW", "SocketService: socket connected")
        listeners.forEach { it.onConnect() }
    }
    private val connectionErrorListener: Emitter.Listener = Emitter.Listener { args ->
        Log.d(TAG, "onConnectionError : ${args.getOrNull(0)}")
        listeners.forEach { it.onConnectionError() }
    }
    private val disconnectListener: Emitter.Listener = Emitter.Listener { args ->
        Log.d(TAG, "onDisconnect : ${args.getOrNull(0).toString()}")
        socket?.off()
        listeners.forEach { it.onDisconnect() }
    }

    @Throws(URISyntaxException::class)
    fun start(token: String) {
        val uri: URI = URI.create(socketUrl)
        val options = IO.Options.builder().setPath(path).setAuth(singletonMap(authKey, addAuthPrefix(token))).setForceNew(true).setTransports(arrayOf(WebSocket.NAME)).setSecure(true)
            .setReconnection(true).setReconnectionAttempts(1).build()
        socket = IO.socket(uri, options)

        socket?.apply {
            on(Socket.EVENT_CONNECT, connectListener)
            on(Socket.EVENT_DISCONNECT, disconnectListener)
            on(Socket.EVENT_CONNECT_ERROR, connectionErrorListener)
            connect()
            offAnyIncoming()
            offAnyOutgoing()
            interceptEvents()
        }
    }

    abstract fun Socket.interceptEvents()

    fun disconnect() {
        socket?.disconnect()
        socket = null
    }

    fun emitEvent(event: String, payload: SocketEventPayload) {
//        return Single.create { singleEmitter ->
//            if (socket == null) singleEmitter.onError(NullSocketException())
        Log.d(TAG, "emitEvent: $event: ${payload.serialize()}")
        socket?.emit(event, payload.serialize(), object : Ack {
            override fun call(vararg args: Any?) {
                Log.d(TAG, "ack: $args")
            }
        })

//            chatMessage.setSent(true)
//            singleEmitter.onSuccess(chatMessage)

    }
}