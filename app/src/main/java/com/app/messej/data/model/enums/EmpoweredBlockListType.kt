package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class EmpoweredBlockListType{
    @SerializedName("flash")BLOCK_LIST_FLASH,
    /*@SerializedName("podium_block")BLOCK_LIST_PODIUM,*/
    @SerializedName("podium_speaking_block")BLOCK_LIST_PODIUM_SPEAKING,
    @SerializedName("podium_write_comments_block")BLOCK_LIST_PODIUM_WRITE_COMMENTS,
    @SerializedName("postat_posts_block")BLOCK_LIST_POSTAT_POST,
    @SerializedName("huddle_posts_block")BLOCK_LIST_HUDDLE_POST;
    override fun toString(): String {
    return javaClass
        .getField(name)
        .getAnnotation(SerializedName::class.java)
        ?.value ?: ""
    }
}