package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.BroadcastMode
import com.google.gson.annotations.SerializedName

data class BroadcastDeleteEvent(
    @SerializedName("id") val messageId: String,
    @SerializedName("broadcast_type") val mode: BroadcastMode,
    @SerializedName("broadcaster") val broadcaster: Int,
    @SerializedName("deleted")  val deleted: Boolean
): SocketEventPayload()
