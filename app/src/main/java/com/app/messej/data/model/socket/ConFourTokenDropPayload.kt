package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.ConFourGameStatus
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard
import com.google.gson.annotations.SerializedName

data class ConFourTokenDropPayload(
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("podium_id") val podiumId: String?,
    @SerializedName("user_id") val userId: Int?,
    @SerializedName("opponent_user_id") val opponentUserId: Int?,

    @SerializedName("participant_token_number") val participantTokenNumber: Int?,
    @SerializedName("row") val row: Int,
    @SerializedName("column") val column: Int,
    @SerializedName("status") val status: ConFourGameStatus?,

    ) : SocketEventPayload() {
    companion object {
        fun from(challenge: PodiumChallenge,
                 slot: ConnectFourBoard.DropPoint,
                 player: ChallengePlayer,
                 myUserId: Int,
                 opponentUserId: Int,
                 status: ConFourGameStatus?): ConFourTokenDropPayload {

            return ConFourTokenDropPayload(
                challengeId = challenge.challengeId,
                podiumId = challenge.podiumId,
                userId = myUserId,
                opponentUserId = opponentUserId,

                row = slot.row,
                column = slot.column,

                participantTokenNumber = player.tokenNumber,
                status = if(status==ConFourGameStatus.NONE) null else status
            )
        }
    }
}
