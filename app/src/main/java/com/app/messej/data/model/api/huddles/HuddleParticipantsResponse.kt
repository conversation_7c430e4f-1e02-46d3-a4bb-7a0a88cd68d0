package com.app.messej.data.model.api.huddles

import com.google.gson.annotations.SerializedName

data class HuddleParticipantsResponse(
    @SerializedName("current_page"  ) var currentPage  : Int?               = null,
    @SerializedName("members"       ) var members      : ArrayList<Participant> = arrayListOf(),
    @SerializedName("next_page"     ) var nextPage     : Int?            = null,
    @SerializedName("total_members" ) var totalMembers : Int?               = null
){}
