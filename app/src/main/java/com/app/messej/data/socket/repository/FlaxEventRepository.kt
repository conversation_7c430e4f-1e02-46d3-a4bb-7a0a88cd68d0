package com.app.messej.data.socket.repository

import com.app.messej.MainApplication
import com.app.messej.data.model.socket.FlaxTransferPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object FlaxEventRepository : BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {
    private val accountRepo = AccountRepository(MainApplication.applicationInstance())
    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when (event) {
            ChatSocketEvent.RX_FLAX_TRANSFER -> {
                onFlaxTransfer(data)
            }

            else -> return false
        }
        return true
    }


    private val _flaxTransferPayLoad: MutableSharedFlow<FlaxTransferPayload> = MutableSharedFlow()
    val flaxTransferPayLoad: SharedFlow<FlaxTransferPayload> = _flaxTransferPayLoad

    private fun onFlaxTransfer(data: JSONObject) = runBlocking {
        val info = Gson().fromJson<FlaxTransferPayload>(data.toString())
        withContext(Dispatchers.IO) {
                _flaxTransferPayLoad.emit(info)

        }
    }


}

