package com.app.messej.data.model.api.profile

import com.app.messej.data.model.enums.Gender
import com.google.gson.annotations.SerializedName

data class SetProfileRequest(
    @SerializedName("name"          ) val name          : String,
    @SerializedName("gender"        ) val gender        : Gender,
    @SerializedName("date_of_birth" ) val dateOfBirth   : String,
    @SerializedName("long"         ) val long         : Double,
    @SerializedName("lat"         ) val lat         : Double

    ) {
}
