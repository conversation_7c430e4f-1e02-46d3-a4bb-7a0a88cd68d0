package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.FlashVideoWithMedia
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class MyFlashListRemoteMediator(
    private val userId: Int,
    private val database: FlashatDatabase,
    private val networkService: FlashAPIService,
    ) : RemoteMediator<Int, FlashVideoWithMedia>() {
    val dao = database.getFlashDao()
    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = "${EntityDescriptions.TABLE_FLASH}-ofUser-$userId"

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, FlashVideoWithMedia>
    ): MediatorResult {
        return try {
            val page = when (loadType) {
                LoadType.REFRESH -> null
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    Log.d("PHRM", "load: APPEND")
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }
                    // You must explicitly check if the page key is null when
                    // appending, since null is only valid for initial load.
                    // If you receive null for APPEND, that means you have
                    // reached the end of pagination and there are no more
                    // items to load.
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }
                    remoteKey.nextPageInt
                }
            }

            // Suspending network load via Retrofit. This doesn't need to be
            // wrapped in a withContext(Dispatcher.IO) { ... } block since
            // Retrofit's Coroutine CallAdapter dispatches on a worker
            // thread.
            val response = networkService.getUserFlashList(id = userId, page = page)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }
            val flashes = result.flashList
            flashes.forEach { it.flashType = FlashVideo.FlashType.MINE }
            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    dao.deleteAllByTypeAndStatus(FlashVideo.FlashType.MINE, AbstractChatMessage.SendStatus.NONE)
                }

                // Update RemoteKey for this query.
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, (result.currentPage+1).toString())
                )

                // Insert new users into database, which invalidates the
                // current PagingData, allowing Paging to present the updates
                // in the DB.
                dao.insert(flashes)
            }


            MediatorResult.Success(endOfPaginationReached = result.currentPage >= result.totalPages)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}