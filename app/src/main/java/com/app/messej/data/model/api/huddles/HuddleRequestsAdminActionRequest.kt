package com.app.messej.data.model.api.huddles

import com.google.gson.annotations.SerializedName

data class HuddleRequestsAdminActionRequest(
    @SerializedName("member_id") var memberId: Int,
    @SerializedName("action") var action: RequestsAdminAction
    ) {
        enum class RequestsAdminAction(val value: String) {
            @SerializedName("admin_declined") ADMIN_DECLINED("admin_declined"),
            @SerializedName("admin_accepted") ADMIN_ACCEPTED("admin_accepted"),
            @SerializedName("block") BLOCK("block");
        }
     }