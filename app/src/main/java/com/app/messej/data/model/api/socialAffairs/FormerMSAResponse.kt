package com.app.messej.data.model.api.socialAffairs

import androidx.annotation.DrawableRes
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class FormerMSAResponse(
    @SerializedName("has_next") val haveNextPage: Boolean? = null,
    @SerializedName("members") val members: List<FormerMSA>? = null
) {
    data class FormerMSA(
        @SerializedName("id") val id : Int?,
        @SerializedName("verified") val verified: Boolean?,
        @SerializedName("name") val name: String?,
        @SerializedName("membership") val membership: UserType,
        @SerializedName("user_image") val userImage: String? = null,
        @SerializedName("country_code_iso") val countryCode: String? = null,
        @SerializedName("from_date") val fromDate: String? = null,
        @SerializedName("to_date") val toDate: String? = null,
        @DrawableRes val countryFlag: Int? = null
    ) {
        val isPremiumUser: <PERSON>ole<PERSON>
            get() = membership == UserType.PREMIUM
    }

    companion object {

        val testFormerMSA = FormerMSA(
            verified = false,
            name = "Abijith",
            membership = UserType.PREMIUM,
            fromDate = "12/10/2025",
            toDate = "12/10/2025",
            userImage = "https://example.com/images/raj.png",
            countryCode = "IN",
            id = 1511
        )

        val testData = FormerMSAResponse(
            haveNextPage = null,
            members = listOf(
                FormerMSA(
                    verified = false,
                    name = "Raj Mehta",
                    membership = UserType.PREMIUM,
                    fromDate = "12/10/2025",
                    toDate = "12/10/2025",
                    userImage = "https://example.com/images/raj.png",
                    countryCode = "IN",
                    id = 1899
                ),
                testFormerMSA
            )
        )
    }
}


