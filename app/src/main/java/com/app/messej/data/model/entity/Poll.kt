package com.app.messej.data.model.entity

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.api.poll.Answer
import com.app.messej.data.model.api.poll.UserAnswer
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.time.ZonedDateTime

@Entity(tableName = EntityDescriptions.TABLE_POLLS_LIST)
@TypeConverters(
    Answer.Converter::class,
)
@Parcelize
data class Poll(
    @SerializedName("answers") val answers: List<Answer> = listOf(),
    @SerializedName("end_date") @ColumnInfo(name = "end_date") val endDate: String? = null,
    @SerializedName("huddle_id") @ColumnInfo(name = HUDDLE_ID) val huddleId: Int,
    @PrimaryKey @SerializedName("id") @ColumnInfo(name = COLUMN_ID) val id: Int,
    @SerializedName("question") @ColumnInfo(name = "question") val question: String?=null,
    @SerializedName("start_date") @ColumnInfo(name = "start_date") val startDate: String?=null,
    @SerializedName("time_created") @ColumnInfo(name = "time_created") val timeCreated: String,
    @SerializedName("time_updated") @ColumnInfo(name = "time_updated") val timeUpdated: String,
    @SerializedName("end_date_iso") @ColumnInfo(name = "end_date_iso") val endDateIso: String? = null,
    @SerializedName("start_date_iso") @ColumnInfo(name = "start_date_iso") val startDateIso: String,
    @SerializedName("visitors_count") @ColumnInfo(name = "visitors_count") val visitorCount: Int,
    @SerializedName("citizens_count") @ColumnInfo(name = "citizens_count") val citizenCount: Int,
    @SerializedName("total_answered") @ColumnInfo(name = "total_answered") val totalAnswered: Int,
    @SerializedName("user_answer") @ColumnInfo(name = "user_answer") val userAnswer: UserAnswer? = null,
):Parcelable {

    companion object {
        const val COLUMN_ID = "id"
        const val HUDDLE_ID = "huddle_id"
        const val HUDDLE_START_DATE = "start_date"
        const val HUDDLE_END_DATE = "end_date"
        const val HUDDLE_START_DATE_ISO = "start_date_iso"
        const val HUDDLE_END_DATE_ISO = "end_date_iso"
    }
    private val endDateParsed: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(endDateIso, DateTimeUtils.FORMAT_ISO_DATE_TIME)

    val endTime
        get() = DateTimeUtils.format(endDateParsed, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)

    val timeStartParsed
        get() = DateTimeUtils.format(DateTimeUtils.parseZonedDateTime(startDateIso, DateTimeUtils.FORMAT_ISO_DATE_TIME), DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)


}