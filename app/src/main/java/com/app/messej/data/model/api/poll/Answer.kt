package com.app.messej.data.model.api.poll


import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize


@Parcelize
data class Answer(
    @SerializedName("id")                   @ColumnInfo(name = "id")                val id              : Int,
    @SerializedName("answer")               @ColumnInfo(name = "answer")            val answer          : String,
    @SerializedName("answer_percentage")    @ColumnInfo(name = "answer_percentage") val answerPercentage: Double,
    @SerializedName("listing_order")        @ColumnInfo(name = "listing_order")     val listingOrder    : Int,
    @SerializedName("poll_id")              @ColumnInfo(name = "poll_id")           val pollId          : Int,
    @SerializedName("time_created")         @ColumnInfo(name = "time_created")      val timeCreated     : String,
    @SerializedName("time_updated")         @ColumnInfo(name = "time_updated")      val timeUpdated     : String,
    @ColumnInfo(name = "no_of_votes") @SerializedName("no_of_votes") val voteCount: Int
) :Parcelable {

    val roundedPercentage
        get() = answerPercentage.toInt()
    class Converter {
        private val gson = Gson()

        @TypeConverter
        fun fromAnswerList(answerList: List<Answer>): String {
            return gson.toJson(answerList)
        }

        @TypeConverter
        fun toAnswerList(answerListString: String): List<Answer> {
            val listType = object : TypeToken<List<Answer>>() {}.type
            return gson.fromJson(answerListString, listType)
        }


        @TypeConverter
        fun fromUserAnswer(userAnswer: UserAnswer?): String? {
            if (userAnswer == null) {
                return null
            }
            val gson = Gson()
            return gson.toJson(userAnswer)
        }

        @TypeConverter
        fun toUserAnswer(userAnswerJson: String?): UserAnswer? {
            if (userAnswerJson == null) {
                return null
            }
            val gson = Gson()
            return gson.fromJson(userAnswerJson, UserAnswer::class.java)
        }


    }
}

