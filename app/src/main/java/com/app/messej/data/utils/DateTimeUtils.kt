package com.app.messej.data.utils

import android.content.Context
import android.content.res.Resources
import android.util.Log
import com.app.messej.R
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.Period
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit


object DateTimeUtils {

    // https://docs.oracle.com/javase/8/docs/api/java/time/format/DateTimeFormatter.html
    const val FORMAT_DDMMYYYY_DASHED = "dd-MM-yyyy"
    const val FORMAT_DDMMYYYY_SLASHED = "dd/MM/yyyy"
    const val FORMAT_DDMMYY_SLASHED = "dd/MM/yy"
    const val FORMAT_DDLLLYYYY_SLASHED = "dd/LLL/yyyy"
    const val FORMAT_READABLE_DATE = "d MMM yyyy"
    const val FORMAT_READABLE_DATE_SLASHED = "dd/MMM/yyyy"
    const val FORMAT_READABLE_DATE_WITH_DAY = "EEE, d MMM yyyy"
    const val FORMAT_READABLE_DATE_TIME = "d MMM yyyy, hh:mm a"
    const val FORMAT_READABLE_TIME = "hh:mm a"
    const val FORMAT_READABLE_TIME_24HRS = "HH:mm"
    const val FORMAT_READABLE_TIME_SECONDS_24HRS = "HH:mm:ss"
    const val FORMAT_DAY_LONG = "EEEE"
    const val FORMAT_DAY_SHORT = "EEE"
    const val FORMAT_RFC_1123_DATE_TIME = "E, dd MMM yyyy HH:mm:ss z"

    const val FORMAT_DATE_TIME_FILE_TS = "yyyyMMddhhmmss"
    const val FORMAT_DATE_TIME_FILE_TS_READABLE = "yyyy-MM-dd hh mm a"

    const val FORMAT_DATE_HUDDLE_GROUP_INFO = "dd MMMM yyyy"
    const val FORMAT_ISO_DATE_TIME = "yyyy-MM-dd'T'HH:mm:ss"
    const val FORMAT_ISO_DATE = "yyyy-MM-dd"

    const val FORMAT_DASHED_SLASHED="MM/dd/yyyy | HH:mm"

    const val FORMAT_ISO_DATE_TIME_WITHOUT_T = "yyyy-MM-dd HH:mm:ss"
    const val FORMAT_DATE_YYYYMMDD_SLASHED = "yyyy/MM/dd"


    fun formatDurationToSingleUnit(dur: Duration, context: Context): String {
        return when {
            dur < Duration.ofMinutes(1) -> context.getString(R.string.time_utils_now)
            dur < Duration.ofHours(1) -> "${dur.toMinutes()}m"
            dur < Duration.ofDays(1) -> "${dur.toHours()}h"
            else -> "${dur.toDays()}d"
        }
    }

    fun parseMillisToDate(millis: Long): LocalDate {
        return parseMillisToDateTime(millis).toLocalDate()
    }

    fun parseMillisToDateTime(millis: Long): ZonedDateTime {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(millis), ZoneOffset.UTC)
    }

    fun parseDateTime(date: String?, format: String? = null): LocalDateTime? {
        date ?: return null
        try {
            format?.let {
                return LocalDateTime.parse(date, DateTimeFormatter.ofPattern(format))
            }
            return LocalDateTime.parse(date)
        } catch (e: Exception) {
            Firebase.crashlytics.log("DateTimeUtil:parseDateTime failed for value: '$date' and format: '$format'")
            return null
        }
    }

    fun parseDate(date: String?, format: String? = null): LocalDate? {
        date?: return null
        try {
            Log.d("DATESMASHED", date)
            format?.let {
                return LocalDate.parse(date, DateTimeFormatter.ofPattern(format))
            }
            return LocalDate.parse(date)
        } catch (e: Exception) {
            Firebase.crashlytics.log("DateTimeUtil:parseDate failed for value: '$date' and format: '$format'")
            return null
        }
    }

    fun parseZonedDateTime(date: String?, format: String? = null): ZonedDateTime? {
        date?: return null
        try {
            format?.let {
                return ZonedDateTime.parse(date, DateTimeFormatter.ofPattern(format).withZone(ZoneId.systemDefault()))
            }
            return ZonedDateTime.parse(date)
        } catch (e: Exception) {
            Firebase.crashlytics.log("DateTimeUtil:parseZonedDateTime failed for value: '$date' and format: '$format'")
            return null
        }
    }

    fun parseZonedDateTimeWithoutZ(date: String?, format: String? = null): ZonedDateTime? {
        date?: return null
        val dt = if(date.endsWith('Z')) date else date+'Z'
        return parseZonedDateTime(dt,format)
    }

    fun getZonedDateTimeNowAsString(): String {
        return ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT)
    }

    fun getZonedDateTimeNow(): ZonedDateTime {
        return ZonedDateTime.now(ZoneOffset.UTC)
    }

    fun formatISOInstant(date: ZonedDateTime): String {
        return date.format(DateTimeFormatter.ISO_INSTANT)
    }

    fun getLocalDateTime(date: ZonedDateTime): LocalDateTime = date.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime()
    fun getLocalDate(date: ZonedDateTime): LocalDate = date.withZoneSameInstant(ZoneId.systemDefault()).toLocalDate()

    fun isSame(a: ZonedDateTime, b: ZonedDateTime, unit: ChronoUnit): Boolean {
        return getLocalDateTime(a).truncatedTo(unit).equals(getLocalDateTime(b).truncatedTo(unit))
    }

    fun format(dt: LocalDate?, format: String = FORMAT_DDMMYYYY_DASHED): String {

        Log.d("DATESMASHED", "format: ${dt.toString()}" )
        dt?: return ""
        val fm = DateTimeFormatter.ofPattern(format)
        return dt.format(fm)
    }

    fun formatToCurrentZone(dt: LocalDate?, format: String = FORMAT_DDMMYYYY_SLASHED): String {
        dt?: return ""
        val fm = DateTimeFormatter.ofPattern(format).withZone(ZoneId.systemDefault())
        return dt.format(fm)
    }

    fun formatToCurrentZone(dt: LocalDateTime?, format: String = FORMAT_DDMMYYYY_SLASHED): String {
        dt?: return ""
        val fm = DateTimeFormatter.ofPattern(format).withZone(ZoneId.systemDefault())
        return dt.format(fm)
    }

    fun format(dt: LocalDateTime?, format: String = FORMAT_DDMMYYYY_DASHED): String {
        dt?: return ""
        val fm = DateTimeFormatter.ofPattern(format)
        return dt.format(fm)
    }

    fun format(dt: LocalTime?, format: String = FORMAT_READABLE_TIME_24HRS): String {
        dt?: return ""
        val fm = DateTimeFormatter.ofPattern(format)
        return dt.format(fm)
    }

    fun format(dt: ZonedDateTime?, format: String = FORMAT_DDMMYYYY_DASHED): String {
        dt?: return ""
        val fm = DateTimeFormatter.ofPattern(format).withZone(ZoneId.systemDefault())
        return dt.format(fm)
    }

    fun formatTime24Hr(dt: ZonedDateTime?): String {
        return format(dt = dt, format = FORMAT_READABLE_TIME_24HRS)
    }

    fun periodToNow(dt: LocalDate): Period {
        return Period.between(dt, LocalDate.now())
    }

    fun durationToNowFromPast(dt: ZonedDateTime?): Duration?{
        dt?: return null
        return Duration.between(dt, ZonedDateTime.now())
    }

    fun periodToNowFromPast(dt: ZonedDateTime?): Period?{
        dt?: return null
        return Period.between(dt.toLocalDate(), LocalDate.now())
    }

    fun durationFromNowToFuture(dt: ZonedDateTime?): Duration?{
        dt?: return null
        return Duration.between(ZonedDateTime.now(),dt)
    }
    /**
     * parse duration in the format 0:08
     */
    fun parseToDuration(value: String): Duration? {
        return try {
            Duration.parse(value.replace("(\\d{1,2}):(\\d{1,2})".toRegex(), "PT$1M$2S"))
        } catch (e: Exception) {
            Duration.ZERO
        }
    }

    fun formatSeconds(seconds: Float) = formatSeconds(seconds.toLong())
    fun formatSeconds(seconds: Int) = formatSeconds(seconds.toLong())

    /**
     * format seconds to the format 00:08
     */
    fun formatSeconds(seconds: Long): String {
        var remSecs = seconds
        val hours: Int = (remSecs/3600).toInt()
        remSecs %= 3600
        val mins: Int = (remSecs/60).toInt()
        remSecs %= 60
        return if(hours>0) {
            return "$hours:${mins.toString().padStart(2,'0')}:${remSecs.toString().padStart(2,'0')}"
        } else {
            "$mins:${remSecs.toString().padStart(2,'0')}"
        }
    }
    fun formatDuration(dur:Duration) = formatSeconds(dur.seconds)

    interface ResourceProvider {
        fun getHoursString(t: Int): String
        fun getMinutesString(t: Int): String
        fun getSecondsString(t: Int): String
    }

    fun getResourceProvider(res: Resources) = object: ResourceProvider {
        override fun getHoursString(t: Int) = res.getQuantityString(R.plurals.common_hours, t, t.toString())
        override fun getMinutesString(t: Int) = res.getQuantityString(R.plurals.common_minutes, t, t.toString())
        override fun getSecondsString(t: Int) = res.getQuantityString(R.plurals.common_seconds, t, t.toString())
    }

    fun formatDurationToHours(seconds:Long, resourceProvider: ResourceProvider): String {
        var remSecs = seconds
        val hours: Int = (remSecs/3600).toInt()
        remSecs %= 3600
        val mins: Int = (remSecs/60).toInt()
        remSecs %= 60
        val time = StringBuilder()
        if(hours>0) {
            time.append(resourceProvider.getHoursString(hours))
            time.append(" ")
        }
        if (mins>0 || (remSecs>0 && hours>0)) {
            time.append(resourceProvider.getMinutesString(mins))
            time.append(" ")

        }
        if (remSecs>0) {
            time.append(resourceProvider.getSecondsString(remSecs.toInt()))
        }
        if (remSecs==0L && mins==0 && hours==0) {
            time.append(resourceProvider.getSecondsString(0))
        }
        return time.toString().trim()
    }

    fun getTimeZoneFromEpochTime(epochTime: Long?): String {
        val instant = epochTime?.let { Instant.ofEpochSecond(it) }
        val zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneId.systemDefault())
        val formatter = DateTimeFormatter.ofPattern("z")
        return zonedDateTime.format(formatter)
    }

    /**
     * emit the remaining seconds every second after accounting for milli difference at the beginning
     */
    fun countDownTimerFlow(startMs: Long, end: Int = 0) = flow {
        val start = (startMs/1000).toInt()
        var delayMs = startMs%1000
        if (delayMs>10) {
            emit(start+1)
        } else {
            delayMs = 0
        }
        for (i in start downTo end) {
            delay(delayMs)
            delayMs = 1000
            emit(i)
        }
    }



}