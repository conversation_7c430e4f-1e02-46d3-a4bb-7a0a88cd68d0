package com.app.messej.data.model.api.podium.challenges

import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.LocaleUtil
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

data class KnowledgeRaceData(
    @SerializedName("question") val currentQuestion: Question?,
    @SerializedName("timer_end_time") val turnEndTime: Double?,
) {
    val parsedTurnEndTime: ZonedDateTime?
        get() = turnEndTime?.let { DateTimeUtils.parseMillisToDateTime((it*1000).toLong()) }

    data class Question(
        @SerializedName("id"                        ) val id: Int,
        @SerializedName("question_number"           ) val questionNumber: Int,
        @SerializedName("correct_answer_option_id"  ) val correctAnswerOptionId: Int,
        @SerializedName("translations"              ) val translations: Map<String, String>?,
        @SerializedName("options"                   ) val options: List<Option>?
    ){
        val localeTitle: String
            get() {
                val locale = LocaleUtil.getAppLocale()
                return localeTitle(locale)
            }

        fun localeTitle(locale: AppLocale): String {
            return translations.orEmpty().getOrElse(locale.isoCode) {
                translations.orEmpty().getOrDefault(AppLocale.ENGLISH.isoCode, "")
            }
        }
    }

    data class Option(
        @SerializedName("id") val id: Int,
        @SerializedName("translations") val translations: Map<String, String>? // Language code to translated text
    ){
        val localeTitle: String
            get() {
                val locale = LocaleUtil.getAppLocale()
                return localeTitle(locale)
            }

        fun localeTitle(locale: AppLocale): String {
            return translations.orEmpty().getOrElse(locale.isoCode) {
                translations.orEmpty().getOrDefault(AppLocale.ENGLISH.isoCode, "")
            }
        }
    }
}
