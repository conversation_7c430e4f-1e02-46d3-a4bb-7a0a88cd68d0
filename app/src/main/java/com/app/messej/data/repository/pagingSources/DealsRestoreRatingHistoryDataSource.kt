package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.BusinessAPIService
import com.app.messej.data.model.BuyFlaxRates

private const val STARTING_KEY = 1

class DealsRestoreRatingHistoryDataSource(private val api: BusinessAPIService) : PagingSource<Int, BuyFlaxRates>() {


    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, BuyFlaxRates> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getRestoreRatingHistory(page = currentPage, limit = 50)


            val responseData = mutableListOf<BuyFlaxRates>()
            val result = response?.body()?.result
            val data = result?.restoreRatings ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (!response?.body()?.result!!.nextPage) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.restoreRatings, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("RestoreRatingResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, BuyFlaxRates>): Int? {
        return null
    }
}