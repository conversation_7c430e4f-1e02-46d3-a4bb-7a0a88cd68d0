package com.app.messej.data.model.api.settings


import com.google.gson.annotations.SerializedName
data class EnabledDocument(
    @SerializedName("effective_date")
    val effectiveDate: String? = "",
    @SerializedName("id")
    val id: Int? = 0,
    @SerializedName("key_changes")
    val keyChanges: String? = "",
    @SerializedName("policy_type")
    val policyType: String? = "",
    @SerializedName("title")
    val title: String? = ""
)