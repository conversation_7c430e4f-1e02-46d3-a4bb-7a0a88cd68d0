package com.app.messej.data.utils

import java.util.Locale

class FFMPEGBuilder(
    var input: String,
    var output: String,

    var videoCodec: String = CODEC_VIDEO_H264,
    var videoBitrate: String = BITRATE_VIDEO_6M,
    var videoFrameRate: String? = null,
    var videoResolution: String = RESOLUTION_1080,
    var videoCRF: Int? = null,

    var audioCodec: String = CODEC_AUDIO_AAC,
    var audioBitrate: String = BITRATE_AUDIO_160K,

    var preset: Preset = Preset.MEDIUM,
    var overwriteOutput: Boolean = true,
    var enableFastStart: Boolean = true,
    var verbosity: Verbosity = Verbosity.ERROR
) {

    companion object {
        const val FORMAT_MP4 = "mp4"

        const val CRF_DEFAULT = 23

        // https://trac.ffmpeg.org/wiki/Encode/H.264
        const val CODEC_VIDEO_H264 = "libx264"

        const val BITRATE_VIDEO_10M = "10M"
        const val BITRATE_VIDEO_6M = "6M"
        const val BITRATE_VIDEO_4M = "4M"
        const val BITRATE_VIDEO_3M = "3M"
        const val BITRATE_VIDEO_2M = "2M"
        const val BITRATE_VIDEO_1500K = "1.5M"

        const val FRAME_RATE_30 = "30"
        const val FRAME_RATE_60 = "60"

        const val RESOLUTION_720 = "1280"
        const val RESOLUTION_1080 = "1920"

        // https://trac.ffmpeg.org/wiki/Encode/HighQualityAudio
        const val CODEC_COPY = "copy"
        const val CODEC_AUDIO_AAC = "aac"
        const val CODEC_AUDIO_MP3 = "libmp3lame"

        const val BITRATE_AUDIO_128K = "128k"
        const val BITRATE_AUDIO_160K = "160k"
        const val BITRATE_AUDIO_192K = "192k"
    }

    enum class Preset {
        ULTRA_FAST, SUPER_FAST, VERY_FAST, FASTER, FAST, MEDIUM, SLOW, SLOWER, VERY_SLOW;

        override fun toString(): String {
            return name.lowercase(Locale.getDefault()).replace("_","")
        }
    }

    enum class Verbosity {
        QUIET, PANIC, FATAL, ERROR, WARNING, INFO, VERBOSE, DEBUG;

        override fun toString(): String {
            return name.lowercase(Locale.getDefault())
        }
    }
    fun build(): String {
        val args = mutableListOf<String>()

        args.addAll(listOf("-i",input))

        args.add(if(overwriteOutput) "-y" else "-n")

        args.addAll(listOf("-c:v",videoCodec))
        if (videoCodec!= CODEC_COPY) {
            args.addAll(listOf("-b:v", videoBitrate))

            val filters = mutableListOf<String>()
            videoResolution.let {
                filters.add("scale=$it:$it:force_original_aspect_ratio=decrease")
            }
            videoFrameRate?.let {
                filters.add("fps=$it")
            }
            if (filters.isNotEmpty()) {
                args.addAll(listOf("-vf", filters.joinToString(",")))
            }
            videoCRF?.let { crf ->
                require(crf in 0..51) { "CRF value should be in the range 0..51" }
                args.addAll(listOf("-crf", crf.toString()))
            }
        }

        args.addAll(listOf("-c:a",audioCodec))
        if (audioCodec!= CODEC_COPY) {
            args.addAll(listOf("-b:a", audioBitrate))
        }
        if (enableFastStart) {
            args.add("-movflags +faststart")
        }

        args.addAll(listOf("-preset",preset.toString()))

//        args.add("-stats_period 2")

        args.addAll(listOf("-v",verbosity.toString()))

        args.add(output)

        return args.joinToString(" ")
    }
}