package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class SearchType {
    @SerializedName("exact_name_match") EXACT_MATCH,
    @SerializedName("contain_name_match") CONTAINS;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    companion object {
        fun fromString(value: String): SearchType {
            return when (value) {
                EXACT_MATCH.name -> EXACT_MATCH
                else -> CONTAINS
            }
        }
    }
}