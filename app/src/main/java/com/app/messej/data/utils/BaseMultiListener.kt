package com.app.messej.data.utils

import java.util.Collections
import java.util.concurrent.ConcurrentHashMap

open class
BaseMultiListener<LISTENER_CLASS> {
    private val mListeners = Collections.newSetFromMap(
        ConcurrentHashMap<LISTENER_CLASS, Boolean>(1)
    )

    fun registerListener(listener: LISTENER_CLASS) {
        mListeners.add(listener)
    }

    fun unregisterListener(listener: LISTENER_CLASS) {
        mListeners.remove(listener)
    }

    fun removeAllListeners() {
        mListeners.clear()
    }

    protected val listeners: Set<LISTENER_CLASS>
        get() = Collections.unmodifiableSet(mListeners)
}