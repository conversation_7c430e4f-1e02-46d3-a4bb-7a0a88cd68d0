package com.app.messej.data.model.api.settings

import com.google.gson.annotations.SerializedName

data class PodiumPrivacyResponse(
    @SerializedName("exclude_live_friends") var excludeLiveFriends: Boolean?=null,
    @SerializedName("join_hidden") var joinHidden: Boolean?=null,
    @SerializedName("receive_challenge_invitation") var receiveChallengeInvitation: Boolean?=null,
    @SerializedName("receive_challenge_start_notification") var receiveChallengeStartNotification: Boolean?=null,

)
