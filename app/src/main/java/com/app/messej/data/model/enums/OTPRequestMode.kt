package com.app.messej.data.model.enums

import com.app.messej.data.model.enums.OTPRequestMode.REGISTER_EMAIL
import com.app.messej.data.model.enums.OTPRequestMode.REGISTER_MOBILE
import com.app.messej.data.model.enums.OTPRequestMode.RESET_EMAIL
import com.app.messej.data.model.enums.OTPRequestMode.RESET_MOBILE
import com.app.messej.data.model.enums.OTPRequestMode.VERIFY_EMAIL
import com.app.messej.data.model.enums.OTPRequestMode.VERIFY_MOBILE


/**
 * @property REGISTER_MOBILE: when coming as part of mobile  registration flow
 * @property REGISTER_EMAIL: when coming as part of email registration flow
 * @property RESET_MOBILE: when coming from the forgot password page
 * @property RESET_EMAIL: when coming from the forgot password page
 * @property VERIFY_EMAIL: when coming from the change email option in profile page
 * @property VERIFY_MOBILE: when coming from the change mobile number option in profile page
 */
enum class OTPRequestMode {
    REGISTER_MOBILE,
    REGIS<PERSON>R_EMAIL,
    RESET_MOBILE,
    RESET_EMAIL,
    VERIFY_EMAIL,
    VERIFY_MOBILE;
    val isEmailType: Boolean
    get() {
        return this==RESET_EMAIL||this==VERIFY_EMAIL||this==REGISTER_EMAIL
    }
}