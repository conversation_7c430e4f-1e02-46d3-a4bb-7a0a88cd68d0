package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.auth.GoogleLoginRequest
import com.app.messej.data.model.api.auth.LoginRequest
import com.app.messej.data.model.api.auth.LoginResponse
import com.app.messej.data.model.api.auth.PhoneAvailabilityResponse
import com.app.messej.data.model.api.auth.RefreshTokenResponse
import com.app.messej.data.model.api.auth.ResetEmailRequest
import com.app.messej.data.model.api.auth.VerifyOTPRequest
import com.app.messej.data.model.api.auth.VerifyOTPResponse
import com.app.messej.data.model.api.auth.VerifyRequest
import com.app.messej.data.model.api.auth.VerifyResponse
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Query

interface NoAuthAPIService {

    @POST("/user/login")
    @Headers("Accept: application/json")
    suspend fun login(@Body request: LoginRequest): Response<APIResponse<LoginResponse>>

    @POST("/user/login/google")
    @Headers("Accept: application/json")
    suspend fun loginGoogle(@Body request: GoogleLoginRequest): Response<APIResponse<LoginResponse>>


    @POST("/user/token")
    @Headers("Accept: application/json")
    fun refreshToken(@Header("Authorization") refresh: String): Call<APIResponse<RefreshTokenResponse>>

    @GET("/user/availability/phone")
    @Headers("Accept: application/json")
    suspend fun checkPhoneAvailability(@Query("phone") phoneNumber: String,
                                       @Query("country_code") encodedCountryCode: String,
                                       @Query("is_edit") isEdit: Boolean): Response<APIResponse<PhoneAvailabilityResponse>>

    @GET("/user/availability/email")
    @Headers("Accept: application/json")
    suspend fun checkEmailAvailability(@Query("email") email: String,
                                       @Query("is_edit") isEdit: Boolean): Response<APIResponse<PhoneAvailabilityResponse>>

    @POST("/user/register/phone")
    @Headers("Accept: application/json")
    suspend fun verifyPhone(@Body request: VerifyRequest): Response<APIResponse<VerifyResponse>>

    @POST("/user/register/email")
    @Headers("Accept: application/json")
    suspend fun verifyEmail(@Body request: VerifyRequest): Response<APIResponse<VerifyResponse>>

    @POST("/user/verify/phone")
    @Headers("Accept: application/json")
    suspend fun verifyOTP(@Body request: VerifyOTPRequest): Response<APIResponse<VerifyOTPResponse>>
    @POST("/user/verify/email")
    @Headers("Accept: application/json")
    suspend fun verifyEmailOTP(@Body request: VerifyOTPRequest): Response<APIResponse<VerifyOTPResponse>>

    @POST("/user/password/verify")
    @Headers("Accept: application/json")
    suspend fun verifyPasswordResetOTP(@Body request: VerifyOTPRequest): Response<APIResponse<VerifyOTPResponse>>

    @POST("/user/password/forgot")
    @Headers("Accept: application/json")
    suspend fun resetPasswordSendOTP(@Body request: ResetEmailRequest): Response<APIResponse<VerifyResponse>>

}
