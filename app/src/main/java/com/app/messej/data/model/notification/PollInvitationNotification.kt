package com.app.messej.data.model.notification


import com.google.gson.annotations.SerializedName

data class PollInvitationNotification(
    @SerializedName("category") val category: String,
    @SerializedName("html_text") val htmlText: String,
    @SerializedName("huddle_id") val huddleId: Int,
    @SerializedName("message") val message: String,
    @SerializedName("notification_id") val notificationId: Int,
    @SerializedName("notification_type") val notificationType: String,
    @SerializedName("private") val `private`: Any,
    @SerializedName("receiver_id") val receiverId: Int,
    @SerializedName("sender_id") val senderId: Int,
    @SerializedName("thumbnail") val thumbnail: String,
)