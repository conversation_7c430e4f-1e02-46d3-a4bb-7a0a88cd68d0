package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.BusinessAPIService
import com.app.messej.data.model.entity.DealsTransferHistory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1
class TransactionHistoryRemoteDataSource (private val api: BusinessAPIService, private val searchKeyWord: String?,private val transferFilter:String) : PagingSource<Int, DealsTransferHistory>() {
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, DealsTransferHistory> {
        Log.d("TransactionResponse", "Start")

        return try {
            withContext(Dispatchers.IO) {

                val currentPage = params.key ?: STARTING_KEY

                val response = api.getTransferHistory(searchKeyWord.toString(), page = currentPage, 50,transferFilter)
                Log.d("TransactionResponse", response.body()?.result?.transfers.toString())
                val responseData = mutableListOf<DealsTransferHistory>()
                val result = response.body()?.result
                val data = result?.transfers ?: emptyList()
                responseData.addAll(data)
                val nextKey = if (result?.transfers?.isEmpty() == true) {
                    null
                } else currentPage.plus(1)

                LoadResult.Page(
                    data = result!!.transfers, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            Log.d("TransactionResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, DealsTransferHistory>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}