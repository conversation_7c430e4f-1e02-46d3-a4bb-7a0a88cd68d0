package com.app.messej.data.model.api.podium.challenges

enum class ChallengePlayer {
    PLAYER1, PLAYER2;

    val otherPlayer: ChallengePlayer
        get() = when (this) {
            PLAYER1 -> PLAYER2
            PLAYER2 -> PLAYER1
        }

    val tokenNumber: Int
        get() = when (this) {
            PLAYER1 -> 1
            PLAYER2 -> 2
        }

    val slotState: SlotState
        get() = when (this) {
            PLAYER1 -> SlotState.PLAYER1
            PLAYER2 -> SlotState.PLAYER2
        }

    companion object {
        fun fromToken(token: Int): ChallengePlayer? = when (token) {
            1 -> PLAYER1
            2 -> PLAYER2
            else -> null
        }
    }
}