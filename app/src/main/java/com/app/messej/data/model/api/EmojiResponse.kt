package com.app.messej.data.model.api


import com.app.messej.data.model.entity.Sticker
import com.google.gson.annotations.SerializedName

data class EmojiResponse(
    @SerializedName("current_page")
    val currentPage: String? = "",
    @SerializedName("next_page")
    val nextPage: Boolean? = false,
    @SerializedName("stickers")
    val stickers: List<Sticker> = listOf(),
    @SerializedName("total")
    val total: Int? = 0
)