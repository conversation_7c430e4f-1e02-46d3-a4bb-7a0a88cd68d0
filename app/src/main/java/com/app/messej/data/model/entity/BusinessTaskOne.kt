package com.app.messej.data.model.entity


import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.model.api.auth.HomeAddress
import com.app.messej.data.model.enums.UserBadge
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(tableName = EntityDescriptions.TABLE_BUSINESS_TASK_ONE )
data class BusinessTaskOne(
    @SerializedName("about")
    val about: String? = null,
    @SerializedName("dob")
    val dob: String? = null,
    @SerializedName("email")
    val email: String? = null,
    @SerializedName("email_verified")
    val emailVerified: Boolean? = null,
    @SerializedName("gender")
    val gender: String? = null,
    @PrimaryKey
    @SerializedName("id")
    val id: Int = 0,
    @SerializedName("membership")
    val membership: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("paypal_id")
    val paypalId: String? = null,
    @SerializedName("paypal_verified")
    val paypalVerified: Boolean? = null,
    @SerializedName("phone")
    val phone: String? = null,
    @SerializedName("profile_complete_percentage")
    val profileCompletePercentage: Int = 0,
    @SerializedName("profile_photo")
    val profilePhoto: String? =null,
    @SerializedName("username")
    val username: String? = null,
    @SerializedName("verified")
    val verified: Boolean? = null,
    @SerializedName("total_payouts_processed")
    val totalPayoutsProcessed:Int?=null,
    @SerializedName("home_address")
    @Embedded(prefix="home_")
    val homeAddress:HomeAddress? =null
){
    val userBadge: UserBadge
        get() {
            return if(verified == true) UserBadge.VERIFIED
            else if(membership=="Premium") UserBadge.PREMIUM
            else UserBadge.NONE
        }
}