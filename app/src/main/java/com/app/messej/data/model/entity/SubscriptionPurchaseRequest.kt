package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.model.api.subscription.TransactionReceipt
import com.app.messej.data.model.enums.PurchaseItem
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(tableName = EntityDescriptions.TABLE_SUBSCRIPTION_DETAILS)
data class SubscriptionPurchaseRequest (
    @SerializedName("currency") @ColumnInfo("currency") val currency: String,
    @Embedded(prefix = "payload_")
    @SerializedName("payload")  val payload: Payload,
    @SerializedName("payment_mode") @ColumnInfo("payment_mode") val payment_mode: String,
    @SerializedName("price") @ColumnInfo("price") val price: Long,
    @SerializedName("restoring_purchases") @ColumnInfo("restoring_purchases") val restoring_purchases: Boolean,
    @SerializedName("timezone") @ColumnInfo("timezone") val timezone: String,
    @SerializedName("type") @ColumnInfo("type")val type: String,
    @ColumnInfo(name = "id") @PrimaryKey(autoGenerate = true) var id: Int? = null,
    @SerializedName("userid") @ColumnInfo("userid") val userid: Int,
    @SerializedName("flax") @ColumnInfo("flax") val flaxRate: Int? = null,
    @SerializedName("value") @ColumnInfo("coins") val coins: Int? = null,
    @SerializedName("additionalColumn") @ColumnInfo("additionalColumn") val additionalColumn: String? = null,
    @SerializedName("purchase_type") @ColumnInfo("purchase_type") val purchase_type: PurchaseItem? = null,
){
    @Entity
    data class Payload(
        @SerializedName("developerPayloadAndroid") @ColumnInfo("developerPayloadAndroid") val developerPayloadAndroid: String? = "",
        @SerializedName("isAcknowledgedAndroid") @ColumnInfo("isAcknowledgedAndroid") val isAcknowledgedAndroid: Boolean? = false,
        @SerializedName("obfuscatedAccountIdAndroid") @ColumnInfo("obfuscatedAccountIdAndroid") val obfuscatedAccountIdAndroid: String? = "",
        @SerializedName("obfuscatedProfileIdAndroid") @ColumnInfo("obfuscatedProfileIdAndroid") val obfuscatedProfileIdAndroid: String? = "",
        @SerializedName("orderId") @ColumnInfo("orderId") val orderId: String? = "",
        @SerializedName("packageNameAndroid") @ColumnInfo("packageNameAndroid") val packageNameAndroid: String? = "",
        @SerializedName("productId") @ColumnInfo("productId") val productId: String? = "",
        @SerializedName("purchaseStateAndroid") @ColumnInfo("purchaseStateAndroid") val purchaseStateAndroid: Int? = 0,
        @SerializedName("purchaseToken") @ColumnInfo("purchaseToken") val purchaseToken: String? = "",
        @SerializedName("signatureAndroid") @ColumnInfo("signatureAndroid") val signatureAndroid: String? = "",
        @SerializedName("transactionDate") @ColumnInfo("transactionDate") val transactionDate: Long? = 0,
        @SerializedName("transactionId") @ColumnInfo("transactionId") val transactionId: String? = "",
        @Embedded(prefix = "receipt_") @SerializedName("transactionReceipt") val transactionReceipt: TransactionReceipt? = TransactionReceipt(),
    )
}
