package com.app.messej.data.model

import androidx.room.TypeConverter
import com.app.messej.data.model.enums.GiftType
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class ActivityMeta(
    @SerializedName("activity_by"       ) val activityBy: String,
    @SerializedName("activity_type"     ) val activityType: String,
    @SerializedName("activity_user_type") val activityUserType: String,
    @SerializedName("args"              ) val args: String,
    @SerializedName("gift_name"         )  val giftName: String? = null,
    @SerializedName("gift_coin_value"   )  val _giftCoinValue: Double? = null,
    @SerializedName("gift_sender_name"  ) val giftSenderName: String? = null,
    @SerializedName("gift_type"         )  val giftType: GiftType? = null,
    @SerializedName("gift_description"  ) val giftDescription: String? = null,
    @SerializedName("gift_description_arabic") val giftDescriptionArabic:String?=null,
    @SerializedName("gift_animation_url_android")  val giftAnimationUrlAndroid:String?=null,
    @SerializedName("gift_animation_url_ios") val giftAnimationUrlIos:String?=null,
    @SerializedName("gift_id"          )  val giftId:Int?=null,
    @SerializedName("gift_name_arabic"  )  val giftNameArabic:String?=null,
    @SerializedName("gift_gif_url")  val giftGifUrl:String?=null,
    ) {
    val giftCoinValue: String
        get() = _giftCoinValue?.toString()?: "0"

    class Converter {
        @TypeConverter
        fun decode(data: String?): ActivityMeta? {
            data?: return null
            val type: Type = object : TypeToken<ActivityMeta?>() {}.type
            return Gson().fromJson<ActivityMeta>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: ActivityMeta?): String? {
            return Gson().toJson(someObjects)
        }
    }
}