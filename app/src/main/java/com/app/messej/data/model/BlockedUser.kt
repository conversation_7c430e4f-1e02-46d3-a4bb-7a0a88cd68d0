package com.app.messej.data.model

import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName

data class BlockedUser(
  @SerializedName("citizenship")  val citizenship: UserCitizenship?=null,
  @SerializedName("country_code") val countryCode: String?=null,
  @SerializedName("country_name") val countryName: String?=null,
  @SerializedName("flash_block" )  val flashBlock: Boolean?=null,
  @SerializedName("huddle_posts_block")  val huddlePostsBlock: Boolean?=null,
  @SerializedName("id")  val id: Int?=null,
  @SerializedName("is_admin")  val isAdmin: Boolean?=null,
  @SerializedName("is_premium")   val isPremium: Boolean?=null,
  @SerializedName("name")  val name: String?=null,
  @SerializedName("podium_block")  val podiumBlock: Boolean?=null,
  @SerializedName("podium_speaking_block")  val podiumSpeakingBlock: Boolean?=null,
  @SerializedName("podium_write_comments_block")  val podiumWriteCommentsBlock: Boolean?=null,
  @SerializedName("postat_posts_block")  val postatPostsBlock: Boolean?=null,
  @SerializedName("profile_url")  val profileUrl: String?=null,
  @SerializedName("thumbnail")  val thumbnail: String?=null,
  @SerializedName("time_created")   val timeCreated: String?=null,
  @SerializedName("time_updated")   val timeUpdated: String?=null,
  @SerializedName("user_blocked")  val userBlocked: Boolean?=null,
  @SerializedName("user_id")   val userId: Int?=null,
  @SerializedName("username")   val username: String?=null,
  @SerializedName("verified")   val verified: Boolean?=null,
)