package com.app.messej.data.model.api.postat

import com.app.messej.data.model.entity.Postat
import com.google.gson.annotations.SerializedName

data class CreatePostatRequest(
    @SerializedName("message"           ) val message         : String?          = null,
    @SerializedName("type"              ) val type            : Postat.PostatType,
    @SerializedName("message_id"        ) val messageId       : String,
    @SerializedName("has_mention"       ) val hasMention      : Boolean?         = false,
    @SerializedName("mentioned_users"   ) val mentionedUsers  : List<Int>? = listOf(),
    @SerializedName("color"             ) val color           : String?          = null,
    @SerializedName("created"           ) val created         : String,
    @SerializedName("temp_id"           ) val tempId          : String?          = null,
    @SerializedName("is_original_audio" ) val isOriginalAudio : Boolean,
    @SerializedName("music_data"        ) val musicData       : MusicData?       = MusicData(),
    @SerializedName("turn_off_comments" ) val turnOffComments : Boolean?         = null,
    @SerializedName("media"             ) val media           : List<PostatMedia>? = listOf()
){
    companion object {
        fun from(postat: Postat) : CreatePostatRequest {
            return CreatePostatRequest(
                messageId = postat.messageId,
                type = postat.type,
                message = postat.message,
                hasMention = postat.hasMention,
                mentionedUsers = postat.mentionedUsers,
                color = postat.color,
                created = postat.created,
                tempId = postat.messageId,
                isOriginalAudio = postat.isOriginalAudio,
                musicData = postat.musicData,
                turnOffComments = postat.turnOffComments,
                media = postat.media
            )
        }
    }
}
