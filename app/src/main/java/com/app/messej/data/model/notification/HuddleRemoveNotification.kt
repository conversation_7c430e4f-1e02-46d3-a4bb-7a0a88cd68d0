package com.app.messej.data.model.notification

import com.google.gson.annotations.SerializedName

data class HuddleRemoveNotification(
    @SerializedName("thumbnail"         ) var thumbnail        : String? = null,
    @SerializedName("private"           ) var private          : <PERSON><PERSON>an,
    @SerializedName("message"           ) var message          : String  = "",
    @SerializedName("html_text"         ) var messageHtml      : String,
    @SerializedName("notification_id"   ) var notificationId   : Int    = 0,
    @SerializedName("category"          ) var category         : String? = null,
    @SerializedName("sender_id"         ) var senderId         : Int,
    @SerializedName("huddle_id"         ) var huddleId         : Int
)