package com.app.messej.data.model.socket

import com.google.gson.JsonArray
import com.google.gson.annotations.SerializedName

data class SuperStarHideEvent(
    @SerializedName("subscriptionArn" ) val subscriptionArn : JsonArray,
    @SerializedName("muted"           ) val muted           : Boolean,
    @SerializedName("soundTrack"      ) val soundTrack      : SoundTrack?       = SoundTrack(),
    @SerializedName("reported"        ) val reported        : Boolean?          = null,
    @SerializedName("userType"        ) val userType        : String?           = null,
    @SerializedName("created"         ) val created         : String?           = null,
    @SerializedName("broadcastType"   ) val broadcastType   : String?           = null,
    @SerializedName("subscriberId"    ) val subscriberId    : Int?              = null,
    @SerializedName("preview"         ) val preview         : Boolean?          = null,
    @SerializedName("notification"    ) val notification    : String?           = null,
    @SerializedName("updated"         ) val updated         : String?           = null,
    @SerializedName("broadcasterId"   ) val broadcasterId   : Int,
    @SerializedName("hide"            ) val hide            : Boolean
) {
    data class SoundTrack (

        @SerializedName("ios"         ) val ios         : String? = null,
        @SerializedName("android"     ) val android     : String? = null,
        @SerializedName("muteChannel" ) val muteChannel : String? = null

    )
}
