package com.app.messej.data.model.entity

import androidx.room.Embedded
import androidx.room.Relation
import com.app.messej.data.model.AbstractChatMessageWithMedia

data class BroadcastChatMessageWithMedia(
    @Embedded
    override val message: BroadcastMessage,
    @Relation(
        parentColumn = BroadcastMessage.COLUMN_MESSAGE_ID,
        entityColumn = OfflineMedia.COLUMN_MESSAGE_ID
    )
    override var offlineMedia: OfflineMedia? = null
): AbstractChatMessageWithMedia() {

}