package com.app.messej.data.model.notification

import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.enums.BroadcastMode
import com.google.gson.annotations.SerializedName

data class BroadcastNotification(
    @SerializedName("broadcaster_id"    ) var broadcasterId    : Int,
    @SerializedName("broadcast_type"    ) var type             : BroadcastMode,
    @SerializedName("name"              ) var name             : String,
    @SerializedName("message"           ) var message          : String = "",
    @SerializedName("notification_id"   ) var notificationId   : Int    = 0,
    @SerializedName("media"             ) var media            : String? = null,
    @SerializedName("media_meta"        ) var mediaMeta        : MediaMeta? = null
)