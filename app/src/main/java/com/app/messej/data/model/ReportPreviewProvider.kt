package com.app.messej.data.model

import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType

interface ReportPreviewProvider {
    val title: String?
    val message: String?
    val mediaMeta: MediaMeta?
    val contentType: ReportContentType?
    val reportType: ReportType

    val user: AbstractUser?

    val countryFlag: Int?

    val edited: Boolean
    val deleted: Boolean
}