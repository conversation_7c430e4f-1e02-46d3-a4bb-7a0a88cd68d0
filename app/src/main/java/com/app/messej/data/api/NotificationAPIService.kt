package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.NotificationResponse
import com.app.messej.data.model.api.NotificationUpdateRequest
import com.app.messej.data.model.api.ReadNotificationResponse
import com.app.messej.data.model.entity.Notification
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface NotificationAPIService {

    @GET("/user/notifications")
    @Headers("Accept: application/json")
    suspend fun getNotifications(@Query("page") page: Int,@Query("tz") timeZone: String,@Query("date") date:String): Response<APIResponse<NotificationResponse>>

    @PUT("/user/notifications/{id}")
    @Headers("Accept: application/json")
    suspend fun updateNotification(@Body request: NotificationUpdateRequest, @Path("id") id: Int): Response<APIResponse<Notification>>

    @DELETE("/user/notifications/{id}")
    @Headers("Accept: application/json")
    suspend fun deleteNotification(@Path("id") id: Int): Response<APIResponse<Unit>>

    @GET("/user/notifications/{id}")
    @Headers("Accept: application/json")
    suspend fun readNotification(@Path("id") id: Int,@Query("date") date:String): Response<APIResponse<ReadNotificationResponse>>


}