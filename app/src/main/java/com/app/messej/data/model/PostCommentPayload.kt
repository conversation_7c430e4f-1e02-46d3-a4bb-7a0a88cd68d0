package com.app.messej.data.model

import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.socket.AbstractChatMessagePayload
import com.google.gson.annotations.SerializedName

data class PostCommentPayload(
    @SerializedName("message") override val message: String?
    ): AbstractChatMessagePayload() {
    override fun setMedia(meta: MediaMeta) {
        super.setMedia(meta)
        media = meta.thumbnail
    }

    override val color: ChatTextColor?
        get() = null
}