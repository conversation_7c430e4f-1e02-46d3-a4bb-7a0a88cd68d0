package com.app.messej.data.socket.repository

import android.util.Log
import com.app.messej.MainApplication
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.socket.MaidanSupportRequestPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.runBlocking
import org.json.JSONObject

object ChallengeEventRepository: BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {

    private val accountRepo = AccountRepository(MainApplication.applicationInstance())
    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when (event) {
            ChatSocketEvent.RX_CHALLENGE_CONTRIBUTOR_APPOINT -> onChallengeContributorRequest(data)
            ChatSocketEvent.RX_MAIDAN_SUPPORTER_INVITE -> onMaidanSupportRequest(data)
            ChatSocketEvent.RX_MAIDAN_COUNT_UPDATE -> onMaidanCountUpdate(data)
            else -> return false
        }
        return true
    }

    val user: CurrentUser get() = accountRepo.user

    //External contributor request for a Challenge
    private val _externalContributorRequestFLow: MutableSharedFlow<PodiumChallenge> = MutableSharedFlow()
    val externalContributorRequestFLow: SharedFlow<PodiumChallenge> = _externalContributorRequestFLow

    private fun onChallengeContributorRequest(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<PodiumChallenge>(data.toString())
            if (result.contributors.any { it.id == user.id }) {
                _externalContributorRequestFLow.emit(result)
            } else { }
        } catch (e: Exception) {
            Log.e("PCER", "onChallengeContributorRequest: ", e)
        }
    }

    //Invite to support in Maidan challenge
    private val _maidanSupportRequestFLow: MutableSharedFlow<MaidanSupportRequestPayload> = MutableSharedFlow()
    val maidanSupportRequestFLow: SharedFlow<MaidanSupportRequestPayload> = _maidanSupportRequestFLow

    private fun onMaidanSupportRequest(data: JSONObject) = runBlocking {
        try {
            val result = Gson().fromJson<MaidanSupportRequestPayload>(data.toString())
            _maidanSupportRequestFLow.emit(result)
        } catch (e: Exception) {
            Log.e("PCER", "onChallengeContributorRequest: ", e)
        }
    }

    private fun onMaidanCountUpdate(data: JSONObject) = runBlocking {
        try {
            data.getInt("maidan_count").let {
                FlashatDatastore().updateUnreadCounts { uc ->
                    return@updateUnreadCounts uc.copy(maidanCount = it)
                }
            }
        } catch (e: Exception) {
            Log.e("PCER", "onChallengeContributorRequest: ", e)
        }

    }
}