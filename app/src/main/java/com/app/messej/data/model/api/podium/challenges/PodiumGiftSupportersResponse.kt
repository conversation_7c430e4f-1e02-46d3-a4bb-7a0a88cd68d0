package com.app.messej.data.model.api.podium.challenges

import com.app.messej.data.model.enums.UserBadge
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class PodiumGiftSupportersResponse(
    @SerializedName("total_items") val totalItems: Int?,
    @SerializedName("total_pages") val totalTotalPages: Int?,
    @SerializedName("current_page") val currentPage: Int,
    @SerializedName("per_page") val perPage: Int,
    @SerializedName("has_next") val hasNext: Boolean,
    @SerializedName("supporters") val supporters: ArrayList<ChallengeSupporter> = arrayListOf(),
) {
    data class ChallengeSupporter(
        @SerializedName("id") val id: Int,
        @SerializedName("name") val name: String,
        @SerializedName("thumbnail") val thumbnail: String? = null,
        @SerializedName("coins_spent") val coinsSpent: Double = 0.0,
        @SerializedName("membership" )  val membership  : UserType,
        @SerializedName("verified"   )  val verified    : Boolean,
        ) {
        val premiumUser: Boolean
            get() {
                return membership == UserType.PREMIUM
            }

        val userBadge: UserBadge
            get() {
                return if(verified) UserBadge.VERIFIED
                else if(premiumUser) UserBadge.PREMIUM
                else UserBadge.NONE
            }

        val coinsSpentFormatted: String
            get() {
                return coinsSpent.toInt().toString()
            }
    }
}
