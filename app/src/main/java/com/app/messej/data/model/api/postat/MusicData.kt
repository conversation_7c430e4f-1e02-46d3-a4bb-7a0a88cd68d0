package com.app.messej.data.model.api.postat

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class MusicData (
    @SerializedName("media_id"   ) val mediaId   : String? = null,
    @SerializedName("media_name" ) val mediaName : String? = null,
    @SerializedName("media_url"  ) val mediaUrl  : String? = null,

    @SerializedName("composer_name" ) var composer : String? = null,
    @SerializedName("singers_name"  ) var singers  : List<String>? = listOf(),
) {

    companion object {
        fun from(music: MusicFile?): MusicData? {
            music?: return null
            return MusicData(
                mediaId = music.mediaMeta.musicId,
                mediaName = music.songName,
                mediaUrl = music.mediaMeta.mediaUrl
            )
        }
    }
    class Converter {
        @TypeConverter
        fun decode(data: String?): MusicData? {
            data?: return null
            val type: Type = object : TypeToken<MusicData?>() {}.type
            return Gson().fromJson<MusicData>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: MusicData?): String? {
            return Gson().toJson(someObjects)
        }
    }

    val valid: Boolean
        get() = !mediaUrl.isNullOrBlank()

    val singersAsString: String
        get() = singers.orEmpty().joinToString(", ", limit = 5)
}