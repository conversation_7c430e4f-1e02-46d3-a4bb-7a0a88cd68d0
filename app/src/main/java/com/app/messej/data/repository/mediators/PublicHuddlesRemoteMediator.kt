package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.enums.HuddleInvolvement
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class PublicHuddlesRemoteMediator(
    private val query: String,
    private val type: HuddleType,
    private val involvement: HuddleInvolvement,
    private val database: FlashatDatabase,
    private val networkService: ChatAPIService,
    private val currentUser: CurrentUser
) : RemoteMediator<Int, PublicHuddle>() {
    val dao = database.getHuddleDao()
    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = "${EntityDescriptions.TABLE_HUDDLES}-${type.name}-${involvement.name}"

    override suspend fun initialize(): InitializeAction {
        return super.initialize()
    }

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, PublicHuddle>
    ): MediatorResult {
        return try {
            val page = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    Log.d("PHRM", "load: APPEND")
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }
                    // You must explicitly check if the page key is null when
                    // appending, since null is only valid for initial load.
                    // If you receive null for APPEND, that means you have
                    // reached the end of pagination and there are no more
                    // items to load.
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPageInt
                }
            }

            Log.d("PHRM", "load: loading page $page")

            // Suspending network load via Retrofit. This doesn't need to be
            // wrapped in a withContext(Dispatcher.IO) { ... } block since
            // Retrofit's Coroutine CallAdapter dispatches on a worker
            // thread.
            val response = networkService.getPublicHuddles(tab = involvement.serializedName(), page = page, type = type)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }
            val deleted = result.deletedHuddleIds
            val huddles = result.huddles
            huddles.forEach {
                it.huddleType = type
                it.involvement = involvement
                if (it.lastMessage?.sender==currentUser.id) {
                    it.unreadCount = 0
                }
                if (it.hasLastMessage) it.lastMessage?.let { message ->
                    if (message.deleted && message.remover != null){
                        if (message.remover.id?.toInt() == currentUser.id) message.displayMessage = message.remover.message
                    }
                }
            }

            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    if (type==HuddleType.PUBLIC) dao.deleteAllHuddles(type, involvement) else dao.deleteAllHuddles(type)
                }

                // Update RemoteKey for this query.
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, result.nextPage.toString())
                )

                // Insert new users into database, which invalidates the
                // current PagingData, allowing Paging to present the updates
                // in the DB.
                deleted.forEach {
                    if(dao.deleteHuddle(it)>0) {
                        database.getChatMessageDao().deleteAllHuddleReportedComments(it)
                        database.getChatMessageDao().deleteAllHuddleReportedMessages(it)
                        database.getChatMessageDao().deleteHuddleComments(it)
                    }
                    database.getChatMessageDao().deleteAllHuddleChatMessages(HuddleChatMessage.prefixHuddleId(it))
                }
                dao.insertHuddles(huddles)
            }

            MediatorResult.Success(endOfPaginationReached = result.nextPage == null)
        } catch (e: Exception) {
            Log.e("PHRM", "load: error",e )
            MediatorResult.Error(e)
        }
    }
}