package com.app.messej.data.model.api.huddles

import com.google.gson.annotations.SerializedName

data class CreateHuddleRequest(
    @SerializedName("creation_key") val uuid: String,
    @SerializedName("name") val name: String,
    @SerializedName("about") val about: String?,
    @SerializedName("category_id") val categoryId: Int?,
    @SerializedName("request_to_join") val requestToJoin: <PERSON><PERSON><PERSON>,
    @SerializedName("private") val isPrivate: <PERSON><PERSON><PERSON>,
    @SerializedName("huddle_language") val language:String
)
