package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class ChatDeleteEvent(
    @SerializedName("message_id") val messageId: String,
    @SerializedName("message") val message: String? = null,
    @SerializedName("deleted")  val deleted: <PERSON><PERSON><PERSON>,
    @SerializedName("delete_for_all") val deleteForAll: <PERSON><PERSON><PERSON>,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("remover") val remover: Remover? = null
): SocketEventPayload() {
    data class Remover(
        @SerializedName("id") val id: String,
        @SerializedName("message") val message: String? = null
    )
}
