package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.PostatAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.enums.PostatTab
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class PostatFeedRemoteMediator(
    private val type: PostatTab,
    private val database: FlashatDatabase,
    private val networkService: PostatAPIService,
) : RemoteMediator<Int, Postat.FeedPostat>(){
    val dao = database.getFeedPostatDao()
    private val remoteKeyDao = database.getRemotePagingDao()
    private val tableKey = "${EntityDescriptions.TABLE_POSTAT_FEED}-ofTab-${type.name}"

    override suspend fun load(loadType: LoadType, state: PagingState<Int, Postat.FeedPostat>): MediatorResult {
        return try {
            val page = when (loadType) {
                LoadType.REFRESH -> null
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    Log.d("PFRM", "load: APPEND ")
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }
                    // You must explicitly check if the page key is null when
                    // appending, since null is only valid for initial load.
                    // If you receive null for APPEND, that means you have
                    // reached the end of pagination and there are no more
                    // items to load.
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }
                    remoteKey.nextPageInt
                }
            }

            // Suspending network load via Retrofit. This doesn't need to be
            // wrapped in a withContext(Dispatcher.IO) { ... } block since
            // Retrofit's Coroutine CallAdapter dispatches on a worker
            // thread.
            Log.d("POSTAT", "load: loading page $page")
            val response = networkService.getFeedPostat(page = page, tab = type.serializedName())
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }
            Log.d("POSTAT", "load: loaded ${result.postatList.size} items | next: ${result.nextPage} | cur: ${result.currentPage}")
            val postat = result.postatList
            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    dao.deleteAllByTab(type)
                }

                // Update RemoteKey for this query.
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, (result.currentPage+1).toString())
                )

                postat.forEach {
                    it.senderDetails.id = it.userId
                }

                val feedPostat = postat.map {
                    Postat.FeedPostat(it).apply {
                        postatTab = type
                    }
                }
                dao.insert(feedPostat)
            }


            MediatorResult.Success(endOfPaginationReached = result.nextPage == null)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }

}