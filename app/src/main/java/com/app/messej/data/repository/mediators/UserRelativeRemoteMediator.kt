package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class UserRelativeRemoteMediator(
    private val relativeType: FollowerType,
    private val database: FlashatDatabase,
    private val networkService: ProfileAPIService,
    private val onCount: (Int) -> Unit = {}
) : RemoteMediator<Int, UserRelative>() {
    private val dao = database.getUserDao()
    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = "${EntityDescriptions.TABLE_STARS_LIST}-$relativeType"

    override suspend fun initialize(): InitializeAction {
        return super.initialize()
    }

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, UserRelative>
    ): MediatorResult {
        return try {
            val page = when (loadType) {
                LoadType.REFRESH -> 1
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    Log.d("URRM", "load: APPEND")
                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }
                    // You must explicitly check if the page key is null when
                    // appending, since null is only valid for initial load.
                    // If you receive null for APPEND, that means you have
                    // reached the end of pagination and there are no more
                    // items to load.
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPageInt
                }
            }

            Log.d("PHRM", "load: loading page $page")

            // Suspending network load via Retrofit. This doesn't need to be
            // wrapped in a withContext(Dispatcher.IO) { ... } block since
            // Retrofit's Coroutine CallAdapter dispatches on a worker
            // thread.
            val response = when(relativeType) {
                FollowerType.DEAR -> networkService.getDearsList(page)
                FollowerType.FAN -> networkService.getFansList(page)
                FollowerType.LIKER -> networkService.getLikersList(page)
            }
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            onCount.invoke(result.totalRecords?:0)

            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    dao.deleteAllUserRelatives(relativeType)
                }

                // Update RemoteKey for this query.
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, result.nextPage.toString())
                )
                val data = response.body()?.result?.list ?: emptyList()
                data.forEach { it.relativeType = relativeType }
                dao.insertUserRelative(data)
            }
            MediatorResult.Success(endOfPaginationReached = result.nextPage == null)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}