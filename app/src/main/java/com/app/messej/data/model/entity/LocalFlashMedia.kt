package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractFlashMedia
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.api.media.S3UploadMedia
import com.app.messej.data.room.EntityDescriptions

@Entity(
    tableName = EntityDescriptions.TABLE_FLASH_MEDIA
)
@TypeConverters(
    MediaUploadState.Converter::class,
    MediaMeta.Converter::class
)
data class LocalFlashMedia(
    @PrimaryKey(autoGenerate = false) @ColumnInfo(name = COLUMN_FLASH_ID)
    val flashId: String,
    override val path: String,
    val key: String,
    val meta: MediaMeta,
    @ColumnInfo(name = COLUMN_UPLOAD_STATE)
    var uploadState: MediaUploadState = MediaUploadState.None,
): AbstractFlashMedia() {

    companion object {
        const val COLUMN_FLASH_ID = "flashId"
        const val COLUMN_UPLOAD_STATE = "uploadState"
    }

    val s3UploadMedia: S3UploadMedia
        get() = S3UploadMedia(file, key)
}