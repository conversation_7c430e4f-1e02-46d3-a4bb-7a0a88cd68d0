package com.app.messej.data.model.api.business


import com.google.gson.annotations.SerializedName
import kotlin.math.roundToInt

data class PayoutEligibility(
    @SerializedName("eligibility") val eligibility: Boolean? = false,
    @SerializedName("social_payout_info") val socialPayOutInfo: PayOutInfo? = null,
    @SerializedName("normal_payout_info") val normalPayOutInfo: PayOutInfo? = null,
    @SerializedName("reason") val reason: String? = null,
//    @SerializedName("pp_value") val ppValue: Double? = 0.0,
    @SerializedName("flax_rate") val flaxRate: Double? = 0.0,
    @SerializedName("minimum_pp") val minimumPP: Double? = 0.0,
    @SerializedName("maximumPP") val maximumPP: Double? = 0.0,
    @SerializedName("processing_fee") val processingFee: Double? = 0.0,
    @SerializedName("transfer_fee") val transferFee: Double? = 0.0,
    @SerializedName("days_pending_next_payout") val daysPending: Int? = 0,
    @SerializedName("payout_data") val payoutData: PayoutData? = null,
    @SerializedName("mena_fees_percentage") val menaFeesPercentage: Double? = 0.0
){
    data class PayOutInfo(
        @SerializedName("pp_value") val ppValue: Double? = 0.0,
        @SerializedName("redeemable_amount") val redeemableAmount: Double? = 0.0
    )

    val netNormalPayout: Double?
        get() = normalPayOutInfo?.ppValue?.minus(processingFee?:0.0)

    val maxNormalPayoutFlaxValue: Double?
        get() = netNormalPayout?.times(flaxRate?:0.0)

    val flaxRateFormatted: Int?
        get() = flaxRate?.times(100)?.roundToInt()

    val processingFeeFormatted: Int?
        get() = flaxRate?.roundToInt()

    val flaxRatePercentage: Double?
        get() = flaxRate?.times(100)

}
