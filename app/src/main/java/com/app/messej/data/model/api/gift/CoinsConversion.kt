package com.app.messej.data.model.api.gift

import com.app.messej.data.model.enums.GiftConversionType
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

data class CoinsConversion(
   @SerializedName("action") val action: GiftConversionType?=null,
   @SerializedName("coins") val coins: Double?=null,
   @SerializedName("flax") val flax: Double?=null,
   @SerializedName("id") val id: Int?=null,
   @SerializedName("time_created") val timeCreated: String?=null,
   @SerializedName("time_updated") val timeUpdated: String?=null,
   @SerializedName("user_id") val userId: Int?=null
){
   val parsedCreatedTime: LocalDateTime?
      get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)?.run { DateTimeUtils.getLocalDateTime(this) }

   val getParsedTime: String
      get() = DateTimeUtils.format(parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")

//   val giftConversionType :GiftConversionType
//      get(){
//         return if (action=="p2f") GiftConversionType.COIN_TO_FLAX
//         else if(action=="f2p") GiftConversionType.FLAX_TO_COIN
//         else if(action=="Rewarded") GiftConversionType.COIN_REWARDED
//         else GiftConversionType.COIN_PURCHASE
//
//      }
}