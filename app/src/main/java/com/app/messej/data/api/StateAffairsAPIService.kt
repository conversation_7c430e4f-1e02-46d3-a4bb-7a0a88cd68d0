package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.stateAffair.StateAffairsCitizenshipUserDataResponse
import com.app.messej.data.model.api.stateAffair.StateAffairsTribeResponse
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.ui.home.publictab.authorities.stateAffairs.StateAffairsTotalUsers
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Query

interface StateAffairsAPIService {

    @GET("/user/state-affairs/strogest-tribes")
    @Headers("Accept: application/json")
    suspend fun getStateAffairsTribes(@Query("page") page: Int, @Query("page_count") limit: Int): Response<APIResponse<StateAffairsTribeResponse>>

    @GET("/user/state-affairs/citizenship-users")
    @Headers("Accept: application/json")
    suspend fun getStateAffairsCitizenshipUsers(@Query("citizenship") citizenship: UserCitizenship, @Query("page") page: Int, @Query("page_count") limit: Int): Response<APIResponse<StateAffairsCitizenshipUserDataResponse>>

    @GET("/user/state-affairs/skillful-flashaters")
    @Headers("Accept: application/json")
    suspend fun getStateAffairsSkillFullFlashaters(@Query("page") page: Int, @Query("page_count") limit: Int): Response<APIResponse<StateAffairsCitizenshipUserDataResponse>>

    @GET("/user/state-affairs/popular-flashaters")
    @Headers("Accept: application/json")
    suspend fun getStateAffairsPopularFlashaters(@Query("page") page: Int, @Query("page_count") limit: Int): Response<APIResponse<StateAffairsCitizenshipUserDataResponse>>

    @GET("/user/state-affairs/generous-flashaters")
    @Headers("Accept: application/json")
    suspend fun getStateAffairsGenerouslFlashaters(@Query("page") page: Int, @Query("page_count") limit: Int): Response<APIResponse<StateAffairsCitizenshipUserDataResponse>>

    @GET("/user/state-affairs/statistics")
    @Headers("Accept: application/json")
    suspend fun getStateAffairsStateStatistics(): Response<APIResponse<StateAffairsTotalUsers>>


}