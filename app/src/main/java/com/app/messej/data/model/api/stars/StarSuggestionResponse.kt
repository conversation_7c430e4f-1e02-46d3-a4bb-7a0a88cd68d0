package com.app.messej.data.model.api.stars

import com.app.messej.data.model.Star
import com.google.gson.annotations.SerializedName

data class StarSuggestionResponse(
    @SerializedName("stars"     ) var stars    : ArrayList<Star> = arrayListOf(),
    @SerializedName("user_type" ) var userType : String?          = null,
    @SerializedName("page"      ) var page     : Int?             = null
){

}
