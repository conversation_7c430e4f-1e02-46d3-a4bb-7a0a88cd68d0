package com.app.messej.data.model.api.legal

import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.enums.CaseVerdictAction
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.ReportCaseStatus
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.utils.MediaUtils
import com.google.gson.annotations.SerializedName
import kotlin.math.max

data class CaseDetails(
    @SerializedName("time_created") override val created: String?,
    @SerializedName("time_updated") override val updated: String?,

    @SerializedName("id") override val id: Int,

    @SerializedName("reason") override val reason: String,
    @SerializedName("reported_id") override val reportedId: String,
    @SerializedName("reported_type") override val reportedContentType: ReportContentType,
    @SerializedName("report_type") override val reportType: ReportType,
    @SerializedName("reporter_id") override val reporterId: Int,
    @SerializedName("user_id") override val userId: Int,
    @SerializedName("proof_files") val _proofFiles: List<String>?,

    @SerializedName("category_icon") override val categoryIcon: String?,
    @SerializedName("category") override val category: String,
    @SerializedName("category_id") override val categoryId: Int,
    @SerializedName("category_type_id") override val categoryTypeId: Int?,

    @SerializedName("media_meta") val mediaMetas: List<PostatMedia>?,
    @SerializedName("caption") val caption: String?,

    @SerializedName("violations_count") val violationsCount: Int,
    @SerializedName("status") override val caseStatus: ReportCaseStatus,

    @SerializedName("investigation_bureau_details") val investigationBureau: IBVoteDetails?,

    @SerializedName("appeal_enabled_at") override val appealEnabledAt: String?,
    @SerializedName("advocate_fee") override val advocateFee: Int?,
    @SerializedName("advocate_defenses") val advocateDefenses: List<AdvocateDefense>?,

    @SerializedName("jury_details") val jury: JuryVoteDetails?,

    @SerializedName("vote_eligible") val eligibleToVote: Boolean?,
    @SerializedName("is_guilty") override val guilty: Boolean?,
    @SerializedName("action") override val actionTaken: CaseVerdictAction?,

    @SerializedName("user_details") override val userDetails: DefendantUser? = null,

    @SerializedName("applicable_fee") override val applicableFee: Double?,
    @SerializedName("is_edited") val edited: Boolean? = false,
    @SerializedName("is_deleted") val deleted: Boolean? = false,

    ): AbstractCaseDetails() {

    data class ProofFile(
        val url: String
    ) {
        val mediaType: MediaType
            get() = MediaUtils.getMediaTypeFromFileName(url)
    }

    val proofFiles: List<ProofFile>
        get() = _proofFiles.orEmpty().map {
            ProofFile(it)
        }

    val showIBStage: Boolean
        get() = if (caseStatus == ReportCaseStatus.INVESTIGATION_BUREAU) true
        else if(caseStatus > ReportCaseStatus.INVESTIGATION_BUREAU) investigationBureau!=null
        else false

    val showDefenses: Boolean
        get() = if (caseStatus == ReportCaseStatus.ADVOCATES_UNION) true
        else if(caseStatus > ReportCaseStatus.ADVOCATES_UNION) advocateDefenses?.isNotEmpty() == true
        else false

    val formattedViolationsCount: String
        get() = violationsCount.toString()

    abstract class VoteDetails {
        abstract val guiltyCount: Int
        abstract val notGuiltyCount: Int

        abstract val maxVotes: Int

        val totalVotes: Int
            get() = guiltyCount + notGuiltyCount

        val totalVotesString: String
            get() = "$totalVotes/$maxVotes"

        val majorityVote: VoteResult
            get() {
                return if (guiltyCount >= notGuiltyCount) VoteResult.GUILTY
                else VoteResult.NOT_GUILTY
            }

        val majorityVoteCount: Int
            get() = max(guiltyCount, notGuiltyCount)

        val majorityVoteCountString: String
            get() = "$majorityVoteCount/$maxVotes"
    }

    data class IBVoteDetails(
        @SerializedName("guilty_count") override val guiltyCount: Int,
        @SerializedName("not_guilty_count") override val notGuiltyCount: Int
    ) : VoteDetails() {
        override val maxVotes: Int
            get() = MAX_VOTES_IB
    }

    data class JuryVoteDetails(
        @SerializedName("guilty_count") override val guiltyCount: Int,
        @SerializedName("not_guilty_count") override val notGuiltyCount: Int
    ) : VoteDetails() {
        override val maxVotes: Int
            get() = MAX_VOTES_JURY
    }

    data class AdvocateDefense(
        @SerializedName("time_created") val created: String?,
        @SerializedName("time_updated") val updated: String?,

        @SerializedName("report_id") val id: Int,
        @SerializedName("defender_id") val defender: Int,

        @SerializedName("defense_comment") val defense: String,
        @SerializedName("proof_files") val proofFileUrls: List<String>?,
    ) {
        val proofFiles: List<ProofFile>
            get() = proofFileUrls.orEmpty().map {
                ProofFile(it)
            }
    }

    val firstMediaMeta: PostatMedia?
        get() = mediaMetas?.firstOrNull()

    val hasProofFiles: Boolean
        get() = proofFiles.isNotEmpty()

    val hasAdvocateDefenseFiles: Boolean
        get() = !advocateDefenses.isNullOrEmpty()

    val hasJuryStage: Boolean
        get() = jury!=null
}