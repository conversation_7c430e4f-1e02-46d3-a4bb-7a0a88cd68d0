package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.BroadcastAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.BroadcastChatMessageWithMedia
import com.app.messej.data.room.FlashatDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@OptIn(ExperimentalPagingApi::class)
class BroadcastIncomingRemoteMediator(
    private val userId: Int,
    private val database: FlashatDatabase,
    private val networkService: BroadcastAPIService
) : RemoteMediator<Int, BroadcastChatMessageWithMedia>() {
    val dao = database.getChatMessageDao()

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, BroadcastChatMessageWithMedia>
    ): MediatorResult = withContext(Dispatchers.IO) {
        return@withContext try {
            val recent = when (loadType) {
                LoadType.REFRESH -> {
                    Log.d("BCMRM", "load: trying to REFRESH")
                    null
                }
                LoadType.PREPEND -> {
                    Log.d("BCMRM", "load: trying to PREPEND")
                    dao.getLatestBroadcast(userId)
                }
                LoadType.APPEND -> return@withContext MediatorResult.Success(endOfPaginationReached = true)
            }

            val response = networkService.getIncomingBroadcasts(userId = userId, recent?.message?.messageId)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            val messages = result.broadcasts

            messages.forEach { msg ->
                msg.sanitize()
            }

            withContext(Dispatchers.IO) {
                database.withTransaction {
                    dao.insertBroadcastMessages(messages)
                }
            }
            MediatorResult.Success(endOfPaginationReached = true)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}