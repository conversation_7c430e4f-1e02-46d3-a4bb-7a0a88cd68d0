package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class FlashTab {
    @SerializedName("all") ALL,
    @SerializedName("stars") STARS,
    @SerializedName("me") ME;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    companion object {
        val default: FlashTab
            get() = ALL
    }

}