package com.app.messej.data.repository

import android.content.Context
import android.util.Log
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.Constants
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.SocialAffairAPIService
import com.app.messej.data.model.SocialProofMedia
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.SocialAffairUpgradeSupportStatus
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.socialAffairs.AskedQuestionsResponse
import com.app.messej.data.model.api.socialAffairs.CommitteeMembersResponse
import com.app.messej.data.model.api.socialAffairs.FormerMSAResponse
import com.app.messej.data.model.api.socialAffairs.HonoursResponse
import com.app.messej.data.model.api.socialAffairs.MySupportListResponse
import com.app.messej.data.model.api.socialAffairs.PersonalSupportRequest
import com.app.messej.data.model.api.socialAffairs.PersonalSupportRequest.Companion.personalSupportBasicData
import com.app.messej.data.model.api.socialAffairs.SocialApproveCaseRequest
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfoResponse
import com.app.messej.data.model.api.socialAffairs.SocialCasesListResponse
import com.app.messej.data.model.api.socialAffairs.SocialDonateRequest
import com.app.messej.data.model.api.socialAffairs.SocialQuestionRequest
import com.app.messej.data.model.api.socialAffairs.SocialUpgradeSupportEligibility
import com.app.messej.data.model.api.socialAffairs.SocialVoteErrorResponse
import com.app.messej.data.model.api.socialAffairs.SocialVoteRequest
import com.app.messej.data.model.api.socialAffairs.VotersResponse
import com.app.messej.data.model.enums.MySocialSupportActionMenuItem
import com.app.messej.data.model.enums.SocialActiveCaseMainTab
import com.app.messej.data.model.enums.SocialActiveTab
import com.app.messej.data.model.enums.SocialCaseFilter
import com.app.messej.data.repository.pagingSources.SocialAffairCommitteeDataSource
import com.app.messej.data.repository.pagingSources.SocialAffairHonoursDataSource
import com.app.messej.data.repository.pagingSources.SocialAffairQuestionsDataSource
import com.app.messej.data.repository.pagingSources.SocialAffairVotersDataSource
import com.app.messej.data.repository.pagingSources.SocialCasesDataSource
import com.app.messej.data.repository.pagingSources.SocialFormerMSADataSource
import com.app.messej.data.repository.pagingSources.SocialMyPersonalRequestsDataSource
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Response
import java.io.File

class SocialAffairsRepository(context: Context) : BaseMediaUploadRepository(context) {

    suspend fun getSocialCaseCount() : ResultOf<SocialCasesListResponse> {
        return try {
            val response = APIServiceGenerator.createService(SocialAffairAPIService::class.java).getCasesCount()
            APIUtil.handleResponse(response)
        } catch (error : Exception) {
            ResultOf.Error(Exception(error))
        }
    }

    fun getSocialCases(
        mainTab: SocialActiveCaseMainTab? = null,
        tab: SocialActiveTab? = null,
        subTab: SocialCaseFilter? = null,
        countCallBack: (SocialCasesListResponse?) -> Unit
    ): Pager<Int, SocialCaseInfo> {
        return Pager(
            config = PagingConfig(pageSize = 20, enablePlaceholders = false),
            pagingSourceFactory = {
                SocialCasesDataSource(
                    apiService = APIServiceGenerator.createService(serviceClass = SocialAffairAPIService::class.java),
                    mainTab = mainTab,
                    tab = tab,
                    subTab = subTab,
                    countCallBack = countCallBack
                )
            }
        )
    }

    fun getMyPersonalSupportRequests(
        isDraft: Boolean = false,
        responseCallBack: (MySupportListResponse?) -> Unit
    ): Pager<Int, MySupportListResponse.MySupportRequest> {
        return Pager(
            config = PagingConfig(pageSize = 20, enablePlaceholders = false),
            pagingSourceFactory = {
                SocialMyPersonalRequestsDataSource(
                    apiService = APIServiceGenerator.createService(serviceClass = SocialAffairAPIService::class.java),
                    isDraft = isDraft,
                    responseCallBack = responseCallBack,
                )
            }
        )
    }

    suspend fun getUpgradeSupportStatus(): ResultOf<SocialAffairUpgradeSupportStatus> {
        return try {
            val resp = APIServiceGenerator.createService(SocialAffairAPIService::class.java).getUpgradeSupportStatus()
            return APIUtil.handleResponse(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun checkActiveUpgradeCaseExist(): ResultOf<SocialUpgradeSupportEligibility> {
        return try {
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .getRequestedForUpgrade()
            return APIUtil.handleResponse(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    fun getSocialCommitteeMembers(): Pager<Int, CommitteeMembersResponse.CommitteeMemberUserDetail> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                SocialAffairCommitteeDataSource(
                    apiService = APIServiceGenerator.createService(serviceClass = SocialAffairAPIService ::class.java)
                )
            }
        )
    }

    fun getHonoursList(): Pager<Int, HonoursResponse.Honour> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                SocialAffairHonoursDataSource(
                    apiService = APIServiceGenerator.createService(serviceClass = SocialAffairAPIService::class.java)
                )
            }
        )
    }

    fun getVotersList(caseId: Int): Pager<Int, VotersResponse.Voter> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                SocialAffairVotersDataSource(
                    caseId = caseId,
                    apiService = APIServiceGenerator.createService(serviceClass = SocialAffairAPIService::class.java)
                )
            }
        )
    }

    fun getAskedQuestions(caseId: Int, questionCallBack: (Boolean, Int) -> Unit): Pager<Int, AskedQuestionsResponse.QuestionAndAnswer> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                SocialAffairQuestionsDataSource(
                    caseId = caseId,
                    apiService = APIServiceGenerator.createService(serviceClass = SocialAffairAPIService::class.java),
                    questionCallBack = questionCallBack
                )
            }
        )
    }

    suspend fun editQuestion(questionId: Int?, question: String?): ResultOf<String> {
        return try {
            val req = SocialQuestionRequest(question = question)
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .editQuestion(questionId = questionId, request = req)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteQuestion(questionId: Int): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .deleteQuestion(questionId = questionId)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteAnswer(questionId: Int): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .deleteAnswer(questionId = questionId)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun submitQuestion(caseId: Int?, question: String?): ResultOf<String> {
        return try {
            val request = SocialQuestionRequest(caseId = caseId, question = question)
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .submitQuestion(request = request)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun submitAnswer(questionId: Int?, answer: String?): ResultOf<String> {
        return try {
            val request = SocialQuestionRequest(answer = answer)
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .submitAnswer(request = request, questionId = questionId)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun payFine(): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .payFine()
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    fun getFormerMSAList(): Pager<Int, FormerMSAResponse.FormerMSA> {
        return Pager(
            config = PagingConfig(pageSize = 20),
            pagingSourceFactory = {
                SocialFormerMSADataSource(
                    apiService = APIServiceGenerator.createService(serviceClass = SocialAffairAPIService ::class.java)
                )
            }
        )
    }

    suspend fun requestSupport(req: PersonalSupportRequest): SocialResultOf<String> {
        return try {
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .requestSupport(request = req, isPersonalSupport = true)
            handleSocialResponse(resp)
        } catch (e: Exception) {
            SocialResultOf.Error(Exception(e))
        }
    }

    suspend fun upgradeSupport(): ResultOf<SocialUpgradeSupportEligibility> {
        return try {
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .requestSupport(isUpgradeSupport = true)
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun approveCase(caseId: Int?): ResultOf<String> {
        return try {
            val request = SocialApproveCaseRequest(supportRequestId = caseId)
            val resp = APIServiceGenerator
                .createService(SocialAffairAPIService::class.java)
                .approveCase(request)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getCaseInfo(id: String, isIncludeResidentStatusOnly: Boolean = true) : ResultOf<SocialCaseInfoResponse> {
        return try {
            val response = APIServiceGenerator
                .createService(serviceClass = SocialAffairAPIService::class.java)
                .getCaseInfo(
                    id = id,
                    includeCommitteeMember = !isIncludeResidentStatusOnly,
                    includeVotersCount = !isIncludeResidentStatusOnly,
                    includeQuestionsCount = !isIncludeResidentStatusOnly,
                    includeResidentStatus = isIncludeResidentStatusOnly
                )
            APIUtil.handleResponse(response = response)
        } catch (error: Exception) {
            ResultOf.Error(Exception(error))
        }
    }

    suspend fun editPersonalSupportCase(
        id: String,
        request: PersonalSupportRequest = personalSupportBasicData,
        status: MySocialSupportActionMenuItem? = null
    ) : SocialResultOf<String> {
        return try {
            val response = APIServiceGenerator
                .createService(serviceClass = SocialAffairAPIService::class.java)
                // status = (status?.serializedName() ?: "new") -> Because, when submitting an draft case and click on submit, need to pass status as "new"
                .editPersonalSupportCase(request = request.copy(status = (status?.serializedName() ?: "new")), id = id)
            handleSocialResponse(response = response)
        } catch (error : Exception) {
            SocialResultOf.Error(Exception(error))
        }
    }

    sealed class SocialResultOf<out T> {
        data class Success<out T>(val value: T) : SocialResultOf<T>()
        data class APIError(val error: SocialVoteErrorResponse, val code: Int = 0) : SocialResultOf<Nothing>()
        data class Error(val exception: Exception) : SocialResultOf<Nothing>()
    }

    private fun <T> handleSocialResponse(response: Response<APIResponse<T>>): SocialResultOf<String> {
        return if (response.isSuccessful && response.code() == 200) {
            val result = response.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            SocialResultOf.Success(result)
        } else {
            val error = SocialVoteErrorResponse.parseError(response.errorBody())
            SocialResultOf.APIError(error, code = response.code())
        }
    }

    suspend fun vote(request: SocialVoteRequest) : SocialResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(SocialAffairAPIService::class.java).vote(request = request)
            handleSocialResponse(response)
        } catch (error : Exception) {
            SocialResultOf.Error(Exception(error))
        }
    }

    suspend fun donateCoins(request: SocialDonateRequest) : ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(SocialAffairAPIService::class.java).donateCoins(request = request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (error : Exception) {
            ResultOf.Error(Exception(error))
        }
    }

    override suspend fun getVideoSecrets(): ResultOf<UploadCredentialsResponse> {
        return try {
            val resp = APIServiceGenerator.createService(SocialAffairAPIService::class.java, true).getUploadCredentials()
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun uploadMedia(med: SocialProofMedia): ResultOf<Unit> = withContext(Dispatchers.IO) {
        val result = prepareAndUploadMultipartMedia(med)
        if (result is ResultOf.Success) {
            med.mediaUploaded = true
        } else {
            med.mediaUploaded = false
        }
        result
    }

    suspend fun processVideo(med: SocialProofMedia, listener: VideoEncoderUtil.MediaProcessingListener): File {
        return VideoEncoderUtil.encodeVideoMedia3(med, listener)
    }

    private suspend fun prepareAndUploadMultipartMedia(media: SocialProofMedia): ResultOf<Unit> {
        try {
            MediaUploadListener.post(media.uuid, 0)
            performMultipartMediaUpload(media.s3UploadMedia).collect {
                when (val res = it) {
                    MultipartMediaUploadResult.Complete -> {

                    }
                    is MultipartMediaUploadResult.Error -> throw res.error
                    is MultipartMediaUploadResult.Progress -> {
                        Log.d("ENCODE", "posting ${res.percent} to MUL")
                        MediaUploadListener.post(media.uuid, res.percent)
                    }
                }
            }
            MediaUploadListener.clear(media.uuid)
        } catch (e: Exception) {
            MediaUploadListener.clear(media.uuid)
            Log.e("ENCODE", "prepareAndUploadMultipartMedia:", e)
            return ResultOf.getError(e)
        }

        Log.d("ENCODE", "prepareAndUploadMultipartMedia: end of uploading Method")
        return ResultOf.Success(Unit)
    }

}