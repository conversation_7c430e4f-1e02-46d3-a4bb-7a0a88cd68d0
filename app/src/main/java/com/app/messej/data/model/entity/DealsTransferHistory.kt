package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

@Entity(tableName = EntityDescriptions.TABLE_TRANSFER_HISTORY)
data class  DealsTransferHistory(
    @ColumnInfo("flax") @SerializedName("flax") val flax: Double? = null,
    @PrimaryKey @ColumnInfo("id") @SerializedName("id") val id: Int,
    @ColumnInfo("purpose") @SerializedName("purpose") val purpose: String? = null,
    @ColumnInfo("purpose_id") @SerializedName("purpose_id") val purposeId: Int? = null,
    @ColumnInfo("sender_id") @SerializedName("sender_id") val senderId: Int? = null,
    @ColumnInfo("receiver_id") @SerializedName("receiver_id") val receiverId: Int? = null,
    @ColumnInfo(name = COLUMN_FILTER_STATUS) @SerializedName("status") val status: FlaxStatus? = null,
    @ColumnInfo(name = COLUMN_CREATED_TIME) @SerializedName("time_created") val timeCreated: String? = null,
    @ColumnInfo("time_updated") @SerializedName("time_updated") val timeUpdated: String? = null,
    @ColumnInfo("user_id") @SerializedName("user_id") val userId: String? = null,
    @ColumnInfo("withdrawn") @SerializedName("withdrawn") val withdrawn: Boolean? = null,
    @ColumnInfo("name") @SerializedName("name") var name: String? = null,
    @ColumnInfo("username") @SerializedName("username") val username: String? = null,
    @ColumnInfo("profile_photo") @SerializedName("profile_photo") val profilePhoto: String? = null,
    @ColumnInfo("premium") @SerializedName("premium") val premium: Boolean? = null,
    @ColumnInfo("is_deleted")@SerializedName("is_deleted") val isDeleted:Boolean?=null,
    @ColumnInfo("received_flax") @SerializedName("received_flax") val receivedFlax: Double? = null,
    @ColumnInfo("sent_flax") @SerializedName("sent_flax") val sentFlax: Double? = null,
    @ColumnInfo("membership") @SerializedName("membership") val membership: String? = null,
){
    enum class FlaxStatus {
        @SerializedName(value = "Sent") Sent,
        @SerializedName(value = "Received") Received,
        @SerializedName(value = "Credited") Credited,
        @SerializedName(value = "Debited") Debited
    }
    companion object {
        const val COLUMN_CREATED_TIME = "time_created"
        const val COLUMN_FILTER_STATUS = "status"
    }

    val parsedCreatedTime:LocalDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)?.run { DateTimeUtils.getLocalDateTime(this) }
}