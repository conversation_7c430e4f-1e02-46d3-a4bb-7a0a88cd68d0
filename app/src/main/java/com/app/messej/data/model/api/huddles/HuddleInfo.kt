package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.AbstractHuddle
import com.app.messej.data.model.HuddlePrivacy
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.SellHuddle
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

data class HuddleInfo (
    @SerializedName("about"                                 ) override var about                    : String?  = null,
    @SerializedName("category"                              ) override var category                 : String?  = null,
    @SerializedName("category_id"                           ) var categoryId                        : Int?     = null,
    @SerializedName("created_by"                            ) var createdBy                         : String?  = null,
    @SerializedName("group_photo"                           ) override var groupPhoto               : String?  = null,
    @SerializedName("huddle_participants_limit_for_free"    ) var huddleParticipantsLimitForFree    : Int?     = null,
    @SerializedName("huddle_participants_limit_for_premium" ) var huddleParticipantsLimitForPremium : Int?  = null,
    @SerializedName("huddle_role"                           ) var huddleRole                        : UserRole?  = null,
    @SerializedName("id"                                    ) override var id                       : Int,
    @SerializedName("invitations_count"                     ) var invitationsCount                  : Int?     = null,
    @SerializedName("invite_code"                           ) var inviteCode                        : String?  = null,
    @SerializedName("invite_link"                           ) var inviteLink                        : String?  = null,
    @SerializedName("is_admin"                              ) var isAdmin                           : Boolean = false,
    @SerializedName("is_manager"                            ) var isManager                         : Boolean = false,
    @SerializedName("is_muted"                              ) var isMuted                           : Boolean? = null,
    @SerializedName("is_premium"                            ) var isPremium                         : Boolean? = null,
    @SerializedName("join_requests_count"                   ) var joinRequestsCount                 : Int?     = null,
    @SerializedName("manager_id"                            ) var managerId                         : Int?     = null,
    @SerializedName("manager_premium_status"                ) override var managerPremium           : Boolean = false,
    @SerializedName("name"                                  ) override var name                     : String,
    @SerializedName("online_participants"                   ) var onlineParticipants                : Int?     = null,
    @SerializedName("participant_share"                     ) var participantShare                  : Boolean,
    @SerializedName("private"                               ) override var isPrivate                : Boolean,
    @SerializedName("report_count"                          ) var reportCount                       : Int?     = null,
    @SerializedName("request_to_join"                       ) override var requestToJoin            : Boolean,
    @SerializedName("status"                                ) override var status                   : HuddleStatus,
    @SerializedName("thumbnail"                             ) override var thumbnail                : String?  = null,
    @SerializedName("time_created"                          ) var timeCreated                       : String?  = null,
    @SerializedName("time_updated"                          ) var timeUpdated                       : String?  = null,
    @SerializedName("total_members"                         ) override var totalMembers             : Int,
    @SerializedName("total_requests"                        ) var totalRequests                     : Int?     = null,
    @SerializedName("user_status"                           ) override var userStatus               : HuddleUserStatus,
    @SerializedName("huddle_admin_limit"                    ) var huddleAdminLimit                  : Int? = null,
    @SerializedName("admin_invited_count"                   ) var totalAdminInvites                 : Int? = null,
    @SerializedName("admins_in_huddle_count"                ) var adminsCount                       : Int? = null,
    @SerializedName("language"                              ) var huddleLanguage                    : String? = null,
    @SerializedName("tribe"                                 ) val isTribe                           : Boolean,
    @SerializedName("privacy"                               ) var privacy                           : HuddlePrivacy? = null,
    @SerializedName("empowered_user_blocked"                ) var empoweredUserBlocked              : Boolean = false,
    @SerializedName("huddle_for_sale"                       ) var huddleForSale: Boolean,
    @SerializedName("huddle_sell_min_participant_count"     ) var huddleSellMinParticipantCount: Int,
    @SerializedName("huddle_sell_flax"                      ) var huddleSellFlax: Double? = null,
    @SerializedName("manager_details"             ) val managerDetails: SenderDetails? = null,

): AbstractHuddle(){

    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)

    val formattedCreatedTime: String
        get() = DateTimeUtils.format(parsedCreatedTime, DateTimeUtils.FORMAT_DATE_HUDDLE_GROUP_INFO)

    val requestsAndInvites: String
        get() {
            return if (invitationsCount == null && joinRequestsCount == null) ""
            else ((invitationsCount ?: 0) + (joinRequestsCount ?: 0)).toString()
        }

    val huddleType: HuddleType
        get() = if (isPrivate) HuddleType.PRIVATE else HuddleType.PUBLIC

    val canSendOrAcceptAdminInvite: Boolean
        get() = (adminsCount?.let { it < (huddleAdminLimit ?: Int.MAX_VALUE)} == true)

    val canAddParticipant: Boolean
        get() {
        val limit = (if (managerPremium) huddleParticipantsLimitForPremium else huddleParticipantsLimitForFree) ?: Int.MAX_VALUE
        return totalMembers < limit
    }

    val showPremiumBadge: Boolean
        get() {
            return if (isTribe) thumbnail != null else managerPremium
        }
    val isParticipantsCountCheck: Boolean
        get() {
            return totalMembers >= huddleSellMinParticipantCount
        }

    val sellHuddle: SellHuddle
        get() {
            return if (isParticipantsCountCheck && !huddleForSale) SellHuddle.FOR_SALE
            else if (huddleForSale && isParticipantsCountCheck) SellHuddle.EDIT_SALE
            else SellHuddle.CANT_SALE

        }
}