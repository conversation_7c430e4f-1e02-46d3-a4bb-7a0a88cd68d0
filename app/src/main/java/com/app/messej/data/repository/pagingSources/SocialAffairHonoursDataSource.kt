package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.SocialAffairAPIService
import com.app.messej.data.model.api.socialAffairs.HonoursResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SocialAffairHonoursDataSource(private val apiService: SocialAffairAPIService) : PagingSource<Int, HonoursResponse.Honour>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, HonoursResponse.Honour>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, HonoursResponse.Honour> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = apiService.getHonoursList(page = currentPage)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (data.hasNext == true) currentPage + 1 else null
                LoadResult.Page(
                    data = data.honoursList ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}