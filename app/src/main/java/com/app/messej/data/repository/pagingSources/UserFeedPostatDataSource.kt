package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PostatAPIService
import com.app.messej.data.model.entity.Postat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class UserFeedPostatDataSource(
    private val api: PostatAPIService,
    private val userId: Int
) : PagingSource<Int, Postat>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Postat> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getUserFeedPostat(page = currentPage, userId = userId)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
//                val nextKey = if (response.body()?.result!!.nextPage!! == null) null else currentPage.plus(1)
                val nextKey = if (data.nextPage==null) null else currentPage.plus(1)

                LoadResult.Page(
                    data = data.postatList,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, Postat>): Int? {
        return null
    }
}
