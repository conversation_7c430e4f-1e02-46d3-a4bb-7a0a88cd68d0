package com.app.messej.data.model.api


import com.google.gson.annotations.SerializedName

data class RatingStatisticsResponse(
    @SerializedName("huddle_posts") val huddlePosts: <PERSON>olean? = null,
    @SerializedName("others_huddle_posts") val otherHuddlePosts : <PERSON>olean? = null,
    @SerializedName("flash") val flash : Boolean? = null,
    @SerializedName("podium_speaker") val podiumSpeaker : Boolean? = null,
    @SerializedName("postat_post") val postatPost : Boolean? = null,
    @SerializedName("postat_comments") val postatComments : Boolean? = null,
    @SerializedName("flash_comments") val flashComments : Boolean? = null,
    @SerializedName("joined_podiums") val joinedPodium : Boolean? = null,
    @SerializedName("gifts") val gift : Boolean? = null,
    @SerializedName("played_maidans") val playedMaidan : Boolean? = null
)