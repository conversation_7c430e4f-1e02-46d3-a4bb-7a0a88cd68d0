package com.app.messej.data

object Constants {

    enum class BuildType(val value: String) {
        DEBUG("debug"),
        QA("qa"),
        PREPROD("preprod"),
        RELEASE("release")
    }

    const val FLASHAT_TAG = "FLGN"

    const val PLATFORM_OS = "android"

    //AES256
    const val ENC256_KEY = "dolpiSkudUonakJekInrbnTanmHprmbo"
    const val ENC256_KEY_IV = "iatRrnecnViotdom"

    enum class InputValidationResult {
        NONE,
        PASS,
        FAIL
    }

    const val ERROR_RESPONSE_BODY_MISSING = "Response body is missing"

    const val HTTP_HEADER_CONTENT_TYPE = "Content-Type"

    const val YOUTUBE_FLASHAT_LINK = "https://www.youtube.com/channel/UC6vrCjLDdKO9t62t8OTFK7w"

    const val YOUTUBE_ARABIC_FLASHAT_LINK = "https://www.youtube.com/@flashatARABIC/videos"

    const val EXTERNAL_CONTRIBUTOR_NOTIFICATION_ID = 1111

    const val CURRENCY_COIN = "COiN"
    const val CURRENCY_FLIX = "FLiX"

}