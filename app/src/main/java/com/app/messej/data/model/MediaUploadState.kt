package com.app.messej.data.model

import androidx.room.TypeConverter

sealed class MediaUploadState {
    object None : MediaUploadState()
    object Pending : MediaUploadState()
    data class Uploading(val progress: Int) : MediaUploadState()


    class Converter {
        companion object {
            private const val KEY_NONE = "NONE"
            private const val KEY_PENDING = "PENDING"
            private const val KEY_UPLOADING = "UPLOADING"
        }
        @TypeConverter
        fun decodeUploadState(data: String?): MediaUploadState? {
            data?: return null
            val parts = data.split('|')

            return when(parts[0]) {
                KEY_NONE -> None
                KEY_PENDING -> Pending
                KEY_UPLOADING -> Uploading(parts[1].toInt())
                else -> None
            }
        }
        @TypeConverter
        fun encodeUploadState(someObject: MediaUploadState?): String? {
            return when(someObject) {
                None -> "$KEY_NONE|0"
                Pending -> "$KEY_PENDING|0"
                is Uploading -> "$KEY_UPLOADING|${someObject.progress}"
                null -> null

            }
        }
    }
}