package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class HuddleInvolvement {
    @SerializedName("user_managed") MANAGER,
    @SerializedName("user_admin") ADMIN,
    @SerializedName("user_participated") PARTICIPANT,
    @SerializedName("NA") NONE;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}