package com.app.messej.data.model.api.podium.challenges

import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoardModel
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard.Companion.parseBoard
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard.Companion.unParseBoard
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.google.gson.annotations.SerializedName

data class BoxData (
    @SerializedName("current_player"     ) override var currentPlayerUserId        : Int? = null,
    @SerializedName("turn_start_time"    ) override val turnStartTime              : Double? = null,
    @SerializedName("box"                ) private var _board                      : List<List<Int>>? = null,
    @SerializedName("hlines"             ) private var _hLines                     : List<List<Int>>? = null,
    @SerializedName("vlines"             ) private var _vLines                     : List<List<Int>>? = null,
    ): BoardData() {

    companion object {
        const val TURN_DURATION_SECONDS = 10L
        const val TURN_DURATION_MILLIS = TURN_DURATION_SECONDS*1000L
    }

    override val turnDurationSeconds: Long
        get() = TURN_DURATION_SECONDS

    data class BoxChallengeBoardData (
        var boxes : List<List<SlotState>> = List(BoxChallengeBoardModel.ROWS) {
        List(BoxChallengeBoardModel.COLS) {
            SlotState.EMPTY
        }
    },
        var verticalLines: List<List<SlotState>> = List(BoxChallengeBoardModel.ROWS+1) {
            List(BoxChallengeBoardModel.COLS) {
                SlotState.EMPTY
            }
        },
        var horizontalLines: List<List<SlotState>> = List(BoxChallengeBoardModel.ROWS) {
            List(BoxChallengeBoardModel.COLS+1) {
                SlotState.EMPTY
            }
        },
    )

    var board: BoxChallengeBoardData
        get() {
            try {
                val data = BoxChallengeBoardData()
                return data.copy(
                    boxes = _board?.parseBoard()?:data.boxes,
                    horizontalLines = _hLines?.parseBoard()?:data.horizontalLines,
                    verticalLines = _vLines?.parseBoard()?:data.verticalLines,
                )
            } catch (e: Exception) {
                Firebase.crashlytics.log("Crashed while trying to parse board: $_board")
                Firebase.crashlytics.recordException(e)
                return BoxChallengeBoardData()
            }
        }
        set(value) {
            _board = value.boxes.unParseBoard()
            _hLines = value.horizontalLines.unParseBoard()
            _vLines = value.verticalLines.unParseBoard()
        }
}
