package com.app.messej.data.model.api.huddles

import com.google.gson.annotations.SerializedName

data class OtherHuddlesList(
    @SerializedName("id") val id:Int,
    @SerializedName("group_photo")val groupPhoto: String?=null,
    @SerializedName("huddle_name")val huddleName: String?=null,
    @SerializedName("participants_count")val participantsCount: Int?=null,
    @SerializedName("premium")val premium: Boolean?=null,
    @SerializedName("thumbnail_url")val thumbnailUrl: String?=null,
    @SerializedName("tribe")val tribe: Boolean?=null
)