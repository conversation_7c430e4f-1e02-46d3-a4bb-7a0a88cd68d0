package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.GiftAPIService
import com.app.messej.data.model.api.gift.PointsPurchase

private const val STARTING_KEY = 1
class GiftPurchaseHistoryDataSource(private val api: GiftAPIService) : PagingSource<Int, PointsPurchase>() {


    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PointsPurchase> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getGiftPurchaseHistory(page = currentPage, limit = 50)
            val responseData = mutableListOf<PointsPurchase>()
            val result = response.body()?.result
            val data = result?.pointsPurchases ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (!response.body()?.result!!.nextPage) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.pointsPurchases, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("TransactionResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, PointsPurchase>): Int? {
        return null
    }
}