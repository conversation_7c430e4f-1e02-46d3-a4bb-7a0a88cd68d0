package com.app.messej.data.model.api.stateAffair


import com.app.messej.ui.home.publictab.authorities.stateAffairs.UserStateAffair
import com.google.gson.annotations.SerializedName

data class StateAffairsCitizenshipUserDataResponse(
    @SerializedName("has_next") val hasNext: Boolean? = false,
    @SerializedName("page") val page: Int,
    @SerializedName("page_count") val pageCount: Int,
    @SerializedName("data") val stateAffairUserData: List<UserStateAffair>,
    @SerializedName("count") val count: Int
)