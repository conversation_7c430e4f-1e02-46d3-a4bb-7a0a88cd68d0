package com.app.messej.data.model.api.huddles


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

@Entity(tableName = EntityDescriptions.TABLE_SELL_HUDDLE_LIST)
data class HuddleForSale(
    @ColumnInfo("about")@SerializedName("about") val about: String?=null,
    @ColumnInfo("flax")@SerializedName("flax") val flax: Double?=null,
    @ColumnInfo(name = HUDDLE_ID)@SerializedName("huddle_id") val huddleId: Int?=null,
    @ColumnInfo("huddle_name")@SerializedName("huddle_name") val huddleName: String?=null,
    @PrimaryKey @ColumnInfo("id")@SerializedName("id") val id: Int?=null,
    @ColumnInfo("owner")@SerializedName("owner") val owner: Int?=null,
    @ColumnInfo("participant_count")@SerializedName("participant_count") val participantCount: Int?=null,
    @ColumnInfo("status")@SerializedName("status") val status: String?=null,
    @ColumnInfo(name = COLUMN_CREATED_TIME)@SerializedName("time_created") val timeCreated: String?=null,
    @ColumnInfo("time_updated")@SerializedName("time_updated") val timeUpdated: String?=null,
    @ColumnInfo("huddle_photo")@SerializedName("huddle_photo") val huddlePhoto:String?=null,
    @ColumnInfo("manager_name")@SerializedName("manager_name") val managerName:String?=null
){
    companion object {
        const val COLUMN_CREATED_TIME = "time_created"
        const val HUDDLE_ID = "huddle_id"
    }
    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)

    val formattedCreatedTime: String
        get() = DateTimeUtils.format(parsedCreatedTime, DateTimeUtils.FORMAT_DATE_HUDDLE_GROUP_INFO)


   /* val flaxAmountFormatted: String
        get() = DataFormatHelper.numberToK(flax?.toInt() ?: 0)
*/
    val flaxAmountFormatted: String
        get() = flax?.toInt()!!.numberToKWithFractions()

}