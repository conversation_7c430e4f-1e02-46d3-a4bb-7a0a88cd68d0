package com.app.messej.data.model.notification


import com.google.gson.annotations.SerializedName

data class GiftNotificationPayLoad(
    @SerializedName("associated_obj_id") val associatedObjId: Any? = Any(),
    @SerializedName("category") val category: String? = "",
    @SerializedName("html_text") val htmlText: String? = "",
    @SerializedName("message") val message: String,
    @SerializedName("notification_id") val notificationId: Int,
    @SerializedName("notification_type") val notificationType: String? = "",
    @SerializedName("receiver_id") val receiverId: Int? = 0,
    @SerializedName("sender_id") val senderId: Int? = 0,
    @SerializedName("thumbnail") val thumbnail: String? = "",
)