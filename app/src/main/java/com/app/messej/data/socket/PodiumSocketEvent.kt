package com.app.messej.data.socket



enum class PodiumSocketEvent(override val key: String): SocketEvent {
    //Podium Socket Events
    TX_JOIN_PODIUM("join"),
    RX_TX_LEAVE_PODIUM("leave"),
    RX_TX_CLOSE_PODIUM("close"), //called by admin or manager only
    RX_TX_ENTER_PODIUM_WAIT_LIST("enter_wait_list"),
    RX_TX_EXIT_PODIUM_WAIT_LIST("exit_wait_list"),
    RX_TX_ENTER_SPEAKER_LIST("enter_speaker_list"),
    RX_TX_EXIT_SPEAKER_LIST("exit_speaker_list"),
    RX_MUTE_PODIUM("mute"),
    RX_UNMUTE_PODIUM("unmute"),
    RX_BLOCK_PODIUM("block"),
    RX_TX_LIKE_PODIUM("like"),
    RX_TX_PODIUM_CHAT("message"),
    RX_PODIUM_SYNC("sync"),
    TX_PODIUM_ATTENDANCE("attendance"),
    RX_FREEZE_USER("freeze"),
    RX_UNFREEZE_USER("unfreeze"),
    RX_APPOINT_ADMIN("appoint_admin"),
    RX_NEW_ADMIN("new_admin"),
    RX_CANCEL_ADMIN_REQUEST("cancel_admin_request"),
    RX_EXIT_ADMIN("exit_admin_list"),
    RX_DISMISS_ADMIN("dismiss_admin"),
    RX_CHAT_DISABLED("chat_disabled"),
    RX_CHAT_ENABLED("chat_enabled"),
    RX_TX_SHOW_IN_MAIN_SCREEN("add_to_main_screen"),
    RX_LIKE_DISABLED("likes_disabled"),
    RX_LIKE_ENABLED("likes_enabled"),
    RX_MIC_ENABLED("mic_enabled"),
    RX_MIC_DISABLED("mic_disabled"),
    RX_TX_PODIUM_GIFT("gift_notification_to_participants"),
    RX_PODIUM_ACTIVE("podium_active"),
    RX_PODIUM_USER_JOINED("join_alert"),
    RX_PODIUM_CAMERA_TIME_EXPIRED("camera_time_expired"),
    RX_PODIUM_CAMERA_TIME_EXPIRING_SOON("camera_time_expiring_soon"),
    RX_SPEAKER_INVITE("podium_speaker_invite"),
    RX_SPEAKER_INVITE_RESPONSE("podium_speaker_invite_response"),

    RX_PODIUM_EDIT("edit"),


    TX_PODIUM_ASSEMBLY_STOP_SPEAKING("stop_speaking"),
    RX_PODIUM_ASSEMBLY_START_SPEAKING("start_speaking"),
    RX_PODIUM_ASSEMBLY_EXTEND_SPEAKING_TIME("extend_speaking_time"),
    RX_PODIUM_ASSEMBLY_PAUSE_SPEAKING("pause_speaking"),
    RX_TX_PODIUM_CHAT_DELETE("podium_chat_delete"),
    RX_TX_PODIUM_GIFT_PAUSE("pause_podium_gifts"),
    RX_TX_PODIUM_GIFT_RESUME("resume_podium_gifts"),

    //Podium Challenges
    RX_CHALLENGE_FACILITATOR_APPOINT("appoint_challenge_facilitator"), //when manager appoints a facilitator for challenge. This is where challenge is created
    RX_CHALLENGE_FACILITATOR_CONFIRMED("challenge_facilitator_confirm"), //when facilitator confirms the request
    RX_CHALLENGE_FACILITATOR_DECLINED("challenge_facilitator_decline"), //when facilitator declines the request
    RX_CHALLENGE_TIMER_SET("challenge_timer_set"), //when a time(duration) is set for the challenge
    RX_CHALLENGE_CONTRIBUTOR_APPOINT("appoint_challenge_contributor"), //when the facilitator sends contributor request (not applicable for all challenges)
    RX_CHALLENGE_CONTRIBUTOR_CONFIRMED("challenge_contributor_confirm"), //when the contributor confirms the request
    RX_CHALLENGE_CONTRIBUTOR_DECLINED("challenge_contributor_decline"), //when the contributor declines the request
    RX_CHALLENGE_CONTRIBUTOR_TIMED_OUT("challenge_contributor_request_timed_out"), //when the contributor declines the request
    RX_CHALLENGE_CONTRIBUTOR_INSUFFICIENT_BALANCE("challenge_contributor_insufficient_balance"), //when balance of the contributor is not enough
    RX_CHALLENGE_START("challenge_started"),
    RX_CHALLENGE_CLOSE("challenge_closed"),
    RX_CHALLENGE_CANCEL("challenge_cancelled"),
    RX_TX_CHALLENGE_SCORE_UPDATE("challenge_score_update"),
    RX_CHALLENGE_SYNC("sync_challenge"),
    RX_CHALLENGE_SPEAKER_INVITE("challenge_speaker_invite"),
    RX_CHALLENGE_SPEAKER_INVITE_RESPONSE("challenge_speaker_invite_response"),
    RX_TX_CONFOUR_TOKEN_DROPPED("con4_token_dropped"),
    RX_TX_CONFOUR_DROP_TIMED_OUT("con4_token_drop_timed_out"),
    RX_CHALLENGE_GAME_OVER("challenge_game_over"),

    RX_TX_PENALTY_PLAYER_READY("penalty_kick_player_ready"),
    RX_PENALTY_START_TURN("penalty_kick_start_turn"),
    RX_TX_PENALTY_SELECT_TARGET("penalty_kick_target_selected"),
    RX_TX_PENALTY_KICK_RESULT("penalty_kick_result"),

    RX_MAIDAN_SCORE_UPDATE("maidan_score_update"),
    RX_MAIDAN_CONTRIBUTOR_UPDATE("created_maidan_contributors"),

    RX_YALLA_UPDATE("yalla_challenge_update"),
    RX_YALLA_REMOVE("yalla_challenge_remove"),
    RX_YALLA_COMPETITOR_REQUEST("yalla_challenge_competitor_request"),
    RX_YALLA_NOTIFICATION("yalla_challenge_notification"),
    RX_TX_BOX_LINE_DRAWN("dots_boxes_line_drawn"),
    RX_TX_BOX_LINE_TIMED_OUT("dots_boxes_line_draw_timeout"),
    RX_KNOWLEDGE_RACE_UPDATE("knowledge_race_update"),
    TX_KNOWLEDGE_RACE_ANSWER("knowledge_race_question_answer"),
    RX_FLASHAT_ANTHEM("flashat_anthem ")
    ;

    companion object {
        private val map = PodiumSocketEvent.entries.associateBy(PodiumSocketEvent::key)
        fun from(key: String) = map[key]
    }
}