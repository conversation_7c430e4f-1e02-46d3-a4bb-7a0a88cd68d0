package com.app.messej.data.socket


import android.util.Log
import com.app.messej.BuildConfig
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import io.socket.client.Socket
import org.json.JSONObject

class PodiumSocketService: AbstractSocketService(
    socketUrl = BuildConfig.PODIUM_SOCKET_URL
) {

    private val TAG = PodiumSocketService::class.java.simpleName
    override fun Socket.interceptEvents() {
        onAnyIncoming { args ->
            val eName = args.getOrNull(0) as String
            val msg = args.getOrNull(1) as? JSONObject
            if (msg==null) {
                Firebase.crashlytics.recordException(Exception("Event [$eName] has invalid payload: ${args.getOrNull(1)}"))
                return@onAnyIncoming
            }
            Log.d(TAG, "RX: $eName: $msg")
            Log.d(TAG, "listeners: $listeners")
            listeners.forEach { it.onEvent(eName,msg) }
        }
        onAnyOutgoing { args ->
            Log.d(TAG, "TX: ${args.getOrNull(0)}: ${args.getOrNull(1)}")
        }
    }
}