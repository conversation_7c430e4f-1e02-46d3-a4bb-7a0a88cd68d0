package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractComment
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.PostCommentItem.Companion.COLUMN_HUDDLE_ID
import com.app.messej.data.model.entity.PostCommentItem.Companion.COLUMN_MESSAGE_ID
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = EntityDescriptions.TABLE_HUDDLE_POST_COMMENTS,
    indices = [
        Index(COLUMN_HUDDLE_ID, unique = false),
        Index(COLUMN_MESSAGE_ID, unique = false)
    ]
)
@TypeConverters(
    SenderDetails.Converter::class,
)
data class PostCommentItem(
    @SerializedName("id") @PrimaryKey(autoGenerate = false) @ColumnInfo(name = COLUMN_COMMENT_ID    ) override val commentId       : String,
    @SerializedName("huddle_id"                           ) @ColumnInfo(name = COLUMN_HUDDLE_ID     ) override val huddleId        : Int,
    @SerializedName("message_id", alternate= ["flash_id"] ) @ColumnInfo(name = COLUMN_MESSAGE_ID    ) override val messageId       : String,
    @SerializedName("created", alternate= ["time_created"]) @ColumnInfo(name = COLUMN_CREATED       ) override val created         : String?        = null,
    @SerializedName("message", alternate= ["comment"]     ) @ColumnInfo(name = COLUMN_MESSAGE       ) override val comment         : String?        = null,
    @SerializedName("is_reported"                         ) @ColumnInfo(name = "isReported"         )          val isReported      : Boolean?       = null,
    @SerializedName("sender"                              ) @ColumnInfo(name = "senderId"           ) override val senderId        : Int,
    @SerializedName("sender_details"                      ) @ColumnInfo(name = "senderDetails"      ) override val senderDetails   : SenderDetails? = null,
    @SerializedName("media"                               ) @ColumnInfo(name = "media"              ) override val media           : String?        = null,
): AbstractComment() {
    companion object {
        const val COLUMN_MESSAGE = "message"
        const val COLUMN_MESSAGE_ID = "messageId"
        const val COLUMN_COMMENT_ID = "commentId"
        const val COLUMN_HUDDLE_ID = "huddleId"
        const val COLUMN_CREATED = "created"
    }

    val flashId: String
        get() = messageId


    fun sanitize() {
        senderDetails?.id = senderId
    }
}