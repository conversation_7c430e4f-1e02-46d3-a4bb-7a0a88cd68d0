package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class MaidanTab {
    @SerializedName("join") CHALLENGE,
    @SerializedName("watch") WATCH,
    @SerializedName("create") CREATE,
    @SerializedName("stats") STATS;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    enum class MaidanSubTab {
        @SerializedName("friends") FRIENDS,
        @SerializedName("all") ALL;

        fun serializedName(): String {
            return javaClass
                .getField(name)
                .getAnnotation(SerializedName::class.java)
                ?.value ?: ""
        }
    }
}