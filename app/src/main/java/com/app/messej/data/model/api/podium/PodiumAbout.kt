package com.app.messej.data.model.api.podium

import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_ISO_DATE_TIME_WITHOUT_T
import com.app.messej.ui.utils.DataFormatHelper
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.annotations.SerializedName

data class PodiumAbout(
    @SerializedName("admins"         ) val admins        : List<PodiumSpeaker>,
    @SerializedName("description"    ) val description   : String?           = null,
    @SerializedName("id"             ) val id            : String?           = null,
    @SerializedName("manager"        ) val manager       : PodiumSpeaker,
    @SerializedName("name"           ) val name          : String?           = null,
    @SerializedName("profile_url"    ) val profileUrl    : String?           = null,
    @SerializedName("thumbnail"      ) val thumbnail     : String?           = null,
    @SerializedName("time_published" ) val timePublished : String?           = null,
    @SerializedName("total_likes"    ) val totalLikes    : Int,
    @SerializedName("total_speakers" ) val totalSpeakers : Int,
    @SerializedName("total_users"    ) val totalUsers    : Int,
    @SerializedName("total_duration" ) val totalDuration : String,
    @SerializedName("total_sessions" ) val totalSessions : String
){
    val likesFormatted: String
        get() = totalLikes.numberToKWithFractions()

    val totalUsersFormatted: String
        get() = DataFormatHelper.numberToK(totalUsers)

    val speakersCountFormatted: String
        get() = DataFormatHelper.numberToK(totalSpeakers)

    val timePublishedFormatted: String
        get() = DateTimeUtils.format(DateTimeUtils.parseDateTime(timePublished, FORMAT_ISO_DATE_TIME_WITHOUT_T))

    val timeCreatedParsed
        get() = DateTimeUtils.format(DateTimeUtils.parseDateTime(timePublished, FORMAT_ISO_DATE_TIME_WITHOUT_T), DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)

}
