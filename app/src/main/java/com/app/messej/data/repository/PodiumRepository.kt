package com.app.messej.data.repository

import android.app.Application
import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.agora.AgoraEngineService
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.BusinessAPIService
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.PaidLikePayload
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.DealsBeneficiaryListResponse
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.PodiumJoinErrorResponse
import com.app.messej.data.model.api.huddles.CreatePodiumRequest
import com.app.messej.data.model.api.huddles.CreatePodiumRequest.PodiumTypeEntry
import com.app.messej.data.model.api.huddles.UpdatePodiumRequest
import com.app.messej.data.model.api.podium.FlagChallengeAnswerRequest
import com.app.messej.data.model.api.podium.HideLiveUsersRequest
import com.app.messej.data.model.api.podium.PodiumAbout
import com.app.messej.data.model.api.podium.PodiumBuyCameraResponse
import com.app.messej.data.model.api.podium.PodiumCategoriesResponse
import com.app.messej.data.model.api.podium.PodiumChallengeQuestionResponse
import com.app.messej.data.model.api.podium.PodiumFriend
import com.app.messej.data.model.api.podium.PodiumInvitationRequest
import com.app.messej.data.model.api.podium.PodiumJoinResponse
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.PodiumRecord
import com.app.messej.data.model.api.podium.PodiumResponse
import com.app.messej.data.model.api.podium.PodiumSettingsRequest
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.api.podium.PodiumUserLikesCoinsResponse
import com.app.messej.data.model.api.podium.challenges.CreateMaidanRequest
import com.app.messej.data.model.api.podium.challenges.MaidanChallengeAgainRequest
import com.app.messej.data.model.api.podium.challenges.MaidanEditResponse
import com.app.messej.data.model.api.podium.challenges.MaidanLikeRequest
import com.app.messej.data.model.api.podium.challenges.MaidanLikeResponse
import com.app.messej.data.model.api.podium.challenges.MaidanPlayerSummary
import com.app.messej.data.model.api.podium.challenges.MaidanStatsResponse
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeContributorRequest
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeContributorRequest.Contributor
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeTimerRequest
import com.app.messej.data.model.api.podium.challenges.PodiumConFourChallengeInviteParticipantRequest
import com.app.messej.data.model.api.podium.challenges.PodiumConFourInviteResponse
import com.app.messej.data.model.api.podium.challenges.PodiumContributorResponse
import com.app.messej.data.model.api.podium.challenges.PodiumCreateChallengeRequest
import com.app.messej.data.model.api.podium.challenges.PodiumGiftSupportersResponse
import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import com.app.messej.data.model.api.podium.challenges.YallaGuysJoinResponse
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.entity.YallaGuysChallenge
import com.app.messej.data.model.enums.AcceptDecline
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.ConfirmDecline
import com.app.messej.data.model.enums.MaidanTab
import com.app.messej.data.model.enums.PodiumActionType
import com.app.messej.data.model.enums.PodiumBlockFrom
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.PodiumPauseGift
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.TheaterJoinType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.socket.PodiumAssemblyStopSpeakingPayload
import com.app.messej.data.repository.mediators.PodiumListingRemoteMediator
import com.app.messej.data.repository.mediators.PodiumYallaListingRemoteMediator
import com.app.messej.data.repository.pagingSources.PodiumAdminsListDataSource
import com.app.messej.data.repository.pagingSources.PodiumBlockedListDataSource
import com.app.messej.data.repository.pagingSources.PodiumFrozenListDataSource
import com.app.messej.data.repository.pagingSources.PodiumGiftChallengeSupportersListDataSource
import com.app.messej.data.repository.pagingSources.PodiumLiveFriendsDataSource
import com.app.messej.data.repository.pagingSources.PodiumLiveUsersListDataSource
import com.app.messej.data.repository.pagingSources.PodiumMaidanChallengeHistoryPagingSource
import com.app.messej.data.repository.pagingSources.PodiumMaidanCompetitorStatsPagingSource
import com.app.messej.data.repository.pagingSources.PodiumMaidanTopSupportersDataSource
import com.app.messej.data.repository.pagingSources.PodiumRecordsDataSource
import com.app.messej.data.repository.pagingSources.PodiumSearchDataSource
import com.app.messej.data.repository.pagingSources.PodiumTheaterChargesPagingSource
import com.app.messej.data.repository.pagingSources.PodiumTheaterLikesPagingSource
import com.app.messej.data.repository.pagingSources.PodiumWaitingListDataSource
import com.app.messej.data.repository.pagingSources.PodiumsLiveListDataSource
import com.app.messej.data.repository.pagingSources.PodiumsLiveMaidanListDataSource
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.socket.repository.PodiumChallengeEventRepository
import com.app.messej.data.socket.repository.PodiumEventRepository
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import retrofit2.Response
import java.io.File

class PodiumRepository(val context: Application) {
    private var db = FlashatDatabase.getInstance(context)

    private val accountRepo: AccountRepository = AccountRepository(context)

    private val podiumEventRepo = PodiumEventRepository
    private val challengeEventRepo = PodiumChallengeEventRepository

    suspend fun createPodium(uuid: String, file: File?, name: String, about: String?, category: Int?, type: PodiumTypeEntry, goLive: Boolean, useProfilePhoto: Boolean?, kind: PodiumKind, audienceFee : Int?, stageFee : Int?,requiredRating:String?,
                             requiredRatingToComment: String?, requiredRatingToSpeak: String?, joiningFee: String?, speakingFee: Int?
    ): ResultOf<Podium> {
        return try {
            var imageBody: MultipartBody.Part? = null
            if (file != null) {
                val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
                imageBody = MultipartBody.Part.createFormData("file", file.name, requestFile)
            }

            val request = CreatePodiumRequest(
                tempId = uuid.toString(),
                type = type,
                name = name,
                about = about,
                categoryId = category,
                live = goLive,
                useProfileDp = useProfilePhoto,
                kind = kind,
                audienceFee = if (kind == PodiumKind.THEATER) audienceFee else speakingFee,
                stageFee = stageFee,
                requiredUserRating = requiredRating,
                requiredRatingToComment = requiredRatingToComment,
                requiredRatingToSpeak = requiredRatingToSpeak,
                joiningFee = joiningFee,
            )

            val dataBody = MultipartBody.Part.createFormData("data", Gson().toJson(request))
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).createPodium(imageBody, dataBody)
            val result = APIUtil.handleResponse(response)
            when (result) {
                is ResultOf.APIError -> {

                }

                is ResultOf.Error -> {

                }

                is ResultOf.Success -> {
                    val podium = result.value
                    db.getPodiumDao().insert(podium)
                }
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "createPodium: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun compressImage(file: File): File = MediaUtils.compressImageToTempFile(context, file.absolutePath)


    suspend fun updatePodium(
        podiumId: String,
        uuid: String, file: File?, name: String, about: String?, category: Int?, type: PodiumTypeEntry, goLive: Boolean,tempId:String, useProfilePhoto : Boolean?, kind: PodiumKind, audienceFee : Int?, stageFee : Int?,requiredRating:String?,
        requiredRatingToComment: String?, requiredRatingToSpeak: String?, joiningFee: String?, speakingFee: Int?
    ): ResultOf<Podium> {
        return try {
            var imageBody: MultipartBody.Part? = null
            if (file != null) {
                val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
                imageBody = MultipartBody.Part.createFormData("file", file.name, requestFile)
            }
            val request = UpdatePodiumRequest(
                tempId = tempId, type = type, name = name, about = about, categoryId = category, id = podiumId, live = goLive,
                useProfileDp = useProfilePhoto, kind = kind, audienceFee = if (kind == PodiumKind.THEATER) audienceFee else speakingFee, stageFee = stageFee,
                requiredUserRating = requiredRating, requiredRatingToComment = requiredRatingToComment,
                requiredRatingToSpeak = requiredRatingToSpeak,
                joiningFee = joiningFee,

            )
            val dataBody = MultipartBody.Part.createFormData("data", Gson().toJson(request))
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).updatePodium(podiumId, imageBody, dataBody)
            val result = APIUtil.handleResponse(response)
            when (result) {
                is ResultOf.APIError -> {

                }

                is ResultOf.Error -> {

                }

                is ResultOf.Success -> {
                    val newPodium = result.value
                    db.withTransaction {
                        val podium = db.getPodiumDao().getPodium(podiumId) ?: return@withTransaction
                        db.getPodiumDao().update(
                            podium.copy(
                                name = newPodium.name,
                                category = newPodium.category,
                                bio = newPodium.bio,
                                profilePic = newPodium.profilePic,
                                isLive = newPodium.isLive,
//                                type = newPodium.type,
                                createdTime = newPodium.createdTime,
                                updatedTime = newPodium.updatedTime,
                                audienceFee = newPodium.audienceFee,
                                stageFee = newPodium.stageFee,
                                requiredUserRating = newPodium.requiredUserRating,
                                requiredRatingToComment = newPodium.requiredRatingToComment,
                                requiredRatingToSpeak = newPodium.requiredRatingToSpeak,
                                joiningFee = newPodium.joiningFee
                            )
                        )
                    }
                }
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "updatePodium: ", e)
            ResultOf.Error(Exception(e))
        }
    }


    @OptIn(ExperimentalPagingApi::class)
    fun getMyPodiumsListingPager(): Pager<Int, Podium> {
        return Pager(config = PagingConfig(pageSize = 25, enablePlaceholders = false),
                     remoteMediator = PodiumListingRemoteMediator(tab = PodiumTab.MY_PODIUM, db, APIServiceGenerator.createService(PodiumAPIService::class.java)),
                     pagingSourceFactory = { db.getPodiumDao().podiumsPagingSource() })
    }

    fun getLivePodiumsListingPager(countCallback: (Int) -> Unit = {}): Pager<Int, Podium> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumsLiveListDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), countCallback) })
    }

    fun getPodiumSearchListingPager(tab: PodiumTab, keyword: String): Pager<Int, Podium> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumSearchDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), tab, keyword) })
    }

    fun getPodiumLiveUsersListingPager(podiumId: String, searchTerm: String? = null, exclude : UserCitizenship? = null, excludeReportedUser: Boolean? = null): Pager<Int, PodiumParticipant> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumLiveUsersListDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId, searchTerm, exclude = exclude, excludeReportedUser = excludeReportedUser) })
    }

    fun getPodiumWaitingListPager(podiumId: String, searchTerm: String? = null): Pager<Int, PodiumSpeaker> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumWaitingListDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId, searchTerm) })
    }

    fun getPodiumAdminsListPager(podiumId: String): Pager<Int, PodiumSpeaker> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumAdminsListDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId) })
    }

    fun getPodiumFrozenListPager(podiumId: String): Pager<Int, PodiumSpeaker> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumFrozenListDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId) })
    }

    fun getPodiumBlockedListPager(podiumId: String): Pager<Int, PodiumSpeaker> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumBlockedListDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId) })
    }

    fun getPodiumRecordsPager(podiumId: String): Pager<Int, PodiumRecord> {
        return Pager(
            config = PagingConfig(pageSize = 20, enablePlaceholders = false),
            pagingSourceFactory = { PodiumRecordsDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId) })
    }

    suspend fun getPodiumCategories(): ResultOf<PodiumCategoriesResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getPodiumCategories()
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getPodiumCategories: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun getMaidanListingPager(tab: MaidanTab, subTab: MaidanTab.MaidanSubTab, metaCallback: (PodiumResponse.MaidanMeta) -> Unit = {}): Pager<Int, Podium> {
        return Pager(config = PagingConfig(pageSize = 25, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumsLiveMaidanListDataSource(tab,subTab,APIServiceGenerator.createService(PodiumAPIService::class.java), metaCallback) })
    }


    fun getPodiumLiveData(id: String) = db.getPodiumDao().podiumLiveData(id)

    suspend fun sendPodiumInvitation(id: String, inviteDears: Boolean, inviteFans: Boolean, inviteLikers: Boolean): ResultOf<String> {
        return try {
            val request = PodiumInvitationRequest(
                inviteDears = inviteDears, inviteFans = inviteFans, inviteLikers = inviteLikers
            )
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).sendPodiumInvitation(id, request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updatePodium(pod: Podium) {
        db.getPodiumDao().update(pod)
    }

    suspend fun getPodiumDetails(id: String, invitedParticipants: String? = null): ResultOf<Podium> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getPodiumDetails(id,invitedParticipants)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                db.getPodiumDao().update(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPodiumWaitList(id: String): ResultOf<List<PodiumSpeaker>> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getWaitingList(id)
            return APIUtil.handleResponse(response).convertTo {
                ResultOf.Success(it.value.users)
            }
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getPodiumWaitList: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPodiumAdminsList(id: String): ResultOf<List<PodiumSpeaker>> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getAdminsList(id)
            return APIUtil.handleResponse(response).convertTo {
                ResultOf.Success(it.value.users)
            }
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getPodiumAdminsList: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPodiumAloneList(): ResultOf<List<Podium>> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getPodiumList(tab = PodiumTab.LIVE_PODIUM.serializedName(), page = 1, kind = PodiumKind.ALONE.serializedName())
            return APIUtil.handleResponse(response).convertTo {
                ResultOf.Success(it.value.podiums)
            }
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getPodiumAloneList: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    sealed class JoinResultOf<out T> {
        data class Success<out T>(val value: T) : JoinResultOf<T>()
        data class APIError(val error: PodiumJoinErrorResponse, val code: Int = 0) : JoinResultOf<Nothing>()
        data class Error(val exception: Exception) : JoinResultOf<Nothing>()
    }

    private fun <T> handleJoinResponse(response: Response<APIResponse<T>>): JoinResultOf<T> {
        return if (response.isSuccessful && response.code() == 200) {
            val result = response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            JoinResultOf.Success(result)

        } else {
            val error = PodiumJoinErrorResponse.parseError(response.errorBody())
            JoinResultOf.APIError(error, code = response.code())
        }
    }

    suspend fun goLive(id: String): JoinResultOf<PodiumJoinResponse> {
        return try {
            Log.w("AGORA", "repo: joinPodium")
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).goLive(id)
            val result = handleJoinResponse(response)
            if (result is JoinResultOf.Success) {
                podiumEventRepo.joinPodium(id, userId = accountRepo.user.id) //calling join event after agora join
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            JoinResultOf.Error(Exception(e))
        }
    }

    suspend fun joinPodium(id: String, hidden: Boolean = false): JoinResultOf<PodiumJoinResponse> {
        return try {
            Log.w("AGORA", "repo: joinPodium")
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).join(id)
            val result = handleJoinResponse(response)
            if (result is JoinResultOf.Success) {
                podiumEventRepo.joinPodium(id, userId = accountRepo.user.id) //calling join event after agora join
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            JoinResultOf.Error(Exception(e))
        }
    }

    fun getJoinError() {

    }

    fun checkExistingAgoraSession(id: String): AgoraEngineService.AgoraSession? {
        val session = AgoraEngineService.activeSession
        return if(session!=null) {
            return if (session.podiumId==id && session.valid) session
            else {
                session.leave()
                null
            }
        } else null
    }

    fun joinAgoraSession(id: String, kind: PodiumKind, token: String, listener: AgoraEngineService.ChannelEventListener) : ResultOf<AgoraEngineService.AgoraSession> {
        val session = AgoraEngineService.createSession(context,accountRepo.user.id,id,token,listener, kind.mainScreens>1)
        return if(session!=null) {
            ResultOf.Success(session)
        } else {
            ResultOf.getError("Unable to join channel")
        }
    }


    suspend fun requestToSpeak(podiumId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).requestToSpeak(podiumId)
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//                podiumEventRepo.enterPodiumWaitList(speaker,podiumId,DateTimeUtils.getZonedDateTimeNowAsString())
//            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }


    suspend fun requestToSpeak(podiumId: String, action: TheaterJoinType): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).requestToSpeakInTheater(podiumId,action.toString())
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun cancelRequestToSpeak(id: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).cancelRequestToSpeak(id)
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//                podiumEventRepo.exitPodiumWaitList(id,accountRepo.user.id)
//            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun allowUserToSpeak(podiumId: String, speaker: PodiumSpeaker): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).allowUserToSpeak(podiumId, speaker.id)
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//                podiumEventRepo.enterSpeakerList(speaker,podiumId,DateTimeUtils.getZonedDateTimeNowAsString())
//            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun leavePodium(podiumId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).leavePodium(podiumId)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                Log.d("PodiumLVM", "leave api success")
                podiumEventRepo.leavePodium(podiumId, accountRepo.user.id)

                Log.d("PodiumLVM", "leave socket success")
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "leavePodium: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun closePodium(podiumId: String): ResultOf<APIResponse<String>> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).closePodium(podiumId)
            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                podiumEventRepo.leavePodium(podiumId, accountRepo.user.id)
                db.getPodiumDao().delete(podiumId)
            } else if (result is ResultOf.APIError) {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "closePodium: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun exitPodium(podiumId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).exitPodium(podiumId)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "exitPodium: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPodiumById(podiumId: String): Podium? {
        return withContext(Dispatchers.IO) {
            db.getPodiumDao().getPodium(podiumId)
        }
    }

    suspend fun blockUnblockPodiumParticipant(podiumId: String, userId: Int, action: BlockUnblockAction, blockFrom: PodiumBlockFrom? = null): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).blockPodiumParticipant(podiumId, userId, action.toString(), blockFrom?.toString())
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "blockUnblockPodiumParticipant: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPodiumFrozenUsers(id: String): ResultOf<List<PodiumSpeaker>> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getPodiumFrozenUserList(id)
            return APIUtil.handleResponse(response).convertTo {
                ResultOf.Success(it.value.users)
            }
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun freezeToggle(podiumId: String, userId: Int, freeze: Boolean): ResultOf<String> {
        return try {
            val response = if (freeze) {
                APIServiceGenerator.createService(PodiumAPIService::class.java).freezeParticipant(podiumId, userId)
            } else {
                APIServiceGenerator.createService(PodiumAPIService::class.java).unfreezeParticipant(podiumId, userId)
            }
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//                podiumEventRepo.freezeUser(podiumId,userId,freeze)
//            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun endParticipantSpeakingSession(podiumId: String, participantId: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).endParticipantSpeakingSession(podiumId, participantId)
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//                podiumEventRepo.exitSpeakerList(podiumId,participantId)
//            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "endParticipantSpeakingSession: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun endSelfSpeakingSession(podiumId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).endSelfSpeakingSession(podiumId)
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//                podiumEventRepo.exitSpeakerList(podiumId,accountRepo.user.id)
//            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "endParticipantSpeakingSession: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun muteUnMuteUser(podiumId: String, user: PodiumSpeaker): ResultOf<String> {
        return try {
            val action = if (user.muted) "unmute" else "mute"
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).muteParticipant(podiumId, user.id,action)
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//                podiumEventRepo.toggleUserMute(podiumId, user.id, !user.muted)
//            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun muteUnMuteSelf(podiumId: String, muted: Boolean): ResultOf<String> {
        return try {
            val action = if (muted) "unmute" else "mute"
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).muteSelf(podiumId, action)
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//                podiumEventRepo.toggleUserMute(podiumId, accountRepo.user.id, !muted)
//            }
            podiumEventRepo.toggleUserMute(podiumId, accountRepo.user.id, !muted)
            return result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendPodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun likePodium(podiumId: String): Boolean {
        return podiumEventRepo.likePodium(podiumId, accountRepo.user.id)
    }

    suspend fun sendPaidLike(request: PaidLikePayload): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).sendPaidLike(request)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }



    suspend fun appointAnAdmin(podiumId: String, participantId: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).appointAnAdmin(podiumId, participantId)
            return APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "appointAnAdmin: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun dismissAnAdmin(podiumId: String, participantId: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).dismissAnAdmin(podiumId, participantId)
            val result = APIUtil.handleResponseWithoutResult(response)

            if (result is ResultOf.Success) {
                db.withTransaction {
                    db.getPodiumDao().delete(podiumId)
                }
            }
            return result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "dismissAnAdmin: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun cancelAdminAppoint(podiumId: String, participantId: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).cancelAdminAppoint(podiumId, participantId)
            return APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "cancelAdminAppoint: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun declinePodiumInvitation(podiumId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).declinePodiumInvitation(podiumId)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                db.withTransaction {
                    db.getPodiumDao().delete(podiumId)
                }
            }
            return result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "declinePodiumInvitation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun declineRequestToSpeak(podiumId: String, participantId: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).declineRequestToSpeak(podiumId, participantId)
            return APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "declineRequestToSpeak: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun respondAdminInviteAction(podiumId: String, participantId: Int, action: PodiumActionType): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).confirmAdminAppointRequest(podiumId, participantId, action.toString())
            return when(val result = APIUtil.handleResponseWithoutResult(response)) {
                is ResultOf.Success -> {
                    db.withTransaction {
                    val podium = db.getPodiumDao().getPodium(podiumId) ?: return@withTransaction
                    db.getPodiumDao().update(
                        podium.copy(
                            invitedToBeAdmin = false
                        )
                    )
            }
                    ResultOf.Success("ok")
                }
                is ResultOf.APIError -> ResultOf.APIError(result.error)
                is ResultOf.Error -> ResultOf.Error(result.exception)
            }
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "confirmAdminAppointRequest: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPodiumAbout(podiumId: String): ResultOf<PodiumAbout> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getPodiumAbout(podiumId)
            val result = APIUtil.handleResponse(response)
            return result.convertTo {
                ResultOf.Success(it.value)
            }
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "getPodiumAbout: ", e)
            ResultOf.Error(Exception(e))
        }
    }
    suspend fun setPodiumSettings(podiumId: String, disableChat: Boolean? = null, disableLikes: Boolean? = null, disableMic : Boolean? = null): ResultOf<String> {
            return try {
                val request = PodiumSettingsRequest(chatDisabled = disableChat,likesDisabled = disableLikes, micDisabled = disableMic)
                val response = APIServiceGenerator.createService(PodiumAPIService::class.java).podiumSettings(podiumId, request)
                return APIUtil.handleResponseWithoutResult(response)
            } catch (e: Exception) {
                Log.e(Constants.FLASHAT_TAG, "setPodiumSettings: ", e)
                ResultOf.Error(Exception(e))
            }
        }

    suspend fun setPausePodiumGift(podiumId: String, pauseGift: PodiumPauseGift?): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).podiumPauseGift(podiumId, pauseGift.toString())
            return APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setPodiumSettings: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun setPauseUserPodiumGift(podiumId: String, participantId: Int, pauseGift: PodiumPauseGift?): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).podiumPauseUserGift(podiumId,participantId, pauseGift.toString())
            return APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setPodiumSettings: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deletePodium(podiumId: String) : ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).deletePodium(podiumId)
            val result =  APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                db.withTransaction {
                    db.getPodiumDao().delete(podiumId)
                }
            }
            return result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "deletePodium: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPodiumUserLikesCoins(podiumId: String, participantId: Int): ResultOf<PodiumUserLikesCoinsResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getPodiumUserLikesCoins(podiumId, participantId)
            val result = APIUtil.handleResponse(response)
            return result.convertTo {
                ResultOf.Success(it.value)
            }
        } catch (e: Exception){
            Log.e(Constants.FLASHAT_TAG, "getPodiumUserLikes: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun showInMainScreen(podiumId: String, participantId: Int): Boolean {
        return podiumEventRepo.showInMainScreen(podiumId, participantId)
    }

    suspend fun createChallenge(
        podiumId: String,
        type: ChallengeType,
        facilitatorId: Int? = null,
        isYalla: Boolean? = null,
        participate: Boolean? = null,
        prize: Int? = null,
    ): ResultOf<PodiumChallenge> {
        return try {
            val request = PodiumCreateChallengeRequest(type, facilitatorId, isYalla, participate, prize)
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).createChallenges(podiumId, request)
            return APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "createChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun appointFacilitator(podiumId: String, challengeId: String, facilitatorId: Int): ResultOf<PodiumChallenge> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).appointFacilitator(podiumId, challengeId, facilitatorId)
            return APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "appointFacilitator: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun acceptDeclineFacilitator(podiumId: String, challengeId: String, facilitatorId: Int, accept :Boolean = true) : ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).acceptDeclineFacilitator(podiumId, challengeId, facilitatorId, if (accept) ConfirmDecline.CONFIRM else ConfirmDecline.DECLINE)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "acceptDeclineFacilitator: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun acceptDeclineContributor(podiumId: String, challengeId: String, contributorId: Int, accept :Boolean = true) : ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).acceptDeclineContributor(podiumId, challengeId, contributorId, if (accept) ConfirmDecline.CONFIRM else ConfirmDecline.DECLINE)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "acceptDeclineContributor: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun setChallengeTimer(podiumId: String, challengeId: String, durationInSeconds: Int) : ResultOf<PodiumChallenge> {
        return try {
            val request = PodiumChallengeTimerRequest(duration = durationInSeconds)
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).setChallengeTimer(podiumId, challengeId, request)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setChallengeTimer: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun appointContributor(podiumId: String, challengeId: String, type: ChallengeContributionType, contributors: List<AbstractUser>?, fee: Long? = null) : ResultOf<PodiumContributorResponse> {
        return try {
            val request  = when(type) {
                ChallengeContributionType.SELF, ChallengeContributionType.SPEAKERS, ChallengeContributionType.CONTRIBUTOR -> {

                    val contributorsForChallenge = arrayListOf<Contributor>()
                    val contributionFee = fee?: 0
                    contributors?.forEach {
                        contributorsForChallenge.add(
                            Contributor(
                                contributorId = it.id, contributorFee = contributionFee
                            )
                        )
                    }

                    PodiumChallengeContributorRequest(
                        contributorType = type, contributors = contributorsForChallenge
                    )
                }
                ChallengeContributionType.FREE -> {
                    PodiumChallengeContributorRequest(
                        contributorType = type
                    )
                }
                else -> return ResultOf.getError("Invalid ChallengeContributionType")
            }
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).appointContributor(podiumId, challengeId, request)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "appointContributor: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun startPodiumChallenge(podiumId: String, challengeId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).startPodiumChallenge(podiumId, challengeId)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "startPodiumChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun closePodiumChallenge(podiumId: String, challengeId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).closePodiumChallenge(podiumId, challengeId)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "startPodiumChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun getPodiumGiftChallengeSupportersListingPager(podiumId: String, challengeId: String? = null): Pager<Int, PodiumGiftSupportersResponse.ChallengeSupporter> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumGiftChallengeSupportersListDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId, challengeId) })
    }

    suspend fun deletePodiumChallenge(podiumId: String, challengeId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).deletePodiumChallenge(podiumId, challengeId)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "deletePodiumChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getChallengeDetails(podiumId: String, challengeId: String): ResultOf<PodiumChallenge> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getChallengeDetails(podiumId, challengeId)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getChallengeDetails: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun inviteToSpeak(podiumId: String, userId: Int, type: TheaterJoinType?, invitedForFree: Boolean = true): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).inviteToSpeak(podiumId, userId, type, invitedForFree)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "inviteChallengeParticipant: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun cancelInviteToSpeak(podiumId: String, userId: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).cancelInviteToSpeak(podiumId, userId)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "inviteChallengeParticipant: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun replyChallengeInvite(podiumId: String, accept: Boolean): ResultOf<String> {
        return try {
            val service = APIServiceGenerator.createService(PodiumAPIService::class.java)
            val response = service.replyChallengeSpeakerInvite(podiumId, if (accept) AcceptDecline.ACCEPT else AcceptDecline.DECLINE)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "inviteChallengeParticipant: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun replySpeakerInvite(podiumId: String, accept: Boolean, joinType: TheaterJoinType?, invitedForFree: Boolean?): ResultOf<String> {
        return try {
            val service = APIServiceGenerator.createService(PodiumAPIService::class.java)
            val response = service.replySpeakerInvite(podiumId, if (accept) AcceptDecline.ACCEPT else AcceptDecline.DECLINE, joinType, invitedForFree)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "inviteChallengeParticipant: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getChallengeQuestions(challengeId: String, podiumId: String): ResultOf<PodiumChallengeQuestionResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getFlagChallengeQuestions(podiumId, challengeId)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "createChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun createQuestion(challengeId: String,podiumId: String): ResultOf<PodiumChallengeQuestionResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).createQuestions(podiumId,challengeId)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "createChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun setAnswer(podiumId: String,challengeId: String,countryCode: String?, answerId: Int?): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).submitAnswer(podiumId, challengeId, FlagChallengeAnswerRequest(answerId = answerId, countryCode=countryCode))
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "createChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun inviteToSpeak(podiumId: String, challengeId: String, userIds: List<Int>): ResultOf<PodiumConFourInviteResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).inviteChallengeParticipant(podiumId, challengeId, PodiumConFourChallengeInviteParticipantRequest(userIds = userIds))
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "inviteChallengeParticipant: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun exitChallenge(podiumId: String, challengeId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).exitChallenge(podiumId, challengeId)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "exitChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun extendSpeakingTime(podiumId: String, userId: Int): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).extendSpeakingTime(podiumId, userId)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "extendSpeakingTime: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun stopSpeaking(payload: PodiumAssemblyStopSpeakingPayload): Boolean {
        return podiumEventRepo.stopSpeaking(payload)
    }

    suspend fun getPodiumBuyCameraDetails(): ResultOf<PodiumBuyCameraResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getPodiumBuyCameraDetails()
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getPodiumBuyCameraDetails: ", e)
            ResultOf.Error(Exception(e))
        }
    }
    suspend fun buyCameraTime(buyCamera:Boolean): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).buyCameraTime(buyCamera)
            val result =    APIUtil.handleResponseWithoutResult(response)
            return result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "buyCameraTime: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun getPodiumLiveFriendsPager(countCallback: (Int) -> Unit = {}): Pager<Int, PodiumFriend> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumLiveFriendsDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java),countCallback) })
    }

    suspend fun postHideLiveUsers( podiumId: String,requestBody: HideLiveUsersRequest): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).podiumHideLiveUsers(podiumId,requestBody)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "hideLiveUsersException: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun createMaidanChallenge(requestBody : CreateMaidanRequest): JoinResultOf<Podium> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).createMaidanChallenge(requestBody)
            val result = handleJoinResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "createMaidanChallenge: ", e)
            JoinResultOf.Error(Exception(e))
        }
    }

    suspend fun editMaidanChallenge(podiumId: String, requestBody : CreateMaidanRequest): ResultOf<MaidanEditResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).editMaidanChallenge(podiumId, requestBody)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "createMaidanChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun joinMaidanChallengeAsCompetitor(podiumId: String): JoinResultOf<Podium> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).joinMaidanChallenge(podiumId)
            val result = handleJoinResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "joinMaidanChallengeAsCompetitor: ", e)
            JoinResultOf.Error(Exception(e))
        }
    }

    suspend fun endMaidanChallenge(podiumId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).endMaidanChallenge(podiumId)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "endMaidanChallenge: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun sendMaidanLike(podiumId: String, challengeId: String): ResultOf<MaidanLikeResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).sendMaidanLike(podiumId, challengeId, MaidanLikeRequest(1))
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "sendMaidanLike: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun acceptMaidanCompetitor(podiumId: String, challengeId: String) : JoinResultOf<Podium?> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).respondToMaidanInvite(challengeId, ConfirmDecline.CONFIRM)
            return handleJoinResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "acceptDeclineContributor: ", e)
            JoinResultOf.Error(Exception(e))
        }
    }
    suspend fun declineMaidanCompetitor(podiumId: String, challengeId: String) : ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).respondToMaidanInvite(challengeId, ConfirmDecline.DECLINE)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "acceptDeclineContributor: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun maidanChallengeAgain(
        challengeId: String? = null,
        podiumId: String? = null,
        prize: Int? = null,
    ): ResultOf<PodiumChallenge> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).challengeAgain(MaidanChallengeAgainRequest(challengeId = challengeId, podiumId = podiumId, prize = prize))
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "maidanChallengeAgain: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun getMaidanTopSupportersListPager(podiumId: String, challengeId: String): Pager<Int, PodiumMaidanSupporter> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumMaidanTopSupportersDataSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId, challengeId) })
    }

    suspend fun getMaidanBeneficiaryList(
        keyword: String,
        prize: Int
    ): ResultOf<DealsBeneficiaryListResponse> {
        return try {
            val resp = APIServiceGenerator.createService(BusinessAPIService::class.java).getBeneficiaryList("created_on", "desc", null, "active", "50", keyword, "Premium", maidanInvite = 1, prize = prize)
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getMaidanStatsSummary(): ResultOf<MaidanStatsResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).getMaidanStatsSummary()
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getMaidanStatsSummary: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun getMaidanChallengeHistoryPager(): Pager<Int, PodiumMaidanSupporter> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumMaidanChallengeHistoryPagingSource(APIServiceGenerator.createService(PodiumAPIService::class.java)) })
    }

    fun getMaidanCompetitorStatsPager(): Pager<Int, PodiumMaidanSupporter> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumMaidanCompetitorStatsPagingSource(APIServiceGenerator.createService(PodiumAPIService::class.java)) })
    }

    fun getTheaterChargesPager(action: String, podiumId: String): Pager<Int, PodiumMaidanSupporter> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumTheaterChargesPagingSource(APIServiceGenerator.createService(PodiumAPIService::class.java), action = action , podiumId = podiumId) })
    }

    fun getTheaterLikesPager(podiumId: String): Pager<Int, PodiumMaidanSupporter> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { PodiumTheaterLikesPagingSource(APIServiceGenerator.createService(PodiumAPIService::class.java), podiumId = podiumId) })
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getYallaGuysListPager(podiumId: String): Pager<Int, YallaGuysChallenge> {
        return Pager(config = PagingConfig(pageSize = 10, enablePlaceholders = false),
                     remoteMediator = PodiumYallaListingRemoteMediator(podiumId, db, APIServiceGenerator.createService(PodiumAPIService::class.java)),
                     pagingSourceFactory = { db.getYallaGuysDao().yallaGuysPagingSource(podiumId) })
    }

    suspend fun joinYallaChallenge(podiumId: String, challengeId: String): ResultOf<YallaGuysJoinResponse> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).joinYallaChallenge(podiumId, challengeId)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getMaidanStatsSummary: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun pauseYallaGuys(podiumId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).pauseYallaGuys(podiumId)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getMaidanStatsSummary: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun resumeYallaGuys(podiumId: String): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).resumeYallaGuys(podiumId)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getMaidanStatsSummary: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun playAnthem(podiumId: String): ResultOf<Podium.Anthem> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java).playFlashatAnthem(podiumId)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "playAnthem: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getMaidanStats(userId:Int, competitorId: Int?): ResultOf<MaidanPlayerSummary> {
        return try {
            val response = APIServiceGenerator.createService(PodiumAPIService::class.java)
                .getMaidanStats(userId,competitorId)
            val result = APIUtil.handleResponse(response)
            Log.d("TEST","Repo getMaidanStats try block $result")
            result
        } catch (e: Exception) {
            Log.e("TEST", "getMaidanStats: ", e)
            ResultOf.Error(Exception(e))
        }
    }

}