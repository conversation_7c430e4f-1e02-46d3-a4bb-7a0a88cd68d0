package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumKind
import com.google.gson.annotations.SerializedName

data class CreatePodiumRequest(
    @SerializedName("about") val about: String?=null,
    @SerializedName("category_id") val categoryId: Int?=null,
    @SerializedName("live") val live: Boolean,
    @SerializedName("name") val name: String?=null,
    @SerializedName("type") val type: PodiumTypeEntry,
    @SerializedName("temp_id") val tempId:String?=null,
    @SerializedName("use_manager_profile_pic") val useProfileDp: Boolean?= false,
    @SerializedName("kind") val kind: PodiumKind,
    @SerializedName("audience_fee") val audienceFee: Int? = null,
    @SerializedName("stage_fee") val stageFee: Int? = null,
    @SerializedName("required_user_rating") val requiredUserRating: String? = null,
    @SerializedName("required_rating_to_comment") val requiredRatingToComment: String? = null,
    @SerializedName("required_rating_to_speak") val requiredRatingToSpeak: String? = null,
    @SerializedName("joining_fee") val joiningFee: String? = null,
)
{
    enum class PodiumTypeEntry{
       @SerializedName("PUBLIC") PUBLIC_GENERAL,
        @SerializedName("PRIVATE") PRIVATE_GENERAL,
        @SerializedName("MEN_ONLY") PUBLIC_MEN_ONLY,
        @SerializedName("WOMEN_ONLY") PUBLIC_WOMEN_ONLY,
        @SerializedName("MEN_ONLY_PRIVATE") PRIVATE_MEN_ONLY,
        @SerializedName("WOMEN_ONLY_PRIVATE") PRIVATE_WOMEN_ONLY;

        companion object {
            fun combine(type: Podium.PodiumType, entry: PodiumEntry): PodiumTypeEntry {
                return when(type) {
                    Podium.PodiumType.PUBLIC -> when(entry) {
                        PodiumEntry.GENERAL -> PUBLIC_GENERAL
                        PodiumEntry.MEN_ONLY -> PUBLIC_MEN_ONLY
                        PodiumEntry.WOMEN_ONLY -> PUBLIC_WOMEN_ONLY
                    }
                    Podium.PodiumType.PRIVATE -> when(entry) {
                        PodiumEntry.GENERAL -> PRIVATE_GENERAL
                        PodiumEntry.MEN_ONLY -> PRIVATE_MEN_ONLY
                        PodiumEntry.WOMEN_ONLY -> PRIVATE_WOMEN_ONLY
                    }
                }
            }
        }

        fun superSet() = when(this) {
            PUBLIC_GENERAL -> arrayOf()
            PUBLIC_MEN_ONLY -> arrayOf(PUBLIC_GENERAL)
            PUBLIC_WOMEN_ONLY -> arrayOf(PUBLIC_GENERAL)
            PRIVATE_GENERAL -> arrayOf(PUBLIC_GENERAL)
            PRIVATE_MEN_ONLY -> arrayOf(PUBLIC_GENERAL, PRIVATE_GENERAL, PUBLIC_MEN_ONLY)
            PRIVATE_WOMEN_ONLY -> arrayOf(PUBLIC_GENERAL, PRIVATE_GENERAL, PUBLIC_WOMEN_ONLY)
        }
    }
}