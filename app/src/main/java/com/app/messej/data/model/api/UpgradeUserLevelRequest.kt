package com.app.messej.data.model.api

import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName

data class UpgradeUserLevelRequest(
    @SerializedName("upgraded_level") val upgradedLevel: UserCitizenship? = null,
    @SerializedName("automatic_upgrade") val automaticUpgrade: Boolean? = null,
    @SerializedName("is_restore") val isRestore: Boolean? = null
)
