package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.HuddleRequestsResponse
import com.app.messej.data.room.dao.UserDao
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1

class HuddleRequestsDataSource(private val api: ChatAPIService,
                               private val dao: UserDao, private val huddleId: Int, private val totalCountCallback:(Int) -> Unit): PagingSource<Int, HuddleRequestsResponse.HuddleRequest>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, HuddleRequestsResponse.HuddleRequest> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getHuddleRequests(
                    huddleId, page = currentPage
                )
                val responseData = mutableListOf<HuddleRequestsResponse.HuddleRequest>()
                val result = response.body()?.result
                val data = result?.requests ?: emptyList()
                totalCountCallback.invoke(result?.total?:0)

                val nicknames = if (data.isNotEmpty()) dao.getNickNames(data.map { it.memberId }) else emptyList()

                data.forEach { member ->
                    val nn = nicknames.find { it.userId == member.memberId }
                    nn?.nickName?.let {
                        member.memberName = it
                    }
                }

                responseData.addAll(data)
                val nextKey = if (!result?.nextPage!!) null else currentPage.plus(1)

                LoadResult.Page(
                    data = data,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, HuddleRequestsResponse.HuddleRequest>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}