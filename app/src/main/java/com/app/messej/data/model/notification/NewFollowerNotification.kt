package com.app.messej.data.model.notification

import com.google.gson.annotations.SerializedName

data class NewFollowerNotification(
    @SerializedName("associated_obj_id" ) var associatedObjId  : String? = null,
    @SerializedName("notification_type" ) var notificationType : String? = null,
    @SerializedName("thumbnail"         ) var thumbnail        : String? = null,
    @SerializedName("receiver_id"       ) var receiverId       : Int?    = null,
    @SerializedName("html_text"         ) var htmlText         : String? = null,
    @SerializedName("notification_id"   ) var notificationId   : Int?    = null,
    @SerializedName("message"           ) var message          : String? = null,
    @SerializedName("category"          ) var category         : String? = null,
    @SerializedName("sender_id"         ) var senderId         : Int?    = null
)
