package com.app.messej.data.model

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class Category(
    @SerializedName(value = "category_id", alternate = ["id"]) val categoryId: Int,
    @SerializedName("name") val name: String,
    @SerializedName("selected") val selected: Boolean = false
){
    class Converter {
        @TypeConverter
        fun decode(data: String?): Category? {
            data?: return null
            val type: Type = object : TypeToken<Category?>() {}.type
            return Gson().fromJson<Category>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: Category?): String? {
            return Gson().toJson(someObjects)
        }
    }
}