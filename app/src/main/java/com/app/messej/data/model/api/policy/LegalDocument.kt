package com.app.messej.data.model.api.policy

import com.google.gson.annotations.SerializedName

data class LegalDocument(
    @SerializedName("description"       ) var description   : String,
    @SerializedName("effective_date"    ) var effectiveDate : String,
    @SerializedName("enabled"           ) var enabled       : <PERSON><PERSON><PERSON>,
    @SerializedName("id"                ) var id            : Int,
    @SerializedName("key_changes"       ) var keyChanges    : String,
    @SerializedName("latest"            ) var latest        : <PERSON><PERSON><PERSON>,
    @SerializedName("notify_user"       ) var notifyUser    : <PERSON><PERSON><PERSON>,
    @SerializedName("policy_type"       ) var policyType    : String,
    @SerializedName("published_date"    ) var publishedDate : String,
    @SerializedName("status"            ) var status        : String,
    @SerializedName("time_created"      ) var timeCreated   : String,
    @SerializedName("time_updated"      ) var timeUpdated   : String,
    @SerializedName("title"             ) var title         : String
)
