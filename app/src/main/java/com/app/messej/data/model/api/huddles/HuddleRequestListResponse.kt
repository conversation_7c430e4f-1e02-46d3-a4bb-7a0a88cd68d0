package com.app.messej.data.model.api.huddles


import com.app.messej.data.model.entity.PublicHuddle
import com.google.gson.annotations.SerializedName

data class HuddleRequestListResponse(
    @SerializedName("current_page"          ) val currentPage       : Int?                  = null,
    @SerializedName("huddle_participant"    ) val huddleParticipant : List<PublicHuddle>    = listOf(),
    @SerializedName("next_page"             ) val nextPage          : Boolean?              = null,
    @SerializedName("participant_total"     ) val participantTotal  : Int?                  = null
)