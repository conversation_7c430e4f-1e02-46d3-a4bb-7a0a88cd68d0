package com.app.messej.data.repository

import android.app.Application
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.model.entity.RecentSearch
import com.app.messej.data.model.enums.RecentSearchType
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.DateTimeUtils

class RecentSearchRepository(private val context: Application) {

    private val db = FlashatDatabase.getInstance(context)

    companion object {
        //Constant that holds the maximum number of rows that can be saved in the RecentSearch table
        private const val RECENT_SEARCH_DB_ROW_LIMIT = 20
    }

    fun getRecentSearchPager(recentSearchType: RecentSearchType) = Pager(
        config = PagingConfig(pageSize = 50, enablePlaceholders = false),
        pagingSourceFactory = { db.getRecentSearchDao().getRecentSearches(recentSearchType) }
    )

    suspend fun insertSearch(keyword: String, recentSearchType: RecentSearchType) {
        if (keyword.isNotBlank()) {
            db.getRecentSearchDao().apply {
                //Delete old record if the user searched for same keyword
                clearDuplicateRecentSearches(keyword, recentSearchType)
                //Insert the keyword to DB
                insert(RecentSearch(keyword = keyword, timeCreated = DateTimeUtils.getZonedDateTimeNowAsString(), screenType = recentSearchType))
                //Get the total number of recent search records in DB and will delete old records if the total number exceeds RECENT_SEARCH_DB_ROW_LIMIT
                getRecentSearchesCount(recentSearchType).let {
                    if (it > RECENT_SEARCH_DB_ROW_LIMIT) {
                        val deleteCount = it - RECENT_SEARCH_DB_ROW_LIMIT
                        deleteOldRecentSearches(deleteCount, recentSearchType)
                    }
                }
            }
        }
    }

    suspend fun clearRecentSearch(){
        db.getRecentSearchDao().clearRecentSearches()
    }
}