package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.api.socialAffairs.SocialAffairUser.Companion.dummySocialUser
import com.app.messej.data.model.enums.SocialCaseStatus
import com.app.messej.data.model.enums.SocialVoteAction
import com.google.gson.annotations.SerializedName

data class SocialCasesListResponse(
    @SerializedName(value = "has_next") val hasNext: <PERSON>ole<PERSON>? = null,
    @SerializedName(value = "data") val cases: List<SocialCaseInfo>? = null,
    @SerializedName(value = "new_cases_count") val newCasesCount: Int?,
    @SerializedName(value = "donate_cases_count") val donateCasesCount: Int?
) {
    companion object {
        val dummySocialCase = SocialCaseInfo(
            id = 1,
            targetAmount = 100.00,
            receivedAmount = 50.00,
            caseTitle = "Test Case",
            caseDescription = "Test Description",
            supportVotes = 10,
            opposedVotes = 6,
            netVotes = 4,
            requestedUser = null,
            proofFiles = null,
            userDetail = dummySocialUser,
            status = SocialCaseStatus.NEW,
            timeCreated = "2025-07-15T05:01:54",
            requestType = null,
            amountRequested = 121.00,
            voteStatus = SocialVoteAction.Oppose,
            isVoted = false
        )
    }
}

