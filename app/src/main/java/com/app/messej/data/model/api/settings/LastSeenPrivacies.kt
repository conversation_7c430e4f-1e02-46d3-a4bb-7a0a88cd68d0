package com.app.messej.data.model.api.settings

import com.google.gson.annotations.SerializedName

data class LastSeenPrivacies(
    @SerializedName("lastSeen")
    val lastSeen: LastSeenPrivacy? = LastSeenPrivacy(),
    @SerializedName("hideStatus")
    val hideStatus: HideStatusPrivacy? = HideStatusPrivacy()
) {
    data class LastSeenPrivacy(
        @SerializedName("audience")
        val audience: List<String> = listOf(),
        @SerializedName("privacy")
        val privacy: String? = ""
    )

    data class HideStatusPrivacy(
        @SerializedName("audience")
        val audience: List<String> = listOf(),
        @SerializedName("privacy")
        val privacy: String? = ""
    )
}