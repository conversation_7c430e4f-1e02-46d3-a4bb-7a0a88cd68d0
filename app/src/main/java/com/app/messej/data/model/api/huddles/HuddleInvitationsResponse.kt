package com.app.messej.data.model.api.huddles

import com.google.gson.annotations.SerializedName

data class HuddleInvitationsResponse(
    @SerializedName("invitations" ) var invitations : ArrayList<HuddleInvitation> = arrayListOf(),
    @SerializedName("next_page"   ) var nextPage    : String?                = null,
    @SerializedName("page"        ) var page        : Int?                   = null,
    @SerializedName("total"       ) var total       : Int?                   = null
){
    data class HuddleInvitation (

        @SerializedName("invitation_id"    ) var invitationId    : Int?     = null,
        @SerializedName("is_premium"       ) var isPremium       : Boolean? = null,
        @SerializedName("member_id"        ) var memberId        : Int,
        @SerializedName("member_name"      ) var memberName      : String?  = null,
        @SerializedName("member_thumbnail" ) var memberThumbnail : String?  = null,
        @SerializedName("member_username"  ) var memberUsername  : String?  = null,
        @SerializedName("status"           ) var status          : HuddleRequestsResponse.Status?  = null,
        @SerializedName("verified"         ) var verified        : Boolean? = null

    ){
        val isBlocked: Boolean
            get() {
                return status == HuddleRequestsResponse.Status.BLOCKED
            }

        val isRequested: Boolean
            get() {
                return status == HuddleRequestsResponse.Status.REQUESTED
            }

        val isInvited: Boolean
            get() {
                return status == HuddleRequestsResponse.Status.INVITED
            }

        val isDeclined: Boolean
            get() {
                return status == HuddleRequestsResponse.Status.DECLINED
            }

        val isAccepted: Boolean
            get() {
                return status == HuddleRequestsResponse.Status.ACCEPTED
            }
    }
}
