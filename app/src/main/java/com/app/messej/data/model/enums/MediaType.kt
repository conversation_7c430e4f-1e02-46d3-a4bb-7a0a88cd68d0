package com.app.messej.data.model.enums

import com.google.gson.TypeAdapter
import com.google.gson.annotations.JsonAdapter
import com.google.gson.annotations.SerializedName
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter
import java.io.IOException

@JsonAdapter(MediaType.MediaTypeAdapter::class)
enum class MediaType(val code: String) {
    @SerializedName("IMAGE") IMAGE("IMAGE"),
    @SerializedName("AUDIO") AUDIO("AUDIO"),
    @SerializedName("VIDEO") VIDEO("VIDEO"),
    @SerializedName("DOCUMENT") DOCUMENT("DOCUMENT");


    override fun toString(): String {
        return code
    }

    companion object {
        infix fun from(value: String): MediaType? = values().firstOrNull { it.code.lowercase() == value }
    }

    class MediaTypeAdapter: TypeAdapter<MediaType>() {
        @Throws(IOException::class)
        override fun write(out: JsonWriter, value: MediaType?) {
            if (value == null) {
                out.nullValue()
            } else {
                out.value(value.code)
            }
        }

        @Throws(IOException::class)
        override fun read(reader: JsonReader): MediaType? {
            return if (reader.peek() === JsonToken.NULL) {
                reader.nextNull()
                null
            } else {
                from(reader.nextString().lowercase())
            }
        }
    }
}