package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class PrivateChatDeletePayload(
    @SerializedName("messages")  val messages: List<MessagesToDelete>,
    @SerializedName("deleteForEveryone")  val deleteForEveryone: Boolean,
): SocketEventPayload() {
    data class MessagesToDelete(
        @SerializedName("message_id") val messageId: String,
        @SerializedName("room_id") val roomId: String,
    )
}
