package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

@Entity(tableName = EntityDescriptions.TABLE_HUDDLE_INTERVENTIONS)
@TypeConverters(
    PublicHuddleInterventions.Converter::class,
)
/**
 * Represents interventions made to huddle participants, used for updating the state of the action popup menus
 */
data class PublicHuddleInterventions(
    @SerializedName("huddle_id"                       ) @ColumnInfo(name = COLUMN_ID                  ) @PrimaryKey(autoGenerate = false) val huddleId : Int,
    @SerializedName("admin_blocked_users_list"        ) @ColumnInfo(name = "blocked_from_huddle"      ) val blockedFromHuddle                          : List<Int>,
    @SerializedName("admin_request_sent_users_list"   ) @ColumnInfo(name = "admin_invited"            ) val invitedToBeAdmin                           : List<Int>,
    @SerializedName("user_blocked_ids"                ) @ColumnInfo(name = "user_blocked"             ) val userBlocked                                : List<Int>,
    @SerializedName("comment_banned_users_list"       ) @ColumnInfo(name = "comment_banned"    , defaultValue = "[]"     ) val commentBanned                              : List<Int> = emptyList(),
    @SerializedName("post_banned_users_list"          ) @ColumnInfo(name = "post_banned"    , defaultValue = "[]"          ) val postBanned                                 : List<Int> = emptyList(),
    @SerializedName("reply_banned_users_list"         ) @ColumnInfo(name = "reply_banned"  , defaultValue = "[]"           ) val replyBanned                                : List<Int> = emptyList(),
) {
    companion object {
        const val COLUMN_ID = "id"
    }

    data class UserIntervention (
        val blockedFromHuddle: Boolean,
        val invitedToBeAdmin: Boolean,
        val userBlocked: Boolean,
        val commentBanned: Boolean,
        val postBanned:Boolean,
        val replayBanned:Boolean
    )

    fun forUser(id: Int): UserIntervention {
        return UserIntervention(
            blockedFromHuddle = blockedFromHuddle.find { it == id }!=null,
            invitedToBeAdmin = invitedToBeAdmin.find { it == id }!=null,
            userBlocked = userBlocked.find { it == id }!=null,
            commentBanned = commentBanned.find { it == id }!=null,
            postBanned = postBanned.find { it == id }!=null,
            replayBanned = replyBanned.find { it == id }!=null,
        )
    }

    class Converter {

        @TypeConverter
        fun decodeInt(data: String?): List<Int>? {
            data?: return null
            val type: Type = object : TypeToken<List<Int>?>() {}.type
            return Gson().fromJson<List<Int>>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: List<Int>?): String? {
            return Gson().toJson(someObjects)
        }
    }
}
