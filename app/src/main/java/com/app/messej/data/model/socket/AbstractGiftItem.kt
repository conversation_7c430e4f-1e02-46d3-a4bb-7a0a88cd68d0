package com.app.messej.data.model.socket


import android.os.Parcelable
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.model.enums.GiftThumbnail
import com.app.messej.data.model.enums.GiftType
import com.app.messej.ui.utils.LocaleUtil
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

abstract class AbstractGiftItem : SocketEventPayload() {

    abstract val id: Int
    abstract val giftIdentifier: String?
    abstract val animationUrl: String?
    abstract val thumbnail: String?
    abstract val coins: Int?
    abstract val flix: Int?
    abstract val categoryName: String?
    abstract val giftName: String?
    abstract val giftType: GiftType?
    abstract val giftAnimationUrl: String?
    abstract val giftAnimationUrlAndroid: String?
    abstract val description: String?

    abstract val nameTranslations           : Translations?
    abstract val descTranslations           : Translations?
    abstract val specialOccasionDate              : String?

   abstract val  managerId                 :Int?
    abstract val  managerReceivedCoins         :Double?
    abstract val  categoryId                 :Int?


    val hasLottieAnimation:Boolean
        get() = giftThumbnail is GiftThumbnail.LottieAnimation

    val hasVideo: Boolean
        get() = giftAnimationUrlAndroid!=null

    val translatedName: String?
        get() = nameTranslations?.ofLocale(LocaleUtil.getAppLocale())?:giftName

    val translatedDescription: String?
        get() = descTranslations?.ofLocale(LocaleUtil.getAppLocale())?:description

    val giftThumbnail: GiftThumbnail?
        get() {
            try {

                val extension = if(!animationUrl.isNullOrBlank()) animationUrl?.split('.')?.last()
                else thumbnail?.split('.')?.last()

                return when(extension) {
                    "json" -> GiftThumbnail.LottieAnimation(animationUrl?: error("null"))
                    "gif" -> GiftThumbnail.GifImage(animationUrl?: error("null"))
                    else -> GiftThumbnail.StaticImage(animationUrl?:thumbnail?: error("null"))
                }
            } catch (e: Exception) {
                return null
            }
        }

    @Parcelize
    data class Translations(
        @SerializedName("english")      val english: String?,
        @SerializedName("hindi")        val hindi: String?,
        @SerializedName("tagalog")      val tagalog: String?,
        @SerializedName("arabic")       val arabic: String?,
        @SerializedName("french")       val french: String?,
        @SerializedName("russian")      val russian: String?,
        @SerializedName("spanish")      val spanish: String?,
        @SerializedName("kurdish")      val kurdish: String?,
        @SerializedName("persian")      val persian: String?
    ): Parcelable {
        fun ofLocale(locale: AppLocale): String? {
            return when(locale) {
                AppLocale.ENGLISH -> english
                AppLocale.ARABIC -> arabic
                AppLocale.SPANISH -> spanish
                AppLocale.HINDI -> hindi
                AppLocale.TAGALOG -> tagalog
                AppLocale.FRENCH -> french
                AppLocale.RUSSIAN -> russian
                AppLocale.KURDISH -> kurdish
                AppLocale.PERSIAN -> persian
            }
        }
    }
}