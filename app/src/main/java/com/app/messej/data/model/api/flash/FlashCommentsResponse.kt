package com.app.messej.data.model.api.flash

import com.app.messej.data.model.entity.PostCommentItem
import com.google.gson.annotations.SerializedName

data class FlashCommentsResponse(
    @SerializedName("items"    ) var items   : ArrayList<PostCommentItem> = arrayListOf(),
    @SerializedName("page"     ) var page    : Int?             = null,
    @SerializedName("pages"    ) var pages   : Int?             = null,
    @SerializedName("per_page" ) var perPage : Int?             = null,
    @SerializedName("total"    ) var total   : Int?             = null
)