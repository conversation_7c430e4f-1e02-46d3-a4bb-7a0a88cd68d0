package com.app.messej.data.model.api

 import com.google.gson.annotations.SerializedName

data class FileCaseRequest(
    @SerializedName("category_id") var categoryId: Int?,
    @SerializedName("reported_type") val reportedType: String = "USER",
    @SerializedName("reported_id") var reportedId: String? = null,
    @SerializedName("report_type") val reportType: String = "REPORT_ONLY",
    @SerializedName("user_id") var userId: Int?,
    @SerializedName("reason") var reason: String? = null,
    @SerializedName("proof_files") var proofFiles: List<String>? = null
)