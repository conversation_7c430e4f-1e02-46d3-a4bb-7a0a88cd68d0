package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName

data class SocialAffairUpgradeSupportStatus(
    @SerializedName("is_resident" ) val isResidentLevelSatisfied : Boolean? = null,
    @SerializedName("rating_satisfied" ) val ratingSatisfied  : Boolean? = null,
    @SerializedName("flashat_age_satisfied" ) val flashatAgeSatisfied : Boolean? = null,
    @SerializedName("is_gained_flix_satisfied" ) val gainedFlixSatisfied : Boolean? = null,
    @SerializedName("never_applied_payout" ) val neverAppliedPayout : Boolean? = null,
    @SerializedName("task_completed" ) val completedTasks : Boolean? = null,
    @SerializedName("sufficient_coin_balance" ) val haveCoinBalance : Boolean? = null,
    @SerializedName("send_gifts_satisfied" ) val isGiftSatisfied : Boolean? = null
)