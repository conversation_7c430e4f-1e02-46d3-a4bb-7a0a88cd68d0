package com.app.messej.data.model.api.auth

import com.google.gson.annotations.SerializedName

data class HomeAddress(
    @SerializedName("time_created") var timeCreated: String?=null,
    @SerializedName("time_updated") var timeUpdated: String?=null,
    @SerializedName("id") var id: Int?=null,
    @SerializedName("recipient_name") var recipientName: String?=null,
    @SerializedName("address_line1") var addressLine1: String?=null,
    @SerializedName("address_line2") var addressLine2: String?=null,
    @SerializedName("address_line3") var addressLine3: String?=null,
    @SerializedName("zip_code") var zipCode: String?=null,
    @SerializedName("city") var city: String?=null,
    @SerializedName("country") var country: String?=null,
    @SerializedName("phone") var phone: String?=null,
    @SerializedName("country_code") var countryCode: String?=null
    )
