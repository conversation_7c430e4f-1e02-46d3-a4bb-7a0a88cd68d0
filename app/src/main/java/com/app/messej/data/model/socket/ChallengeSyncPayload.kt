package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.BoxData
import com.app.messej.data.model.api.podium.challenges.ConFourData
import com.app.messej.data.model.api.podium.challenges.KnowledgeRaceData
import com.app.messej.data.model.api.podium.challenges.PenaltyData
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.app.messej.data.model.enums.ChallengeType
import com.google.gson.annotations.SerializedName

data class ChallengeSyncPayload(
    @SerializedName("challenge_id"      ) val challengeId           : String,
    @SerializedName("game_type"         ) val challengeType         : ChallengeType,
    @SerializedName("podium_id"         ) val podiumId              : String?,
    @SerializedName("server_time"       ) val serverTime            : String?,
    @SerializedName("status"            ) val status                : PodiumChallenge.ChallengeStatus,
    @SerializedName("end_timestamp_utc" ) var endTimeUTC            : Long?                 = null,
    @SerializedName("participants"      ) val participantScores     : List<PodiumChallengeScore>,
    @SerializedName("top_supporters"    ) val topSupporters         : List<PodiumChallenge.ChallengeUser>,
    // For con4
    @SerializedName("con4_data"         ) val connectFourData       : ConFourData?   = null,
    // For Penalty
    @SerializedName("penalty_data"      ) val penaltyData           : PenaltyData?   = null,
    // For Box Challenge
    @SerializedName("box_data"          ) val boxData               : BoxData?   = null,
    //For Knowledge Race
    @SerializedName("knowledge_data"    ) val knowledgeRaceData     : KnowledgeRaceData?   = null,
    ) : SocketEventPayload() {

    fun isOutdated(challenge: PodiumChallenge): Boolean {
        return if(status== PodiumChallenge.ChallengeStatus.LIVE) {
            return when(challengeType) {
                ChallengeType.LIKES,ChallengeType.GIFTS,ChallengeType.FLAGS,ChallengeType.MAIDAN -> {
                    participantScores.any { ps -> (challenge.participantScores?.find { it.id == ps.id }?.score ?: 0.0) > ps.score }
                }
                ChallengeType.CONFOUR -> {
                    val tst = connectFourData?.parsedTurnStartTime?: return true
                    challenge.conFourData?.parsedTurnStartTime?.isAfter(tst)==true
                }
                ChallengeType.PENALTY -> {
                    penaltyData?.activeTurn?.isLesserThan(challenge.penaltyData?.activeTurn)==true
                }
                ChallengeType.BOXES -> {
                    val tst = boxData?.parsedTurnStartTime?: return false
                    challenge.boxData?.parsedTurnStartTime?.isAfter(tst)==true
                }
                ChallengeType.KNOWLEDGE -> {
                    val tst = knowledgeRaceData?.parsedTurnEndTime?: return false
                    challenge.knowledgeRaceData?.parsedTurnEndTime?.isAfter(tst)==true
                }
            }
        } else false
    }
}