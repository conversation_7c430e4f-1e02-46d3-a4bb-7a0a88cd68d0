package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.SocialAffairAPIService
import com.app.messej.data.model.api.socialAffairs.MySupportListResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SocialMyPersonalRequestsDataSource(
    private val apiService: SocialAffairAPIService,
    private val isDraft: Boolean = false,
    private val responseCallBack: (MySupportListResponse?) -> Unit
) : PagingSource<Int, MySupportListResponse.MySupportRequest>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, MySupportListResponse.MySupportRequest>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, MySupportListResponse.MySupportRequest> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = apiService.getMySupportRequests(page = currentPage, isDraft = isDraft)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val dataWithoutRequests = data.copy(mySupportList = null)
                responseCallBack(dataWithoutRequests)
                val nextKey = if (data.hasNext == true) currentPage + 1 else null
                LoadResult.Page(
                    data = data.mySupportList ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}