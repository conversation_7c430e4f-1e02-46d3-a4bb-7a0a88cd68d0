package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class ReportCaseStatus {
    @SerializedName(value = "INVESTIGATION_BUREAU") INVESTIGATION_BUREAU,
    @SerializedName(value = "APPEAL") APPEAL,
    @SerializedName(value = "ADVOCATES") ADVOCATES_UNION,
    @SerializedName(value = "JURY") JURY,
    @SerializedName(value = "PENDING_FINE") VERDICT,
    @SerializedName(value = "CLOSED") CLOSED,
}