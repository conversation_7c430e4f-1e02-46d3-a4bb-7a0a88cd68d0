package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.app.messej.data.model.api.podium.challenges.YallaGuysJoinResponse.YallaJoinStatus
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type
import java.time.ZonedDateTime
import kotlin.math.roundToInt

@Entity(
    tableName = EntityDescriptions.TABLE_YALLA_LIVE,
    indices = []
)
@TypeConverters(
    YallaGuysChallenge.Initiator.Converter::class,
    YallaGuysChallenge.YallaGuysParticipant.ListConverter::class
)
data class YallaGuysChallenge(
    @ColumnInfo(COLUMN_ID                       ) @PrimaryKey @SerializedName("challenge_id"      ) val challengeId: String,
    @ColumnInfo(COLUMN_PODIUM_ID                ) @SerializedName("podium_id"                     ) val podiumId: String,
    @ColumnInfo("game_type"                     ) @SerializedName("game_type"                     ) val gameType: ChallengeType,
    @ColumnInfo("initiator"                     ) @SerializedName("initiator"                     ) val initiator: Initiator,
    @ColumnInfo("is_more_participants_allowed"  ) @SerializedName("is_more_participants_allowed"  ) val isMoreParticipantsAllowed: Boolean,
    @ColumnInfo("min_participants_count"        ) @SerializedName("min_participants_count"        ) val minParticipantsCount: Int,
    @ColumnInfo("max_participants_count"        ) @SerializedName("max_participants_count"        ) val maxParticipantsCount: Int,
    @ColumnInfo(COLUMN_QUEUED                   ) @SerializedName("queued"                        ) val queued: Boolean = false,
    @ColumnInfo("status"                        ) @SerializedName("status"                        ) val status: YallaJoinStatus? = null,
    @ColumnInfo("participants_count"            ) @SerializedName("participants_count"            ) val participantsCount: Int,
    @ColumnInfo("prize"                         ) @SerializedName("prize"                         ) val prize: Double?,
    @ColumnInfo(COLUMN_TIME_CREATED             ) @SerializedName("time_created"                  ) val timeCreated: String?,
    @ColumnInfo("time_updated"                  ) @SerializedName("time_updated"                  ) val timeUpdated: String?,
    @ColumnInfo("participants"                  ) @SerializedName("participants"                  ) val participants: List<YallaGuysParticipant>?,
) {

    companion object {
        const val COLUMN_ID = "challenge_id"
        const val COLUMN_PODIUM_ID = "podium_id"
        const val COLUMN_QUEUED = "queued"
        const val COLUMN_TIME_CREATED = "time_created"

        val dummy: YallaGuysChallenge
            get() = YallaGuysChallenge(
                challengeId = "asdasdasd",
                podiumId = "qweqweqwe",
                gameType = ChallengeType.CONFOUR,
                initiator = Initiator(1,"Shahim"),
                isMoreParticipantsAllowed = true,
                minParticipantsCount = 2,
                maxParticipantsCount = 2,
                queued = false,
                participantsCount = 1,
                prize = 200.0,
                timeCreated = null,
                timeUpdated = null,
                participants = listOf(
                    YallaGuysParticipant(0,"Shahim",null,null),
                    YallaGuysParticipant(0,"Ajay",null,null),
                    YallaGuysParticipant(0,"Nikhil",null,null),
                    YallaGuysParticipant(0,"شهيم",null,null),
                )
            )
    }

    val waiting: Boolean
        get() = status == YallaJoinStatus.WAITING

    fun isParticipant(id: Int) = participants.orEmpty().any { it.id == id }

    data class Initiator(
        @SerializedName("id") val id: Int,
        @SerializedName("name") val name: String
    ) {
        class Converter {
            @TypeConverter
            fun decode(data: String?): Initiator? {
                data?: return null
                val type: Type = object : TypeToken<Initiator?>() {}.type
                return Gson().fromJson<Initiator>(data, type)
            }
            @TypeConverter
            fun encode(someObjects: Initiator?): String? {
                return Gson().toJson(someObjects)
            }
        }
    }

    data class YallaGuysParticipant(
        @SerializedName("id") val id: Int,
        @SerializedName("name") val name: String?,
        @SerializedName("profile_url") val profileUrl: String?,
        @SerializedName("thumbnail") val thumbnail: String?
    ) {
        class ListConverter {
            @TypeConverter
            fun decode(data: String?): List<YallaGuysParticipant>? {
                data?: return null
                val type: Type = object : TypeToken<List<YallaGuysParticipant>?>() {}.type
                return Gson().fromJson<List<YallaGuysParticipant>>(data, type)
            }
            @TypeConverter
            fun encode(someObjects: List<YallaGuysParticipant>?): String? {
                return Gson().toJson(someObjects)
            }
        }
    }

    val formattedPrize: String
        get() = prize?.roundToInt()?.toString().orEmpty()

    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)

    val parsedUpdatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeUpdated)
}