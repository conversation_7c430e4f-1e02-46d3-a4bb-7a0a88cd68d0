package com.app.messej.data.model.api.podium

import com.google.gson.annotations.SerializedName

data class PodiumInvitationRequest(
    @SerializedName("invite_my_huddles"   ) val inviteMyHuddles   : <PERSON><PERSON><PERSON> = false,
    @SerializedName("invite_huddle_admin" ) val inviteHuddleAdmin : <PERSON><PERSON><PERSON> = false,
    @SerializedName("invite_dears"        ) val inviteDears       : <PERSON><PERSON><PERSON> = false,
    @SerializedName("invite_fans"         ) val inviteFans        : <PERSON><PERSON><PERSON> = false,
    @SerializedName("invite_likers"       ) val inviteLikers      : <PERSON><PERSON><PERSON> = false
)
