package com.app.messej.data.model.api.podium

import com.app.messej.data.model.entity.YallaGuysChallenge
import com.google.gson.annotations.SerializedName

data class YallaGuysListResponse (
    @SerializedName("challenges") val challenges: List<YallaGuysChallenge>,
    @SerializedName("current_page") val currentPage: Int,
    @SerializedName("has_next") val hasNext: <PERSON><PERSON><PERSON>,
    @SerializedName("per_page") val perPage: Int,
    @SerializedName("total_items") val totalItems: Int,
    @SerializedName("total_pages") val totalPages: Int
)