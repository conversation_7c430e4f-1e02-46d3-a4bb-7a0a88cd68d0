package com.app.messej.data.model.socket

import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.MediaType
import com.google.gson.annotations.SerializedName

abstract class AbstractChatMessagePayload : SocketEventPayload() {

    abstract val message: String?

    abstract val color: ChatTextColor?

    @SerializedName("media") var media: String? = null
        protected set
    @SerializedName("media_type") var mediaType: MediaType? = null
        protected set
    @SerializedName("media_name") var mediaName: String? = null
        protected set
    @SerializedName("mime_type") var mimeType: String? = null
        protected set
    @SerializedName("s3_key") var s3Key: String? = null
        protected set

    // for images/video
    @SerializedName("media_height") var mediaHeight: String? = null
        protected set
    @SerializedName("media_width") var mediaWidth: String? = null
        protected set

    // for audio/video
    @SerializedName("media_duration") var mediaDuration: String? = null
        protected set

    // for video
    @SerializedName("media_size") var mediaSize: String? = null
        protected set
    @SerializedName("unicode") var emojiUnicode: String? = null
        protected set

    // for document
    @SerializedName("display_name") var documentDisplayName: String? = null
        protected set

    @SerializedName("reply_to") var replyTo: String? = null
    @SerializedName("reply_id") var replyId: String? = null

    open fun setMedia(meta: MediaMeta) {
        media = meta.s3Key
        mediaType = meta.mediaType
        mediaName = meta.mediaName
        mimeType = meta.mimeType
        s3Key = meta.s3Key
        mediaHeight = meta.mediaHeight
        mediaWidth = meta.mediaWidth
        mediaDuration = meta.mediaDuration
        mediaSize = meta.formattedSize
        documentDisplayName = meta.documentDisplayName
        emojiUnicode=meta.unicode

        if (emojiUnicode!=null) {
            media = meta.thumbnail
        }
    }

    val hasMedia: Boolean
        get() = media!=null
}