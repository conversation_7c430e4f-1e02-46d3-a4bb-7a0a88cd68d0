package com.app.messej.data.model.api.podium


import com.app.messej.data.model.entity.Podium
import com.google.gson.annotations.SerializedName

data class PodiumResponse(
    @SerializedName("podiums") val podiums: List<Podium>,
    @SerializedName("has_next") val hasNextPage: <PERSON><PERSON>an,
    @SerializedName("per_page") val perPage: Int,
    @SerializedName("current_page") val currentPage: Int,
    @SerializedName("total_pages") val totalPages: Int,
    @SerializedName("total_items") val totalItems: Int,

    @SerializedName("metadata") val maidanMeta: MaidanMeta?
) {
    data class MaidanMeta(
        @SerializedName("count_of_maidans_to_join") val joinCount: Int?,
        @SerializedName("count_of_maidans_to_support") val watchCount: Int?,
        @SerializedName("friends_maidan_count") val friendsCount: Int?,
        @SerializedName("receive_challenge_start_notification") val notifyStartChallenge: <PERSON><PERSON><PERSON>?,
        @SerializedName("receive_challenge_invitation") val receiveChallengeInvitation: Bo<PERSON>an?
    )
}