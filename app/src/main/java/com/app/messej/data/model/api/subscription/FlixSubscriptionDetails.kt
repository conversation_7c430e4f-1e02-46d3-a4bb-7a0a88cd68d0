package com.app.messej.data.model.api.subscription

import com.google.gson.annotations.SerializedName

data class FlixSubscriptionDetails(
    @SerializedName("amount"            ) val amount: Int?=null,
    @SerializedName("currency"          ) val currency: String?=null,
    @SerializedName("subscription_acive") val subscription_acive: Boolean? = false,
    @SerializedName("userid"            ) val userid: Int?=null,
    @SerializedName("type"              ) val type: String?=null,
    @SerializedName("timezone"          ) val timezone: String?=null,
    @SerializedName("purchase_time"     ) val purchaseTime: String?=null,
    @SerializedName("reason"            ) val reason:   String?=null,
    @SerializedName("success"           ) val success:  Boolean?=false,
    @SerializedName("toggle"            ) val toggle:  Boolean?=false,
    @SerializedName("subscription_method"           ) val subscriptionMethod:  String?=null,
    @SerializedName("flix_balance"      ) val flixBalance: Double?=null
)