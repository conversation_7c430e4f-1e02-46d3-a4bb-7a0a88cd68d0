package com.app.messej.data.socket.repository

import com.app.messej.MainApplication
import com.app.messej.data.model.socket.FlaxRatePayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object NotificationEventRepository : BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {
    private val accountRepo = AccountRepository(MainApplication.applicationInstance())
    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when (event) {
            ChatSocketEvent.RX_NOTIFICATION_COUNT_STATUS -> {
                onNewNotification(data)
            }
            ChatSocketEvent.RX_FLAX_UPDATE -> {
                onFlaxRateUpdate(data)
            }

            else -> return false
        }
        return true
    }

    private fun onFlaxRateUpdate(data: JSONObject) = runBlocking {
        val _data = Gson().fromJson<FlaxRatePayload>(data.toString())
        withContext(Dispatchers.IO) {
            var accountDetails = accountRepo.getAccountDetailsFlow().firstOrNull()
            if (accountDetails?.id == _data.userId) {
                accountDetails.flaxRatePercentage = _data.flaxPercentage
                accountDetails.isFlaxIncrement = _data.flaxIncrement
                accountRepo.saveAccountDetails(accountDetails)
        }
    }
    }


    private fun onNewNotification(data: JSONObject) = runBlocking {
        val unread = if (data.has("unread")) data.getInt("unread") else 0
        withContext(Dispatchers.IO) {
            FlashatDatastore().setNotificationCount(unread)
        }
    }

}