package com.app.messej.data.model.api.huddles

import com.google.gson.annotations.SerializedName

data class BlockUnblockHuddleUserResponse(
    @SerializedName("users" ) var users : ArrayList<User> = arrayListOf()
){
    data class User(
        @SerializedName("active"          ) var active         : Boolean? = null,
        @SerializedName("admin_status"    ) var adminStatus    : String?  = null,
        @SerializedName("huddle_id"       ) var huddleId       : Int?     = null,
        @SerializedName("member_id"       ) var memberId       : Int?     = null,
        @SerializedName("member_name"     ) var memberName     : String?  = null,
        @SerializedName("member_username" ) var memberUsername : String?  = null,
        @SerializedName("mute"            ) var mute           : Boolean? = null,
        @SerializedName("pin"             ) var pin            : String?  = null,
        @SerializedName("role"            ) var role           : String?  = null,
        @SerializedName("status"          ) var status         : String?  = null,
        @SerializedName("time_created"    ) var timeCreated    : String?  = null,
        @SerializedName("time_updated"    ) var timeUpdated    : String?  = null
    )
}
