package com.app.messej.data.room.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.entity.LocalPostatMedia
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.entity.PostatWithMedia
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class MyPostatDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(postat: Postat): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(postat: List<Postat>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(media: LocalPostatMedia): Long

    @Update
    abstract suspend fun update(postat: Postat): Int

    @Update
    abstract suspend fun update(media: LocalPostatMedia): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_MY_POSTAT} WHERE ${Postat.COLUMN_POSTAT_TYPE} = :type")
    abstract suspend fun deleteAll(type: Postat.PostatType)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_MY_POSTAT} WHERE ${Postat.COLUMN_ID} = :id")
    abstract suspend fun deletePostat(id: String)

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_MY_POSTAT} WHERE ${Postat.COLUMN_ID} = :id")
    abstract suspend fun getLocalPostatWithMedia(id: String): PostatWithMedia?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_MY_POSTAT} WHERE ${Postat.COLUMN_SEND_STATUS}= :status ORDER BY ${Postat.COLUMN_CREATED_TIME} ASC")
    abstract suspend fun getPostatByStatus(status: AbstractChatMessage.SendStatus): PostatWithMedia?

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_MY_POSTAT} WHERE ${Postat.COLUMN_ID} = :id LIMIT 1")
    abstract suspend fun getLocalPostat(id: String): Postat?

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POSTAT_MEDIA} WHERE ${LocalPostatMedia.COLUMN_MEDIA_ID} = :id LIMIT 1")
    abstract suspend fun getLocalPostatMedia(id: Int): LocalPostatMedia?

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POSTAT_MEDIA} WHERE ${LocalPostatMedia.COLUMN_POSTAT_ID} = :id")
    abstract suspend fun getLocalPostatMediaByPostatId(id: String): List<LocalPostatMedia>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_POSTAT_MEDIA} WHERE ${LocalPostatMedia.COLUMN_POSTAT_ID} = :id")
    abstract suspend fun deletePostatMedia(id: String)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_MY_POSTAT} ORDER BY ${Postat.COLUMN_POSTAT_TYPE} ASC, ${Postat.COLUMN_CREATED_TIME} DESC")
    abstract fun getPostatPagingSource(): PagingSource<Int, Postat>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_MY_POSTAT} WHERE ${Postat.COLUMN_POSTAT_TYPE} = :type")
    abstract fun getPostatPagingSourceByType(type: Postat.PostatType = Postat.PostatType.FINAL): PagingSource<Int, Postat>

}