package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.api.socialAffairs.SocialAffairUser.Companion.dummySocialUser
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

data class VotersResponse(
    @SerializedName("has_next") val haveNextPage: Boolean? = null,
    @SerializedName("data") val honoursList: List<Voter>? = null
) {

    data class Voter(
        @SerializedName("user_details") val userDetail : SocialAffairUser?,
        @SerializedName("time_created") val createdAt : String?,
        @SerializedName("supported") private val isSupported : Boolean?,
    ) {
        val voteAction: SocialVoteAction
            get() = if (isSupported == true) SocialVoteAction.Support else SocialVoteAction.Oppose

        private val zonedDateTime: ZonedDateTime?
            get() = DateTimeUtils.parseZonedDateTimeWithoutZ(createdAt)

        private val formattedTime: String
            get() = DateTimeUtils.formatTime24Hr(dt = zonedDateTime)

        val formatedDateAndTime: String
            get() = DateTimeUtils.format(dt = zonedDateTime, format = DateTimeUtils.FORMAT_DDMMYYYY_SLASHED) + "  |  " + formattedTime
    }

    companion object {
        val voterTestData = Voter(
            userDetail = dummySocialUser,
            createdAt = "2025-07-22T06:42:19",
            isSupported = false
        )
    }
}