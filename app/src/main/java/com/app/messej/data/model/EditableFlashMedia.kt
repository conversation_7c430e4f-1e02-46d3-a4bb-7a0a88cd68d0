package com.app.messej.data.model

import android.net.Uri
import androidx.media3.common.MediaItem
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.MediaResolution
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.MediaUtils.uri
import kotlin.math.ceil

data class EditableFlashMedia(
    override val path: String,
    val meta: FlashMeta,
    val sourceUri: Uri? = null,
    var edits: VideoEditInfo = VideoEditInfo()
    ): AbstractFlashMedia() {

    companion object {
        fun from(rec: TempFlashMedia) = EditableFlashMedia(
            rec.path, FlashMeta(
                MediaUtils.getDuration(rec.file, true), MediaUtils.getVideoResolution(rec.file.uri)
            )
        )

        fun from(uri : Uri) = EditableFlashMedia(
            uri.toString(), FlashMeta(
                MediaUtils.getDuration(uri, true), MediaUtils.getVideoResolution(uri)
            ), uri
        )
    }

    data class FlashMeta (
        val durationMs: Long,
        val resolution: MediaResolution
    ) {
        val formattedDuration: String
            get() = DateTimeUtils.formatSeconds(ceil(durationMs/1000f).toLong())
    }

    val isFromGallery: Boolean
        get() = sourceUri!=null

    override val mediaItem: MediaItem
        get() {
            val inputMediaItem = MediaItem.Builder()
            inputMediaItem.setUri(path)
            edits.trim?.let {
                inputMediaItem.setClippingConfiguration(it.clippingConfig)
            }
            return inputMediaItem.build()
        }
}