package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.HuddleReportedComment
import com.google.gson.annotations.SerializedName

data class HuddleReportedCommentsResponse(
    @SerializedName("current_page" ) var currentPage : Int,
    @SerializedName("next_page"    ) var nextPage    : Boolean,
    @SerializedName("reports"      ) var reports     : ArrayList<HuddleReportedComment> = arrayListOf(),
    @SerializedName("total"        ) var total       : Int = 0
)