package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.api.podium.PodiumSpeaker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PodiumBlockedListDataSource(private val api: PodiumAPIService, private val podiumId: String) : PagingSource<Int, PodiumSpeaker>() {
    companion object {
        private const val STARTING_KEY = 1
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PodiumSpeaker> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getPodiumBlockedUserList(podiumId, page = currentPage)
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (!data.hasNext) null else currentPage.inc()

                LoadResult.Page(
                    data = data.users, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, PodiumSpeaker>): Int? {
        return null
    }
}