package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.ContactETribeRequest
import com.app.messej.data.model.api.EditEtribeRequest
import com.app.messej.data.model.api.eTribe.ETribeResponse
import com.app.messej.data.model.api.eTribe.ETribeSuperStarMessageResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Query

interface ETribeAPIService {
    @GET("/huddles/e-tribe")
    @Headers("Accept: application/json")
    suspend fun getETribe(
        @Query("page") page: Int? = null,
        @Query("tab") tab: String? = null
    ): Response<APIResponse<ETribeResponse>>

    @PUT("/huddles/e-tribe")
    @Headers("Accept: application/json")
    suspend fun editTribeName(
        @Body req: EditEtribeRequest
    ): Response<APIResponse<Unit>>

    @POST("/huddles/e-tribe/notify-users")
    @Headers("Accept: application/json")
    suspend fun notifyUsers(
        @Body req: ContactETribeRequest
    ): Response<APIResponse<Unit>>

    @GET("/user/superstar-messages")
    @Headers("Accept: application/json")
    suspend fun getSuperStarMessages() : Response<APIResponse<ETribeSuperStarMessageResponse>>

}