package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class GiftContext {
   @SerializedName("huddle") GIFT_HUDDLE,
    @SerializedName("flash")GIFT_FLASH,
    @SerializedName("postat")GIFT_POSTAT,
    @SerializedName("buddies")GIFT_BUDDIES,
    @SerializedName("podium")GIFT_PODIUM,
    @SerializedName("podium")GIFT_PODIUM_MAIDAN,
    @SerializedName("idcard")GIFT_ID_CARD,
    @SerializedName("saythanks")GIFT_SAY_THANKS,
    @SerializedName("birthday")BIRTHDAY,
    @Serial<PERSON><PERSON><PERSON>("president")PRESIDENT,
    @SerializedName("postat_comment")GIFT_POSTAT_COMMENTS,
    @SerializedName("user_level_upgrade") GIFT_USER_LEVEL;
    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    val isPodiumType: <PERSON>ole<PERSON>
        get() = this == GIFT_PODIUM || this == GIFT_PODIUM_MAIDAN

}