package com.app.messej.data.api.external

import com.app.messej.data.model.api.GeoLocationResponse
import com.app.messej.ui.utils.MapUtils
import retrofit2.Response
import retrofit2.http.Headers
import retrofit2.http.POST

interface GoogleAPIService {
    @POST("/geolocation/v1/geolocate/?key=${MapUtils.GOOGLE_MAPS_API_KEY}")
    @Headers(
        "Accept: application/json",
        "X-Android-Package: com.example.myapp",
        "X-Android-Cert: 0123456789ABCDEF0123456789ABCDEF01234567"
    )
    suspend fun getGeoLocation() :Response<GeoLocationResponse>
}