package com.app.messej.data.model.api.legal

import com.app.messej.data.model.enums.CaseVerdictAction
import com.app.messej.data.model.enums.ReportCaseStatus
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.google.gson.annotations.SerializedName

data class LegalRecordsResponse(
    @SerializedName("reportings_list") val reportingList: List<ReportCase>? = null,
    @SerializedName("violations_count") val violationsCount: Int? = null,
    @SerializedName("ban_count") val banCount: Int? = null,
    @SerializedName("reportings_count") val reportingCount: Int? = null,
    @SerializedName("reports_count") val reportsCount: Int? = null,
    @SerializedName("next_page") val nextPage: Boolean? = null,
    @SerializedName("confirmed_violating") val guilty: Int? = null,
    @SerializedName("found_not_violating") val notGuilty: Int? = null,
    @SerializedName("appeal") val appeal: Int? = null,
    @SerializedName("payable_fines") val fine: Int? = null,
    @SerializedName("appointing_advocates") val advocates: Int? = null,
    @SerializedName("in_jury") val jury: Int? = null,
    @SerializedName("under_investigation") val underInvestigation: Int? = null,
    @SerializedName("reported_count") val reportedCount: Int? = null,
    @SerializedName("hidden_count") val hiddenCount: Int? = null,
) {

    data class ReportCase(
        @SerializedName("time_created") override val created: String?,
        @SerializedName("time_updated") override val updated: String?,

        @SerializedName("id") override val id: Int,

        @SerializedName("reason") override val reason: String,
        @SerializedName("reported_id") override val reportedId: String,
        @SerializedName("reported_type") override val reportedContentType: ReportContentType,
        @SerializedName("report_type") override val reportType: ReportType,
        @SerializedName("reporter_id") override val reporterId: Int,
        @SerializedName("user_id") override val userId: Int,

        @SerializedName("category_icon") override val categoryIcon: String?,
        @SerializedName("category") override val category: String,
        @SerializedName("category_id") override val categoryId: Int,
        @SerializedName("category_type_id") override val categoryTypeId: Int?,

        @SerializedName("status") override val caseStatus: ReportCaseStatus,

        @SerializedName("appeal_enabled_at") override val appealEnabledAt: String?,
        @SerializedName("advocate_fee") override val advocateFee: Int?,

        @SerializedName("is_guilty") override val guilty: Boolean?,
        @SerializedName("action") override val actionTaken: CaseVerdictAction?,

        @SerializedName("user_details") override val userDetails: DefendantUser? = null,
        @SerializedName("applicable_fee") override val applicableFee: Double?,
    ): AbstractCaseDetails()

}

