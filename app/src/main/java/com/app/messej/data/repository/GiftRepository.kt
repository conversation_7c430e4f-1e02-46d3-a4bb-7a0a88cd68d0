package com.app.messej.data.repository

import android.app.Application
import android.util.Log
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.amazonaws.mobile.config.AWSConfiguration
import com.app.messej.R
import com.app.messej.data.Constants
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.GiftAPIService
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.gift.CoinsConversion
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.api.gift.GiftNotificationResponse
import com.app.messej.data.model.api.gift.GiftPointToFlaxConvertRequest
import com.app.messej.data.model.api.gift.GiftPointToFlaxConvertResponse
import com.app.messej.data.model.api.gift.GiftReplyRequest
import com.app.messej.data.model.api.gift.GiftResponse
import com.app.messej.data.model.api.gift.GiftSendRequest
import com.app.messej.data.model.api.gift.GiftSendResponse
import com.app.messej.data.model.api.gift.PointsPurchase
import com.app.messej.data.model.api.gift.Sender
import com.app.messej.data.model.entity.OfflineGiftVideo
import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.model.socket.AbstractGiftItem
import com.app.messej.data.repository.pagingSources.GiftConversionHistoryDataSource
import com.app.messej.data.repository.pagingSources.GiftListingDataSource
import com.app.messej.data.repository.pagingSources.GiftPurchaseHistoryDataSource
import com.app.messej.data.repository.pagingSources.GiftReceivedHistoryDataSource
import com.app.messej.data.repository.pagingSources.GiftSendersHistoryDataSource
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.gift.GiftListingViewModel
import com.github.f4b6a3.uuid.UuidCreator
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import java.io.File

class GiftRepository(val context: Application) : BaseMediaUploadRepository(context) {
    suspend fun giftConversion(giftPointToFlaxConvertRequest: GiftPointToFlaxConvertRequest): ResultOf<APIResponse<GiftPointToFlaxConvertResponse>> {
        return try {
            val service = APIServiceGenerator.createService(GiftAPIService::class.java)
            val response = service.giftPointToFlaxConvert(giftPointToFlaxConvertRequest)

            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception create poll: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun getGiftPurchaseHistoryPager(): Pager<Int, PointsPurchase> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { GiftPurchaseHistoryDataSource(APIServiceGenerator.createService(GiftAPIService::class.java)) })
    }


    fun getGiftSendersHistoryPager(sendersId: Int, sendersHistory: Boolean): Pager<Int, Sender> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { GiftSendersHistoryDataSource(APIServiceGenerator.createService(GiftAPIService::class.java), sendersId, sendersHistory) })
    }

    fun getGiftReceivedHistoryPager(giftType:String ): Pager<Int, GiftItem> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { GiftReceivedHistoryDataSource(APIServiceGenerator.createService(GiftAPIService::class.java),giftType) })
    }

    suspend fun getGiftPurchaseList(): ResultOf<GiftResponse> {
        return try {
            val response = APIServiceGenerator.createService(GiftAPIService::class.java).getPurchaseCoins()
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    fun getGiftPagerList( giftType: GiftType,birthday:Boolean?=false,userCongrats:Boolean?=false): Pager<Int, GiftItem> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = { GiftListingDataSource(APIServiceGenerator.createService(GiftAPIService::class.java), giftType = giftType,birthday = birthday, userLevelCongrats = userCongrats) }
        )
    }

    suspend fun sendGift(id: Int, params: GiftListingViewModel.GiftParams, preview: Boolean = true): ResultOf<GiftSendResponse> {
        return try {
            val request = GiftSendRequest(
                giftId = id,
                receiverId = params.receiver,
                podiumId = params.contextId.takeIf { params.giftContext.isPodiumType },
                challengeId = params.challengeId,
                challengeEndTimeStampUTC = params.challengeEndTimeStampUTC,
                sendingSource = params.giftContext.toString(),
                sendingSourceId = params.contextId,
                managerId = params.managerId
            )
            val response = if (preview) APIServiceGenerator.createService(GiftAPIService::class.java).sendGiftPreview(request)
            else APIServiceGenerator.createService(GiftAPIService::class.java).sendGift(request)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getGift(giftId: Int): ResultOf<GiftNotificationResponse> {
        return try {
            val response = APIServiceGenerator.createService(GiftAPIService::class.java).getGift(giftId)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }


    suspend fun getGiftDetailsInSendersHistory(sendersId: Int, sendersHistory: Boolean): ResultOf<GiftItem> {
        return try {
            val response = APIServiceGenerator.createService(GiftAPIService::class.java).getGiftDetailsInSendersHistory(sendersId, sendersHistory)
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }


    suspend fun sendGiftReply(id: Int, req: GiftReplyRequest): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(GiftAPIService::class.java).sendReplyGift(id, req)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    fun getGiftConversionHistoryPager(historyType: String): Pager<Int, CoinsConversion> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { GiftConversionHistoryDataSource(APIServiceGenerator.createService(GiftAPIService::class.java), historyType) })
    }

    override suspend fun getVideoSecrets(): ResultOf<UploadCredentialsResponse> {
        return try {
            val resp = APIServiceGenerator.createService(GiftAPIService::class.java, true).getGiftVideoDownloadCredentials()
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun downloadGiftVideo(gift: AbstractGiftItem): Flow<VideoDownloadResult> {
        val s3Key = gift.giftAnimationUrlAndroid?: return callbackFlow {
            trySend(VideoDownloadResult.Error(Exception("Video URL is null")))
            channel.close()
        }
        val existing = db.getGiftVideoDao().getLocalGiftVideo(gift.id)
        if (existing!=null) {
            if(existing.key!=gift.giftAnimationUrlAndroid) {
                File(existing.path).delete()
                db.getGiftVideoDao().deleteGiftVideo(existing)
            } else {
                return callbackFlow {
                    trySend(VideoDownloadResult.Complete(File(existing.path)))
                    channel.close()
                }
            }
        }
        val fileExtension = s3Key.substringAfterLast('.', "")
        val filename = "gift_${gift.id}_${UuidCreator.getTimeBased()}.$fileExtension"
        val destFile = MediaUtils.createGiftVideoFile(context, filename)
        return performDownload(s3Key, destFile)
    }

    suspend fun downloadChallengeResultVideo(winner: Boolean): Flow<VideoDownloadResult> {
        val s3Key = if (winner) "challenges/challenge-winner-android.mp4"
        else "challenges/challenge-lost-android.mp4"
        val destFile = MediaUtils.createChallengeVideoFile(context, s3Key.split("/").last())
        Log.d("BIRTHDAYDESTINATION",destFile.absolutePath)
        return if (destFile.exists()) callbackFlow {
            trySend(VideoDownloadResult.Complete(destFile))
            channel.close()
        }
        else performDownload(s3Key, destFile)
    }

    suspend fun downloadBirthdayResultVideo(birthdayUrl: String): Flow<VideoDownloadResult> {
        val s3Key = birthdayUrl
//        val s3Key = "development/gifts/welcome-personal_android.mp4" //todo video url format
        val destFile = MediaUtils.createBirthdayVideoFile(context, s3Key.split("/").last())
        Log.d("BIRTHDAYDESTINATION",destFile.absolutePath)
        return if (destFile.exists()) callbackFlow {
            trySend(VideoDownloadResult.Complete(destFile))
            channel.close()
        }
        else performDownload(s3Key, destFile)
    }



    suspend fun saveGiftVideo(gift: AbstractGiftItem, file: File) {
        db.getGiftVideoDao().apply {
            val existing = getLocalGiftVideo(gift.id)
            val vid = OfflineGiftVideo(
                giftId = gift.id, key = gift.giftAnimationUrlAndroid.orEmpty(), name = gift.translatedName.orEmpty(),path = file.absolutePath
            )
            if (existing!=null && existing.key!=vid.key) {
                File(existing.path).delete()
                deleteGiftVideo(existing)
            }
            insertOrReplace(vid)
        }
    }

    override fun getAwsConfig() = AWSConfiguration(context, R.raw.awsconfiguration, "Gift")

    override suspend fun downloadMultipartMedia(meta: MediaMeta): Flow<VideoDownloadResult> {
        error("Wrong method called. Call downloadGiftVideo(gift: AbstractGiftItem) instead")
    }

    suspend fun getSayThanksGift(): ResultOf<GiftNotificationResponse> {
        return try {
            val response = APIServiceGenerator.createService(GiftAPIService::class.java).getSayThanksGift()
            APIUtil.handleResponse(response)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

}