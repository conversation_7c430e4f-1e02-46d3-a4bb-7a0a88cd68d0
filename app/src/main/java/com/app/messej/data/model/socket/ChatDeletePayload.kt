package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class ChatDeletePayload(
    @SerializedName("messages")  val messages: List<MessagesToDelete>,
    @SerializedName("deleteForEveryone")  val deleteForEveryone: Boolean,
    @SerializedName("language") val language: String
): SocketEventPayload() {
    data class MessagesToDelete(
        @SerializedName("message_id") val messageId: String,
        @SerializedName("room_id") val roomId: String,
    )
}
