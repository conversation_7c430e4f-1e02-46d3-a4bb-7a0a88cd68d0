package com.app.messej.data.model

import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class PostatMentionedUser(
    @SerializedName("userId", alternate = ["id"]) override val id: Int,
    @SerializedName("name"                  ) override val name: String,
    @SerializedName("userName"              ) override val username: String?=null,
    @SerializedName("thumbnail_url", alternate = ["thumbnail"])override val thumbnail: String? = null,
    @SerializedName("deleted_account"       ) val isDeleted: Boolean = false,
    @SerializedName("nickName"              ) val nickName: String? = null,
    @SerializedName("is_premium"            ) val isPremium: Boolean,
    @SerializedName("verified"              ) override val verified: <PERSON><PERSON><PERSON>,
    @SerializedName("country_name"          ) val countryName: String?,
    @SerializedName("country_code"          ) override val countryCode: String,
    @SerializedName("blocked_by_admin"      ) val blockedByAdmin: Boolean,
    @SerializedName("blocked_by_leader"     ) val blockedByLeader: Boolean,
    @SerializedName("citizenship"           ) override val citizenship: UserCitizenship,

    ) : AbstractUser() {
    override val membership: UserType
        get() = if (isPremium) UserType.PREMIUM else UserType.FREE

    companion object {
        fun from(user: OtherUser) = PostatMentionedUser(
            id = user.id,
            name = user.name,
            username = user.username,
            thumbnail = user.thumbnail,
            nickName = user.name,
            isPremium = user.premiumUser,
            verified = user.verified,
            countryName = user.countryCode.orEmpty(),
            countryCode = user.countryCode.orEmpty(),
            blockedByAdmin = user.blockedByAdmin?: false,
            blockedByLeader = user.blockedByLeader?: false,
            citizenship = user.citizenship?: UserCitizenship.default()
        )
    }
}
