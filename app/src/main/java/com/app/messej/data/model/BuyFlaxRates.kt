package com.app.messej.data.model

import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

data class BuyFlaxRates(
    @SerializedName("id") val id: Int? = null,
    @SerializedName("user_id") val userId: Int? = null,
    @SerializedName("flax") val flax: Double? = 0.0,
    @SerializedName("coins") val coins: Double? = 0.0,
    @SerializedName("best_value") val bestValue: Boolean? = null,
    @SerializedName("most_popular") val mostPopular: Boolean? = null,
    @SerializedName("time_created") val timeCreated: String? = null,
    @SerializedName("time_updated") val timeUpdated: String? = null,
    @SerializedName("usd") var usd: Double? = 0.0,
    @SerializedName("android_product_id") var androidProductId: String? = null,
    @SerializedName("ios_product_id") var iosProductId: String? = null,
    @SerializedName("discount_percentage") var discountPercentage: Int? = 0,
    @SerializedName("purchased_by") var purchasedBy: String? = null,
    @SerializedName("purchased_by_user") var purchasedByUser: String? = null,
    @SerializedName("purchased_for") var purchasedFor: String? = null,
    @SerializedName("restored_for") var restoredFor: String? =null,
    @SerializedName("restored_by") var restoredBy: String? =null,

    ) {

    val parsedCreatedTime: LocalDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)?.run { DateTimeUtils.getLocalDateTime(this) }

    val getParsedTime: String
        get() = DateTimeUtils.format(parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")

    val getSingleFlaxRate: Double?
        get() = flax?.let { usd?.div(it) }

    val isPurchaseForEmpty: Boolean
        get()= purchasedFor.isNullOrEmpty()

    val isPurchaseByEmpty: Boolean
        get()= purchasedByUser.isNullOrEmpty()


    val isRestoredForEmpty: Boolean
        get()= restoredFor.isNullOrEmpty()

    val isRestoredByEmpty: Boolean
        get()= restoredBy.isNullOrEmpty()
}
