package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.enums.UserRole
import com.google.gson.annotations.SerializedName

data class PodiumUserJoinedPayload(
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("role") val role: UserRole? = null,
    @SerializedName("user_id") val userId: String? = null,
    @SerializedName("user_details") val userDetails: PodiumParticipant,
) : SocketEventPayload()
