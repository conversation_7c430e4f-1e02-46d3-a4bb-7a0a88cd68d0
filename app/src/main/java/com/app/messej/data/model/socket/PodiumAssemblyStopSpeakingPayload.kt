package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.AssemblySpeakingSkipReason
import com.app.messej.data.model.enums.AssemblySpeakingSkippedBy
import com.app.messej.data.model.enums.AssemblyStopSpeakingReason
import com.google.gson.annotations.SerializedName

data class PodiumAssemblyStopSpeakingPayload(
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("reason") val reason: AssemblyStopSpeakingReason? = null,
    @SerializedName("skipped_by") val skippedBy: AssemblySpeakingSkippedBy? = null,
    @SerializedName("skip_reason") val skipReason: AssemblySpeakingSkipReason? = null,
    ) : SocketEventPayload()
