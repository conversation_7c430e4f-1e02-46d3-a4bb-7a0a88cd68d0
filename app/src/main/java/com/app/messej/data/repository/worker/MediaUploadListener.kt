package com.app.messej.data.repository.worker

import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

object MediaUploadListener {

    private val uploadProgress = MutableStateFlow<Map<String, Int>>(mapOf())
    val uploadProgressFlow: StateFlow<Map<String, Int>> = uploadProgress

    fun post(id: String, progress: Int) {
        val map = uploadProgress.value.toMutableMap()
        map[id] = progress
        uploadProgress.value = map
        Log.d("ENCODE", "MUL updated $map")
    }

    fun clear(id: String) {
        val map = uploadProgress.value.toMutableMap()
        map.remove(id)
        uploadProgress.value = map
    }
}