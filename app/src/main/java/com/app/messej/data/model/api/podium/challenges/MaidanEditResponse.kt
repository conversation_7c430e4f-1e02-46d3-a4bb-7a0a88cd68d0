package com.app.messej.data.model.api.podium.challenges

import com.google.gson.annotations.SerializedName

data class MaidanEditResponse(
    @SerializedName("debit_coins") val debitedCoins: Double? = null,
    @SerializedName("refund_coins") val refundedCoins: Double? = null
) {

    sealed class PrizeChange(val amount: Double) {
        class Debit(amount: Double) : PrizeChange(amount)
        class Refund(amount: Double) : PrizeChange(amount)
    }

    val prizeChange: PrizeChange?
        get() = debitedCoins?.let { PrizeChange.Debit(it) }
            ?: refundedCoins?.let { PrizeChange.Refund(it) }
}
