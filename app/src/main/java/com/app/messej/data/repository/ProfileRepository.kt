package com.app.messej.data.repository

import android.content.Context
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.room.withTransaction
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.app.messej.data.Constants
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.AppAPIService
import com.app.messej.data.api.AuthAPIService
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.api.external.ExternalServiceGenerator
import com.app.messej.data.model.AbstractUserWithStats
import com.app.messej.data.model.BlockedUser
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.Star
import com.app.messej.data.model.User
import com.app.messej.data.model.api.CitizenshipUpdateResponse
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.GeoLocationResponse
import com.app.messej.data.model.api.RestrictUserRequest
import com.app.messej.data.model.api.UnHideRequest
import com.app.messej.data.model.api.UpdateUserNameRequest
import com.app.messej.data.model.api.UserNickNameRequest
import com.app.messej.data.model.api.auth.HomeAddress
import com.app.messej.data.model.api.auth.UnreadItemsResponse
import com.app.messej.data.model.api.flash.UserFunctionalityBlockRequest
import com.app.messej.data.model.api.huddles.AddParticipantsResponse
import com.app.messej.data.model.api.huddles.UserBlockRequest
import com.app.messej.data.model.api.huddles.UserUnblockRequest
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.api.profile.EditProfileRequest
import com.app.messej.data.model.api.profile.LikersHideRequest
import com.app.messej.data.model.api.profile.NotificationsResponse
import com.app.messej.data.model.api.profile.PasswordChangeRequest
import com.app.messej.data.model.api.profile.RegisterLocationRequest
import com.app.messej.data.model.api.profile.RegisterPasswordRequest
import com.app.messej.data.model.api.profile.RegisterUsernameRequest
import com.app.messej.data.model.api.profile.SetProfileRequest
import com.app.messej.data.model.api.profile.SetSuperstarRequest
import com.app.messej.data.model.api.profile.SuperstarSuggestionResponse
import com.app.messej.data.model.api.profile.TempUserBlockRequest
import com.app.messej.data.model.api.profile.UpdateUserLocation
import com.app.messej.data.model.api.profile.UserBirthdayResponse
import com.app.messej.data.model.api.profile.VerifyAccountResponse
import com.app.messej.data.model.api.stars.FollowStarRequest
import com.app.messej.data.model.api.stars.UnfollowStarRequest
import com.app.messej.data.model.api.subscription.FlixSubscriptionDetails
import com.app.messej.data.model.api.subscription.PlayStoreSuccessResponse
import com.app.messej.data.model.api.subscription.PremiumResponse
import com.app.messej.data.model.api.subscription.Subscription
import com.app.messej.data.model.api.subscription.TransactionReceipt
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.SubscriptionPurchaseRequest
import com.app.messej.data.model.entity.UserRelative
import com.app.messej.data.model.entity.UserStar
import com.app.messej.data.model.entity.UserStar.Companion.asStar
import com.app.messej.data.model.entity.UserStatsResult
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.Functionality
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.PurchaseItem
import com.app.messej.data.model.enums.SearchType
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.repository.mediators.ProfileStarsRemoteMediator
import com.app.messej.data.repository.mediators.UserRelativeRemoteMediator
import com.app.messej.data.repository.pagingSources.AddParticipantsDataSource
import com.app.messej.data.repository.pagingSources.BlockedBroadcastDataSource
import com.app.messej.data.repository.pagingSources.BlockedDearsDataSource
import com.app.messej.data.repository.pagingSources.EmpoweredBlockedUsersDataSource
import com.app.messej.data.repository.pagingSources.StarsSearchDataSource
import com.app.messej.data.repository.pagingSources.StarsSearchSuggestionDataSource
import com.app.messej.data.repository.pagingSources.StarsSuggestionDataSource
import com.app.messej.data.repository.pagingSources.UserRelativeSearchDataSource
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DeviceInfoUtil
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.time.Duration
import java.time.LocalDateTime

/**
 * Repository to handle auth operations (login/signup etc)
 */
class ProfileRepository(private val mContext: Context) {

    private val db = FlashatDatabase.getInstance(mContext)

    private var accountRepo: AccountRepository = AccountRepository(mContext)
    private val datastore: FlashatDatastore = FlashatDatastore()

    companion object {
        private var lastAccountDetailsUpdate: LocalDateTime? = null
    }

    suspend fun setPassword(password: String, confirmPassword: String, countryCode: String, phoneNumber: String): ResultOf<CurrentUser> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).setPassword(
                RegisterPasswordRequest(
                    countryCode = UserInfoUtil.addPlusToCountryCode(countryCode), phone = UserInfoUtil.sanitizePhoneNumber(phoneNumber), password = password, confirmPassword = confirmPassword
                )
            )
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                accountRepo.updateUser(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setPassword: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun setEmailPassword(password: String, confirmPassword: String,email: String): ResultOf<CurrentUser> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).setPassword(
                RegisterPasswordRequest(
                   email=email, password = password, confirmPassword = confirmPassword
                )
            )
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                accountRepo.updateUser(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setPassword: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun checkOrSetUsername(username: String, checkAvailability: Boolean = false): ResultOf<String> {
        return try {
            val req = RegisterUsernameRequest(
                username = username, checkAvailable = checkAvailability
            )
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).checkSetUsername(req)
            APIUtil.handleResponseWithoutResult(response)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "checkOrSetUsername: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun setProfile(req: SetProfileRequest): ResultOf<CurrentUser> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).createProfile(req)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                accountRepo.updateUser(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setProfile: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateProfile(req: EditProfileRequest): ResultOf<CurrentUser> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).updateProfile(req)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                accountRepo.updateUser(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setProfile: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun setLocation(req: RegisterLocationRequest): ResultOf<CurrentUser> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).setLocation(req)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                accountRepo.updateUser(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setLocation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getGeoLocation(): ResultOf<GeoLocationResponse> {
        return try {
            val response = ExternalServiceGenerator.createGoogleAPIService().getGeoLocation()
            ResultOf.Success(response.body()!!)
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getGeoLocation: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    data class SuperStarSearchKey(
        var userType: UserType,
        var searchType: SearchType,
        var page: Int,
    )

    suspend fun getSuperstarSuggestions(
        page: Int,
        keyword: String?,
        countries: List<String>?,
        genders: List<Gender>?,
        userType: UserType = UserType.PREMIUM,
        searchType: SearchType = SearchType.EXACT_MATCH,
    ): ResultOf<SuperstarSuggestionResponse> {

        var currentSuperStarSearchKey = SuperStarSearchKey(userType, searchType, page)
        lateinit var result: ResultOf<SuperstarSuggestionResponse>
        val userList: ArrayList<User> = arrayListOf()

        return try {
            while (true) {
                val response = APIServiceGenerator.createService(AuthAPIService::class.java).getSuperstarSuggestions(
                    page = currentSuperStarSearchKey.page,
                    keyword = keyword,
                    countryFilter = countries?.ifEmpty { null },
                    country = keyword.isNullOrBlank(),
                    genderFilter = genders?.map { it.code }?.ifEmpty { null },
                    userType = currentSuperStarSearchKey.userType,
                    searchType = currentSuperStarSearchKey.searchType
                )

                result = APIUtil.handleResponse(response)

                if (result is ResultOf.Success && !keyword.isNullOrEmpty()) {
                    userList.addAll(result.value.users)

                    val newSuperStarSearchKey: SuperStarSearchKey? = when {

                        result.value.nextPage -> currentSuperStarSearchKey.copy(page = currentSuperStarSearchKey.page + 1)

                        result.value.searchType != null -> currentSuperStarSearchKey.copy(
                            searchType = SearchType.fromString(result.value.searchType.orEmpty()), page = 1
                        )

                        currentSuperStarSearchKey.userType == UserType.PREMIUM -> currentSuperStarSearchKey.copy(
                            userType = UserType.FREE, searchType = SearchType.EXACT_MATCH, page = 1
                        )

                        else -> null
                    }

                    if (newSuperStarSearchKey == null) {
                        result.value.users = userList
                        break
                    } else {
                        currentSuperStarSearchKey = newSuperStarSearchKey
                    }
                } else {
                    break
                }
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "getSuperstarSuggestions: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun setSuperstar(req: SetSuperstarRequest): ResultOf<CurrentUser> {
        return try {
            req.copyDeviceInfoFrom(DeviceInfoUtil.getAllDeviceInfo(FirebaseRepository()))
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).setSuperstar(req)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                accountRepo.updateUser(result.value)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setSuperstar: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    private fun getFollowerListPager(type: FollowerType, onCount: (Int) -> Unit = {}): Pager<Int, UserRelative> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     remoteMediator = UserRelativeRemoteMediator(type, db, APIServiceGenerator.createService(ProfileAPIService::class.java)) {
                         onCount(it)
                     },
                     pagingSourceFactory = {
                         db.getUserDao().userRelativePagingSource((type))
                     })
    }

    fun getFansListPager(onCount: (Int) -> Unit = {}) = getFollowerListPager(FollowerType.FAN, onCount)
    fun getDearsListPager(onCount: (Int) -> Unit = {}) = getFollowerListPager(FollowerType.DEAR, onCount)
    fun getLikersListPager(onCount: (Int) -> Unit = {}) = getFollowerListPager(FollowerType.LIKER, onCount)

    private fun getFollowerSearchListPager(type: FollowerType, keyword: String?): Pager<Int, UserRelative> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = {
                UserRelativeSearchDataSource(APIServiceGenerator.createService(ProfileAPIService::class.java), FollowerType.LIKER, keyword)
            }
        )
    }

    fun getLikersSearchListPager(keyword: String?) = getFollowerSearchListPager(FollowerType.LIKER, keyword)
    fun getFansSearchListPager(keyword: String?) = getFollowerSearchListPager(FollowerType.FAN, keyword)
    fun getDearsSearchListPager(keyword: String?) = getFollowerSearchListPager(FollowerType.DEAR, keyword)

    /**
     * Get list of all stars available for the user
     */
    @OptIn(ExperimentalPagingApi::class)
    fun getPublicStarsListPager(): Pager<Int, UserStar> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     remoteMediator = ProfileStarsRemoteMediator("", db, APIServiceGenerator.createService(ProfileAPIService::class.java)),
                     pagingSourceFactory = { db.getUserDao().starsPagingSource() })
    }

    fun getPublicStarsSearchListPager(keyword: String?): Pager<Int, UserStar> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = { StarsSearchDataSource(APIServiceGenerator.createService(ProfileAPIService::class.java), keyword) })
    }

    /**
     * Get other user's data from API and update room database
     */
    suspend fun fetchUserStarProfile(id: Int) = withContext(Dispatchers.IO) {
        val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).getPublicUserDetails(id)
        val result = APIUtil.handleResponse(resp)
        if (result is ResultOf.Success) {
            val star = result.value.asStar()
            db.getUserDao().insert(star)
        }
    }

    fun getUserStar(id: Int): UserStar? = db.getUserDao().getUserStar(id)

    fun getUserStarLiveData(id: Int): LiveData<UserStar?> = db.getUserDao().getUserStarLiveData(id)

    /**
     * Get list of suggested stars
     * - Used on search star page for first load
     */
    fun getSuggestedStarsListPager(searchKeyWord: String?): Pager<Int,Star> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { StarsSuggestionDataSource(APIServiceGenerator.createService(ProfileAPIService::class.java), accountRepo, searchKeyWord) })
    }

    fun getSearchSuggestedStarsListPager(searchKeyWord: String?): Pager<Int,Star> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { StarsSearchSuggestionDataSource(APIServiceGenerator.createService(ProfileAPIService::class.java), accountRepo, searchKeyWord) })
    }

    suspend fun refreshAccountDetails(lazy: Boolean = true) {
        if (lazy) {
            if (lastAccountDetailsUpdate != null && Duration.between(lastAccountDetailsUpdate, LocalDateTime.now()).seconds < 30) {
                // Skipping update
                return
            }
        }
        lastAccountDetailsUpdate = LocalDateTime.now()
        getAccountDetails()
    }

    suspend fun getAccountDetails(): ResultOf<AccountDetailsResponse> {
        return try {
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).getAccountDetails()
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)

                accountRepo.apply {
                    Log.w("BCVM", "save account details: $result")
                    saveAccountDetails(result)
                }
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getVerifyAccountDetails(): ResultOf<VerifyAccountResponse> {
        return try {
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).getVerifyAccountDetails()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                result.value.verificationStatus?.let { FlashatDatastore().setAccountVerifiedStatus(it) }
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateAccountDetails(username: String): ResultOf<String> {
        return try {
            val req = UpdateUserNameRequest(
                username = username
            )
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).editAccountDetails(req)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun checkUserNameAvailability(username: String): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).checkAvailabilityUsername(username)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    fun getAddParticipantsList(type: String, huddleId: Int, searchKeyword: String = ""): Pager<Int,AddParticipantsResponse.Members> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false), pagingSourceFactory = {
            AddParticipantsDataSource(APIServiceGenerator.createService(ChatAPIService::class.java), accountRepo, huddleId, type, searchKeyword)
        })
    }

    suspend fun changePassword(req: PasswordChangeRequest): ResultOf<CurrentUser> {
        return try {
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).changePassword(req)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateSubscription(purchase: Purchase, currencyDetails: ProductDetails.PricingPhase?): ResultOf<PremiumResponse> {
        val gson = Gson()
        val response = gson.fromJson(purchase.originalJson, PlayStoreSuccessResponse::class.java)
        val accountIdentifiers = purchase.accountIdentifiers
        val price = currencyDetails?.priceAmountMicros?.div(1000000)
        val request = SubscriptionPurchaseRequest(
            currencyDetails?.priceCurrencyCode!!, SubscriptionPurchaseRequest.Payload(
                purchase.developerPayload,
                purchase.isAcknowledged,
                accountIdentifiers?.obfuscatedAccountId,
                accountIdentifiers?.obfuscatedProfileId,
                response.orderId,
                response.packageName,
                response.productId,
                response.purchaseState,
                response.purchaseToken,
                purchase.signature,
                purchase.purchaseTime,
                purchase.orderId,
                TransactionReceipt(purchase.isAcknowledged, purchase.orderId, response.packageName, response.productId, response.purchaseState, purchase.purchaseTime, purchase.purchaseToken)
            ), "In App Purchase", restoring_purchases = true, price = price!!, timezone = DateTimeUtils.getTimeZoneFromEpochTime(response.purchaseTime), type = "android", userid = accountRepo.user.id
        )
        return try {
            val resp = APIServiceGenerator.createService(AuthAPIService::class.java).updateSubscription(request)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)

                val user = accountRepo.user
                user.premium = true
                accountRepo.updateUser(user)
                APIUtil.handleResponse(resp)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateInAppPurchase(purchase: Purchase,coins:Int,purchaseItem: PurchaseItem,amount:Long?,currencyCode:String?): ResultOf<PremiumResponse> {
        val gson = Gson()
        val response = gson.fromJson(purchase.originalJson, PlayStoreSuccessResponse::class.java)
        val accountIdentifiers = purchase.accountIdentifiers

        val request = SubscriptionPurchaseRequest(
            currencyCode.toString(), SubscriptionPurchaseRequest.Payload(
                purchase.developerPayload,
                purchase.isAcknowledged,
                accountIdentifiers?.obfuscatedAccountId,
                accountIdentifiers?.obfuscatedProfileId,
                response.orderId,
                response.packageName,
                response.productId,
                response.purchaseState,
                response.purchaseToken,
                purchase.signature,
                purchase.purchaseTime,
                purchase.orderId,
                TransactionReceipt(purchase.isAcknowledged, purchase.orderId, response.packageName, response.productId, response.purchaseState, purchase.purchaseTime, purchase.purchaseToken)
            ), "In App Purchase", restoring_purchases = true, price = amount!!, timezone = DateTimeUtils.getTimeZoneFromEpochTime(response.purchaseTime), type = "android", userid = accountRepo.user.id, coins = coins, purchase_type = purchaseItem
        )
        return try {
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).updateInAppPurchase(request)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                APIUtil.handleResponse(resp)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }
    suspend fun updateToLocalDb(purchase: Purchase, amount:Long?,currencyCode:String?, coins: Int) {
        val gson = Gson()
        val response = gson.fromJson(purchase.originalJson, PlayStoreSuccessResponse::class.java)
        val accountIdentifiers = purchase.accountIdentifiers
        val price = amount
        val request = SubscriptionPurchaseRequest(
            currencyCode.toString(),
            SubscriptionPurchaseRequest.Payload(
                purchase.developerPayload,
                purchase.isAcknowledged,
                accountIdentifiers?.obfuscatedAccountId,
                accountIdentifiers?.obfuscatedProfileId,
                response.orderId,
                response.packageName,
                response.productId,
                response.purchaseState,
                response.purchaseToken,
                purchase.signature,
                purchase.purchaseTime,
                purchase.orderId,
                TransactionReceipt(purchase.isAcknowledged, purchase.orderId, response.packageName, response.productId, response.purchaseState, purchase.purchaseTime, purchase.purchaseToken)
            ),
            "In App Purchase",
            restoring_purchases = true,
            price = price!!,
            timezone = DateTimeUtils.getTimeZoneFromEpochTime(response.purchaseTime),
            type = "android",
            userid = accountRepo.user.id,
            coins = coins
        )
        db.withTransaction {
            db.getSubscriptionDao().insert(request)
        }

    }


    suspend fun updateInAppPurchaseFromLocal(request: SubscriptionPurchaseRequest): ResultOf<PremiumResponse> {
        return try {
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).updateInAppPurchase(request)
            if (resp.isSuccessful && resp.code() == 200) {
                APIUtil.handleResponse(resp)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getInAppPurchaseRequestsFromDB(): List<SubscriptionPurchaseRequest> {
        return withContext(Dispatchers.IO){
            db.getSubscriptionDao().getAllPurchaseRequests()
        }
    }

    suspend fun deletePurchaseFromDB(orderID:String){
        return withContext(Dispatchers.IO){
            db.getSubscriptionDao().deleteByOrderId(orderID)
        }
    }



    suspend fun getSubscriptionDetails(): ResultOf<Subscription> {
        return try {
            val resp = APIServiceGenerator.createService(AuthAPIService::class.java).getSubscriptionDetails(accountRepo.user.id)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                APIUtil.handleResponse(resp)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getUserCitizenship() : ResultOf<CitizenshipUpdateResponse> {
        return try {
            val response = APIServiceGenerator.createService(serviceClass = ProfileAPIService::class.java).getUserCitizenship()
            APIUtil.handleResponse(response)
        } catch (error: Exception) {
            ResultOf.Error(Exception(error))
        }
    }

    suspend fun verifyAccount(): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).verifyAccount()
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getNotificationsSettings(): ResultOf<NotificationsResponse> {
        return try {
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).getNotificationsSettings()
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setProfile: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    fun getBlockedUsers(): Pager<Int,UserRelative> {
        return Pager(
            config = PagingConfig(pageSize = 30, enablePlaceholders = false),
            pagingSourceFactory = { BlockedDearsDataSource(APIServiceGenerator.createService(ProfileAPIService::class.java)) })
    }

    fun getHideUsers(): Pager<Int,UserRelative> {
        return Pager(
            config = PagingConfig(pageSize = 30, enablePlaceholders = false),
            pagingSourceFactory = { BlockedBroadcastDataSource(APIServiceGenerator.createService(ProfileAPIService::class.java)) })
    }

    suspend fun blockUser(id: Int): ResultOf<String> {
        return try {
            val req = UserBlockRequest(id)
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).blockUser(req)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    db.getUserDao().deleteStar(id)
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun unblockUser(id: Int): ResultOf<String> {
        return try {
            val req = UserUnblockRequest(
                unblockUserId = id
            )
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).unblockUser(req)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateNotifications(data: NotificationsResponse): ResultOf<NotificationsResponse> {
        return try {
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).updateNotifications(data)
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setProfile: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun unhideBroadcast(id: Int): ResultOf<String> {
        return try {
            val req = UnHideRequest(
                hideId = id, hide = false
            )
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).unBlockBroadcast(req)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun uploadProfileDp(file: File?): ResultOf<CurrentUser> {
        return try {
            var imageBody: MultipartBody.Part? = null
            if (file != null) {
                val requestFile = file.asRequestBody("multipart/form-data".toMediaTypeOrNull())
                imageBody = MultipartBody.Part.createFormData("file", file.name, requestFile)
            }
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).uploadProfileDp(imageBody)
            val result = APIUtil.handleResponse(response)
            when (result) {
                is ResultOf.APIError -> {

                }
                is ResultOf.Error -> {

                }
                is ResultOf.Success -> {
                    delay(1000)
                    accountRepo.updateUser(result.value)
                }
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "uploadProfileDP: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun resetPassword(password: String, confirmPassword: String, countryCode: String, phoneNumber: String, email: String): ResultOf<CurrentUser> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).resetPassword(
                RegisterPasswordRequest(
                    countryCode = UserInfoUtil.addPlusToCountryCode(countryCode),
                    phone = UserInfoUtil.sanitizePhoneNumber(phoneNumber),
                    password = password,
                    confirmPassword = confirmPassword,
                    email = email
                )
            )
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "setPassword: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPublicUserDetails(id: Int, quick: Boolean = false): ResultOf<OtherUser> {
        return try {
            if (quick) {
                val user = db.getUserDao().getOtherUser(id)
                user?.let {
                    return ResultOf.Success(it)
                }
            }
            val user = db.getUserDao().getRelativeUser(id)
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).getPublicUserDetails(id, user?.relativeType)
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                db.getUserDao().insertOtherUser(result.value)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateOtherUser(user: OtherUser) = db.getUserDao().insertOtherUser(user)

    fun getLocalOtherUserProfile(id: Int) = db.getUserDao().getOtherUserLiveData(id)

    suspend fun refreshUserNickNames() {
        try {
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).getUserNickName()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                db.getUserDao().insertAllNickNames(result.value.names)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateUserNickName(id: Int,_name: String): ResultOf<String> {
        return try {
            val name = _name.ifBlank { null }
            val req = UserNickNameRequest(id = id, name = name)
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).updateUserNickName(req)
            val result = APIUtil.handleResponseWithoutResult(resp)
            if (result is ResultOf.Success) {
                if (name==null) {
                    db.getUserDao().deleteNickName(id)
                }
                refreshUserNickNames()
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun restrictUser(chatId: String, userId: Int): ResultOf<String> {
        return try {
            val req = RestrictUserRequest(huddleType = HuddleType.PRIVATE, chatroom = chatId, changeChatType = "REQUEST")
            val resp = APIServiceGenerator.createService(ChatAPIService::class.java).restrictUser(req)
            val result = APIUtil.handleResponseWithoutResult(resp)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    db.getPrivateChatDao().apply {
                        val chat = getPrivateChat(chatId) ?: return@apply
                        insert(chat.copy(type = PrivateChat.ChatType.REQUEST))
                    }
                }
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteStar(starId: Int) {
        withContext(Dispatchers.IO) {
            db.getUserDao().deleteStar(starId)
        }
    }

    suspend fun followUser(userId: Int): ResultOf<String> = withContext(Dispatchers.IO) {
        return@withContext try {
            val req = FollowStarRequest(starId = userId)
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).followStar(req)
            val result = APIUtil.handleResponseWithoutResult(response)
            if(result is ResultOf.Success) {
                db.getChatMessageDao().updateHuddleMessageStarRelation(userId)
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "followStar: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun followUser(user: AbstractUserWithStats): ResultOf<String> = withContext(Dispatchers.IO) {
        val result = followUser(user.id)
        if (result is ResultOf.Success) {
            db.getUserDao().insert(user.asStar())
        }
        result
    }

    suspend fun followIntruder(user: AbstractUserWithStats, chat: PrivateChat): ResultOf<String> = withContext(Dispatchers.IO) {
        val result = followUser(user)
        if (result is ResultOf.Success) {
            db.getPrivateChatDao().update(
                chat.apply {
                    privateMessageTab = PrivateChat.PrivateMessageTabType.BUDDIES
                }
            )
        }
        result
    }

    suspend fun unFollowStar(id: Int): ResultOf<String> {
        return try {
            val req = UnfollowStarRequest(starId = id)
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).unfollowStar(req)
            val result = APIUtil.handleResponseWithoutResult(response)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    db.getUserDao().deleteStar(id)
                    db.getChatMessageDao().updateHuddleMessageStarRelation(id, null)
                }
            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "followStar: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun hideLiker(boolean: Boolean, id: Int): ResultOf<String> {
        return try {
            val req = LikersHideRequest(hide = boolean, hideId = id)
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).hideLiker(req)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    fun getNickNamesLiveData(triggerRefresh: Boolean = false, scope: CoroutineScope? = null): LiveData<List<NickName>> {
        if (triggerRefresh) {
            scope?.launch(Dispatchers.IO) {
                refreshUserNickNames()
            }
        }
        return db.getUserDao().getNickNamesLiveData()
    }

    fun getNickNamesFlow(triggerRefresh: Boolean = false, scope: CoroutineScope? = null): Flow<List<NickName>> {
        if (triggerRefresh) {
            scope?.launch(Dispatchers.IO) {
                refreshUserNickNames()
            }
        }
        return db.getUserDao().getNickNamesFlow()
    }

    fun getNickNameLiveData(userId: Int, triggerRefresh: Boolean = false, scope: CoroutineScope? = null): LiveData<NickName?> {
        if (triggerRefresh) {
            scope?.launch(Dispatchers.IO) {
                refreshUserNickNames()
            }
        }
        return db.getUserDao().getNickNameLiveData(userId)
    }

    suspend fun getNickName(userId: Int, triggerRefresh: Boolean = false, scope: CoroutineScope? = null): NickName? {
        if (triggerRefresh) {
            refreshUserNickNames()
        }
        return db.getUserDao().getNickName(userId)
    }

    suspend fun compressImage(file: File): File = MediaUtils.compressImageToTempFile(mContext, file.absolutePath)

    suspend fun countAppShare(): ResultOf<String> {
        return try {
            val response = APIServiceGenerator.createService(AppAPIService::class.java).countAppShare()
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getUnreadCounts(onMaidanLive: (String) -> Unit): ResultOf<UnreadItemsResponse> {
        return try {
            val response = APIServiceGenerator.createService(ChatAPIService::class.java).getUnreadCounts()
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                FlashatDatastore().saveUnreadCounts(result.value)
                result.value.myLiveMaidan?.let {
                    onMaidanLive.invoke(it)
                }
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun addHomeAddress(address: HomeAddress, previousAddress: HomeAddress? = null): ResultOf<String> {
        return try {
            val service = APIServiceGenerator.createService(ProfileAPIService::class.java)
            val response = if (previousAddress==null ) service.addHomeAddress(address)
            else service.editHomeAddress(address.copy(
                id = previousAddress.id
            ))
            val result = APIUtil.handleResponseWithoutResult(response)
//            if (result is ResultOf.Success) {
//            } else {
//                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
//                ResultOf.APIError(error)
//            }
            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception add home address: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getHomeAddress(): ResultOf<HomeAddress> {
        return try {
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).getHomeAddress()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
               /* dao.insertBusinessTaskOne(result.value)*/
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    /**
     * [toBlock] pass true to block or false to unblock
     */
    suspend fun blockUnblockUserTemporarily(userId: Int, toBlock: Boolean): ResultOf<String> {
        return try {
            val req = TempUserBlockRequest(toBlock)
            val resp = APIServiceGenerator.createService(ProfileAPIService::class.java).blockUserTemporarily(userId, req)
            val result = APIUtil.handleResponseWithoutResult(resp)
            if (result is ResultOf.Success){
                ResultOf.Success(result)
                db.withTransaction {
                    val user = db.getUserDao().getOtherUser(userId)
                    user?.let {
                        updateOtherUser(user.copy(
                            blockedByLeader = toBlock
                        ))
                    }
                }

            }
            result
        } catch (e: Exception){
            ResultOf.Error(Exception(e))
        }
    }
    suspend fun getUserBirthday(): ResultOf<UserBirthdayResponse> {
        return try {
            val resp = APIServiceGenerator.createService(AppAPIService::class.java).getBirthdays()
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun updateUserLocation(request:UpdateUserLocation): ResultOf<String> {
        return try {
            val service = APIServiceGenerator.createService(ProfileAPIService::class.java)
            val response =service.updateCountry(request)
            val result = APIUtil.handleResponseWithoutResult(response)

            result
        } catch (e: Exception) {
            Log.e(Constants.FLASHAT_TAG, "Exception update location: ", e)
            ResultOf.Error(Exception(e))
        }
    }

    suspend  fun getFlixSubscriptionDetails(): ResultOf<FlixSubscriptionDetails> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).getFlixSubscriptionDetails()
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend fun subscribeByFlix(request: FlixSubscriptionDetails): ResultOf<FlixSubscriptionDetails> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).setSubscriptionByFlix(request)
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend fun setSubscriptionToggle(request: FlixSubscriptionDetails): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(AuthAPIService::class.java).setSubscriptionToggle(request)
            if (resp.isSuccessful && resp.code() == 200) {
                val result = resp.body()?.message ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
                ResultOf.Success(result)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(resp.errorBody()!!)
                ResultOf.APIError(error)
            }
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    fun getEmpoweredBlockedUsers(blockType:String): Pager<Int,BlockedUser> {
        return Pager(
            config = PagingConfig(pageSize = 30, enablePlaceholders = false),
            pagingSourceFactory = { EmpoweredBlockedUsersDataSource(APIServiceGenerator.createService(ProfileAPIService::class.java),blockType) })
    }

    suspend fun empoweredUnBlockAction(userId: Int, blockType: Functionality) : ResultOf<String> {
        return try {
            val request = UserFunctionalityBlockRequest(blockType, action = BlockUnblockAction.UNBLOCK)
            val resp = APIServiceGenerator.createService(FlashAPIService::class.java).blockuserFunctionality(userId, request)
            val result = APIUtil.handleResponseWithoutResult(resp)
            result
        }catch (e: Exception) {
            ResultOf.Error(Exception(e))

        }
    }

    suspend fun getPopularity(): ResultOf<UserStatsResult> {
        return try {
            val response = APIServiceGenerator.createService(ProfileAPIService::class.java).getUserPopularity()
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

}