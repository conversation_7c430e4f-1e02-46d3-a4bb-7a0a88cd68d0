package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.api.socialAffairs.SocialAffairUser.Companion.dummySocialUser
import com.google.gson.annotations.SerializedName

data class CommitteeMembersResponse(
    @SerializedName("has_next") val haveNextPage: Boolean? = null,
    @SerializedName("data") val members: List<CommitteeMemberUserDetail>? = null
) {
    data class CommitteeMemberUserDetail(
        @SerializedName("user_details") val userDetail : SocialAffairUser?,
        @SerializedName("committee_member_count") val committeeMemberCount : Int?,
    )

    companion object {
        val testSingleCommitteeMember = CommitteeMemberUserDetail(
            userDetail = dummySocialUser,
            committeeMemberCount = 10
        )
    }
}


