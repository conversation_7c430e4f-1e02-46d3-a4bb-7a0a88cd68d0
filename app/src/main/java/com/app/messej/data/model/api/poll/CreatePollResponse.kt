package com.app.messej.data.model.api.poll


import com.google.gson.annotations.SerializedName

data class
CreatePollResponse(
    @SerializedName("answers") val answers: List<Answer>,
    @SerializedName("end_date") val endDate: Any,
    @SerializedName("huddle_id") val huddleId: Int,
    @SerializedName("id") val id: Int,
    @SerializedName("question") val question: String,
    @SerializedName("start_date") val startDate: String,
    @SerializedName("time_created") val timeCreated: String,
    @SerializedName("time_updated") val timeUpdated: String
)