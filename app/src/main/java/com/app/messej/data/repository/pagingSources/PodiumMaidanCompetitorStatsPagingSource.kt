package com.app.messej.data.repository.pagingSources


import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PodiumMaidanCompetitorStatsPagingSource(private val api: PodiumAPIService) : PagingSource<Int, PodiumMaidanSupporter>() {
    companion object {
        private const val STARTING_KEY = 1
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PodiumMaidanSupporter> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getCompetitorStats(page = currentPage)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (!data.hasNext) null else currentPage.inc()

                LoadResult.Page(
                    data = data.users, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, PodiumMaidanSupporter>): Int? {
        return null
    }
}