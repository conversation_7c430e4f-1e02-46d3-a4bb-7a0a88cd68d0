package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ForwardAPIService
import com.app.messej.data.model.api.forward.Forward
import com.app.messej.data.model.enums.ForwardType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1
class ForwardDataSource(private val api: ForwardAPIService, private val  forwardType: @JvmSuppressWildcards ForwardType): PagingSource<Int, Forward>() {

    private var pageState: String? = null
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Forward> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage =  params.key ?: STARTING_KEY
                val response = currentPage.let {
                    when(forwardType){
                        ForwardType.MESSAGES ->  api.getForwardPrivateMessageList("PRIVATE",50, pageState = pageState,page=currentPage)
                        ForwardType.GROUPS ->  api.getForwardList(forwardType.type,50,page=currentPage)
                        ForwardType.HUDDLES ->  api.getForwardList(forwardType.type,50,page=currentPage) }
                }
                val responseData = mutableListOf<Forward>()

                val data = response.body()?.result?.forwardsList ?: emptyList()
                responseData.addAll(data)
                if (currentPage == STARTING_KEY && response.body()?.result?.pageState != null) {
                    pageState = response.body()?.result?.pageState
                }
                val nextKey = if (response.body()?.result?.nextPage == false) null else currentPage.plus(1)
                LoadResult.Page(
                    data = response.body()?.result?.forwardsList?: emptyList(), prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, Forward>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}