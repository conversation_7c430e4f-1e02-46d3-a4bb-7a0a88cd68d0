package com.app.messej.data.api

import com.app.messej.data.model.LegalAffairPermission
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.CaseVoteRequest
import com.app.messej.data.model.api.DefendCaseRequest
import com.app.messej.data.model.api.FileCaseRequest
import com.app.messej.data.model.api.ReportCategoryResponse
import com.app.messej.data.model.api.ReportRequest
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.legal.CaseDetails
import com.app.messej.data.model.api.legal.LegalAffairsPayRequest
import com.app.messej.data.model.api.legal.LegalAffairsPendingFineResponse
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface LegalAffairsAPIService {

    @GET("/user/report/categories")
    @Headers("Accept: application/json")
    suspend fun getUserReportCategories(): Response<APIResponse<ReportCategoryResponse>>

    @GET("/user/report-proof-files/secrets")
    @Headers("Accept: application/json")
    suspend fun getUploadCredentials(): Response<APIResponse<UploadCredentialsResponse>>

    @POST("/user/reports")
    @Headers("Accept: application/json")
    suspend fun reportContent(@Body request: ReportRequest): Response<APIResponse<Unit>>

    @GET("/user/legal-affairs/my-legal-records")
    @Headers("Accept: application/json")
    suspend fun getLegalAffairsList(
        @Query("page") page: Int? = null,
        @Query("record_type") recordType: String? = null,
        @Query("status") status: String? = null,
        @Query("reporting_type") reportingType: String? = null,
    ): Response<APIResponse<LegalRecordsResponse>>

    @GET("/user/legal-affairs/boards")
    @Headers("Accept: application/json")
    suspend fun getLegalAffairBoardsList(
        @Query("record_type") recordType: String? = null,
        @Query("tab") tab: String? = null,
        @Query("reporting_type") reportingType: String? = null,
        @Query("page") page: Int? = null,
    ): Response<APIResponse<LegalRecordsResponse>>

    @GET("/user/legal-affairs/legal-records/{id}")
    @Headers("Accept: application/json")
    suspend fun getCaseDetails(
        @Path("id") caseId: Int,
    ): Response<APIResponse<CaseDetails>>

    @POST("/user/legal-affairs/file-case")
    @Headers("Accept: application/json")
    suspend fun fileCase(@Body request: FileCaseRequest): Response<APIResponse<Unit>>

    @POST("/user/legal-affairs/legal-records/{id}/defend")
    @Headers("Accept: application/json")
    suspend fun defendCase(
        @Path("id") id: Int?,
        @Body request: DefendCaseRequest
    ): Response<APIResponse<Unit>>
    @POST("/user/legal-affairs/vote")
    @Headers("Accept: application/json")
    suspend fun vote(@Body request: CaseVoteRequest): Response<APIResponse<Unit>>

    @POST("/user/legal-affairs/pay")
    @Headers("Accept: application/json")
    suspend fun legalAffairsPay(@Body request: LegalAffairsPayRequest): Response<APIResponse<Unit>>

    @GET("/user/legal-affairs/fines")
    @Headers("Accept: application/json")
    suspend fun getPendingFines(@Query("enforcement") enforcement: String? = null, @Query("report_id") reportId: Int? = null): Response<APIResponse<LegalAffairsPendingFineResponse>>

    @GET("/user/legal-affairs/permissions/{id}")
    @Headers("Accept: application/json")
    suspend fun legalAffairsCheckPermission(
        @Path("id") id : String,
        @Query("source") source : String,
        @Query("parent_id") parentId : String?
    ) : Response<APIResponse<LegalAffairPermission>>

}