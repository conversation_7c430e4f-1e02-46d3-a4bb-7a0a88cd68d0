package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.PenaltyKickTarget
import com.google.gson.annotations.SerializedName

data class PodiumChallengePenaltyTargetPayload(
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("target_point") val _target: Int
) : SocketEventPayload() {
    val target: PenaltyKickTarget?
        get() = PenaltyKickTarget.entries.find { it.target == _target }
}