package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.AcceptDecline
import com.google.gson.annotations.SerializedName

data class PodiumSpeakerInviteResponsePayload(
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("invitee_id") val inviteeId: Int,
    @SerializedName("invited_by") val invitedBy: Int,
    @SerializedName("action") val action: AcceptDecline,
    @SerializedName("invitee_name") val inviteeName: String,
    @SerializedName("participants_count") val participantsCount: Int,
)