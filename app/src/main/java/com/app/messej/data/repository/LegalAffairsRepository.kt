package com.app.messej.data.repository

import android.content.Context
import android.util.Log
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.LegalAffairsAPIService
import com.app.messej.data.model.LegalAffairPermission
import com.app.messej.data.model.ReportProofMedia
import com.app.messej.data.model.api.CaseVoteRequest
import com.app.messej.data.model.api.DefendCaseRequest
import com.app.messej.data.model.api.FileCaseRequest
import com.app.messej.data.model.api.ReportCategoryResponse
import com.app.messej.data.model.api.ReportRequest
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.legal.CaseDetails
import com.app.messej.data.model.api.legal.LegalAffairsPayRequest
import com.app.messej.data.model.api.legal.LegalAffairsPendingFineResponse
import com.app.messej.data.model.api.legal.LegalRecordsResponse
import com.app.messej.data.model.enums.CaseVerdictAction
import com.app.messej.data.model.enums.LegalAffairPermissionSource
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.repository.pagingSources.LegalAffairsBoardsDataSource
import com.app.messej.data.repository.pagingSources.LegalAffairsListDataSource
import com.app.messej.data.repository.pagingSources.LegalAffairsPendingFinesDataSource
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine.PayFineViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Repository to handle content reports
 */
class LegalAffairsRepository(private var mContext: Context): BaseMediaUploadRepository(mContext) {

    suspend fun getReportCategories(): ResultOf<ReportCategoryResponse> {
        return try {
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java).getUserReportCategories()
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    override suspend fun getVideoSecrets(): ResultOf<UploadCredentialsResponse> {
        return try {
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java, true).getUploadCredentials()
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun reportContent(req: ReportRequest): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java).reportContent(req)
            val result = APIUtil.handleResponseWithoutResult(resp)
            if (result is ResultOf.Success) {
                if (req.reportType== ReportType.REPORT_AND_HIDE) {
                    when(req.contentType) {
                        ReportContentType.HUDDLE_POST -> db.getChatMessageDao().deleteHuddleChatMessages(listOf(req.contentId))
                        else -> {}
                    }
                }
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun processVideo(med: ReportProofMedia, listener: VideoEncoderUtil.MediaProcessingListener): File {
        return VideoEncoderUtil.encodeVideoMedia3(med, listener)
    }

    suspend fun uploadMedia(med: ReportProofMedia): ResultOf<Unit> = withContext(Dispatchers.IO) {
        val result = prepareAndUploadMultipartMedia(med)
        if (result is ResultOf.Success) {
            med.mediaUploaded = true
        } else {
            med.mediaUploaded = false
        }
        result
    }

    private suspend fun prepareAndUploadMultipartMedia(media: ReportProofMedia): ResultOf<Unit> {
        try {
            MediaUploadListener.post(media.uuid, 0)
            performMultipartMediaUpload(media.s3UploadMedia).collect {
                when (val res = it) {
                    MultipartMediaUploadResult.Complete -> {

                    }
                    is MultipartMediaUploadResult.Error -> throw res.error
                    is MultipartMediaUploadResult.Progress -> {
                        Log.d("ENCODE", "posting ${res.percent} to MUL")
                        MediaUploadListener.post(media.uuid, res.percent)
                    }
                }
            }
            MediaUploadListener.clear(media.uuid)
        } catch (e: Exception) {
            MediaUploadListener.clear(media.uuid)
            Log.e("ENCODE", "prepareAndUploadMultipartMedia:", e)
            return ResultOf.getError(e)
        }

        Log.d("ENCODE", "prepareAndUploadMultipartMedia: end of uploading Method")
        return ResultOf.Success(Unit)
    }

    fun getViolationList(
        recordType: String?,
        status: String? = null,
        reportingType: String? = null,
        countCallBack: ((LegalRecordsResponse?) -> Unit)? = null
    ): Pager<Int, LegalRecordsResponse.ReportCase> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = {
                LegalAffairsListDataSource(
                    apiService = APIServiceGenerator.createService(LegalAffairsAPIService::class.java),
                    recordType = recordType,
                    status = status,
                    reportingType = reportingType,
                    countCallBack = countCallBack
                )
            }
        )
    }

    fun getLegalAffairsBoardDetails(
        recordType: String? = null,
        tab: String? = null,
        reportingType: String? = null,
        countCallBack: ((LegalRecordsResponse?) -> Unit)? = null
    ): Pager<Int, LegalRecordsResponse.ReportCase> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = {
                LegalAffairsBoardsDataSource(
                    apiService = APIServiceGenerator.createService(LegalAffairsAPIService::class.java),
                    recordType = recordType,
                    tab = tab,
                    reportingType = reportingType,
                    countCallBack = countCallBack
                )
            }
        )
    }

    suspend fun getCaseDetails(id: Int): ResultOf<CaseDetails> {
        return try {
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java).getCaseDetails(id)
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun fileCase(req: FileCaseRequest): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java).fileCase(req)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun voteInInvestigation(caseId: Int, guilty: Boolean): ResultOf<String> {
        return try {
            val req = CaseVoteRequest(caseId,guilty,null)
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java).vote(req)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun voteInJury(caseId: Int, guilty: Boolean, action: CaseVerdictAction): ResultOf<String> {
        return try {
            val req = CaseVoteRequest(caseId,guilty,action)
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java).vote(req)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun defendCase(req: DefendCaseRequest, id: Int?): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java).defendCase(id = id, request = req)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun legalAffairsPay(req: LegalAffairsPayRequest) : ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java).legalAffairsPay(request = req)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    fun getPendingFines(params: PayFineViewModel.FineParams?,totalFineAmountCallBack: (Double) -> Unit): Pager<Int, LegalAffairsPendingFineResponse.LegalAffairsFine> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            pagingSourceFactory = {
                LegalAffairsPendingFinesDataSource(
                    fineParams = params,
                    apiService = APIServiceGenerator.createService(LegalAffairsAPIService::class.java),
                    totalFineAmountCallBack = totalFineAmountCallBack
                )
            }
        )
    }

    suspend fun checkPermission(id: String, source: LegalAffairPermissionSource, parentId: String? = null) : ResultOf<LegalAffairPermission> {
        return try {
            val resp = APIServiceGenerator.createService(LegalAffairsAPIService::class.java)
                .legalAffairsCheckPermission(id = id, source = source.value, parentId = parentId)
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }
}