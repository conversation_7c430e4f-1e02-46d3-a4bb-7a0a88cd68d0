package com.app.messej.data.model

import androidx.databinding.BaseObservable
import com.app.messej.data.model.enums.MediaType

abstract class AbstractUriMedia : BaseObservable() {
    abstract val mimeType : String?

    open val mediaType : MediaType
        get() {
            return if (mimeType?.contains("video") == true) MediaType.VIDEO else MediaType.IMAGE
        }
    abstract val thumbnail : String?
}
