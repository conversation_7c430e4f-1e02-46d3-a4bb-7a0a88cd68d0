package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.entity.FlashVideo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FlashSearchDataSource(private val api: FlashAPIService, private val searchKeyWord: String?): PagingSource<Int, FlashVideo>() {

    companion object {
        private const val STARTING_KEY = 0
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FlashVideo> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.flashSearch(
                    keyword = if (searchKeyWord.isNullOrBlank()) null else searchKeyWord,
                    offset = currentPage
                )
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (!data.hasNextPage || data.flashOffset >= data.flashTotal) null else data.flashOffset
                data.flashes.forEach { it.sanitize() }
                LoadResult.Page(
                    data = data.flashes, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, FlashVideo>): Int? {
        return null
    }
}