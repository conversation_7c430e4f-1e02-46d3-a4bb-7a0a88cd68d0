package com.app.messej.data.model.entity

import androidx.room.TypeConverter
import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.Gson
import com.google.gson.JsonElement
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class NotificationAssociate(
    @SerializedName("other_data") val otherData: JsonElement? = null,
    @SerializedName("flash_id"          ) val flashId           : String?=null,
    @SerializedName("comment_id"        ) val commentId              : String?=null,
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("contributed_coins") val contributedCoins: String,
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("podium_name") val podiumName: String,
    @SerializedName("citizenship_priority") val citizenshipPriority: String?=null,
    @SerializedName("user_level_animation_url") val userLevelAnimationUrl : String?,
    @SerializedName("citizenship") val citizenship: UserCitizenship?,
    @SerializedName("userId") val userId: Int?,
    @SerializedName("superstar_name") val superStarName: String?,
    @SerializedName("content") val content: String? = null,

    @SerializedName("animation_for_president"   ) val animationForPresident   : String?=null,
    @SerializedName("current_president_name"   ) val currentPresidentName   : String?=null,
    @SerializedName("current_president_id"   ) val currentPresidentId   : Int?=null,
    @SerializedName("thumbnail_url"          ) val thumbnailUrl         :String?=null,
    @SerializedName("profile_url"          ) val profileUrl         :String?=null,
    @SerializedName("required_user_rating" ) val requiredUserRating   :Int?=null
){
    class Converter {
        @TypeConverter
        fun decode(data: String?): NotificationAssociate? {
            data?: return null
            val type: Type = object : TypeToken<NotificationAssociate?>() {}.type
            return Gson().fromJson<NotificationAssociate>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: NotificationAssociate?): String? {
            return Gson().toJson(someObjects)
        }
    }
}

