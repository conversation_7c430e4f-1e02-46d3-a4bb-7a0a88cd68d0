package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class SubscriptionPayload(
    @SerializedName("name") var name: String,
    @SerializedName("membership") var membership: UserType?= UserType.FREE,
    @SerializedName("user_id") var userId: Int,
    @SerializedName("username") var username: String,
    @SerializedName("country") var country: String,
)