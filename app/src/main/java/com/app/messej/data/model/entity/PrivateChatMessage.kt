package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.ActivityMeta
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.entity.PrivateChatMessage.Companion.COLUMN_ROOM_ID
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.MessageDeliveryReadStatus
import com.app.messej.data.model.socket.PrivateChatMessagePayload
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

@Entity(
    tableName = EntityDescriptions.TABLE_PRIVATE_CHAT_MESSAGES,
    indices = [
        Index(COLUMN_ROOM_ID, unique = false),
        Index(PrivateChatMessage.COLUMN_MESSAGE_CREATED, unique = false)
    ]
)
@TypeConverters(
    ActivityMeta.Converter::class,
    ReplyTo.Converter::class,
    MediaMeta.Converter::class
)
data class PrivateChatMessage(
    @SerializedName("message_id"    ) @ColumnInfo(name = COLUMN_MESSAGE_ID          ) @PrimaryKey(autoGenerate = false) override val messageId     : String,
    @SerializedName("room_id"       ) @ColumnInfo(name = COLUMN_ROOM_ID             ) override val roomId        : String,
    @SerializedName("message"       ) @ColumnInfo(name = "message"                  ) override var rawMessage       : String?,

    @SerializedName("created"       ) @ColumnInfo(name = COLUMN_MESSAGE_CREATED     ) override val createdTime   : String,
    @SerializedName("delivered"     ) @ColumnInfo(name = COLUMN_MESSAGE_DELIVERED   ) override val deliveredTime : String? = null,
    @SerializedName("sent"          ) @ColumnInfo(name = COLUMN_MESSAGE_SENT        ) override val sentTime      : String? = null,

    @SerializedName("deleted"       ) @ColumnInfo(name = "deleted"                  ) override val deleted       : Boolean = false,

    @SerializedName("is_activity"   ) @ColumnInfo(name = "is_activity"              ) override val isActivity    : Boolean = false,
    @SerializedName("activity_meta" ) @ColumnInfo(name = "activity_meta"            ) override val activityMeta  : ActivityMeta? = null,
    @SerializedName("media"         ) @ColumnInfo(name = COLUMN_MEDIA               ) override var media         : String? = null,
    @SerializedName("media_meta"    ) @ColumnInfo(name = "media_meta"               ) override var mediaMeta     : MediaMeta? = null,
    @SerializedName("message_type"  ) @ColumnInfo(name = "message_type"             ) override var internalMessageType  : MessageType? = null,

    @SerializedName("read"          ) @ColumnInfo(name = COLUMN_MESSAGE_READ        ) override val read          : String? = null,
    @SerializedName("receiver"      ) @ColumnInfo(name = "receiver"                 ) override val receiver      : Int,
    @SerializedName("reply_to"      ) @ColumnInfo(name = "reply_to"                 ) override val replyTo       : ReplyTo? = null,
    @SerializedName("sender"        ) @ColumnInfo(name = "sender"                   ) override val sender        : Int,

    @SerializedName("liked"         ) @ColumnInfo(name = "liked"                    ) override val liked         : Boolean = false,
    @SerializedName("reported"      ) @ColumnInfo(name = "reported"                 ) override val reported      : Boolean = false,

    @SerializedName("blocked"       ) @ColumnInfo(name = "blocked"                  )          val blocked       : Boolean = false,
    @SerializedName("user_id"       ) @ColumnInfo(name = "user_id"                  )          val userId       : Int,
    @SerializedName("color"          ) @ColumnInfo(name = "color") override val chatTextColor         : ChatTextColor?,
    @SerializedName("forward_id"    ) @ColumnInfo(name = "forward_id")                override  var forwardId   : String?  = null

//    @SerializedName("chat_type"       ) @ColumnInfo(name = "blocked"                  )          val blocked       : Boolean = false,

): AbstractChatMessage() {

    val messageDeliveryReadStatus: MessageDeliveryReadStatus?
        get() {
            return if ((sendStatus != SendStatus.NONE)){
                MessageDeliveryReadStatus.PENDING
            } else if (isRead) {
                MessageDeliveryReadStatus.READ
            } else if (isDelivered){
                MessageDeliveryReadStatus.DELIVERED
            }else {
                MessageDeliveryReadStatus.SENT
            }
        }

    val socketpayload: PrivateChatMessagePayload
        get() {
            val payload = PrivateChatMessagePayload(messageId, roomId, receiver, rawMessage, chatTextColor,forwardId)
            mediaMeta?.let { med ->
                payload.setMedia(med)
            }
            replyTo?.let {
                payload.replyTo = it.messageId
                payload.replyId = it.replyId
            }
            return payload
        }

    companion object {
        private const val ROOM_ID_PREFIX = "CHAT"

        const val COLUMN_MESSAGE_ID = "message_id"
        const val COLUMN_ROOM_ID = "room_id"
        const val COLUMN_MESSAGE_CREATED = "created"
        const val COLUMN_MESSAGE_DELIVERED = "delivered"
        const val COLUMN_MESSAGE_SENT = "sent"
        const val COLUMN_MEDIA = "media"
        const val COLUMN_MESSAGE_READ = "read"

        fun getChatRoomId(sender: Int, receiver: Int): String {
            return if (sender<receiver)"$ROOM_ID_PREFIX#$sender#$receiver"
            else "$ROOM_ID_PREFIX#$receiver#$sender"
        }

    }

    class Converter {
        @TypeConverter
        fun decode(data: String?): PrivateChatMessage? {
            data?: return null
            val type: Type = object : TypeToken<PrivateChatMessage?>() {}.type
            return Gson().fromJson<PrivateChatMessage>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: PrivateChatMessage?): String? {
            return Gson().toJson(someObjects)
        }
    }
}