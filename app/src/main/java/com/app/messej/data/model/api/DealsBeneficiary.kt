package com.app.messej.data.model.api


import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class DealsBeneficiary(
    @SerializedName("citizenship"                       ) override val citizenship: UserCitizenship,
    @SerializedName("country"                           ) override val countryCode: String,
    @SerializedName("created_on"                        ) val createdOn: String,
    @SerializedName("disable_broadcast_notification"    ) val disableBroadcastNotification: Bo<PERSON>an,
    @SerializedName("email"                             ) val email: String,
    @SerializedName("id"                                ) override val id: Int,
    @SerializedName("inactive_checkpoint"               ) val inactiveCheckpoint: Int,
    @SerializedName("ip"                                ) val ip: String,
    @SerializedName("is_leader"                         ) val isLeader: <PERSON><PERSON><PERSON>,
    @SerializedName("membership"                        ) override val membership: UserType,
    @SerializedName("name"                              ) override var name: String,
    @SerializedName("payment_date"                      ) val paymentDate: String,
    @SerializedName("phone"                             ) val phone: String?,
    @SerializedName("pp"                                ) val pp: Double,
    @SerializedName("profile_image"                     ) override val thumbnail: String,
    @SerializedName("status"                            ) val status: String,
    @SerializedName("superstar"                         ) val superstar: String,
    @SerializedName("username"                          ) override val username: String,
    @SerializedName("verified"                          ) override val verified: Boolean,
) : AbstractUser()