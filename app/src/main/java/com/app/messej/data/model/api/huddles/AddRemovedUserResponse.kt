package com.app.messej.data.model.api.huddles


import com.google.gson.annotations.SerializedName

data class AddRemovedUserResponse(
    @SerializedName("active") val active: Boolean? = null,
    @SerializedName("admin_status") val adminStatus: String? = null,
    @SerializedName("comment") val comment: Boolean?=null,
    @SerializedName("huddle_id") val huddleId: Int?=null,
    @SerializedName("member_id") val memberId: Int?=null,
    @SerializedName("member_name") val memberName: String?=null,
    @SerializedName("member_username") val memberUsername: String?=null,
    @SerializedName("mute") val mute: Boolean?=null,
    @SerializedName("pin") val pin: String? = null,
    @SerializedName("post") val post: Boolean?=null,
    @SerializedName("reply") val reply: Boolean?=null,
    @SerializedName("role") val role: String?=null,
    @SerializedName("status") val status: String?=null,
    @SerializedName("time_created") val timeCreated: String?=null,
    @SerializedName("time_updated") val timeUpdated: String?=null,
)