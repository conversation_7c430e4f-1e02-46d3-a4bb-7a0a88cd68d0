package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.PrivateChat
import com.google.gson.annotations.SerializedName

data class AcceptChatRequest(
    @SerializedName("chatroom") val chatRoomId: String,
    @SerializedName("currentChatType") val currentChatType: PrivateChat.ChatType = PrivateChat.ChatType.REQUEST,
    @SerializedName("changeChatType") val changeChatType: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE,
){
}
