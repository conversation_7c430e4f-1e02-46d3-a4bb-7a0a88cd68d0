package com.app.messej.data.model.api.business

import com.google.gson.annotations.SerializedName

data class BusinessPayoutStatus(
    @SerializedName("minimum_pp_for_first_payout")
    val minimumPpForFirstPayout: Double? = null,
    @SerializedName("minimum_pp_for_fourth_payout")
    val minimumPpForFourthPayout: Double? = null,
    @SerializedName("minimum_pp_for_second_payout")
    val minimumPpForSecondPayout: Double? = null,
    @SerializedName("minimum_pp_for_third_payout")
    val minimumPpForThirdPayout: Double? = null,
    @SerializedName("payout_min_app_shares_monthly")
    val payoutMinAppSharesMonthly: Int? = null,
    @SerializedName("payout_min_broadcasts")
    val payoutMinBroadcasts: Int? = null,
    @SerializedName("payout_min_dears")
    val payoutMinDears: Int? = null,
    @SerializedName("payout_min_huddles")
    val payoutMinHuddles: Int? = null,
    @SerializedName("payout_min_likes")
    val payoutMinLikes: Int? = null,
    @SerializedName("payout_min_no_fans")
    val payoutMinNoFans: Int? = null,
    @SerializedName("payout_min_no_likers")
    val payoutMinNoLikers: Int? = null,
    @SerializedName("payout_min_points_for_next_review")
    val payoutMinPointsForNextReview: Int? = null,
    @SerializedName("payout_min_total_participants_in_huddles")
    val payoutMinTotalParticipantsInHuddles: Int? = null,
    @SerializedName("payout_min_total_posts_by_others_participants")
    val payoutMinTotalPostsByOthersParticipants: Int? = null,
    @SerializedName("refundable_pp")
    val refundablePp: Double? = null
)