package com.app.messej.data.room.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.app.messej.data.model.entity.RecentSearch
import com.app.messej.data.model.enums.RecentSearchType
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class RecentSearchDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(keyword: RecentSearch): Long

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_RECENT_SEARCHES} WHERE ${RecentSearch.SCREEN_TYPE} = :recentSearchType ORDER BY ${RecentSearch.COLUMN_TIME_CREATED} DESC LIMIT 6")
    abstract fun getRecentSearches(recentSearchType: RecentSearchType): PagingSource<Int, RecentSearch>

    @Query("SELECT COUNT(*) FROM ${EntityDescriptions.TABLE_RECENT_SEARCHES} WHERE ${RecentSearch.SCREEN_TYPE} = :recentSearchType")
    abstract fun getRecentSearchesCount(recentSearchType: RecentSearchType): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_RECENT_SEARCHES}")
    abstract suspend fun clearRecentSearches()

    @Query("DELETE FROM ${EntityDescriptions.TABLE_RECENT_SEARCHES} WHERE ${RecentSearch.COLUMN_ID} IN (SELECT ${RecentSearch.COLUMN_ID} FROM ${EntityDescriptions.TABLE_RECENT_SEARCHES} WHERE ${RecentSearch.SCREEN_TYPE} = :recentSearchType ORDER BY ${RecentSearch.COLUMN_TIME_CREATED} ASC LIMIT :deleteCount)")
    abstract fun deleteOldRecentSearches(deleteCount : Int, recentSearchType: RecentSearchType)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_RECENT_SEARCHES} WHERE ${RecentSearch.COLUMN_KEYWORD} = :keyword AND ${RecentSearch.SCREEN_TYPE} = :recentSearchType")
    abstract suspend fun clearDuplicateRecentSearches(keyword : String, recentSearchType: RecentSearchType)
}