package com.app.messej.data.model.api.postat

data class BlockedUsersResult(
    val blockedUsers: List<BlockedUser>
)
{
    data class BlockedUser(
        val userId: Int,
        val name: String,
        val deleted_account: <PERSON><PERSON><PERSON>,
        val is_premium: <PERSON><PERSON><PERSON>,
        val profile_url: String?,
        val thumbnail_url: String?,
        val verified: <PERSON><PERSON>an,
        val country_name: String,
        val country_code: String?,
        val blocked_by_admin: <PERSON><PERSON><PERSON>,
        val blocked_by_leader: <PERSON><PERSON><PERSON>,
        val citizenship: String
    )

}

