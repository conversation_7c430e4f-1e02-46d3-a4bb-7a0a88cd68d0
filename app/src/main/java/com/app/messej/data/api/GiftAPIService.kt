package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.gift.GiftConversionHistoryResponse
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.api.gift.GiftListResponse
import com.app.messej.data.model.api.gift.GiftNotificationResponse
import com.app.messej.data.model.api.gift.GiftPointToFlaxConvertRequest
import com.app.messej.data.model.api.gift.GiftPointToFlaxConvertResponse
import com.app.messej.data.model.api.gift.GiftPurchaseHistoryResponse
import com.app.messej.data.model.api.gift.GiftReceivedHistoryResponse
import com.app.messej.data.model.api.gift.GiftReplyRequest
import com.app.messej.data.model.api.gift.GiftResponse
import com.app.messej.data.model.api.gift.GiftSendRequest
import com.app.messej.data.model.api.gift.GiftSendResponse
import com.app.messej.data.model.api.gift.GiftSendersHistoryResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface GiftAPIService {
    @POST("/user/coins/convert")
    @Headers("Accept: application/json")
    suspend fun giftPointToFlaxConvert(@Body req: GiftPointToFlaxConvertRequest): Response<APIResponse<GiftPointToFlaxConvertResponse>>

    @GET("/user/gift/purchasehistory")
    @Headers("Accept: application/json")
    suspend fun getGiftPurchaseHistory(@Query("page") page: Int, @Query("limit") limit: Int): Response<APIResponse<GiftPurchaseHistoryResponse>>

    @GET("/user/gift/receivedhistory")
    @Headers("Accept: application/json")
    suspend fun getGiftReceivedHistory(@Query("page") page: Int, @Query("limit") limit: Int,@Query("gift_type") giftType:String): Response<APIResponse<GiftReceivedHistoryResponse>>

    @GET("user/gift/{id}")
    @Headers("Accept: application/json")
    suspend fun getGiftSendersHistory(
        @Path("id") sendersId: Int,
        @Query("page") page: Int,
        @Query("limit") limit: Int,
        @Query("sender_history") senderHistory: Boolean,
    ): Response<APIResponse<GiftSendersHistoryResponse>>

    @GET("user/gift/{id}")
    @Headers("Accept: application/json")
    suspend fun getGiftDetailsInSendersHistory(@Path("id") sendersId: Int, @Query("sender_history") senderHistory: Boolean): Response<APIResponse<GiftItem>>

    @GET("/user/coins/convert")
    @Headers("Accept: application/json")
    suspend fun getPurchaseCoins(): Response<APIResponse<GiftResponse>>

    @GET("user/gift/pointsforgift")
    @Headers("Accept: application/json")
    suspend fun getGiftList(@Query("page") page: Int,@Query("gift_type") giftType:String,@Query("birthday") birthday:Boolean?=false,@Query("citizenship_upgrade") citizenshipUpgrade:Boolean?=false,@Query("current_date") currentDate:String): Response<APIResponse<GiftListResponse>>

    @POST("user/gift/sendgift?send=True")
    @Headers("Accept: application/json")
    suspend fun sendGift(@Body req: GiftSendRequest): Response<APIResponse<GiftSendResponse>>

    @POST("user/gift/sendgift?send=False")
    @Headers("Accept: application/json")
    suspend fun sendGiftPreview(@Body req: GiftSendRequest): Response<APIResponse<GiftSendResponse>>

    @POST("/user/gift/{id}/acknowledge")
    @Headers("Accept: application/json")
    suspend fun sendReplyGift(@Path("id") id: Int, @Body req: GiftReplyRequest): Response<APIResponse<Unit>>

    @GET("/user/gift/{id}/getdetails")
    @Headers("Accept: application/json")
    suspend fun getGift(@Path("id") giftId: Int): Response<APIResponse<GiftNotificationResponse>>

    @GET("/user/coins/conversionhistory")
    @Headers("Accept: application/json")
    suspend fun getGiftConversionHistory(@Query("page") page: Int, @Query("limit") limit: Int,@Query("history_type") history_type:String ): Response<APIResponse<GiftConversionHistoryResponse>>

    @GET("/user/video/secrets?gift=true")
    @Headers("Accept: application/json")
    suspend fun getGiftVideoDownloadCredentials(): Response<APIResponse<UploadCredentialsResponse>>

    @GET("/user/gift/saythanks")
    @Headers("Accept: application/json")
    suspend fun getSayThanksGift():Response<APIResponse<GiftNotificationResponse>>
}