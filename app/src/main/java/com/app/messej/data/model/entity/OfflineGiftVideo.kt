package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.entity.OfflineGiftVideo.Companion.COLUMN_GIFT_ID
import com.app.messej.data.model.entity.OfflineGiftVideo.Companion.COLUMN_MEDIA_KEY
import com.app.messej.data.room.EntityDescriptions

@Entity(
    tableName = EntityDescriptions.TABLE_OFFLINE_GIFT_VIDEO,
    indices = [
        Index(COLUMN_GIFT_ID, unique = true),
        Index(COLUMN_MEDIA_KEY, unique = true)
    ]
)
@TypeConverters(MediaUploadState.Converter::class)
/**
 * used to represent a gift video file in local storage
 * @param key: The S3 Key of the media file
 * @param name: the filename with extension
 * @param path: the absolutePath of the file in storage
 */
data class OfflineGiftVideo (
    @PrimaryKey(autoGenerate = false) @ColumnInfo(name = COLUMN_GIFT_ID) var giftId: Int,
    @ColumnInfo(name = COLUMN_MEDIA_KEY)
    val key: String,
    @ColumnInfo(name = COLUMN_MEDIA_NAME)
    var name: String,
    var path: String,
){

    companion object {
        const val COLUMN_GIFT_ID = "gift_id"
        const val COLUMN_MEDIA_NAME = "name"
        const val COLUMN_MEDIA_KEY = "key"
    }

}