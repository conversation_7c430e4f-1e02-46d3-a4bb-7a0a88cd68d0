package com.app.messej.data.repository

import android.app.Application
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.BroadcastAPIService
import com.app.messej.data.model.ChatMessageSearchResult
import com.app.messej.data.model.entity.BroadcastChatMessageWithMedia
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.repository.mediators.BroadcastIncomingRemoteMediator
import com.app.messej.data.repository.mediators.BroadcastOutgoingRemoteMediator
import com.app.messej.data.room.FlashatDatabase

class BroadcastRepository(context: Application) {

    private val accountRepo: AccountRepository = AccountRepository(context)

    private val db = FlashatDatabase.getInstance(context)

    @OptIn(ExperimentalPagingApi::class)
    fun getOutgoingBroadcastPager(mode: BroadcastMode) : Pager<Int,BroadcastChatMessageWithMedia> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = BroadcastOutgoingRemoteMediator(accountRepo.user.id, mode, db, APIServiceGenerator.createService(BroadcastAPIService::class.java)),
            pagingSourceFactory = { db.getChatMessageDao().outgoingBroadcastsPagingSource(accountRepo.user.id,mode) }
        )
    }

    suspend fun getOutgoingBroadcastSearchPositions(mode: BroadcastMode, search: String): List<ChatMessageSearchResult> {
        return db.getChatMessageDao().outgoingBroadcastsSearchResult(accountRepo.user.id,mode, search)
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getOutgoingStarredBroadcastPager(mode: BroadcastMode) : Pager<Int,BroadcastChatMessageWithMedia> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = BroadcastOutgoingRemoteMediator(accountRepo.user.id, mode, db, APIServiceGenerator.createService(BroadcastAPIService::class.java)),
            pagingSourceFactory = { db.getChatMessageDao().outgoingStarredBroadcastsPagingSource(accountRepo.user.id,mode) }
        )
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getIncomingBroadcastPager(broadcaster: Int) : Pager<Int,BroadcastChatMessageWithMedia> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = BroadcastIncomingRemoteMediator(broadcaster, db, APIServiceGenerator.createService(BroadcastAPIService::class.java)),
            pagingSourceFactory = { db.getChatMessageDao().incomingBroadcastsPagingSource(broadcaster) }
        )
    }

    suspend fun getIncomingBroadcastSearchPositions(broadcaster: Int, search: String): List<ChatMessageSearchResult> {
        return db.getChatMessageDao().incomingBroadcastsSearchResult(broadcaster, search)
    }

    @OptIn(ExperimentalPagingApi::class)
    fun getIncomingStarredBroadcastPager(broadcaster: Int) : Pager<Int,BroadcastChatMessageWithMedia> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = BroadcastIncomingRemoteMediator(broadcaster, db, APIServiceGenerator.createService(BroadcastAPIService::class.java)),
            pagingSourceFactory = { db.getChatMessageDao().incomingStarredBroadcastsPagingSource(broadcaster) }
        )
    }

}