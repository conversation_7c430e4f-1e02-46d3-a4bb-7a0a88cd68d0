package com.app.messej.data.model.enums

import com.app.messej.data.utils.EnumUtil.except
import com.google.gson.annotations.SerializedName

enum class CaseVerdictAction {
        @SerializedName("DELETE_AND_FINE") DELETE_AND_FINE,
        @SerializedName("DELETE_CONTENT") DELETE_CONTENT,
        @SerializedName("ONLY_FINE") ONLY_FINE,
        @SerializedName("BAN_AND_FINE_TO_RESTORE") BAN_AND_FINE_TO_RESTORE,
        @SerializedName("BLACKLIST_AND_FINE") BLACKLIST_AND_FINE,

        // Not guilty Cases
        @SerializedName("NO_ACTION") NO_ACTION,
        @SerializedName("COMPENSATE_USER") COMPENSATE_USER,
        @SerializedName("REMOVE_BAN") REMOVE_BAN,
        @SerializedName("NO_FINE") NO_FINE;

    fun serializedName(): String {
        return javaClass.getField(name).getAnnotation(SerializedName::class.java)?.value ?: ""
    }

    val guiltyCase: Boolean
        get() = this in listOf(DELETE_AND_FINE,DELETE_CONTENT,ONLY_FINE,BAN_AND_FINE_TO_RESTORE,BLACKLIST_AND_FINE)

    val notGuiltyCase: Boolean
        get() = this in listOf(NO_ACTION,COMPENSATE_USER,REMOVE_BAN,NO_FINE)

    val hasPlaintiffFine: Boolean
        get() = this in listOf(NO_ACTION, COMPENSATE_USER, REMOVE_BAN)

    val hasDefendantFine: Boolean
        get() = this in listOf(DELETE_AND_FINE, ONLY_FINE, BLACKLIST_AND_FINE, BAN_AND_FINE_TO_RESTORE)

    val hasDefendantCompensation: Boolean
        get() = this in listOf(COMPENSATE_USER)

    companion object {
        fun fineItems() = entries.except(DELETE_CONTENT, NO_ACTION, REMOVE_BAN)
    }
}