package com.app.messej.data.utils

import com.app.messej.data.model.api.ErrorResponse

sealed class ResultOf<out T> {
    data class Success<out T>(val value: T) : ResultOf<T>()
    data class APIError(val error: ErrorResponse, val code: Int = 0) : ResultOf<Nothing>()
    data class Error(val exception: Exception) : ResultOf<Nothing>()

    companion object {
        fun getError(error: String? = null): ResultOf.Error {
            return ResultOf.Error(Exception(error))
        }

        fun getError(exception: Exception): ResultOf.Error {
            return ResultOf.Error(exception)
        }

        fun APIError.asException(): Throwable {
            return Exception(error.message)
        }
    }

    fun <P> convertTo(successConverter: (Success<T>) -> ResultOf<P>): ResultOf<P> {
        return when(this) {
            is Success -> successConverter.invoke(this)
            is Error -> Error(this.exception)
            is APIError -> APIError(this.error,this.code)
        }
    }

    fun errorMessage(): String? = when(this) {
        is Success -> null
        is Error -> this.exception.message
        is APIError -> this.error.message
    }

    fun <P> convertWithoutSuccess(): ResultOf<P> {
        return when(this) {
            is Success -> getError("Missing Success Converter")
            is Error -> Error(this.exception)
            is APIError -> APIError(this.error,this.code)
        }
    }
}