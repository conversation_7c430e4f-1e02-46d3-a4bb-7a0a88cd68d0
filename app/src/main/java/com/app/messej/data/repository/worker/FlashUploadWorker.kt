package com.app.messej.data.repository.worker

import android.app.NotificationManager
import android.content.Context
import android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.work.CoroutineWorker
import androidx.work.ForegroundInfo
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkInfo
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.app.messej.MainApplication
import com.app.messej.R
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.ResultOf
import com.app.messej.service.FlashatFirebaseMessagingService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

class FlashUploadWorker(appContext: Context, workerParams: WorkerParameters):
    CoroutineWorker(appContext, workerParams) {

    private val notificationManager = appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    companion object {
        private const val WORK_TAG = "flash_send_service"
        const val NOTIFICATION_ID = 1356

        suspend fun startIfNotRunning() = withContext(Dispatchers.IO) {

            Log.d("CMW", "checking if work is running")
            val workManager = WorkManager.getInstance(MainApplication.applicationContext())

            val future = workManager.getWorkInfosByTag(WORK_TAG)
            val existing = future.get()
            val workIsRunning = existing.any { it.state == WorkInfo.State.RUNNING }

            if (!workIsRunning) {

                Log.d("CMW", "nope. Starting work")
                val workRequest = OneTimeWorkRequestBuilder<FlashUploadWorker>()
                    .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                    .addTag(WORK_TAG)
                    .build()

                workManager.enqueue(workRequest)
            }
        }
    }

    override suspend fun doWork(): Result {
        try {
            Log.d("CMW", "Starting SYNC Worker")
            val db = FlashatDatabase.getInstance(applicationContext)
            val repo = FlashRepository(applicationContext)
            delay(100)
            db.getFlashDao().apply {
                do {
                    val flash = repo.getPendingFlash() ?: break
                    Log.d("CMW", "sending flash: ${flash.flash.id}: ${flash.flash.caption}")
                    delay(50)
                    if (repo.sendFlash(flash) !is ResultOf.Success) {
                        Log.d("CMW", "SYNC failed")
                        return Result.failure()
                    }
                } while (true)

            }
            // Indicate whether the work finished successfully with the Result
            Log.d("CMW", "SYNC complete")
            return Result.success()
        } catch (throwable: Throwable) {
            // clean up and log
            Log.d("CMW", "SYNC job cancelled")
            return Result.failure()
        }
    }

    override suspend fun getForegroundInfo() = createForegroundInfo(applicationContext.getString(R.string.chat_worker_notification_progess_sending))

    private fun createForegroundInfo(progress: String): ForegroundInfo {
        Log.d("CMW", "createForegroundInfo")
        val title = applicationContext.getString(R.string.chat_worker_notification_title)
        // Create a Notification channel if necessary
        val channel = FlashatFirebaseMessagingService.createChannel(notificationManager,"SYNC","Flash Uploads",NotificationManager.IMPORTANCE_LOW)

        val notification = NotificationCompat.Builder(applicationContext, channel.id)
            .setContentTitle(title)
            .setContentText(progress)
            .setSilent(true)
            .setSmallIcon(R.drawable.ic_notification_small)
            .setOngoing(true)
            .build()

        return if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            ForegroundInfo(NOTIFICATION_ID, notification)
        } else ForegroundInfo(NOTIFICATION_ID, notification, FOREGROUND_SERVICE_TYPE_DATA_SYNC)
    }
}