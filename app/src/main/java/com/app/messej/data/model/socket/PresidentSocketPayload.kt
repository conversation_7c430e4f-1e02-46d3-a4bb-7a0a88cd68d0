package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class PresidentSocketPayload(
    @SerializedName("user_id") val userId: Int?=null,
    @SerializedName("current_president_id") val currentPresidentId: Int?=null,
    @SerializedName("current_president_name") val currentPresidentName: String?=null,
    @SerializedName("animation_for_president") val animationForPresident: String?=null,)
