package com.app.messej.data.model.api.podium

import com.google.gson.annotations.SerializedName

data class UserStats(
    @SerializedName("rating") val rating: Int?,
    @SerializedName("generosity") private val _generosity: String? = null,
    @SerializedName("skills") private val _skills: String? = null,
){
    companion object {
        const val CROWN_THRESHOLD = 10000
    }

    val generosity: String
        get() = _generosity?:"0"

    val skills: String
        get() = _skills?:"0"

    val showCrownForGenerosity: Boolean
        get() = (_generosity?.toIntOrNull() ?: 0) > CROWN_THRESHOLD

    val showCrownForSkills: Boole<PERSON>
        get() = (_skills?.toIntOrNull() ?: 0) > CROWN_THRESHOLD

}
