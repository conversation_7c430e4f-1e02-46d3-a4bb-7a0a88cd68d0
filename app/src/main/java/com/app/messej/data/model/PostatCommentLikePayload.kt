package com.app.messej.data.model

import com.google.gson.annotations.SerializedName

class PostatCommentLikePayload(
    @SerializedName("post_id") val postId: String,
    @SerializedName("id") val id: String? = null,
    @SerializedName("reply_id") val replyId: String? = null,
    @SerializedName("reply_owner_id") val replyOwnerId: Int? = null,
    @SerializedName("is_liked") val isLiked: Boolean,
    @SerializedName("post_owner_id") val postOwnerId:Int?=null)