package com.app.messej.data.model.enums

import androidx.annotation.ColorRes
import com.app.messej.R
import com.google.gson.annotations.SerializedName

enum class ChatTextColor(@ColorRes val colorRes: Int) {
    @SerializedName("DEFAULT")      DEFAULT(R.color.textColorSecondary),
    @SerializedName("RED")          RED(R.color.chatFormatColorRed),
    @SerializedName("PINK")         PINK(R.color.chatFormatColorPink),
    @SerializedName("PURPLE")       PURPLE(R.color.chatFormatColorPurple),
    @SerializedName("DEEP_PURPLE")  DEEP_PURPLE(R.color.chatFormatColorDeepPurple),
    @SerializedName("INDIGO")       INDIGO(R.color.chatFormatColorIndigo),
    @SerializedName("BLUE")         BLUE(R.color.chatFormatColorBlue),
    @SerializedName("LIGHT_BLUE")   LIGHT_BLUE(R.color.chatFormatColorLightBlue),
    @SerializedName("CYAN")         CYAN(R.color.chatFormatColorCyan),
    @SerializedName("TEAL")         TEAL(R.color.chatFormatColorTeal),
    @SerializedName("GREEN")        GREEN(R.color.chatFormatColorGreen),
    @SerializedName("LIGHT_GREEN")  LIGHT_GREEN(R.color.chatFormatColorLightGreen),
    @SerializedName("LIME")         LIME(R.color.chatFormatColorLime),
    @SerializedName("YELLOW")       YELLOW(R.color.chatFormatColorYellow),
    @SerializedName("AMBER")        AMBER(R.color.chatFormatColorAmber),
    @SerializedName("ORANGE")       ORANGE(R.color.chatFormatColorOrange),
    @SerializedName("DEEP_ORANGE")  DEEP_ORANGE(R.color.chatFormatColorDeepOrange),
    @SerializedName("BROWN")        BROWN(R.color.chatFormatColorBrown),
    @SerializedName("GRAY")         GRAY(R.color.chatFormatColorGray),
    @SerializedName("BLUE_GRAY")    BLUE_GRAY(R.color.chatFormatColorBlueGray);

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }


    val alwaysLightColorRes: Int
        get() = when(this) {
            DEFAULT -> R.color.textColorAlwaysLightSecondary
            RED -> R.color.chatFormatColorAlwaysLightRed
            PINK -> R.color.chatFormatColorAlwaysLightPink
            PURPLE -> R.color.chatFormatColorAlwaysLightPurple
            DEEP_PURPLE -> R.color.chatFormatColorAlwaysLightDeepPurple
            INDIGO -> R.color.chatFormatColorAlwaysLightIndigo
            BLUE -> R.color.chatFormatColorAlwaysLightBlue
            LIGHT_BLUE -> R.color.chatFormatColorAlwaysLightLightBlue
            CYAN -> R.color.chatFormatColorAlwaysLightCyan
            TEAL -> R.color.chatFormatColorAlwaysLightTeal
            GREEN -> R.color.chatFormatColorAlwaysLightGreen
            LIGHT_GREEN -> R.color.chatFormatColorAlwaysLightLightGreen
            LIME -> R.color.chatFormatColorAlwaysLightLime
            YELLOW -> R.color.chatFormatColorAlwaysLightYellow
            AMBER -> R.color.chatFormatColorAlwaysLightAmber
            ORANGE -> R.color.chatFormatColorAlwaysLightOrange
            DEEP_ORANGE -> R.color.chatFormatColorAlwaysLightDeepOrange
            BROWN -> R.color.chatFormatColorAlwaysLightBrown
            GRAY -> R.color.chatFormatColorAlwaysLightGray
            BLUE_GRAY -> R.color.chatFormatColorAlwaysLightBlueGray
        }

    companion object {
        fun ChatTextColor?.orDefault(): ChatTextColor {
            return this?: DEFAULT
        }
    }
}