package com.app.messej.data.model

import android.graphics.Bitmap
import androidx.media3.common.MediaItem.ClippingConfiguration

data class VideoEditInfo(
    val trim: Trim? = null,
    val mute: Boolean = false,
    val overlay: TextOverlay? = null
) {
    data class Trim(
        val startMs: Long,
        val endMs: Long,
        val originalDurationMs: Long
    ) {
        val clippingConfig: ClippingConfiguration
            get() {
                val cfg = ClippingConfiguration.Builder()
                startMs.let { cfg.setStartPositionMs(it) }
                endMs.let { cfg.setEndPositionMs(it) }
                return cfg.build()
            }

        val trimDurationMs: Long
            get() = endMs - startMs
    }

    data class TextOverlay(
        val bitmap: Bitmap
    )
}