package com.app.messej.data.model.api.gift


import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

data class PointsPurchase(
    @SerializedName("action") val action: String? = null,
    @SerializedName("flax") val flax: Int? = null,
    @SerializedName("id") val id: Int? = null,
    @SerializedName("points") val points: Int? = null,
    @SerializedName("time_created") val timeCreated: String? = null,
    @SerializedName("time_updated") val timeUpdated: String? = null,
    @SerializedName("user_id") val userId: Int? = null,
) {
    val parsedCreatedTime: LocalDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)?.run { DateTimeUtils.getLocalDateTime(this) }

    val getParsedTime: String
        get() = DateTimeUtils.format(parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")
}