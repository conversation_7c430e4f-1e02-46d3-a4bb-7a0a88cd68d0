package com.app.messej.data.model.api.stars

import com.app.messej.data.model.entity.UserStar
import com.google.gson.annotations.SerializedName

data class StarsSearchListResponse(
    @SerializedName("stars_list"     ) var starsList     : ArrayList<UserStar> = arrayListOf(),
    @SerializedName("superstar"      ) var superstar     : UserStar?              = null,
    @SerializedName("archived_count" ) var archivedCount : Int?                 = null,
    @SerializedName("total_pages"    ) var totalPages    : Int?                 = null,
    @SerializedName("next_page"      ) var nextPage      : Boolean
)

