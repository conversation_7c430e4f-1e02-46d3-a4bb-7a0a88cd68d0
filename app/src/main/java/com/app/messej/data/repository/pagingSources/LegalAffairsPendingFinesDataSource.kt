package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.LegalAffairsAPIService
import com.app.messej.data.model.api.legal.LegalAffairsPendingFineResponse
import com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine.PayFineViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class LegalAffairsPendingFinesDataSource(
    private val fineParams: PayFineViewModel.FineParams?,
    private val apiService: LegalAffairsAPIService,
    private val totalFineAmountCallBack: (Double) -> Unit
) : PagingSource<Int, LegalAffairsPendingFineResponse.LegalAffairsFine>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, LegalAffairsPendingFineResponse.LegalAffairsFine>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, LegalAffairsPendingFineResponse.LegalAffairsFine> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = apiService.getPendingFines(fineParams?.reportCategory, fineParams?.reportId)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                totalFineAmountCallBack.invoke(data.totalFineAmount ?: 0.0)
                val nextKey = if (data.nextPage != true) null else currentPage.inc()
                LoadResult.Page(
                    data = data.reportingList ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}