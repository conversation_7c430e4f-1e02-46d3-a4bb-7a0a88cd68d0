package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.AbstractUserWithStats
import com.app.messej.data.model.enums.PrivateMessageSuggestionType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class PrivateMessagesSuggestionResponse(
    @SerializedName("suggestions") val suggestions: List<User> = listOf(),
    @SerializedName("suggestion_type") val suggestionType: PrivateMessageSuggestionType,
    @SerializedName("page") val page: Int,
    @SerializedName("user_type") val userType: String
){
    data class User(
        @SerializedName("id"            )  override val id           : Int = 0,
        @SerializedName("name"          )  override val name         : String = "",
        @SerializedName("thumbnail"     )  override val thumbnail    : String? = null,
        @SerializedName("username"      )  override val username     : String = "",

        @SerializedName("membership"    )  override val membership   : UserType = UserType.FREE,
        @SerializedName("verified"      )  override val verified     : Boolean = false,

        @SerializedName("dears"         )  override val dears        : Int     = 0,
        @SerializedName("fans"          )  override val fans         : Int     = 0,
        @SerializedName("likers"        )  override val likers       : Int     = 0,
        @SerializedName("stars"         )  override val stars        : Int     = 0,
        @SerializedName("blocked"       )  val blocked               : Boolean?
    ): AbstractUserWithStats(){
        var isSuggestion: Boolean = false

        override val citizenship: UserCitizenship
            get() = UserCitizenship.default()
    }
}
