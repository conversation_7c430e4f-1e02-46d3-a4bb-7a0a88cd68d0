package com.app.messej.data.model

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class ReplyToSimple(
    @SerializedName("message_id" ) var messageId : String? = null,
    @SerializedName("reply_id"   ) var replyId   : String? = null
) {
    class Converter {
        @TypeConverter
        fun decode(data: String?): ReplyToSimple? {
            data?: return null
            val type: Type = object : TypeToken<ReplyToSimple?>() {}.type
            return Gson().fromJson<ReplyToSimple>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: ReplyToSimple?): String? {
            return Gson().toJson(someObjects)
        }
    }
}