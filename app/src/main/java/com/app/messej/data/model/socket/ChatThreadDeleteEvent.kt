package com.app.messej.data.model.socket

import com.app.messej.data.model.entity.PrivateChat
import com.google.gson.annotations.SerializedName

data class ChatThreadDeleteEvent(
    @SerializedName("chatroomId"        ) val roomId        : String,
    @SerializedName("chatType"          ) val chatType      : PrivateChat.ChatType?  = null,
    @SerializedName("sender"            ) val sender        : Int
    ):SocketEventPayload()

