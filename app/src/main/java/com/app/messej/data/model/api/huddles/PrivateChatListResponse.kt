package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.PrivateChat
import com.google.gson.annotations.SerializedName

data class PrivateChatListResponse(
    @SerializedName("chats"                 ) val chats             : List<PrivateChat>  = listOf(),
    @SerializedName("totalRequestCount"     ) val totalRequestCount : Int                = 0,
    @SerializedName("pageState"             ) val pageState         : String?            = null,
    @SerializedName("deletedThreadList"     ) val deletedThreadList : List<String>       = listOf(),
    @SerializedName("adminRoom"             ) val adminRoom         : PrivateChat?       = null
)