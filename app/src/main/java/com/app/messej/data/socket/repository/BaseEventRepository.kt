package com.app.messej.data.socket.repository

import com.app.messej.MainApplication
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.socket.AbstractSocketRepository
import com.app.messej.data.socket.SocketEvent
import com.app.messej.data.socket.SocketEventHandler
import org.json.JSONObject

abstract class BaseEventRepository<T: SocketEvent>(private val socketRepo: AbstractSocketRepository<T>): SocketEventHandler<T> {

    protected val db = FlashatDatabase.getInstance(MainApplication.applicationContext())

    override fun onEvent(event: T, data: JSONObject): Boolean = handleEvent(event, data)

    protected abstract fun handleEvent(event: T, data: JSONObject): Boolean

    val connected: Boolean
        get() = socketRepo.connected

    fun start() = socketRepo.start()

    fun stop() = socketRepo.stop()
}