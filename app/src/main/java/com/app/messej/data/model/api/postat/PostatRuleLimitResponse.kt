package com.app.messej.data.model.api.postat

import com.app.messej.data.model.enums.UserCitizenship
import com.google.gson.annotations.SerializedName

class PostatRuleLimitResponse(
    @SerializedName("user_id") val userId: Int,
    @SerializedName("citizenship") val citizenship: UserCitizenship,
    @SerializedName("role_limit") val roleLimit: Int,
    @SerializedName("balance_limit") val balanceLimit: Int,
    @SerializedName("eligible") val eligible: <PERSON>olean
){
    val showUpgradeButton: <PERSON>olean
        get() = citizenship in listOf(UserCitizenship.VISITOR, UserCitizenship.RESIDENT)
}