package com.app.messej.data.model.api.flash

import com.app.messej.data.model.AbstractFlashVideo
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.ShareTo
import com.google.gson.annotations.SerializedName


data class CreateFlashRequest(
    @SerializedName("flash_temp_id" ) override val id       : String,
    @SerializedName("caption"           ) override val caption      : String?,
    @SerializedName("media_meta"        ) override val mediaMeta    : MediaMeta,

    @SerializedName("user_id"           ) override val userId       : Int,
    @SerializedName("share_to"          ) override val shareTo      : ShareTo,
    @SerializedName("video_url"         ) override val videoUrl     : String,

    @SerializedName("category_id"       ) override val categoryId   : Int?,
    @SerializedName("comment_disabled"  ) override val commentDisabled: Boolean?
): AbstractFlashVideo() {
    companion object {
        fun from(flash: AbstractFlashVideo): CreateFlashRequest {
            return CreateFlashRequest(
                id = flash.id,
                caption = flash.caption,
                mediaMeta = flash.mediaMeta,
                userId = flash.userId,
                shareTo = flash.shareTo,
                videoUrl = flash.videoUrl,
                categoryId = flash.categoryId,
                commentDisabled = flash.commentDisabled
            )
        }
    }

    override val thumbnailUrl: String
        get() = ""

}
