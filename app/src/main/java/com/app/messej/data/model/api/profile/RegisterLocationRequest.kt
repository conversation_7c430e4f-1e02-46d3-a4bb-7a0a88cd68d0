package com.app.messej.data.model.api.profile

import com.google.gson.annotations.SerializedName

data class RegisterLocationRequest(
    @SerializedName("lat") var lat: String? = null,
    @SerializedName("long") var long: String? = null,
    @SerializedName("country") var country: String? = null,
    @SerializedName("location_skipped") var locationSkipped: Boolean? = false,
    @SerializedName("country_code_iso") var countryCode: String? = null,
    var name: String? = ""
) {
    @Transient
    var isAlreadySaved: Boolean = false
}