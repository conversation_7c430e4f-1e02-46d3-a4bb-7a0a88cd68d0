package com.app.messej.data.model.api.profile


import com.google.gson.annotations.SerializedName

data class NotificationsResponse(
    @SerializedName("broadcast_received"    ) var broadcastReceived     : Boolean? = null,
    @SerializedName("dear_added"            ) var dearAdded             : Boolean? = null,
    @SerializedName("fan_added"             ) var fanAdded              : Boolean? = null,
    @SerializedName("huddle_alerts"         ) var huddleAlerts          : Boolean? = null,
    @SerializedName("liker_added"           ) var likerAdded            : Boolean? = null,
    @SerializedName("message_received"      ) var messageReceived       : Boolean? = null,
    @SerializedName("user_id"               ) var userId                : Int? = null
)