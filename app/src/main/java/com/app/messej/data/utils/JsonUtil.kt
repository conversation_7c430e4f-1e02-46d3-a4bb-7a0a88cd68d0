package com.app.messej.data.utils

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

object JsonUtil {

    private val TAG = JsonUtil::class.java.simpleName

    inline fun <reified T> Gson.fromJson(json: String) = this.fromJson<T>(json, object: TypeToken<T>() {}.type)

    inline fun <I, reified T> reparse(input: I): T? {
        val json = Gson().toJson(input)
        return Gson().fromJson<T>(json)
    }
}