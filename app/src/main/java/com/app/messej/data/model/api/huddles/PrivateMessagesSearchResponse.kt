package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.enums.SearchType
import com.google.gson.annotations.SerializedName

data class PrivateMessagesSearchResponse(
    @SerializedName("users") val users: List<PrivateMessagesSuggestionResponse.User> = listOf(),
    @SerializedName("search_type") val searchType: SearchType? = null,
    @SerializedName("next_page") val nextPage: <PERSON><PERSON><PERSON>,
    @SerializedName("current_page") val currentPage: Int,
    @SerializedName("total") val total: Int
)
