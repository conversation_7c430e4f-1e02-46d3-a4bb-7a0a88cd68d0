package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PrivateMessageSuggestionType {
    @SerializedName("two_way_followed") TWO_WAY_FOLLOWED,
    @SerializedName("one_way_followed") ONE_WAY_FOLLOWED,
    @SerializedName("dears") DEARS,
    @SerializedName("fans") FANS,
    @SerializedName("likers") LIKERS,
    @SerializedName("default_suggestion") DEFAULT;


    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}