package com.app.messej.data.model.api.podium

import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.model.enums.LiveFriendsRole
import com.app.messej.data.model.enums.PodiumEntry
import com.app.messej.data.model.enums.PodiumKind
import com.google.gson.annotations.SerializedName

data class PodiumFriend(
    @SerializedName("is_premium") var isPremium: Boolean?=null,
    @SerializedName("name")var name: String?=null,
    @SerializedName("podium_id")var podiumId: String,
    @SerializedName("podium_live_users_count")val podiumLiveUsersCount: Int?=null,
    @SerializedName("podium_name")var podiumName: String?=null,
    @SerializedName("podium_type")var podiumEntry: PodiumEntry?=null,
    @SerializedName("profile_url")var profileUrl: String?=null,
    @SerializedName("role")var role: LiveFriendsRole?=null,
    @SerializedName("thumbnail")var thumbnail: String?=null,
    @SerializedName("user_id")var userId: Int?=null,
    @SerializedName("username")var username: String?=null,
    @SerializedName("podium_kind") var podiumKind: PodiumKind?=null,
    @SerializedName("hide_live_list") val hideLiveUsers: Boolean? = false,
    @SerializedName("required_user_rating") var requiredUserRating :Int?=null,
    @SerializedName("joining_fee") val joiningFee: Int? = null,
    @SerializedName("joining_fee_paid") val joiningFeePaid: Boolean? = false,
    @SerializedName("podium_role") var podiumRole: Podium.PodiumUserRole?=null
    ){

    val isDearOrFan: Boolean
        get() = role == LiveFriendsRole.DEAR || role == LiveFriendsRole.FAN

    val isSuperStar: Boolean
        get() = role == LiveFriendsRole.SUPERSTAR

    val isManagerOrAdmin: Boolean
        get() = podiumRole == Podium.PodiumUserRole.MANAGER || podiumRole == Podium.PodiumUserRole.ADMIN


    fun allowedFor(user: CurrentUser): Boolean {
        val gender = user.profile.gender
        // User is Male and trying to join a Women-only podium
        if (podiumEntry == PodiumEntry.WOMEN_ONLY && gender != Gender.FEMALE) {
            return false
        }
        // User is Female and trying to join a Men-only podium
        else if (podiumEntry == PodiumEntry.MEN_ONLY && gender != Gender.MALE) {
            return false
        }
        return true
    }
}