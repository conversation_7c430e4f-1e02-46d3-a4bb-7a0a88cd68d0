package com.app.messej.data.model.status


import com.google.gson.annotations.SerializedName

data class AppRating(
    @SerializedName("is_satisfied")
    val isSatisfied: Boolean? = false,

    @SerializedName("current_status")
    val status: RatingStatus?=RatingStatus.NEW,

    @SerializedName("screenshot")
    val screenshot: String? = null
){

    enum class RatingStatus{
        @SerializedName("Accepted")
        ACCEPTED,
        @SerializedName("Rejected")
        REJECTED,
        @SerializedName("New")
        NEW
    }

}