package com.app.messej.data.model.api

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import okhttp3.ResponseBody

data class ErrorResponse (val message: String) {
    companion object {
        fun parseError(response: ResponseBody?): ErrorResponse {
            response?: return ErrorResponse("")
            return try {
                val type = object : TypeToken<ErrorResponse>() {}.type
                Gson().fromJson(response.charStream(), type)
            } catch (e: Exception) {
                ErrorResponse("")
            }
        }
    }
}