package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.entity.BusinessPayoutHistory
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.model.entity.BusinessTaskOne
import com.app.messej.data.model.status.BusinessActivityStatus
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class BusinessDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertStatements(models: BusinessStatement)
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertBusinessTaskOne(models: BusinessTaskOne)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BUSINESS_STATEMENTS}")
    abstract fun getStatements(): LiveData<BusinessStatement?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertOperations(models: BusinessOperation)

    @Update (onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun update(chat: BusinessOperation): Int
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BUSINESS_OPERATIONS}")
    abstract fun getOperations(): LiveData<BusinessOperation?>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BUSINESS_TASK_ONE}")
    abstract fun getBusinessTaskOne(): LiveData<BusinessTaskOne?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertPayoutHistory(history: List<BusinessPayoutHistory>)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BUSINESS_OPERATIONS_PAYOUT_HISTORY}")
    abstract fun PayoutHistoryListPagingSource(): PagingSource<Int, BusinessPayoutHistory>


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertActivityStatus(models: BusinessActivityStatus)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_BUSINESS_FLASHAT}")
    abstract fun getActivityStatus(): LiveData<BusinessActivityStatus?>

}