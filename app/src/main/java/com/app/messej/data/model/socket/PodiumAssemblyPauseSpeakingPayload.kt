package com.app.messej.data.model.socket

import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.Duration
import java.time.ZonedDateTime

data class PodiumAssemblyPauseSpeakingPayload(
    @SerializedName("podium_id") val podiumId: String?,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("paused_time") val pausedTime: Double?
) : SocketEventPayload() {
    val parsePausedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(pausedTime?.toLong().toString())

    val speakingTimeRemaining: Long
        get() {
            if(pausedTime == null) return 0
            return ((DateTimeUtils.durationFromNowToFuture(parsePausedTime))?: Duration.ZERO).toMillis() + (60*1000L)
        }
}
