package com.app.messej.data.model.api.subscription


import androidx.room.ColumnInfo
import androidx.room.Entity
import com.google.gson.annotations.SerializedName

@Entity
data class TransactionReceipt(
    @SerializedName("acknowledged") @ColumnInfo("acknowledged") val acknowledged: Boolean? = false,
    @SerializedName("orderId") @ColumnInfo("orderId") val orderId: String? = "",
    @SerializedName("packageName") @ColumnInfo("packageName") val packageName: String? = "",
    @SerializedName("productId") @ColumnInfo("productId") val productId: String? = "",
    @SerializedName("purchaseState") @ColumnInfo("purchaseState") val purchaseState: Int? = 0,
    @SerializedName("purchaseTime") @ColumnInfo("purchaseTime") val purchaseTime: Long? = 0,
    @SerializedName("purchaseToken") @ColumnInfo("purchaseToken") val purchaseToken: String? = "",
)