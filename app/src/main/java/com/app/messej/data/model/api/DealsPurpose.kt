package com.app.messej.data.model.api


import com.google.gson.annotations.SerializedName

data class DealsPurpose(
    @SerializedName("id")
    val id: Int,
    @SerializedName("purpose")
    val purpose: String,
    @SerializedName("status")
    val status: <PERSON><PERSON><PERSON>,
    @SerializedName("time_created")
    val timeCreated: String,
    @SerializedName("time_updated")
    val timeUpdated: String
)