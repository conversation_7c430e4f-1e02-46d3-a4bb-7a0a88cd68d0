package com.app.messej.data.utils

import android.content.res.AssetFileDescriptor
import android.media.MediaPlayer
import androidx.annotation.RawRes
import androidx.fragment.app.Fragment

object BeepUtils {
    fun Fragment.playTone(@RawRes resource: Int):MediaPlayer? {
        val mp = MediaPlayer()
        val afd: AssetFileDescriptor = resources.openRawResourceFd(resource) ?: return null
        mp.setDataSource(afd)
        afd.close()
        mp.prepare()
        mp.start()
        mp.setOnCompletionListener {
            mp.stop()
            mp.release()
        }
        return mp
    }
}