package com.app.messej.data.model.status


import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.model.enums.BusinessColor
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName
@Entity(tableName = EntityDescriptions.TABLE_BUSINESS_FLASHAT)
data class BusinessActivityStatus(
    @Embedded("app_rating_") @SerializedName("app_rating") val appRating: AppRating? = AppRating(),
    @SerializedName("citizenship") val citizenship: UserCitizenship? = null,
    @SerializedName("next_level") val nextLevelCitizenShip: UserCitizenship? = null,
    @Embedded("cus_")@SerializedName("customers") val customers: Customers? = Customers(),
    @SerializedName("eligibility") val eligibility: Boolean? = false,
    @Embedded("account_complete") @SerializedName("account_complete") val accountComplete: AccountComplete? = AccountComplete(),
    @Embedded("elig_days_")@SerializedName("eligibility_in_days") val eligibilityInDays: EligibilityInDays? = EligibilityInDays(),
    @Embedded("flax_balance_")@SerializedName("flax_balance") val flaxBalance: FlaxBalance? = FlaxBalance(),
    @Embedded("generosity") @SerializedName("generosity") val generosity: Generosity? = Generosity(),
    @Embedded("etribe") @SerializedName("etribe") val eTribe: ETribe? = ETribe(),
    @Embedded("huddles_")@SerializedName("huddles") val huddles: Huddles? = Huddles(),
    @Embedded("Pay_exist_")@SerializedName("payout_exist") val payoutExist: PayoutExist? = PayoutExist(),
    @SerializedName("premium")  @PrimaryKey val premium: Boolean? = false,
    @Embedded("prof_comple")@SerializedName("profile_completeness") val profileCompleteness: ProfileCompleteness? = ProfileCompleteness(),
    @Embedded("user_rating_")@SerializedName("user_rating") val userRating: UserRating? = UserRating(),
    @SerializedName("tasks_completed") val tasksCompleted: Boolean? = false,
) {
    val isProfileCompleted: Boolean
        get() = profileCompleteness?.isSatisfied == true

    val isAccountIsEligible: Boolean
        get()=eligibility==true

    fun percentageColor(percentage: Int?): BusinessColor {
        if (percentage == null) {
            BusinessColor.GREY
        }
        return when (percentage) {
            in 0..19 -> BusinessColor.RED
            in 20..49 -> BusinessColor.ORANGE
            in 50..79 -> BusinessColor.YELLOW
            in 80..99 -> BusinessColor.LIGHT_GREEN
            100 -> BusinessColor.GREEN
            else -> {
                BusinessColor.GREY
            }
        }
    }

}