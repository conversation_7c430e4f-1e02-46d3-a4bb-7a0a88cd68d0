package com.app.messej.data.model.notification

import com.app.messej.data.model.MediaMeta
import com.google.gson.annotations.SerializedName

data class PrivateMessageNotification(
    @SerializedName("room_id"           ) var roomId           : String,
    @SerializedName("thumbnail"         ) var thumbnail        : String? = null,
    @SerializedName("user_id"           ) var userId           : Int,
    @SerializedName("unread"            ) var unread           : Int?    = null,
    @SerializedName("name"              ) var name             : String,
    @SerializedName("message_id"        ) var messageId        : String,
    @SerializedName("media"             ) var media            : String? = null,
    @SerializedName("notification_id"   ) var notificationId   : Int    = 0,
    @SerializedName("message"           ) var message          : String = "",
    @SerializedName("sender_id"         ) var senderId         : Int,
    @SerializedName("media_meta"        ) var mediaMeta        : MediaMeta? = null
)
