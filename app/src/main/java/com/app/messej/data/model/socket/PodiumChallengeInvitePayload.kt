package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime
import kotlin.math.max

data class PodiumChallengeInvitePayload(
    @SerializedName("invited_by") val invitedBy: Int,
    @SerializedName("invitee_id") val inviteeId: Int,
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("game_type") val challengeType: ChallengeType,
    @SerializedName("time_invited_participants") val invitedParticipantsTime: String?,
): SocketEventPayload() {
    private val parsedInvitedParticipantsTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(invitedParticipantsTime)

    val conFourParticipantRequestedTimeRemaining: Long
        get() {
            val dur = (DateTimeUtils.durationToNowFromPast(parsedInvitedParticipantsTime))?.seconds?: 30L
            return max(0,30-dur)
        }
}