package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName

data class TransferHistoryResponse(@SerializedName("flax")val flax:Double,
                                   @SerializedName("id") val id:Int,
                                   @SerializedName("purpose") val purpose:String,
                                   @SerializedName("purpose_id")val purposeId:Int,
                                   @SerializedName("sender_id")val senderId:Int,
                                   @SerializedName("status")val status:String,
                                   @SerializedName("time_created") val timeCreated:String,
                                   @SerializedName("time_updated")val timeUpdated:String,
                                   @SerializedName("user_id")val userId:String,
                                   @SerializedName("withdrawn")val withdrawn:Boolean,
                                   @SerializedName("name") val name:String,
                                   @SerializedName("username") val username:String,
                                   @SerializedName("profile_photo")val profilePhoto:String,
                                   @SerializedName("premium") val premium:<PERSON><PERSON><PERSON>)
