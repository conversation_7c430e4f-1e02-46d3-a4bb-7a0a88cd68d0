package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.TypeConverters
import androidx.room.Update
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.entity.PrivateChatRoomInfo
import com.app.messej.data.model.entity.PrivateChatWithRoomInfo
import com.app.messej.data.room.EntityDescriptions

@Dao
@TypeConverters(
    PrivateChatMessage.Converter::class
)
abstract class PrivateChatDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(chat: PrivateChat): Long

    @Update
    abstract suspend fun update(chat: PrivateChat): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_ID} = :id")
    abstract suspend fun delete(id: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(list: List<PrivateChat>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(chat: PrivateChatRoomInfo): Long

    @Update
    abstract suspend fun updatePrivateChatRoomInfo(privateChatRoomInfo: PrivateChatRoomInfo): Int

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_CHAT_TYPE} = :type ORDER BY ${PrivateChat.COLUMN_UPDATED} DESC")
    abstract fun chatPagingSource(type: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE): PagingSource<Int, PrivateChat>
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_CHAT_TYPE} = :type AND ${PrivateChat.COLUMN_PRIVATE_MESSAGE_TAB} IS :tabType ORDER BY ${PrivateChat.COLUMN_UPDATED} DESC")
    abstract fun chatPagingSource(type: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE, tabType: PrivateChat.PrivateMessageTabType?): PagingSource<Int, PrivateChat>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_ID} = :id")
    abstract fun getPrivateChatLiveData(id: String): LiveData<PrivateChat?>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_ID} = :id")
    abstract suspend fun getPrivateChat(id: String): PrivateChat?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_ID} = :id")
    abstract suspend fun getPrivateChatWithRoomInfo(id: String): PrivateChatWithRoomInfo?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_ID} = :id")
    abstract fun getPrivateChatWithRoomInfoLiveData(id: String): LiveData<PrivateChatWithRoomInfo?>

    @Query("UPDATE ${EntityDescriptions.TABLE_PRIVATE_CHATS} SET ${PrivateChat.COLUMN_UPDATED} = :act WHERE ${PrivateChat.COLUMN_ID} = :id")
    abstract suspend fun setPrivateChatActivity(id: String, act: String)

    @Query("SELECT count(*) FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_CHAT_TYPE} = :type AND ${PrivateChat.COLUMN_UNREAD_COUNT} > 0")
    abstract fun countUnreadChats(type: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE): LiveData<Int>

    @Query("SELECT count(*) FROM ${EntityDescriptions.TABLE_PRIVATE_CHATS} WHERE ${PrivateChat.COLUMN_CHAT_TYPE} = :type AND ${PrivateChat.COLUMN_PRIVATE_MESSAGE_TAB} = :tabType AND ${PrivateChat.COLUMN_UNREAD_COUNT} > 0")
    abstract fun countUnreadChatsForTabs(type: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE, tabType: PrivateChat.PrivateMessageTabType = PrivateChat.PrivateMessageTabType.BUDDIES): LiveData<Int>

    @Query("UPDATE ${EntityDescriptions.TABLE_PRIVATE_CHATS} SET ${PrivateChat.COLUMN_UNREAD_COUNT} = :count WHERE ${PrivateChat.COLUMN_ID} = :id")
    abstract suspend fun updateUnreadCount(id: String, count: Int)


}