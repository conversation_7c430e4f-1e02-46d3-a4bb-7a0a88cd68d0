package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.PublicHuddle
import com.google.gson.annotations.SerializedName

data class HuddleMyPostSummaryResponse(
    @SerializedName("current_page"      ) val currentPage       : Int,
    @SerializedName("huddles"           ) val huddles           : List<PublicHuddle> = listOf(),
    @SerializedName("post_count"        ) val postCount         : Int,
    @SerializedName("reply_count"       ) val replyCount        : Int,
    @SerializedName("migration_status"  ) val migrationStatus   : String?               = null,
    @SerializedName("messages"          ) val messages          : String?               = null,
)