package com.app.messej.data.repository

import android.content.Context
import android.net.Uri
import android.util.Log
import com.amazonaws.AmazonClientException
import com.amazonaws.auth.AWSCredentials
import com.amazonaws.auth.BasicSessionCredentials
import com.amazonaws.mobile.config.AWSConfiguration
import com.amazonaws.mobileconnectors.s3.transferutility.TransferListener
import com.amazonaws.mobileconnectors.s3.transferutility.TransferNetworkLossHandler
import com.amazonaws.mobileconnectors.s3.transferutility.TransferObserver
import com.amazonaws.mobileconnectors.s3.transferutility.TransferState
import com.amazonaws.mobileconnectors.s3.transferutility.TransferUtility
import com.amazonaws.regions.Region
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.model.AmazonS3Exception
import com.app.messej.MainApplication
import com.app.messej.data.api.external.ExternalServiceGenerator
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.media.S3UploadMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.github.f4b6a3.uuid.UuidCreator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File


/**
 * Repository to handle media storage
 */
abstract class BaseMediaUploadRepository(private var mContext: Context) {

    protected var accountRepo: AccountRepository = AccountRepository(mContext)

    protected val db = FlashatDatabase.getInstance(mContext)

    suspend fun createTempImageFile(): File = MediaUtils.createTempFile(mContext, MediaType.IMAGE)

    suspend fun createTempAudioFile(): File = MediaUtils.createTempFile(mContext, MediaType.AUDIO)

    suspend fun createTempVideoFile(): File = MediaUtils.createTempFile(mContext, MediaType.VIDEO)

    suspend fun createTempDocumentFile(): File = MediaUtils.createTempFile(mContext, MediaType.DOCUMENT)

    suspend fun getUriForFile(file: File): Uri = MediaUtils.getUriForFile(mContext, file)

    suspend fun deleteFile(path: String) = MediaUtils.deleteFile(path)

    suspend fun deleteFile(file: File) = MediaUtils.deleteFile(file)

    suspend fun storeImageUriToTempFile(uri: Uri): File = MediaUtils.storeContentUriToTempImageFile(mContext, uri)

    suspend fun compressImage(media: File): File = MediaUtils.compressImageToTempFile(mContext, media.absolutePath)

    suspend fun cleanupMediaFiles() = withContext(Dispatchers.IO) {
        try {
            val disconnectedMedia = db.getChatMessageDao().getOrphanedMedia()
            Log.w("MEDCLNP", "cleanupMediaFiles: ${disconnectedMedia.size} disconnected: ${disconnectedMedia.joinToString { it.name }}")
            if (disconnectedMedia.isNotEmpty()) {
                disconnectedMedia.forEach { it.file.delete() }
                db.getChatMessageDao().deleteMedia(*disconnectedMedia.toTypedArray())
            }
        } catch (e: Exception) {

        }
    }

    sealed class MultipartMediaUploadResult {
        data class Progress(val percent: Int) : MultipartMediaUploadResult()
        object Complete : MultipartMediaUploadResult()
        data class Error(val error: java.lang.Exception) : MultipartMediaUploadResult()
    }

    protected fun performMediaUpload(media: S3UploadMedia, url: String) {
        val service = ExternalServiceGenerator.createAwsS3Service()

        val uri = MediaUtils.getUriForFile(mContext, media.file)
        val type = mContext.contentResolver.getType(uri) ?: ""

        val mt = type.toMediaTypeOrNull()
        val requestFile = media.file.asRequestBody(mt)

        // finally, execute the request
        val call = service.uploadMedia(url, requestFile, type)
        val response = call.execute()

        if (response.isSuccessful && response.code() == 200) {
            return
        }
        val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
        throw Exception(error.message)
    }

    protected suspend fun performMultipartMediaUpload(media: S3UploadMedia): Flow<MultipartMediaUploadResult> {
//        val inputStream = c.contentResolver.openInputStream(uri)

//        val meta = ObjectMetadata().apply {
//            contentType = "video/mp4"
//        }

//        val options = UploadOptions.builder().bucket("flashat-video-storage")
//            .objectMetadata(meta).build()
        return callbackFlow {
            try {
                val transferUtility = getTransferUtility()
                val uploadObserver: TransferObserver = transferUtility.upload(media.key, media.file)


                Log.d("ENCODE", "Starting transfer")

                uploadObserver.setTransferListener(object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState) {
                        Log.d("ENCODE", "ID:$id $state")
                        if (TransferState.COMPLETED == state) {
                            // Handle a completed upload.
//                    cont.resumeWith(Result.success(Unit))
                            trySend(MultipartMediaUploadResult.Complete)
                            channel.close()
                        }
                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                        val percentDonef = bytesCurrent.toFloat() / bytesTotal.toFloat() * 100
                        val percentDone = percentDonef.toInt()
//                onProgress.invoke(percentDone)
                        Log.d("ENCODE", "ID:$id current: ${MediaUtils.humanizeBytes(bytesCurrent)} total: ${MediaUtils.humanizeBytes(bytesTotal)} $percentDone% ")
                        trySend(MultipartMediaUploadResult.Progress(percentDone))
                    }

                    override fun onError(id: Int, ex: java.lang.Exception) {
                        // Handle errors
                        Log.d("ENCODE", "ID:$id error: $ex | ${ex.message}")
//                cont.resumeWithException(Exception(ex))
                        trySend(MultipartMediaUploadResult.Error(ex))
                        channel.close()
                    }
                })

                awaitClose {
                    Log.d("ENCODE", "Transfer cancelled. stopping transferUtility")
                    transferUtility.cancel(uploadObserver.id)
                }

            } catch (e: AmazonS3Exception) {
                trySend(MultipartMediaUploadResult.Error(e))
            }
        }
    }

    abstract suspend fun getVideoSecrets(): ResultOf<UploadCredentialsResponse>

    open fun getAwsConfig(): AWSConfiguration = AWSConfiguration(mContext)

    private suspend fun getTransferUtility(): TransferUtility {
        val secrets = when (val result = getVideoSecrets()) {
            is ResultOf.Success -> result.value
            is ResultOf.APIError -> throw Exception(result.error.message)
            is ResultOf.Error -> throw result.exception
        }

        val awsCredentials: AWSCredentials = BasicSessionCredentials(secrets.accessKey, secrets.secretKey, secrets.sessionToken)
        val s3Client = AmazonS3Client(awsCredentials, Region.getRegion(Regions.AP_SOUTHEAST_1))
        val context = MainApplication.applicationContext()
        val awsConfiguration = getAwsConfig()
//        awsConfiguration.optJsonObject("S3TransferUtility").put("CredentialsProvider", awsCredentials)
        TransferNetworkLossHandler.getInstance(context)
        return TransferUtility.builder().context(context).awsConfiguration(awsConfiguration).s3Client(s3Client).build()
    }

    sealed class VideoDownloadResult {
        data class Progress(val percent: Int) : VideoDownloadResult()
        data class Complete(val file: File) : VideoDownloadResult()
        data class Error(val error: java.lang.Exception) : VideoDownloadResult()
    }

    protected suspend fun performDownload(s3Key: String, destFile: File): Flow<VideoDownloadResult> {
        val transferUtility = getTransferUtility()

        return callbackFlow {

            var downloadObserver: TransferObserver? = null
            try {
                downloadObserver = transferUtility.download(s3Key, destFile)
                Log.d("VDOWNLOAD", "Starting download")

                downloadObserver.setTransferListener(object : TransferListener {
                    override fun onStateChanged(id: Int, state: TransferState) {
                        Log.d("VDOWNLOAD", "ID:$id $state")
                        if (TransferState.COMPLETED == state) {
                            // Handle a completed upload.
//                    cont.resumeWith(Result.success(Unit))
                            trySend(VideoDownloadResult.Complete(destFile))
                            channel.close()
                        }
                    }

                    override fun onProgressChanged(id: Int, bytesCurrent: Long, bytesTotal: Long) {
                        val percentDonef = bytesCurrent.toFloat() / bytesTotal.toFloat() * 100
                        val percentDone = percentDonef.toInt()
//                onProgress.invoke(percentDone)
                        Log.d("VDOWNLOAD", "ID:$id current: ${MediaUtils.humanizeBytes(bytesCurrent)} total: ${MediaUtils.humanizeBytes(bytesTotal)} $percentDone% ")
                        trySend(VideoDownloadResult.Progress(percentDone))
                    }

                    override fun onError(id: Int, ex: java.lang.Exception) {
                        // Handle errors
                        Log.d("VDOWNLOAD", "ID:$id error: $ex | ${ex.message}")
//                cont.resumeWithException(Exception(ex))
                        trySend(VideoDownloadResult.Error(ex))
                        channel.close()
                    }
                })
            } catch (ex: AmazonClientException) {
                trySend(VideoDownloadResult.Error(ex))
            } catch (ex: Exception) {
                trySend(VideoDownloadResult.Error(ex))
            } catch (e: AmazonS3Exception) {
                trySend(VideoDownloadResult.Error(e))
            }

            awaitClose {
                Log.d("ENCODE", "Transfer cancelled. stopping transferUtility")
                downloadObserver?.let { obs ->
                    if (obs.state != TransferState.COMPLETED) {
                        MediaUtils.deleteFile(destFile)
                        transferUtility.cancel(obs.id)
                    }
                }
            }

        }
    }

    open suspend fun downloadMultipartMedia(meta: MediaMeta): Flow<VideoDownloadResult> {

        val destFile = if (meta.mediaType == MediaType.DOCUMENT) MediaUtils.createBlankFile(
            mContext, meta.mediaName ?: (UuidCreator.getTimeBased().toString() + MediaUtils.getFileExtensionFromName(meta.documentDisplayName)), MediaType.DOCUMENT
        )
        else MediaUtils.createBlankFile(mContext, meta.mediaName ?: (UuidCreator.getTimeBased().toString() + ".mp4"), MediaType.VIDEO)
        return performDownload(meta.s3Key, destFile)
    }
}