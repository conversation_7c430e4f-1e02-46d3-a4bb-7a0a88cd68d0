package com.app.messej.data.model

import androidx.room.TypeConverter
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class MentionedUser(
    @SerializedName("userId"      ) override val id          : Int,
    @SerializedName("displayName" ) override val name        : String,
    @SerializedName("userName"    ) override val username    : String,
    @SerializedName("thumbnail"   ) override val thumbnail   : String?  = null,
    @SerializedName("isDeleted"   )          val isDeleted   : Boolean = false,
    @SerializedName("nickName"    )          val nickName    : String?  = null
): AbstractUser() {
    class Converter {
        @TypeConverter
        fun decode(data: String?): List<MentionedUser>? {
            data?: return null
            val type: Type = object : TypeToken<List<MentionedUser>>() {}.type
            return Gson().fromJson(data, type)
        }
        @TypeConverter
        fun encode(someObjects: List<MentionedUser>?): String = Gson().toJson(someObjects)
    }

    class SingleConverter {
        @TypeConverter
        fun decode(data: String?): MentionedUser? {
            data?: return null
                val type: Type = object : TypeToken<MentionedUser?>() {}.type
                return Gson().fromJson<MentionedUser>(data, type)
        }
        @TypeConverter
        fun encode(someObject: MentionedUser?): String? {
            return Gson().toJson(someObject)
        }
    }

    override val membership: UserType
        get() = UserType.PREMIUM

    override val verified: Boolean
        get() = false

    override val citizenship: UserCitizenship
        get() = UserCitizenship.default()

    val userNickNameOrName: String
        get() {
            nickName?.let {
                if(it.isNotEmpty()) {
                    return it
                }
            }
            return name
        }

}
