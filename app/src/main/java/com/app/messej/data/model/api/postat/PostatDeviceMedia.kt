package com.app.messej.data.model.api.postat

import android.net.Uri
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import androidx.media3.common.MediaItem
import com.app.messej.data.model.AbstractUriMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.DateTimeUtils
import java.io.File

data class PostatDeviceMedia(
    val uri: Uri,
//    val name: String,
//    val size: Long,
    override val mimeType: String,
    val videoDurationMillis: Long? = null,
    val isCaptured: Boolean = false
): AbstractUriMedia() {

    val uriString: String
        get() = uri.toString()

    override val thumbnail: String?
        get() = if (mediaType == MediaType.IMAGE) uriString else null

    @get:Bindable
    var mediaSelected: Boolean = false
        set(value) {
            field = value
            if (!value) {
                selectionOrder = 0
            }
            notifyPropertyChanged(BR.mediaSelected)
        }

    val videoDuration: String?
        get() = videoDurationMillis?.let {
            DateTimeUtils.formatSeconds(it/1000)
        }

    val mediaItem: MediaItem
        get() {
            val inputMediaItem = MediaItem.Builder()
            inputMediaItem.setUri(uri)
            return inputMediaItem.build()
        }

    var processedFile: File? = null

    @get:Bindable
    var selectionOrder: Int = 0
        set(value) {
            field = value
            notifyPropertyChanged(BR.selectionOrder)
        }
}
