package com.app.messej.data.room.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.app.messej.data.model.entity.YallaGuysChallenge
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class YallaGuysDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(challenge: YallaGuysChallenge)

    @Update
    abstract suspend fun update(challenge: YallaGuysChallenge)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(challengeList: List<YallaGuysChallenge>)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_YALLA_LIVE}")
    abstract suspend fun deleteAll()

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_YALLA_LIVE} WHERE ${YallaGuysChallenge.COLUMN_PODIUM_ID} = :podiumId ORDER BY ${YallaGuysChallenge.COLUMN_QUEUED} DESC, ${YallaGuysChallenge.COLUMN_TIME_CREATED} ASC")
    abstract fun yallaGuysPagingSource(podiumId: String): PagingSource<Int, YallaGuysChallenge>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_YALLA_LIVE} WHERE ${YallaGuysChallenge.COLUMN_ID} = :id")
    abstract fun getChallenge(id: String): YallaGuysChallenge?

    @Query("DELETE FROM ${EntityDescriptions.TABLE_YALLA_LIVE} WHERE ${YallaGuysChallenge.COLUMN_ID} = :id")
    abstract fun delete(id: String)

}