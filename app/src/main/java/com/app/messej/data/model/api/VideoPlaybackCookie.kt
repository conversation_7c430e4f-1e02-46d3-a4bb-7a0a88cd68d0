package com.app.messej.data.model.api

import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

data class VideoPlaybackCookie(
    @SerializedName("CloudFront-Policy" ) val cloudFrontPolicy : String,
    @SerializedName("CloudFront-Signature" ) val cloudFrontSignature : String,
    @SerializedName("CloudFront-Key-Pair-Id" ) val cloudFrontKeyPairId : String,
    @SerializedName("expire_at") val expiry: String
) {
    val cookieValue: String
        get() = "CloudFront-Policy=$cloudFrontPolicy;CloudFront-Signature=$cloudFrontSignature;CloudFront-Key-Pair-Id=$cloudFrontKeyPairId;"

    val expiryDate: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(expiry)

    val isExpired: Boolean
        get() = ZonedDateTime.now().isAfter(expiryDate)
}