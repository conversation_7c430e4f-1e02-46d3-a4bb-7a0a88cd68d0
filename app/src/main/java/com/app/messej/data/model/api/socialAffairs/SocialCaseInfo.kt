package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.socialAffairs.CommitteeMembersResponse.CommitteeMemberUserDetail
import com.app.messej.data.model.api.socialAffairs.SocialAffairUser.Companion.dummySocialUser
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.SocialCaseStatus
import com.app.messej.data.model.enums.SocialRequestType
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.utils.MediaUtils
import com.google.gson.annotations.SerializedName

data class SocialCaseInfoResponse(
    @SerializedName(value = "data") val data : SocialCaseInfo?,
    @SerializedName(value = "committee_members") val committeeMembers : List<CommitteeMemberUserDetail>?,
    @SerializedName(value = "vote_count") val voteCount : Int?,
    @SerializedName(value = "questions_count") val questionsCount : Int?,
    @SerializedName(value = "user_status") val userStatus : SocialUserStatus?
)

data class SocialCaseInfo(
    @SerializedName(value = "id") override val id : Int?,
    @SerializedName(value = "target_amount") override val targetAmount: Double?,
    @SerializedName(value = "received_amount") override val receivedAmount: Double?,
    @SerializedName(value = "case_title") override val caseTitle: String?,
    @SerializedName(value = "case_description") override val caseDescription: String?,
    @SerializedName(value = "support_votes") override val supportVotes: Int?,
    @SerializedName(value = "opposed_votes") override val opposedVotes: Int?,
    @SerializedName(value = "net_votes") override val netVotes: Int?,
    @SerializedName(value = "proof_files") override val proofFiles: List<String>?,
    @SerializedName(value = "user_details") override val userDetail: SocialAffairUser?,
    @SerializedName(value = "status") override val status: SocialCaseStatus?,
    @SerializedName(value = "time_created") override val timeCreated: String?,
    @SerializedName(value = "is_voted") override val isVoted: Boolean?,

    @SerializedName(value = "request_type") val requestType: SocialRequestType?,
    @SerializedName(value = "vote_status") val voteStatus: SocialVoteAction?,
    @SerializedName(value = "amount_requested") val amountRequested: Double?,
    @SerializedName(value = "requested_user") val requestedUser: Int?
): AbstractSocialCase() {

    data class SocialProofFile(val url: String) {
        val fileType: MediaType?
        get() = MediaUtils.getMediaTypeFromFileName(fileName = url)
    }

    val socialProofFile: List<SocialProofFile>?
        get() = proofFiles?.map {
            SocialProofFile(url = it)
        }

    companion object {
        val dummySocialCaseInfo = SocialCaseInfo(
            id = 1,
            targetAmount = 100.00,
            receivedAmount = 50.00,
            caseTitle = "Test Case",
            caseDescription = "Test Description",
            supportVotes = 10,
            opposedVotes = 6,
            netVotes = -4,
            status = SocialCaseStatus.NEW,
            proofFiles = listOf(
                "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Google_2015_logo.svg/640px-Google_2015_logo.svg.png",
                "https://www.techmonitor.ai/wp-content/uploads/sites/29/2017/02/shutterstock_552493561.webp",
                "https://searchengineland.com/wp-content/seloads/2015/12/google-amp-fast-speed-travel-ss-1920.jpg",
                "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQcwNFKmmUPCCUdWX5kjzHiqt9iwvDMMJBd2w&s",
                "https://i.pinimg.com/736x/d6/84/7d/d6847d6ec792c38e37cecdceb2d11f72.jpg"
            ),
            userDetail = dummySocialUser,
            timeCreated = "2025-07-15T05:01:54",
            amountRequested = 90.00,
            requestedUser = 101,
            requestType = null,
            isVoted = false,
            voteStatus = SocialVoteAction.Oppose
        )
        val dummySocialProofFile = listOf(
            SocialProofFile("https://upload.wikimedia.org/wikipedia/commons/thumb/2/2f/Google_2015_logo.svg/640px-Google_2015_logo.svg.png"),
            SocialProofFile("https://searchengineland.com/wp-content/seloads/2015/12/google-amp-fast-speed-travel-ss-1920.jpg"),
            SocialProofFile("https://i.pinimg.com/736x/d6/84/7d/d6847d6ec792c38e37cecdceb2d11f72.jpg"),
        )
    }

    val isPersonalRequest: Boolean
        get() = requestType == SocialRequestType.Personal

    val isUpgradeRequest: Boolean
        get() = requestType == SocialRequestType.Upgrade

    //Hiding committee members in voting section
    val isCommitteeMembersListVisible: Boolean
        get() = isDonateView && isPersonalRequest

    //Hiding Voters in voting section
    fun isVotersCountVisible(currentUser: CurrentUser, committeeMembersIdList: List<Int?>?): Boolean =
        isDonateView && isPersonalRequest && (currentUser.citizenship.isPresident || committeeMembersIdList?.contains(currentUser.id) == true) /* Check when MSA available. with OR condition*/

    //Hiding Questions in voting section
    val isQuestionsVisible: Boolean
        get() = isDonateView && isPersonalRequest

    fun isCurrentUser(currentUserId: Int?) : Boolean = currentUserId == requestedUser

    fun isVotingButtonsVisible(currentUserId: Int): Boolean =
        status == SocialCaseStatus.NEW && isVoted == false && !isCurrentUser(currentUserId)

    //Used for archive, un archive and delete current user case
    fun canDoSelfActionsOnCase(currentUserId: Int): Boolean =
        isCurrentUser(currentUserId) && when(status) {
            SocialCaseStatus.NEW, SocialCaseStatus.ARCHIVED -> true
            else -> false
        }

    val canArchive: Boolean
        get() = isNewCase

    val isArchived: Boolean
        get() = status == SocialCaseStatus.ARCHIVED

    val canDelete: Boolean
        get() = isNewCase || isArchived

    fun canApproveCase(userCitizenship: UserCitizenship): Boolean =
        isPendingCase && userCitizenship.isPresident

    fun isPersonalCaseDonateButtonVisible(currentUserId: Int): Boolean =
        isStatusActive && isPersonalRequest && !isCurrentUser(currentUserId)

    fun isUpgradeCaseDonateButtonVisible(currentUserId: Int): Boolean =
        isStatusActive && !isCurrentUser(currentUserId) && isUpgradeRequest

    val isVoteActionStatusVisible: Boolean
        get() = !isDonateView

    fun isCaseStatusVisible(currentUserId: Int?): Boolean =
        isCurrentUser(currentUserId)

    fun isCaseStatusVisibleToPresident(user: CurrentUser?): Boolean =
        user?.citizenship?.isPresident == true && (isPendingCase || isClosedCase)

    val isCaseActionButtonVisibleInActiveCaseList: Boolean
        get() = when(status) {
            SocialCaseStatus.CLOSED, SocialCaseStatus.PENDING_APPROVAL, SocialCaseStatus.DECLINED -> false
            else -> true
        }

    val needToBeReplaceCaseTitleWithStatus: Boolean
        get() = when(status) {
            SocialCaseStatus.CLOSED, SocialCaseStatus.PENDING_APPROVAL -> true
            else -> false
        }
}
