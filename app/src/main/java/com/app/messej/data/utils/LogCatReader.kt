package com.app.messej.data.utils

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material3.FilledTonalIconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.messej.MainApplication
import com.app.messej.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.io.File
import java.io.IOException
import java.time.LocalDateTime
import java.time.LocalTime


object LogCatReader {

    private var logcatProcess: Process? = null
    private var lastLogFile: File? = null
        set(value) {
            field = value
            _loggingFlow.value = value!=null
            Log.w("LCR", "lastLogFile: emit $value")
        }

    private val _loggingFlow = MutableStateFlow(false)
    val loggingFlow: StateFlow<Boolean> = _loggingFlow

    private val _loggingEnabledFlow = MutableStateFlow(false)
    val loggingEnabledFlow: StateFlow<Boolean> = _loggingEnabledFlow

    fun enableLogging(enable: Boolean) {
        if (_loggingEnabledFlow.value && enable) return
        if (!enable && isLogging) {
            val context = MainApplication.applicationContext()
            stopLogging(context)
        }
        _loggingEnabledFlow.value = enable
    }

    val isLogging: Boolean
        get() = lastLogFile!=null

    val enableActionString: String
        get() = "Enable Log Capture (Debug Only)"

    fun resultMessage(fileName: String) = "Log file saved to \"Documents/Flashat Logs/$fileName\""

    fun startLogging(c: Context) {
        stopLogging(c)
        CoroutineScope(Dispatchers.IO).launch {
            try {
                //clear logCat so that old logs are not caught
                ProcessBuilder("logcat", "-c").start().waitFor()
                val fileName = "logdump"
                val storageDir: File = c.cacheDir
                val tempDir = File(storageDir, "/logs")
                if (!tempDir.exists()) {
                    tempDir.mkdirs()
                }
                val logFile = File.createTempFile(fileName, ".log", tempDir)

                lastLogFile = logFile
                val pid = android.os.Process.myPid().toString()
                val packageName = c.packageName
                val processBuilder = ProcessBuilder("logcat", "--pid=$pid")
                logcatProcess = processBuilder.start()

                val excludedTags = listOf("SurfaceView")
                logcatProcess?.inputStream?.bufferedReader()?.use { reader ->
                    logFile.outputStream().bufferedWriter().use { writer ->
                        reader.forEachLine { line ->
                            val tagExcluded = excludedTags.any { tag -> line.contains(tag) }
                            if (!tagExcluded) {
                                writer.write(line)
                                writer.newLine()
                                writer.flush() // Ensure logs are written immediately
                            }
                        }
                    }
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    fun stopLogging(c: Context, save: ((Pair<Intent,String>) -> Unit)? = null) {
        // ensure logs are dumped before saving file
        if (lastLogFile==null) return
        CoroutineScope(Dispatchers.IO).launch {
            delay(2000)
            if (logcatProcess != null) {
                logcatProcess!!.destroy()
            }
            if (save!=null) {
                val filename = "log-${DateTimeUtils.format(LocalDateTime.now(), DateTimeUtils.FORMAT_DATE_TIME_FILE_TS_READABLE)}"
                val file = lastLogFile?.let { MediaUtils.saveFileToDocuments(c, it, "Flashat Logs", filename) }
                lastLogFile = null
                val intent = Pair(
                    Intent(Intent.ACTION_VIEW).apply {
                        setDataAndType(file, "text/plain")
                        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    }, filename
                )
                save.invoke(intent)
            }
        }

    }
}

@Composable
fun CurrentTimeDisplay(onStop: () -> Unit) {
    fun getCurrentTime(): String {
        return DateTimeUtils.format(LocalTime.now(), DateTimeUtils.FORMAT_READABLE_TIME_SECONDS_24HRS)
    }

    val timeState = remember { mutableStateOf(getCurrentTime()) }

    val running = LogCatReader.loggingFlow.collectAsState(initial = false)

    LaunchedEffect(Unit) {
        while (true) {
            delay(1000) // Update every second
            timeState.value = getCurrentTime()
        }
    }

    val density = LocalDensity.current
    val elevation = with(density) { 8.dp.toPx() }
    Row(
        modifier = Modifier
            .padding(8.dp)
            .wrapContentSize()
            .graphicsLayer(
                shadowElevation = elevation,
                shape = RoundedCornerShape(32.dp),
                ambientShadowColor = if (running.value) colorResource(R.color.colorError) else colorResource(R.color.textColorSecondary), // Outer glow color
                spotShadowColor = if (running.value) colorResource(R.color.colorError) else colorResource(R.color.textColorSecondary) // Adjust for effect
            )
            .background(colorResource(R.color.colorSurface), RoundedCornerShape(32.dp))
            .padding(4.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        FilledTonalIconButton(
            onClick = {
                onStop.invoke()
            }, modifier = Modifier.size(36.dp), // Adjust size as needed,
            colors = IconButtonDefaults.filledTonalIconButtonColors(
                containerColor = if (running.value) colorResource(R.color.colorError).copy(alpha = 0.1f)
                else colorResource(R.color.colorPrimary).copy(alpha = 0.1f)
            ) // Set background color
        ) {
            if (running.value) {
                Icon(
                    painter = painterResource(R.drawable.ic_media_stop), // Replace with your desired icon
                    contentDescription = "Favorite", tint = colorResource(R.color.colorError) // Change icon color if needed
                )
            } else {
                Icon(
                    painter = painterResource(R.drawable.ic_media_play), // Replace with your desired icon
                    contentDescription = "Favorite", tint = colorResource(R.color.colorPrimary) // Change icon color if needed
                )
            }
        }
        Column(
            modifier = Modifier.wrapContentHeight().width(70.dp), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = if (running.value) "RECORDING" else "READY",
                style = TextStyle(
                    color = colorResource(R.color.textColorSecondary),
                    fontSize = 10.sp,
                ),
            )
            if(running.value) {
                Text(
                    text = timeState.value,
                    style = TextStyle(
                        color = colorResource(R.color.colorError),
                        fontSize = 14.sp,
                    ),
                )
            }
        }
        FilledTonalIconButton(
            onClick = {
                LogCatReader.enableLogging(false)
            }, modifier = Modifier.size(36.dp), // Adjust size as needed,
            colors = IconButtonDefaults.filledTonalIconButtonColors(
                containerColor = colorResource(R.color.textColorSecondary).copy(alpha = 0.1f)
            ) // Set background color
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_trash_outline), // Replace with your desired icon
                contentDescription = "Favorite", tint = colorResource(R.color.textColorSecondary) // Change icon color if needed
            )
        }
    }

}

@Preview
@Composable
fun CurrentTimeDisplayPreview() {
    CurrentTimeDisplay() {}
}