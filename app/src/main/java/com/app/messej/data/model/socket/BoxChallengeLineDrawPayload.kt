package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.ConFourGameStatus
import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoardModel
import com.app.messej.ui.home.publictab.podiums.challenges.boxChallenge.BoxChallengeBoardModel.LineDirection
import com.google.gson.annotations.SerializedName

data class BoxChallengeLineDrawPayload(
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("podium_id") val podiumId: String?,
    @SerializedName("user_id") val userId: Int?,
    @SerializedName("opponent_user_id") val opponentUserId: Int?,

    @SerializedName("participant_token_number") val participantTokenNumber: Int?,
    @SerializedName("row") val row: Int,
    @SerializedName("column") val column: Int,
    @SerializedName("direction") val lineDirection: LineDirection,
    @SerializedName("status") val status: ConFourGameStatus?,


    ) : SocketEventPayload() {
    companion object {

        fun from(
            challenge: PodiumChallenge,
            line: BoxChallengeBoardModel.Line,
            player: ChallengePlayer,
            myUserId: Int,
            opponentUserId: Int,
            status: ConFourGameStatus?
        ): BoxChallengeLineDrawPayload {

            return BoxChallengeLineDrawPayload(
                challengeId = challenge.challengeId,
                podiumId = challenge.podiumId, userId = myUserId, opponentUserId = opponentUserId,

                row = line.row, column = line.col, lineDirection = line.direction,

                participantTokenNumber = player.tokenNumber, status = if (status == ConFourGameStatus.NONE) null else status
            )
        }
    }

}
