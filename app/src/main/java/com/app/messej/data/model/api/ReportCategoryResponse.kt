package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName

data class ReportCategoryResponse(
    @SerializedName("report_categories" ) var categories : ArrayList<ReportCategory> = arrayListOf()
) {
    data class ReportCategory (
        @SerializedName("category"      ) var category     : String,
        @SerializedName("category_id"   ) var categoryId   : Int,
        @SerializedName("category_text" ) var categoryText : String,
        @SerializedName("category_type" ) var categoryType : String
    )
}