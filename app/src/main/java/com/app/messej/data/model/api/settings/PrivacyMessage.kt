package com.app.messej.data.model.api.settings


import com.google.gson.annotations.SerializedName
data class PrivacyMessage(
    @SerializedName("disablePrivateChat")
    val disablePrivateChat: <PERSON><PERSON><PERSON>,
    @SerializedName("whoCanStartPrivateChat")
    val whoCanStartPrivateChat: PrivacyMessage.WhoCanStartPrivateChat
){
    data class WhoCanStartPrivateChat(
        @SerializedName("ANYONE")
        val anyOne: <PERSON><PERSON>an,
        @SerializedName("NO_ONE")
        val noOne: <PERSON>olean,
        @SerializedName("ONLY_CITIZENS")
        val onlyCitizens: <PERSON><PERSON><PERSON>,
        @SerializedName("ONLY_DEARS")
        val onlyDears: Boolean
    )
}