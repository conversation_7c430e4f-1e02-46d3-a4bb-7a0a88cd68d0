package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class ParticipantsSearchType {
    @SerializedName("") NONE,
    @SerializedName("Admin-invited") ADMIN_INVITED,
    @SerializedName("Admin") ADMIN,
    @SerializedName("Manager") MANAGER,
    @SerializedName("Blocked") BLOCKED,
    @SerializedName("dear") DEAR,
    @SerializedName("fan") FAN,
    @SerializedName("liker") LIKER;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}