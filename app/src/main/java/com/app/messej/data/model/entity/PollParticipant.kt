package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(tableName = EntityDescriptions.TABLE_POLLS_PARTICIPANT)
data class PollParticipant(

    @SerializedName("deletedUser")  @ColumnInfo(name = "deletedUser") val deletedUser: Boolean,
    @SerializedName("membership")  @ColumnInfo(name = "membership")val membership: <PERSON>olean,
    @SerializedName("name")  @ColumnInfo(name = "name") var name: String,
    @SerializedName("thumbnail")  @ColumnInfo(name = "thumbnail")val thumbnail: String?=null,
    @SerializedName("username") @ColumnInfo(name = "username") val username: String,
    @SerializedName("user_id") @ColumnInfo(name = "userId" ,defaultValue = "0") val userId:Int,
    @SerializedName("verified") @ColumnInfo(name = "verified") val verified: Boolean
){
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = POLL_ID)
    var pollID: Int = 1

    companion object {
        const val POLL_ID = "poll_id"
    }
}