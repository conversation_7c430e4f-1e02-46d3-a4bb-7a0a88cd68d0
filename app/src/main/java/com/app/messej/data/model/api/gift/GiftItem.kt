package com.app.messej.data.model.api.gift


import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.model.socket.AbstractGiftItem
import com.google.gson.annotations.SerializedName

data class GiftItem(
    @SerializedName("id")                           override val id                         : Int,
    @SerializedName("gift_identifier")              override val giftIdentifier             : String?,
    @SerializedName("gift_url")                     override val animationUrl               : String?,
    @SerializedName("gift_static_url")              override val thumbnail                  : String?,
    @SerializedName("coins")                        override val coins                      : Int?,
    @SerializedName("flix")                         override val flix                       :Int?,
    @SerializedName("category_name")                override val categoryName               : String?,
    @SerializedName("gift_name")                    override val giftName                   : String?,
    @SerializedName("gift_description")             override val description                : String?,
    @SerializedName("gift_type")                    override val giftType                   : GiftType?,
    @SerializedName("gift_animation_url" )          override val giftAnimationUrl           : String?,
    @SerializedName("gift_animation_url_android" )  override val giftAnimationUrlAndroid    : String?,
    @SerializedName("category_id"   )               override val categoryId: Int,

    @SerializedName("count") val count: String? = null,
//    @SerializedName("language") val language: String? = null,

    @SerializedName("translations")                 override val nameTranslations           : Translations?,
    @SerializedName("description_translations")     override val descTranslations           : Translations?,
    @SerializedName("special_occasion_date")        override val specialOccasionDate        : String?=null,
    @SerializedName("manager_id")                   override val managerId: Int? = null,
    @SerializedName("manager_received_coins")       override val managerReceivedCoins: Double? = null,
): AbstractGiftItem(){

    val isFlix: Boolean
        get() = categoryName == "FLiX" && giftType == GiftType.BANK

    val isCoin: Boolean
    get() = categoryName== "COiNS" && giftType == GiftType.BANK
}