package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.ActivityMeta
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.Remover
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.HuddleChatMessage.Companion.COLUMN_HUDDLE_ID
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.HuddlePostType
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.socket.HuddleChatMessagePayload
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.ui.utils.DataFormatHelper
import com.app.messej.ui.utils.MentionTokenizer
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

@Entity(
    tableName = EntityDescriptions.TABLE_HUDDLE_MESSAGES,
    indices = [
        Index(COLUMN_HUDDLE_ID, unique = false)
    ]
)
@TypeConverters(
    SenderDetails.Converter::class,
    ActivityMeta.Converter::class,
    ReplyTo.Converter::class,
    MediaMeta.Converter::class,
    Remover.Converter::class,
    MentionedUser.Converter::class,
)
data class HuddleChatMessage(

    @SerializedName("message_id"            ) @ColumnInfo(name = COLUMN_MESSAGE_ID          ) @PrimaryKey(autoGenerate = false) override val messageId     : String,
    @SerializedName("room_id"               ) @ColumnInfo(name = "room_id"                  ) override var roomId        : String,
    @SerializedName("message"               ) @ColumnInfo(name = "message"                  ) override var rawMessage       : String?,

    @SerializedName("created"               ) @ColumnInfo(name = COLUMN_MESSAGE_CREATED     ) override val createdTime   : String,
    @SerializedName("delivered"             ) @ColumnInfo(name = COLUMN_MESSAGE_DELIVERED   ) override val deliveredTime : String? = null,
    @SerializedName("sent"                  ) @ColumnInfo(name = COLUMN_MESSAGE_SENT        ) override val sentTime      : String? = null,

    @SerializedName("remover"               ) @ColumnInfo(name = "remover"                  )          val remover       : Remover? = null,

    @SerializedName("deleted"               ) @ColumnInfo(name = COLUMN_DELETED             ) override var deleted       : Boolean = false,
    @SerializedName("is_edited"             ) @ColumnInfo(name = "edited"                   )          val edited       : Boolean? = false,

    @SerializedName("is_activity"           ) @ColumnInfo(name = COLUMN_IS_ACTIVITY         ) override val isActivity    : Boolean = false,
    @SerializedName("activity_meta"         ) @ColumnInfo(name = "activity_meta"            ) override val activityMeta  : ActivityMeta? = null,
    @SerializedName("media"                 ) @ColumnInfo(name = COLUMN_MEDIA               ) override var media         : String? = null,
    @SerializedName("media_meta"            ) @ColumnInfo(name = "media_meta"               ) override var mediaMeta     : MediaMeta? = null,
    @SerializedName("message_type"          ) @ColumnInfo(name = "message_type"             ) override var internalMessageType  : MessageType? = null,

    @SerializedName("read"                  ) @ColumnInfo(name = "read"                     ) override val read          : String? = null,
    @SerializedName("receiver"              ) @ColumnInfo(name = "receiver"                 ) override val receiver      : Int? = null,
    @SerializedName("reply_to"              ) @ColumnInfo(name = COLUMN_REPLY_TO            ) override var replyTo       : ReplyTo? = null,

    @SerializedName("sender"                ) @ColumnInfo(name = COLUMN_SENDER              ) override val sender        : Int,
    @SerializedName("sender_broadcastType"  ) @ColumnInfo(name = "sender_relation"          )          val senderRelation: FollowerType? = null,
    @SerializedName("sender_details"        ) @ColumnInfo(name = "sender_details"           )          var senderDetails : SenderDetails?,

    @SerializedName("liked"                 ) @ColumnInfo(name = "liked"                    ) override val liked         : Boolean = false,
    @SerializedName("reported"              ) @ColumnInfo(name = COLUMN_REPORTED            ) override val reported      : Boolean = false,

    @SerializedName("huddle_id"             ) @ColumnInfo(name = COLUMN_HUDDLE_ID           )          var huddleId      : String,
    @SerializedName("huddle_type"           ) @ColumnInfo(name = COLUMN_HUDDLE_TYPE         )          var huddleType    : HuddleType = HuddleType.PRIVATE,
    @SerializedName("total_likes"           ) @ColumnInfo(name = "total_likes"              )          val totalLikes    : Int = 0,
    @SerializedName("total_gifts"           ) @ColumnInfo(name = "total_gifts",defaultValue = "0"   )          val totalGifts    : Int = 0,
    @SerializedName("total_comments"        ) @ColumnInfo(name = "total_comments", defaultValue = "0")          var totalComments : Int = 0,
    @SerializedName("star_type"             ) @ColumnInfo(name = "star_type"                )          val starType      : String? = null,

    @SerializedName("pinned"                ) @ColumnInfo(name = COLUMN_PINNED              )          val pinned        : Boolean? = null,

    @SerializedName("has_mention"           ) @ColumnInfo(name = "has_mention", defaultValue = "0")    var hasMention    : Boolean  = false,
    @SerializedName("color"                 ) @ColumnInfo(name = "color")    override val chatTextColor    : ChatTextColor?  = ChatTextColor.DEFAULT,
    @SerializedName("mentioned_users"       ) @ColumnInfo(name = "mentioned_users"          )          val mentionedUsers: List<MentionedUser>? = null,
    @SerializedName("post_type"             ) @ColumnInfo(name = "post_type"                )          val postType      : HuddlePostType? = HuddlePostType.POST,
    @SerializedName("huddle_name"           ) @ColumnInfo(name = "huddle_name"              )          val huddleName    : String? = null,
    @SerializedName("forward_id"            ) @ColumnInfo(name = "forward_id")                override  var forwardId   : String?  = null,

    @ColumnInfo(name = COLUMN_REPLY_MESSAGE_ID) var replyMessageId : String? = null,
    @ColumnInfo(name = COLUMN_REPLY_MESSAGE_SENDER) var replyMessageSenderId : Int? = null
): AbstractChatMessage() {

    init {
        prefixHuddleId()
    }

    val socketpayload: HuddleChatMessagePayload
        get() {
            val payload = HuddleChatMessagePayload(messageId, huddleIdInt, rawMessage, hasMention, chatTextColor,forwardId)
            mediaMeta?.let { med ->
                payload.setMedia(med)
            }
            replyTo?.let {
                payload.replyTo = it.messageId
                payload.replyId = it.replyId
            }
            return payload
        }

    companion object {
        const val HUDDLE_ID_PREFIX = "HUDDLE#"

        const val COLUMN_MESSAGE_ID = "message_id"
        const val COLUMN_HUDDLE_ID = "huddle_id"
        const val COLUMN_MESSAGE_CREATED = "created"
        const val COLUMN_MESSAGE_DELIVERED = "delivered"
        const val COLUMN_MESSAGE_SENT = "sent"
        const val COLUMN_DELETED = "deleted"
        const val COLUMN_REPORTED = "reported"
        const val COLUMN_IS_ACTIVITY = "is_activity"
        const val COLUMN_MEDIA = "media"
        const val COLUMN_SENDER = "sender"
        const val COLUMN_REPLY_MESSAGE_ID = "reply_message_id"
        const val COLUMN_REPLY_MESSAGE_SENDER = "reply_message_sender_id"
        const val COLUMN_REPLY_TO = "reply_to"
        const val COLUMN_PINNED = "pinned"
        const val COLUMN_HUDDLE_TYPE = "huddle_type"
        const val STAR_TYPE_STAR = "STAR"

        fun prefixHuddleId(id: Int): String {
            return HUDDLE_ID_PREFIX+id
        }
        fun prefixHuddleId(id: String): String {
            return if(id.startsWith(HUDDLE_ID_PREFIX)) id else HUDDLE_ID_PREFIX+id
        }

    }

    val commentsCountFormatted: String
        get() = DataFormatHelper.numberToK(totalComments)

    val giftCountFormatted: String
        get() = DataFormatHelper.numberToK(totalGifts)

    val huddleIdInt: Int
        get() = huddleId.removePrefix(HUDDLE_ID_PREFIX).toInt()

    val isStar: Boolean
        get() = starType != null

    override val showChatTextColor: Boolean
        get() = super.showChatTextColor && senderDetails?.blockedByEitherAdmin!=true

    val convertedMentionMessage: String
        get() = UserInfoUtil.decodeMentions(displayMessage.orEmpty(), mentionedUsers.orEmpty()) { user, token ->
            "${MentionTokenizer.TOKEN_START_CHAR}${user.userNickNameOrName}"
        }

    val isReportedOrBlocked: Boolean
        get() = reported || senderDetails?.blockedByEitherAdmin==true

    val isMyPostItem: Boolean
        get() = postType == HuddlePostType.POST || postType == HuddlePostType.REPLY

    val needsHeaderNotch: Boolean
        get() {
            return senderDetails?.priority !=null || senderDetails?.citizenship!=null
        }

    fun prefixHuddleId() {
        huddleId = prefixHuddleId(huddleId)
    }

    val formattedCreatedDateTime: String?
        get() = DateTimeUtils.format(parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")

    class Converter {
        @TypeConverter
        fun decode(data: String?): HuddleChatMessage? {
            data?: return null
            val type: Type = object : TypeToken<HuddleChatMessage?>() {}.type
            return Gson().fromJson<HuddleChatMessage>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: HuddleChatMessage?): String? {
            return Gson().toJson(someObjects)
        }
    }

    override fun sanitize() {
        prefixHuddleId()
        replyTo?.let {rt ->
            replyMessageId = rt.messageId
            replyMessageSenderId = rt.senderId
        }
        senderDetails?.id = sender

//        if (huddleType==HuddleType.PUBLIC) {
//            if (senderDetails?.deletedAccount==true) {
//                deleted = true
//            }
//        }
    }
}