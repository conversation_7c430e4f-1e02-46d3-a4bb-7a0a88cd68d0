package com.app.messej.data.model.notification

import com.app.messej.data.model.MediaMeta
import com.google.gson.annotations.SerializedName

data class HuddleMessageNotification(
    @SerializedName("thumbnail"         ) var thumbnail        : String? = null,
    @SerializedName("private"           ) var private          : <PERSON><PERSON>an,
    @SerializedName("name"              ) var name             : String,
    @SerializedName("message"           ) var message          : String = "",
    @SerializedName("message_id"        ) var messageId        : String,
    @SerializedName("media"             ) var media            : String? = null,
    @SerializedName("notification_id"   ) var notificationId   : Int    = 0,
    @SerializedName("sender_id"         ) var senderId         : Int,
    @SerializedName("sender_name"       ) var senderName       : String,
    @SerializedName("huddle_id"         ) var huddleId         : Int,
    @SerializedName("media_meta"        ) var mediaMeta        : MediaMeta? = null
)
