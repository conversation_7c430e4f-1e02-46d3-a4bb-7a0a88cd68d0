package com.app.messej.data.model.api.subscription


import com.google.gson.annotations.SerializedName

data class PlayStoreSuccessResponse(
    @SerializedName("acknowledged")
    val acknowledged: Boolean? = false,
    @SerializedName("autoRenewing")
    val autoRenewing: Boolean? = false,
    @SerializedName("orderId")
    val orderId: String? = "",
    @SerializedName("packageName")
    val packageName: String? = "",
    @SerializedName("productId")
    val productId: String? = "",
    @SerializedName("purchaseState")
    val purchaseState: Int? = 0,
    @SerializedName("purchaseTime")
    val purchaseTime: Long? = 0,
    @SerializedName("purchaseToken")
    val purchaseToken: String? = "",
    @SerializedName("quantity")
    val quantity: Int? = 0
)