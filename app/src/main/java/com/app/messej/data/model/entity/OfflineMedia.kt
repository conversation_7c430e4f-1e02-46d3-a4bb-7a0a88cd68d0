package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractMedia
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.api.media.S3UploadMedia
import com.app.messej.data.model.entity.OfflineMedia.Companion.COLUMN_MEDIA_KEY
import com.app.messej.data.model.entity.OfflineMedia.Companion.COLUMN_MESSAGE_ID
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.room.EntityDescriptions

@Entity(
    tableName = EntityDescriptions.TABLE_OFFLINE_MEDIA,
    indices = [
        Index(COLUMN_MESSAGE_ID, unique = true),
        Index(COLUMN_MEDIA_KEY, unique = true)
    ]
)
@TypeConverters(MediaUploadState.Converter::class)
/**
 * used to represent a media file in local storage
 * @param key: The S3 Key of the media file
 * @param name: the filename with extension
 * @param path: the absolutePath of the file in storage
 */
data class OfflineMedia (
    @PrimaryKey(autoGenerate = false) @ColumnInfo(name = COLUMN_MESSAGE_ID) var messageId: String,
    @ColumnInfo(name = COLUMN_MEDIA_KEY)
    val key: String,
    @ColumnInfo(name = COLUMN_MEDIA_NAME)
    override var name: String,
    override var path: String,
    override val mediaType: MediaType,
    @ColumnInfo(name = COLUMN_UPLOAD_STATE) var uploadState: MediaUploadState = MediaUploadState.None,
//    var pathIsSourceUri: Boolean = false

): AbstractMedia() {

    companion object {
        const val COLUMN_MESSAGE_ID = "message_id"
        const val COLUMN_MEDIA_NAME = "name"
        const val COLUMN_MEDIA_KEY = "key"
        const val COLUMN_UPLOAD_STATE = "uploadState"
    }

    val s3UploadMedia: S3UploadMedia
        get() = S3UploadMedia(file, key)

}