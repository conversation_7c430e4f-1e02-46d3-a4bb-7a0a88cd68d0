package com.app.messej.data.model

import com.app.messej.data.model.api.ReportRequest
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName

sealed class ReportPackage: ReportPreviewProvider {
    override val title: String? = null
    override val message: String? = null
    override val mediaMeta: MediaMeta? = null
    abstract override val user: AbstractUser?

    abstract override val contentType: ReportContentType
    abstract override val reportType: ReportType

    override val edited: Boolean
        get() = false

    override val deleted: Boolean
        get() = false

    override var countryFlag: Int? = null

    abstract fun getRequest(): ReportRequest

    data class ReportPackageSense(
        @SerializedName("contentType")
        val contentType: ReportContentType
    )

    data class HuddlePost(
        val chat: HuddleChatMessage,
        override val reportType: ReportType = ReportType.REPORT
    ): ReportPackage() {
        override val contentType: ReportContentType = ReportContentType.HUDDLE_POST
        override val message: String?
            get() = chat.displayMessage
        override val mediaMeta: MediaMeta?
            get() = chat.mediaMeta
        override val user: AbstractUser?
            get() = chat.senderDetails

        fun serialize(): String = Gson().toJson(this)

        override fun getRequest() = ReportRequest(
            contentType = contentType,
            reportType = reportType,
            userId = chat.sender,
            contentId = chat.messageId,
            parentId = chat.huddleIdInt.toString()
        )
    }

    data class HuddlePostComment(
        val comment: PostCommentItem,
        override val reportType: ReportType = ReportType.REPORT
    ): ReportPackage() {
        override val contentType: ReportContentType = ReportContentType.HUDDLE_COMMENT

        override val message: String?
            get() = comment.comment
        override val user: AbstractUser?
            get() = comment.senderDetails

        fun serialize(): String = Gson().toJson(this)

        override fun getRequest() = ReportRequest(
            contentType = contentType,
            reportType = reportType,
            userId = comment.senderId,
            contentId = comment.commentId,
            parentId = comment.messageId,
            appuppanId = comment.huddleId.toString()
        )
    }

    data class User(
        private val _user: AbstractUser.BasicUser,
        override val reportType: ReportType = ReportType.REPORT
    ): ReportPackage() {
        override val contentType: ReportContentType = ReportContentType.USER

        override val user: AbstractUser
            get() = _user

        fun serialize(): String = Gson().toJson(this)

        override fun getRequest() = ReportRequest(
            contentType = contentType,
            reportType = reportType,
            userId = _user.id,
            contentId = _user.id.toString()
        )
    }

    data class Flash(
        val flash: FlashVideo,
        override val reportType: ReportType = ReportType.REPORT
    ): ReportPackage() {
        override val contentType: ReportContentType = ReportContentType.FLASH
        override val message: String?
            get() = flash.caption
        override val mediaMeta: MediaMeta?
            get() = flash.mediaMeta.copy(
                mediaType = MediaType.VIDEO,
                thumbnail = flash.thumbnailUrl
            )
        override val user: AbstractUser?
            get() = flash.senderDetails

        fun serialize(): String = Gson().toJson(this)

        override fun getRequest() = ReportRequest(
            contentType = contentType,
            reportType = reportType,
            userId = flash.senderDetails?.id?:0,
            contentId = flash.id
        )
    }

    data class FlashComment(
        val comment: PostCommentItem,
        override val reportType: ReportType = ReportType.REPORT
    ): ReportPackage() {
        override val contentType: ReportContentType = ReportContentType.FLASH_COMMENT
        override val message: String?
            get() = comment.comment
        override val user: AbstractUser?
            get() = comment.senderDetails

        fun serialize(): String = Gson().toJson(this)

        override fun getRequest() = ReportRequest(
            contentType = contentType,
            reportType = reportType,
            userId = comment.senderId,
            contentId = comment.commentId,
            parentId = comment.flashId
        )
    }

    data class Postat(
        val postat: com.app.messej.data.model.entity.Postat,
        override val reportType: ReportType = ReportType.REPORT
    ): ReportPackage() {
        override val contentType: ReportContentType = ReportContentType.POSTAT
        override val message: String?
            get() = postat.message
        override val mediaMeta: MediaMeta?
            get() = postat.media.firstOrNull()?.asMediaMeta
        override val user: AbstractUser
            get() = postat.senderDetails

        fun serialize(): String = Gson().toJson(this)

        override fun getRequest() = ReportRequest(
            contentType = contentType,
            reportType = reportType,
            userId = postat.userId,
            contentId = postat.messageId
        )
    }

    data class PostatComment(
        val comment: com.app.messej.data.model.api.postat.PostComment,
        override val reportType: ReportType = ReportType.REPORT
    ): ReportPackage() {
        override val contentType: ReportContentType = ReportContentType.POSTAT_COMMENT
        override val message: String?
            get() = comment.comment
        override val user: AbstractUser?
            get() = comment.senderDetails

        fun serialize(): String = Gson().toJson(this)

        override fun getRequest() = ReportRequest(
            contentType = contentType,
            reportType = reportType,
            userId = comment.senderId,
            contentId = comment.commentId,
            parentId = comment.messageId
        )
    }

    data class PodiumReport(
        val podium: Podium,
        override val reportType: ReportType = ReportType.REPORT
    ) : ReportPackage() {

        override val contentType: ReportContentType = ReportContentType.PODIUM
        override val title: String?
            get() = podium.name
        override val user: AbstractUser?
            get() = null

        override val mediaMeta: MediaMeta?
            get() = MediaMeta(thumbnail = podium.thumbnail?.takeIf { it.isNotEmpty() } ?: podium.managerProfileThumbnail, mediaType = MediaType.IMAGE, s3Key = "")

        fun serialize(): String = Gson().toJson(this)

        override fun getRequest() = ReportRequest(
            contentType = contentType,
            reportType = reportType,
            userId = podium.managerId,
            contentId = podium.id
        )
    }
}