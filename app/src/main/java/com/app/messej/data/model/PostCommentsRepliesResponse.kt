package com.app.messej.data.model

import com.app.messej.data.model.entity.PostReply
import com.google.gson.annotations.SerializedName

data class PostCommentsRepliesResponse(
    @SerializedName("comments") val data: List<PostReply> = arrayListOf(),
    @SerializedName("page")val page: Int,
    @SerializedName("pages")val pages: Int,
    @SerializedName("per_page")val perPage: Int,
    @SerializedName("total")val total: Int
)
