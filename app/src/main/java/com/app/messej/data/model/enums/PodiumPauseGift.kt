package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PodiumPauseGift {
    @SerializedName("pause_podium_gifts") PODIUM_PAUSE_GIFT,
    @SerializedName("resume_podium_gifts") PODIUM_RESUME_GIFT;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}