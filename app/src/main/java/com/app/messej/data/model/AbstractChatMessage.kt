package com.app.messej.data.model

import androidx.room.ColumnInfo
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

abstract class AbstractChatMessage {
    abstract val messageId     : String
    abstract val roomId        : String?
    abstract var rawMessage       : String?
    abstract val createdTime   : String
    abstract val deliveredTime : String?
    abstract val sentTime      : String?
    abstract val forwardId : String?
    abstract val deleted       : Boolean

    abstract val isActivity    : Boolean
    abstract val activityMeta  : ActivityMeta?
    abstract var media         : String?
    abstract var mediaMeta     : MediaMeta?
    abstract var internalMessageType  : MessageType?

    abstract val read          : String?
    abstract val receiver      : Int?
    abstract val replyTo       : ReplyTo?
    abstract val sender        : Int

    abstract val liked    : Boolean
    abstract val reported : Boolean
    abstract val chatTextColor : ChatTextColor?

    enum class MessageType {
        TEXT,
        MEDIA,
        ACTIVITY,
        @SerializedName("poll") POLL,
        @SerializedName("location") LOCATION,
        @SerializedName("sticker") STICKER,
        @SerializedName("auto_generated_gift_message") GIFT;

        fun isNotActivity(): Boolean {
            return this != ACTIVITY
        }

        fun canSendInstantly() : Boolean = this in listOf(TEXT, LOCATION, STICKER)
    }

    enum class SendStatus { NONE, PENDING, SENDING }

    companion object {
        const val COLUMN_SEND_STATUS = "sendStatus"
    }

    /**
     * set to [SendStatus.PENDING] when creating a chat to be sent via socket.
     */
    @ColumnInfo(name = COLUMN_SEND_STATUS,defaultValue = "NONE")
    @Transient
    var sendStatus: SendStatus? = SendStatus.NONE
        get() {
            if (field==null) field = SendStatus.NONE
            return field
        }

    val messageType: MessageType
        get() {
            return if (isActivity) MessageType.ACTIVITY
            else if(hasLocation) MessageType.LOCATION
            else if(hasSticker) MessageType.STICKER
            else if(hasMedia) MessageType.MEDIA
            else if(hasPoll) MessageType.POLL
            else if(hasGift) MessageType.GIFT
            else MessageType.TEXT
        }

    var displayMessage: String?
        get() = if (messageType==MessageType.LOCATION) "" else rawMessage.orEmpty()
        set(value) {
            rawMessage = value
        }

    open val showChatTextColor: Boolean
        get() = !isActivity && !reported

    val mediaType: MediaType?
        get() = mediaMeta?.mediaType

    val hasText: Boolean
        get() = !displayMessage.isNullOrBlank()

    val hasMedia: Boolean
        get() = mediaMeta!=null
    val hasDocument:Boolean
        get()= mediaMeta?.mediaType == MediaType.DOCUMENT

    val hasLocation: Boolean
        get() = internalMessageType == MessageType.LOCATION
    val hasSticker: Boolean
        get() = internalMessageType == MessageType.STICKER
    private val hasPoll: Boolean
        get() = internalMessageType == MessageType.POLL
    private val hasGift: Boolean
        get() = internalMessageType == MessageType.GIFT

    val attachedLocation: AttachLocation?
        get() {
            if (hasLocation) {
                try {
                    rawMessage?.let {
                        return AttachLocation.decode(it)
                    }
                } catch (e: Exception) {
                    return null
                }
            }
            return null
        }

    val hasReply: Boolean
        get() = replyTo!=null

    val isRead: Boolean
        get() {
            return parsedReadTime!=null
        }
    val isDelivered: Boolean
        get() {
            return parsedDeliveredTime!=null
        }
    fun belongsToUser(id: Int?): Boolean {
        id?: return false
        return id==sender
    }

    open val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(createdTime)

    open val parsedDeliveredTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(deliveredTime)

    open val parsedReadTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(read)

    open val parsedSentTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(sentTime)

    open val formattedCreatedTime: String?
        get() = DateTimeUtils.format(parsedCreatedTime, DateTimeUtils.FORMAT_READABLE_TIME)

    open fun sanitize() {
        media = mediaMeta?.s3Key
    }

}