package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PostatTab {
    @SerializedName("unset")UNSET,
    @SerializedName("me") ME,
    @SerializedName("star") STARS,
    @SerializedName("all") ALL;

    fun serializedName(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    companion object {
        val default: PostatTab
            get() = ALL
    }

}