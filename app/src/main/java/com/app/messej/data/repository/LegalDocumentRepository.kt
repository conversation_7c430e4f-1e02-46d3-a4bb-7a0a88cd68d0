package com.app.messej.data.repository

import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.DocumentAPIService
import com.app.messej.data.model.api.policy.PolicyResponse
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf

class LegalDocumentRepository {

    suspend fun getLegalDocument(documentType: String): ResultOf<PolicyResponse> {
        return try {
            val resp = APIServiceGenerator.createService(DocumentAPIService::class.java).getLegalDocument(documentType)
            APIUtil.handleResponse(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

}