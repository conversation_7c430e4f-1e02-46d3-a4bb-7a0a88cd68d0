package com.app.messej.data.model.status


import com.google.gson.annotations.SerializedName

data class FlaxBalance(
    @SerializedName("current_flax_balance") val currentFlaxBalance: Double? = 0.0,
    @SerializedName("is_satisfied") val isSatisfied: Boolean? = false,
    @SerializedName("required_flax_balance") val requiredFlaxBalance: Int? = 0,
    @SerializedName("current_coin_balance") val currentCoinBalance: Double? = 0.0,
    @SerializedName("effective_flix") val effectiveFlixBalance: Double? = 0.0,
    @SerializedName("social_flax_balance") val socialFlaxBalance: Double? = 0.0,
    @SerializedName("is_social_satisfied") val isSocialSatisfied: Boolean? = false,
    @SerializedName("is_normal_satisfied") val isNormalSatisfied: Boolean? = false
)