package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class PodiumFreezeUserPayload(
    @SerializedName("podium_id"  ) var podiumId  : String?  = null,
    @SerializedName("user_id"    ) var userId    : Int?     = null,
    @SerializedName("freezed"    ) var frozen    : Boolean? = null,
    @SerializedName("frozen_by"  ) var frozenBy  : Int? = null,
    ): SocketEventPayload()