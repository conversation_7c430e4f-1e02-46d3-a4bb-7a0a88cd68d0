package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.ChallengePlayer
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.google.gson.annotations.SerializedName

data class ConFourDropTimedOutPayload(
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("podium_id") val podiumId: String?,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("opponent_user_id") val opponentUserId: Int,
    @SerializedName("participant_token_number") val participantTokenNumber: Int,
) : SocketEventPayload() {
    companion object {
        fun from(
            challenge: PodiumChallenge,
            player: ChallengePlayer,
            currentPlayerId: Int,
            opponentUserId: Int,
        ): ConFourDropTimedOutPayload {

            return ConFourDropTimedOutPayload(
                challengeId = challenge.challengeId,
                podiumId = challenge.podiumId,
                userId = currentPlayerId,
                opponentUserId = opponentUserId,
                participantTokenNumber = player.tokenNumber,
            )
        }
    }
}
