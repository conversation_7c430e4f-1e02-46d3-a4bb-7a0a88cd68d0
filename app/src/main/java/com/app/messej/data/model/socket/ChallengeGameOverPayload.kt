package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.PodiumChallengeScore
import com.google.gson.annotations.SerializedName

data class ChallengeGameOverPayload(
    @SerializedName("challenge_id"      ) val challengeId       : String,
    @SerializedName("podium_id"         ) val podiumId          : String?,
    @SerializedName("user_id"           ) val userId            : Int,
    @SerializedName("reason"            ) val reason            : GameOverReason?,
    @SerializedName("end_time"          ) val endTime           : String,
    @SerializedName("participants"      ) val participantScores : List<PodiumChallengeScore>?,
) : SocketEventPayload() {
    enum class GameOverReason {
        PARTICIPANT_EXITED_CHALLENGE,
        AUTO_CLOSE,
        DRAW,
        WON
    }
}
