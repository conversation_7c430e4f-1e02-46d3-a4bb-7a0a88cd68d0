package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.AbstractUserWithStats
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class FlashSearchResponse(
    @SerializedName("user_total"   ) var userTotal   : Int,
    @SerializedName("flash_total"  ) var flashTotal  : Int,
    @SerializedName("flashes"      ) var flashes     : List<FlashVideo> = listOf(),
    @SerializedName("users"        ) var users       : List<FlashUser>  = listOf(),
    @SerializedName("flash_offset" ) var flashOffset : Int,
    @SerializedName("user_offset"  ) var userOffset  : Int,
    @SerializedName("has_next"     ) var hasNextPage : Boolean            = false

) {
    data class FlashUser(
        @SerializedName("user_id") override var id: Int,
        @SerializedName("username") override var name: String,
        @SerializedName("thumbnail") override var thumbnail: String? = null,
        @SerializedName("name") override var username: String,
        @SerializedName("verified") override var verified: Boolean,
        @SerializedName("likers_count") override var likers: Int,
        @SerializedName("dears_count") override var dears: Int,
        @SerializedName("fans_count") override var fans: Int,
        @SerializedName("stars_count") override var stars: Int,
        @SerializedName("membership") override var membership: UserType
    ) : AbstractUserWithStats() {
        override val citizenship: UserCitizenship
            get() = UserCitizenship.default()
    }
}
