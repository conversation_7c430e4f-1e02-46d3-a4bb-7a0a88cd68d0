package com.app.messej.data.model.api.gift


import com.google.gson.annotations.SerializedName

data class GiftSendResponse(
    @SerializedName("gift_id") val giftId: Int? = 0,
    @SerializedName("id") val id: Int? = 0,
    @SerializedName("points") val points: Double? = 0.0,
    @SerializedName("receiver_id") val receiverId: Int? = 0,
    @SerializedName("sender_id") val senderId: Int? = 0,
    @SerializedName("time_created") val timeCreated: String? = "",
    @SerializedName("time_updated") val timeUpdated: String? = "",
    @SerializedName("sent_coins") val sentCoins:Double?=null,
    @SerializedName("user_rating") val userRating:Double?=null,
    @SerializedName("received_coins") val receivedCoins:Double?=null,
    @SerializedName("coins_received") val coinsReceived:Double?=null,
    @SerializedName("coins_sent") val coinsSent:Double?=null,
    @SerializedName("receiver_rating") val receiverRating:Double?=null,
    @SerializedName("manager_id")   val managerId: Int? = null,
    @SerializedName("manager_received_coins")  val managerReceivedCoins: Double? = null,
    @SerializedName("sent_flix") val sentFlix: Double? = null,
    @SerializedName("received_flix") val receivedFlix: Double? = null,
    @SerializedName("sending_fee") val sendingFee: Double? = null,

)