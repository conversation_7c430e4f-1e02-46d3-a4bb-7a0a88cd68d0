package com.app.messej.data.socket.repository

import com.app.messej.MainApplication
import com.app.messej.data.model.entity.BroadcastMessage
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.socket.BroadcastDeleteEvent
import com.app.messej.data.model.socket.BroadcastDeletePayload
import com.app.messej.data.model.socket.BroadcastLikeEvent
import com.app.messej.data.model.socket.BroadcastLikePayload
import com.app.messej.data.model.socket.BroadcastReadPayload
import com.app.messej.data.model.socket.BroadcastStarEvent
import com.app.messej.data.model.socket.BroadcastStarPayload
import com.app.messej.data.model.socket.BroadcastUnstarAllPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object BroadcastEventRepository: BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {

    private val accountRepo = AccountRepository(MainApplication.applicationInstance())
    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when (event) {
            ChatSocketEvent.RX_TX_BROADCAST -> onNewBroadcast(data)
            ChatSocketEvent.RX_TX_BROADCAST_STAR -> onStarBroadcast(data)
            ChatSocketEvent.RX_TX_BROADCAST_LIKE -> onLikeBroadcast(data)
            ChatSocketEvent.RX_TX_BROADCAST_DELETE -> onDeleteBroadcast(data)
            else -> return false
        }
        return true
    }

    private val _onStarAction = MutableSharedFlow<BroadcastStarEvent>(replay = 0)
    val onStarAction: SharedFlow<BroadcastStarEvent> = _onStarAction

    private fun onStarBroadcast(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<BroadcastStarEvent>(data.toString())
        var msg = db.getChatMessageDao().getBroadcastMessage(evt.messageId)
        msg ?: return@runBlocking
        msg = msg.copy(
            starred = evt.starred
        )
        withContext(Dispatchers.IO) {
            db.getChatMessageDao().updateBroadcast(msg)
        }
        _onStarAction.emit(evt)
    }

    private fun onDeleteBroadcast(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<BroadcastDeleteEvent>(data.toString())
        var msg = db.getChatMessageDao().getBroadcastMessage(evt.messageId)
        msg ?: return@runBlocking
        msg = msg.copy(
            deleted = true
        )
        withContext(Dispatchers.IO) {
            db.getChatMessageDao().updateBroadcast(msg)
        }
    }

    private fun onLikeBroadcast(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<BroadcastLikeEvent>(data.toString())
        var msg = db.getChatMessageDao().getBroadcastMessageByBroadcastId(evt.broadcastId)
        msg ?: return@runBlocking
        msg = msg.copy(
            totalLikes = evt.totalLikes
        )
        withContext(Dispatchers.IO) {
            db.getChatMessageDao().updateBroadcast(msg)
        }
    }

    private fun onNewBroadcast(data: JSONObject) = runBlocking {
        val msg = Gson().fromJson<BroadcastMessage>(data.toString())
        if (!accountRepo.loggedIn) return@runBlocking
        ChatRepository(MainApplication.applicationInstance()).insertNewChatMessage(msg, msg.broadcastId)
        if (accountRepo.user.id!=msg.broadcaster) {
            val star = db.getUserDao().getUserStar(msg.broadcaster)
            star?.let { starUser ->
                db.getUserDao().update(
                    starUser.copy(
                        unreadMessagesCount = starUser.unreadMessagesCount.inc(),
                        lastBroadcastTime = msg.createdTime
                    )
                )
            }
        }
    }

    fun sendBroadcastMessage(msg: BroadcastMessage) = ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_BROADCAST, msg.socketpayload)

    fun starBroadcastMessage(messages: List<BroadcastMessage>, star: Boolean) {
        val broadcaster = messages.getOrNull(0)?.broadcaster ?: return
        BroadcastMode.values().forEach { mode ->
            val ids = messages.filter { bm -> bm.broadcastMode == mode }.map { it.messageId }
            if (ids.isNotEmpty()) {
                val payload = BroadcastStarPayload(
                    broadcaster = broadcaster, broadcastType = mode, messageIds = ids, starred = star
                )
                ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_BROADCAST_STAR, payload)
            }
        }
    }

    fun unstarAllBroadcastMessages(broadcaster: Int, mode: BroadcastMode?) {
        if (mode != null) {
            ChatSocketRepository.sendEvent(ChatSocketEvent.TX_BROADCAST_UNSTAR_ALL, BroadcastUnstarAllPayload(broadcaster, mode))
        } else {
            BroadcastMode.values().forEach {
                ChatSocketRepository.sendEvent(ChatSocketEvent.TX_BROADCAST_UNSTAR_ALL, BroadcastUnstarAllPayload(broadcaster, it))
            }
        }
    }

    suspend fun likeBroadcastMessage(msg: BroadcastMessage) = withContext(Dispatchers.IO) {
        val like = !msg.liked
        val sent = ChatSocketRepository.sendEvent(
            ChatSocketEvent.RX_TX_BROADCAST_LIKE, BroadcastLikePayload(
                mode = msg.broadcastMode,
                broadcaster = msg.broadcaster,
                subscriber = msg.subscriber,
                broadcastId = msg.broadcastId,
                id = msg.messageId,
                liked = like,
                totalLikes = msg.totalLikes,
                message = msg.displayMessage.orEmpty()
            )
        )
        if (!sent) return@withContext
        var message = db.getChatMessageDao().getBroadcastMessageByBroadcastId(msg.broadcastId)
        message ?: return@withContext
        message = message.copy(
            totalLikes = if (like) message.totalLikes + 1 else (message.totalLikes - 1).coerceAtLeast(0), liked = like
        )
        db.getChatMessageDao().updateBroadcast(message)
    }

    fun deleteBroadcastMessage(messages: List<BroadcastMessage>, deleteForEveryone: Boolean) {
        val payload = BroadcastDeletePayload(
            messages = messages.map {
                BroadcastDeletePayload.MessagesToDelete(
                    messageId = it.messageId, mode = it.broadcastMode, broadcaster = it.broadcaster
                )
            }, deleteForEveryone = deleteForEveryone
        )
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_BROADCAST_DELETE, payload)
    }

    suspend fun setBroadcastMessageReadStatus(starId: Int) = withContext(Dispatchers.IO) {

        val message = db.getChatMessageDao().getLastBroadcastMessages(starId)
        message?.let {
            val payload = BroadcastReadPayload(
                messageId = message.messageId,
                broadcaster = message.broadcaster,
                broadcastId = message.broadcastId,
                broadcastMode = message.broadcastMode,
                createdTime = message.createdTime,
                subscriber = message.subscriber
            )
            val send = ChatSocketRepository.sendEvent(ChatSocketEvent.TX_BROADCAST_READ, payload)
            if (send) {
                db.getUserDao().setUserStarUnreadMessageCount(id = starId, count = 0)
            }
        }



    }
}