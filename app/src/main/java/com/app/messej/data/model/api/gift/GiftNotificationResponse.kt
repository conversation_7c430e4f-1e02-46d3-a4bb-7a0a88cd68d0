package com.app.messej.data.model.api.gift

import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.model.socket.AbstractGiftItem
import com.google.gson.annotations.SerializedName

class GiftNotificationResponse (
//    @SerializedName("userId") val userId: Int,
//    @SerializedName("status") val status: Boolean? = false,
//    @SerializedName("time_created") val timeCreated: String? = "",
//    @SerializedName("time_updated") val timeUpdated: String? = "",
    @SerializedName("id")                           override val id                         : Int,
    @SerializedName("gift_identifier")              override val giftIdentifier             : String?,
    @SerializedName("gift_url")                     override val animationUrl               : String?,
    @SerializedName("gift_static_url")              override val thumbnail                  : String?,
    @SerializedName("coins")                       override val coins                      : Int?,
    @SerializedName("gift_name")                    override val giftName                   : String?,
    @SerializedName("gift_type")                    override val giftType                   : GiftType?,
    @SerializedName("animation_time")                        val animationTime              :Int?,
    @SerializedName("gift_animation_url" )          override val giftAnimationUrl           : String?,
    @SerializedName("gift_animation_url_android" )  override val giftAnimationUrlAndroid    : String?,
    @SerializedName("special_occasion_date")        override val specialOccasionDate        : String?,
    @SerializedName("manager_id")                   override val managerId: Int? = null,
    @SerializedName("manager_received_coins")       override val managerReceivedCoins: Double? = null,
    override val flix: Int?,
    override val categoryName: String?,
    override val categoryId:Int?
): AbstractGiftItem() {
    override val description: String?
        get() = null

    override val nameTranslations: Translations?
        get() = null

    override val descTranslations: Translations?
        get() = null

    val isFlix: Boolean
        get() = categoryName == "FLiX"  && giftType == GiftType.BANK

    val isCoin: Boolean
        get() = categoryName == "COiNS" && giftType == GiftType.BANK
}