package com.app.messej.data.model.api.business

import com.google.gson.annotations.SerializedName

data class GetRestoreRatingResponse(
    @SerializedName("can_restore")val canRestore: Boolean?=null,
    @SerializedName("coin_balance")val coinBalance: Double?=null,
    @SerializedName("flix_balance")val flixBalance: Double?=null,
    @SerializedName("rating")val rating: Double?=null,
    @SerializedName("restorating_flix")val restoratingFlix: Double?=null,
    @SerializedName("sufficient_balance")val sufficientBalance: Boolean?=null,
    @SerializedName("effective_balance")val effectiveBalance: Double?=null,
    @SerializedName("sufficient_effective_balance")val sufficientEffectiveBalance: Boolean?=null,

){
val convertedRating:String
    get(){
        return rating?.toInt().toString() + "%"
    }
    val enableRestoreButton: Boolean
        get() {
            return sufficientBalance==true || sufficientEffectiveBalance == true /**old canRestore*/
        }
    val requiredFlix: Double
        get() = (restoratingFlix ?: 0.0) - (flixBalance?:0.0)

    val coinAmount: Double
        get() = requiredFlix * 100


}