package com.app.messej.data.repository

import android.app.Application
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.AppAPIService
import com.app.messej.data.model.api.promo.PromoHistoryResponse
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf

class AppRepository(val context: Application) {

    suspend fun getPromoHistory(): ResultOf<PromoHistoryResponse> {
        return try {
            val resp = APIServiceGenerator.createService(AppAPIService::class.java).getAnnouncements()
            APIUtil.handleResponse(resp)
        }catch (e:Exception){
            ResultOf.Error(Exception(e))
        }
    }
}