package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.api.huddles.FlashSearchResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FlashAccountSearchDataSource(private val api: FlashAPIService, private val searchKeyWord: String?): PagingSource<Int, FlashSearchResponse.FlashUser>() {

    companion object {
        private const val STARTING_KEY = 0
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FlashSearchResponse.FlashUser> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.flashAccountsSearch(
                    if (searchKeyWord.isNullOrBlank()) null else searchKeyWord,
                    offset = currentPage
                )
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (data.userOffset >= data.userTotal) null else data.userOffset

                LoadResult.Page(
                    data = data.users, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, FlashSearchResponse.FlashUser>): Int? {
        return null
    }
}