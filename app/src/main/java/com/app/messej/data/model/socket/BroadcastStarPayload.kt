package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.BroadcastMode
import com.google.gson.annotations.SerializedName

data class BroadcastStarPayload(
    @SerializedName("broadcaster") val broadcaster: Int,
    @SerializedName("broadcast_type") val broadcastType: BroadcastMode,
    @SerializedName("messages") val messageIds: List<String>,
    @SerializedName("starred") val starred: Bo<PERSON>an
): SocketEventPayload()