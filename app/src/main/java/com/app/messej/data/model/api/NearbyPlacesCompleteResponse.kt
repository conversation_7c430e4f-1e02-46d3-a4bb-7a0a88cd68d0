package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName

data class NearbyPlacesCompleteResponse(
    @SerializedName("html_attributions" ) val htmlAttributions : ArrayList<String>  = arrayListOf(),
    @SerializedName("next_page_token"   ) val nextPageToken    : String?            = null,
    @SerializedName("results"           ) val results          : ArrayList<Results> = arrayListOf(),
    @SerializedName("status"            ) val status           : String?            = null
){
    data class Results (
        @SerializedName("geometry"              ) val geometry            : Geometry?         = Geometry(),
        @SerializedName("icon"                  ) val icon                : String?           = null,
        @SerializedName("icon_background_color" ) val iconBackgroundColor : String?           = null,
        @SerializedName("icon_mask_base_uri"    ) val iconMaskBaseUri     : String?           = null,
        @SerializedName("name"                  ) val name                : String?           = null,
        @SerializedName("photos"                ) val photos              : ArrayList<Photos> = arrayListOf(),
        @SerializedName("place_id"              ) val placeId             : String?           = null,
        @SerializedName("reference"             ) val reference           : String?           = null,
        @SerializedName("scope"                 ) val scope               : String?           = null,
        @SerializedName("types"                 ) val types               : ArrayList<String> = arrayListOf(),
        @SerializedName("vicinity"              ) val vicinity            : String?           = null,
        @SerializedName("plus_code"             ) val plusCode            : PlusCode?         = null
    ) {

        data class PlusCode(
            @SerializedName("compound_code"     ) val compoundCode      : String? = null,
            @SerializedName("global_code"       ) val globalCode        : String? = null
        )

        data class Geometry (
            @SerializedName("location" ) val location : Location? = Location(),
        ) {
            data class Location (
                @SerializedName("lat" ) val lat : Double? = null,
                @SerializedName("lng" ) val lng : Double? = null
            )
        }

        data class Photos (
            @SerializedName("height"            ) val height           : Int?              = null,
            @SerializedName("html_attributions" ) val htmlAttributions : ArrayList<String> = arrayListOf(),
            @SerializedName("photo_reference"   ) val photoReference   : String?           = null,
            @SerializedName("width"             ) val width            : Int?              = null
        )
    }
}
