package com.app.messej.data.model.notification

import com.app.messej.data.model.enums.ChallengeType
import com.google.gson.annotations.SerializedName

data class ExternalContributorNotification(
    @SerializedName("notification_type" ) var notificationType : String? = "",
    @SerializedName("private"           ) var private          : Boolean? = null,
    @SerializedName("challenge_id"      ) var challengeId      : String,
    @SerializedName("receiver_id"       ) var receiverId       : Int,
    @SerializedName("html_text"         ) var htmlText         : String = "",
    @SerializedName("podium_name"       ) var podiumName       : String,
    @SerializedName("notification_id"   ) var notificationId   : Int,
    @SerializedName("message"           ) var message          : String,
    @SerializedName("category"          ) var category         : String? = "",
    @SerializedName("thumbnail_url"     ) var thumbnailUrl     : String? = null,
    @SerializedName("podium_id"         ) var podiumId         : String,
    @SerializedName("sender_id"         ) var senderId         : Int,
    @SerializedName("time_created"      ) var timeCreated      : String? = null,
    @SerializedName("game_type"         ) var challengeType    : ChallengeType? = null,
    @SerializedName("contributed_coins" ) var challengeFee     : Double? = null
)

