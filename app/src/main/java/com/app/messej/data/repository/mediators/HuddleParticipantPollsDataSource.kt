package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PollsAPIService
import com.app.messej.data.model.entity.PollParticipant
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1

class HuddleParticipantPollsDataSource(private val pollId: Int,private val api: PollsAPIService) : PagingSource<Int, PollParticipant>() {
    override fun getRefreshKey(state: PagingState<Int, PollParticipant>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PollParticipant> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                Log.d("onpollResponse", "load:${"enters polLload"} ")
                val response = api.getPollParticipants(page = 1, pollId = pollId, limit = 50)
                Log.d("onpollResponse", "load:$response ")
                val responseData = mutableListOf<PollParticipant>()
                val data = response.body()?.result?.pollParticipants ?: emptyList()

                responseData.addAll(data)
                val nextKey = if (!response.body()?.result!!.nextPage!!) null else currentPage.plus(1)

                LoadResult.Page(
                    data = response.body()?.result!!.pollParticipants,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        }catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}