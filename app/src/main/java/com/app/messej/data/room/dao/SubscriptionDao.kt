package com.app.messej.data.room.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.app.messej.data.model.entity.SubscriptionPurchaseRequest
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class SubscriptionDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(transferHistory: SubscriptionPurchaseRequest): Long

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_SUBSCRIPTION_DETAILS} ORDER BY payload_receipt_purchaseTime ASC")
   abstract suspend fun getAllPurchaseRequests(): List<SubscriptionPurchaseRequest>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_SUBSCRIPTION_DETAILS} WHERE payload_orderId = :orderId")
   abstract suspend fun deleteByOrderId(orderId: String)

}