package com.app.messej.data.room.dao

import androidx.lifecycle.LiveData
import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.TypeConverters
import androidx.room.Update
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.MediaUploadState
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.entity.FlashVideoWithMedia
import com.app.messej.data.model.entity.LocalFlashMedia
import com.app.messej.data.room.EntityDescriptions

@Dao
@TypeConverters(
    MediaMeta.Converter::class,
    MediaUploadState.Converter::class
)
abstract class FlashDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(flash: FlashVideo): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(flash: List<FlashVideo>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(media: LocalFlashMedia): Long

    @Update
    abstract suspend fun update(flash: FlashVideo): Int

    @Update
    abstract suspend fun update(media: LocalFlashMedia): Int

    @Query("DELETE FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_FLASH_TYPE} = :type AND ${FlashVideo.COLUMN_SEND_STATUS} = :status")
    abstract suspend fun deleteAllByTypeAndStatus(type: FlashVideo.FlashType, status: AbstractChatMessage.SendStatus)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_ID} = :id")
    abstract suspend fun deleteFlashVideo(id: String)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_ID} in (:idList)")
    abstract suspend fun deleteMultipleFlashVideo(idList: List<String>)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_FLASH_MEDIA} WHERE ${LocalFlashMedia.COLUMN_FLASH_ID} = :id")
    abstract suspend fun deleteFlashMedia(id: String)

    @Query("DELETE FROM ${EntityDescriptions.TABLE_FLASH_MEDIA} WHERE ${LocalFlashMedia.COLUMN_FLASH_ID} in (:idList)")
    abstract suspend fun deleteMultipleFlashMedia(idList: List<String>)

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_ID} = :id LIMIT 1")
    abstract suspend fun getLocalFlashVideo(id: String): FlashVideo?

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_ID} = :id LIMIT 1")
    abstract fun getLocalFlashVideoLiveData(id: String): LiveData<FlashVideo?>

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_FLASH_MEDIA} WHERE ${LocalFlashMedia.COLUMN_FLASH_ID} = :id LIMIT 1")
    abstract suspend fun getLocalFlashMedia(id: String): LocalFlashMedia?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_ID} = :id LIMIT 1")
    abstract suspend fun getLocalFlashVideoWithMedia(id: String): FlashVideoWithMedia?

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_FLASH_TYPE} = :type ORDER BY ${FlashVideo.COLUMN_SEND_STATUS} DESC, ${FlashVideo.COLUMN_CREATED_TIME} DESC")
    abstract fun getFlashVideoWithMediaPager(type: FlashVideo.FlashType): PagingSource<Int, FlashVideoWithMedia>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_FLASH_TYPE} = :type ORDER BY ${FlashVideo.COLUMN_SEND_STATUS} DESC, ${FlashVideo.COLUMN_CREATED_TIME} DESC")
    abstract fun getFlashVideoPager(type: FlashVideo.FlashType): PagingSource<Int, FlashVideo>

    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_FLASH} WHERE ${FlashVideo.COLUMN_SEND_STATUS}= :status ORDER BY ${FlashVideo.COLUMN_CREATED_TIME} ASC LIMIT 1")
    abstract suspend fun getFlashByStatus(status: AbstractChatMessage.SendStatus): FlashVideoWithMedia?

    @Query("UPDATE ${EntityDescriptions.TABLE_FLASH_MEDIA} SET ${LocalFlashMedia.COLUMN_UPLOAD_STATE}=:set WHERE ${LocalFlashMedia.COLUMN_UPLOAD_STATE} = :current")
    abstract suspend fun resetMediaUploadState(current: String?, set: String?)

    @Query("UPDATE ${EntityDescriptions.TABLE_FLASH} SET ${FlashVideo.COLUMN_SEND_STATUS}=:set WHERE ${FlashVideo.COLUMN_SEND_STATUS} = :current")
    abstract suspend fun resetFlashSendStatus(current: String?, set: String?)

    @Transaction
    open suspend fun resetSendStatus() {
        resetFlashSendStatus(AbstractChatMessage.SendStatus.SENDING.name, AbstractChatMessage.SendStatus.PENDING.name)
    }

    @Transaction
    open suspend fun resetUploadingMedia() {
        val converter = MediaUploadState.Converter()
        resetMediaUploadState(converter.encodeUploadState(MediaUploadState.Uploading(0)), converter.encodeUploadState(MediaUploadState.Pending))
    }
}