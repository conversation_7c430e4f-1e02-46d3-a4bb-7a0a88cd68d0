package com.app.messej.data.model.api

import com.app.messej.data.model.enums.EnabledFlashTypes
import com.google.gson.annotations.SerializedName

data class FlashEligibility(
    @SerializedName("enabled_flash_count") val enabledFlashCount: Int? = null,
    @SerializedName("enabled_flash_duration") val flashDuration: Int? = null,
    @SerializedName("enabled_flash_types") val enabledFlashTypes: EnabledFlashTypes? = null,
    @SerializedName("flash_eligibility") val flashEligibility: Boolean? = null
) {
    //for president case currently null. setting default value to 180 in null case
    //ie, for president users
    val enabledFlashDuration: Int
        get() = flashDuration ?: 180
}
