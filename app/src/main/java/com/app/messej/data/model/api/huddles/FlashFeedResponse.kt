package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.FlashTab
import com.google.gson.annotations.SerializedName

data class FlashFeedResponse(
    @SerializedName("items") var items: List<FlashVideo> = listOf(),
    @SerializedName("tab") var tab: FlashTab,
    @SerializedName("per_page") var pageSize: Int,
    @SerializedName("private_offset") var privateOffset: Int,
    @SerializedName("public_offset") var publicOffset: Int,
    @SerializedName("viewed_offset") var viewedOffset: Int,
    @SerializedName("huddle_offset") var huddleOffset: Int,
    @SerializedName("total") var totalItems: Int,
    @SerializedName("pages") var totalPages: Int,
    @SerializedName("page") var currentPage: Int,
)
