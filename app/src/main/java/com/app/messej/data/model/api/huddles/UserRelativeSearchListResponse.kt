package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.UserRelative
import com.google.gson.annotations.SerializedName

data class UserRelativeSearchListResponse(
    @SerializedName(value = "fans", alternate = ["likers","dears"]) var list             : ArrayList<UserRelative> = arrayListOf(),
    @SerializedName("total_pages"          ) var totalPages         : Int?              = null,
    @SerializedName("premium_likers_count" ) var premiumLikersCount : Int?              = null,
    @SerializedName("total_records"        ) var totalRecords       : Int?              = null,
    @SerializedName("next_page"            ) var nextPage           : Boolean?              = null
)
