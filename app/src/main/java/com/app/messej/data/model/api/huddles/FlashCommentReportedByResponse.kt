package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.enums.UserBadge
import com.google.gson.annotations.SerializedName

data class FlashCommentReportedByResponse(
    @SerializedName("page"       ) var currentPage : Int?               = null,
    @SerializedName("items"      ) var reports     : ArrayList<ReportedUser> = arrayListOf(),
    @SerializedName("total"      ) var total       : Int?               = null
){
    data class ReportedUser(
        @SerializedName("userId"            ) var userId   : Int?     = null,
        @SerializedName("comment"           ) var comment          : String?  = null,
        @SerializedName("deletedUser"       ) var deletedUser      : Boolean? = null,
        @SerializedName("category"          ) var category         : String?  = null,
        @SerializedName("category_text"     ) var categoryText     : String?  = null,
        @SerializedName("category_type"     ) var categoryType     : String?  = null,
        @SerializedName("language"          ) var language         : String?  = null,
        @SerializedName("report_id"         ) var reportId         : Int?     = null,
        @SerializedName("sender_membership" ) var senderMembership : String?  = null,
        @SerializedName("sender_name"       ) var senderName       : String?  = null,
        @SerializedName("sender_username"   ) var senderUsername   : String?  = null,
        @SerializedName("thumbnail"         ) var thumbnail        : String?  = null,
        @SerializedName("time_created"      ) var timeCreated      : String?  = null,
        @SerializedName("time_updated"      ) var timeUpdated      : String?  = null,
        @SerializedName("verified"          ) var verified         : Boolean? = null
    ) {
        val userBadge: UserBadge
            get() {
                return if(senderMembership == "Premium") UserBadge.PREMIUM
                else UserBadge.NONE
            }
    }
}
