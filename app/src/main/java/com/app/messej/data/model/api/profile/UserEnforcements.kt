package com.app.messej.data.model.api.profile

import com.google.gson.annotations.SerializedName

data class UserEnforcements(
    @SerializedName("user_id") val userId: Int,
    @SerializedName("enforcements_status") val enforcementsStatus: EnforcementStatus,
    @SerializedName("enforcements_meta") val enforcementsMeta: EnforcementMeta
) {
    companion object {
        const val ENF_KEY_BANNED = "BANNED"
        const val ENF_KEY_BLACKLISTED = "BLACKLISTED"
        const val ENF_KEY_DISABLE_BANNING = "DISABLE_BAN_REPORTING"
        const val ENF_KEY_DISABLE_FLASH_POST = "DISABLE_FLASH_POST"
        const val ENF_KEY_DISABLE_HUDDLE_POST = "DISABLE_HUDDLE_POST"
        const val ENF_KEY_DISABLE_POSTAT_POST = "DISABLE_POSTAT_POST"
        const val ENF_KEY_DISABLE_REPORTING_CONTENT = "DISABLE_REPORTING_CONTENT"
        const val ENF_KEY_DISABLE_REPORTING_USER = "DISABLE_REPORTING_USER"
        const val ENF_KEY_PENDING_BAN = "PENDING_BAN"
        const val ENF_KEY_PENDING_BLACKLIST = "PENDING_BLACKLIST"
        const val ENF_KEY_SUSPECTED_BAN = "SUSPECTED_BAN"
        const val DISABLE_CREATE_PODIUM = "DISABLE_CREATE_PODIUM"

        fun EnforcementStatus?.orEmpty(): EnforcementStatus {
            return this ?: EnforcementStatus()
        }

        fun EnforcementMeta?.orEmpty(): EnforcementMeta {
            return this ?: EnforcementMeta()
        }
    }

    data class EnforcementStatus(
        @SerializedName(ENF_KEY_BANNED) val banned: Boolean = false,
        @SerializedName(ENF_KEY_BLACKLISTED) val blacklisted: Boolean = false,
        @SerializedName(ENF_KEY_DISABLE_BANNING) val disableBanning: Boolean = false,
        @SerializedName(ENF_KEY_DISABLE_FLASH_POST) val disableFlashPost: Boolean = false,
        @SerializedName(ENF_KEY_DISABLE_HUDDLE_POST) val disableHuddlePost: Boolean = false,
        @SerializedName(ENF_KEY_DISABLE_POSTAT_POST) val disablePostatPost: Boolean = false,
        @SerializedName(ENF_KEY_DISABLE_REPORTING_CONTENT) val disableReportingContent: Boolean = false,
        @SerializedName(ENF_KEY_DISABLE_REPORTING_USER) val disableReportingUser: Boolean = false,
        @SerializedName(ENF_KEY_PENDING_BAN) val pendingBan: Boolean = false,
        @SerializedName(ENF_KEY_PENDING_BLACKLIST) val pendingBlacklist: Boolean = false,
        @SerializedName(ENF_KEY_SUSPECTED_BAN) val suspectedBan: Boolean = false,
        @SerializedName(DISABLE_CREATE_PODIUM) val disableCreatePodium: Boolean = false
    )
    data class EnforcementMeta(
        @SerializedName("content_reporting_fine_due_amount") val contentReportingFineDueAmount: Int = 0,
        @SerializedName("user_reporting_fine_due_amount") val userReportingFineDueAmount: Int = 0,
        @SerializedName("ban_reporting_fine_due_amount") val banReportingFineDueAmount: Int = 0,
        @SerializedName("suspected_ban_report_id") val suspectedBanReportId: Int? = null
    )
}