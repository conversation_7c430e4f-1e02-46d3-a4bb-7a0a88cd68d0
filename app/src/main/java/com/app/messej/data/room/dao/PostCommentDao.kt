package com.app.messej.data.room.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.app.messej.data.model.api.postat.PostComment
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class PostCommentDao {


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertPostComments(comments: List<PostComment>): List<Long>

    @Update
    abstract suspend fun updatePostComment(comment: PostComment): Int

    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POSTAT_COMMENTS} WHERE ${PostComment.COLUMN_COMMENT_ID} = :commentId AND ${PostComment.COLUMN_TYPE} = :type")
    abstract suspend fun getPostComment(commentId: String, type: CommentType): PostComment?


    @Query("UPDATE ${EntityDescriptions.TABLE_POSTAT_COMMENTS} SET totalLikeCount = totalLikeCount + :increment WHERE ${PostComment.COLUMN_COMMENT_ID} = :commentId AND ${PostComment.COLUMN_TYPE} = :type")
    abstract suspend fun updateCommentLikeCount(commentId: String, type: CommentType, increment: Int): Int


    @Query("DELETE FROM ${EntityDescriptions.TABLE_POSTAT_COMMENTS} WHERE ${PostComment.COLUMN_POSTAT_ID} = :basePostId AND ${PostComment.COLUMN_TYPE} = :type")
    abstract suspend fun deletePostComments(basePostId: String, type: CommentType): Int


    @Transaction
    @Query("SELECT * FROM ${EntityDescriptions.TABLE_POSTAT_COMMENTS} WHERE ${PostComment.COLUMN_POSTAT_ID} = :basePostId AND ${PostComment.COLUMN_TYPE} = :type ORDER BY ${PostComment.COLUMN_CREATED} DESC")
    abstract fun getBaseCommentsWithRepliesPaging(basePostId: String, type: CommentType): PagingSource<Int, PostCommentWithReplies>

    @Query("DELETE FROM ${EntityDescriptions.TABLE_POSTAT_COMMENTS} WHERE ${PostComment.COLUMN_COMMENT_ID} = :commentId AND ${PostComment.COLUMN_TYPE} = :type")
    abstract suspend fun deletePostCommentFromDB(commentId: String, type: CommentType): Int

}
