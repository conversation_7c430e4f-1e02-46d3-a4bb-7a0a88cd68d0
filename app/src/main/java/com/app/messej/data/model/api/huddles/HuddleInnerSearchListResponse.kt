package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.HuddleChatMessage
import com.google.gson.annotations.SerializedName

data class HuddleInnerSearchListResponse(
    @SerializedName("messages"          ) val messages          : List<HuddleChatMessage> = listOf(),
    @SerializedName("current_page"      ) val currentPage       : Int?               = null,
    @SerializedName("next_page"         ) val nextPage          : Boolean?              = null,
    @SerializedName("total"             ) val total             : Int?               = null
    )