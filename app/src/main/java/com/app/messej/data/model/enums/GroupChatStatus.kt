package com.app.messej.data.model.enums

enum class GroupChatStatus {
    OPEN_TO_JOIN,
    REQUEST_TO_JOIN,
    JOIN_REQUESTED,
    INVITED,
    BLOCKED, // Blocked by Group/Huddle Admin
    ADMIN_BLOCKED, // Blocked by Flashat Admin
    RESTRICTED,
    ACTIVE,
    UNKNOWN;

    companion object {

        /**
         * Returns list of entries for which a user cannot enter the group/huddle chat page
         */
        fun entryBlocked(): Array<GroupChatStatus> {
            return arrayOf(ADMIN_BLOCKED,BLOCKED)
        }
    }
}