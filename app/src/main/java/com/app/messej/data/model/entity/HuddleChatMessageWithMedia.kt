package com.app.messej.data.model.entity

import androidx.room.Embedded
import androidx.room.Relation
import com.app.messej.data.model.AbstractChatMessageWithMedia

data class HuddleChatMessageWithMedia(
    @Embedded
    override val message: HuddleChatMessage,
    @Relation(
        parentColumn = HuddleChatMessage.COLUMN_MEDIA,
        entityColumn = OfflineMedia.COLUMN_MEDIA_KEY
    )
    override var offlineMedia: OfflineMedia? = null,

    @Relation(
        parentColumn = HuddleChatMessage.COLUMN_SENDER,
        entityColumn = NickName.COLUMN_USER_ID
    )
    val nickName: NickName? = null,

    @Relation(
        parentColumn = HuddleChatMessage.COLUMN_REPLY_MESSAGE_SENDER,
        entityColumn = NickName.COLUMN_USER_ID
    )
    val replyNickName: NickName? = null

): AbstractChatMessageWithMedia() {

    val senderNickNameOrName: String
        get() {
            nickName?.nickName?.let {
                if(it.isNotEmpty()) {
                    return it
                }
            }
            return message.senderDetails?.name?:""
        }

    val replySenderNickNameOrName: String
        get() {
            replyNickName?.nickName?.let {
                if(it.isNotEmpty()) {
                    return it
                }
            }
            return message.replyTo?.senderName?:""
        }
}