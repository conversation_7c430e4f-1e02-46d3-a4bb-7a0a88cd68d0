package com.app.messej.data.model.api.poll


import android.os.Parcelable
import androidx.room.ColumnInfo
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class UserAnswer(
    @ColumnInfo(name = "answer_id") @SerializedName("answer_id") val answerId: Int? = 0,
    @ColumnInfo(name = "id") @SerializedName("id") val id: Int? = 0,
    @ColumnInfo(name = "poll_id") @SerializedName("poll_id") val pollId: Int? = 0,
    @ColumnInfo(name = "time_created") @SerializedName("time_created") val timeCreated: String? = "",
    @ColumnInfo(name = "time_updated") @SerializedName("time_updated") val timeUpdated: String? = "",
    @ColumnInfo(name = "user_id") @SerializedName("user_id") val userId: Int? = 0,
):Parcelable