package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.model.api.huddles.Participant
import com.app.messej.data.model.enums.ParticipantsSearchType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1

class HuddleParticipantsDataSource(private val api: ProfileAPIService, private val huddleId: Int, private val searchKeyword: String = "", private val searchTypeList: List<ParticipantsSearchType>,private val isTribe:Boolean?=null): PagingSource<Int, Participant>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Participant> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getHuddleParticipantsList(
                    huddleId, currentPage, searchKeyword, searchTypeList.joinToString(separator = ",") {
                        it.toString()
                    },isTribe
                )
                val responseData = mutableListOf<Participant>()
                val data = response.body()?.result?.members ?: emptyList()

                responseData.addAll(data)
                val nextKey = if (response.body()?.result!!.nextPage == null) null else response.body()?.result!!.nextPage!!

                LoadResult.Page(
                    data = response.body()?.result!!.members,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }

        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, Participant>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}
