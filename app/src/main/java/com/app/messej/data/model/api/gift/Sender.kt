package com.app.messej.data.model.api.gift


import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.LocalDateTime

data class Sender(
    @SerializedName("gift_id") val giftId: Int,
    @SerializedName("id") val id: Int,
    @SerializedName("points") val points: Double,
    @SerializedName("receiver_id") val receiverId: Int,
    @SerializedName("sender_id") val senderId: Int,
    @SerializedName("sender_name") val senderName: String,
    @SerializedName("time_created") val timeCreated: String,
    @SerializedName("time_updated") val timeUpdated: String,
    @SerializedName("sender_photo") val senderPhoto:String,
    @SerializedName("sender_ispremium") val senderIsPremium:Boolean,
){
    val parsedCreatedTime: LocalDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)?.run { DateTimeUtils.getLocalDateTime(this) }

    val getParsedTime: String
        get() = DateTimeUtils.format(parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")
}