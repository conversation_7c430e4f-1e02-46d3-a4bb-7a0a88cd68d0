package com.app.messej.data.model.entity

import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.model.api.business.BusinessPayoutStatus
import com.app.messej.data.model.api.business.BusinessTargets
import com.app.messej.data.model.enums.AppReviewStatus
import com.app.messej.data.model.enums.BusinessColor
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(tableName = EntityDescriptions.TABLE_BUSINESS_OPERATIONS)
data class BusinessOperation(
                            @SerializedName("app_shares_count") val appSharesCount: Int? = null,
                             @SerializedName("dears") val dears: Int? = null,
                             @SerializedName("dears_today") val dearsToday: Int? = null,
                             @SerializedName("new_app_share_percentage")
                             val newAppSharePercentage: Int? = 0,
                             @SerializedName("new_broadcast_percentage")
                             val newBroadcastPercentage: Int? = 0,
                             @SerializedName("new_customers_percentage")
                             val newCustomersPercentage: Int? = 0,
                             @SerializedName("new_followers_percentage")
                             val newFollowersPercentage: Int? = 0,
                              var newFansPercentage: Int? = 0,
                             @SerializedName("new_huddle_percentage")
                             var newHuddlePercentage: Int? = 0,
                             @SerializedName("new_likes_percentage")
                             val newLikesPercentage: Int? = 0,
                             @SerializedName("new_participant_percentage")
                             val newParticipantPercentage: Int? = 0,
                             @SerializedName("fans") var fans: Int? = null,
                             @SerializedName("fans_today") val fansToday: Int? = null,
                             @SerializedName("huddles_count") var huddlesCount: Int? = null,
                             @SerializedName("likers") val likers: Int? = null,
                             @SerializedName("rating") val rating: Boolean? = null,
                             @SerializedName("app_review_status") val appReviewStatus:String?=null,
                             @SerializedName("followers") val followers: Int? = null,
                             @SerializedName("membership") @PrimaryKey val membership: String = "",
                             @Embedded @SerializedName("requirement_targets") val requirementTargets: BusinessTargets? = null,
                             @SerializedName("minimum_pp_required") val minimumPpRequired: Double? = null,
                             @SerializedName("participant_count") val participantCount: Int? = null,
                             @SerializedName("payout_eligiblity") val payoutEligiblity: Boolean? = null,
                             @Embedded(prefix = "payout_")  @SerializedName("payout_status") val payoutStatus: BusinessPayoutStatus? = null,
                             @SerializedName("percent_of_app_shares") val percentOfAppShares: Int? = null,
                             @SerializedName("percent_of_followers") val percentOfFollowers: Int? = null,
                             @SerializedName("percent_of_likes") val percentOfLikes: Int? = null,
                             @SerializedName("percent_of_participants") val percentOfParticipants: Int? = null,
                             @SerializedName("profile_complete_percentage") val profileCompletePercentage: Int? = null,
                             @SerializedName("total_broadcasts") val totalBroadcasts: Int? = null,
                             @SerializedName("total_likes") val totalLikes: Int? = null) {


    fun percentageColor(percentage: Int?): BusinessColor {
        if (percentage == null) {
            BusinessColor.GREY
        }
        return when (percentage) {
            in 0..19 -> BusinessColor.RED
            in 20..49 -> BusinessColor.ORANGE
            in 50..79 -> BusinessColor.YELLOW
            in 80..99 -> BusinessColor.LIGHT_GREEN
             100 -> BusinessColor.GREEN
            else -> {
                BusinessColor.GREY
            }
        }
    }

    private fun getAppReview(appReviewStatus:String?): AppReviewStatus {
        return when (appReviewStatus) {
            "" -> AppReviewStatus.NO_RATING
            null -> AppReviewStatus.NO_RATING
            "Edit" -> AppReviewStatus.EDIT
            "Accepted" -> AppReviewStatus.ACCEPTED
            else -> AppReviewStatus.NO_RATING
        }
    }

    val reviewStatus: AppReviewStatus
        get() = getAppReview(appReviewStatus)

    val followerPercentColor: BusinessColor
        get() = percentageColor(newFollowersPercentage)
    val fansPercentColor: BusinessColor
        get() = percentageColor(newFansPercentage)

    val participantPercentColor: BusinessColor
        get() = percentageColor(newParticipantPercentage)

    val likesPercentColor: BusinessColor
        get() = percentageColor(newLikesPercentage)

    val sharesPercentColor: BusinessColor
        get() = percentageColor(newAppSharePercentage)

    val fansTodayCount: Int
        get() {
            return fansToday ?: 0
        }

    val dearsTodayCount: Int
        get() {
            return dearsToday ?: 0
        }

    val badgeColor: BusinessColor
        get() {
            return if ((dears ?: 0) < 25) {
                if ((fansToday ?: 0) >= 1 && dearsToday == 0) {
                    BusinessColor.ORANGE
                } else if (fansToday == 0 && dearsToday == 0) {
                    BusinessColor.RED
                } else {
                    BusinessColor.GREEN
                }
            } else if ((dears ?: 0) > 25) {
                if ((fansToday ?: 0) >= 1 && dearsToday == 0) {
                    BusinessColor.ORANGE
                } else if (fansToday == 0 && dearsToday == 0) {
                    BusinessColor.RED
                } else {
                    BusinessColor.GREEN
                }
            } else {
                // TODO color will be red when it is exactly 25. Intended behavior?
                BusinessColor.RED
            }
        }
}

