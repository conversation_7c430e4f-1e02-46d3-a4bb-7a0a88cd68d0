package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class PodiumAssemblyStartSpeakingPayload(
    @SerializedName("podium_id") val podiumId: String?,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("spoken_time" )      val spokenTime: Double? = null,
    @SerializedName("speaking_start_time") val speakingStartTime: Double? = null,
) : SocketEventPayload() {
}
