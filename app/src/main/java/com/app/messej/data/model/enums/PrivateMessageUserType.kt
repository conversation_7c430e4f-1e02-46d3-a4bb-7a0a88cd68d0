package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class PrivateMessageUserType {
    @SerializedName("not_initiated_chats") NOT_INITIATED_CHATS,
    @SerializedName("initiated_chats") INITIATED_CHATS;
    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }
}