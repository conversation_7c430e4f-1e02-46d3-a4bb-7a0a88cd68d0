package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class BroadcastMode {
    @SerializedName("DEARS") ALL_DEARS,
    @SerializedName("FANS") ALL_FANS,
    @SerializedName("ALL_LIKERS") ALL_LIKERS,
    @SerializedName("ALL_PREMIUM_LIKERS") ALL_PREMIUM_LIKERS;

    override fun toString(): String {
        return javaClass
            .getField(name)
            .getAnnotation(SerializedName::class.java)
            ?.value ?: ""
    }

    val isOfLikerType: <PERSON><PERSON><PERSON>
        get() {
            return this==ALL_LIKERS || this==ALL_PREMIUM_LIKERS
        }
}