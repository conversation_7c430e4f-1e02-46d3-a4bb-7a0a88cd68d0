package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.BroadcastMode
import com.google.gson.annotations.SerializedName

data class BroadcastDeletePayload(
    @SerializedName("messages")  val messages: List<MessagesToDelete>,
    @SerializedName("deleteForEveryone")  val deleteForEveryone: Boolean,
): SocketEventPayload() {
    data class MessagesToDelete(
        @SerializedName("id") val messageId: String,
        @SerializedName("broadcast_type") val mode: BroadcastMode,
        @SerializedName("broadcaster") val broadcaster: Int
    )
}
