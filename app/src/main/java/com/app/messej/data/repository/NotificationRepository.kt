package com.app.messej.data.repository

import android.app.Application
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.NotificationAPIService
import com.app.messej.data.model.api.NotificationUpdateRequest
import com.app.messej.data.model.api.ReadNotificationResponse
import com.app.messej.data.model.entity.Notification
import com.app.messej.data.repository.mediators.NotificationListRemoteMediator
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.LocalDate

class NotificationRepository(context: Application) {

    private var database = FlashatDatabase.getInstance(context)
    private var dao = database.getNotificationDao()

    @OptIn(ExperimentalPagingApi::class)
    fun getNotificationListPager(): Pager<Int, Notification> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     remoteMediator = NotificationListRemoteMediator(database, APIServiceGenerator.createService(NotificationAPIService::class.java)),
                     pagingSourceFactory = { database.getNotificationDao().notificationListPagingSource() })
    }

    suspend fun updateNotification(req: NotificationUpdateRequest, id: Int): ResultOf<Notification> {
        return try {
            val resp = APIServiceGenerator.createService(NotificationAPIService::class.java).updateNotification(req, id)
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    dao.updateNotification(result.value)
                }
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun deleteNotification(id: Int): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(NotificationAPIService::class.java).deleteNotification(id)
            val result = APIUtil.handleResponseWithoutResult(resp)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    dao.deleteNotification(id)
                }
                ResultOf.Success(result)
            } else if (result is ResultOf.Error) {
                ResultOf.getError(result.exception.message)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    suspend fun readNotification(id: Int): ResultOf<ReadNotificationResponse> {
        return try {
            val resp = APIServiceGenerator.createService(NotificationAPIService::class.java).readNotification(id, date = DateTimeUtils.format(
                LocalDate.now(), DateTimeUtils.FORMAT_DATE_YYYYMMDD_SLASHED))
            val result = APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                withContext(Dispatchers.IO) {
                    result.value.notification?.let {
                        dao.updateNotification(it)
                    }
                }
                ResultOf.Success(result)
            }
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

}