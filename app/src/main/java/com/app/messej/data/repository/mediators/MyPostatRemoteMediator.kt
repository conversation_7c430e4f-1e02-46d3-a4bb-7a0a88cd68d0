package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.PostatAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class MyPostatRemoteMediator(
    private val database: FlashatDatabase,
    private val postatApiService: PostatAPIService,
) : RemoteMediator<Int, Postat>() {

    private val postatDao = database.getMyPostatDao()
    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = EntityDescriptions.TABLE_MY_POSTAT

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, Postat>
    ): MediatorResult {
        val page = when (loadType) {
            LoadType.REFRESH -> 1
            LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
            LoadType.APPEND -> {
                Log.d("PHRM", "load: APPEND")
                val remoteKey = database.withTransaction {
                    remoteKeyDao.remoteKeyByQuery(tableKey)
                }

                if (remoteKey?.nextPage == null) {
                    return MediatorResult.Success(
                        endOfPaginationReached = true
                    )
                }
                remoteKey.nextPageInt
            }
        }

        return try {
            Log.e("MyPostat", "loading page: $page" )
            val response = postatApiService.getMyPostat(page)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            val posts = result.postatList

            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    postatDao.deleteAll(Postat.PostatType.FINAL)
                }
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, (result.currentPage.plus(1)).toString())
                )
                postatDao.insert(posts)
            }

            return MediatorResult.Success(endOfPaginationReached = result.nextPage == null)
        } catch (e: Exception) {
            Log.e("MyPostat", "load: ",e )
            MediatorResult.Error(e)
        }
    }
}


