package com.app.messej.data.model.api.huddles


import com.google.gson.annotations.SerializedName

data class HuddleSalesListResponse(
    @SerializedName("current_page") val currentPage: Int? = null,
    @SerializedName("huddle_for_sale") val huddleForSale: ArrayList<HuddleForSale> = arrayListOf(),
    @SerializedName("next_page") val nextPage: Boolean? = null,
    @SerializedName("total") val total: Int? = null,
)