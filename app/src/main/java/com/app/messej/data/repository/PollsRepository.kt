package com.app.messej.data.repository

import android.app.Application
import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingSource
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.PollsAPIService
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.api.PollAnswerRequest
import com.app.messej.data.model.api.poll.InvitePollRequest
import com.app.messej.data.model.api.poll.PollDeleteRequest
import com.app.messej.data.model.api.poll.PollResponse
import com.app.messej.data.model.entity.Poll
import com.app.messej.data.model.entity.PollParticipant
import com.app.messej.data.model.enums.PollType
import com.app.messej.data.repository.mediators.PollsListingRemoteMediator
import com.app.messej.data.repository.pagingSources.HuddleParticipantPollsDataSource
import com.app.messej.data.room.FlashatDatabase
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import java.time.ZoneId
import java.time.ZonedDateTime


class PollsRepository(val context: Application) {
    private var database = FlashatDatabase.getInstance(context)
    private var currentDate = ZonedDateTime.now(ZoneId.systemDefault())
   val date = DateTimeUtils.format(currentDate, DateTimeUtils.FORMAT_ISO_DATE_TIME)


    @OptIn(ExperimentalPagingApi::class)
    fun getPollsListingPager(huddleId: Int, status: PollType): Pager<Int, Poll> {
        Log.d("on time", "getPollsListingPager: $currentDate")
        val pagingSourceFactory: () -> PagingSource<Int, Poll> = when (status) {
//            "active" -> { database.getPollsDao().getActivePolls(huddleId) }
            PollType.SCHEDULE_POLL -> {
                { database.getPollsDao().getScheduledPolls(huddleId,date) }
            }
            PollType.PAST_POLL -> {{ database.getPollsDao().getPastPolls(huddleId, date) }}
            else -> { throw IllegalArgumentException("Invalid status: $status") }
        }

        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     remoteMediator = PollsListingRemoteMediator(database, APIServiceGenerator.createService(PollsAPIService::class.java),huddleId, status = status.type),
//                     pagingSourceFactory = {database.getPollsDao().getScheduledPolls(huddleId)}
                     pagingSourceFactory = pagingSourceFactory
                     )
    }


   /* @OptIn(ExperimentalPagingApi::class)
    fun getPollParticipants(pollId: Int): Pager<Int, PollParticipant> {
        return Pager(
            config = PagingConfig(pageSize = 50, enablePlaceholders = false),
            remoteMediator = HuddleParticipantPollsRemoteMediator(pollId,database, APIServiceGenerator.createService(PollsAPIService::class.java)),
            pagingSourceFactory = { database.getPollsDao().getParticipants(pollId)}
        )
    }*/

    fun getPollParticipants(pollId: Int): Pager<Int, PollParticipant> {
        return Pager(config = PagingConfig(pageSize = 50, enablePlaceholders = false),
                     pagingSourceFactory = { HuddleParticipantPollsDataSource(pollId,APIServiceGenerator.createService(PollsAPIService::class.java)) })
    }

    suspend fun updateHuddlePoll(pollId: Int, huddleId: Int): ResultOf<APIResponse<PollResponse>> {
        return try {
            val pollRequest = PollDeleteRequest(
                pollId = pollId,
                huddleId = huddleId,
                pollStatus = "Closed"
            )
            val response =  APIServiceGenerator.createService(PollsAPIService::class.java).deletePoll(pollRequest)

            val result = APIUtil.handleResponseWithMessage(response)
            if (result is ResultOf.Success) {
                ResultOf.Success(response.message())
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response.errorBody()!!)
                ResultOf.APIError(error)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPollsList(huddleId: Int, status: String): ResultOf<PollResponse> {

        return try {
            val resp = APIServiceGenerator.createService(PollsAPIService::class.java).getPollList(page =1, limit = 10, huddleId = huddleId, status =status )
            val result= APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                database.getPollsDao().insertPolls(result.value.polls)
            }
            result
        }catch (e:Exception){
            ResultOf.Error(Exception(e))
        }
    }

    suspend fun getPoll(pollId:Int): ResultOf<Poll> {
        return try {
            val resp = APIServiceGenerator.createService(PollsAPIService::class.java).getPollsById(pollId )
            val result= APIUtil.handleResponse(resp)
            if (result is ResultOf.Success) {
                database.getPollsDao().insertPoll(result.value)
            }
            result
        }catch (e:Exception){
            ResultOf.Error(Exception(e))
        }

    }

    suspend fun setAnswer(answerID: Int, pollID:Int): ResultOf<String> {
        return try {
            val resp = APIServiceGenerator.createService(PollsAPIService::class.java).setAnswer(PollAnswerRequest(answerID, pollID))
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {
            ResultOf.Error(Exception(e))
        }
    }

     fun getPolls(huddleId: Int) = database.getPollsDao().getPolls(huddleId)

    fun getSelectedPoll(pollID: Int) = database.getPollsDao().getSelectedPoll(pollID)

    suspend fun pollInviteByMember(pollID: Int,memberList:List<String>): ResultOf<String> {

        return try {
            val pollInviteRequest = InvitePollRequest(memberList)
            val resp = APIServiceGenerator.createService(PollsAPIService::class.java).pollInviteByMember(pollId = pollID, req = pollInviteRequest)
            APIUtil.handleResponseWithoutResult(resp)
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }

    }
}