package com.app.messej.data.agora

import android.util.Log
import com.app.messej.data.repository.PodiumRepository.JoinResultOf
import com.app.messej.data.utils.DateTimeUtils
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import java.time.ZonedDateTime

object PodiumEventService {

    private val _joinErrorFlow: MutableSharedFlow<JoinResultOf.APIError> = MutableSharedFlow()
    val joinErrorFlow: SharedFlow<JoinResultOf.APIError> = _joinErrorFlow

    suspend fun onJoinError(error: JoinResultOf.APIError) {
        _joinErrorFlow.emit(error)
    }

    var lastSyncEvent: ZonedDateTime? = null
        private set

    fun onSyncEvent() {
        Log.w("PLW", "onSyncEvent")
        lastSyncEvent = ZonedDateTime.now()
    }

    val timeSinceLastSync
        get() = DateTimeUtils.durationToNowFromPast(lastSyncEvent)
}