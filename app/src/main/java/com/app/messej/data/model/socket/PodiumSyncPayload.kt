package com.app.messej.data.model.socket

import android.util.Log
import com.app.messej.data.model.UserGiftPaused
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.entity.Podium.MaidanStatus
import com.app.messej.data.model.entity.Podium.SpeakerInvite
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.Duration
import java.time.ZonedDateTime

data class PodiumSyncPayload(
    @SerializedName("likes") val likes: Int,
    @SerializedName("total_coins") val totalCoins: Int?,
    @SerializedName("live_users") val liveUsers: Int,
    @SerializedName("total_users") val totalUsers: Int,
    @SerializedName("gifts_count") val giftCount: Int = 0,

    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("speaker_list") val speakerList: List<PodiumSpeaker> = listOf(),
    @SerializedName("admin_list") val adminList: List<Int>,
    @SerializedName("inactive_since") private val _inactiveSince: Long?,

    @SerializedName("paused_time") val pausedTime: String?,
    @SerializedName("live_user_hide") val liveUserHide: Boolean,

    @SerializedName("speaker_invites") val speakerInvites: List<SpeakerInvite>? = null,

    @SerializedName("talk_time_duration") val talkTimeDuration : Int? = null,
    @SerializedName("talk_time_start") val talkTimeStart    : Int? = null,

    @SerializedName("competitor_muted") val competitorMuted: Boolean = false,
    @SerializedName("maidan_status") val maidanStatus: MaidanStatus? = null,
    @SerializedName("maidan_sessions_count") val sessionCount: Int? = null,
    @SerializedName("gift_paused_participants") val giftsPausedParticipants:List<UserGiftPaused>?=null,
    @SerializedName("podium_gift_paused") val podiumGiftPaused : Boolean? = false,
    @SerializedName("yalla_challenges_count") val yallaChallengesCount : Int? = 0,
    @SerializedName("allow_yalla") val allowYallaGuys : Boolean? = true,
    @SerializedName("anthem_start_time") val anthemStartTime: Long? = null,

    ) : SocketEventPayload() {

    fun copyTo(pod: Podium): Podium {
        return pod.copy(
            likes = this.likes,
            totalCoins = this.totalCoins,
            liveUsers = this.liveUsers,
            totalUsers = this.totalUsers,
            giftCount = this.giftCount,

            hideLiveUsers = this.liveUserHide,

            talkTimeDuration = this.talkTimeDuration,
            talkTimeStart = this.talkTimeStart,

            competitorUserId = if(pod.kind==PodiumKind.MAIDAN) this.speakerList.firstOrNull { !it.isManager }?.id else null,
            competitorMuted = this.competitorMuted,
            maidanStatus = this.maidanStatus,
            podiumGiftPaused = this.podiumGiftPaused,
            allowYallaGuys = this.allowYallaGuys!=false,
            yallaChallengesCount = this.yallaChallengesCount,
            sessionCount = this.sessionCount,
            flashatAnthem = this.anthemStartTime?.let { start ->
                Podium.Anthem(start,this.podiumId)
            }
        )
    }

    val inactiveSince: ZonedDateTime?
        get() = if(_inactiveSince!=null) DateTimeUtils.parseMillisToDateTime(_inactiveSince*1000) else null

    companion object {
        // will close in 20 minutes. show alert after 18:45.
        const val SHOW_CLOSE_ALERT_AFTER_SECONDS: Long = (18*60)+15
        const val AUTO_CLOSE_AFTER_SECONDS: Long = 20*60
    }
    val aboutToClose: Boolean
        get() {
            Log.w("PSP", "inactive: $_inactiveSince | $inactiveSince elapsed: ${DateTimeUtils.durationToNowFromPast(inactiveSince)?.seconds}, SHOW_CLOSE_ALERT_AFTER_SECONDS: $SHOW_CLOSE_ALERT_AFTER_SECONDS ", )
            val inactive = inactiveSince?: return false
            return (DateTimeUtils.durationToNowFromPast(inactive)?.seconds ?: 0) >= SHOW_CLOSE_ALERT_AFTER_SECONDS
        }

    val willAutoCloseIn: Duration?
        get() {
            val inactive = inactiveSince?: return null
            val closeTime = inactive.plusSeconds(AUTO_CLOSE_AFTER_SECONDS)
            Log.w("PSP", "elapsed: ${DateTimeUtils.durationToNowFromPast(inactive)?.seconds}, close in: ${DateTimeUtils.durationFromNowToFuture(closeTime)?.seconds} ", )
            if (closeTime.isBefore(ZonedDateTime.now())) return null
            return DateTimeUtils.durationFromNowToFuture(closeTime)
        }

    val parsePausedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(pausedTime)

    val speakingTimeRemaining: Long
        get() {
            if(pausedTime == null) return 0
            return ((DateTimeUtils.durationFromNowToFuture(parsePausedTime))?: Duration.ZERO).toMillis() + (60*1000L)
        }
}
