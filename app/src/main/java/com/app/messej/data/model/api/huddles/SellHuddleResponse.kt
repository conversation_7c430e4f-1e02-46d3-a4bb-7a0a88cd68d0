package com.app.messej.data.model.api.huddles


import com.google.gson.annotations.SerializedName

data class SellHuddleResponse(
    @SerializedName("about")
    val about: String,
    @SerializedName("flax")
    val flax: Double?=null,
    @SerializedName("huddle_id")
    val huddleId: Int,
    @SerializedName("huddle_name")
    val huddleName: String,
    @SerializedName("id")
    val id: Int,
    @SerializedName("owner")
    val owner: Int,
    @SerializedName("participant_count")
    val participantCount: Int,
    @SerializedName("status")
    val status: String,
    @SerializedName("time_created")
    val timeCreated: String,
    @SerializedName("time_updated")
    val timeUpdated: String
)