package com.app.messej.data.model

import androidx.room.TypeConverter
import com.app.messej.data.model.api.profile.CurrentUserAndAccountDetails
import com.app.messej.data.model.api.profile.UserEnforcements
import com.app.messej.data.model.enums.Gender
import com.app.messej.data.model.enums.ProfileCheckpoint
import com.app.messej.data.model.enums.UserBadge
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DDMMYYYY_DASHED
import com.app.messej.data.utils.DateTimeUtils.FORMAT_ISO_DATE_TIME
import com.app.messej.data.utils.UserInfoUtil
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import java.time.LocalDate
import java.time.LocalDateTime

data class CurrentUser(
    @SerializedName("active"                      ) var active                    : Boolean? = null,
    @SerializedName("blocked_by_admin"            ) override var blockedByAdmin            : Boolean? = null,
    @SerializedName("email"                       ) override var email                     : String?  = null,
    @SerializedName("email_verified"              ) var emailVerified             : Boolean? = null,
    @SerializedName("id"                          ) override var id               : Int,
    @SerializedName("phone"                       ) override var phone                     : String?  = null,
    @SerializedName("phone_verified"              ) var phoneVerified             : Boolean? = null,
    @SerializedName("profile"                     ) var profile                   : Profile,
    @SerializedName("profile_checkpoint"          ) var profileCheckpoint         : ProfileCheckpoint?     = null,
    @SerializedName("profile_complete_percentage" ) override var profileCompletePercentage : Int     = 0,
    @SerializedName("time_created"                ) var timeCreated               : String?  = null,
    @SerializedName("time_updated"                ) var timeUpdated               : String?  = null,
    @SerializedName("username"                    ) override var username         : String,
    @SerializedName("membership"                  ) override var membership       : UserType = UserType.FREE,
    @SerializedName("verified"                    ) override var verified         : Boolean = false,
    @SerializedName("citizenship"                 ) override val citizenship      : UserCitizenship,
    @SerializedName("empowerments"                ) override val userEmpowerment           : UserEmpowerment? = null,
    @SerializedName("country_code"                ) override var countryCode               : String?  = null,
    @SerializedName("country_code_iso"            ) override var countryCodeIso            : String?  = null,
    @SerializedName("has_password"                ) var hasPassword               : Boolean?  = false,
    @SerializedName("flash_block"                 ) override var isFlashBlocked : Boolean? = false,
    @SerializedName("fans"                        ) override var fans                      : Int = 0,
    @SerializedName("dears"                       ) override var dears                     : Int = 0,
    @SerializedName("stars"                       ) override var stars                     : Int = 0,
    @SerializedName("likers"                      ) override var likers                    : Int = 0,

    @SerializedName("user_level_animation_url") override var userLevelAnimationUrl : String? = null,
    @SerializedName("citizenship_priority") override var citizenshipPriority : Int? = null,
    @SerializedName("location_changed"            ) override var locationChanged : Boolean? = false,
    @SerializedName("active_points"                ) override val activePoints              : Double?           = null,
    @SerializedName("total_gift_points"            ) override var totalGiftPoints              : Double?           = null,
    @SerializedName("is_flix_subscription_renewed" ) override var isFlixSubscriptionRenewed : Boolean?          = null,
    @SerializedName("user_functionality_blocks"    ) override val userFunctionalityBlocks : UserFunctionalityBlocks? = null,
    @SerializedName("coins_needed_next_contributor_level") override val coinsNeededNextContributorLevel : Double?=0.0,
    @SerializedName("games_needed_next_player_level") override val gamesNeededNextPlayerLevel : Double?=0.0,
    @SerializedName("enforcements_status" ) override val enforcementStatus: UserEnforcements.EnforcementStatus? = null,
    @SerializedName("superstar_id") override val superStarId: Int? = null,
    @SerializedName("user_tribe_name") val tribeName: String?=null

): CurrentUserAndAccountDetails(), UserRatingProvider {
    data class Profile (
        @SerializedName("name"                         ) var name                       : String  = "",
        @SerializedName("date_of_birth"                ) var dateOfBirth                : String? = null,
        @SerializedName("gender"                       ) var genderString               : String? = null,
        @SerializedName("about"                        ) var about                      : String? = null,
        @SerializedName("profile_photo"                ) var profilePhoto               : String? = null,
        @SerializedName("thumbnail"                    ) var thumbnail                  : String? = null,
        @SerializedName("referral_code"                ) var referralCode               : String? = null,
        @SerializedName("referral_link"                ) var referralLink               : String? = null,
        @SerializedName("subscription_expiration_date" ) private var _subscriptionExpiration : String? = null,
        @SerializedName("time_created"                 ) var timeCreated                : String? = null,
        @SerializedName("time_updated"                 ) var timeUpdated                : String? = null,
        @SerializedName("total_performance_points"     ) var totalPerformancePoints     : Double?  = null,
        @SerializedName("verified"                     ) var verified                   : Boolean = false,
        @SerializedName("is_premium"                   ) var premium                    : Boolean = false,
        @SerializedName("allow_change_location"        ) var allowChangeLocation        : Boolean? = null,
        @SerializedName("allow_hide_country_flag"      ) var allowHideCountryFlag       : Boolean? = null,
        @SerializedName("long"                         ) val long                       : Double?  = null,
        @SerializedName("lat"                          ) val lat                        : Double?  = null,
        @SerializedName("user_flax_rate"               ) val userRatingPercentage                 : Double? = null
    ) {
        val userBadge: UserBadge
            get() {
                return if(verified) UserBadge.VERIFIED
                else if(premium) UserBadge.PREMIUM
                else UserBadge.NONE
            }

        val gender: Gender?
            get() = Gender.from(genderString.orEmpty())

        val dobParsed: LocalDate?
            get() = DateTimeUtils.parseDate(dateOfBirth,FORMAT_DDMMYYYY_DASHED)

        var subscriptionExpiration: LocalDateTime?
            get() = DateTimeUtils.parseDateTime(_subscriptionExpiration, FORMAT_ISO_DATE_TIME)
            set(value) {_subscriptionExpiration = DateTimeUtils.format(value, FORMAT_ISO_DATE_TIME)}

        val formattedSubscriptionExpiration: String
            get() = DateTimeUtils.formatToCurrentZone(subscriptionExpiration, DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)
    }

    data class UserEmpowerment(
//        @SerializedName("time_created"             ) val timeCreated           : String,
//        @SerializedName("time_updated"             ) val timeUpdated           : String,
//        @SerializedName("id"                       ) val id                    : Int,
//        @SerializedName("user_id"                  ) val userId                : Int,
        @SerializedName("allow_hide_flag"          ) val canHideFlag         : Boolean = false,
        @SerializedName("allow_change_location"    ) val canChangeLocation   : Boolean = false,
        @SerializedName("allow_block_user_huddle"  ) val canBlockAnyUserInHuddle  : Boolean = false,
        @SerializedName("allow_block_user_flashat" ) val canTemporarilyBlockAnyUser : Boolean? = false,
        @SerializedName("allow_delete_post_huddle" ) val canDeleteAnyHuddlePost : Boolean = false,
        @SerializedName("allow_enter_huddle"       ) val canEnterAnyHuddle      : Boolean = false,
        @SerializedName("allow_delete_flash_video" ) val canDeleteAnyFlashVideo : Boolean?=null,
        @SerializedName("allow_end_podium"         ) val allowEndPodium  : Boolean? = false,
        @SerializedName("allow_join_hidden_podium"         ) val allowJoinHiddenPodium  : Boolean? = false,
        @SerializedName("allow_delete_comments_podium") val allowDeleteCommentsFromPodium : Boolean? = null,
        @SerializedName("allow_end_speaking_session_podium") val allowEndSpeakingSessionPodium : Boolean? = null,
        @SerializedName("allow_block_user_speaking_podium") val allowBlockUserSpeakingPodium : Boolean? = null,
        @SerializedName("allow_block_user_podium") val allowBlockUserPodium : Boolean? = null,
        @SerializedName("allow_block_user_comments_podium") val allowBlockUserCommentsPodium : Boolean? = null,
        @SerializedName("allow_hide_online_status") val allowHideOnlineStatus : Boolean? = null,
        @SerializedName("allow_freeze_user_comments_podium") val allowFreezeUserCommentsPodium : Boolean? = null,
        @SerializedName("allow_delete_postat_post") val allowDeletePostatPost : Boolean? = null,
        @SerializedName("allow_block_user_posting_postat") val allowBlockUserPostingPostat : Boolean? = null,
        @SerializedName("allow_block_user_posting_flash") val allowBlockUserPostingFlash : Boolean? = null,
        @SerializedName("allow_block_user_posting_huddle") val allowBlockUserPostingHuddle : Boolean? = null,
        @SerializedName("allow_see_hidden_users_podium") val allowSeeHiddenUsersPodium : Boolean? = null,
        @SerializedName("allow_join_speak_podiums_for_free") val allowJoinSpeakPodiumForFree : Boolean? = null,

        ){
        class UserEmpowermentTypeConverter {

            @TypeConverter
            fun fromJson(json: String?):CurrentUser.UserEmpowerment? {
                return Gson().fromJson(json, CurrentUser.UserEmpowerment::class.java)
            }

            @TypeConverter
            fun toJson(userEmpowerment: CurrentUser.UserEmpowerment?): String? {
                return Gson().toJson(userEmpowerment)
            }
        }

        val isFlashBlocked: Boolean
            get() = allowBlockUserPostingFlash?:false
        val isPodiumBlocked: Boolean
            get() = allowBlockUserPodium?:false
        val isPodiumSpeakingBlocked: Boolean
            get() = allowBlockUserSpeakingPodium?:false
        val isPodiumWriteCommentsBlocked: Boolean
            get() = allowBlockUserCommentsPodium?:false
        val isPostatPostsBlocked: Boolean
            get() = allowBlockUserPostingPostat?:false
        val isHuddlePostsBlockBlocked: Boolean
            get() = allowBlockUserPostingHuddle?:false
    }
    data class UserFunctionalityBlocks(
       @SerializedName("time_created") val timeCreated : String?=null,
       @SerializedName("time_updated") val timeUpdated : String?=null,
       @SerializedName("id") val id : Int?=null,
       @SerializedName("user_id") val userId : Int?=null,
       @SerializedName("flash_block") val flashBlock : Boolean?=null,
       @SerializedName("podium_block") val podiumBlock : Boolean?=null,
       @SerializedName("podium_speaking_block") val podiumSpeakingBlock : Boolean?=null,
       @SerializedName("podium_write_comments_block") val podiumWriteCommentsBlock : Boolean?=null,
       @SerializedName("postat_posts_block") val postatPostsBlock : Boolean?=null,
       @SerializedName("huddle_posts_block") val huddlePostsBlock : Boolean?=null,
    ){
        class UserFunctionalityBlocksTypeConverter {

            @TypeConverter
            fun fromJson(json: String?):CurrentUser.UserFunctionalityBlocks? {
                return Gson().fromJson(json, CurrentUser.UserFunctionalityBlocks::class.java)
            }

            @TypeConverter
            fun toJson(userFunctionalityBlocks: CurrentUser.UserFunctionalityBlocks?): String? {
                return Gson().toJson(userFunctionalityBlocks)
            }
        }
    }
    companion object {
        fun UserEmpowerment?.orDefault(): UserEmpowerment = this ?: UserEmpowerment()
        fun UserFunctionalityBlocks?.orDefault(): UserFunctionalityBlocks = this ?: UserFunctionalityBlocks()
    }

    override val name: String
        get() = profile.name

    override val thumbnail: String?
        get() = profile.thumbnail

    var premium: Boolean
        get() = profile.premium
        set(value) {
            profile.premium = value
        }

    val phoneNumberWithCountryCode: String?
        get() {
            val phone = phone?: return null
            val cc = countryCode?:return phone
            return UserInfoUtil.combineCountryCodeAndMobileNumber(cc, phone)
        }

    val totalFollowersCount: Int
        get() = dears+fans+likers

    val hasEmpowerment: Boolean
        get() = userEmpowerment?.run {
            listOf(
                allowBlockUserPostingFlash,
                allowBlockUserPodium,
                allowBlockUserSpeakingPodium,
                allowBlockUserCommentsPodium,
                allowBlockUserPostingHuddle,
                allowBlockUserPostingPostat
            ).any { it == true }
        } ?: false

    override val userRating: Double?
        get() = (profile.userRatingPercentage ?: 0.0) / 100

    fun asUser() = User(
        id = id,
        name = profile.name,
        thumbnail = profile.thumbnail,
        username = username,

        membership = membership,
        verified = verified,
        blocked = blockedByAdmin?:false,
    )

    val coinBalance : Double
        get() = totalGiftPoints ?: 0.0


    val themeConfig: ThemeConfig
        get() {
            val userIsInRegistrationFlow = profileCheckpoint != ProfileCheckpoint.PROFILE_CHECKPOINT_NONE
            return ThemeConfig(
                showPremiumTheme = premium || userIsInRegistrationFlow, allowDayNightSetting = premium && !userIsInRegistrationFlow
            )
        }

    data class ThemeConfig(
        val showPremiumTheme: Boolean,
        val allowDayNightSetting: Boolean,
    )

    // Add this property to check if user is Egyptian
    val isEgyptianUser: Boolean
        get() = countryCodeIso == "EG"
}
