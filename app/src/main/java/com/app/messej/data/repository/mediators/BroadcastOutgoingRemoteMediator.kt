package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.BroadcastAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.BroadcastChatMessageWithMedia
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.room.FlashatDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@OptIn(ExperimentalPagingApi::class)
class BroadcastOutgoingRemoteMediator(
    private val userId: Int,
    private val mode: BroadcastMode,
    private val database: FlashatDatabase,
    private val networkService: BroadcastAPIService
) : RemoteMediator<Int, BroadcastChatMessageWithMedia>() {
    val dao = database.getChatMessageDao()

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, BroadcastChatMessageWithMedia>
    ): MediatorResult {
        return try {
            when (loadType) {
                LoadType.REFRESH -> {
                    Log.d("BCMRM", "load: trying to REFRESH")
                }
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> return MediatorResult.Success(endOfPaginationReached = true)
            }

           val response = networkService.getOutgoingBroadcasts(userId = userId, type = mode)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            val messages = result.broadcasts

            withContext(Dispatchers.IO) {
                database.withTransaction {
                    dao.insertBroadcastMessages(messages)
                }
            }
            MediatorResult.Success(endOfPaginationReached = true)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}