package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.flow.StateFlow

@Entity(tableName = EntityDescriptions.TABLE_USER_NICK_NAMES)
data class NickName(
    @PrimaryKey @SerializedName("created_for_id" ) @ColumnInfo(name = COLUMN_USER_ID       ) var userId: Int,
    @SerializedName("nick_name"                  ) @ColumnInfo(name = "nickName"           ) var nickName: String? = null,
    @SerializedName("time_created"               ) @ColumnInfo(name = "timeCreated"        ) var timeCreated: String? = null,
    @SerializedName("time_updated"               ) @ColumnInfo(name = "timeUpdated"        ) var timeUpdated: String? = null
) {
    companion object {
        const val COLUMN_USER_ID = "userId"

        fun List<NickName>.findNickName(id: Int): String? {
            // Return null if null or blank otherwise value will be return
            val nn = this.find { it.userId == id }?.nickName
            return if (nn.isNullOrBlank()) null else nn
        }

        fun List<NickName>?.nickNameOrName(user: AbstractUser): String {
            // Return null if null or blank otherwise value will be return
            this?: return user.name
            return findNickName(user.id)?:user.name
        }

        fun StateFlow<List<NickName>>.nickNameOrName(user: AbstractUser?): String {
            user?: return ""
            // Return null if null or blank otherwise value will be return
            return this.value.nickNameOrName(user)
        }

        fun List<NickName>?.nickNameOrName(id: Int, name: String): String {
            // Return null if null or blank otherwise value will be return
            this?: return name
            return findNickName(id)?:name
        }

        fun StateFlow<List<NickName>>.nickNameOrName(id: Int, name: String): String {
            // Return null if null or blank otherwise value will be return
            return this.value.nickNameOrName(id,name)
        }
    }
}