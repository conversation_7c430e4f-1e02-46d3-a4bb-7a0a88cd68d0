package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class HuddleMentionableUsersResponse(
    @SerializedName("members" ) var members : ArrayList<MentionableUser> = arrayListOf()
){
    data class MentionableUser(
        @SerializedName("id"       ) override val id        : Int,
        @SerializedName("name"     ) override val name      : String,
        @SerializedName("username" ) override val username  : String,
        @SerializedName("thumbnail") override val thumbnail : String?  = null,
        @SerializedName("nickname") val nickName : String?  = null
    ): AbstractUser() {
        override val membership: UserType
            get() = UserType.PREMIUM

        override val verified: <PERSON>olean
            get() = false

        override val citizenship: UserCitizenship
            get() = UserCitizenship.default()

        val userNickNameOrName: String
            get() {
                nickName?.let {
                    if(it.isNotEmpty()) {
                        return it
                    }
                }
                return name
            }
    }
}