package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.SocialAffairAPIService
import com.app.messej.data.model.api.socialAffairs.FormerMSAResponse
import com.app.messej.data.model.api.socialAffairs.FormerMSAResponse.Companion.testData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SocialFormerMSADataSource(private val apiService: SocialAffairAPIService) : PagingSource<Int, FormerMSAResponse.FormerMSA>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, FormerMSAResponse.FormerMSA>): Int? {
        return state.anchorPosition
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FormerMSAResponse.FormerMSA> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
//                val response = apiService.getCommitteeMembers(page = currentPage)
//                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))
                val data = testData
                val nextKey = if (data.haveNextPage == true) currentPage + 1 else null
                LoadResult.Page(
                    data = data.members ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}