package com.app.messej.data.model

import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.socket.AbstractChatMessagePayload
import com.google.gson.annotations.SerializedName

data class FlashCommentPayload(
    @SerializedName("user_id") val userId: Int?,
    @SerializedName("comment") override val message: String?,
) : AbstractChatMessagePayload() {
    override fun setMedia(meta: MediaMeta) {
        super.setMedia(meta)
        media = meta.thumbnail
    }

    override val color: ChatTextColor?
        get() = null
}