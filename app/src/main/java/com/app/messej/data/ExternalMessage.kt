package com.app.messej.data

import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.ActivityMeta
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.MediaType

data class ExternalMessage(
    override val messageId: String = " ",
    override val roomId: String? = null,
    override var rawMessage: String?,
    override val createdTime: String = " ",
    override val deliveredTime: String? = null,
    override val sentTime: String? = null,
    override val forwardId: String? = null,
    override val deleted: Boolean = false,
    override val isActivity: Boolean = false,
    override val activityMeta: ActivityMeta? = null,
    override var media: String? = null,
    override var mediaMeta: MediaMeta? = null,
    override var internalMessageType: MessageType? = null,
    override val read: String? = null,
    override val receiver: Int? = null,
    override val replyTo: ReplyTo? = null,
    override val sender: Int = 0,
    override val liked: Boolean = false,
    override val reported: Boolean = false,
    override val chatTextColor: ChatTextColor? = null,
    val externalMediaType: MediaType? = null,
) : AbstractChatMessage()