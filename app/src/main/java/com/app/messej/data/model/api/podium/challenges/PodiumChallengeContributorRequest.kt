package com.app.messej.data.model.api.podium.challenges

import com.app.messej.data.model.enums.ChallengeContributionType
import com.google.gson.annotations.SerializedName

data class PodiumChallengeContributorRequest(
    @SerializedName("contributor_type" ) val contributorType : ChallengeContributionType,
    @SerializedName("contributors"     ) val contributors    : ArrayList<Contributor>? = null
) {
    data class Contributor (
        @SerializedName("contributor_fee" ) val contributorFee : Long,
        @SerializedName("contributor_id"  ) val contributorId  : Int
    )
}
