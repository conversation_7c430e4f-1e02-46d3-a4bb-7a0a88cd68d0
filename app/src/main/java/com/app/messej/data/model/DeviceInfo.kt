package com.app.messej.data.model

import com.app.messej.data.Constants.PLATFORM_OS
import com.google.gson.annotations.SerializedName

open class DeviceInfo{

    @SerializedName("device_type") private val deviceType: String = PLATFORM_OS
    @SerializedName("device_name") var deviceName: String? = null
    @SerializedName("device_model") var deviceModel: String? = null
    @SerializedName("device_uid") var deviceUID: String? = null
    @SerializedName("city") var city: String? = null
    @SerializedName("region") var region: String? = null
    @SerializedName("country") var country: String? = null
    @SerializedName("user_ip") var userIP: String? = null

    fun copyDeviceInfoFrom(obj: DeviceInfo) {
        city = obj.city
        region = obj.region
        country = obj.country
        userIP = obj.userIP
        deviceName = obj.deviceName
        deviceModel = obj.deviceModel
        deviceUID = obj.deviceUID
    }
}
