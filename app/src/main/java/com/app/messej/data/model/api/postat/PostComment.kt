package com.app.messej.data.model.api.postat

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractComment
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.api.postat.PostComment.Companion.COLUMN_MESSAGE_ID
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.room.EntityDescriptions
import com.google.gson.annotations.SerializedName

@Entity(
    tableName = EntityDescriptions.TABLE_POSTAT_COMMENTS,
    indices = [
        Index(COLUMN_MESSAGE_ID, unique = false),
    ]
)
@TypeConverters(
    SenderDetails.Converter::class,
    MediaMeta.Converter::class // Keep if MediaMeta is sometimes present
)
data class PostComment(
    @SerializedName("id", alternate = ["comment_id"])
    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = COLUMN_COMMENT_ID)
    override val commentId: String,

    @SerializedName("huddle_id")
    @ColumnInfo(name = COLUMN_HUDDLE_ID)
    override val huddleId: Int = 0, // Made nullable as it's not in the sample

    @SerializedName("content_id", alternate = ["flash_id"])
    @ColumnInfo(name = COLUMN_MESSAGE_ID)
    override val messageId: String,

    @SerializedName("created", alternate = ["time_created"])
    @ColumnInfo(name = COLUMN_CREATED)
    override val created: String? = null,

    @SerializedName("message", alternate = ["comment"])
    @ColumnInfo(name = COLUMN_MESSAGE)
    override val comment: String? = null,

    @SerializedName("is_reported")
    @ColumnInfo(name = COLUMN_IS_REPORTED)
    val isReported: Boolean? = null,

    @SerializedName("sender")
    @ColumnInfo(name = "senderId")
    override val senderId: Int,

    @SerializedName("sender_details")
    @ColumnInfo(name = "senderDetails")
    override val senderDetails: SenderDetails? = null, // Ensure SenderDetails is correctly defined

    @SerializedName("media")
    @ColumnInfo(name = "media")
    override val media: String? = null,


    @SerializedName("total_replies")
    @ColumnInfo(name = "totalReplies")
    val totalReplies: Int? = null,

    @SerializedName("total_like_count", alternate = ["total_likes"])
    @ColumnInfo(name = "totalLikeCount")
    val totalLikeCount: Int? = null,

    @SerializedName("is_delete")
    @ColumnInfo(name = COLUMN_IS_DELETE)
    val isDelete: Boolean? = null,

    @SerializedName("reports_count")
    @ColumnInfo(name = COLUMN_REPORTS_COUNT)
    val reportsCount: Int? = null,

    @SerializedName("time_updated")
    @ColumnInfo(name = COLUMN_TIME_UPDATED)
    val timeUpdated: String? = null,

    @SerializedName("user_id") // This might be redundant if 'sender' always holds the same value
    @ColumnInfo(name = COLUMN_USER_ID)
    val userId: Int? = null,


    @ColumnInfo(name = COLUMN_POSTAT_ID)
    val postatId: String = "",

    @SerializedName("type")
    @ColumnInfo(name = COLUMN_TYPE, defaultValue = "POSTAT")
    val type: CommentType = CommentType.POSTAT,

    @SerializedName("content_owner")
    @ColumnInfo(name = COLUMN_CONTENT_OWNER)
    val contentOwnerId:Int?=null

) : AbstractComment() {

    companion object {
        const val COLUMN_MESSAGE = "message"
        const val COLUMN_MESSAGE_ID = "messageId"
        const val COLUMN_COMMENT_ID = "commentId"
        const val COLUMN_HUDDLE_ID = "huddleId"
        const val COLUMN_CREATED = "created"
        const val COLUMN_POSTAT_ID = "postatId"
        const val COLUMN_IS_DELETE = "isDelete"
        const val COLUMN_REPORTS_COUNT = "reportsCount"
        const val COLUMN_TIME_UPDATED = "timeUpdated"
        const val COLUMN_USER_ID = "userId"
        const val COLUMN_IS_REPORTED = "isReported"
        const val COLUMN_TYPE = "type"

        const val COLUMN_CONTENT_OWNER ="contentOwner"
    }

    val hasReplies: Boolean
        get() = (totalReplies ?: 0) > 0

    fun sanitize() {
        senderDetails?.id = senderId
    }
}
