package com.app.messej.data.utils

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import com.app.messej.data.api.external.ExternalServiceGenerator
import com.app.messej.data.model.DeviceInfo
import com.app.messej.data.repository.FirebaseRepository
import java.security.MessageDigest

object DeviceInfoUtil {

    private val IPAPI_FALLBACK_VALUE = "IPAPI Failed"

    suspend fun getAllDeviceInfo(firebaseRepo: FirebaseRepository): DeviceInfo {
        val deviceInfo = DeviceInfo()
        deviceInfo.deviceName = Build.MANUFACTURER
        deviceInfo.deviceModel = Build.MODEL
        deviceInfo.deviceUID = firebaseRepo.getFirebaseInstallationID()

        val response = ExternalServiceGenerator.createIPApiService().getIpApiData()
        if (response.isSuccessful) {
            val ipApiResponse = response.body()!!

            deviceInfo.userIP = ipApiResponse.ip
            deviceInfo.city = ipApiResponse.city
            deviceInfo.country = ipApiResponse.country
            deviceInfo.region = ipApiResponse.region
        } else {
            deviceInfo.userIP = IPAPI_FALLBACK_VALUE
            deviceInfo.city = IPAPI_FALLBACK_VALUE
            deviceInfo.country = IPAPI_FALLBACK_VALUE
            deviceInfo.region = IPAPI_FALLBACK_VALUE
//            throw Exception("Error getting IPAPI info")
        }
        return deviceInfo
    }

    fun getSHA1Signature(context: Context): String? {
        return try {

            val packageManager = context.packageManager
            val packageName = context.packageName

            val signatures = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val packageInfo = packageManager.getPackageInfo(
                    packageName,
                    PackageManager.GET_SIGNING_CERTIFICATES
                )
                val signingInfo = packageInfo.signingInfo?: return null
                if (signingInfo.hasMultipleSigners()) {
                    signingInfo.apkContentsSigners
                } else {
                    signingInfo.signingCertificateHistory
                }
            } else {
                @Suppress("DEPRECATION")
                val packageInfo = packageManager.getPackageInfo(
                    packageName,
                    PackageManager.GET_SIGNATURES
                )
                packageInfo.signatures?: return null
            }

            val md = MessageDigest.getInstance("SHA1")
            md.update(signatures[0].toByteArray())
            val digest = md.digest()
            digest.joinToString("") { "%02X".format(it) }
        } catch (e: Exception) {
            Log.e("Signature", "Error getting SHA1", e)
            null
        }
    }
}