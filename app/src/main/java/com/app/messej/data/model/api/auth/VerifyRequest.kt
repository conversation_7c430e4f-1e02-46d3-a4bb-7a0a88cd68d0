package com.app.messej.data.model.api.auth

import com.app.messej.data.Constants.PLATFORM_OS
import com.google.gson.annotations.SerializedName

data class VerifyRequest(
    @SerializedName("phone") val phone: String?=null,
    @SerializedName("country_code") val countryCode: String?=null,
    @SerializedName("device") val device: String = PLATFORM_OS,
    @SerializedName("referral_code") val referralCode: String? = null,
    @SerializedName("resend") val resend: Boolean = false,
    @SerializedName("identity") val identity: String?=null,
    @SerializedName("email") val email: String?=null
)
