package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.SocialAffairUpgradeSupportStatus
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.socialAffairs.AskedQuestionsResponse
import com.app.messej.data.model.api.socialAffairs.CommitteeMembersResponse
import com.app.messej.data.model.api.socialAffairs.HonoursResponse
import com.app.messej.data.model.api.socialAffairs.MySupportListResponse
import com.app.messej.data.model.api.socialAffairs.PersonalSupportRequest
import com.app.messej.data.model.api.socialAffairs.SocialApproveCaseRequest
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfoResponse
import com.app.messej.data.model.api.socialAffairs.SocialCasesListResponse
import com.app.messej.data.model.api.socialAffairs.SocialDonateRequest
import com.app.messej.data.model.api.socialAffairs.SocialQuestionRequest
import com.app.messej.data.model.api.socialAffairs.SocialUpgradeSupportEligibility
import com.app.messej.data.model.api.socialAffairs.SocialVoteRequest
import com.app.messej.data.model.api.socialAffairs.VotersResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface SocialAffairAPIService {

    @GET("/user/social-affairs/upgrade-support")
    @Headers("Accept: application/json")
    suspend fun getUpgradeSupportStatus(): Response<APIResponse<SocialAffairUpgradeSupportStatus>>

    @GET("/user/social-affairs/committee-members")
    @Headers("Accept: application/json")
    suspend fun getCommitteeMembers(
        @Query(value = "page") page: Int? = null
    ): Response<APIResponse<CommitteeMembersResponse>>

    @GET("/user/social-affairs/honours")
    @Headers("Accept: application/json")
    suspend fun getHonoursList(
        @Query(value = "page") page: Int? = null
    ): Response<APIResponse<HonoursResponse>>

    @GET("/user/social-affairs/voters/{id}")
    @Headers("Accept: application/json")
    suspend fun getVotersList(
        @Path(value = "id") id: Int? = null,
        @Query(value = "page") page: Int? = null
    ): Response<APIResponse<VotersResponse>>

    @GET("/user/social-affairs/question-answers")
    @Headers("Accept: application/json")
    suspend fun getQuestionsAndAnswers(
        @Query(value = "support_request_id") caseId: Int,
        @Query(value = "page") page: Int? = null
    ): Response<APIResponse<AskedQuestionsResponse>>

    @POST("/user/social-affairs/questions")
    @Headers("Accept: application/json")
    suspend fun submitQuestion(
        @Body request: SocialQuestionRequest,
    ): Response<APIResponse<String>>

    @PUT("/user/social-affairs/questions/{question_id}")
    @Headers("Accept: application/json")
    suspend fun editQuestion(
        @Path("question_id") questionId: Int?,
        @Body request: SocialQuestionRequest
    ): Response<APIResponse<String>>

    @DELETE("/user/social-affairs/questions/{question_id}")
    @Headers("Accept: application/json")
    suspend fun deleteQuestion(
        @Path("question_id") questionId: Int?
    ): Response<APIResponse<String>>

    @POST("/user/social-affairs/questions/{question_id}/answer")
    @Headers("Accept: application/json")
    suspend fun submitAnswer(
        @Body request: SocialQuestionRequest,
        @Path("question_id") questionId: Int?
    ): Response<APIResponse<String>>

    @POST("/user/social-affairs/pay-fine")
    @Headers("Accept: application/json")
    suspend fun payFine(): Response<APIResponse<String>>

    @DELETE("/user/social-affairs/questions/{question_id}/answer")
    @Headers("Accept: application/json")
    suspend fun deleteAnswer(
        @Path("question_id") questionId: Int?
    ): Response<APIResponse<String>>

    @GET("/user/social-affairs")
    @Headers("Accept: application/json")
    suspend fun getSocialCases(
        @Query(value = "page") page: Int? = null,
        @Query(value = "main_tab") mainTab: String? = null,
        @Query(value = "tab") tab: String? = null,
        @Query(value = "sub_tab") subTab: String? = null
    ): Response<APIResponse<SocialCasesListResponse>>

    @GET("/user/social-affairs")
    @Headers("Accept: application/json")
    suspend fun getCasesCount(
        @Query(value = "count_only") isCountOnly: Boolean = true
    ): Response<APIResponse<SocialCasesListResponse>>

    @POST("/user/social-affairs/support-request")
    @Headers("Accept: application/json")
    suspend fun requestSupport(
        @Query(value = "personal_support") isPersonalSupport: Boolean? = null,
        @Query(value = "upgrade_support") isUpgradeSupport: Boolean? = null
    ): Response<APIResponse<SocialUpgradeSupportEligibility>>

    @POST("/user/social-affairs/approve")
    @Headers("Accept: application/json")
    suspend fun approveCase(
        @Body request: SocialApproveCaseRequest,
    ): Response<APIResponse<Unit>>

    @GET("/user/social-proof-files/secrets")
    @Headers("Accept: application/json")
    suspend fun getUploadCredentials(): Response<APIResponse<UploadCredentialsResponse>>

    @GET("/user/social-affairs/active-upgrade-case")
    @Headers("Accept: application/json")
    suspend fun getRequestedForUpgrade(): Response<APIResponse<SocialUpgradeSupportEligibility>>

    @POST("/user/social-affairs/vote")
    @Headers("Accept: application/json")
    suspend fun vote(
        @Body request: SocialVoteRequest
    ): Response<APIResponse<String>>

    @POST("/user/social-affairs/donate")
    @Headers("Accept: application/json")
    suspend fun donateCoins(
        @Body request: SocialDonateRequest
    ): Response<APIResponse<String>>

    @POST("/user/social-affairs/support-request")
    @Headers("Accept: application/json")
    suspend fun requestSupport(
        @Body request: PersonalSupportRequest,
        @Query(value = "personal_support") isPersonalSupport: Boolean? = null,
        @Query(value = "upgrade_support") isUpgradeSupport: Boolean? = null
    ): Response<APIResponse<Unit>>

    @PUT("/user/social-affairs/support-request/{id}")
    @Headers("Accept: application/json")
    suspend fun editPersonalSupportCase(
        @Path("id") id: String,
        @Body request: PersonalSupportRequest
    ): Response<APIResponse<String>>

    @GET("/user/social-affairs/my-support-requests")
    @Headers("Accept: application/json")
    suspend fun getMySupportRequests(
        @Query(value = "page") page: Int? = null,
        @Query(value = "drafts") isDraft: Boolean = false
    ): Response<APIResponse<MySupportListResponse>>

    @GET("/user/social-affairs/case-info/{id}")
    @Headers("Accept: application/json")
    suspend fun getCaseInfo(
        @Path(value = "id") id: String,
        @Query(value = "include_committee_members") includeCommitteeMember: Boolean? = null,
        @Query(value = "include_voters_count") includeVotersCount: Boolean? = null,
        @Query(value = "include_questions_count") includeQuestionsCount: Boolean? = null,
        @Query(value = "include_resident_status") includeResidentStatus: Boolean? = null
    ): Response<APIResponse<SocialCaseInfoResponse>>
}