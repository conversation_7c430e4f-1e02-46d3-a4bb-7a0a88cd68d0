package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.ChallengeType
import com.google.gson.annotations.SerializedName

data class YallaCompetitorRequestPayload(
    @SerializedName("podium_id") val podiumId: String,
    @SerializedName("challenge") val challenge: Challenge,
    @SerializedName("player") val player: Player
) {
    data class Challenge(
        @SerializedName("id") val challengeId: String,
        @SerializedName("game_type") val gameType: ChallengeType,
        @SerializedName("prize") val prize: Int
    )

    data class Player(
        @SerializedName("user_id") val userId: Int,
        @SerializedName("name") val name: String
    )
}
