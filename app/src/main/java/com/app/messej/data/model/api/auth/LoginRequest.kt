package com.app.messej.data.model.api.auth

import com.app.messej.data.model.DeviceInfo
import com.google.gson.annotations.SerializedName

data class LoginRequest(
    @SerializedName("email") val email: String? = null,
    @SerializedName("phone") val phone: String? = null,
    @SerializedName("country_code") val countryCode: String? = null,
    @SerializedName("password") val password: String,
): DeviceInfo()
