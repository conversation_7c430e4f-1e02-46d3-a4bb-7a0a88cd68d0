package com.app.messej.data.model

import com.app.messej.data.model.enums.UserCitizenship
import kotlin.math.roundToInt

interface UserRatingProvider {
    val userRating: Double?
    val citizenship : UserCitizenship?

    val inactive: Boolean
        get() {
            val rating = userRating
            return if (citizenship?.isFreeType==true) rating!=null && rating<0.8
            else false
        }

    val userRatingPercent: String
        get() = ((userRating?:0.0)*100).roundToInt().toString()
}