package com.app.messej.data.model

import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.google.gson.annotations.SerializedName

data class Star (
    @SerializedName("id"         ) override val id         : Int,
    @SerializedName("name"       ) override val name       : String     = "",
    @SerializedName("thumbnail"  ) override val thumbnail  : String?    = null,
    @SerializedName("username"   ) override val username   : String     = "",

    @SerializedName("stars"      ) override val stars      : Int        = 0,
    @SerializedName("likers"     ) override val likers     : Int        = 0,
    @SerializedName("dears"      ) override val dears      : Int        = 0,
    @SerializedName("fans"       ) override val fans       : Int        = 0,

    @SerializedName("membership" ) override val membership : UserType,
    @SerializedName("verified"   ) override val verified   : <PERSON><PERSON><PERSON>,
    @SerializedName("blocked"    )          val blocked    : Boolean
): AbstractUserWithStats() {

    override val citizenship: UserCitizenship
        get() = UserCitizenship.default()
}