package com.app.messej.data.model.api.legal

import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.CaseVerdictAction
import com.app.messej.data.model.enums.ReportCaseStatus
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

abstract class AbstractCaseDetails {
    abstract val created: String?
    abstract val updated: String?

    abstract val id: Int

    abstract val reason: String
    abstract val reportedId: String
    abstract val reportedContentType: ReportContentType
    abstract val reportType: ReportType
    abstract val reporterId: Int
    abstract val userId: Int
//    abstract val contentOwnerName: String
//    abstract val countryCodeIso: String?

    abstract val categoryIcon: String?
    abstract val category: String
    abstract val categoryId: Int
    abstract val categoryTypeId: Int?

    abstract val caseStatus: ReportCaseStatus

    abstract val applicableFee: Double?

    abstract val appealEnabledAt: String?
    abstract val advocateFee: Int?

    abstract val guilty: Boolean?
    abstract val actionTaken: CaseVerdictAction?

    abstract val userDetails: DefendantUser?

    enum class VoteResult {
        GUILTY, NOT_GUILTY
    }

    companion object {
        const val MAX_VOTES_IB = 10
        const val MAX_VOTES_JURY = 5
    }

    val userReport: Boolean
        get() = reportedContentType == ReportContentType.USER

    fun userPrivate(id:Int) : Boolean = caseStatus.ordinal < ReportCaseStatus.ADVOCATES_UNION.ordinal && userId != id && reporterId != id

    val parsedCreatedDate: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(created)

    val formattedCreatedDate: String
        get() = DateTimeUtils.format(parsedCreatedDate, format = DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)

    val parsedUpdatedDate: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(updated)

    val formattedUpdatedDate: String
        get() = DateTimeUtils.format(parsedUpdatedDate, format = DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)


    val verdict: VoteResult?
        get() = if (caseStatus.ordinal < ReportCaseStatus.VERDICT.ordinal) null
        else if (guilty == true) VoteResult.GUILTY
        else VoteResult.NOT_GUILTY

    fun hasFineFor(id: Int): Boolean {
        return if (caseStatus!=ReportCaseStatus.VERDICT) false
        else if(id==userId) actionTaken?.hasDefendantFine==true
        else if(id==reporterId) actionTaken?.hasPlaintiffFine==true
        else false
    }

    data class DefendantUser(
        @SerializedName("name") override val name: String,
        @SerializedName("thumbnail_url") override val thumbnail: String? = null,
        @SerializedName("premium") val premium: Boolean? = null,
        @SerializedName("citizenship") override val citizenship: UserCitizenship? = null,
        @SerializedName("country_code_iso") override val countryCode: String? = null
    ): AbstractUser() {
        override val id: Int
            get() = 0
        override val username: String
            get() = ""
        override val membership: UserType
            get() = if(premium == true) UserType.PREMIUM else UserType.FREE
        override val verified: Boolean
            get() = false
    }
}