package com.app.messej.data.model.entity

import androidx.room.Embedded
import androidx.room.Relation
import com.app.messej.data.model.MediaUploadState

data class PostatWithMedia(
    @Embedded
    val postat: Postat,
    @Relation(
        parentColumn = Postat.COLUMN_ID,
        entityColumn = LocalPostatMedia.COLUMN_POSTAT_ID
    )
    val medias: List<LocalPostatMedia>
) {
    val needsMediaUpload: Boolean
        get() {
            return medias.any{
                it.uploadState is MediaUploadState.Pending
            } && !mediaIsUploading
        }

    val mediaIsUploading: Boolean
        get() {
            return medias.any {
                it.uploadState is MediaUploadState.Uploading
            }
        }
}
