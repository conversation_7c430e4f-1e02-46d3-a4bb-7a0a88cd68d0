package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class KnowledgeRaceAnswerPayload(
    @SerializedName("challenge_id") val challengeId: String,
    @SerializedName("podium_id") val podiumId: String?,

    @SerializedName("question_id") val questionId: Int,
    @SerializedName("answer_id") val answerId: Int,
    @SerializedName("user_id") val userId: Int

    ) : SocketEventPayload()