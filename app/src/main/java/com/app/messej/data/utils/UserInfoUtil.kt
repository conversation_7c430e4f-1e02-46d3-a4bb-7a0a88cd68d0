package com.app.messej.data.utils

import android.util.Log
import android.util.Patterns
import com.app.messej.MainApplication
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.MentionedUser
import com.app.messej.ui.utils.MentionTokenizer
import com.google.gson.Gson

object UserInfoUtil {
    fun addPlusToCountryCode(code: String): String {
        if (code.isEmpty()) return code
        return if (code.startsWith("+")) code else "+$code"
    }

    fun getCountryCode(code: String): String {
            val  parts = code.split("-")
            val countryCode = if (parts.size > 1) parts[0] else null
            return countryCode.toString().removePrefix("+")
    }

    fun extractPhoneNumber(string: String): String {
        val parts = string.split("-")
        return parts[1]
    }


    fun removePlusFromCountryCode(code: String?): String? {
        if (code.isNullOrEmpty()) return null
        val result = sanitizePhoneNumber(code).removePrefix("+")
        return if (result.isNullOrBlank()) null else result
    }

    fun sanitizePhoneNumber(number: String): String {
        return number.filter { it.isDigit() || it == '+' }
    }

    fun combineCountryCodeAndMobileNumber(code: String, number: String): String {
        return "${addPlusToCountryCode(code)} $number"
    }

    fun isEmailValid(emailAddress: String): Boolean {
        return Patterns.EMAIL_ADDRESS.matcher(emailAddress).matches()
    }


    fun String.containsSpecialCharacters(): Boolean {
        val regex = Regex("[^a-zA-Z0-9 .@]")
        return regex.containsMatchIn(this)
    }


    fun removeLeadingZeroes(str: String?): String? {
        return str?.replace("^0+(?!$)".toRegex(), "")
    }

    val mentionDecodedRegex = Regex("${MentionTokenizer.TOKEN_START_CHAR}([\\w\\.]+)")
    val mentionEncodedRegex = Regex("#mention-(\\d+?)#")

    fun encodeMentions(message: String, users: List<AbstractUser>): String {
        Log.w("UIU", "encodeMentions: $message with ${Gson().toJson(users)}", )
        val res = mentionDecodedRegex.replace(message) { result ->

            val username = result.groupValues[1]
            val user = users.find { it.username == username } ?: return@replace result.value
            return@replace "#mention-${user.id}#"
        }
        Log.w("UIU", "encodeMentions: got $res", )
        return res
    }

    fun hasMentions(message: String) = mentionEncodedRegex.containsMatchIn(message)

    fun <T:AbstractUser, V:CharSequence>decodeMentions(message: V, users: List<T>, transform: (T,String) -> String = { _, token -> token }) = mentionEncodedRegex.replace(message) { result ->
        val userId = result.groupValues[1].toIntOrNull()
        val user = users.find { it.id == userId } ?: return@replace result.value
        return@replace if(user is MentionedUser && user.isDeleted) {
             transform.invoke(user,"${MentionTokenizer.TOKEN_START_CHAR}${MainApplication.applicationContext().getString(R.string.chat_deleted_user)}")
        } else {
            transform.invoke(user,"${MentionTokenizer.TOKEN_START_CHAR}${user.username}")
        }
    }

}
