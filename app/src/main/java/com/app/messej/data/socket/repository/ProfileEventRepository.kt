package com.app.messej.data.socket.repository

import com.app.messej.MainApplication
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.profile.UserEnforcements
import com.app.messej.data.model.enums.BlockUnblockAction
import com.app.messej.data.model.enums.Functionality
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.model.socket.CitizenshipViewedPayload
import com.app.messej.data.model.socket.CountryFlagPayload
import com.app.messej.data.model.socket.ETribeSuperStarMessagePayload
import com.app.messej.data.model.socket.LevelUpgradationEvent
import com.app.messej.data.model.socket.PendingFinePayload
import com.app.messej.data.model.socket.ProfileUpdatePayload
import com.app.messej.data.model.socket.SubscriptionPayload
import com.app.messej.data.model.socket.UserBlockUpdate
import com.app.messej.data.model.socket.UserLogoutEvent
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object ProfileEventRepository: BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {

    private val accountRepo = AccountRepository(MainApplication.applicationInstance())

    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when(event) {
            ChatSocketEvent.RX_PROFILE_UPDATE -> onProfileUpdate(data)
            ChatSocketEvent.RX_BLOCK_ACCOUNT -> onAdminBlockUser(data)
            ChatSocketEvent.RX_USER_EMPOWERMENT_UPDATE ->onUserEmpowermentUpdate(data)
            ChatSocketEvent.RX_COUNTRY_FLAG_UPDATE -> onUserCountryFlagUpdate(data)
            ChatSocketEvent.RX_SUBSCRIPTION_UPDATE -> onSubscriptionUpdate(data)
            ChatSocketEvent.RX_FLASH_USER_BLOCKED -> onFlashUserBlocked(data)
            //RX_USER_LEVEL_UPGRADE
            ChatSocketEvent.RX_USER_LEVEL_UPGRADE -> onUserLevelUpgrade(data)
            ChatSocketEvent.RX_TX_CITIZENSHIP_LEVEL_UPGRADE -> onCitizenshipLevelUpgrade(data)
            ChatSocketEvent.RX_USER_ENFORCEMENTS -> onUserEnforcements(data)
            ChatSocketEvent.PENDING_FINE_UPDATES -> { onPendingFineUpdate(data) }
            ChatSocketEvent.E_TRIBE_SUPER_STAR_MESSAGE -> { onSuperstarMessageEvent(data) }
            ChatSocketEvent.ADMIN_ADDED_FLIX_COINS  -> {onAdminAddedFlixCoins()}
            else -> return false
        }
        return true
    }

    private fun onUserCountryFlagUpdate(data: JSONObject) = runBlocking {
        val _data = Gson().fromJson<CountryFlagPayload>(data.toString())

        withContext(Dispatchers.IO) {
            val accountDetails = accountRepo.user
            if (accountDetails.id == _data.userId) {
                accountRepo.updateUser(
                    accountDetails.copy(
                        profile = accountDetails.profile.copy(
                            allowChangeLocation = _data.allow_change_location, allowHideCountryFlag = _data.allow_hide_country_flag
                        )
                    )
                )
            }
        }
    }


    private fun onAdminBlockUser(data: JSONObject) = runBlocking {
        val eventData = Gson().fromJson<UserLogoutEvent>(data.toString())
        val adminBlockedUserId = eventData.userId
        val currentUserId = accountRepo.user.id
        if (adminBlockedUserId == currentUserId) withContext(Dispatchers.IO) {
            AuthenticationRepository(MainApplication.applicationInstance()).clearLoginSessionAndData()
        }
    }

    private fun onFlashUserBlocked(data: JSONObject) = runBlocking {
        val _data = Gson().fromJson<UserBlockUpdate>(data.toString())
        withContext(Dispatchers.IO) {
            val accountDetails = accountRepo.user
            val userFunctionalityBlock = accountRepo.user.userFunctionalityBlocks
            when(_data.functionality) {
                Functionality.FLASH -> accountRepo.updateUser(
                    accountDetails.copy(
                        isFlashBlocked = _data.action == BlockUnblockAction.BLOCK
                    )
                )
                Functionality.POSTAT_POST -> {
                    accountRepo.updateUser(
                        accountDetails.copy(
                            userFunctionalityBlocks = userFunctionalityBlock?.copy(
                                postatPostsBlock = _data.action == BlockUnblockAction.BLOCK
                            )
                        )
                    )
                }
                else -> {}
            }
        }
    }

    private val _levelUpgradationEvent: MutableSharedFlow<LevelUpgradationEvent> = MutableSharedFlow()
    val levelUpgradationEvent: SharedFlow<LevelUpgradationEvent> = _levelUpgradationEvent

    private fun onUserLevelUpgrade(data: JSONObject) = runBlocking {
        val _data = Gson().fromJson<LevelUpgradationEvent>(data.toString())
        val user = accountRepo.user
        if (user.id == _data.userId) {
            _data?.citizenship?.let {
                accountRepo.updateUser(user.copy(citizenship = it))
            }
        }
        _levelUpgradationEvent.emit(_data)
    }

    private val _citizenshipLevelUpgradeEvent: MutableSharedFlow<LevelUpgradationEvent> = MutableSharedFlow()
    val citizenshipLevelUpgradeEvent: SharedFlow<LevelUpgradationEvent> = _citizenshipLevelUpgradeEvent

    private fun onCitizenshipLevelUpgrade(data: JSONObject)= runBlocking {
        val _data = Gson().fromJson<LevelUpgradationEvent>(data.toString())
        val user = accountRepo.user
        if (user.id == _data.userId) {
            _data?.citizenship?.let { accountRepo.updateUser(user.copy(citizenship = it)) }
        }
        _citizenshipLevelUpgradeEvent.emit(_data)
    }

    private fun onProfileUpdate(data: JSONObject) = runBlocking {
        val msg = Gson().fromJson<ProfileUpdatePayload>(data.toString())
//        val accountRepo = AccountRepository(MainApplication.applicationInstance())
        //TODO handle this data if required
    }

    private fun onUserEmpowermentUpdate(data: JSONObject) = runBlocking {
        val msg  = Gson().fromJson<CurrentUser.UserEmpowerment>(data.toString())
        val user = accountRepo.user
        accountRepo.updateUser(
            user.copy(
                userEmpowerment = msg
            )
        )
    }

    private fun onSubscriptionUpdate(data: JSONObject) =runBlocking {
        val _data = Gson().fromJson<SubscriptionPayload>(data.toString())
        withContext(Dispatchers.IO) {
            val accountDetails = accountRepo.getAccountDetailsFlow().firstOrNull()
            if (accountRepo.user.id == _data.userId) {
                accountDetails?.isPremium = if(_data.membership == UserType.PREMIUM) true else false
                accountDetails?.let { details ->
                    accountRepo.saveAccountDetails(details)
                }
            }
        }
    }

    private fun onUserEnforcements(data: JSONObject) =runBlocking {
        val enf = Gson().fromJson<UserEnforcements>(data.toString())
        withContext(Dispatchers.IO) {
            val accountDetails = accountRepo.getAccountDetailsFlow().firstOrNull()
            if (accountRepo.user.id == enf.userId) {
                accountDetails?.enforcements = enf
                accountDetails?.let { accountRepo.saveAccountDetails(accountDetails) }
            }
        }
    }

    private fun onPendingFineUpdate(data: JSONObject) =runBlocking {
        val finePayload = Gson().fromJson<PendingFinePayload>(data.toString())
        val dataStore = FlashatDatastore()
        withContext(Dispatchers.IO) {
            val unreadItemsCount = dataStore.getUnreadCountsFlow().firstOrNull()
            if (accountRepo.user.id == finePayload.userId) {
                val updatedCount = unreadItemsCount?.copy(hasPendingFines = finePayload.hasPendingFines)
                updatedCount?.let { dataStore.saveUnreadCounts(it) }
            }
        }
    }

    private val _eTribeSuperstarMessageEvent: MutableSharedFlow<ETribeSuperStarMessagePayload> = MutableSharedFlow()
    val eTribeSuperstarMessageEvent: SharedFlow<ETribeSuperStarMessagePayload> = _eTribeSuperstarMessageEvent

    private fun onSuperstarMessageEvent(data: JSONObject)= runBlocking {
        val _data = Gson().fromJson<ETribeSuperStarMessagePayload>(data.toString())
        _eTribeSuperstarMessageEvent.emit(_data)
    }

    fun sendCitizenshipViewedAcknowledgement(userId: Int,otherUserId: Int)  {
        ChatSocketRepository.sendEvent(ChatSocketEvent.CITIZENSHIP_VIEW_ACKNOWLEDGEMENT, CitizenshipViewedPayload(userId,otherUserId))
    }

    private val _adminAddedFlixCoinEvent: MutableSharedFlow<Boolean> = MutableSharedFlow()
    val adminAddedFlixCoinEvent: SharedFlow<Boolean> = _adminAddedFlixCoinEvent

    private   fun onAdminAddedFlixCoins() = runBlocking {
        _adminAddedFlixCoinEvent.emit(true)
    }

}