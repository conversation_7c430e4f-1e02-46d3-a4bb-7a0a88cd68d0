package com.app.messej.data.model

import android.net.Uri
import androidx.media3.common.MediaItem
import com.app.messej.data.model.api.media.S3UploadMedia
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.MediaUtils.FileExtension
import com.github.f4b6a3.uuid.UuidCreator
import java.io.File

data class ReportProofMedia(
    val uri: Uri,
    val name: String,
//    val size: Long,
    override val mimeType: String,
//    val videoDurationMillis: Long? = null,
): AbstractUriMedia() {

    var uuid: String = UuidCreator.getTimeBased().toString()

    val uriString: String
        get() = uri.toString()

    override val thumbnail: String?
        get() = if (mediaType == MediaType.IMAGE) uriString else null

    val mediaItem: MediaItem
        get() {
            val inputMediaItem = MediaItem.Builder()
            inputMediaItem.setUri(uri)
            return inputMediaItem.build()
        }

    var processedFile: File? = null

    val isProcessed: Boolean
        get() = processedFile!=null

    val s3UploadMedia: S3UploadMedia
        get() {
            val ext = if(mediaType == MediaType.VIDEO) FileExtension.MP4.ext else FileExtension.JPEG.ext
            return S3UploadMedia(processedFile?: throw(Exception("File not yet processed")), APIUtil.getReportProofUploadKey("$uuid.$ext"))
        }

    var mediaUploaded: Boolean = false
}
