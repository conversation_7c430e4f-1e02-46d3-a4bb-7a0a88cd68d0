package com.app.messej.data.model.api.profile

import com.google.gson.annotations.SerializedName

data class RegisterPasswordRequest(
    @SerializedName("password"          ) val password          : String,
    @SerializedName("confirm_password"  ) val confirmPassword   : String,
    @SerializedName("phone"             ) val phone             : String?=null,
    @SerializedName("country_code"      ) val countryCode       : String?=null,
    @SerializedName("email"             ) val email             : String?=null,
)
