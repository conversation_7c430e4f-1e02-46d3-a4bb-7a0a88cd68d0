package com.app.messej.data.repository.pagingSources



import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.BusinessAPIService
import com.app.messej.data.model.api.business.PayoutHistory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1
class PayoutHistoryDataSource (private val api: BusinessAPIService) : PagingSource<Int, PayoutHistory>() {
    init {
        Log.d("Test","Success")
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PayoutHistory> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage =  params.key ?: STARTING_KEY
                val response = currentPage.let {
                    api.getPayOutHistory()
                }
                val responseData = mutableListOf<PayoutHistory>()
                val data = response.body()?.result?.payoutHistory ?: emptyList()
                responseData.addAll(data.toMutableList())
                val nextKey = if (response.body()?.result?.nextPage == false) null else currentPage.plus(1)
                LoadResult.Page(
                    data = response.body()?.result?.payoutHistory?: emptyList(), prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
    override fun getRefreshKey(state: PagingState<Int, PayoutHistory>): Int? {
        val anchorPosition = state.anchorPosition ?: return null
        val emoji = state.closestItemToPosition(anchorPosition) ?: return null
        return ensureValidKey(key = emoji.id!! - (state.config.pageSize / 2) )
    }
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)

}
