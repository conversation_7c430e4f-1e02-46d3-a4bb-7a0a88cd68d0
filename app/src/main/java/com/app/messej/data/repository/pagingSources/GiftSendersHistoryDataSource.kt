package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.GiftAPIService
import com.app.messej.data.model.api.gift.Sender

private const val STARTING_KEY = 1
class GiftSendersHistoryDataSource(private val api: GiftAPIService, val sendersId: Int, val sendersHistory: Boolean) : PagingSource<Int, Sender>() {


    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Sender> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getGiftSendersHistory(page = currentPage, limit = 50, sendersId = sendersId, senderHistory = sendersHistory)
            val responseData = mutableListOf<Sender>()
            val result = response.body()?.result
            val data = result?.senders ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (!response.body()?.result!!.nextPage) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.senders, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("TransactionResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, Sender>): Int? {
        return null
    }
}