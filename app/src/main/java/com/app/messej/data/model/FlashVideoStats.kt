package com.app.messej.data.model

import android.util.Log
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserCitizenship.Companion.orDefault
import com.app.messej.ui.utils.DataFormatHelper

data class FlashVideoStats (
    val flashId: String?,
): BaseObservable() {

    @get:Bindable
    var commentDisabled: Boolean = false
        set(value) {
            field = value
            notifyPropertyChanged(BR.commentDisabled)
        }

    @get:Bindable
    var likeCount: Int = 0
        set(value) {
            field = value
            notifyPropertyChanged(BR.likeCount)
            notifyPropertyChanged(BR.likeCountFormatted)
        }

    @get:Bindable
    val likeCountFormatted: String
        get() = DataFormatHelper.numberToK(likeCount)

    @get:Bindable
    var shareCount: Int = 0
        set(value) {
            field = value
            notifyPropertyChanged(BR.shareCount)
        }

    @get:Bindable
    var viewCount: Int = 0
        set(value) {
            field = value
            notifyPropertyChanged(BR.viewCount)
            notifyPropertyChanged(BR.viewCountFormatted)
        }

    @get:Bindable
    val viewCountFormatted: String
        get() = DataFormatHelper.numberToK(viewCount)

    @get:Bindable
    var commentCount: Int = 0
        set(value) {
            field = value
            notifyPropertyChanged(BR.commentCount)
            notifyPropertyChanged(BR.commentCountFormatted)
        }

    @get:Bindable
    val commentCountFormatted: String
        get() = DataFormatHelper.numberToK(commentCount)

    @get:Bindable
    var liked: Boolean = false
        set(value) {
            field = value
            notifyPropertyChanged(BR.liked)
        }

    @get:Bindable
    var reported: Boolean = false
        set(value) {
            field = value
            notifyPropertyChanged(BR.reported)
        }

    @get:Bindable
    var userBlockedFromFlash: Boolean = false
        set(value) {
            field = value
            notifyPropertyChanged(BR.userBlockedFromFlash)
        }

    @get:Bindable
    var shareLink: String? = null
        set(value) {
            field = value
            notifyPropertyChanged(BR.shareLink)
        }
    @get:Bindable
    var giftCount: Int = 0
        set(value) {
            field = value
            Log.d("BVSM","Inside FVS: $giftCountFormatted")
            notifyPropertyChanged(BR.giftCountFormatted)
            notifyPropertyChanged(BR.giftCount)
        }

    @get:Bindable
    val giftCountFormatted: String
        get() = DataFormatHelper.numberToK(giftCount)

    @get:Bindable
    var citizenship: UserCitizenship? = UserCitizenship.default()
        set(value) {
            field = value
            notifyPropertyChanged(BR.citizenship)
        }

    @get:Bindable
    var userLivePodium: Boolean = false
        set(value) {
            field = value
            notifyPropertyChanged(BR.userLivePodium)
        }

    @get:Bindable
    var userLivePodiumId: String? = null
        set(value) {
            field = value
            notifyPropertyChanged(BR.userLivePodiumId)
        }

    fun updateWith(flash: FlashVideo) {
        Log.d("BVSM1","Inside FVS Update: ${flash.caption} ${System.identityHashCode(this)}")
        commentDisabled = flash.commentDisabled
        likeCount = flash.likeCount
        shareCount = flash.shareCount
        viewCount = flash.viewCount
        commentCount = flash.commentCount
        giftCount = flash.giftCount
        liked = flash.isLiked
        reported = flash.isReported
        shareLink = flash.shareLink
        userBlockedFromFlash = flash.senderDetails?.blockedFromPostingFlash == true
        citizenship = flash.senderDetails?.citizenship.orDefault()
        Log.w("BVSM", "getDetailForItem: ${flash.senderDetails?.userLivePodium} | ${flash.senderDetails?.name}|${flash.senderDetails?.userLivePodiumId} | ${flash.caption} |${flash.senderDetails?.userLivePodium == true}")
        userLivePodium = flash.senderDetails?.userLivePodium == true
        userLivePodiumId = flash.senderDetails?.userLivePodiumId
    }

    companion object {

        fun from(flash: FlashVideo) = FlashVideoStats(flash.id).apply {
            Log.d("BVSM","Inside FVS data: $flash")
            updateWith(flash)
            Log.d("BVSM1","Inside FVS from: ${flash.caption} ${System.identityHashCode(this)}")
        }
    }
}
