package com.app.messej.data.model.api.podium.challenges

import com.google.gson.annotations.SerializedName

data class PodiumContributorResponse(
    @SerializedName("challenge_id"     ) val challengeId     : String,
    @SerializedName("challenge_screen" ) val challengeScreen : Int?                    = null,
    @SerializedName("challenge_time"   ) val challengeTime   : String?                 = null,
    @SerializedName("challenge_type"   ) val challengeType   : String,
    @SerializedName("contributor_type" ) val contributorType : String?                 = null,
    @SerializedName("contributors"     ) val contributors    : List<ChallengeParticipant> = listOf(),
    @SerializedName("facilitator"      ) val facilitator     : ChallengeParticipant
) {
    data class ChallengeParticipant(

        @SerializedName("appointed_by"      ) val appointedBy     : Int,
        @SerializedName("citizenship"       ) val citizenship     : String,
        @SerializedName("coins_spent"       ) val coinsSpent      : Double?  = null,
        @SerializedName("country_code"      ) val countryCode     : String,
        @SerializedName("enable_camera"     ) val enableCamera    : Boolean? = null,
        @SerializedName("id"                ) val id              : Int,
        @SerializedName("membership"        ) val membership      : String,
        @SerializedName("name"              ) val name            : String?  = null,
        @SerializedName("profile_url"       ) val profileUrl      : String?  = null,
        @SerializedName("request_accepted"  ) val requestAccepted : Boolean,
        @SerializedName("self_requested"    ) val selfRequested   : Boolean,
        @SerializedName("thumbnail"         ) val thumbnail       : String?  = null,
        @SerializedName("time_request_sent" ) val timeRequestSent : String?  = null,
        @SerializedName("user_id"           ) val userId          : Int,
        @SerializedName("username"          ) val username        : String?  = null,
        @SerializedName("verified"          ) val verified        : Boolean

    )
}
