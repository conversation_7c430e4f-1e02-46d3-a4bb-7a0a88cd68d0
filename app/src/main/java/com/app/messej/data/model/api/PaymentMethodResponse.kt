package com.app.messej.data.model.api


import com.google.gson.annotations.SerializedName


data class PaymentMethodResponse(
    @SerializedName("payment_methods") val paymentMethods: List<PaymentMethod>,
) {
    data class PaymentMethod(
        @SerializedName("icon") val icon: String,
        @SerializedName("id") val id: Int,
        @SerializedName("language") val language: String,
        @SerializedName("minimum_withdrawal_amount") val minimumWithdrawalAmount: Double,
        @SerializedName("payment_method") val paymentMethod: String,
        @SerializedName("payment_method_identifier") val paymentMethodIdentifier: String,
        @SerializedName("show_popup") val showPopup: Boolean,
        @SerializedName("process_fee") val processFee: Double,
        @SerializedName("service_fee") val serviceFee: Double,
        @SerializedName("transfer_fee" ) val transferFee: Double,
        @SerializedName("process_time" ) val processTime: Int,
        @SerializedName("status") val status: Boolean,
        @SerializedName("time_created") val timeCreated: String,
        @SerializedName("time_updated") val timeUpdated: String,
        @SerializedName("process_date") val processDate: String,
    ) {
        val isMenaPaymentMethod: Boolean
            get() = paymentMethodIdentifier.equals("mena-office", ignoreCase = true)
    }
}
