package com.app.messej.data.model.api.podium

import com.app.messej.data.model.api.podium.challenges.PodiumMaidanSupporter
import com.google.gson.annotations.SerializedName

data class PodiumMaidanUsersListResponse (
    @SerializedName("current_page" ) val currentPage : Int,
    @SerializedName("has_next"     ) val hasNext     : <PERSON><PERSON><PERSON>,
    @SerializedName("per_page"     ) val perPage     : Int,
    @SerializedName("total_items"  ) val totalItems  : Int,
    @SerializedName("total_pages"  ) val totalPages  : Int,
    @SerializedName("supporters", alternate = ["data","users"]) val users: List<PodiumMaidanSupporter>,
)