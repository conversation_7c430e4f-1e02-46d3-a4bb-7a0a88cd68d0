package com.app.messej.data.model.socket

import com.google.gson.annotations.SerializedName

data class HuddleTypingInfo(
    @SerializedName("huddle_id"   ) var huddleId   : Int     = 0,
    @SerializedName("typing"      ) var typing     : Boolean = false,
    @SerializedName("chat_type"   ) var chatType   : String?  = null, //HUDDLE
    @SerializedName("sender_id"   ) var senderId   : Int?     = null,
    @SerializedName("sender_name" ) var senderName : String?  = null,
    @SerializedName("room_id"     ) var roomId     : String?  = null
)
