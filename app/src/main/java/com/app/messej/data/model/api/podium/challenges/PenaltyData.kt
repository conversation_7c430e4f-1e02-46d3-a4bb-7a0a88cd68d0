package com.app.messej.data.model.api.podium.challenges

import android.util.Log
import androidx.annotation.IntRange
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.time.Duration
import java.time.ZonedDateTime

data class PenaltyData (
    @SerializedName("current_round"         )  val _currentRound              : Int? = null,
    @SerializedName("current_ball"          )  val _currentTurn              : Int? = null,
    @SerializedName("turn_start_time"    )  val _turnStartTime              : Double? = null,
    ) {

    companion object {
        const val TURN_DURATION_SECONDS = 15L
        const val TURN_DURATION_MILLIS = TURN_DURATION_SECONDS*1000L
    }

    enum class PenaltyRound(val number: Int) {
        ROUND_ONE(1), ROUND_TWO(2)
    }

    val currentRound: PenaltyRound?
        get() = when(_currentRound) {
            1 -> PenaltyRound.ROUND_ONE
            2 -> PenaltyRound.ROUND_TWO
            else -> null
        }

    val currentTurn: Int
        @IntRange(from = 1, to = 5)
        get() = (_currentTurn?:0).coerceIn(1..5)

    val turnStartTime: ZonedDateTime?
        get() = _turnStartTime?.let { DateTimeUtils.parseMillisToDateTime((it*1000).toLong()) }

    val turnEndTime: ZonedDateTime?
        get() = turnStartTime?.plusSeconds(TURN_DURATION_SECONDS)

    val activeTurn: ActiveTurn?
        get() {
            val now = ZonedDateTime.now()
            Log.d("PCPPT", "activeTurn: ${now.isAfter(turnStartTime)} ${now.isBefore(turnEndTime)}")
            // TODO checking for start time is causing clock sync issues
            if (now.isBefore(turnEndTime)) {
                currentRound?.let { round ->
                    return ActiveTurn(round,currentTurn,turnEndTime?: ZonedDateTime.now())
                }
            }
            return null
        }

    val didPlayAtLeastOnce: Boolean
        get() = currentRound==PenaltyRound.ROUND_TWO || currentTurn>1

    data class ActiveTurn(
        val round: PenaltyRound,
        @IntRange(from = 1, to = 5)
        val turn: Int,
        val endTime: ZonedDateTime
    ) {
        val remainingTime: Duration
            get() = DateTimeUtils.durationFromNowToFuture(endTime)?:Duration.ZERO

        fun isLesserThan(turn: ActiveTurn?): Boolean {
            turn?:return false
            return this.round.number < turn.round.number || (this.round.number == turn.round.number && this.turn < turn.turn)
        }
    }
}
