package com.app.messej.data.model.api

import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.google.gson.annotations.SerializedName

data class ReportRequest (
    @SerializedName("reported_type") var contentType: ReportContentType,
    @SerializedName("report_type") var reportType: ReportType,
    @SerializedName("user_id") var userId: Int,
    @SerializedName("reported_id") var contentId: String,
    @SerializedName("parent_id") var parentId: String? = null,
    @SerializedName("grand_parent_id") var appuppanId: String? = null,

    @SerializedName("category_id") var categoryId: Int? = null,
    @SerializedName("reason") var explanation: String? = null,
    @SerializedName("proof_files") var proofFiles: List<String>? = null,
)