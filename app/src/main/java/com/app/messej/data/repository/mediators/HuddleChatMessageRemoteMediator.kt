package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddleChatMessageWithMedia
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class HuddleChatMessageRemoteMediator(
    private val huddleId: Int,
    private val huddleType: HuddleType,
    private val database: FlashatDatabase,
    private val networkService: ChatAPIService,
    private val accountRepo: AccountRepository
) : RemoteMediator<Int, HuddleChatMessageWithMedia>() {
    val dao = database.getChatMessageDao()
    val user = accountRepo.user

    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = "${EntityDescriptions.TABLE_HUDDLE_MESSAGES}-$huddleId"
//    override suspend fun initialize(): InitializeAction {
//        return InitializeAction.SKIP_INITIAL_REFRESH
//    }

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, HuddleChatMessageWithMedia>
    ): MediatorResult {
        return try {
            val previous = when (loadType) {
                LoadType.REFRESH -> {
                    Log.d("HCMRM", "load: trying to REFRESH $huddleId")
                    null
                }
                LoadType.PREPEND -> {
                    Log.d("HCMRM", "load: trying to PREPEND $huddleId")
                    return MediatorResult.Success(endOfPaginationReached = true)
                }
                LoadType.APPEND -> {
//                    val lastItem = state.lastItemOrNull() ?: return MediatorResult.Success(endOfPaginationReached = true)
//                    Log.d("HCMRM", "load: trying to APPEND $huddleId after ${lastItem.message.messageId}")
//                    lastItem.message.messageId

                    val remoteKey = database.withTransaction {
                        remoteKeyDao.remoteKeyByQuery(tableKey)
                    }
                    // You must explicitly check if the page key is null when
                    // appending, since null is only valid for initial load.
                    // If you receive null for APPEND, that means you have
                    // reached the end of pagination and there are no more
                    // items to load.
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }
                    Log.d("HCMRM", "load: trying to APPEND $huddleId after ${remoteKey.nextPage}")
                    remoteKey.nextPage
                }
            }

            val response = networkService.getHuddleMessages(id = huddleId, previous = previous)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            fun sanitizeChatMessage(msg: HuddleChatMessage) {
                msg.huddleType = huddleType
                //For delete message of manager removed messages
                if (msg.deleted && msg.remover != null){
                    if (msg.remover.id?.toInt() == user.id) msg.displayMessage = msg.remover.message
                }
                msg.sanitize()
            }

            val messages = result.messages
            messages.forEach {
                sanitizeChatMessage(it)
            }


            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    if (huddleType==HuddleType.PUBLIC) {
                        if (dao.getHiddenHuddleChatMessage(HuddleChatMessage.prefixHuddleId(huddleId)) > 0) {
                            dao.cleanupHuddleChatMessages(HuddleChatMessage.prefixHuddleId(huddleId))
                        }
                        if (result.pinnedPost == null) {
                            dao.deletePinnedHuddleChatMessage()
                        }
                    }
                }

                // Update RemoteKey for this query.
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, messages.lastOrNull()?.messageId)
                )
                dao.apply {
                    database.withTransaction {
                        if (huddleType==HuddleType.PUBLIC) {
                            result.removedPosts?.let {
                                if (it.isNotEmpty()) {
                                    dao.deleteHuddleChatMessages(it)
                                }
                            }
                            result.pinnedPost?.apply {
                                sanitizeChatMessage(this)
                            }?.let {
                                dao.insertChat(it)
                            }
                        }
                        insertHuddleChatMessages(messages)
                    }
                }
            }
            Log.d("HCMRM", "load: endOfPaginationReached = ${result.messages.isEmpty()}")
            MediatorResult.Success(endOfPaginationReached = result.messages.isEmpty())
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}