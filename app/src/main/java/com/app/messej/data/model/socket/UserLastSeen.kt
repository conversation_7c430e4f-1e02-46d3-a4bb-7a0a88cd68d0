package com.app.messej.data.model.socket

import android.content.Context
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.DateFormatHelper
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime
import java.util.Locale

data class UserLastSeen(
    @SerializedName("userId"      ) val userId     : Int     = 0,
    @SerializedName("online"      ) val online     : Boolean = false,
    @SerializedName("last_seen"      ) val _lastSeen     : String? = null,
    @SerializedName("hide_online_status") val hideOnlineStatus: Boolean = false
) {
    val lastSeen: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(_lastSeen)

    val isOnlineAndNotHidden = !hideOnlineStatus && online

    fun lastSeenHumanized(c: Context): String {
            lastSeen?: return ""
            val day = DateFormatHelper.humanizeChatDate(lastSeen, c)
            val time = DateTimeUtils.format(lastSeen, DateTimeUtils.FORMAT_READABLE_TIME)
            return "$day ${time.uppercase(Locale.getDefault())}"
        }
}
