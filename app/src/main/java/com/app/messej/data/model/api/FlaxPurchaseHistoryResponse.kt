package com.app.messej.data.model.api


import com.app.messej.data.model.BuyFlaxRates
import com.google.gson.annotations.SerializedName

data class FlaxPurchaseHistoryResponse(
    @SerializedName("next_page")
    val nextPage: <PERSON><PERSON>an,
    @SerializedName("current_page"  ) var currentPage  : Int?               = null,
    @SerializedName("purchases")
    val purchases: ArrayList<BuyFlaxRates> = arrayListOf()
)