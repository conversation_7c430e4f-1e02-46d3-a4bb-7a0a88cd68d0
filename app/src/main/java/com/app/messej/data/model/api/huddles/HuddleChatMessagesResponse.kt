package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.entity.HuddleChatMessage
import com.google.gson.annotations.SerializedName

data class HuddleChatMessagesResponse(
    @SerializedName("messages"      ) var messages     : List<HuddleChatMessage> = listOf(),
    @SerializedName("pinned_post"   ) var pinnedPost : HuddleChatMessage? = null,
    @SerializedName("removed_messages"   ) var removedPosts : List<String>? = null
)
