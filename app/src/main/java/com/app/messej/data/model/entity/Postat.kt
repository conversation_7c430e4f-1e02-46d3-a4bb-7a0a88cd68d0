package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractChatMessage.SendStatus
import com.app.messej.data.model.MentionedUser
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.api.postat.MusicData
import com.app.messej.data.model.api.postat.PostatMedia
import com.app.messej.data.model.entity.Postat.Companion.COLUMN_ID
import com.app.messej.data.model.enums.PostatTab
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.DataFormatHelper
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

@Entity(
    tableName = EntityDescriptions.TABLE_MY_POSTAT,
    primaryKeys = [COLUMN_ID]
)
@TypeConverters(
    PostatMedia.Converter::class,
    SenderDetails.Converter::class,
    MusicData.Converter::class,
    MentionedUser.Converter::class
)
data class Postat(
    @SerializedName("message_id"        ) @ColumnInfo(COLUMN_ID                 ) val messageId       : String,
    @SerializedName("type"              ) @ColumnInfo(COLUMN_POSTAT_TYPE        ) var type            : PostatType,
    @SerializedName("hide"              ) @ColumnInfo("hide"              ) val hide            : Boolean?          = null,
    @SerializedName("user_id"           ) @ColumnInfo(COLUMN_USER_ID            ) val userId          : Int,
    @SerializedName("temp_id"           ) @ColumnInfo("temp_id"           ) val tempId          : String?           = null,
    @SerializedName("created"           ) @ColumnInfo(COLUMN_CREATED_TIME       ) val created         : String,
    @SerializedName("sent"              ) @ColumnInfo("sent"              ) val sent            : String?           = null,
    @SerializedName("media_count"       ) @ColumnInfo("media_count"       ) val mediaCount      : Int?              = null,
    @SerializedName("turn_off_comments" ) @ColumnInfo("turn_off_comments" ) val turnOffComments : Boolean?          = null,
    @SerializedName("is_original_audio" ) @ColumnInfo("is_original_audio" ) val isOriginalAudio : Boolean,
    @SerializedName("media"             ) @ColumnInfo("media"             ) val media           : List<PostatMedia>,
    @SerializedName("has_mention"       ) @ColumnInfo("has_mention"       ) val hasMention      : Boolean?          = null,
    @SerializedName("mentioned_users"   ) @ColumnInfo("mentioned_users"   ) val mentionedUsers  : List<Int>         = listOf(),
    @SerializedName("music_data"        ) @ColumnInfo("music_data"        ) val musicData       : MusicData?        = null,
    @SerializedName("message"           ) @ColumnInfo("message"           ) val message         : String?           = null,
    @SerializedName("color"             ) @ColumnInfo("color"             ) val color           : String?           = null,
    @SerializedName("sender_details"    ) @ColumnInfo("sender_details"    ) val senderDetails   : SenderDetails,
    @SerializedName("total_gift_count"  ) @ColumnInfo("total_gift_count"  ) val totalGiftCount  : Int               = 0,
    @SerializedName("total_gift_value"  ) @ColumnInfo("total_gift_value"  ) val totalGiftValue  : Double            = 0.0,
    @SerializedName("total_comments"    ) @ColumnInfo("total_comments"    ) val totalComments   : Int               = 0,
    @SerializedName("is_followed"       ) @ColumnInfo("is_followed"       ) val isFollowed      : Boolean?          = null
){

    companion object {
        const val COLUMN_ID = "id"
        const val COLUMN_USER_ID = "userId"
        const val COLUMN_CREATED_TIME = "time_created"
        const val COLUMN_POSTAT_TYPE = "postat_type"
        const val COLUMN_SEND_STATUS = "sendStatus"
        const val COLUMN_TAB = "postat_tab"
    }

    enum class PostatType {
        DRAFT,
        FINAL
    }

    @ColumnInfo(name = COLUMN_SEND_STATUS)
    @Transient
    var sendStatus: SendStatus? = SendStatus.NONE
        get() {
            if (field==null) field = SendStatus.NONE
            return field
        }

    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(created)

    val giftCountFormatted: String
        get() = DataFormatHelper.numberToK(totalGiftCount)

    val giftValueFormatted: String
        get() = totalGiftValue.numberToKWithFractions()

    val totalCommentsFormatted: String
        get() = DataFormatHelper.numberToK(totalComments)

    val hasMusic: Boolean
        get() = !isOriginalAudio && musicData?.valid==true

    @Entity(
        tableName = EntityDescriptions.TABLE_POSTAT_FEED,
        primaryKeys = [COLUMN_ID, COLUMN_TAB]
    )
    data class FeedPostat (
        @Embedded val postat: Postat
    ) {
        @ColumnInfo(name = COLUMN_TAB)
        var postatTab: PostatTab = PostatTab.UNSET
    }
}
