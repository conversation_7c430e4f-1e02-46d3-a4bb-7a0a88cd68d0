package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ProfileAPIService
import com.app.messej.data.model.BlockedUser

private const val STARTING_KEY = 1
class EmpoweredBlockedUsersDataSource(val api: ProfileAPIService, val type: String) : PagingSource<Int, BlockedUser>() {
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, BlockedUser> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getAllBlockedUsersList(functionality = type,page = currentPage, limit = 50)
            val responseData = mutableListOf<BlockedUser>()
            val result = response.body()?.result
            val data = result?.users ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (!(response.body()?.result!!.nextPage == true)) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.users, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("EmpoweredBlockedUsersResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, BlockedUser>): Int? {
        return null
    }
}