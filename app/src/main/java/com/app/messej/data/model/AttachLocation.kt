package com.app.messej.data.model

import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.android.gms.maps.model.LatLng
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName

data class AttachLocation(
    @SerializedName("latitude") val latitude: Double,
    @SerializedName("longitude") val longitude: Double,
    @SerializedName("placeName") val placeName: String? = null,
    @SerializedName("message_type") val messageType: AbstractChatMessage.MessageType = AbstractChatMessage.MessageType.LOCATION,
) {

    fun encode(): String = Gson().toJson(this)
    companion object {
        fun of(latLng: LatLng) = AttachLocation(latLng.latitude, latLng.longitude)

        fun decode(json: String): AttachLocation = Gson().fromJson<AttachLocation>(json)
    }
}
