package com.app.messej.data.model.api

import com.app.messej.data.model.entity.DealsTransferHistory
import com.google.gson.annotations.SerializedName

data class DealsTransferHistoryResponse(@SerializedName("total")val total:Int?=null,
                                        @SerializedName("next_page")val nextPage:Boolean?=null,
                                        @SerializedName("current_page") val currentPage:Int?=null,
                                        @SerializedName("transfers") val transfers:ArrayList<DealsTransferHistory> = arrayListOf())
