package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.entity.Sticker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1
class StickerDataSource (private val api: ChatAPIService, private val keyword: String) : PagingSource<Int, Sticker>() {

    init {
        Log.d("Test","Success")
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Sticker> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage =  params.key ?: STARTING_KEY
                val response = currentPage.let {
                    api.getSearchEmojis(keyword = keyword, page = currentPage.toString())
                }
                val responseData = mutableListOf<Sticker>()

                Log.d("EmojiSearchResponse",response.body()?.result?.stickers.toString())

                val data = response.body()?.result?.stickers ?: emptyList()
                 responseData.addAll(data)
                val nextKey = if (response.body()?.result?.nextPage == false) null else currentPage.plus(1)
                LoadResult.Page(
                    data = response.body()?.result?.stickers?: emptyList(), prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
    override fun getRefreshKey(state: PagingState<Int, Sticker>): Int? {
        val anchorPosition = state.anchorPosition ?: return null
        val emoji = state.closestItemToPosition(anchorPosition) ?: return null
        return ensureValidKey(key = emoji.id!! - (state.config.pageSize / 2) )
    }
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)

}