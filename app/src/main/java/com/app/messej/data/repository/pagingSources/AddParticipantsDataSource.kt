package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.AddParticipantsResponse
import com.app.messej.data.repository.AccountRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.max

private const val STARTING_KEY = 1

class AddParticipantsDataSource(private val api: ChatAPIService,
                                private val accountRepo: AccountRepository, private val huddleId: Int, private val searchType: String,
                                private val searchKeyword: String = ""): PagingSource<Int, AddParticipantsResponse.Members>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, AddParticipantsResponse.Members> {
        return try {
            withContext(Dispatchers.IO) {
            val currentPage = params.key ?: STARTING_KEY
                val response = api.getAddParticipantsList(
                    huddleId, searchType, currentPage, keyword = searchKeyword
                )
                val responseData = mutableListOf<AddParticipantsResponse.Members>()
                val result = response.body()?.result
                val data = result?.members ?: emptyList()

                responseData.addAll(data)
                val nextKey = if (result?.nextPage == null) null else result.nextPage!!

                LoadResult.Page(
                    data = result!!.members,
                    prevKey = if (currentPage == 1) null else currentPage.minus(1),
                    nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, AddParticipantsResponse.Members>): Int? {
        return null
    }

    /**
     * Makes sure the paging key is never less than [STARTING_KEY]
     */
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)
}
