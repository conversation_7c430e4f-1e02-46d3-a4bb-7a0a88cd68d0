package com.app.messej.data.api

import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.huddles.BroadcastListResponse
import com.app.messej.data.model.enums.BroadcastMode
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Path
import retrofit2.http.Query

interface BroadcastAPIService {
    @GET("/chat/broadcasts/{userId}/messages")
    @Headers("Accept: application/json")
    suspend fun getOutgoingBroadcasts(@Path("userId") userId: Int, @Query("broadcastType") type: BroadcastMode): Response<APIResponse<BroadcastListResponse>>

    @GET("/chat/broadcasts/{userId}/messages")
    @Headers("Accept: application/json")
    suspend fun getIncomingBroadcasts(@Path("userId") userId: Int, @Query("recent") recent: String?): Response<APIResponse<BroadcastListResponse>>

}
