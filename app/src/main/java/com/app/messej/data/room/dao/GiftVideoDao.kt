package com.app.messej.data.room.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.app.messej.data.model.entity.OfflineGiftVideo
import com.app.messej.data.room.EntityDescriptions

@Dao
abstract class GiftVideoDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insertOrReplace(gift: OfflineGiftVideo)


    @Query("SELECT * FROM ${EntityDescriptions.TABLE_OFFLINE_GIFT_VIDEO} WHERE ${OfflineGiftVideo.COLUMN_GIFT_ID} = :id LIMIT 1")
    abstract suspend fun getLocalGiftVideo(id: Int): OfflineGiftVideo?

    @Query("DELETE FROM ${EntityDescriptions.TABLE_OFFLINE_GIFT_VIDEO} WHERE ${OfflineGiftVideo.COLUMN_GIFT_ID} = :id")
    abstract suspend fun deleteGiftVideo(id: Int)

    @Delete
    abstract suspend fun deleteGiftVideo(vararg video: OfflineGiftVideo): Int
}