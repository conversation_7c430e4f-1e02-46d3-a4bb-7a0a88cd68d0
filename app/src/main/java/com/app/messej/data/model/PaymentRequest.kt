package com.app.messej.data.model


import com.google.gson.annotations.SerializedName

data class PaymentRequest(
    @SerializedName("inputs") val inputs: List<Input>,
    @SerializedName("payment_method_id") val paymentMethodId: Int,
    @SerializedName("persist") val persist: <PERSON><PERSON><PERSON>,
    @SerializedName("payout_id") val payoutId: Int,
) {
    data class Input(
        @SerializedName("input_data") val inputData: String,
        @SerializedName("payment_form_id") val paymentFormId: Int,
    )
}