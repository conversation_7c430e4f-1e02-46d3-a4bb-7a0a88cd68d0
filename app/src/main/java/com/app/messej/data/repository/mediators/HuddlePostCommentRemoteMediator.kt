package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.room.FlashatDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@OptIn(ExperimentalPagingApi::class)
class HuddlePostCommentRemoteMediator(
    private val huddleId: Int,
    private val postId: String,
    private val database: FlashatDatabase,
    private val networkService: ChatAPIService,
) : RemoteMediator<Int, PostCommentItem>() {
    val dao = database.getChatMessageDao()

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, PostCommentItem>,
    ): MediatorResult {
        return try {
            var previous: String? = null
            var recent: String? = null
            when (loadType) {
                LoadType.REFRESH -> {
                    Log.d("PCMRM", "load: trying to REFRESH")
                }

                LoadType.PREPEND -> {
                    Log.d("PCMRM", "load: trying to PREPEND")
                    return MediatorResult.Success(endOfPaginationReached = true)
                }

                LoadType.APPEND -> {
                    Log.d("PCMRM", "load: trying to APPEND")
                    val lastItem = state.lastItemOrNull() ?: return MediatorResult.Success(endOfPaginationReached = true)
                    previous = lastItem.commentId
                }
            }

            val response = networkService.getHuddlePostComments(huddleId, postId)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse = ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            val messages = result.comments

            withContext(Dispatchers.IO) {
                database.withTransaction {
                    if (loadType==LoadType.REFRESH) {
                        dao.deleteHuddlePostComments(postId)
                    }

                    dao.getHuddleChatMessage(postId)?.let {
                        if (it.totalComments!=result.commentsCount) {
                            dao.updateChat(
                                it.copy(
                                    totalComments = result.commentsCount
                                )
                            )
                        }
                    }

                    messages.let {
                        it.forEach { it.sanitize() }
                        dao.insertHuddlePostComments(it)
                    }
                }
            }

            val isRepeating = messages.findLast { it.commentId == previous } != null

            MediatorResult.Success(endOfPaginationReached = result.comments.isEmpty() || isRepeating)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}