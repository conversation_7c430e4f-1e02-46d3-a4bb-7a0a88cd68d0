package com.app.messej.data.model.api.eTribe

import com.google.gson.annotations.SerializedName

data class ETribeSuperStarMessageResponse(
    @SerializedName("superstar_messages") val members: List<SuperStarMessage>? = null
) {
    data class SuperStarMessage(
        @SerializedName("superstar_name") val superstarName: String? = null,
        @SerializedName("message") val message: String? = null
    )
}


