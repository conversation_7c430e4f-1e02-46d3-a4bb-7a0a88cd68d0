package com.app.messej.data.model.api


import com.google.gson.annotations.SerializedName

data class UserDetailpayoutResponse(
    @SerializedName("address") val address: String,
    @SerializedName("city") val city: String,
    @SerializedName("country_code") val countryCode: String,
    @SerializedName("country_code_iso") val countryCodeIso: String,
    @SerializedName("fname") val fname: String,
    @SerializedName("id_number") val idNumber: String,
    @SerializedName("lname") val lname: String,
    @SerializedName("persist") val persist: <PERSON><PERSON>an,
    @SerializedName("phone") val phone: String,
    @SerializedName("pincode") val pincode: String,
    @SerializedName("id") val id: Int?,
    @SerializedName("payout_request_id") val payoutRequestId:   Int?,
)