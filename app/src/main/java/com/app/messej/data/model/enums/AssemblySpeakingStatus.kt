package com.app.messej.data.model.enums

import com.google.gson.annotations.SerializedName

enum class AssemblySpeakingStatus {
    @SerializedName(value = "ACTIVE", alternate = ["active"]) ACTIVE,
    @SerializedName(value = "PAUSED", alternate = ["paused"]) PAUSED,
    @SerializedName(value = "INACTIVE", alternate = ["inactive"]) INACTIVE,
    @SerializedName(value = "WAITING", alternate = ["waiting"]) WAITING;
}