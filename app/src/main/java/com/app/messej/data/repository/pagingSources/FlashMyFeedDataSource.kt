package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.FlashAPIService
import com.app.messej.data.model.entity.FlashVideo
import com.app.messej.data.model.enums.FlashTab
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class FlashMyFeedDataSource(private val api: FlashAPIService): PagingSource<Int, FlashVideo>() {

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, FlashVideo> {
        return try {
            withContext(Dispatchers.IO) {
                val response = api.getFlashFeed(
                    tab = FlashTab.ME.serializedName(),
                    page = params.key
                )
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                data.items.forEach { it.sanitize() }
                LoadResult.Page(
                    data = data.items, prevKey = null, nextKey = if (data.currentPage<data.totalPages) data.currentPage+1 else null
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override val keyReuseSupported: Boolean
        get() = true

    override fun getRefreshKey(state: PagingState<Int, FlashVideo>): Int? {
        return null
    }
}