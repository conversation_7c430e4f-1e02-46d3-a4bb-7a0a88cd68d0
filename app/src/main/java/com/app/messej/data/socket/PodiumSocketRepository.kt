package com.app.messej.data.socket

import android.util.Log
import com.app.messej.data.socket.repository.PodiumChallengeEventRepository
import com.app.messej.data.socket.repository.PodiumEventRepository
import org.json.JSONObject

object PodiumSocketRepository: AbstractSocketRepository<PodiumSocketEvent>(PodiumSocketService()) {

    override fun onEvent(eventName: String, event: JSONObject) {
        val eventType = PodiumSocketEvent.from(eventName)?:return
        Log.d(logTag, "onEvent: $eventType")
        val data = event
        handleMessage(eventType,data)
    }

    override fun addListeners() {
        registerListener(PodiumEventRepository)
        registerListener(PodiumChallengeEventRepository)
    }
}