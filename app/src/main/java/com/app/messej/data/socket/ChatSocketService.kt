package com.app.messej.data.socket


import android.util.Log
import com.app.messej.BuildConfig
import io.socket.client.Socket
import org.json.JSONObject

class ChatSocketService: AbstractSocketService(
    socketUrl = BuildConfig.SOCKET_URL,
    path = "/messej"
) {

    private val TAG = ChatSocketService::class.java.simpleName
    override fun Socket.interceptEvents() {
        onAnyIncoming { args ->
            val eName = args.getOrNull(0) as String
            if (eName == "message") {
                val msg = args.getOrNull(1) as JSONObject
                Log.d(TAG, "RX: $msg")
                Log.d(TAG, "listeners: $listeners")
                listeners.forEach { it.onEvent(eName,msg) }
            } else {
                Log.d(TAG, "RX else: ${args.getOrNull(0)}: ${args.getOrNull(1)}")
            }
        }
        onAnyOutgoing { args ->
            Log.d(TAG, "TX: ${args.getOrNull(0)}: ${args.getOrNull(1)}")
        }
    }
}