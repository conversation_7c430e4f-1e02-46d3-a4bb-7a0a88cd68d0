package com.app.messej.data.repository

import android.app.Application
import android.util.Log
import com.app.messej.data.api.APIServiceGenerator
import com.app.messej.data.api.AuthAPIService
import com.app.messej.data.model.api.CountryFlagResponse
import com.app.messej.data.model.api.auth.VerifyOTPRequest
import com.app.messej.data.model.api.settings.PodiumPrivacyResponse
import com.app.messej.data.model.api.settings.PrivacyMessage
import com.app.messej.data.model.api.settings.PrivacySettingsResponse
import com.app.messej.data.model.api.settings.PrivacyUpdateRequest
import com.app.messej.data.model.enums.PrivacySettingsMode
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

class SettingsRepository(application: Application) {

    private val datastore = FlashatDatastore()

    //Delete Account Api Calls
    suspend fun deleteAccountPhone(phoneNumber: String, countryCode: String): ResultOf<Unit> {
        return try {
            val phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(phoneNumber)
            val req = VerifyOTPRequest(
                countryCode = UserInfoUtil.addPlusToCountryCode(countryCode),
                phone = phoneNumberFormatted
            )
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).deleteUser(req)
            val result = APIUtil.handleDeleteResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }


    suspend fun deleteAccountEmail(email: String): ResultOf<Unit> {
        return try {
            val req = VerifyOTPRequest(email = email)
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).deleteUser(req)
            val result = APIUtil.handleDeleteResponse(response)
            result
        } catch (e: Exception) {

            ResultOf.Error(Exception(e))
        }
    }

    /*
    *Privacy Settings Api Call
    * */

    // Api to get the status of the privacy settings
    suspend fun getPrivacyStatus(force: Boolean = true): ResultOf<PrivacySettingsResponse> {
        if (!force) {
            val existing = privacySettingsFlow().firstOrNull()
            Log.d("LIMIT", "getPrivacyStatus: existing limit - ${existing?.huddleVoiceMessageLengthInSeconds}")
            if (existing!=null) return ResultOf.Success(existing)
        }
        return try {
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).getPrivacySettingDetails()
            val result = APIUtil.handleResponse(response)
            if (result is ResultOf.Success) {
                Log.d("LIMIT", "getPrivacyStatus: response limit - ${result.value.huddleVoiceMessageLengthInSeconds}")
                result.value.unseenNotificationCount?.let {
                    FlashatDatastore().setNotificationCount(it)
                }
                savePrivacySettings(result.value)
            }
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend fun savePrivacySettings(result: PrivacySettingsResponse) {
        datastore.savePrivacySettings(result)
    }

    fun privacySettingsFlow(): Flow<PrivacySettingsResponse?> = datastore.getPrivacySettingsFlow()

    suspend fun setPrivacy(mode: PrivacySettingsMode, privacy:String, audience:List<String>): ResultOf<PrivacySettingsResponse> {

        when(mode){
            PrivacySettingsMode.LAST_SEEN -> {
                val response = APIServiceGenerator.createService(AuthAPIService::class.java).setLastSeenPrivacySettings(PrivacyUpdateRequest(audience=audience, privacy =privacy))
                return APIUtil.handleResponse(response)
            }
            PrivacySettingsMode.PROFILE_PHOTO -> {
                val response = APIServiceGenerator.createService(AuthAPIService::class.java).setProfilePicPrivacySettings(PrivacyUpdateRequest(audience=audience, privacy =privacy))
                return APIUtil.handleResponse(response)
            }

            PrivacySettingsMode.ABOUT -> {
                val response = APIServiceGenerator.createService(AuthAPIService::class.java).setAboutPrivacySettings(PrivacyUpdateRequest(audience=audience, privacy =privacy))
                return APIUtil.handleResponse(response)
            }
            PrivacySettingsMode.ONLINE_STATUS -> {
                val response = APIServiceGenerator.createService(AuthAPIService::class.java).setOnlineStatusPrivacySettings(PrivacyUpdateRequest(audience=audience, privacy =privacy))
                return APIUtil.handleResponse(response)
            }
        }
    }

    suspend  fun getMessagePrivacyStatus(): ResultOf<PrivacyMessage> {
        return try {
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).getPrivacyMessageSettingDetails()
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend  fun setMessagePrivacy(privacy: PrivacyMessage): ResultOf<Any> {
        return try {
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).setMessagePrivacy(privacy)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend  fun getCountryFlagDetails(): ResultOf<CountryFlagResponse> {
        return try {
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).getCountryFlagDetails()
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend  fun updateCountryFlagDetails(isChecked: Boolean): ResultOf<CountryFlagResponse> {
        return try {
            val response = APIServiceGenerator.createService(AuthAPIService::class.java).setCountryFlagDetails(CountryFlagResponse(displayCountryFlag = isChecked))
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    fun getSettings() = datastore.settingsFlow()

    suspend  fun getPodiumPrivacyStatus(): ResultOf<PodiumPrivacyResponse> {
        return try {
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).getPodiumPrivacyDetails()
            val result = APIUtil.handleResponse(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend fun setPodiumPrivacy(podiumPrivacy: PodiumPrivacyResponse): ResultOf<Any> {
        return try {
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).setPodiumPrivacy(podiumPrivacy)
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

    suspend  fun updateLanguage(): ResultOf<Any> {
        return try {
            val response =  APIServiceGenerator.createService(AuthAPIService::class.java).setLanguage()
            val result = APIUtil.handleResponseWithoutResult(response)
            result
        } catch (e: Exception) {
            ResultOf.Error(Exception(e.message))
        }
    }

}