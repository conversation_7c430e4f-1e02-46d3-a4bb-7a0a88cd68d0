package com.app.messej.data.model.api.huddles

import com.app.messej.data.model.SuggestedHuddle
import com.google.gson.annotations.SerializedName

data class HuddleSuggestionResponse(
    @SerializedName("free_offset"    ) var freeOffset    : Int?               = null,
    @SerializedName("huddles"        ) var huddles       : ArrayList<SuggestedHuddle> = arrayListOf(),
    @SerializedName("premium_offset" ) var premiumOffset : Int?               = null,
    @SerializedName("total"          ) var total         : Int?               = null
)
