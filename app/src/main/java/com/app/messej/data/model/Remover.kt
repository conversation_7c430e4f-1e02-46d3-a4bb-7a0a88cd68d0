package com.app.messej.data.model

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class Remover(
    @SerializedName("id"      ) val id      : String? = null,
    @SerializedName("message" ) val message : String? = null
){

    val idInt: Int?
        get() = id?.toIntOrNull()
    class Converter {
        @TypeConverter
        fun decode(data: String?): Remover? {
            data?: return null
            val type: Type = object : TypeToken<Remover?>() {}.type
            return Gson().fromJson<Remover>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: Remover?): String? {
            return Gson().toJson(someObjects)
        }
    }
}
