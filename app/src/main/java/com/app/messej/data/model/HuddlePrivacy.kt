package com.app.messej.data.model

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

data class HuddlePrivacy(
    @SerializedName("canComment"                        ) var canComment                        : <PERSON><PERSON>an,
    @SerializedName("canPost"                           ) var canPost                           : <PERSON><PERSON>an,
    @SerializedName("canReply"                          ) var canReply                          : Boolean
) {
    class Converter {
        @TypeConverter
        fun decode(data: String?): HuddlePrivacy? {
            data?: return null
            val type: Type = object : TypeToken<HuddlePrivacy?>() {}.type
            return Gson().fromJson<HuddlePrivacy>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: HuddlePrivacy?): String? {
            return Gson().toJson(someObjects)
        }
    }
}


