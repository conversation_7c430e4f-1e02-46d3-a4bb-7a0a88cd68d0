package com.app.messej.data.model.enums

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.app.messej.R
import com.app.messej.data.utils.EnumUtil.except
import com.google.gson.annotations.SerializedName

enum class ChallengeType(@StringRes val resId: Int) {
    @SerializedName("MAIDAN") MAIDAN(R.string.podium_challenge_maidan),
    @SerializedName("LIKES") LIKES(R.string.podium_challenge_likes),
    @SerializedName("GIFTS") GIFTS(R.string.podium_challenge_gifts),
    @SerializedName("PENALTIES") PENALTY(R.string.podium_challenge_penalty),
    @SerializedName("CONFOUR") CONFOUR(R.string.podium_challenge_confour),
    @SerializedName("FLAGS") FLAGS(R.string.podium_challenge_flags),
    @SerializedName("BOX") BOXES(R.string.podium_challenge_boxes),
    @SerializedName("KNOWLEDGE") KNOWLEDGE(R.string.podium_challenge_knowledge);

    val iconRes: Int
        @DrawableRes
        get() {
            return when (this) {
                LIKES -> R.drawable.ic_podium_challenge_like
                GIFTS -> R.drawable.ic_podium_challenge_gift
                FLAGS -> R.drawable.ic_podium_challenge_flag
                CONFOUR -> R.drawable.ic_podium_challenge_confour
                PENALTY -> R.drawable.ic_podium_challenge_penalty
                BOXES -> R.drawable.ic_podium_challenge_box
                KNOWLEDGE -> R.drawable.ic_podium_challenge_knowledge
                MAIDAN -> 0
            }
        }

    companion object {
        val forYallaGuys: List<ChallengeType>
            get() = ChallengeType.entries.except(MAIDAN, LIKES, GIFTS).toList()
    }
}