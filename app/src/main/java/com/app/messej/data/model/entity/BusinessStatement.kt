package com.app.messej.data.model.entity


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DDMMYYYY_SLASHED
import com.app.messej.data.utils.DateTimeUtils.FORMAT_READABLE_DATE_SLASHED
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type
import kotlin.math.roundToInt

@Entity(tableName = EntityDescriptions.TABLE_BUSINESS_STATEMENTS)

@TypeConverters(
    BusinessStatement.SocialTransactions.Converter::class
)

data class BusinessStatement(
    @SerializedName("account_name"                  ) var accountName           : String,
    @SerializedName("address"                       ) var address               : String? = "",
    @SerializedName("available_balance"             ) var availableBalance      : Double? = 0.0,
    @SerializedName("balance_brought_forward"       ) var balanceBroughtForward : Double? = 0.0,
    @SerializedName("beneficiary_name"              ) var beneficiaryName       : String? = "",
    @SerializedName("grand_total"                   ) var grandTotal            : Double? = 0.0,
    @SerializedName("grand_total_generated"         ) var grandTotalGenerated   : Double? = 0.0,
    @SerializedName("grand_total_refunded"          ) var grandTotalRefunded    : Double? = 0.0,
    @SerializedName("grand_total_rewarded"          ) var grandTotalRewarded    : Double? = 0.0,
    @SerializedName("grand_total_withdrawn"         ) var grandTotalWithdrawn   : Double? = 0.0,
    @SerializedName("statement_date") var statementDate: String? = "",
    @SerializedName("this_month_generated") var thisMonthGenerated: Double? = 0.0,
    @SerializedName("this_month_net") var thisMonthNet: Double? = 0.0,
    @SerializedName("this_month_refunded") var thisMonthRefunded: Double? = 0.0,
    @SerializedName("this_month_rewarded") var thisMonthRewarded: Double? = 0.0,
    @SerializedName("this_month_total") var thisMonthTotal: Double? = 0.0,
    @SerializedName("this_month_withdrawn") var thisMonthWithdrawn: Double? = 0.0,
    @SerializedName("total_pending_pp") var totalPendingPP: Double? = 0.0,
    @SerializedName("balance_carry_forward_monthyear") var balanceCarryMonth: String? = "",
    @SerializedName("flax_increment") var isFlaxIncrement: Boolean? = false,
    @SerializedName("flax_rate_percentage") var flaxRatePercentage: Double? = 0.0,
    @SerializedName("received_flax") var receivedFlax:Double?=0.0,
    @SerializedName("sent_flax") var sentFlax:Double?=0.0,
    @SerializedName("purchased_flax") var purchasedFlax:Double?=0.0,
    @SerializedName("sold_gift_flax") var soldGiftFlax:Double?=0.0,
    @SerializedName("purchased_gift_flax") var purchasedGiftFlax:Double?=0.0,
    @SerializedName("grand_total_debit") var grandTotalDebit:Double?=0.0,
    @SerializedName("podium_camera_purchase") var podiumCameraPurchase:Double?=0.0,
    @SerializedName("cancelled_deductions") var cancelledDeductions:Double?=0.0,
    @SerializedName("social_transactions") var socialTransactions:SocialTransactions?=null
) {

    data class SocialTransactions(
        @SerializedName("donations_received")
        val donationsReceived: Double?=0.0,

        @SerializedName("withdrawn")
        val withdrawn: Double?=0.0,

        @SerializedName("available_balance")
        val availableBalance: Double?=0.0,

        @SerializedName("pending_pp")
        val pendingPP: Double?=0.0
    ) {
        class Converter {
            @TypeConverter
            fun decode(data: String?): SocialTransactions? {
                data?: return null
                val type: Type = object : TypeToken<SocialTransactions?>() {}.type
                return Gson().fromJson<SocialTransactions>(data, type)
            }
            @TypeConverter
            fun encode(someObjects: SocialTransactions?): String? {
                return Gson().toJson(someObjects)
            }
        }
    }

    @PrimaryKey(autoGenerate = false)
    @ColumnInfo(name = "local_id")
    var localId: Int = 1

    val statementDateFormatted
        get() = DateTimeUtils.format(DateTimeUtils.parseDate(statementDate, FORMAT_DDMMYYYY_SLASHED), FORMAT_READABLE_DATE_SLASHED)


    val flaxRate: Int
        get() {
            return flaxRatePercentage?.roundToInt() ?: 0
        }

}