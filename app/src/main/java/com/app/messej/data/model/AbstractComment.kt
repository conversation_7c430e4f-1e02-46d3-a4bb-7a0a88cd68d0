package com.app.messej.data.model

import com.app.messej.data.utils.DateTimeUtils
import java.time.ZonedDateTime

abstract class AbstractComment {
    abstract val commentId: String
    abstract val huddleId: Int
    abstract val messageId: String
    abstract val created: String?
    abstract val comment: String?
    abstract val media: String?
    abstract val senderId: Int?
    abstract val senderDetails: SenderDetails?

    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(created)

    val formattedCreatedDateTime: String
        get() = DateTimeUtils.format(parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")
}