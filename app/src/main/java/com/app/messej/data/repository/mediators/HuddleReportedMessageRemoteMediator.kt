package com.app.messej.data.repository.mediators

import android.util.Log
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import androidx.room.withTransaction
import com.app.messej.data.Constants
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.model.entity.HuddleReportedMessageWithMedia
import com.app.messej.data.model.entity.RemotePagingKey
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.room.FlashatDatabase

@OptIn(ExperimentalPagingApi::class)
class HuddleReportedMessageRemoteMediator(
    private val huddleId: Int,
    private val huddleType: HuddleType,
    private val database: FlashatDatabase,
    private val networkService: ChatAPIService
) : RemoteMediator<Int, HuddleReportedMessageWithMedia>() {
    val dao = database.getChatMessageDao()

    private val remoteKeyDao = database.getRemotePagingDao()

    private val tableKey = "${EntityDescriptions.TABLE_HUDDLE_REPORTED_MESSAGES}-$huddleId"

    override suspend fun initialize(): InitializeAction {
        return InitializeAction.LAUNCH_INITIAL_REFRESH
    }

    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, HuddleReportedMessageWithMedia>
    ): MediatorResult {
        return try {
            val page: Int? = when (loadType) {
                LoadType.REFRESH -> null
                LoadType.PREPEND -> return MediatorResult.Success(endOfPaginationReached = true)
                LoadType.APPEND -> {
                    Log.d("PHRM", "load: APPEND")
                    val remoteKey = database.withTransaction {
                        database.getRemotePagingDao().remoteKeyByQuery(tableKey)
                    }
                    // You must explicitly check if the page key is null when
                    // appending, since null is only valid for initial load.
                    // If you receive null for APPEND, that means you have
                    // reached the end of pagination and there are no more
                    // items to load.
                    if (remoteKey?.nextPage == null) {
                        return MediatorResult.Success(
                            endOfPaginationReached = true
                        )
                    }

                    remoteKey.nextPageInt
                }
            }

            val response = networkService.getHuddleReportedMessages(huddleId = huddleId, page = page)
            val result = if (response.isSuccessful && response.code() == 200) {
                response.body()?.result ?: throw Exception(Constants.ERROR_RESPONSE_BODY_MISSING)
            } else {
                val error: ErrorResponse =
                    ErrorResponse.parseError(response = response.errorBody()!!)
                throw Exception(error.message)
            }

            val reports = result.reports
            database.withTransaction {
                if (loadType == LoadType.REFRESH) {
                    remoteKeyDao.deleteByQuery(tableKey)
                    dao.deleteAllHuddleReportedMessages(huddleId)
                }
                // Update RemoteKey for this query.
                remoteKeyDao.insertOrReplace(
                    RemotePagingKey(tableKey, if(result.nextPage) (result.currentPage+1).toString() else null)
                )
                dao.insertHuddleReportedMessages(reports)
            }

            MediatorResult.Success(endOfPaginationReached = result.nextPage)
        } catch (e: Exception) {
            MediatorResult.Error(e)
        }
    }
}