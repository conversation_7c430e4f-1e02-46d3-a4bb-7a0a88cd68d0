package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.enums.SocialActionButtonType
import com.app.messej.data.model.enums.SocialCaseStatus
import com.app.messej.data.utils.DateTimeUtils
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import kotlin.math.abs

abstract class AbstractSocialCase {
    abstract val id: Int?
    abstract val receivedAmount: Double?
    abstract val targetAmount: Double?
    abstract val caseTitle: String?
    abstract val status: SocialCaseStatus?
    abstract val supportVotes: Int?
    abstract val opposedVotes: Int?
    abstract val netVotes: Int?
    abstract val caseDescription: String?
    abstract val proofFiles: List<String>?
    abstract val userDetail: SocialAffairUser?
    abstract val timeCreated: String?
    abstract val isVoted: Boolean?

    companion object {
        private const val MAX_NET_VOTE_LIMIT = 80
    }

    val isStatusActive: Boolean
        get() = status == SocialCaseStatus.ACTIVE

    val isDonateView : Boolean
        get() = when(status) {
            SocialCaseStatus.ACTIVE, SocialCaseStatus.CLOSED, SocialCaseStatus.PENDING_APPROVAL -> true
            else -> false
        }

    val isNewCase: Boolean
        get() = status == SocialCaseStatus.NEW

    val isNetVotesNegative: Boolean
        get() = (netVotes ?: 0) < 0

    val isPendingCase: Boolean
        get() = status == SocialCaseStatus.PENDING_APPROVAL

    val isClosedCase: Boolean
        get() = status == SocialCaseStatus.CLOSED

    val remainingCoinsCanDonate: Double
        get() = (targetAmount ?: 0.0) - (receivedAmount ?: 0.0)

    val isDonationCompleted: Boolean
        get() = receivedAmount == targetAmount

    val donateButtonType: SocialActionButtonType
        get() = if (isDonationCompleted) SocialActionButtonType.DisabledButton else SocialActionButtonType.ActiveButton

    val voteButtonType: SocialActionButtonType
        get() = when {
            isVoted == true -> SocialActionButtonType.DisabledButton
            isNewCase -> SocialActionButtonType.ActiveButton
            else -> SocialActionButtonType.ExpiredButton
        }

    val progress : Float
        get() = if (isDonateView) {
            targetAmount?.takeIf { it != 0.0 }?.let { receivedAmount?.div(other = it) } ?: 0.0
        } else {
            ((abs(n = netVotes ?: 0)).toDouble() / MAX_NET_VOTE_LIMIT.toDouble())
        }.toFloat()

    val canChangeCaseStatus: Boolean
        get() = when(status) {
            SocialCaseStatus.NEW, SocialCaseStatus.DRAFT, SocialCaseStatus.ARCHIVED -> true
//            SocialCaseStatus.ACTIVE -> TODO()
//            SocialCaseStatus.CLOSED -> TODO()
//            SocialCaseStatus.DELETED -> TODO()
//            SocialCaseStatus.PENDING_APPROVAL -> TODO()
//            SocialCaseStatus.UNMET_VOTING -> TODO()
            else -> false
        }

    private val zonedDateTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTimeWithoutZ(timeCreated)

    private val formattedTime: String
        get() = DateTimeUtils.formatTime24Hr(dt = zonedDateTime)

    val formatedDateAndTime: String
        get() = DateTimeUtils.format(dt = zonedDateTime, format = DateTimeUtils.FORMAT_DDMMYYYY_SLASHED) + "  |  " + formattedTime

    private val todayZonedTime: ZonedDateTime
        get() = ZonedDateTime.now()

    val remainingDaysInArchive: Int
        get() = 30 - ChronoUnit.DAYS.between(zonedDateTime, todayZonedTime).toInt()
}
