package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.OtherHuddlesList


private const val STARTING_KEY = 1
class OtherUsersHuddlesDataSource(private val api: ChatAPIService,private val id:Int): PagingSource<Int, OtherHuddlesList>() {


    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, OtherHuddlesList> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getOtherUsersHuddles(huddleId = id,page = currentPage, limit = 50)
            val responseData = mutableListOf<OtherHuddlesList>()
            val result = response.body()?.result
            val data = result?.otherHuddlesListList ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (response.body()?.result!!.next_page != true) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.otherHuddlesListList, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("HuddlesOtherUsersResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, OtherHuddlesList>): Int? {
        return null
    }


}