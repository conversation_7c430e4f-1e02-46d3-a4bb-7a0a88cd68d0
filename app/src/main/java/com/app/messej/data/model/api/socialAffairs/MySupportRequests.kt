package com.app.messej.data.model.api.socialAffairs

import com.app.messej.data.model.enums.SocialCaseStatus
import com.google.gson.annotations.SerializedName

data class MySupportListResponse(
    @SerializedName("has_next") val hasNext: Boolean?,
    @SerializedName("archived_case") val archivedCase: MySupportRequest?,
    @SerializedName("data") val mySupportList: List<MySupportRequest>?,
    @SerializedName("eligibility") val eligibility: SocialSupportEligibility?
) {

    data class SocialSupportEligibility(
        @SerializedName("is_eligible") val isEligibleToApply: Boolean? = null,
        @SerializedName("info") val eligibilityInfo: SocialVoteErrorResponse.SocialError? = null,
    )

    data class MySupportRequest(
        @SerializedName(value = "id") override val id : Int?,
        @SerializedName(value = "target_amount") override val targetAmount: Double?,
        @SerializedName(value = "received_amount") override val receivedAmount: Double?,
        @SerializedName(value = "case_title") override val caseTitle: String?,
        @SerializedName(value = "case_description") override val caseDescription: String?,
        @SerializedName(value = "support_votes") override val supportVotes: Int?,
        @SerializedName(value = "opposed_votes") override val opposedVotes: Int?,
        @SerializedName(value = "net_votes") override val netVotes: Int?,
        @SerializedName(value = "proof_files") override val proofFiles: List<String>?,
        @SerializedName(value = "user_details") override val userDetail: SocialAffairUser?,
        @SerializedName(value = "status") override val status: SocialCaseStatus?,
        @SerializedName(value = "time_created") override val timeCreated: String?,
        @SerializedName(value = "is_voted") override val isVoted: Boolean?,

        @SerializedName(value = "amount_requested") val amountRequested: Double?

    ): AbstractSocialCase() {

        companion object {
            val dummyTestRequest = MySupportRequest(
                id = 14,
                targetAmount = 138.75,
                receivedAmount = 0.0,
                caseTitle = "Test Case Title",
                caseDescription = "Test Case Description",
                supportVotes = 0,
                opposedVotes = 0,
                netVotes = 0,
                proofFiles = null,
                userDetail = null,
                status = SocialCaseStatus.ACTIVE,
                timeCreated = "2025-07-15T05:01:54",
                amountRequested = 111.0,
                isVoted = false
            )
        }

    }
}
