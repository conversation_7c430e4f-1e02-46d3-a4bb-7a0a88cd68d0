package com.app.messej.data.model

import androidx.room.TypeConverter
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.enums.ChatTextColor
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.UserBadge
import com.app.messej.data.model.enums.UserRole
import com.app.messej.data.utils.DateTimeUtils
import com.github.f4b6a3.uuid.UuidCreator
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type
import java.time.ZonedDateTime

data class ReplyTo(
    @SerializedName("message_id"      ) var messageId     : String,
    @SerializedName("reply_id"        ) var replyId       : String,
    @SerializedName("message"         ) var message       : String?  = null,

    @SerializedName("media"           ) var media         : String?  = null,
    @SerializedName("display_name"    ) var mediaName     : String?  = null,
    @SerializedName("media_type"      ) var mediaType     : MediaType?  = null,
    @SerializedName("mime_type"       ) var mimeType      : String?  = null,
    @SerializedName("message_type"    ) var internalMessageType  : AbstractChatMessage.MessageType? = null,

    @SerializedName("sender_name"     ) var senderName    : String  = "",
    @SerializedName("sender_role"     ) var senderRole    : UserRole?  = null,
    @SerializedName("sender_id"       ) var senderId      : Int,
    @SerializedName("private_chat_id" ) var privateChatId : String?  = null,

    @SerializedName("deleted"         ) var deleted       : Boolean = false,
    @SerializedName("reported"        ) var reported      : Boolean = false,

    // For audio
    @SerializedName("media_duration"  ) var mediaDuration : String?  = null,

    // For Huddle
    @SerializedName("is_premium"      ) var premium       : Boolean = false,
    @SerializedName("verified"        ) var isVerified    : Boolean? = false,
    @SerializedName("profile_url"     ) var thumbnailUrl  : String?  = null,
//    @SerializedName("country_name"    ) var countryName   : String?  = null,
    @SerializedName("country_code"    ) var countryCode   : String?  = null,
    @SerializedName("total_likes"     ) val totalLikes    : Int = 0,
    @SerializedName("liked"           ) val liked         : Boolean = false,
    @SerializedName("created"         ) val createdTime   : String? = null,
//    "sent": null,
    @SerializedName("sender_broadcastType") val senderRelation: FollowerType? = null,

    @SerializedName("has_mention"         )           val hasMention    : Boolean = false,
    @SerializedName("color"               )           val chatTextColor : ChatTextColor? = null,
    @SerializedName("mentioned_users"     )           val mentionedUsers: List<MentionedUser>? = null,

    ) {

    var nickName: NickName? = null

    val hasMedia: Boolean
        get() = media!==null

    val hasLocation: Boolean
        get() = internalMessageType == AbstractChatMessage.MessageType.LOCATION
    val hasSticker: Boolean
        get() = internalMessageType == AbstractChatMessage.MessageType.STICKER

    val messageType: AbstractChatMessage.MessageType
        get() {
            return if(hasMedia) AbstractChatMessage.MessageType.MEDIA
            else if(hasLocation) AbstractChatMessage.MessageType.LOCATION
            else if(hasSticker) AbstractChatMessage.MessageType.STICKER
            else AbstractChatMessage.MessageType.TEXT
        }

    val hasText: Boolean
        get() = !message.isNullOrBlank()

    val formattedCreatedDateTime: String?
        get() = DateTimeUtils.format(parsedCreatedTime, "${DateTimeUtils.FORMAT_DDMMYYYY_SLASHED} | ${DateTimeUtils.FORMAT_READABLE_TIME_24HRS}")

    val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(createdTime)

    val userBadge: UserBadge
        get() {
            return if(isVerified==true) UserBadge.VERIFIED
            else if(premium) UserBadge.PREMIUM
            else UserBadge.NONE
        }

    companion object {
        fun create(msg: AbstractChatMessage, sender: SenderDetails): ReplyTo {
            val uuid = UuidCreator.getTimeBased().toString()
            return ReplyTo(
                messageId = msg.messageId,
                replyId = uuid,
                message = msg.rawMessage,

                media = if(msg.internalMessageType==AbstractChatMessage.MessageType.STICKER) msg.media else msg.mediaMeta?.thumbnail,
                mediaType = msg.mediaMeta?.mediaType,
                mimeType = msg.mediaMeta?.mimeType,
                internalMessageType = msg.internalMessageType,

                senderName = sender.name,
                senderRole = sender.role,
                senderId = msg.sender,

                deleted = msg.deleted,
                reported = msg.reported,

                mediaDuration = msg.mediaMeta?.mediaDuration,

                premium = sender.premium,
                isVerified = sender.verified,
                thumbnailUrl = sender.thumbnail,
                countryCode = sender.countryCode,
                liked = msg.liked,
                createdTime = msg.createdTime,
                chatTextColor = msg.chatTextColor,
                hasMention = if (msg is HuddleChatMessage) msg.hasMention else false,
                mentionedUsers = if (msg is HuddleChatMessage) msg.mentionedUsers else listOf(),

                totalLikes = if (msg is HuddleChatMessage) msg.totalLikes else 0,
                senderRelation = if (msg is HuddleChatMessage) msg.senderRelation else null
            )
        }
    }

    class Converter {
        @TypeConverter
        fun decode(data: String?): ReplyTo? {
            data?: return null
            val type: Type = object : TypeToken<ReplyTo?>() {}.type
            return Gson().fromJson<ReplyTo>(data, type)
        }
        @TypeConverter
        fun encode(someObjects: ReplyTo?): String? {
            return Gson().toJson(someObjects)
        }
    }
}
