package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.GiftAPIService
import com.app.messej.data.model.api.gift.CoinsConversion

private const val STARTING_KEY = 1
class GiftConversionHistoryDataSource(private val api: GiftAPIService, private val historyType:String) : PagingSource<Int, CoinsConversion>() {


    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, CoinsConversion> {
        return try {
            val currentPage = params.key ?: STARTING_KEY
            val response = api.getGiftConversionHistory(page = currentPage, limit = 50, history_type = historyType)
            val responseData = mutableListOf<CoinsConversion>()
            val result = response.body()?.result
            val data = result?.coinsConversions ?: emptyList()

            responseData.addAll(data)
            val nextKey = if (!(response.body()?.result!!.nextPage == true)) null else currentPage.plus(1)

            LoadResult.Page(
                data = result!!.coinsConversions, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
            )

        } catch (e: Exception) {
            Log.d("ConversionHistoryResponse", e.message.toString())
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, CoinsConversion>): Int? {
        return null
    }
}