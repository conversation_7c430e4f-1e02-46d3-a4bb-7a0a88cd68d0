package com.app.messej.data.model.entity

import androidx.room.Embedded
import androidx.room.Relation
import com.app.messej.data.model.api.postat.PostComment


data class PostCommentWithReplies(
    @Embedded
    val commentItem: PostComment,

    @Relation(
        parentColumn = PostComment.COLUMN_COMMENT_ID,
        entityColumn = PostReply.COLUMN_PARENT_COMMENT_ID,
    )
    val replies: List<PostReply> = emptyList()
) {

    val hasReplies: Boolean
        get() = replies.isNotEmpty()


    val replyCount: Int
        get() = replies.size
}
