package com.app.messej.data.model.socket

import com.app.messej.data.model.ReplyTo
import com.app.messej.data.model.entity.PrivateChat.ChatType
import com.google.gson.annotations.SerializedName

data class ChatReadDeliverEvent(
    @SerializedName("room_id"         ) val roomId        : String,
    @SerializedName("user_id"         ) val userId        : Int?     = null,
    @SerializedName("message_id"      ) val messageId     : String,
    @SerializedName("sender"          ) val sender        : Int,
    @SerializedName("receiver"        ) val receiver      : Int,
    @SerializedName("blocked"         ) val blocked       : Boolean? = null,
    @SerializedName("chat_type"       ) val chatType      : ChatType?  = null,
    @SerializedName("created"         ) val created       : String,
    @SerializedName("deleted"         ) val deleted       : Boolean? = null,
    @SerializedName("delivered"       ) val delivered     : String?  = null,
    @SerializedName("media"           ) val media         : String?  = null,
    @SerializedName("message"         ) val message       : String?  = null,
    @SerializedName("message_type"    ) val messageType   : String?  = null,
    @SerializedName("private_chat_id" ) val privateChatId : String?  = null,
    @SerializedName("read"            ) val read          : String?  = null,
    @SerializedName("reply_to"        ) val replyTo       : ReplyTo?  = null,
    @SerializedName("reported"        ) val reported      : String?  = null,
    @SerializedName("sent"            ) val sent          : String?  = null
    //    @SerializedName("media_meta"      ) val mediaMeta     : MediaMeta?  = null,
    //    @SerializedName("activity_meta"   ) val activityMeta  : ActivityMeta?  = null,
    //    @SerializedName("is_activity"     ) val isActivity    : String?  = null,
    //    @SerializedName("liked"           ) val liked         : String?  = null,


): SocketEventPayload()
