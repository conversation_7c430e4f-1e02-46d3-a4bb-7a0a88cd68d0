package com.app.messej.data.api

import com.app.messej.data.model.PostAtCommentPayload
import com.app.messej.data.model.PostCommentsRepliesResponse
import com.app.messej.data.model.PostatCommentLikePayload
import com.app.messej.data.model.PostatReplyCommentPayload
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.BlockedUserListResponse
import com.app.messej.data.model.api.flash.UserFunctionalityBlockRequest
import com.app.messej.data.model.api.postat.BlockUserRequest
import com.app.messej.data.model.api.postat.PostatCommentResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface PostatAPIServices {
    @GET("/postat/{postId}/comment")
    suspend fun getComments(
        @Path("postId") postId: String,
        @Query("pageState") pageState: String? = null,
        @Query("page") page: Int? = null,
    ): Response<APIResponse<PostatCommentResponse>>


    @GET("/postat/{postatId}/comment")
    @Headers("Accept: application/json")
    suspend fun getPostatReplies(
        @Path("postatId") postatId: String,
        @Query("page") page: Int? = null,
        @Query("commentId") commentId: String? = null,
        @Query("replies") replies: Int? = null
    ) : Response<APIResponse<PostCommentsRepliesResponse>>

    @DELETE("postat/{postId}/{commentId}")
    suspend fun deletePostatComment(
        @Path("postId") postId: String,
        @Path("commentId") commentId: String
    ): Response<APIResponse<Unit>>

    @DELETE("postat/{commentId}/{replyId}")
    suspend fun deletePostatReplyComment(
        @Path("commentId") commentId: String,
        @Path("replyId") replyId: String
    ): Response<APIResponse<Unit>>

    @POST("postat/{postId}/comment")
    suspend fun writePostatComment(
        @Path("postId") postId: String,
        @Body commentRequest: PostAtCommentPayload
    ): Response<APIResponse<Unit>>

    @POST("postat/{postId}/comment")
    suspend fun writePostatReplyComment(
        @Path("postId") postId: String,
        @Body commentRequest: PostatReplyCommentPayload
    ): Response<APIResponse<Unit>>

    @POST("postat/block_user/")
    suspend fun blockUser(@Body request: BlockUserRequest): Response<APIResponse<Unit>>

    @POST("/user/func-block/{userId}")
    @Headers("Accept: application/json")
    suspend fun blockUserFromPostingOnPostat(@Path("userId") id: Int, @Body request: UserFunctionalityBlockRequest): Response<APIResponse<Unit>>

    @GET("postat/blocked_user_list")
    suspend fun getPostatBlockedUserList(@Query("page") page: Int = 1, @Query("per_page") perPage: Int = 50): Response<APIResponse<BlockedUserListResponse>>

    @POST("/postat/like")
    @Headers("Accept: application/json")
    suspend fun postPostatCommentlike(@Body request: PostatCommentLikePayload): Response<APIResponse<Unit>>

}