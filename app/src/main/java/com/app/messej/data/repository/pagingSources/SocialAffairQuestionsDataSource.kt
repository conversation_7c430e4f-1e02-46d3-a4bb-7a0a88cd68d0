package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.SocialAffairAPIService
import com.app.messej.data.model.api.socialAffairs.AskedQuestionsResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SocialAffairQuestionsDataSource(
    private val apiService: SocialAffairAPIService,
    private val caseId: Int,
    private val questionCallBack: (isEligibleToAskQuestion: Boolean, questionCount: Int) -> Unit
) : PagingSource<Int, AskedQuestionsResponse.QuestionAndAnswer>() {

    companion object {
        private const val STARTING_KEY = 1
    }

    override fun getRefreshKey(state: PagingState<Int, AskedQuestionsResponse.QuestionAndAnswer>): Int? {
        return null
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, AskedQuestionsResponse.QuestionAndAnswer> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = apiService.getQuestionsAndAnswers(caseId = caseId, page = currentPage)
                val data = response.body()?.result ?: return@withContext LoadResult.Error(Exception("No data received from server"))

                //Mapping answerer id, and case status into each questions.
                //So that we can check who can answer to the question directly from the "QuestionAndAnswer data class"
                val updatedQuestionsAndAnswer = data.questionAndAnswers?.onEach {
                    it.answererUserId = data.answererUserId
                    it.caseStatus = data.caseStatus
                }
                val updatedResponse = data.copy(questionAndAnswers = updatedQuestionsAndAnswer)
                questionCallBack(updatedResponse.eligibleForAskQuestion ?: false, updatedResponse.totalQuestions ?: 0)
                val nextKey = if (data.haveNextPage == true) currentPage + 1 else null
                LoadResult.Page(
                    data = updatedResponse.questionAndAnswers ?: emptyList(), prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
}