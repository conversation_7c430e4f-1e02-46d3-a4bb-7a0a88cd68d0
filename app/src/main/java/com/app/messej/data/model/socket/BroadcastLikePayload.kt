package com.app.messej.data.model.socket

import com.app.messej.data.model.enums.BroadcastMode
import com.google.gson.annotations.SerializedName

data class BroadcastLikePayload(
    @SerializedName("broadcast_type") val mode: BroadcastMode,
    @SerializedName("broadcaster") val broadcaster: Int,
    @SerializedName("subscriber")  val subscriber: Int,
    @SerializedName("broadcast_id")  val broadcastId: String,
    @SerializedName("id")  val id: String,
    @SerializedName("liked")  val liked: <PERSON><PERSON><PERSON>,
    @SerializedName("total_likes")  val totalLikes: Int,
    @SerializedName("messages")  val message: String,
): SocketEventPayload()
