package com.app.messej.data.model.api


import com.google.gson.annotations.SerializedName

data class AppReviewResponse(
    @SerializedName("id"                ) val id            : Int?      = null,
    @SerializedName("screenshot_url"    ) val screenshotUrl : String?   = null,
    @SerializedName("thumbnail_url"     ) val thumbnailUrl  : String?   = null,
    @SerializedName("time_created"      ) val timeCreated   : String?   = null,
    @SerializedName("time_updated"      ) val timeUpdated   : String?   = null,
    @SerializedName("user_id"           ) val userId        : Int?      = null
)