package com.app.messej.data.model

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.app.messej.BR

data class OptionsModel(val id: Int = 0, var text: String = ""  )
class ObservableOptionsModel( val id: Int = 0, text: String = "",) : BaseObservable() {
    @get:Bindable
    var text: String = text
        set(value) {
            field = value
            notifyPropertyChanged(BR.text)
        }
}