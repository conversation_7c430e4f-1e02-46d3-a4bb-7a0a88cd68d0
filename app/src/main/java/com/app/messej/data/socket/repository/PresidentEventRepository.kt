package com.app.messej.data.socket.repository

import com.app.messej.MainApplication
import com.app.messej.data.model.api.profile.UserBirthdayResponse
import com.app.messej.data.model.socket.SocialPendingApprovalPayload
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object PresidentEventRepository : BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {
    private val accountRepo = AccountRepository(MainApplication.applicationInstance())
    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when (event) {
            ChatSocketEvent.RX_NEW_PRESIDENT_CROWNED -> {
                onSocketData(data)
            }
            ChatSocketEvent.SOCIAL_PENDING_APPROVAL -> {
                onSocialPendingApproval(data)
            }
            else -> return false
        }
        return true
    }


    private val _presidentPayLoad: MutableSharedFlow<UserBirthdayResponse> = MutableSharedFlow()
    val presidentPayLoad: SharedFlow<UserBirthdayResponse> = _presidentPayLoad

    private fun onSocketData(data: JSONObject) = runBlocking {
        val info = Gson().fromJson<UserBirthdayResponse>(data.toString())
        withContext(Dispatchers.IO) {
            _presidentPayLoad.emit(info)

        }
    }


    private val _onSocialPendingApproval: MutableSharedFlow<SocialPendingApprovalPayload> = MutableSharedFlow()
    val onSocialPendingApproval: SharedFlow<SocialPendingApprovalPayload> = _onSocialPendingApproval
    private fun onSocialPendingApproval(data: JSONObject) = runBlocking {
        val data = Gson().fromJson<SocialPendingApprovalPayload>(data.toString())
        _onSocialPendingApproval.emit(data)
    }

}

